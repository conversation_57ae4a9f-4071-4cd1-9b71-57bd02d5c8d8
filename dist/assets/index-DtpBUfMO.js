var tm=Object.defineProperty;var nm=(e,t,n)=>t in e?tm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Jo=(e,t,n)=>nm(e,typeof t!="symbol"?t+"":t,n);function CE(){import.meta.url,import("_").catch(()=>1),async function*(){}().next()}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const u of o.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&r(u)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();(function(){const e=()=>Math.floor((1+Math.random())*16777216).toString(16).substring(1),t=()=>e()+e(),n={},r={},i={},o=s=>{Object.assign(r,s),Object.keys(r).forEach(function(a){typeof window.$data[a]<"u"||Object.defineProperty(window.$data,a,{get(){return r[a]},set(d){r[a]=d},enumerable:!0})}),window.dispatchEvent(new CustomEvent("data.changed"))};window.$perkd={onMessage(s){const a=n[s.id];a?s.error?a.reject(s.error):a.resolve(s.data):s.name==="data.changed"?o(s.data):s.name==="bag.changed"?(Object.assign(i,s.data),window.dispatchEvent(new CustomEvent("bag.changed"))):window.dispatchEvent(new CustomEvent(s.name,{detail:s.data}))},emit(s,a){const d=t(),f=JSON.stringify({id:d,name:s,data:a});return window.ReactNativeWebView?window.ReactNativeWebView.postMessage(f):window.parent.postMessage(JSON.parse(f),"*"),d},do(s,a){const d=window.$perkd.emit("do",{action:s,param:a});return new Promise(function(f,h){n[d]={resolve:f,reject:h}})}};class u{save(){window.$perkd.do("data.save",r)}add(a){Object.defineProperty(this,a,{get(){return r[a]},set(d){r[a]=d},enumerable:!0})}}window.$data=new u;class c{constructor(){Jo(this,"items");Jo(this,"amount")}addItems(a){window.$perkd.do("bag.addItems",{items:a})}updateItems(a){window.$perkd.do("bag.updateItems",{items:a})}removeItems(a){window.$perkd.do("bag.removeItems",{items:a})}}window.$bag=new c,["items","amount"].forEach(s=>Object.defineProperty(window.$bag,s,{get(){return i[s]},enumerable:!0})),window.$perkd.do("init").then(o)})();function Rf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Zr={},Ka;function kf(){if(Ka)return Zr;Ka=1,Object.defineProperty(Zr,"__esModule",{value:!0});const e={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),weekStart:1,weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),ordinal:t=>{const n=["th","st","nd","rd"],r=t%100;return"[".concat(t).concat(n[(r-20)%10]||n[r]||n[0],"]")},formats:{LT:"h:mma",LTS:"ha",L:"D MMM, LTX",LL:"D MMM YYYY, LTX",LLL:"dddd, D MMM, LTX",LLLL:"dddd, D MMM YYYY, LTX",l:"D MMM",ll:"D MMM YYYY",lll:"dddd, D MMM",llll:"dddd, D MMM YYYY"},calendar:{lastDay:"[yesterday]",sameDay:"[today]",nextDay:"[tomorrow]",lastWeek:"[last] dddd",sameWeek:"dddd",nextWeek:"[next] dddd",sameYear:"l",sameElse:"ll",timeFormat:"%c, LTX"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},humane:{daysToRelative:0,daysToCalendar:1,skipFromUnit:"second",startFrom:{value:30,unit:"second"},soon:"in few %u",justnow:"just now",s:"1 sec",ss:"%d secs",m:"1 min",mm:"%d mins",h:"1 hour",hh:"%d hours",d:"1 day",dd:"%d days",M:"1 month",MM:"%d months",y:"1 year",yy:"%d years"},period:{daysToCalendar:1,showSameDayToday:!1,sameYear:{startDate:"l",endDate:"lyx",startTime:"LYX",endTime:"LYX",format:"%ds - %de"},sameMonth:{startDate:"D",endDate:"lyx",startTime:"LYX",endTime:"LYX",format:"%ds - %de"},sameDay:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds - %de"},sameMeridiem:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds - %de"},others:{startDate:"ll",endDate:"ll",startTime:"LL",endTime:"LL",format:"%ds - %de"}}};return Zr.default=e,Zr}kf();var ei={},ge={},zi={exports:{}},rm=zi.exports,Qa;function Nf(){return Qa||(Qa=1,function(e,t){(function(n,r){e.exports=r()})(rm,function(){var n=1e3,r=6e4,i=36e5,o="millisecond",u="second",c="minute",l="hour",s="day",a="week",d="month",f="quarter",h="year",p="date",m="Invalid Date",_=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,g=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,v={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(N){var G=["th","st","nd","rd"],Y=N%100;return"["+N+(G[(Y-20)%10]||G[Y]||G[0])+"]"}},b=function(N,G,Y){var W=String(N);return!W||W.length>=G?N:""+Array(G+1-W.length).join(Y)+N},E={s:b,z:function(N){var G=-N.utcOffset(),Y=Math.abs(G),W=Math.floor(Y/60),V=Y%60;return(G<=0?"+":"-")+b(W,2,"0")+":"+b(V,2,"0")},m:function N(G,Y){if(G.date()<Y.date())return-N(Y,G);var W=12*(Y.year()-G.year())+(Y.month()-G.month()),V=G.clone().add(W,d),J=Y-V<0,te=G.clone().add(W+(J?-1:1),d);return+(-(W+(Y-V)/(J?V-te:te-V))||0)},a:function(N){return N<0?Math.ceil(N)||0:Math.floor(N)},p:function(N){return{M:d,y:h,w:a,d:s,D:p,h:l,m:c,s:u,ms:o,Q:f}[N]||String(N||"").toLowerCase().replace(/s$/,"")},u:function(N){return N===void 0}},D="en",I={};I[D]=v;var O="$isDayjsObject",M=function(N){return N instanceof L||!(!N||!N[O])},T=function N(G,Y,W){var V;if(!G)return D;if(typeof G=="string"){var J=G.toLowerCase();I[J]&&(V=J),Y&&(I[J]=Y,V=J);var te=G.split("-");if(!V&&te.length>1)return N(te[0])}else{var ie=G.name;I[ie]=G,V=ie}return!W&&V&&(D=V),V||!W&&D},k=function(N,G){if(M(N))return N.clone();var Y=typeof G=="object"?G:{};return Y.date=N,Y.args=arguments,new L(Y)},R=E;R.l=T,R.i=M,R.w=function(N,G){return k(N,{locale:G.$L,utc:G.$u,x:G.$x,$offset:G.$offset})};var L=function(){function N(Y){this.$L=T(Y.locale,null,!0),this.parse(Y),this.$x=this.$x||Y.x||{},this[O]=!0}var G=N.prototype;return G.parse=function(Y){this.$d=function(W){var V=W.date,J=W.utc;if(V===null)return new Date(NaN);if(R.u(V))return new Date;if(V instanceof Date)return new Date(V);if(typeof V=="string"&&!/Z$/i.test(V)){var te=V.match(_);if(te){var ie=te[2]-1||0,ue=(te[7]||"0").substring(0,3);return J?new Date(Date.UTC(te[1],ie,te[3]||1,te[4]||0,te[5]||0,te[6]||0,ue)):new Date(te[1],ie,te[3]||1,te[4]||0,te[5]||0,te[6]||0,ue)}}return new Date(V)}(Y),this.init()},G.init=function(){var Y=this.$d;this.$y=Y.getFullYear(),this.$M=Y.getMonth(),this.$D=Y.getDate(),this.$W=Y.getDay(),this.$H=Y.getHours(),this.$m=Y.getMinutes(),this.$s=Y.getSeconds(),this.$ms=Y.getMilliseconds()},G.$utils=function(){return R},G.isValid=function(){return this.$d.toString()!==m},G.isSame=function(Y,W){var V=k(Y);return this.startOf(W)<=V&&V<=this.endOf(W)},G.isAfter=function(Y,W){return k(Y)<this.startOf(W)},G.isBefore=function(Y,W){return this.endOf(W)<k(Y)},G.$g=function(Y,W,V){return R.u(Y)?this[W]:this.set(V,Y)},G.unix=function(){return Math.floor(this.valueOf()/1e3)},G.valueOf=function(){return this.$d.getTime()},G.startOf=function(Y,W){var V=this,J=!!R.u(W)||W,te=R.p(Y),ie=function(X,Q){var fe=R.w(V.$u?Date.UTC(V.$y,Q,X):new Date(V.$y,Q,X),V);return J?fe:fe.endOf(s)},ue=function(X,Q){return R.w(V.toDate()[X].apply(V.toDate("s"),(J?[0,0,0,0]:[23,59,59,999]).slice(Q)),V)},me=this.$W,Ee=this.$M,Oe=this.$D,Ae="set"+(this.$u?"UTC":"");switch(te){case h:return J?ie(1,0):ie(31,11);case d:return J?ie(1,Ee):ie(0,Ee+1);case a:var U=this.$locale().weekStart||0,K=(me<U?me+7:me)-U;return ie(J?Oe-K:Oe+(6-K),Ee);case s:case p:return ue(Ae+"Hours",0);case l:return ue(Ae+"Minutes",1);case c:return ue(Ae+"Seconds",2);case u:return ue(Ae+"Milliseconds",3);default:return this.clone()}},G.endOf=function(Y){return this.startOf(Y,!1)},G.$set=function(Y,W){var V,J=R.p(Y),te="set"+(this.$u?"UTC":""),ie=(V={},V[s]=te+"Date",V[p]=te+"Date",V[d]=te+"Month",V[h]=te+"FullYear",V[l]=te+"Hours",V[c]=te+"Minutes",V[u]=te+"Seconds",V[o]=te+"Milliseconds",V)[J],ue=J===s?this.$D+(W-this.$W):W;if(J===d||J===h){var me=this.clone().set(p,1);me.$d[ie](ue),me.init(),this.$d=me.set(p,Math.min(this.$D,me.daysInMonth())).$d}else ie&&this.$d[ie](ue);return this.init(),this},G.set=function(Y,W){return this.clone().$set(Y,W)},G.get=function(Y){return this[R.p(Y)]()},G.add=function(Y,W){var V,J=this;Y=Number(Y);var te=R.p(W),ie=function(Ee){var Oe=k(J);return R.w(Oe.date(Oe.date()+Math.round(Ee*Y)),J)};if(te===d)return this.set(d,this.$M+Y);if(te===h)return this.set(h,this.$y+Y);if(te===s)return ie(1);if(te===a)return ie(7);var ue=(V={},V[c]=r,V[l]=i,V[u]=n,V)[te]||1,me=this.$d.getTime()+Y*ue;return R.w(me,this)},G.subtract=function(Y,W){return this.add(-1*Y,W)},G.format=function(Y){var W=this,V=this.$locale();if(!this.isValid())return V.invalidDate||m;var J=Y||"YYYY-MM-DDTHH:mm:ssZ",te=R.z(this),ie=this.$H,ue=this.$m,me=this.$M,Ee=V.weekdays,Oe=V.months,Ae=V.meridiem,U=function(Q,fe,S,y){return Q&&(Q[fe]||Q(W,J))||S[fe].slice(0,y)},K=function(Q){return R.s(ie%12||12,Q,"0")},X=Ae||function(Q,fe,S){var y=Q<12?"AM":"PM";return S?y.toLowerCase():y};return J.replace(g,function(Q,fe){return fe||function(S){switch(S){case"YY":return String(W.$y).slice(-2);case"YYYY":return R.s(W.$y,4,"0");case"M":return me+1;case"MM":return R.s(me+1,2,"0");case"MMM":return U(V.monthsShort,me,Oe,3);case"MMMM":return U(Oe,me);case"D":return W.$D;case"DD":return R.s(W.$D,2,"0");case"d":return String(W.$W);case"dd":return U(V.weekdaysMin,W.$W,Ee,2);case"ddd":return U(V.weekdaysShort,W.$W,Ee,3);case"dddd":return Ee[W.$W];case"H":return String(ie);case"HH":return R.s(ie,2,"0");case"h":return K(1);case"hh":return K(2);case"a":return X(ie,ue,!0);case"A":return X(ie,ue,!1);case"m":return String(ue);case"mm":return R.s(ue,2,"0");case"s":return String(W.$s);case"ss":return R.s(W.$s,2,"0");case"SSS":return R.s(W.$ms,3,"0");case"Z":return te}return null}(Q)||te.replace(":","")})},G.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},G.diff=function(Y,W,V){var J,te=this,ie=R.p(W),ue=k(Y),me=(ue.utcOffset()-this.utcOffset())*r,Ee=this-ue,Oe=function(){return R.m(te,ue)};switch(ie){case h:J=Oe()/12;break;case d:J=Oe();break;case f:J=Oe()/3;break;case a:J=(Ee-me)/6048e5;break;case s:J=(Ee-me)/864e5;break;case l:J=Ee/i;break;case c:J=Ee/r;break;case u:J=Ee/n;break;default:J=Ee}return V?J:R.a(J)},G.daysInMonth=function(){return this.endOf(d).$D},G.$locale=function(){return I[this.$L]},G.locale=function(Y,W){if(!Y)return this.$L;var V=this.clone(),J=T(Y,W,!0);return J&&(V.$L=J),V},G.clone=function(){return R.w(this.$d,this)},G.toDate=function(){return new Date(this.valueOf())},G.toJSON=function(){return this.isValid()?this.toISOString():null},G.toISOString=function(){return this.$d.toISOString()},G.toString=function(){return this.$d.toUTCString()},N}(),B=L.prototype;return k.prototype=B,[["$ms",o],["$s",u],["$m",c],["$H",l],["$W",s],["$M",d],["$y",h],["$D",p]].forEach(function(N){B[N[1]]=function(G){return this.$g(G,N[0],N[1])}}),k.extend=function(N,G){return N.$i||(N(G,L,k),N.$i=!0),k},k.locale=T,k.isDayjs=M,k.unix=function(N){return k(1e3*N)},k.en=I[D],k.Ls=I,k.p={},k})}(zi)),zi.exports}var Xi={exports:{}},im=Xi.exports,Ja;function om(){return Ja||(Ja=1,function(e,t){(function(n,r){e.exports=r()})(im,function(){return function(n,r,i){i.updateLocale=function(o,u){var c=i.Ls[o];if(c)return(u?Object.keys(u):[]).forEach(function(l){c[l]=u[l]}),c}}})}(Xi)),Xi.exports}var Ki={exports:{}},sm=Ki.exports,Za;function am(){return Za||(Za=1,function(e,t){(function(n,r){e.exports=r()})(sm,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(r,i,o){var u=i.prototype,c=u.format;o.en.formats=n,u.format=function(l){l===void 0&&(l="YYYY-MM-DDTHH:mm:ssZ");var s=this.$locale().formats,a=function(d,f){return d.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(h,p,m){var _=m&&m.toUpperCase();return p||f[m]||n[m]||f[_].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(g,v,b){return v||b.slice(1)})})}(l,s===void 0?{}:s);return c.call(this,a)}}})}(Ki)),Ki.exports}var Qi={exports:{}},um=Qi.exports,eu;function lm(){return eu||(eu=1,function(e,t){(function(n,r){e.exports=r()})(um,function(){var n="day";return function(r,i,o){var u=function(s){return s.add(4-s.isoWeekday(),n)},c=i.prototype;c.isoWeekYear=function(){return u(this).year()},c.isoWeek=function(s){if(!this.$utils().u(s))return this.add(7*(s-this.isoWeek()),n);var a,d,f,h,p=u(this),m=(a=this.isoWeekYear(),d=this.$u,f=(d?o.utc:o)().year(a).startOf("year"),h=4-f.isoWeekday(),f.isoWeekday()>4&&(h+=7),f.add(h,n));return p.diff(m,"week")+1},c.isoWeekday=function(s){return this.$utils().u(s)?this.day()||7:this.day(this.day()%7?s:s-7)};var l=c.startOf;c.startOf=function(s,a){var d=this.$utils(),f=!!d.u(a)||a;return d.p(s)==="isoweek"?f?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):l.bind(this)(s,a)}}})}(Qi)),Qi.exports}var Ji={exports:{}},cm=Ji.exports,tu;function fm(){return tu||(tu=1,function(e,t){(function(n,r){e.exports=r()})(cm,function(){return function(n,r){r.prototype.weekday=function(i){var o=this.$locale().weekStart||0,u=this.$W,c=(u<o?u+7:u)-o;return this.$utils().u(i)?c:this.subtract(c,"day").add(i,"day")}}})}(Ji)),Ji.exports}var Zi={exports:{}},dm=Zi.exports,nu;function hm(){return nu||(nu=1,function(e,t){(function(n,r){e.exports=r()})(dm,function(){return function(n,r,i){n=n||{};var o=r.prototype,u={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function c(s,a,d,f){return o.fromToBase(s,a,d,f)}i.en.relativeTime=u,o.fromToBase=function(s,a,d,f,h){for(var p,m,_,g=d.$locale().relativeTime||u,v=n.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],b=v.length,E=0;E<b;E+=1){var D=v[E];D.d&&(p=f?i(s).diff(d,D.d,!0):d.diff(s,D.d,!0));var I=(n.rounding||Math.round)(Math.abs(p));if(_=p>0,I<=D.r||!D.r){I<=1&&E>0&&(D=v[E-1]);var O=g[D.l];h&&(I=h(""+I)),m=typeof O=="string"?O.replace("%d",I):O(I,a,D.l,_);break}}if(a)return m;var M=_?g.future:g.past;return typeof M=="function"?M(m):M.replace("%s",m)},o.to=function(s,a){return c(s,a,this,!0)},o.from=function(s,a){return c(s,a,this)};var l=function(s){return s.$u?i.utc():i()};o.toNow=function(s){return this.to(l(this),s)},o.fromNow=function(s){return this.from(l(this),s)}}})}(Zi)),Zi.exports}var eo={exports:{}},mm=eo.exports,ru;function pm(){return ru||(ru=1,function(e,t){(function(n,r){e.exports=r()})(mm,function(){return function(n,r,i){r.prototype.isBetween=function(o,u,c,l){var s=i(o),a=i(u),d=(l=l||"()")[0]==="(",f=l[1]===")";return(d?this.isAfter(s,c):!this.isBefore(s,c))&&(f?this.isBefore(a,c):!this.isAfter(a,c))||(d?this.isBefore(s,c):!this.isAfter(s,c))&&(f?this.isAfter(a,c):!this.isBefore(a,c))}}})}(eo)),eo.exports}var to={exports:{}},gm=to.exports,iu;function _m(){return iu||(iu=1,function(e,t){(function(n,r){e.exports=r()})(gm,function(){var n,r,i=1e3,o=6e4,u=36e5,c=864e5,l=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,s=31536e6,a=2628e6,d=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,f={years:s,months:a,days:c,hours:u,minutes:o,seconds:i,milliseconds:1,weeks:6048e5},h=function(I){return I instanceof E},p=function(I,O,M){return new E(I,M,O.$l)},m=function(I){return r.p(I)+"s"},_=function(I){return I<0},g=function(I){return _(I)?Math.ceil(I):Math.floor(I)},v=function(I){return Math.abs(I)},b=function(I,O){return I?_(I)?{negative:!0,format:""+v(I)+O}:{negative:!1,format:""+I+O}:{negative:!1,format:""}},E=function(){function I(M,T,k){var R=this;if(this.$d={},this.$l=k,M===void 0&&(this.$ms=0,this.parseFromMilliseconds()),T)return p(M*f[m(T)],this);if(typeof M=="number")return this.$ms=M,this.parseFromMilliseconds(),this;if(typeof M=="object")return Object.keys(M).forEach(function(N){R.$d[m(N)]=M[N]}),this.calMilliseconds(),this;if(typeof M=="string"){var L=M.match(d);if(L){var B=L.slice(2).map(function(N){return N!=null?Number(N):0});return this.$d.years=B[0],this.$d.months=B[1],this.$d.weeks=B[2],this.$d.days=B[3],this.$d.hours=B[4],this.$d.minutes=B[5],this.$d.seconds=B[6],this.calMilliseconds(),this}}return this}var O=I.prototype;return O.calMilliseconds=function(){var M=this;this.$ms=Object.keys(this.$d).reduce(function(T,k){return T+(M.$d[k]||0)*f[k]},0)},O.parseFromMilliseconds=function(){var M=this.$ms;this.$d.years=g(M/s),M%=s,this.$d.months=g(M/a),M%=a,this.$d.days=g(M/c),M%=c,this.$d.hours=g(M/u),M%=u,this.$d.minutes=g(M/o),M%=o,this.$d.seconds=g(M/i),M%=i,this.$d.milliseconds=M},O.toISOString=function(){var M=b(this.$d.years,"Y"),T=b(this.$d.months,"M"),k=+this.$d.days||0;this.$d.weeks&&(k+=7*this.$d.weeks);var R=b(k,"D"),L=b(this.$d.hours,"H"),B=b(this.$d.minutes,"M"),N=this.$d.seconds||0;this.$d.milliseconds&&(N+=this.$d.milliseconds/1e3,N=Math.round(1e3*N)/1e3);var G=b(N,"S"),Y=M.negative||T.negative||R.negative||L.negative||B.negative||G.negative,W=L.format||B.format||G.format?"T":"",V=(Y?"-":"")+"P"+M.format+T.format+R.format+W+L.format+B.format+G.format;return V==="P"||V==="-P"?"P0D":V},O.toJSON=function(){return this.toISOString()},O.format=function(M){var T=M||"YYYY-MM-DDTHH:mm:ss",k={Y:this.$d.years,YY:r.s(this.$d.years,2,"0"),YYYY:r.s(this.$d.years,4,"0"),M:this.$d.months,MM:r.s(this.$d.months,2,"0"),D:this.$d.days,DD:r.s(this.$d.days,2,"0"),H:this.$d.hours,HH:r.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:r.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:r.s(this.$d.seconds,2,"0"),SSS:r.s(this.$d.milliseconds,3,"0")};return T.replace(l,function(R,L){return L||String(k[R])})},O.as=function(M){return this.$ms/f[m(M)]},O.get=function(M){var T=this.$ms,k=m(M);return k==="milliseconds"?T%=1e3:T=k==="weeks"?g(T/f[k]):this.$d[k],T||0},O.add=function(M,T,k){var R;return R=T?M*f[m(T)]:h(M)?M.$ms:p(M,this).$ms,p(this.$ms+R*(k?-1:1),this)},O.subtract=function(M,T){return this.add(M,T,!0)},O.locale=function(M){var T=this.clone();return T.$l=M,T},O.clone=function(){return p(this.$ms,this)},O.humanize=function(M){return n().add(this.$ms,"ms").locale(this.$l).fromNow(!M)},O.valueOf=function(){return this.asMilliseconds()},O.milliseconds=function(){return this.get("milliseconds")},O.asMilliseconds=function(){return this.as("milliseconds")},O.seconds=function(){return this.get("seconds")},O.asSeconds=function(){return this.as("seconds")},O.minutes=function(){return this.get("minutes")},O.asMinutes=function(){return this.as("minutes")},O.hours=function(){return this.get("hours")},O.asHours=function(){return this.as("hours")},O.days=function(){return this.get("days")},O.asDays=function(){return this.as("days")},O.weeks=function(){return this.get("weeks")},O.asWeeks=function(){return this.as("weeks")},O.months=function(){return this.get("months")},O.asMonths=function(){return this.as("months")},O.years=function(){return this.get("years")},O.asYears=function(){return this.as("years")},I}(),D=function(I,O,M){return I.add(O.years()*M,"y").add(O.months()*M,"M").add(O.days()*M,"d").add(O.hours()*M,"h").add(O.minutes()*M,"m").add(O.seconds()*M,"s").add(O.milliseconds()*M,"ms")};return function(I,O,M){n=M,r=M().$utils(),M.duration=function(R,L){var B=M.locale();return p(R,{$l:B},L)},M.isDuration=h;var T=O.prototype.add,k=O.prototype.subtract;O.prototype.add=function(R,L){return h(R)?D(this,R,1):T.bind(this)(R,L)},O.prototype.subtract=function(R,L){return h(R)?D(this,R,-1):k.bind(this)(R,L)}}})}(to)),to.exports}var no={exports:{}},vm=no.exports,ou;function ym(){return ou||(ou=1,function(e,t){(function(n,r){e.exports=r()})(vm,function(){var n="minute",r=/[+-]\d\d(?::?\d\d)?/g,i=/([+-]|\d\d)/g;return function(o,u,c){var l=u.prototype;c.utc=function(m){var _={date:m,utc:!0,args:arguments};return new u(_)},l.utc=function(m){var _=c(this.toDate(),{locale:this.$L,utc:!0});return m?_.add(this.utcOffset(),n):_},l.local=function(){return c(this.toDate(),{locale:this.$L,utc:!1})};var s=l.parse;l.parse=function(m){m.utc&&(this.$u=!0),this.$utils().u(m.$offset)||(this.$offset=m.$offset),s.call(this,m)};var a=l.init;l.init=function(){if(this.$u){var m=this.$d;this.$y=m.getUTCFullYear(),this.$M=m.getUTCMonth(),this.$D=m.getUTCDate(),this.$W=m.getUTCDay(),this.$H=m.getUTCHours(),this.$m=m.getUTCMinutes(),this.$s=m.getUTCSeconds(),this.$ms=m.getUTCMilliseconds()}else a.call(this)};var d=l.utcOffset;l.utcOffset=function(m,_){var g=this.$utils().u;if(g(m))return this.$u?0:g(this.$offset)?d.call(this):this.$offset;if(typeof m=="string"&&(m=function(D){D===void 0&&(D="");var I=D.match(r);if(!I)return null;var O=(""+I[0]).match(i)||["-",0,0],M=O[0],T=60*+O[1]+ +O[2];return T===0?0:M==="+"?T:-T}(m),m===null))return this;var v=Math.abs(m)<=16?60*m:m,b=this;if(_)return b.$offset=v,b.$u=m===0,b;if(m!==0){var E=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(b=this.local().add(v+E,n)).$offset=v,b.$x.$localOffset=E}else b=this.utc();return b};var f=l.format;l.format=function(m){var _=m||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return f.call(this,_)},l.valueOf=function(){var m=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*m},l.isUTC=function(){return!!this.$u},l.toISOString=function(){return this.toDate().toISOString()},l.toString=function(){return this.toDate().toUTCString()};var h=l.toDate;l.toDate=function(m){return m==="s"&&this.$offset?c(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():h.call(this)};var p=l.diff;l.diff=function(m,_,g){if(m&&this.$u===m.$u)return p.call(this,m,_,g);var v=this.local(),b=c(m).local();return p.call(v,b,_,g)}}})}(no)),no.exports}var ro={exports:{}},bm=ro.exports,su;function Em(){return su||(su=1,function(e,t){(function(n,r){e.exports=r()})(bm,function(){var n={year:0,month:1,day:2,hour:3,minute:4,second:5},r={};return function(i,o,u){var c,l=function(f,h,p){p===void 0&&(p={});var m=new Date(f),_=function(g,v){v===void 0&&(v={});var b=v.timeZoneName||"short",E=g+"|"+b,D=r[E];return D||(D=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:g,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:b}),r[E]=D),D}(h,p);return _.formatToParts(m)},s=function(f,h){for(var p=l(f,h),m=[],_=0;_<p.length;_+=1){var g=p[_],v=g.type,b=g.value,E=n[v];E>=0&&(m[E]=parseInt(b,10))}var D=m[3],I=D===24?0:D,O=m[0]+"-"+m[1]+"-"+m[2]+" "+I+":"+m[4]+":"+m[5]+":000",M=+f;return(u.utc(O).valueOf()-(M-=M%1e3))/6e4},a=o.prototype;a.tz=function(f,h){f===void 0&&(f=c);var p,m=this.utcOffset(),_=this.toDate(),g=_.toLocaleString("en-US",{timeZone:f}),v=Math.round((_-new Date(g))/1e3/60),b=15*-Math.round(_.getTimezoneOffset()/15)-v;if(!Number(b))p=this.utcOffset(0,h);else if(p=u(g,{locale:this.$L}).$set("millisecond",this.$ms).utcOffset(b,!0),h){var E=p.utcOffset();p=p.add(m-E,"minute")}return p.$x.$timezone=f,p},a.offsetName=function(f){var h=this.$x.$timezone||u.tz.guess(),p=l(this.valueOf(),h,{timeZoneName:f}).find(function(m){return m.type.toLowerCase()==="timezonename"});return p&&p.value};var d=a.startOf;a.startOf=function(f,h){if(!this.$x||!this.$x.$timezone)return d.call(this,f,h);var p=u(this.format("YYYY-MM-DD HH:mm:ss:SSS"),{locale:this.$L});return d.call(p,f,h).tz(this.$x.$timezone,!0)},u.tz=function(f,h,p){var m=p&&h,_=p||h||c,g=s(+u(),_);if(typeof f!="string")return u(f).tz(_);var v=function(I,O,M){var T=I-60*O*1e3,k=s(T,M);if(O===k)return[T,O];var R=s(T-=60*(k-O)*1e3,M);return k===R?[T,k]:[I-60*Math.min(k,R)*1e3,Math.max(k,R)]}(u.utc(f,m).valueOf(),g,_),b=v[0],E=v[1],D=u(b).utcOffset(E);return D.$x.$timezone=_,D},u.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},u.tz.setDefault=function(f){c=f}}})}(ro)),ro.exports}var io={exports:{}},Am=io.exports,au;function wm(){return au||(au=1,function(e,t){(function(n,r){e.exports=r()})(Am,function(){var n="month",r="quarter";return function(i,o){var u=o.prototype;u.quarter=function(s){return this.$utils().u(s)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(s-1))};var c=u.add;u.add=function(s,a){return s=Number(s),this.$utils().p(a)===r?this.add(3*s,n):c.bind(this)(s,a)};var l=u.startOf;u.startOf=function(s,a){var d=this.$utils(),f=!!d.u(a)||a;if(d.p(s)===r){var h=this.quarter()-1;return f?this.month(3*h).startOf(n).startOf("day"):this.month(3*h+2).endOf(n).endOf("day")}return l.bind(this)(s,a)}}})}(io)),io.exports}var oo={exports:{}},Om=oo.exports,uu;function Tm(){return uu||(uu=1,function(e,t){(function(n,r){e.exports=r()})(Om,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,i=/\d/,o=/\d\d/,u=/\d\d?/,c=/\d*[^-_:/,()\s\d]+/,l={},s=function(_){return(_=+_)+(_>68?1900:2e3)},a=function(_){return function(g){this[_]=+g}},d=[/[+-]\d\d:?(\d\d)?|Z/,function(_){(this.zone||(this.zone={})).offset=function(g){if(!g||g==="Z")return 0;var v=g.match(/([+-]|\d\d)/g),b=60*v[1]+(+v[2]||0);return b===0?0:v[0]==="+"?-b:b}(_)}],f=function(_){var g=l[_];return g&&(g.indexOf?g:g.s.concat(g.f))},h=function(_,g){var v,b=l.meridiem;if(b){for(var E=1;E<=24;E+=1)if(_.indexOf(b(E,0,g))>-1){v=E>12;break}}else v=_===(g?"pm":"PM");return v},p={A:[c,function(_){this.afternoon=h(_,!1)}],a:[c,function(_){this.afternoon=h(_,!0)}],Q:[i,function(_){this.month=3*(_-1)+1}],S:[i,function(_){this.milliseconds=100*+_}],SS:[o,function(_){this.milliseconds=10*+_}],SSS:[/\d{3}/,function(_){this.milliseconds=+_}],s:[u,a("seconds")],ss:[u,a("seconds")],m:[u,a("minutes")],mm:[u,a("minutes")],H:[u,a("hours")],h:[u,a("hours")],HH:[u,a("hours")],hh:[u,a("hours")],D:[u,a("day")],DD:[o,a("day")],Do:[c,function(_){var g=l.ordinal,v=_.match(/\d+/);if(this.day=v[0],g)for(var b=1;b<=31;b+=1)g(b).replace(/\[|\]/g,"")===_&&(this.day=b)}],w:[u,a("week")],ww:[o,a("week")],M:[u,a("month")],MM:[o,a("month")],MMM:[c,function(_){var g=f("months"),v=(f("monthsShort")||g.map(function(b){return b.slice(0,3)})).indexOf(_)+1;if(v<1)throw new Error;this.month=v%12||v}],MMMM:[c,function(_){var g=f("months").indexOf(_)+1;if(g<1)throw new Error;this.month=g%12||g}],Y:[/[+-]?\d+/,a("year")],YY:[o,function(_){this.year=s(_)}],YYYY:[/\d{4}/,a("year")],Z:d,ZZ:d};function m(_){var g,v;g=_,v=l&&l.formats;for(var b=(_=g.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(k,R,L){var B=L&&L.toUpperCase();return R||v[L]||n[L]||v[B].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(N,G,Y){return G||Y.slice(1)})})).match(r),E=b.length,D=0;D<E;D+=1){var I=b[D],O=p[I],M=O&&O[0],T=O&&O[1];b[D]=T?{regex:M,parser:T}:I.replace(/^\[|\]$/g,"")}return function(k){for(var R={},L=0,B=0;L<E;L+=1){var N=b[L];if(typeof N=="string")B+=N.length;else{var G=N.regex,Y=N.parser,W=k.slice(B),V=G.exec(W)[0];Y.call(R,V),k=k.replace(V,"")}}return function(J){var te=J.afternoon;if(te!==void 0){var ie=J.hours;te?ie<12&&(J.hours+=12):ie===12&&(J.hours=0),delete J.afternoon}}(R),R}}return function(_,g,v){v.p.customParseFormat=!0,_&&_.parseTwoDigitYear&&(s=_.parseTwoDigitYear);var b=g.prototype,E=b.parse;b.parse=function(D){var I=D.date,O=D.utc,M=D.args;this.$u=O;var T=M[1];if(typeof T=="string"){var k=M[2]===!0,R=M[3]===!0,L=k||R,B=M[2];R&&(B=M[2]),l=this.$locale(),!k&&B&&(l=v.Ls[B]),this.$d=function(W,V,J,te){try{if(["x","X"].indexOf(V)>-1)return new Date((V==="X"?1e3:1)*W);var ie=m(V)(W),ue=ie.year,me=ie.month,Ee=ie.day,Oe=ie.hours,Ae=ie.minutes,U=ie.seconds,K=ie.milliseconds,X=ie.zone,Q=ie.week,fe=new Date,S=Ee||(ue||me?1:fe.getDate()),y=ue||fe.getFullYear(),C=0;ue&&!me||(C=me>0?me-1:fe.getMonth());var F,$=Oe||0,j=Ae||0,A=U||0,w=K||0;return X?new Date(Date.UTC(y,C,S,$,j,A,w+60*X.offset*1e3)):J?new Date(Date.UTC(y,C,S,$,j,A,w)):(F=new Date(y,C,S,$,j,A,w),Q&&(F=te(F).week(Q).toDate()),F)}catch(x){return new Date("")}}(I,T,O,v),this.init(),B&&B!==!0&&(this.$L=this.locale(B).$L),L&&I!=this.format(T)&&(this.$d=new Date("")),l={}}else if(T instanceof Array)for(var N=T.length,G=1;G<=N;G+=1){M[1]=T[G-1];var Y=v.apply(this,M);if(Y.isValid()){this.$d=Y.$d,this.$L=Y.$L,this.init();break}G===N&&(this.$d=new Date(""))}else E.call(this,D)}}})}(oo)),oo.exports}var so={exports:{}},Sm=so.exports,lu;function Cm(){return lu||(lu=1,function(e,t){(function(n,r){e.exports=r()})(Sm,function(){return function(n,r,i){var o=r.prototype,u=function(f){var h,p=f.date,m=f.utc,_={};if(!((h=p)===null||h instanceof Date||h instanceof Array||o.$utils().u(h)||h.constructor.name!=="Object")){if(!Object.keys(p).length)return new Date;var g=m?i.utc():i();Object.keys(p).forEach(function(T){var k,R;_[k=T,R=o.$utils().p(k),R==="date"?"day":R]=p[T]});var v=_.day||(_.year||_.month>=0?1:g.date()),b=_.year||g.year(),E=_.month>=0?_.month:_.year||_.day?0:g.month(),D=_.hour||0,I=_.minute||0,O=_.second||0,M=_.millisecond||0;return m?new Date(Date.UTC(b,E,v,D,I,O,M)):new Date(b,E,v,D,I,O,M)}return p},c=o.parse;o.parse=function(f){f.date=u.bind(this)(f),c.bind(this)(f)};var l=o.set,s=o.add,a=o.subtract,d=function(f,h,p,m){m===void 0&&(m=1);var _=Object.keys(h),g=this;return _.forEach(function(v){g=f.bind(g)(h[v]*m,v)}),g};o.set=function(f,h){return h=h===void 0?f:h,f.constructor.name==="Object"?d.bind(this)(function(p,m){return l.bind(this)(m,p)},h,f):l.bind(this)(f,h)},o.add=function(f,h){return f.constructor.name==="Object"?d.bind(this)(s,f,h):s.bind(this)(f,h)},o.subtract=function(f,h){return f.constructor.name==="Object"?d.bind(this)(s,f,h,-1):a.bind(this)(f,h)}}})}(so)),so.exports}var xn={},cu;function Im(){if(cu)return xn;cu=1;var e=xn&&xn.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(xn,"__esModule",{value:!0});const t=e(Nf());return xn.default=(n,r,i)=>{const o=r.prototype,u=o.format;o.format=function(c){if(!this.isValid())return u.bind(this)(c);const l=c||"YYYY-MM-DDTHH:mm:ssZ",{formats:s={}}=this.$locale(),a=l.replace(/\[([^\]]+)]|LT[S|X|Z]?|lyx|LYX|L{1,4}$/g,d=>{const f=this.minute(),p=this.year()===(0,t.default)().year(),m=f===0?"LTS":"LT";switch(d){case"LTZ":return s[m].replace("A","").replace("a","");case"LTX":return m;case"L":case"LL":case"LLL":case"LLLL":return s[c].replace("LTX",m);case"lyx":return s[p?"l":"ll"];case"LYX":return s[p?"L":"LL"].replace("LTX",m);default:return d}});return u.bind(this)(a)}},xn}var ti={},fu;function Mm(){return fu||(fu=1,Object.defineProperty(ti,"__esModule",{value:!0}),ti.default=(e,t,n)=>{const r={lastDay:"[yesterday]",sameDay:"[today]",nextDay:"[tomorrow]",lastWeek:"[last] dddd",sameWeek:"on dddd",nextWeek:"[next] dddd",sameYear:"l",sameElse:"ll",timeFormat:"%c, LTX"};t.prototype.calendar=function(i=void 0,o=!1,u){const c=Object.assign({},this.$locale().calendar||r,u),l=n(i||void 0).startOf("d"),s=this.startOf("d").diff(l,"d"),a=this.isoWeek()===l.isoWeek(),d=this.isoWeek()===l.isoWeek()-1,f=this.isoWeek()===l.isoWeek()+1,h=this.year()===l.year(),m=s===0?"sameDay":s===-1?"lastDay":s===1?"nextDay":a?"sameWeek":d?"lastWeek":f?"nextWeek":h?"sameYear":"sameElse",_=c[m]||r[m],g=typeof _=="function"?_.call(this,n()):this.format(_);return o?this.format(c.timeFormat).replace("%c",g):g},n.calendarFormat=function(i,o){const u=n(o||void 0).startOf("d"),c=n(i||void 0).startOf("d"),l=c.diff(u,"days",!0),s=c.isoWeek()===u.isoWeek(),a=c.isoWeek()===u.isoWeek()-1,d=c.isoWeek()===u.isoWeek()+1,f=c.year()===u.year();return l===0?"sameDay":l===-1?"lastDay":l===1?"nextDay":s?"sameWeek":a?"lastWeek":d?"nextWeek":f?"sameYear":"sameElse"}}),ti}var Pn={},du;function Lm(){if(du)return Pn;du=1;var e=Pn&&Pn.__rest||function(r,i){var o={};for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&i.indexOf(u)<0&&(o[u]=r[u]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,u=Object.getOwnPropertySymbols(r);c<u.length;c++)i.indexOf(u[c])<0&&Object.prototype.propertyIsEnumerable.call(r,u[c])&&(o[u[c]]=r[u[c]]);return o};Object.defineProperty(Pn,"__esModule",{value:!0});const t={year:0,month:0,day:0,hour:0,minute:0,second:0,firstDateWasLater:!1},n={nodiff:"",year:"y",years:"yy",month:"M",months:"MM",day:"d",days:"dd",hour:"h",hours:"hh",minute:"m",minutes:"mm",second:"s",seconds:"ss",delimiter:" "};return Pn.default=(r,i,o)=>{function u(c,l,s){const a=n[l+(c===1?"":"s")];return s[a].replace("%d",c)}i.prototype.humane=function(c=void 0,l=!0,s){const a=Object.assign({},this.$locale().humane||o.Ls.en.humane,s),{daysToRelative:d,startFrom:f,justnow:h,soon:p}=a,m=o(c),_=o.getDuration(this,m),{firstDateWasLater:g}=_,v=e(_,["firstDateWasLater"]),b=Math.abs(this.startOf("day").diff(m.startOf("day"),"day",!0)),E=Math.abs(this.diff(m,f.unit,!0))<=f.value,D=Object.keys(v).findIndex(k=>k===f.unit),I=D>=0?Object.keys(v)[D]:"second",O=a[n["".concat(I,"s")]].replace("%d","").trim(),M=g?p.replace("%u",O):h;return b<=d?E?M:o.durationToString(_,a):this.smartDateTime(m,l,a)},i.prototype.smartDateTime=function(c=void 0,l=!0,s){const a=Object.assign({},this.$locale().humane||o.Ls.en.humane,s),{daysToCalendar:d}=a,f=o(c),p=this.isSame(f,"year")?"l":"ll",m=Math.abs(this.startOf("day").diff(f.startOf("day"),"day",!0));return d>=m?this.calendar(f,l):this.format(l?p.toUpperCase():p)},o.durationToString=function(c,l){const s=Object.assign({},o.Ls[o.locale()].humane||o.Ls.en.humane,l),{skipFromUnit:a}=s,d=Object.assign({},t,c),{firstDateWasLater:f}=d,h=e(d,["firstDateWasLater"]),p=o.Ls[o.locale()].relativeTime||o.Ls.en.relativeTime,m=f?"future":"past",_=Object.keys(h).findIndex(b=>a.includes(b)),v=Object.keys(h).reduce((b,E,D)=>{const I=h[E];return I===0||b.length>0&&_!==-1&&_<=D||b.push(u(I,E,s)),b},[]).join(n.delimiter);return v?p[m].replace("%s",v):s.justnow},o.getDuration=function(c,l){let s=o(c),a=o(l),d;if(s.add(a.utcOffset()-s.utcOffset(),"minutes"),s.isSame(a))return{year:0,month:0,day:0,hour:0,minute:0,second:0,firstDateWasLater:!1};if(s.isAfter(a)){const v=s;s=a,a=v,d=!0}else d=!1;let f=a.year()-s.year(),h=a.month()-s.month(),p=a.date()-s.date(),m=a.hour()-s.hour(),_=a.minute()-s.minute(),g=a.second()-s.second();if(g<0&&(g=60+g,_--),_<0&&(_=60+_,m--),m<0&&(m=24+m,p--),p<0){const v=o("".concat(a.year(),"-").concat(a.month()+1>9?"":"0").concat(a.month()+1),"YYYY-MM").subtract(1,"M").daysInMonth();v<s.date()?p=v+p+(s.date()-v):p=v+p,h--}return h<0&&(h=12+h,f--),{year:f,month:h,day:p,hour:m,minute:_,second:g,firstDateWasLater:d}},o.getHumanePeriod=function(c,l,s,a=!0,d){var f;const h=o(s),p=o(c),m=o(l);if(!c&&!l)return"";if(!c&&l||c&&!l)return(c?p:m).smartDateTime(h,a);const _=Object.assign({},o.Ls[o.locale()].period||o.Ls.en.period,d),g=p.isSame(m,"day"),v=p.isSame(h,"day"),b=Math.abs(p.startOf("day").diff(h.startOf("day"),"day"))<=_.daysToCalendar,E=Math.abs(m.startOf("day").diff(h.startOf("day"),"day"))<=_.daysToCalendar,D=g&&p.format("a")===m.format("a")?"sameMeridiem":g?"sameDay":!(b||E)&&p.isSame(m,"month")?"sameMonth":p.isSame(m,"year")?"sameYear":"others",{startDate:I,endDate:O,startTime:M,endTime:T,format:k}=_[D];let R=a?M:I;const L=a?T:O,B=["LTZ","LTX","LTS","LT"],N=new RegExp("".concat(B.join("|")),"g"),G=(J,te)=>J.replace(/yx/gi,te?"":J[0]);g&&v&&!_.showSameDayToday&&(R=((f=(o.Ls[o.locale()]||o.Ls.en).formats[G(R,!0)].match(N))===null||f===void 0?void 0:f[0])||R);const Y=(J,te,ie)=>{const ue=G(te,J.isSame(h,"y"));if(B.includes(ue)||!ie)return J.format(ue);const Ee=ue.match(N),Oe=o.Ls[o.locale()].calendar.timeFormat||o.Ls.en.calendar.timeFormat,Ae=Oe.match(N),U=Ee&&Ae?{timeFormat:Oe.replace(Ae[0],Ee[0])}:void 0;return J.calendar(h,a,U)},W=R?Y(p,R,b):"",V=L?Y(m,L,E):"";return W&&V?k.replace("%ds",W).replace("%de",V):W||V}},Pn}var hu;function So(){if(hu)return ge;hu=1;var e=ge&&ge.__importDefault||function(v){return v&&v.__esModule?v:{default:v}};Object.defineProperty(ge,"__esModule",{value:!0}),ge.formatDateTime=ge.humane=ge.calendar=ge.customFormat=ge.objectSupport=ge.customParseFormat=ge.quarterOfYear=ge.timezone=ge.utc=ge.duration=ge.isBetween=ge.relativeTime=ge.weekday=ge.isoWeek=ge.localizedFormat=ge.updateLocale=ge.LOCALE_LANGUAGE=void 0;const t=e(Nf());ge.formatDateTime=t.default;const n=e(om());ge.updateLocale=n.default;const r=e(am());ge.localizedFormat=r.default;const i=e(lm());ge.isoWeek=i.default;const o=e(fm());ge.weekday=o.default;const u=e(hm());ge.relativeTime=u.default;const c=e(pm());ge.isBetween=c.default;const l=e(_m());ge.duration=l.default;const s=e(ym());ge.utc=s.default;const a=e(Em());ge.timezone=a.default;const d=e(wm());ge.quarterOfYear=d.default;const f=e(Tm());ge.customParseFormat=f.default;const h=e(Cm());ge.objectSupport=h.default;const p=e(Im());ge.customFormat=p.default;const m=e(Mm());ge.calendar=m.default;const _=e(Lm());ge.humane=_.default;const g=e(kf());return t.default.extend(o.default),t.default.extend(n.default),t.default.extend(r.default),t.default.extend(i.default),t.default.extend(u.default,{thresholds:[{l:"s",r:1},{l:"m",r:1},{l:"mm",r:59,d:"minute"},{l:"h",r:1},{l:"hh",r:23,d:"hour"},{l:"d",r:1},{l:"dd",r:29,d:"day"},{l:"M",r:1},{l:"MM",r:11,d:"month"},{l:"y",r:1},{l:"yy",d:"year"}]}),t.default.extend(c.default),t.default.extend(l.default),t.default.extend(s.default),t.default.extend(a.default),t.default.extend(d.default),t.default.extend(f.default),t.default.extend(h.default),t.default.extend(p.default),t.default.extend(m.default),t.default.extend(_.default),t.default.Ls.en=g.default,ge.LOCALE_LANGUAGE={en:"en",ms:"ms",id:"id",ja:"ja",ko:"ko","zh-Hans":"zh-cn","zh-Hant":"zh-tw","zh-Hant-TW":"zh-tw","zh-Hant-HK":"zh-hk"},ge}var mu;function Dm(){if(mu)return ei;mu=1,Object.defineProperty(ei,"__esModule",{value:!0});const e=So(),t={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:(n,r)=>{switch(r){case"W":return"".concat(n,"周");default:return"".concat(n,"日")}},weekStart:1,yearStart:4,formats:{LTS:"Ah点",LT:"Ah点mm分",L:"M月D日LTX",LL:"YYYY年M月D日LTX",LLL:"M月D日ddddLTX",LLLL:"YYYY年M月D日ddddLTX",l:"M月D日",ll:"YYYY年M月D日",lll:"M月D日dddd",llll:"YYYY年M月D日dddd"},calendar:{lastDay:"[昨天]",sameDay:"[今天]",nextDay:"[明天]",lastWeek:"[上]dddd",sameWeek:"dddd",nextWeek:"[下]dddd",sameYear:"l",sameElse:"ll",timeFormat:"%cLTX"},relativeTime:{future:"%s后",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},humane:{daysToRelative:0,daysToCalendar:1,skipFromUnit:"second",startFrom:{value:30,unit:"second"},soon:"几%u后",justnow:"刚刚",s:"1 秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},period:{daysToCalendar:1,showSameDayToday:!1,sameYear:{startDate:"lyx",endDate:"l",startTime:"LYX",endTime:"L",format:"%ds-%de"},sameMonth:{startDate:"lyx",endDate:"D日",startTime:"LYX",endTime:"D日LTX",format:"%ds-%de"},sameDay:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds-%de"},sameMeridiem:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTZ",format:"%ds-%de"},others:{startDate:"ll",endDate:"ll",startTime:"LL",endTime:"LL",format:"%ds-%de"}},meridiem:(n,r)=>{const i=n*100+r;return i<600?"凌晨":i<900?"早上":i<1100?"上午":i<1300?"中午":i<1800?"下午":"晚上"}};return e.formatDateTime.locale(t,null,!0),ei.default=t,ei}Dm();var ni={},pu;function Rm(){if(pu)return ni;pu=1,Object.defineProperty(ni,"__esModule",{value:!0});const e=So(),t={name:"zh-hk",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:(n,r)=>{switch(r){case"W":return"".concat(n,"週");default:return"".concat(n,"日")}},formats:{LTS:"Ah點",LT:"Ah點mm分",L:"M月D日LTX",LL:"YYYY年M月D日LTX",LLL:"M月D日ddddLTX",LLLL:"YYYY年M月D日ddddLTX",l:"M月D日",ll:"YYYY年M月D日",lll:"M月D日dddd",llll:"YYYY年M月D日dddd"},calendar:{lastDay:"[昨日]",sameDay:"[今日]",nextDay:"[明日]",lastWeek:"[上]dddd",sameWeek:"dddd",nextWeek:"[下]dddd",sameYear:"l",sameElse:"ll",timeFormat:"%cLTX"},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},humane:{daysToRelative:0,daysToCalendar:1,skipFromUnit:"second",startFrom:{value:30,unit:"second"},soon:"幾%u後",justnow:"啱啱",s:"1 秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},period:{daysToCalendar:1,showSameDayToday:!1,sameYear:{startDate:"lyx",endDate:"l",startTime:"LYX",endTime:"L",format:"%ds-%de"},sameMonth:{startDate:"lyx",endDate:"D日",startTime:"LYX",endTime:"D日LTX",format:"%ds-%de"},sameDay:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds-%de"},sameMeridiem:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTZ",format:"%ds-%de"},others:{startDate:"ll",endDate:"ll",startTime:"LL",endTime:"LL",format:"%ds-%de"}},meridiem:(n,r)=>{const i=n*100+r;return i<600?"凌晨":i<900?"早晨":i<1100?"上午":i<1300?"中午":i<1800?"下午":"晚上"}};return e.formatDateTime.locale(t,null,!0),ni.default=t,ni}Rm();var ri={},gu;function km(){if(gu)return ri;gu=1,Object.defineProperty(ri,"__esModule",{value:!0});const e=So(),t={name:"zh-tw",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:(n,r)=>{switch(r){case"W":return"".concat(n,"週");default:return"".concat(n,"日")}},formats:{LTS:"Ah點",LT:"Ah點mm分",L:"M月D日LTX",LL:"YYYY年M月D日LTX",LLL:"M月D日ddddLTX",LLLL:"YYYY年M月D日ddddLTX",l:"M月D日",ll:"YYYY年M月D日",lll:"M月D日dddd",llll:"YYYY年M月D日dddd"},calendar:{lastDay:"[昨天]",sameDay:"[今天]",nextDay:"[明天]",lastWeek:"[上]dddd",sameWeek:"dddd",nextWeek:"[下]dddd",sameYear:"l",sameElse:"ll",timeFormat:"%cLTX"},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},humane:{daysToRelative:0,daysToCalendar:1,skipFromUnit:"second",startFrom:{value:30,unit:"second"},soon:"幾%u後",justnow:"剛剛",s:"1 秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},period:{daysToCalendar:1,showSameDayToday:!1,sameYear:{startDate:"lyx",endDate:"l",startTime:"LYX",endTime:"L",format:"%ds-%de"},sameMonth:{startDate:"lyx",endDate:"D日",startTime:"LYX",endTime:"D日LTX",format:"%ds-%de"},sameDay:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds-%de"},sameMeridiem:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTZ",format:"%ds-%de"},others:{startDate:"ll",endDate:"ll",startTime:"LL",endTime:"LL",format:"%ds-%de"}},meridiem:(n,r)=>{const i=n*100+r;return i<600?"凌晨":i<900?"早上":i<1100?"上午":i<1300?"中午":i<1800?"下午":"晚上"}};return e.formatDateTime.locale(t,null,!0),ri.default=t,ri}km();var vr=So();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ba(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Re={},Vn=[],Ct=()=>{},Nm=()=>!1,Co=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ea=e=>e.startsWith("onUpdate:"),Ke=Object.assign,Aa=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},xm=Object.prototype.hasOwnProperty,Ie=(e,t)=>xm.call(e,t),le=Array.isArray,Wn=e=>Io(e)==="[object Map]",xf=e=>Io(e)==="[object Set]",he=e=>typeof e=="function",Pe=e=>typeof e=="string",Kt=e=>typeof e=="symbol",xe=e=>e!==null&&typeof e=="object",Pf=e=>(xe(e)||he(e))&&he(e.then)&&he(e.catch),Ff=Object.prototype.toString,Io=e=>Ff.call(e),Pm=e=>Io(e).slice(8,-1),Bf=e=>Io(e)==="[object Object]",wa=e=>Pe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,yr=ba(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Mo=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Fm=/-(\w)/g,At=Mo(e=>e.replace(Fm,(t,n)=>n?n.toUpperCase():"")),Bm=/\B([A-Z])/g,Dn=Mo(e=>e.replace(Bm,"-$1").toLowerCase()),Lo=Mo(e=>e.charAt(0).toUpperCase()+e.slice(1)),Zo=Mo(e=>e?"on".concat(Lo(e)):""),un=(e,t)=>!Object.is(e,t),ao=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},$f=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Us=e=>{const t=parseFloat(e);return isNaN(t)?e:t},$m=e=>{const t=Pe(e)?Number(e):NaN;return isNaN(t)?e:t};let _u;const Do=()=>_u||(_u=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function dt(e){if(le(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],i=Pe(r)?Ym(r):dt(r);if(i)for(const o in i)t[o]=i[o]}return t}else if(Pe(e)||xe(e))return e}const Hm=/;(?![^(]*\))/g,jm=/:([^]+)/,Um=/\/\*[^]*?\*\//g;function Ym(e){const t={};return e.replace(Um,"").split(Hm).forEach(n=>{if(n){const r=n.split(jm);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function $e(e){let t="";if(Pe(e))t=e;else if(le(e))for(let n=0;n<e.length;n++){const r=$e(e[n]);r&&(t+=r+" ")}else if(xe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Hf(e){if(!e)return null;let{class:t,style:n}=e;return t&&!Pe(t)&&(e.class=$e(t)),n&&(e.style=dt(n)),e}const qm="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Gm=ba(qm);function jf(e){return!!e||e===""}const Uf=e=>!!(e&&e.__v_isRef===!0),Le=e=>Pe(e)?e:e==null?"":le(e)||xe(e)&&(e.toString===Ff||!he(e.toString))?Uf(e)?Le(e.value):JSON.stringify(e,Yf,2):String(e),Yf=(e,t)=>Uf(t)?Yf(e,t.value):Wn(t)?{["Map(".concat(t.size,")")]:[...t.entries()].reduce((n,[r,i],o)=>(n[es(r,o)+" =>"]=i,n),{})}:xf(t)?{["Set(".concat(t.size,")")]:[...t.values()].map(n=>es(n))}:Kt(t)?es(t):xe(t)&&!le(t)&&!Bf(t)?String(t):t,es=(e,t="")=>{var n;return Kt(e)?"Symbol(".concat((n=e.description)!=null?n:t,")"):e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let it;class qf{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=it,!t&&it&&(this.index=(it.scopes||(it.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=it;try{return it=this,t()}finally{it=n}}}on(){it=this}off(){it=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function Oa(e){return new qf(e)}function Gf(){return it}function Vm(e,t=!1){it&&it.cleanups.push(e)}let ke;const ts=new WeakSet;class Vf{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,it&&it.active&&it.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ts.has(this)&&(ts.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||zf(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,vu(this),Xf(this);const t=ke,n=It;ke=this,It=!0;try{return this.fn()}finally{Kf(this),ke=t,It=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ca(t);this.deps=this.depsTail=void 0,vu(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ts.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ys(this)&&this.run()}get dirty(){return Ys(this)}}let Wf=0,br,Er;function zf(e,t=!1){if(e.flags|=8,t){e.next=Er,Er=e;return}e.next=br,br=e}function Ta(){Wf++}function Sa(){if(--Wf>0)return;if(Er){let t=Er;for(Er=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;br;){let t=br;for(br=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Xf(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Kf(e){let t,n=e.depsTail,r=n;for(;r;){const i=r.prevDep;r.version===-1?(r===n&&(n=i),Ca(r),Wm(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=i}e.deps=t,e.depsTail=n}function Ys(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Qf(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Qf(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ir))return;e.globalVersion=Ir;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Ys(e)){e.flags&=-3;return}const n=ke,r=It;ke=e,It=!0;try{Xf(e);const i=e.fn(e._value);(t.version===0||un(i,e._value))&&(e._value=i,t.version++)}catch(i){throw t.version++,i}finally{ke=n,It=r,Kf(e),e.flags&=-3}}function Ca(e,t=!1){const{dep:n,prevSub:r,nextSub:i}=e;if(r&&(r.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Ca(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Wm(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let It=!0;const Jf=[];function dn(){Jf.push(It),It=!1}function hn(){const e=Jf.pop();It=e===void 0?!0:e}function vu(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ke;ke=void 0;try{t()}finally{ke=n}}}let Ir=0;class zm{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ia{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ke||!It||ke===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ke)n=this.activeLink=new zm(ke,this),ke.deps?(n.prevDep=ke.depsTail,ke.depsTail.nextDep=n,ke.depsTail=n):ke.deps=ke.depsTail=n,Zf(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ke.depsTail,n.nextDep=void 0,ke.depsTail.nextDep=n,ke.depsTail=n,ke.deps===n&&(ke.deps=r)}return n}trigger(t){this.version++,Ir++,this.notify(t)}notify(t){Ta();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Sa()}}}function Zf(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Zf(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const mo=new WeakMap,Sn=Symbol(""),qs=Symbol(""),Mr=Symbol("");function Je(e,t,n){if(It&&ke){let r=mo.get(e);r||mo.set(e,r=new Map);let i=r.get(n);i||(r.set(n,i=new Ia),i.map=r,i.key=n),i.track()}}function Vt(e,t,n,r,i,o){const u=mo.get(e);if(!u){Ir++;return}const c=l=>{l&&l.trigger()};if(Ta(),t==="clear")u.forEach(c);else{const l=le(e),s=l&&wa(n);if(l&&n==="length"){const a=Number(r);u.forEach((d,f)=>{(f==="length"||f===Mr||!Kt(f)&&f>=a)&&c(d)})}else switch((n!==void 0||u.has(void 0))&&c(u.get(n)),s&&c(u.get(Mr)),t){case"add":l?s&&c(u.get("length")):(c(u.get(Sn)),Wn(e)&&c(u.get(qs)));break;case"delete":l||(c(u.get(Sn)),Wn(e)&&c(u.get(qs)));break;case"set":Wn(e)&&c(u.get(Sn));break}}Sa()}function Xm(e,t){const n=mo.get(e);return n&&n.get(t)}function Fn(e){const t=we(e);return t===e?t:(Je(t,"iterate",Mr),Et(e)?t:t.map(Ze))}function Ro(e){return Je(e=we(e),"iterate",Mr),e}const Km={__proto__:null,[Symbol.iterator](){return ns(this,Symbol.iterator,Ze)},concat(...e){return Fn(this).concat(...e.map(t=>le(t)?Fn(t):t))},entries(){return ns(this,"entries",e=>(e[1]=Ze(e[1]),e))},every(e,t){return Ht(this,"every",e,t,void 0,arguments)},filter(e,t){return Ht(this,"filter",e,t,n=>n.map(Ze),arguments)},find(e,t){return Ht(this,"find",e,t,Ze,arguments)},findIndex(e,t){return Ht(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ht(this,"findLast",e,t,Ze,arguments)},findLastIndex(e,t){return Ht(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ht(this,"forEach",e,t,void 0,arguments)},includes(...e){return rs(this,"includes",e)},indexOf(...e){return rs(this,"indexOf",e)},join(e){return Fn(this).join(e)},lastIndexOf(...e){return rs(this,"lastIndexOf",e)},map(e,t){return Ht(this,"map",e,t,void 0,arguments)},pop(){return rr(this,"pop")},push(...e){return rr(this,"push",e)},reduce(e,...t){return yu(this,"reduce",e,t)},reduceRight(e,...t){return yu(this,"reduceRight",e,t)},shift(){return rr(this,"shift")},some(e,t){return Ht(this,"some",e,t,void 0,arguments)},splice(...e){return rr(this,"splice",e)},toReversed(){return Fn(this).toReversed()},toSorted(e){return Fn(this).toSorted(e)},toSpliced(...e){return Fn(this).toSpliced(...e)},unshift(...e){return rr(this,"unshift",e)},values(){return ns(this,"values",Ze)}};function ns(e,t,n){const r=Ro(e),i=r[t]();return r!==e&&!Et(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=n(o.value)),o}),i}const Qm=Array.prototype;function Ht(e,t,n,r,i,o){const u=Ro(e),c=u!==e&&!Et(e),l=u[t];if(l!==Qm[t]){const d=l.apply(e,o);return c?Ze(d):d}let s=n;u!==e&&(c?s=function(d,f){return n.call(this,Ze(d),f,e)}:n.length>2&&(s=function(d,f){return n.call(this,d,f,e)}));const a=l.call(u,s,r);return c&&i?i(a):a}function yu(e,t,n,r){const i=Ro(e);let o=n;return i!==e&&(Et(e)?n.length>3&&(o=function(u,c,l){return n.call(this,u,c,l,e)}):o=function(u,c,l){return n.call(this,u,Ze(c),l,e)}),i[t](o,...r)}function rs(e,t,n){const r=we(e);Je(r,"iterate",Mr);const i=r[t](...n);return(i===-1||i===!1)&&Da(n[0])?(n[0]=we(n[0]),r[t](...n)):i}function rr(e,t,n=[]){dn(),Ta();const r=we(e)[t].apply(e,n);return Sa(),hn(),r}const Jm=ba("__proto__,__v_isRef,__isVue"),ed=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Kt));function Zm(e){Kt(e)||(e=String(e));const t=we(this);return Je(t,"has",e),t.hasOwnProperty(e)}class td{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(i?o?lp:od:o?id:rd).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const u=le(t);if(!i){let l;if(u&&(l=Km[n]))return l;if(n==="hasOwnProperty")return Zm}const c=Reflect.get(t,n,je(t)?t:r);return(Kt(n)?ed.has(n):Jm(n))||(i||Je(t,"get",n),o)?c:je(c)?u&&wa(n)?c:c.value:xe(c)?i?ad(c):In(c):c}}class nd extends td{constructor(t=!1){super(!1,t)}set(t,n,r,i){let o=t[n];if(!this._isShallow){const l=Mn(o);if(!Et(r)&&!Mn(r)&&(o=we(o),r=we(r)),!le(t)&&je(o)&&!je(r))return l?!1:(o.value=r,!0)}const u=le(t)&&wa(n)?Number(n)<t.length:Ie(t,n),c=Reflect.set(t,n,r,je(t)?t:i);return t===we(i)&&(u?un(r,o)&&Vt(t,"set",n,r):Vt(t,"add",n,r)),c}deleteProperty(t,n){const r=Ie(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&r&&Vt(t,"delete",n,void 0),i}has(t,n){const r=Reflect.has(t,n);return(!Kt(n)||!ed.has(n))&&Je(t,"has",n),r}ownKeys(t){return Je(t,"iterate",le(t)?"length":Sn),Reflect.ownKeys(t)}}class ep extends td{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const tp=new nd,np=new ep,rp=new nd(!0);const Gs=e=>e,ii=e=>Reflect.getPrototypeOf(e);function ip(e,t,n){return function(...r){const i=this.__v_raw,o=we(i),u=Wn(o),c=e==="entries"||e===Symbol.iterator&&u,l=e==="keys"&&u,s=i[e](...r),a=n?Gs:t?Vs:Ze;return!t&&Je(o,"iterate",l?qs:Sn),{next(){const{value:d,done:f}=s.next();return f?{value:d,done:f}:{value:c?[a(d[0]),a(d[1])]:a(d),done:f}},[Symbol.iterator](){return this}}}}function oi(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function op(e,t){const n={get(i){const o=this.__v_raw,u=we(o),c=we(i);e||(un(i,c)&&Je(u,"get",i),Je(u,"get",c));const{has:l}=ii(u),s=t?Gs:e?Vs:Ze;if(l.call(u,i))return s(o.get(i));if(l.call(u,c))return s(o.get(c));o!==u&&o.get(i)},get size(){const i=this.__v_raw;return!e&&Je(we(i),"iterate",Sn),Reflect.get(i,"size",i)},has(i){const o=this.__v_raw,u=we(o),c=we(i);return e||(un(i,c)&&Je(u,"has",i),Je(u,"has",c)),i===c?o.has(i):o.has(i)||o.has(c)},forEach(i,o){const u=this,c=u.__v_raw,l=we(c),s=t?Gs:e?Vs:Ze;return!e&&Je(l,"iterate",Sn),c.forEach((a,d)=>i.call(o,s(a),s(d),u))}};return Ke(n,e?{add:oi("add"),set:oi("set"),delete:oi("delete"),clear:oi("clear")}:{add(i){!t&&!Et(i)&&!Mn(i)&&(i=we(i));const o=we(this);return ii(o).has.call(o,i)||(o.add(i),Vt(o,"add",i,i)),this},set(i,o){!t&&!Et(o)&&!Mn(o)&&(o=we(o));const u=we(this),{has:c,get:l}=ii(u);let s=c.call(u,i);s||(i=we(i),s=c.call(u,i));const a=l.call(u,i);return u.set(i,o),s?un(o,a)&&Vt(u,"set",i,o):Vt(u,"add",i,o),this},delete(i){const o=we(this),{has:u,get:c}=ii(o);let l=u.call(o,i);l||(i=we(i),l=u.call(o,i)),c&&c.call(o,i);const s=o.delete(i);return l&&Vt(o,"delete",i,void 0),s},clear(){const i=we(this),o=i.size!==0,u=i.clear();return o&&Vt(i,"clear",void 0,void 0),u}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=ip(i,e,t)}),n}function Ma(e,t){const n=op(e,t);return(r,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?r:Reflect.get(Ie(n,i)&&i in r?n:r,i,o)}const sp={get:Ma(!1,!1)},ap={get:Ma(!1,!0)},up={get:Ma(!0,!1)};const rd=new WeakMap,id=new WeakMap,od=new WeakMap,lp=new WeakMap;function cp(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function fp(e){return e.__v_skip||!Object.isExtensible(e)?0:cp(Pm(e))}function In(e){return Mn(e)?e:La(e,!1,tp,sp,rd)}function sd(e){return La(e,!1,rp,ap,id)}function ad(e){return La(e,!0,np,up,od)}function La(e,t,n,r,i){if(!xe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=i.get(e);if(o)return o;const u=fp(e);if(u===0)return e;const c=new Proxy(e,u===2?r:n);return i.set(e,c),c}function ln(e){return Mn(e)?ln(e.__v_raw):!!(e&&e.__v_isReactive)}function Mn(e){return!!(e&&e.__v_isReadonly)}function Et(e){return!!(e&&e.__v_isShallow)}function Da(e){return e?!!e.__v_raw:!1}function we(e){const t=e&&e.__v_raw;return t?we(t):e}function Ra(e){return!Ie(e,"__v_skip")&&Object.isExtensible(e)&&$f(e,"__v_skip",!0),e}const Ze=e=>xe(e)?In(e):e,Vs=e=>xe(e)?ad(e):e;function je(e){return e?e.__v_isRef===!0:!1}function _e(e){return ld(e,!1)}function ud(e){return ld(e,!0)}function ld(e,t){return je(e)?e:new dp(e,t)}class dp{constructor(t,n){this.dep=new Ia,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:we(t),this._value=n?t:Ze(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||Et(t)||Mn(t);t=r?t:we(t),un(t,n)&&(this._rawValue=t,this._value=r?t:Ze(t),this.dep.trigger())}}function Z(e){return je(e)?e.value:e}const hp={get:(e,t,n)=>t==="__v_raw"?e:Z(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const i=e[t];return je(i)&&!je(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};function cd(e){return ln(e)?e:new Proxy(e,hp)}function ko(e){const t=le(e)?new Array(e.length):{};for(const n in e)t[n]=pp(e,n);return t}class mp{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Xm(we(this._object),this._key)}}function pp(e,t,n){const r=e[t];return je(r)?r:new mp(e,t,n)}class gp{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ia(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ir-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ke!==this)return zf(this,!0),!0}get value(){const t=this.dep.track();return Qf(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function _p(e,t,n=!1){let r,i;return he(e)?r=e:(r=e.get,i=e.set),new gp(r,i,n)}const si={},po=new WeakMap;let wn;function vp(e,t=!1,n=wn){if(n){let r=po.get(n);r||po.set(n,r=[]),r.push(e)}}function yp(e,t,n=Re){const{immediate:r,deep:i,once:o,scheduler:u,augmentJob:c,call:l}=n,s=E=>i?E:Et(E)||i===!1||i===0?Wt(E,1):Wt(E);let a,d,f,h,p=!1,m=!1;if(je(e)?(d=()=>e.value,p=Et(e)):ln(e)?(d=()=>s(e),p=!0):le(e)?(m=!0,p=e.some(E=>ln(E)||Et(E)),d=()=>e.map(E=>{if(je(E))return E.value;if(ln(E))return s(E);if(he(E))return l?l(E,2):E()})):he(e)?t?d=l?()=>l(e,2):e:d=()=>{if(f){dn();try{f()}finally{hn()}}const E=wn;wn=a;try{return l?l(e,3,[h]):e(h)}finally{wn=E}}:d=Ct,t&&i){const E=d,D=i===!0?1/0:i;d=()=>Wt(E(),D)}const _=Gf(),g=()=>{a.stop(),_&&_.active&&Aa(_.effects,a)};if(o&&t){const E=t;t=(...D)=>{E(...D),g()}}let v=m?new Array(e.length).fill(si):si;const b=E=>{if(!(!(a.flags&1)||!a.dirty&&!E))if(t){const D=a.run();if(i||p||(m?D.some((I,O)=>un(I,v[O])):un(D,v))){f&&f();const I=wn;wn=a;try{const O=[D,v===si?void 0:m&&v[0]===si?[]:v,h];l?l(t,3,O):t(...O),v=D}finally{wn=I}}}else a.run()};return c&&c(b),a=new Vf(d),a.scheduler=u?()=>u(b,!1):b,h=E=>vp(E,!1,a),f=a.onStop=()=>{const E=po.get(a);if(E){if(l)l(E,4);else for(const D of E)D();po.delete(a)}},t?r?b(!0):v=a.run():u?u(b.bind(null,!0),!0):a.run(),g.pause=a.pause.bind(a),g.resume=a.resume.bind(a),g.stop=g,g}function Wt(e,t=1/0,n){if(t<=0||!xe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,je(e))Wt(e.value,t,n);else if(le(e))for(let r=0;r<e.length;r++)Wt(e[r],t,n);else if(xf(e)||Wn(e))e.forEach(r=>{Wt(r,t,n)});else if(Bf(e)){for(const r in e)Wt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Wt(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ur(e,t,n,r){try{return r?e(...r):e()}catch(i){No(i,t,n)}}function Mt(e,t,n,r){if(he(e)){const i=Ur(e,t,n,r);return i&&Pf(i)&&i.catch(o=>{No(o,t,n)}),i}if(le(e)){const i=[];for(let o=0;o<e.length;o++)i.push(Mt(e[o],t,n,r));return i}}function No(e,t,n,r=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||Re;if(t){let c=t.parent;const l=t.proxy,s="https://vuejs.org/error-reference/#runtime-".concat(n);for(;c;){const a=c.ec;if(a){for(let d=0;d<a.length;d++)if(a[d](e,l,s)===!1)return}c=c.parent}if(o){dn(),Ur(o,null,10,[e,l,s]),hn();return}}bp(e,n,i,r,u)}function bp(e,t,n,r=!0,i=!1){if(i)throw e;console.error(e)}const ot=[];let xt=-1;const zn=[];let rn=null,Un=0;const fd=Promise.resolve();let go=null;function xo(e){const t=go||fd;return e?t.then(this?e.bind(this):e):t}function Ep(e){let t=xt+1,n=ot.length;for(;t<n;){const r=t+n>>>1,i=ot[r],o=Lr(i);o<e||o===e&&i.flags&2?t=r+1:n=r}return t}function ka(e){if(!(e.flags&1)){const t=Lr(e),n=ot[ot.length-1];!n||!(e.flags&2)&&t>=Lr(n)?ot.push(e):ot.splice(Ep(t),0,e),e.flags|=1,dd()}}function dd(){go||(go=fd.then(pd))}function hd(e){le(e)?zn.push(...e):rn&&e.id===-1?rn.splice(Un+1,0,e):e.flags&1||(zn.push(e),e.flags|=1),dd()}function bu(e,t,n=xt+1){for(;n<ot.length;n++){const r=ot[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;ot.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function md(e){if(zn.length){const t=[...new Set(zn)].sort((n,r)=>Lr(n)-Lr(r));if(zn.length=0,rn){rn.push(...t);return}for(rn=t,Un=0;Un<rn.length;Un++){const n=rn[Un];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}rn=null,Un=0}}const Lr=e=>e.id==null?e.flags&2?-1:1/0:e.id;function pd(e){try{for(xt=0;xt<ot.length;xt++){const t=ot[xt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ur(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;xt<ot.length;xt++){const t=ot[xt];t&&(t.flags&=-2)}xt=-1,ot.length=0,md(),go=null,(ot.length||zn.length)&&pd()}}let Ve=null,gd=null;function _o(e){const t=Ve;return Ve=e,gd=e&&e.type.__scopeId||null,t}function He(e,t=Ve,n){if(!t||e._n)return e;const r=(...i)=>{r._d&&Du(-1);const o=_o(t);let u;try{u=e(...i)}finally{_o(o),r._d&&Du(1)}return u};return r._n=!0,r._c=!0,r._d=!0,r}function Ws(e,t){if(Ve===null)return e;const n=Ho(Ve),r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,u,c,l=Re]=t[i];o&&(he(o)&&(o={mounted:o,updated:o}),o.deep&&Wt(u),r.push({dir:o,instance:n,value:u,oldValue:void 0,arg:c,modifiers:l}))}return e}function _n(e,t,n,r){const i=e.dirs,o=t&&t.dirs;for(let u=0;u<i.length;u++){const c=i[u];o&&(c.oldValue=o[u].value);let l=c.dir[r];l&&(dn(),Mt(l,n,8,[e.el,c,e,t]),hn())}}const Ap=Symbol("_vte"),_d=e=>e.__isTeleport,on=Symbol("_leaveCb"),ai=Symbol("_enterCb");function wp(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return $t(()=>{e.isMounted=!0}),Yr(()=>{e.isUnmounting=!0}),e}const yt=[Function,Array],vd={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:yt,onEnter:yt,onAfterEnter:yt,onEnterCancelled:yt,onBeforeLeave:yt,onLeave:yt,onAfterLeave:yt,onLeaveCancelled:yt,onBeforeAppear:yt,onAppear:yt,onAfterAppear:yt,onAppearCancelled:yt},yd=e=>{const t=e.subTree;return t.component?yd(t.component):t},Op={name:"BaseTransition",props:vd,setup(e,{slots:t}){const n=fn(),r=wp();return()=>{const i=t.default&&Ad(t.default(),!0);if(!i||!i.length)return;const o=bd(i),u=we(e),{mode:c}=u;if(r.isLeaving)return is(o);const l=Eu(o);if(!l)return is(o);let s=zs(l,u,r,n,d=>s=d);l.type!==st&&Dr(l,s);let a=n.subTree&&Eu(n.subTree);if(a&&a.type!==st&&!On(l,a)&&yd(n).type!==st){let d=zs(a,u,r,n);if(Dr(a,d),c==="out-in"&&l.type!==st)return r.isLeaving=!0,d.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete d.afterLeave,a=void 0},is(o);c==="in-out"&&l.type!==st?d.delayLeave=(f,h,p)=>{const m=Ed(r,a);m[String(a.key)]=a,f[on]=()=>{h(),f[on]=void 0,delete s.delayedLeave,a=void 0},s.delayedLeave=()=>{p(),delete s.delayedLeave,a=void 0}}:a=void 0}else a&&(a=void 0);return o}}};function bd(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==st){t=n;break}}return t}const Tp=Op;function Ed(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function zs(e,t,n,r,i){const{appear:o,mode:u,persisted:c=!1,onBeforeEnter:l,onEnter:s,onAfterEnter:a,onEnterCancelled:d,onBeforeLeave:f,onLeave:h,onAfterLeave:p,onLeaveCancelled:m,onBeforeAppear:_,onAppear:g,onAfterAppear:v,onAppearCancelled:b}=t,E=String(e.key),D=Ed(n,e),I=(T,k)=>{T&&Mt(T,r,9,k)},O=(T,k)=>{const R=k[1];I(T,k),le(T)?T.every(L=>L.length<=1)&&R():T.length<=1&&R()},M={mode:u,persisted:c,beforeEnter(T){let k=l;if(!n.isMounted)if(o)k=_||l;else return;T[on]&&T[on](!0);const R=D[E];R&&On(e,R)&&R.el[on]&&R.el[on](),I(k,[T])},enter(T){let k=s,R=a,L=d;if(!n.isMounted)if(o)k=g||s,R=v||a,L=b||d;else return;let B=!1;const N=T[ai]=G=>{B||(B=!0,G?I(L,[T]):I(R,[T]),M.delayedLeave&&M.delayedLeave(),T[ai]=void 0)};k?O(k,[T,N]):N()},leave(T,k){const R=String(e.key);if(T[ai]&&T[ai](!0),n.isUnmounting)return k();I(f,[T]);let L=!1;const B=T[on]=N=>{L||(L=!0,k(),N?I(m,[T]):I(p,[T]),T[on]=void 0,D[R]===e&&delete D[R])};D[R]=e,h?O(h,[T,B]):B()},clone(T){const k=zs(T,t,n,r,i);return i&&i(k),k}};return M}function is(e){if(Po(e))return e=cn(e),e.children=null,e}function Eu(e){if(!Po(e))return _d(e.type)&&e.children?bd(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&he(n.default))return n.default()}}function Dr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Dr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ad(e,t=!1,n){let r=[],i=0;for(let o=0;o<e.length;o++){let u=e[o];const c=n==null?u.key:String(n)+String(u.key!=null?u.key:o);u.type===Be?(u.patchFlag&128&&i++,r=r.concat(Ad(u.children,t,c))):(t||u.type!==st)&&r.push(c!=null?cn(u,{key:c}):u)}if(i>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Ge(e,t){return he(e)?Ke({name:e.name},t,{setup:e}):e}function wd(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function vo(e,t,n,r,i=!1){if(le(e)){e.forEach((p,m)=>vo(p,t&&(le(t)?t[m]:t),n,r,i));return}if(Xn(r)&&!i){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&vo(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?Ho(r.component):r.el,u=i?null:o,{i:c,r:l}=e,s=t&&t.r,a=c.refs===Re?c.refs={}:c.refs,d=c.setupState,f=we(d),h=d===Re?()=>!1:p=>Ie(f,p);if(s!=null&&s!==l&&(Pe(s)?(a[s]=null,h(s)&&(d[s]=null)):je(s)&&(s.value=null)),he(l))Ur(l,c,12,[u,a]);else{const p=Pe(l),m=je(l);if(p||m){const _=()=>{if(e.f){const g=p?h(l)?d[l]:a[l]:l.value;i?le(g)&&Aa(g,o):le(g)?g.includes(o)||g.push(o):p?(a[l]=[o],h(l)&&(d[l]=a[l])):(l.value=[o],e.k&&(a[e.k]=l.value))}else p?(a[l]=u,h(l)&&(d[l]=u)):m&&(l.value=u,e.k&&(a[e.k]=u))};u?(_.id=-1,ft(_,n)):_()}}}Do().requestIdleCallback;Do().cancelIdleCallback;const Xn=e=>!!e.type.__asyncLoader,Po=e=>e.type.__isKeepAlive;function Sp(e,t){Od(e,"a",t)}function Cp(e,t){Od(e,"da",t)}function Od(e,t,n=Xe){const r=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Fo(t,r,n),n){let i=n.parent;for(;i&&i.parent;)Po(i.parent.vnode)&&Ip(r,t,n,i),i=i.parent}}function Ip(e,t,n,r){const i=Fo(t,e,r,!0);tr(()=>{Aa(r[t],i)},n)}function Fo(e,t,n=Xe,r=!1){if(n){const i=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...u)=>{dn();const c=Vr(n),l=Mt(t,n,e,u);return c(),hn(),l});return r?i.unshift(o):i.push(o),o}}const Qt=e=>(t,n=Xe)=>{(!Nr||e==="sp")&&Fo(e,(...r)=>t(...r),n)},Bo=Qt("bm"),$t=Qt("m"),Td=Qt("bu"),Mp=Qt("u"),Yr=Qt("bum"),tr=Qt("um"),Lp=Qt("sp"),Dp=Qt("rtg"),Rp=Qt("rtc");function kp(e,t=Xe){Fo("ec",e,t)}const Np="components",Sd=Symbol.for("v-ndc");function xp(e){return Pe(e)?Pp(Np,e,!1)||e:e||Sd}function Pp(e,t,n=!0,r=!1){const i=Ve||Xe;if(i){const o=i.type;{const c=wg(o,!1);if(c&&(c===t||c===At(t)||c===Lo(At(t))))return o}const u=Au(i[e]||o[e],t)||Au(i.appContext[e],t);return!u&&r?o:u}}function Au(e,t){return e&&(e[t]||e[At(t)]||e[Lo(At(t))])}function qr(e,t,n,r){let i;const o=n,u=le(e);if(u||Pe(e)){const c=u&&ln(e);let l=!1;c&&(l=!Et(e),e=Ro(e)),i=new Array(e.length);for(let s=0,a=e.length;s<a;s++)i[s]=t(l?Ze(e[s]):e[s],s,void 0,o)}else if(typeof e=="number"){i=new Array(e);for(let c=0;c<e;c++)i[c]=t(c+1,c,void 0,o)}else if(xe(e))if(e[Symbol.iterator])i=Array.from(e,(c,l)=>t(c,l,void 0,o));else{const c=Object.keys(e);i=new Array(c.length);for(let l=0,s=c.length;l<s;l++){const a=c[l];i[l]=t(e[a],a,l,o)}}else i=[];return i}function Ln(e,t){for(let n=0;n<t.length;n++){const r=t[n];if(le(r))for(let i=0;i<r.length;i++)e[r[i].name]=r[i].fn;else r&&(e[r.name]=r.key?(...i)=>{const o=r.fn(...i);return o&&(o.key=r.key),o}:r.fn)}return e}function Qe(e,t,n={},r,i){if(Ve.ce||Ve.parent&&Xn(Ve.parent)&&Ve.parent.ce)return t!=="default"&&(n.name=t),ne(),et(Be,null,[be("slot",n,r&&r())],64);let o=e[t];o&&o._c&&(o._d=!1),ne();const u=o&&Cd(o(n)),c=n.key||u&&u.key,l=et(Be,{key:(c&&!Kt(c)?c:"_".concat(t))+(!u&&r?"_fb":"")},u||(r?r():[]),u&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function Cd(e){return e.some(t=>kr(t)?!(t.type===st||t.type===Be&&!Cd(t.children)):!0)?e:null}const Xs=e=>e?Kd(e)?Ho(e):Xs(e.parent):null,Ar=Ke(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Xs(e.parent),$root:e=>Xs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ld(e),$forceUpdate:e=>e.f||(e.f=()=>{ka(e.update)}),$nextTick:e=>e.n||(e.n=xo.bind(e.proxy)),$watch:e=>sg.bind(e)}),os=(e,t)=>e!==Re&&!e.__isScriptSetup&&Ie(e,t),Fp={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:i,props:o,accessCache:u,type:c,appContext:l}=e;let s;if(t[0]!=="$"){const h=u[t];if(h!==void 0)switch(h){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return o[t]}else{if(os(r,t))return u[t]=1,r[t];if(i!==Re&&Ie(i,t))return u[t]=2,i[t];if((s=e.propsOptions[0])&&Ie(s,t))return u[t]=3,o[t];if(n!==Re&&Ie(n,t))return u[t]=4,n[t];Ks&&(u[t]=0)}}const a=Ar[t];let d,f;if(a)return t==="$attrs"&&Je(e.attrs,"get",""),a(e);if((d=c.__cssModules)&&(d=d[t]))return d;if(n!==Re&&Ie(n,t))return u[t]=4,n[t];if(f=l.config.globalProperties,Ie(f,t))return f[t]},set({_:e},t,n){const{data:r,setupState:i,ctx:o}=e;return os(i,t)?(i[t]=n,!0):r!==Re&&Ie(r,t)?(r[t]=n,!0):Ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:o}},u){let c;return!!n[u]||e!==Re&&Ie(e,u)||os(t,u)||(c=o[0])&&Ie(c,u)||Ie(r,u)||Ie(Ar,u)||Ie(i.config.globalProperties,u)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Ie(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Bp(){return Id().slots}function $p(){return Id().attrs}function Id(){const e=fn();return e.setupContext||(e.setupContext=Jd(e))}function wu(e){return le(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ks=!0;function Hp(e){const t=Ld(e),n=e.proxy,r=e.ctx;Ks=!1,t.beforeCreate&&Ou(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:u,watch:c,provide:l,inject:s,created:a,beforeMount:d,mounted:f,beforeUpdate:h,updated:p,activated:m,deactivated:_,beforeDestroy:g,beforeUnmount:v,destroyed:b,unmounted:E,render:D,renderTracked:I,renderTriggered:O,errorCaptured:M,serverPrefetch:T,expose:k,inheritAttrs:R,components:L,directives:B,filters:N}=t;if(s&&jp(s,r,null),u)for(const W in u){const V=u[W];he(V)&&(r[W]=V.bind(n))}if(i){const W=i.call(n,n);xe(W)&&(e.data=In(W))}if(Ks=!0,o)for(const W in o){const V=o[W],J=he(V)?V.bind(n,n):he(V.get)?V.get.bind(n,n):Ct,te=!he(V)&&he(V.set)?V.set.bind(n):Ct,ie=de({get:J,set:te});Object.defineProperty(r,W,{enumerable:!0,configurable:!0,get:()=>ie.value,set:ue=>ie.value=ue})}if(c)for(const W in c)Md(c[W],r,n,W);if(l){const W=he(l)?l.call(n):l;Reflect.ownKeys(W).forEach(V=>{uo(V,W[V])})}a&&Ou(a,e,"c");function Y(W,V){le(V)?V.forEach(J=>W(J.bind(n))):V&&W(V.bind(n))}if(Y(Bo,d),Y($t,f),Y(Td,h),Y(Mp,p),Y(Sp,m),Y(Cp,_),Y(kp,M),Y(Rp,I),Y(Dp,O),Y(Yr,v),Y(tr,E),Y(Lp,T),le(k))if(k.length){const W=e.exposed||(e.exposed={});k.forEach(V=>{Object.defineProperty(W,V,{get:()=>n[V],set:J=>n[V]=J})})}else e.exposed||(e.exposed={});D&&e.render===Ct&&(e.render=D),R!=null&&(e.inheritAttrs=R),L&&(e.components=L),B&&(e.directives=B),T&&wd(e)}function jp(e,t,n=Ct){le(e)&&(e=Qs(e));for(const r in e){const i=e[r];let o;xe(i)?"default"in i?o=mt(i.from||r,i.default,!0):o=mt(i.from||r):o=mt(i),je(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:u=>o.value=u}):t[r]=o}}function Ou(e,t,n){Mt(le(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Md(e,t,n,r){let i=r.includes(".")?qd(n,r):()=>n[r];if(Pe(e)){const o=t[e];he(o)&&Ft(i,o)}else if(he(e))Ft(i,e.bind(n));else if(xe(e))if(le(e))e.forEach(o=>Md(o,t,n,r));else{const o=he(e.handler)?e.handler.bind(n):t[e.handler];he(o)&&Ft(i,o,e)}}function Ld(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:u}}=e.appContext,c=o.get(t);let l;return c?l=c:!i.length&&!n&&!r?l=t:(l={},i.length&&i.forEach(s=>yo(l,s,u,!0)),yo(l,t,u)),xe(t)&&o.set(t,l),l}function yo(e,t,n,r=!1){const{mixins:i,extends:o}=t;o&&yo(e,o,n,!0),i&&i.forEach(u=>yo(e,u,n,!0));for(const u in t)if(!(r&&u==="expose")){const c=Up[u]||n&&n[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const Up={data:Tu,props:Su,emits:Su,methods:gr,computed:gr,beforeCreate:nt,created:nt,beforeMount:nt,mounted:nt,beforeUpdate:nt,updated:nt,beforeDestroy:nt,beforeUnmount:nt,destroyed:nt,unmounted:nt,activated:nt,deactivated:nt,errorCaptured:nt,serverPrefetch:nt,components:gr,directives:gr,watch:qp,provide:Tu,inject:Yp};function Tu(e,t){return t?e?function(){return Ke(he(e)?e.call(this,this):e,he(t)?t.call(this,this):t)}:t:e}function Yp(e,t){return gr(Qs(e),Qs(t))}function Qs(e){if(le(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function nt(e,t){return e?[...new Set([].concat(e,t))]:t}function gr(e,t){return e?Ke(Object.create(null),e,t):t}function Su(e,t){return e?le(e)&&le(t)?[...new Set([...e,...t])]:Ke(Object.create(null),wu(e),wu(t!=null?t:{})):t}function qp(e,t){if(!e)return t;if(!t)return e;const n=Ke(Object.create(null),e);for(const r in t)n[r]=nt(e[r],t[r]);return n}function Dd(){return{app:null,config:{isNativeTag:Nm,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Gp=0;function Vp(e,t){return function(r,i=null){he(r)||(r=Ke({},r)),i!=null&&!xe(i)&&(i=null);const o=Dd(),u=new WeakSet,c=[];let l=!1;const s=o.app={_uid:Gp++,_component:r,_props:i,_container:null,_context:o,_instance:null,version:Tg,get config(){return o.config},set config(a){},use(a,...d){return u.has(a)||(a&&he(a.install)?(u.add(a),a.install(s,...d)):he(a)&&(u.add(a),a(s,...d))),s},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),s},component(a,d){return d?(o.components[a]=d,s):o.components[a]},directive(a,d){return d?(o.directives[a]=d,s):o.directives[a]},mount(a,d,f){if(!l){const h=s._ceVNode||be(r,i);return h.appContext=o,f===!0?f="svg":f===!1&&(f=void 0),e(h,a,f),l=!0,s._container=a,a.__vue_app__=s,Ho(h.component)}},onUnmount(a){c.push(a)},unmount(){l&&(Mt(c,s._instance,16),e(null,s._container),delete s._container.__vue_app__)},provide(a,d){return o.provides[a]=d,s},runWithContext(a){const d=Cn;Cn=s;try{return a()}finally{Cn=d}}};return s}}let Cn=null;function uo(e,t){if(Xe){let n=Xe.provides;const r=Xe.parent&&Xe.parent.provides;r===n&&(n=Xe.provides=Object.create(r)),n[e]=t}}function mt(e,t,n=!1){const r=Xe||Ve;if(r||Cn){const i=Cn?Cn._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&he(t)?t.call(r&&r.proxy):t}}function Wp(){return!!(Xe||Ve||Cn)}const Rd={},kd=()=>Object.create(Rd),Nd=e=>Object.getPrototypeOf(e)===Rd;function zp(e,t,n,r=!1){const i={},o=kd();e.propsDefaults=Object.create(null),xd(e,t,i,o);for(const u in e.propsOptions[0])u in i||(i[u]=void 0);n?e.props=r?i:sd(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function Xp(e,t,n,r){const{props:i,attrs:o,vnode:{patchFlag:u}}=e,c=we(i),[l]=e.propsOptions;let s=!1;if((r||u>0)&&!(u&16)){if(u&8){const a=e.vnode.dynamicProps;for(let d=0;d<a.length;d++){let f=a[d];if($o(e.emitsOptions,f))continue;const h=t[f];if(l)if(Ie(o,f))h!==o[f]&&(o[f]=h,s=!0);else{const p=At(f);i[p]=Js(l,c,p,h,e,!1)}else h!==o[f]&&(o[f]=h,s=!0)}}}else{xd(e,t,i,o)&&(s=!0);let a;for(const d in c)(!t||!Ie(t,d)&&((a=Dn(d))===d||!Ie(t,a)))&&(l?n&&(n[d]!==void 0||n[a]!==void 0)&&(i[d]=Js(l,c,d,void 0,e,!0)):delete i[d]);if(o!==c)for(const d in o)(!t||!Ie(t,d))&&(delete o[d],s=!0)}s&&Vt(e.attrs,"set","")}function xd(e,t,n,r){const[i,o]=e.propsOptions;let u=!1,c;if(t)for(let l in t){if(yr(l))continue;const s=t[l];let a;i&&Ie(i,a=At(l))?!o||!o.includes(a)?n[a]=s:(c||(c={}))[a]=s:$o(e.emitsOptions,l)||(!(l in r)||s!==r[l])&&(r[l]=s,u=!0)}if(o){const l=we(n),s=c||Re;for(let a=0;a<o.length;a++){const d=o[a];n[d]=Js(i,l,d,s[d],e,!Ie(s,d))}}return u}function Js(e,t,n,r,i,o){const u=e[n];if(u!=null){const c=Ie(u,"default");if(c&&r===void 0){const l=u.default;if(u.type!==Function&&!u.skipFactory&&he(l)){const{propsDefaults:s}=i;if(n in s)r=s[n];else{const a=Vr(i);r=s[n]=l.call(null,t),a()}}else r=l;i.ce&&i.ce._setProp(n,r)}u[0]&&(o&&!c?r=!1:u[1]&&(r===""||r===Dn(n))&&(r=!0))}return r}const Kp=new WeakMap;function Pd(e,t,n=!1){const r=n?Kp:t.propsCache,i=r.get(e);if(i)return i;const o=e.props,u={},c=[];let l=!1;if(!he(e)){const a=d=>{l=!0;const[f,h]=Pd(d,t,!0);Ke(u,f),h&&c.push(...h)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!l)return xe(e)&&r.set(e,Vn),Vn;if(le(o))for(let a=0;a<o.length;a++){const d=At(o[a]);Cu(d)&&(u[d]=Re)}else if(o)for(const a in o){const d=At(a);if(Cu(d)){const f=o[a],h=u[d]=le(f)||he(f)?{type:f}:Ke({},f),p=h.type;let m=!1,_=!0;if(le(p))for(let g=0;g<p.length;++g){const v=p[g],b=he(v)&&v.name;if(b==="Boolean"){m=!0;break}else b==="String"&&(_=!1)}else m=he(p)&&p.name==="Boolean";h[0]=m,h[1]=_,(m||Ie(h,"default"))&&c.push(d)}}const s=[u,c];return xe(e)&&r.set(e,s),s}function Cu(e){return e[0]!=="$"&&!yr(e)}const Fd=e=>e[0]==="_"||e==="$stable",Na=e=>le(e)?e.map(Pt):[Pt(e)],Qp=(e,t,n)=>{if(t._n)return t;const r=He((...i)=>Na(t(...i)),n);return r._c=!1,r},Bd=(e,t,n)=>{const r=e._ctx;for(const i in e){if(Fd(i))continue;const o=e[i];if(he(o))t[i]=Qp(i,o,r);else if(o!=null){const u=Na(o);t[i]=()=>u}}},$d=(e,t)=>{const n=Na(t);e.slots.default=()=>n},Hd=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},Jp=(e,t,n)=>{const r=e.slots=kd();if(e.vnode.shapeFlag&32){const i=t._;i?(Hd(r,t,n),n&&$f(r,"_",i,!0)):Bd(t,r)}else t&&$d(e,t)},Zp=(e,t,n)=>{const{vnode:r,slots:i}=e;let o=!0,u=Re;if(r.shapeFlag&32){const c=t._;c?n&&c===1?o=!1:Hd(i,t,n):(o=!t.$stable,Bd(t,i)),u=t}else t&&($d(e,t),u={default:1});if(o)for(const c in i)!Fd(c)&&u[c]==null&&delete i[c]},ft=hg;function eg(e){return tg(e)}function tg(e,t){const n=Do();n.__VUE__=!0;const{insert:r,remove:i,patchProp:o,createElement:u,createText:c,createComment:l,setText:s,setElementText:a,parentNode:d,nextSibling:f,setScopeId:h=Ct,insertStaticContent:p}=e,m=(S,y,C,F=null,$=null,j=null,A=void 0,w=null,x=!!y.dynamicChildren)=>{if(S===y)return;S&&!On(S,y)&&(F=U(S),ue(S,$,j,!0),S=null),y.patchFlag===-2&&(x=!1,y.dynamicChildren=null);const{type:H,ref:ee,shapeFlag:z}=y;switch(H){case Gr:_(S,y,C,F);break;case st:g(S,y,C,F);break;case lo:S==null&&v(y,C,F,A);break;case Be:L(S,y,C,F,$,j,A,w,x);break;default:z&1?D(S,y,C,F,$,j,A,w,x):z&6?B(S,y,C,F,$,j,A,w,x):(z&64||z&128)&&H.process(S,y,C,F,$,j,A,w,x,Q)}ee!=null&&$&&vo(ee,S&&S.ref,j,y||S,!y)},_=(S,y,C,F)=>{if(S==null)r(y.el=c(y.children),C,F);else{const $=y.el=S.el;y.children!==S.children&&s($,y.children)}},g=(S,y,C,F)=>{S==null?r(y.el=l(y.children||""),C,F):y.el=S.el},v=(S,y,C,F)=>{[S.el,S.anchor]=p(S.children,y,C,F,S.el,S.anchor)},b=({el:S,anchor:y},C,F)=>{let $;for(;S&&S!==y;)$=f(S),r(S,C,F),S=$;r(y,C,F)},E=({el:S,anchor:y})=>{let C;for(;S&&S!==y;)C=f(S),i(S),S=C;i(y)},D=(S,y,C,F,$,j,A,w,x)=>{y.type==="svg"?A="svg":y.type==="math"&&(A="mathml"),S==null?I(y,C,F,$,j,A,w,x):T(S,y,$,j,A,w,x)},I=(S,y,C,F,$,j,A,w)=>{let x,H;const{props:ee,shapeFlag:z,transition:P,dirs:q}=S;if(x=S.el=u(S.type,j,ee&&ee.is,ee),z&8?a(x,S.children):z&16&&M(S.children,x,null,F,$,ss(S,j),A,w),q&&_n(S,null,F,"created"),O(x,S,S.scopeId,A,F),ee){for(const ce in ee)ce!=="value"&&!yr(ce)&&o(x,ce,null,ee[ce],j,F);"value"in ee&&o(x,"value",null,ee.value,j),(H=ee.onVnodeBeforeMount)&&Rt(H,F,S)}q&&_n(S,null,F,"beforeMount");const se=ng($,P);se&&P.beforeEnter(x),r(x,y,C),((H=ee&&ee.onVnodeMounted)||se||q)&&ft(()=>{H&&Rt(H,F,S),se&&P.enter(x),q&&_n(S,null,F,"mounted")},$)},O=(S,y,C,F,$)=>{if(C&&h(S,C),F)for(let j=0;j<F.length;j++)h(S,F[j]);if($){let j=$.subTree;if(y===j||Vd(j.type)&&(j.ssContent===y||j.ssFallback===y)){const A=$.vnode;O(S,A,A.scopeId,A.slotScopeIds,$.parent)}}},M=(S,y,C,F,$,j,A,w,x=0)=>{for(let H=x;H<S.length;H++){const ee=S[H]=w?sn(S[H]):Pt(S[H]);m(null,ee,y,C,F,$,j,A,w)}},T=(S,y,C,F,$,j,A)=>{const w=y.el=S.el;let{patchFlag:x,dynamicChildren:H,dirs:ee}=y;x|=S.patchFlag&16;const z=S.props||Re,P=y.props||Re;let q;if(C&&vn(C,!1),(q=P.onVnodeBeforeUpdate)&&Rt(q,C,y,S),ee&&_n(y,S,C,"beforeUpdate"),C&&vn(C,!0),(z.innerHTML&&P.innerHTML==null||z.textContent&&P.textContent==null)&&a(w,""),H?k(S.dynamicChildren,H,w,C,F,ss(y,$),j):A||V(S,y,w,null,C,F,ss(y,$),j,!1),x>0){if(x&16)R(w,z,P,C,$);else if(x&2&&z.class!==P.class&&o(w,"class",null,P.class,$),x&4&&o(w,"style",z.style,P.style,$),x&8){const se=y.dynamicProps;for(let ce=0;ce<se.length;ce++){const ve=se[ce],ze=z[ve],Ue=P[ve];(Ue!==ze||ve==="value")&&o(w,ve,ze,Ue,$,C)}}x&1&&S.children!==y.children&&a(w,y.children)}else!A&&H==null&&R(w,z,P,C,$);((q=P.onVnodeUpdated)||ee)&&ft(()=>{q&&Rt(q,C,y,S),ee&&_n(y,S,C,"updated")},F)},k=(S,y,C,F,$,j,A)=>{for(let w=0;w<y.length;w++){const x=S[w],H=y[w],ee=x.el&&(x.type===Be||!On(x,H)||x.shapeFlag&70)?d(x.el):C;m(x,H,ee,null,F,$,j,A,!0)}},R=(S,y,C,F,$)=>{if(y!==C){if(y!==Re)for(const j in y)!yr(j)&&!(j in C)&&o(S,j,y[j],null,$,F);for(const j in C){if(yr(j))continue;const A=C[j],w=y[j];A!==w&&j!=="value"&&o(S,j,w,A,$,F)}"value"in C&&o(S,"value",y.value,C.value,$)}},L=(S,y,C,F,$,j,A,w,x)=>{const H=y.el=S?S.el:c(""),ee=y.anchor=S?S.anchor:c("");let{patchFlag:z,dynamicChildren:P,slotScopeIds:q}=y;q&&(w=w?w.concat(q):q),S==null?(r(H,C,F),r(ee,C,F),M(y.children||[],C,ee,$,j,A,w,x)):z>0&&z&64&&P&&S.dynamicChildren?(k(S.dynamicChildren,P,C,$,j,A,w),(y.key!=null||$&&y===$.subTree)&&jd(S,y,!0)):V(S,y,C,ee,$,j,A,w,x)},B=(S,y,C,F,$,j,A,w,x)=>{y.slotScopeIds=w,S==null?y.shapeFlag&512?$.ctx.activate(y,C,F,A,x):N(y,C,F,$,j,A,x):G(S,y,x)},N=(S,y,C,F,$,j,A)=>{const w=S.component=yg(S,F,$);if(Po(S)&&(w.ctx.renderer=Q),bg(w,!1,A),w.asyncDep){if($&&$.registerDep(w,Y,A),!S.el){const x=w.subTree=be(st);g(null,x,y,C)}}else Y(w,S,y,C,$,j,A)},G=(S,y,C)=>{const F=y.component=S.component;if(fg(S,y,C))if(F.asyncDep&&!F.asyncResolved){W(F,y,C);return}else F.next=y,F.update();else y.el=S.el,F.vnode=y},Y=(S,y,C,F,$,j,A)=>{const w=()=>{if(S.isMounted){let{next:z,bu:P,u:q,parent:se,vnode:ce}=S;{const Ot=Ud(S);if(Ot){z&&(z.el=ce.el,W(S,z,A)),Ot.asyncDep.then(()=>{S.isUnmounted||w()});return}}let ve=z,ze;vn(S,!1),z?(z.el=ce.el,W(S,z,A)):z=ce,P&&ao(P),(ze=z.props&&z.props.onVnodeBeforeUpdate)&&Rt(ze,se,z,ce),vn(S,!0);const Ue=Mu(S),vt=S.subTree;S.subTree=Ue,m(vt,Ue,d(vt.el),U(vt),S,$,j),z.el=Ue.el,ve===null&&dg(S,Ue.el),q&&ft(q,$),(ze=z.props&&z.props.onVnodeUpdated)&&ft(()=>Rt(ze,se,z,ce),$)}else{let z;const{el:P,props:q}=y,{bm:se,m:ce,parent:ve,root:ze,type:Ue}=S,vt=Xn(y);vn(S,!1),se&&ao(se),!vt&&(z=q&&q.onVnodeBeforeMount)&&Rt(z,ve,y),vn(S,!0);{ze.ce&&ze.ce._injectChildStyle(Ue);const Ot=S.subTree=Mu(S);m(null,Ot,C,F,S,$,j),y.el=Ot.el}if(ce&&ft(ce,$),!vt&&(z=q&&q.onVnodeMounted)){const Ot=y;ft(()=>Rt(z,ve,Ot),$)}(y.shapeFlag&256||ve&&Xn(ve.vnode)&&ve.vnode.shapeFlag&256)&&S.a&&ft(S.a,$),S.isMounted=!0,y=C=F=null}};S.scope.on();const x=S.effect=new Vf(w);S.scope.off();const H=S.update=x.run.bind(x),ee=S.job=x.runIfDirty.bind(x);ee.i=S,ee.id=S.uid,x.scheduler=()=>ka(ee),vn(S,!0),H()},W=(S,y,C)=>{y.component=S;const F=S.vnode.props;S.vnode=y,S.next=null,Xp(S,y.props,F,C),Zp(S,y.children,C),dn(),bu(S),hn()},V=(S,y,C,F,$,j,A,w,x=!1)=>{const H=S&&S.children,ee=S?S.shapeFlag:0,z=y.children,{patchFlag:P,shapeFlag:q}=y;if(P>0){if(P&128){te(H,z,C,F,$,j,A,w,x);return}else if(P&256){J(H,z,C,F,$,j,A,w,x);return}}q&8?(ee&16&&Ae(H,$,j),z!==H&&a(C,z)):ee&16?q&16?te(H,z,C,F,$,j,A,w,x):Ae(H,$,j,!0):(ee&8&&a(C,""),q&16&&M(z,C,F,$,j,A,w,x))},J=(S,y,C,F,$,j,A,w,x)=>{S=S||Vn,y=y||Vn;const H=S.length,ee=y.length,z=Math.min(H,ee);let P;for(P=0;P<z;P++){const q=y[P]=x?sn(y[P]):Pt(y[P]);m(S[P],q,C,null,$,j,A,w,x)}H>ee?Ae(S,$,j,!0,!1,z):M(y,C,F,$,j,A,w,x,z)},te=(S,y,C,F,$,j,A,w,x)=>{let H=0;const ee=y.length;let z=S.length-1,P=ee-1;for(;H<=z&&H<=P;){const q=S[H],se=y[H]=x?sn(y[H]):Pt(y[H]);if(On(q,se))m(q,se,C,null,$,j,A,w,x);else break;H++}for(;H<=z&&H<=P;){const q=S[z],se=y[P]=x?sn(y[P]):Pt(y[P]);if(On(q,se))m(q,se,C,null,$,j,A,w,x);else break;z--,P--}if(H>z){if(H<=P){const q=P+1,se=q<ee?y[q].el:F;for(;H<=P;)m(null,y[H]=x?sn(y[H]):Pt(y[H]),C,se,$,j,A,w,x),H++}}else if(H>P)for(;H<=z;)ue(S[H],$,j,!0),H++;else{const q=H,se=H,ce=new Map;for(H=se;H<=P;H++){const ut=y[H]=x?sn(y[H]):Pt(y[H]);ut.key!=null&&ce.set(ut.key,H)}let ve,ze=0;const Ue=P-se+1;let vt=!1,Ot=0;const nr=new Array(Ue);for(H=0;H<Ue;H++)nr[H]=0;for(H=q;H<=z;H++){const ut=S[H];if(ze>=Ue){ue(ut,$,j,!0);continue}let Dt;if(ut.key!=null)Dt=ce.get(ut.key);else for(ve=se;ve<=P;ve++)if(nr[ve-se]===0&&On(ut,y[ve])){Dt=ve;break}Dt===void 0?ue(ut,$,j,!0):(nr[Dt-se]=H+1,Dt>=Ot?Ot=Dt:vt=!0,m(ut,y[Dt],C,null,$,j,A,w,x),ze++)}const za=vt?rg(nr):Vn;for(ve=za.length-1,H=Ue-1;H>=0;H--){const ut=se+H,Dt=y[ut],Xa=ut+1<ee?y[ut+1].el:F;nr[H]===0?m(null,Dt,C,Xa,$,j,A,w,x):vt&&(ve<0||H!==za[ve]?ie(Dt,C,Xa,2):ve--)}}},ie=(S,y,C,F,$=null)=>{const{el:j,type:A,transition:w,children:x,shapeFlag:H}=S;if(H&6){ie(S.component.subTree,y,C,F);return}if(H&128){S.suspense.move(y,C,F);return}if(H&64){A.move(S,y,C,Q);return}if(A===Be){r(j,y,C);for(let z=0;z<x.length;z++)ie(x[z],y,C,F);r(S.anchor,y,C);return}if(A===lo){b(S,y,C);return}if(F!==2&&H&1&&w)if(F===0)w.beforeEnter(j),r(j,y,C),ft(()=>w.enter(j),$);else{const{leave:z,delayLeave:P,afterLeave:q}=w,se=()=>r(j,y,C),ce=()=>{z(j,()=>{se(),q&&q()})};P?P(j,se,ce):ce()}else r(j,y,C)},ue=(S,y,C,F=!1,$=!1)=>{const{type:j,props:A,ref:w,children:x,dynamicChildren:H,shapeFlag:ee,patchFlag:z,dirs:P,cacheIndex:q}=S;if(z===-2&&($=!1),w!=null&&vo(w,null,C,S,!0),q!=null&&(y.renderCache[q]=void 0),ee&256){y.ctx.deactivate(S);return}const se=ee&1&&P,ce=!Xn(S);let ve;if(ce&&(ve=A&&A.onVnodeBeforeUnmount)&&Rt(ve,y,S),ee&6)Oe(S.component,C,F);else{if(ee&128){S.suspense.unmount(C,F);return}se&&_n(S,null,y,"beforeUnmount"),ee&64?S.type.remove(S,y,C,Q,F):H&&!H.hasOnce&&(j!==Be||z>0&&z&64)?Ae(H,y,C,!1,!0):(j===Be&&z&384||!$&&ee&16)&&Ae(x,y,C),F&&me(S)}(ce&&(ve=A&&A.onVnodeUnmounted)||se)&&ft(()=>{ve&&Rt(ve,y,S),se&&_n(S,null,y,"unmounted")},C)},me=S=>{const{type:y,el:C,anchor:F,transition:$}=S;if(y===Be){Ee(C,F);return}if(y===lo){E(S);return}const j=()=>{i(C),$&&!$.persisted&&$.afterLeave&&$.afterLeave()};if(S.shapeFlag&1&&$&&!$.persisted){const{leave:A,delayLeave:w}=$,x=()=>A(C,j);w?w(S.el,j,x):x()}else j()},Ee=(S,y)=>{let C;for(;S!==y;)C=f(S),i(S),S=C;i(y)},Oe=(S,y,C)=>{const{bum:F,scope:$,job:j,subTree:A,um:w,m:x,a:H}=S;Iu(x),Iu(H),F&&ao(F),$.stop(),j&&(j.flags|=8,ue(A,S,y,C)),w&&ft(w,y),ft(()=>{S.isUnmounted=!0},y),y&&y.pendingBranch&&!y.isUnmounted&&S.asyncDep&&!S.asyncResolved&&S.suspenseId===y.pendingId&&(y.deps--,y.deps===0&&y.resolve())},Ae=(S,y,C,F=!1,$=!1,j=0)=>{for(let A=j;A<S.length;A++)ue(S[A],y,C,F,$)},U=S=>{if(S.shapeFlag&6)return U(S.component.subTree);if(S.shapeFlag&128)return S.suspense.next();const y=f(S.anchor||S.el),C=y&&y[Ap];return C?f(C):y};let K=!1;const X=(S,y,C)=>{S==null?y._vnode&&ue(y._vnode,null,null,!0):m(y._vnode||null,S,y,null,null,null,C),y._vnode=S,K||(K=!0,bu(),md(),K=!1)},Q={p:m,um:ue,m:ie,r:me,mt:N,mc:M,pc:V,pbc:k,n:U,o:e};return{render:X,hydrate:void 0,createApp:Vp(X)}}function ss({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function vn({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ng(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function jd(e,t,n=!1){const r=e.children,i=t.children;if(le(r)&&le(i))for(let o=0;o<r.length;o++){const u=r[o];let c=i[o];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=i[o]=sn(i[o]),c.el=u.el),!n&&c.patchFlag!==-2&&jd(u,c)),c.type===Gr&&(c.el=u.el)}}function rg(e){const t=e.slice(),n=[0];let r,i,o,u,c;const l=e.length;for(r=0;r<l;r++){const s=e[r];if(s!==0){if(i=n[n.length-1],e[i]<s){t[r]=i,n.push(r);continue}for(o=0,u=n.length-1;o<u;)c=o+u>>1,e[n[c]]<s?o=c+1:u=c;s<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,u=n[o-1];o-- >0;)n[o]=u,u=t[u];return n}function Ud(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ud(t)}function Iu(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ig=Symbol.for("v-scx"),og=()=>mt(ig);function Ft(e,t,n){return Yd(e,t,n)}function Yd(e,t,n=Re){const{immediate:r,deep:i,flush:o,once:u}=n,c=Ke({},n),l=t&&r||!t&&o!=="post";let s;if(Nr){if(o==="sync"){const h=og();s=h.__watcherHandles||(h.__watcherHandles=[])}else if(!l){const h=()=>{};return h.stop=Ct,h.resume=Ct,h.pause=Ct,h}}const a=Xe;c.call=(h,p,m)=>Mt(h,a,p,m);let d=!1;o==="post"?c.scheduler=h=>{ft(h,a&&a.suspense)}:o!=="sync"&&(d=!0,c.scheduler=(h,p)=>{p?h():ka(h)}),c.augmentJob=h=>{t&&(h.flags|=4),d&&(h.flags|=2,a&&(h.id=a.uid,h.i=a))};const f=yp(e,t,c);return Nr&&(s?s.push(f):l&&f()),f}function sg(e,t,n){const r=this.proxy,i=Pe(e)?e.includes(".")?qd(r,e):()=>r[e]:e.bind(r,r);let o;he(t)?o=t:(o=t.handler,n=t);const u=Vr(this),c=Yd(i,o.bind(r),n);return u(),c}function qd(e,t){const n=t.split(".");return()=>{let r=e;for(let i=0;i<n.length&&r;i++)r=r[n[i]];return r}}const ag=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e["".concat(t,"Modifiers")]||e["".concat(At(t),"Modifiers")]||e["".concat(Dn(t),"Modifiers")];function ug(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Re;let i=n;const o=t.startsWith("update:"),u=o&&ag(r,t.slice(7));u&&(u.trim&&(i=n.map(a=>Pe(a)?a.trim():a)),u.number&&(i=n.map(Us)));let c,l=r[c=Zo(t)]||r[c=Zo(At(t))];!l&&o&&(l=r[c=Zo(Dn(t))]),l&&Mt(l,e,6,i);const s=r[c+"Once"];if(s){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Mt(s,e,6,i)}}function Gd(e,t,n=!1){const r=t.emitsCache,i=r.get(e);if(i!==void 0)return i;const o=e.emits;let u={},c=!1;if(!he(e)){const l=s=>{const a=Gd(s,t,!0);a&&(c=!0,Ke(u,a))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!c?(xe(e)&&r.set(e,null),null):(le(o)?o.forEach(l=>u[l]=null):Ke(u,o),xe(e)&&r.set(e,u),u)}function $o(e,t){return!e||!Co(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ie(e,t[0].toLowerCase()+t.slice(1))||Ie(e,Dn(t))||Ie(e,t))}function Mu(e){const{type:t,vnode:n,proxy:r,withProxy:i,propsOptions:[o],slots:u,attrs:c,emit:l,render:s,renderCache:a,props:d,data:f,setupState:h,ctx:p,inheritAttrs:m}=e,_=_o(e);let g,v;try{if(n.shapeFlag&4){const E=i||r,D=E;g=Pt(s.call(D,E,a,d,h,f,p)),v=c}else{const E=t;g=Pt(E.length>1?E(d,{attrs:c,slots:u,emit:l}):E(d,null)),v=t.props?c:lg(c)}}catch(E){wr.length=0,No(E,e,1),g=be(st)}let b=g;if(v&&m!==!1){const E=Object.keys(v),{shapeFlag:D}=b;E.length&&D&7&&(o&&E.some(Ea)&&(v=cg(v,o)),b=cn(b,v,!1,!0))}return n.dirs&&(b=cn(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&Dr(b,n.transition),g=b,_o(_),g}const lg=e=>{let t;for(const n in e)(n==="class"||n==="style"||Co(n))&&((t||(t={}))[n]=e[n]);return t},cg=(e,t)=>{const n={};for(const r in e)(!Ea(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function fg(e,t,n){const{props:r,children:i,component:o}=e,{props:u,children:c,patchFlag:l}=t,s=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?Lu(r,u,s):!!u;if(l&8){const a=t.dynamicProps;for(let d=0;d<a.length;d++){const f=a[d];if(u[f]!==r[f]&&!$o(s,f))return!0}}}else return(i||c)&&(!c||!c.$stable)?!0:r===u?!1:r?u?Lu(r,u,s):!0:!!u;return!1}function Lu(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let i=0;i<r.length;i++){const o=r[i];if(t[o]!==e[o]&&!$o(n,o))return!0}return!1}function dg({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Vd=e=>e.__isSuspense;function hg(e,t){t&&t.pendingBranch?le(e)?t.effects.push(...e):t.effects.push(e):hd(e)}const Be=Symbol.for("v-fgt"),Gr=Symbol.for("v-txt"),st=Symbol.for("v-cmt"),lo=Symbol.for("v-stc"),wr=[];let ht=null;function ne(e=!1){wr.push(ht=e?null:[])}function mg(){wr.pop(),ht=wr[wr.length-1]||null}let Rr=1;function Du(e,t=!1){Rr+=e,e<0&&ht&&t&&(ht.hasOnce=!0)}function Wd(e){return e.dynamicChildren=Rr>0?ht||Vn:null,mg(),Rr>0&&ht&&ht.push(e),e}function ae(e,t,n,r,i,o){return Wd(oe(e,t,n,r,i,o,!0))}function et(e,t,n,r,i){return Wd(be(e,t,n,r,i,!0))}function kr(e){return e?e.__v_isVNode===!0:!1}function On(e,t){return e.type===t.type&&e.key===t.key}const zd=({key:e})=>e!=null?e:null,co=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Pe(e)||je(e)||he(e)?{i:Ve,r:e,k:t,f:!!n}:e:null);function oe(e,t=null,n=null,r=0,i=null,o=e===Be?0:1,u=!1,c=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&zd(t),ref:t&&co(t),scopeId:gd,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Ve};return c?(xa(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=Pe(n)?8:16),Rr>0&&!u&&ht&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&ht.push(l),l}const be=pg;function pg(e,t=null,n=null,r=0,i=null,o=!1){if((!e||e===Sd)&&(e=st),kr(e)){const c=cn(e,t,!0);return n&&xa(c,n),Rr>0&&!o&&ht&&(c.shapeFlag&6?ht[ht.indexOf(e)]=c:ht.push(c)),c.patchFlag=-2,c}if(Og(e)&&(e=e.__vccOpts),t){t=Xd(t);let{class:c,style:l}=t;c&&!Pe(c)&&(t.class=$e(c)),xe(l)&&(Da(l)&&!le(l)&&(l=Ke({},l)),t.style=dt(l))}const u=Pe(e)?1:Vd(e)?128:_d(e)?64:xe(e)?4:he(e)?2:0;return oe(e,t,n,r,i,u,o,!0)}function Xd(e){return e?Da(e)||Nd(e)?Ke({},e):e:null}function cn(e,t,n=!1,r=!1){const{props:i,ref:o,patchFlag:u,children:c,transition:l}=e,s=t?bo(i||{},t):i,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&zd(s),ref:t&&t.ref?n&&o?le(o)?o.concat(co(t)):[o,co(t)]:co(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Be?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&cn(e.ssContent),ssFallback:e.ssFallback&&cn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&Dr(a,l.clone(a)),a}function gg(e=" ",t=0){return be(Gr,null,e,t)}function pe(e="",t=!1){return t?(ne(),et(st,null,e)):be(st,null,e)}function Pt(e){return e==null||typeof e=="boolean"?be(st):le(e)?be(Be,null,e.slice()):kr(e)?sn(e):be(Gr,null,String(e))}function sn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:cn(e)}function xa(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(le(t))n=16;else if(typeof t=="object")if(r&65){const i=t.default;i&&(i._c&&(i._d=!1),xa(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!Nd(t)?t._ctx=Ve:i===3&&Ve&&(Ve.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else he(t)?(t={default:t,_ctx:Ve},n=32):(t=String(t),r&64?(n=16,t=[gg(t)]):n=8);e.children=t,e.shapeFlag|=n}function bo(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const i in r)if(i==="class")t.class!==r.class&&(t.class=$e([t.class,r.class]));else if(i==="style")t.style=dt([t.style,r.style]);else if(Co(i)){const o=t[i],u=r[i];u&&o!==u&&!(le(o)&&o.includes(u))&&(t[i]=o?[].concat(o,u):u)}else i!==""&&(t[i]=r[i])}return t}function Rt(e,t,n,r=null){Mt(e,t,7,[n,r])}const _g=Dd();let vg=0;function yg(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||_g,o={uid:vg++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new qf(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Pd(r,i),emitsOptions:Gd(r,i),emit:null,emitted:null,propsDefaults:Re,inheritAttrs:r.inheritAttrs,ctx:Re,data:Re,props:Re,attrs:Re,slots:Re,refs:Re,setupState:Re,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ug.bind(null,o),e.ce&&e.ce(o),o}let Xe=null;const fn=()=>Xe||Ve;let Eo,Zs;{const e=Do(),t=(n,r)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(r),o=>{i.length>1?i.forEach(u=>u(o)):i[0](o)}};Eo=t("__VUE_INSTANCE_SETTERS__",n=>Xe=n),Zs=t("__VUE_SSR_SETTERS__",n=>Nr=n)}const Vr=e=>{const t=Xe;return Eo(e),e.scope.on(),()=>{e.scope.off(),Eo(t)}},Ru=()=>{Xe&&Xe.scope.off(),Eo(null)};function Kd(e){return e.vnode.shapeFlag&4}let Nr=!1;function bg(e,t=!1,n=!1){t&&Zs(t);const{props:r,children:i}=e.vnode,o=Kd(e);zp(e,r,o,t),Jp(e,i,n);const u=o?Eg(e,t):void 0;return t&&Zs(!1),u}function Eg(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Fp);const{setup:r}=n;if(r){dn();const i=e.setupContext=r.length>1?Jd(e):null,o=Vr(e),u=Ur(r,e,0,[e.props,i]),c=Pf(u);if(hn(),o(),(c||e.sp)&&!Xn(e)&&wd(e),c){if(u.then(Ru,Ru),t)return u.then(l=>{ku(e,l)}).catch(l=>{No(l,e,0)});e.asyncDep=u}else ku(e,u)}else Qd(e)}function ku(e,t,n){he(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:xe(t)&&(e.setupState=cd(t)),Qd(e)}function Qd(e,t,n){const r=e.type;e.render||(e.render=r.render||Ct);{const i=Vr(e);dn();try{Hp(e)}finally{hn(),i()}}}const Ag={get(e,t){return Je(e,"get",""),e[t]}};function Jd(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ag),slots:e.slots,emit:e.emit,expose:t}}function Ho(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(cd(Ra(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ar)return Ar[n](e)},has(t,n){return n in t||n in Ar}})):e.proxy}function wg(e,t=!0){return he(e)?e.displayName||e.name:e.name||t&&e.__name}function Og(e){return he(e)&&"__vccOpts"in e}const de=(e,t)=>_p(e,t,Nr);function Wr(e,t,n){const r=arguments.length;return r===2?xe(t)&&!le(t)?kr(t)?be(e,null,[t]):be(e,t):be(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&kr(n)&&(n=[n]),be(e,t,n))}const Tg="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ea;const Nu=typeof window<"u"&&window.trustedTypes;if(Nu)try{ea=Nu.createPolicy("vue",{createHTML:e=>e})}catch(e){}const Zd=ea?e=>ea.createHTML(e):e=>e,Sg="http://www.w3.org/2000/svg",Cg="http://www.w3.org/1998/Math/MathML",Gt=typeof document<"u"?document:null,xu=Gt&&Gt.createElement("template"),Ig={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const i=t==="svg"?Gt.createElementNS(Sg,e):t==="mathml"?Gt.createElementNS(Cg,e):n?Gt.createElement(e,{is:n}):Gt.createElement(e);return e==="select"&&r&&r.multiple!=null&&i.setAttribute("multiple",r.multiple),i},createText:e=>Gt.createTextNode(e),createComment:e=>Gt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Gt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,i,o){const u=n?n.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===o||!(i=i.nextSibling)););else{xu.innerHTML=Zd(r==="svg"?"<svg>".concat(e,"</svg>"):r==="mathml"?"<math>".concat(e,"</math>"):e);const c=xu.content;if(r==="svg"||r==="mathml"){const l=c.firstChild;for(;l.firstChild;)c.appendChild(l.firstChild);c.removeChild(l)}t.insertBefore(c,n)}return[u?u.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Jt="transition",ir="animation",xr=Symbol("_vtc"),eh={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Mg=Ke({},vd,eh),Lg=e=>(e.displayName="Transition",e.props=Mg,e),Or=Lg((e,{slots:t})=>Wr(Tp,Dg(e),t)),yn=(e,t=[])=>{le(e)?e.forEach(n=>n(...t)):e&&e(...t)},Pu=e=>e?le(e)?e.some(t=>t.length>1):e.length>1:!1;function Dg(e){const t={};for(const L in e)L in eh||(t[L]=e[L]);if(e.css===!1)return t;const{name:n="v",type:r,duration:i,enterFromClass:o="".concat(n,"-enter-from"),enterActiveClass:u="".concat(n,"-enter-active"),enterToClass:c="".concat(n,"-enter-to"),appearFromClass:l=o,appearActiveClass:s=u,appearToClass:a=c,leaveFromClass:d="".concat(n,"-leave-from"),leaveActiveClass:f="".concat(n,"-leave-active"),leaveToClass:h="".concat(n,"-leave-to")}=e,p=Rg(i),m=p&&p[0],_=p&&p[1],{onBeforeEnter:g,onEnter:v,onEnterCancelled:b,onLeave:E,onLeaveCancelled:D,onBeforeAppear:I=g,onAppear:O=v,onAppearCancelled:M=b}=t,T=(L,B,N,G)=>{L._enterCancelled=G,bn(L,B?a:c),bn(L,B?s:u),N&&N()},k=(L,B)=>{L._isLeaving=!1,bn(L,d),bn(L,h),bn(L,f),B&&B()},R=L=>(B,N)=>{const G=L?O:v,Y=()=>T(B,L,N);yn(G,[B,Y]),Fu(()=>{bn(B,L?l:o),jt(B,L?a:c),Pu(G)||Bu(B,r,m,Y)})};return Ke(t,{onBeforeEnter(L){yn(g,[L]),jt(L,o),jt(L,u)},onBeforeAppear(L){yn(I,[L]),jt(L,l),jt(L,s)},onEnter:R(!1),onAppear:R(!0),onLeave(L,B){L._isLeaving=!0;const N=()=>k(L,B);jt(L,d),L._enterCancelled?(jt(L,f),ju()):(ju(),jt(L,f)),Fu(()=>{L._isLeaving&&(bn(L,d),jt(L,h),Pu(E)||Bu(L,r,_,N))}),yn(E,[L,N])},onEnterCancelled(L){T(L,!1,void 0,!0),yn(b,[L])},onAppearCancelled(L){T(L,!0,void 0,!0),yn(M,[L])},onLeaveCancelled(L){k(L),yn(D,[L])}})}function Rg(e){if(e==null)return null;if(xe(e))return[as(e.enter),as(e.leave)];{const t=as(e);return[t,t]}}function as(e){return $m(e)}function jt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[xr]||(e[xr]=new Set)).add(t)}function bn(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[xr];n&&(n.delete(t),n.size||(e[xr]=void 0))}function Fu(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let kg=0;function Bu(e,t,n,r){const i=e._endId=++kg,o=()=>{i===e._endId&&r()};if(n!=null)return setTimeout(o,n);const{type:u,timeout:c,propCount:l}=Ng(e,t);if(!u)return r();const s=u+"end";let a=0;const d=()=>{e.removeEventListener(s,f),o()},f=h=>{h.target===e&&++a>=l&&d()};setTimeout(()=>{a<l&&d()},c+1),e.addEventListener(s,f)}function Ng(e,t){const n=window.getComputedStyle(e),r=p=>(n[p]||"").split(", "),i=r("".concat(Jt,"Delay")),o=r("".concat(Jt,"Duration")),u=$u(i,o),c=r("".concat(ir,"Delay")),l=r("".concat(ir,"Duration")),s=$u(c,l);let a=null,d=0,f=0;t===Jt?u>0&&(a=Jt,d=u,f=o.length):t===ir?s>0&&(a=ir,d=s,f=l.length):(d=Math.max(u,s),a=d>0?u>s?Jt:ir:null,f=a?a===Jt?o.length:l.length:0);const h=a===Jt&&/\b(transform|all)(,|$)/.test(r("".concat(Jt,"Property")).toString());return{type:a,timeout:d,propCount:f,hasTransform:h}}function $u(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>Hu(n)+Hu(e[r])))}function Hu(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function ju(){return document.body.offsetHeight}function xg(e,t,n){const r=e[xr];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ao=Symbol("_vod"),th=Symbol("_vsh"),Uu={beforeMount(e,{value:t},{transition:n}){e[Ao]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):or(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),or(e,!0),r.enter(e)):r.leave(e,()=>{or(e,!1)}):or(e,t))},beforeUnmount(e,{value:t}){or(e,t)}};function or(e,t){e.style.display=t?e[Ao]:"none",e[th]=!t}const nh=Symbol("");function Pg(e){const t=fn();if(!t)return;const n=t.ut=(i=e(t.proxy))=>{Array.from(document.querySelectorAll('[data-v-owner="'.concat(t.uid,'"]'))).forEach(o=>wo(o,i))},r=()=>{const i=e(t.proxy);t.ce?wo(t.ce,i):ta(t.subTree,i),n(i)};Td(()=>{hd(r)}),$t(()=>{Ft(r,Ct,{flush:"post"});const i=new MutationObserver(r);i.observe(t.subTree.el.parentNode,{childList:!0}),tr(()=>i.disconnect())})}function ta(e,t){if(e.shapeFlag&128){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{ta(n.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)wo(e.el,t);else if(e.type===Be)e.children.forEach(n=>ta(n,t));else if(e.type===lo){let{el:n,anchor:r}=e;for(;n&&(wo(n,t),n!==r);)n=n.nextSibling}}function wo(e,t){if(e.nodeType===1){const n=e.style;let r="";for(const i in t)n.setProperty("--".concat(i),t[i]),r+="--".concat(i,": ").concat(t[i],";");n[nh]=r}}const Fg=/(^|;)\s*display\s*:/;function Bg(e,t,n){const r=e.style,i=Pe(n);let o=!1;if(n&&!i){if(t)if(Pe(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();n[c]==null&&fo(r,c,"")}else for(const u in t)n[u]==null&&fo(r,u,"");for(const u in n)u==="display"&&(o=!0),fo(r,u,n[u])}else if(i){if(t!==n){const u=r[nh];u&&(n+=";"+u),r.cssText=n,o=Fg.test(n)}}else t&&e.removeAttribute("style");Ao in e&&(e[Ao]=o?r.display:"",e[th]&&(r.display="none"))}const Yu=/\s*!important$/;function fo(e,t,n){if(le(n))n.forEach(r=>fo(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=$g(e,t);Yu.test(n)?e.setProperty(Dn(r),n.replace(Yu,""),"important"):e[r]=n}}const qu=["Webkit","Moz","ms"],us={};function $g(e,t){const n=us[t];if(n)return n;let r=At(t);if(r!=="filter"&&r in e)return us[t]=r;r=Lo(r);for(let i=0;i<qu.length;i++){const o=qu[i]+r;if(o in e)return us[t]=o}return t}const Gu="http://www.w3.org/1999/xlink";function Vu(e,t,n,r,i,o=Gm(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Gu,t.slice(6,t.length)):e.setAttributeNS(Gu,t,n):n==null||o&&!jf(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Kt(n)?String(n):n)}function Wu(e,t,n,r,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Zd(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const c=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(c!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let u=!1;if(n===""||n==null){const c=typeof e[t];c==="boolean"?n=jf(n):n==null&&c==="string"?(n="",u=!0):c==="number"&&(n=0,u=!0)}try{e[t]=n}catch(c){}u&&e.removeAttribute(i||t)}function Yn(e,t,n,r){e.addEventListener(t,n,r)}function Hg(e,t,n,r){e.removeEventListener(t,n,r)}const zu=Symbol("_vei");function jg(e,t,n,r,i=null){const o=e[zu]||(e[zu]={}),u=o[t];if(r&&u)u.value=r;else{const[c,l]=Ug(t);if(r){const s=o[t]=Gg(r,i);Yn(e,c,s,l)}else u&&(Hg(e,c,u,l),o[t]=void 0)}}const Xu=/(?:Once|Passive|Capture)$/;function Ug(e){let t;if(Xu.test(e)){t={};let r;for(;r=e.match(Xu);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Dn(e.slice(2)),t]}let ls=0;const Yg=Promise.resolve(),qg=()=>ls||(Yg.then(()=>ls=0),ls=Date.now());function Gg(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Mt(Vg(r,n.value),t,5,[r])};return n.value=e,n.attached=qg(),n}function Vg(e,t){if(le(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>i=>!i._stopped&&r&&r(i))}else return t}const Ku=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Wg=(e,t,n,r,i,o)=>{const u=i==="svg";t==="class"?xg(e,r,u):t==="style"?Bg(e,n,r):Co(t)?Ea(t)||jg(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):zg(e,t,r,u))?(Wu(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Vu(e,t,r,u,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Pe(r))?Wu(e,At(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Vu(e,t,r,u))};function zg(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ku(t)&&he(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Ku(t)&&Pe(n)?!1:t in e}const Qu=e=>{const t=e.props["onUpdate:modelValue"]||!1;return le(t)?n=>ao(t,n):t};function Xg(e){e.target.composing=!0}function Ju(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const cs=Symbol("_assign"),Kg={created(e,{modifiers:{lazy:t,trim:n,number:r}},i){e[cs]=Qu(i);const o=r||i.props&&i.props.type==="number";Yn(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;n&&(c=c.trim()),o&&(c=Us(c)),e[cs](c)}),n&&Yn(e,"change",()=>{e.value=e.value.trim()}),t||(Yn(e,"compositionstart",Xg),Yn(e,"compositionend",Ju),Yn(e,"change",Ju))},mounted(e,{value:t}){e.value=t==null?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:i,number:o}},u){if(e[cs]=Qu(u),e.composing)return;const c=(o||e.type==="number")&&!/^0\d/.test(e.value)?Us(e.value):e.value,l=t==null?"":t;c!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||i&&e.value.trim()===l)||(e.value=l))}},Qg=["ctrl","shift","alt","meta"],Jg={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Qg.some(n=>e["".concat(n,"Key")]&&!t.includes(n))},Zg=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(i,...o)=>{for(let u=0;u<t.length;u++){const c=Jg[t[u]];if(c&&c(i,t))return}return e(i,...o)})},e0=Ke({patchProp:Wg},Ig);let Zu;function t0(){return Zu||(Zu=eg(e0))}const n0=(...e)=>{const t=t0().createApp(...e),{mount:n}=t;return t.mount=r=>{const i=i0(r);if(!i)return;const o=t._component;!he(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const u=n(i,!1,r0(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),u},t};function r0(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function i0(e){return Pe(e)?document.querySelector(e):e}/*!
 * pinia v3.0.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let rh;const jo=e=>rh=e,ih=Symbol();function na(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Tr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Tr||(Tr={}));function o0(){const e=Oa(!0),t=e.run(()=>_e({}));let n=[],r=[];const i=Ra({install(o){jo(i),i._a=o,o.provide(ih,i),o.config.globalProperties.$pinia=i,r.forEach(u=>n.push(u)),r=[]},use(o){return this._a?n.push(o):r.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}const oh=()=>{};function el(e,t,n,r=oh){e.push(t);const i=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&Gf()&&Vm(i),i}function Bn(e,...t){e.slice().forEach(n=>{n(...t)})}const s0=e=>e(),tl=Symbol(),fs=Symbol();function ra(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],i=e[n];na(i)&&na(r)&&e.hasOwnProperty(n)&&!je(r)&&!ln(r)?e[n]=ra(i,r):e[n]=r}return e}const a0=Symbol();function u0(e){return!na(e)||!e.hasOwnProperty(a0)}const{assign:nn}=Object;function l0(e){return!!(je(e)&&e.effect)}function c0(e,t,n,r){const{state:i,actions:o,getters:u}=t,c=n.state.value[e];let l;function s(){c||(n.state.value[e]=i?i():{});const a=ko(n.state.value[e]);return nn(a,o,Object.keys(u||{}).reduce((d,f)=>(d[f]=Ra(de(()=>{jo(n);const h=n._s.get(e);return u[f].call(h,h)})),d),{}))}return l=sh(e,s,t,n,r,!0),l}function sh(e,t,n={},r,i,o){let u;const c=nn({actions:{}},n),l={deep:!0};let s,a,d=[],f=[],h;const p=r.state.value[e];!o&&!p&&(r.state.value[e]={}),_e({});let m;function _(M){let T;s=a=!1,typeof M=="function"?(M(r.state.value[e]),T={type:Tr.patchFunction,storeId:e,events:h}):(ra(r.state.value[e],M),T={type:Tr.patchObject,payload:M,storeId:e,events:h});const k=m=Symbol();xo().then(()=>{m===k&&(s=!0)}),a=!0,Bn(d,T,r.state.value[e])}const g=o?function(){const{state:T}=n,k=T?T():{};this.$patch(R=>{nn(R,k)})}:oh;function v(){u.stop(),d=[],f=[],r._s.delete(e)}const b=(M,T="")=>{if(tl in M)return M[fs]=T,M;const k=function(){jo(r);const R=Array.from(arguments),L=[],B=[];function N(W){L.push(W)}function G(W){B.push(W)}Bn(f,{args:R,name:k[fs],store:D,after:N,onError:G});let Y;try{Y=M.apply(this&&this.$id===e?this:D,R)}catch(W){throw Bn(B,W),W}return Y instanceof Promise?Y.then(W=>(Bn(L,W),W)).catch(W=>(Bn(B,W),Promise.reject(W))):(Bn(L,Y),Y)};return k[tl]=!0,k[fs]=T,k},E={_p:r,$id:e,$onAction:el.bind(null,f),$patch:_,$reset:g,$subscribe(M,T={}){const k=el(d,M,T.detached,()=>R()),R=u.run(()=>Ft(()=>r.state.value[e],L=>{(T.flush==="sync"?a:s)&&M({storeId:e,type:Tr.direct,events:h},L)},nn({},l,T)));return k},$dispose:v},D=In(E);r._s.set(e,D);const O=(r._a&&r._a.runWithContext||s0)(()=>r._e.run(()=>(u=Oa()).run(()=>t({action:b}))));for(const M in O){const T=O[M];if(je(T)&&!l0(T)||ln(T))o||(p&&u0(T)&&(je(T)?T.value=p[M]:ra(T,p[M])),r.state.value[e][M]=T);else if(typeof T=="function"){const k=b(T,M);O[M]=k,c.actions[M]=T}}return nn(D,O),nn(we(D),O),Object.defineProperty(D,"$state",{get:()=>r.state.value[e],set:M=>{_(T=>{nn(T,M)})}}),r._p.forEach(M=>{nn(D,u.run(()=>M({store:D,app:r._a,pinia:r,options:c})))}),p&&o&&n.hydrate&&n.hydrate(D.$state,p),s=!0,a=!0,D}/*! #__NO_SIDE_EFFECTS__ */function f0(e,t,n){let r;const i=typeof t=="function";r=i?n:t;function o(u,c){const l=Wp();return u=u||(l?mt(ih,null):null),u&&jo(u),u=rh,u._s.has(e)||(i?sh(e,t,r,u):c0(e,r,u)),u._s.get(e)}return o.$id=e,o}/*!
  * shared v11.1.2
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Oo=typeof window<"u",mn=(e,t=!1)=>t?Symbol.for(e):Symbol(e),d0=(e,t,n)=>h0({l:e,k:t,s:n}),h0=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Ye=e=>typeof e=="number"&&isFinite(e),m0=e=>Pa(e)==="[object Date]",Kn=e=>Pa(e)==="[object RegExp]",Uo=e=>ye(e)&&Object.keys(e).length===0,We=Object.assign,p0=Object.create,De=(e=null)=>p0(e);let nl;const Tn=()=>nl||(nl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:De());function rl(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const g0=Object.prototype.hasOwnProperty;function St(e,t){return g0.call(e,t)}const qe=Array.isArray,Ne=e=>typeof e=="function",re=e=>typeof e=="string",Te=e=>typeof e=="boolean",Se=e=>e!==null&&typeof e=="object",_0=e=>Se(e)&&Ne(e.then)&&Ne(e.catch),ah=Object.prototype.toString,Pa=e=>ah.call(e),ye=e=>Pa(e)==="[object Object]",v0=e=>e==null?"":qe(e)||ye(e)&&e.toString===ah?JSON.stringify(e,null,2):String(e);function Fa(e,t=""){return e.reduce((n,r,i)=>i===0?n+r:n+t+r,"")}function y0(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const ui=e=>!Se(e)||qe(e);function ho(e,t){if(ui(e)||ui(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:r,des:i}=n.pop();Object.keys(r).forEach(o=>{o!=="__proto__"&&(Se(r[o])&&!Se(i[o])&&(i[o]=Array.isArray(r[o])?[]:De()),ui(i[o])||ui(r[o])?i[o]=r[o]:n.push({src:r[o],des:i[o]}))})}}/*!
  * message-compiler v11.1.2
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function b0(e,t,n){return{line:e,column:t,offset:n}}function ia(e,t,n){return{start:e,end:t}}const Me={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14},E0=17;function Yo(e,t,n={}){const{domain:r,messages:i,args:o}=n,u=e,c=new SyntaxError(String(u));return c.code=e,t&&(c.location=t),c.domain=r,c}function A0(e){throw e}const Ut=" ",w0="\r",rt="\n",O0="\u2028",T0="\u2029";function S0(e){const t=e;let n=0,r=1,i=1,o=0;const u=O=>t[O]===w0&&t[O+1]===rt,c=O=>t[O]===rt,l=O=>t[O]===T0,s=O=>t[O]===O0,a=O=>u(O)||c(O)||l(O)||s(O),d=()=>n,f=()=>r,h=()=>i,p=()=>o,m=O=>u(O)||l(O)||s(O)?rt:t[O],_=()=>m(n),g=()=>m(n+o);function v(){return o=0,a(n)&&(r++,i=0),u(n)&&n++,n++,i++,t[n]}function b(){return u(n+o)&&o++,o++,t[n+o]}function E(){n=0,r=1,i=1,o=0}function D(O=0){o=O}function I(){const O=n+o;for(;O!==n;)v();o=0}return{index:d,line:f,column:h,peekOffset:p,charAt:m,currentChar:_,currentPeek:g,next:v,peek:b,reset:E,resetPeek:D,skipToPeek:I}}const Zt=void 0,C0=".",il="'",I0="tokenizer";function M0(e,t={}){const n=t.location!==!1,r=S0(e),i=()=>r.index(),o=()=>b0(r.line(),r.column(),r.index()),u=o(),c=i(),l={currentType:13,offset:c,startLoc:u,endLoc:u,lastType:13,lastOffset:c,lastStartLoc:u,lastEndLoc:u,braceNest:0,inLinked:!1,text:""},s=()=>l,{onError:a}=t;function d(A,w,x,...H){const ee=s();if(w.column+=x,w.offset+=x,a){const z=n?ia(ee.startLoc,w):null,P=Yo(A,z,{domain:I0,args:H});a(P)}}function f(A,w,x){A.endLoc=o(),A.currentType=w;const H={type:w};return n&&(H.loc=ia(A.startLoc,A.endLoc)),x!=null&&(H.value=x),H}const h=A=>f(A,13);function p(A,w){return A.currentChar()===w?(A.next(),w):(d(Me.EXPECTED_TOKEN,o(),0,w),"")}function m(A){let w="";for(;A.currentPeek()===Ut||A.currentPeek()===rt;)w+=A.currentPeek(),A.peek();return w}function _(A){const w=m(A);return A.skipToPeek(),w}function g(A){if(A===Zt)return!1;const w=A.charCodeAt(0);return w>=97&&w<=122||w>=65&&w<=90||w===95}function v(A){if(A===Zt)return!1;const w=A.charCodeAt(0);return w>=48&&w<=57}function b(A,w){const{currentType:x}=w;if(x!==2)return!1;m(A);const H=g(A.currentPeek());return A.resetPeek(),H}function E(A,w){const{currentType:x}=w;if(x!==2)return!1;m(A);const H=A.currentPeek()==="-"?A.peek():A.currentPeek(),ee=v(H);return A.resetPeek(),ee}function D(A,w){const{currentType:x}=w;if(x!==2)return!1;m(A);const H=A.currentPeek()===il;return A.resetPeek(),H}function I(A,w){const{currentType:x}=w;if(x!==7)return!1;m(A);const H=A.currentPeek()===".";return A.resetPeek(),H}function O(A,w){const{currentType:x}=w;if(x!==8)return!1;m(A);const H=g(A.currentPeek());return A.resetPeek(),H}function M(A,w){const{currentType:x}=w;if(!(x===7||x===11))return!1;m(A);const H=A.currentPeek()===":";return A.resetPeek(),H}function T(A,w){const{currentType:x}=w;if(x!==9)return!1;const H=()=>{const z=A.currentPeek();return z==="{"?g(A.peek()):z==="@"||z==="|"||z===":"||z==="."||z===Ut||!z?!1:z===rt?(A.peek(),H()):R(A,!1)},ee=H();return A.resetPeek(),ee}function k(A){m(A);const w=A.currentPeek()==="|";return A.resetPeek(),w}function R(A,w=!0){const x=(ee=!1,z="")=>{const P=A.currentPeek();return P==="{"||P==="@"||!P?ee:P==="|"?!(z===Ut||z===rt):P===Ut?(A.peek(),x(!0,Ut)):P===rt?(A.peek(),x(!0,rt)):!0},H=x();return w&&A.resetPeek(),H}function L(A,w){const x=A.currentChar();return x===Zt?Zt:w(x)?(A.next(),x):null}function B(A){const w=A.charCodeAt(0);return w>=97&&w<=122||w>=65&&w<=90||w>=48&&w<=57||w===95||w===36}function N(A){return L(A,B)}function G(A){const w=A.charCodeAt(0);return w>=97&&w<=122||w>=65&&w<=90||w>=48&&w<=57||w===95||w===36||w===45}function Y(A){return L(A,G)}function W(A){const w=A.charCodeAt(0);return w>=48&&w<=57}function V(A){return L(A,W)}function J(A){const w=A.charCodeAt(0);return w>=48&&w<=57||w>=65&&w<=70||w>=97&&w<=102}function te(A){return L(A,J)}function ie(A){let w="",x="";for(;w=V(A);)x+=w;return x}function ue(A){let w="";for(;;){const x=A.currentChar();if(x==="{"||x==="}"||x==="@"||x==="|"||!x)break;if(x===Ut||x===rt)if(R(A))w+=x,A.next();else{if(k(A))break;w+=x,A.next()}else w+=x,A.next()}return w}function me(A){_(A);let w="",x="";for(;w=Y(A);)x+=w;return A.currentChar()===Zt&&d(Me.UNTERMINATED_CLOSING_BRACE,o(),0),x}function Ee(A){_(A);let w="";return A.currentChar()==="-"?(A.next(),w+="-".concat(ie(A))):w+=ie(A),A.currentChar()===Zt&&d(Me.UNTERMINATED_CLOSING_BRACE,o(),0),w}function Oe(A){return A!==il&&A!==rt}function Ae(A){_(A),p(A,"'");let w="",x="";for(;w=L(A,Oe);)w==="\\"?x+=U(A):x+=w;const H=A.currentChar();return H===rt||H===Zt?(d(Me.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),0),H===rt&&(A.next(),p(A,"'")),x):(p(A,"'"),x)}function U(A){const w=A.currentChar();switch(w){case"\\":case"'":return A.next(),"\\".concat(w);case"u":return K(A,w,4);case"U":return K(A,w,6);default:return d(Me.UNKNOWN_ESCAPE_SEQUENCE,o(),0,w),""}}function K(A,w,x){p(A,w);let H="";for(let ee=0;ee<x;ee++){const z=te(A);if(!z){d(Me.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),0,"\\".concat(w).concat(H).concat(A.currentChar()));break}H+=z}return"\\".concat(w).concat(H)}function X(A){return A!=="{"&&A!=="}"&&A!==Ut&&A!==rt}function Q(A){_(A);let w="",x="";for(;w=L(A,X);)x+=w;return x}function fe(A){let w="",x="";for(;w=N(A);)x+=w;return x}function S(A){const w=x=>{const H=A.currentChar();return H==="{"||H==="@"||H==="|"||H==="("||H===")"||!H||H===Ut?x:(x+=H,A.next(),w(x))};return w("")}function y(A){_(A);const w=p(A,"|");return _(A),w}function C(A,w){let x=null;switch(A.currentChar()){case"{":return w.braceNest>=1&&d(Me.NOT_ALLOW_NEST_PLACEHOLDER,o(),0),A.next(),x=f(w,2,"{"),_(A),w.braceNest++,x;case"}":return w.braceNest>0&&w.currentType===2&&d(Me.EMPTY_PLACEHOLDER,o(),0),A.next(),x=f(w,3,"}"),w.braceNest--,w.braceNest>0&&_(A),w.inLinked&&w.braceNest===0&&(w.inLinked=!1),x;case"@":return w.braceNest>0&&d(Me.UNTERMINATED_CLOSING_BRACE,o(),0),x=F(A,w)||h(w),w.braceNest=0,x;default:{let ee=!0,z=!0,P=!0;if(k(A))return w.braceNest>0&&d(Me.UNTERMINATED_CLOSING_BRACE,o(),0),x=f(w,1,y(A)),w.braceNest=0,w.inLinked=!1,x;if(w.braceNest>0&&(w.currentType===4||w.currentType===5||w.currentType===6))return d(Me.UNTERMINATED_CLOSING_BRACE,o(),0),w.braceNest=0,$(A,w);if(ee=b(A,w))return x=f(w,4,me(A)),_(A),x;if(z=E(A,w))return x=f(w,5,Ee(A)),_(A),x;if(P=D(A,w))return x=f(w,6,Ae(A)),_(A),x;if(!ee&&!z&&!P)return x=f(w,12,Q(A)),d(Me.INVALID_TOKEN_IN_PLACEHOLDER,o(),0,x.value),_(A),x;break}}return x}function F(A,w){const{currentType:x}=w;let H=null;const ee=A.currentChar();switch((x===7||x===8||x===11||x===9)&&(ee===rt||ee===Ut)&&d(Me.INVALID_LINKED_FORMAT,o(),0),ee){case"@":return A.next(),H=f(w,7,"@"),w.inLinked=!0,H;case".":return _(A),A.next(),f(w,8,".");case":":return _(A),A.next(),f(w,9,":");default:return k(A)?(H=f(w,1,y(A)),w.braceNest=0,w.inLinked=!1,H):I(A,w)||M(A,w)?(_(A),F(A,w)):O(A,w)?(_(A),f(w,11,fe(A))):T(A,w)?(_(A),ee==="{"?C(A,w)||H:f(w,10,S(A))):(x===7&&d(Me.INVALID_LINKED_FORMAT,o(),0),w.braceNest=0,w.inLinked=!1,$(A,w))}}function $(A,w){let x={type:13};if(w.braceNest>0)return C(A,w)||h(w);if(w.inLinked)return F(A,w)||h(w);switch(A.currentChar()){case"{":return C(A,w)||h(w);case"}":return d(Me.UNBALANCED_CLOSING_BRACE,o(),0),A.next(),f(w,3,"}");case"@":return F(A,w)||h(w);default:{if(k(A))return x=f(w,1,y(A)),w.braceNest=0,w.inLinked=!1,x;if(R(A))return f(w,0,ue(A));break}}return x}function j(){const{currentType:A,offset:w,startLoc:x,endLoc:H}=l;return l.lastType=A,l.lastOffset=w,l.lastStartLoc=x,l.lastEndLoc=H,l.offset=i(),l.startLoc=o(),r.currentChar()===Zt?f(l,13):$(r,l)}return{nextToken:j,currentOffset:i,currentPosition:o,context:s}}const L0="parser",D0=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function R0(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"�"}}}function k0(e={}){const t=e.location!==!1,{onError:n}=e;function r(g,v,b,E,...D){const I=g.currentPosition();if(I.offset+=E,I.column+=E,n){const O=t?ia(b,I):null,M=Yo(v,O,{domain:L0,args:D});n(M)}}function i(g,v,b){const E={type:g};return t&&(E.start=v,E.end=v,E.loc={start:b,end:b}),E}function o(g,v,b,E){t&&(g.end=v,g.loc&&(g.loc.end=b))}function u(g,v){const b=g.context(),E=i(3,b.offset,b.startLoc);return E.value=v,o(E,g.currentOffset(),g.currentPosition()),E}function c(g,v){const b=g.context(),{lastOffset:E,lastStartLoc:D}=b,I=i(5,E,D);return I.index=parseInt(v,10),g.nextToken(),o(I,g.currentOffset(),g.currentPosition()),I}function l(g,v){const b=g.context(),{lastOffset:E,lastStartLoc:D}=b,I=i(4,E,D);return I.key=v,g.nextToken(),o(I,g.currentOffset(),g.currentPosition()),I}function s(g,v){const b=g.context(),{lastOffset:E,lastStartLoc:D}=b,I=i(9,E,D);return I.value=v.replace(D0,R0),g.nextToken(),o(I,g.currentOffset(),g.currentPosition()),I}function a(g){const v=g.nextToken(),b=g.context(),{lastOffset:E,lastStartLoc:D}=b,I=i(8,E,D);return v.type!==11?(r(g,Me.UNEXPECTED_EMPTY_LINKED_MODIFIER,b.lastStartLoc,0),I.value="",o(I,E,D),{nextConsumeToken:v,node:I}):(v.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,kt(v)),I.value=v.value||"",o(I,g.currentOffset(),g.currentPosition()),{node:I})}function d(g,v){const b=g.context(),E=i(7,b.offset,b.startLoc);return E.value=v,o(E,g.currentOffset(),g.currentPosition()),E}function f(g){const v=g.context(),b=i(6,v.offset,v.startLoc);let E=g.nextToken();if(E.type===8){const D=a(g);b.modifier=D.node,E=D.nextConsumeToken||g.nextToken()}switch(E.type!==9&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),E=g.nextToken(),E.type===2&&(E=g.nextToken()),E.type){case 10:E.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),b.key=d(g,E.value||"");break;case 4:E.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),b.key=l(g,E.value||"");break;case 5:E.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),b.key=c(g,E.value||"");break;case 6:E.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),b.key=s(g,E.value||"");break;default:{r(g,Me.UNEXPECTED_EMPTY_LINKED_KEY,v.lastStartLoc,0);const D=g.context(),I=i(7,D.offset,D.startLoc);return I.value="",o(I,D.offset,D.startLoc),b.key=I,o(b,D.offset,D.startLoc),{nextConsumeToken:E,node:b}}}return o(b,g.currentOffset(),g.currentPosition()),{node:b}}function h(g){const v=g.context(),b=v.currentType===1?g.currentOffset():v.offset,E=v.currentType===1?v.endLoc:v.startLoc,D=i(2,b,E);D.items=[];let I=null;do{const T=I||g.nextToken();switch(I=null,T.type){case 0:T.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(T)),D.items.push(u(g,T.value||""));break;case 5:T.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(T)),D.items.push(c(g,T.value||""));break;case 4:T.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(T)),D.items.push(l(g,T.value||""));break;case 6:T.value==null&&r(g,Me.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(T)),D.items.push(s(g,T.value||""));break;case 7:{const k=f(g);D.items.push(k.node),I=k.nextConsumeToken||null;break}}}while(v.currentType!==13&&v.currentType!==1);const O=v.currentType===1?v.lastOffset:g.currentOffset(),M=v.currentType===1?v.lastEndLoc:g.currentPosition();return o(D,O,M),D}function p(g,v,b,E){const D=g.context();let I=E.items.length===0;const O=i(1,v,b);O.cases=[],O.cases.push(E);do{const M=h(g);I||(I=M.items.length===0),O.cases.push(M)}while(D.currentType!==13);return I&&r(g,Me.MUST_HAVE_MESSAGES_IN_PLURAL,b,0),o(O,g.currentOffset(),g.currentPosition()),O}function m(g){const v=g.context(),{offset:b,startLoc:E}=v,D=h(g);return v.currentType===13?D:p(g,b,E,D)}function _(g){const v=M0(g,We({},e)),b=v.context(),E=i(0,b.offset,b.startLoc);return t&&E.loc&&(E.loc.source=g),E.body=m(v),e.onCacheKey&&(E.cacheKey=e.onCacheKey(g)),b.currentType!==13&&r(v,Me.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,g[b.offset]||""),o(E,v.currentOffset(),v.currentPosition()),E}return{parse:_}}function kt(e){if(e.type===13)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function N0(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:o=>(n.helpers.add(o),o)}}function ol(e,t){for(let n=0;n<e.length;n++)Ba(e[n],t)}function Ba(e,t){switch(e.type){case 1:ol(e.cases,t),t.helper("plural");break;case 2:ol(e.items,t);break;case 6:{Ba(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function x0(e,t={}){const n=N0(e);n.helper("normalize"),e.body&&Ba(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function P0(e){const t=e.body;return t.type===2?sl(t):t.cases.forEach(n=>sl(n)),e}function sl(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=Fa(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}function qn(e){switch(e.t=e.type,e.type){case 0:{const t=e;qn(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let r=0;r<n.length;r++)qn(n[r]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let r=0;r<n.length;r++)qn(n[r]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;qn(t.key),t.k=t.key,delete t.key,t.modifier&&(qn(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function F0(e,t){const{filename:n,breakLineCode:r,needIndent:i}=t,o=t.location!==!1,u={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:i,indentLevel:0};o&&e.loc&&(u.source=e.loc.source);const c=()=>u;function l(m,_){u.code+=m}function s(m,_=!0){const g=_?r:"";l(i?g+"  ".repeat(m):g)}function a(m=!0){const _=++u.indentLevel;m&&s(_)}function d(m=!0){const _=--u.indentLevel;m&&s(_)}function f(){s(u.indentLevel)}return{context:c,push:l,indent:a,deindent:d,newline:f,helper:m=>"_".concat(m),needIndent:()=>u.needIndent}}function B0(e,t){const{helper:n}=e;e.push("".concat(n("linked"),"(")),Qn(e,t.key),t.modifier?(e.push(", "),Qn(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function $0(e,t){const{helper:n,needIndent:r}=e;e.push("".concat(n("normalize"),"([")),e.indent(r());const i=t.items.length;for(let o=0;o<i&&(Qn(e,t.items[o]),o!==i-1);o++)e.push(", ");e.deindent(r()),e.push("])")}function H0(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push("".concat(n("plural"),"([")),e.indent(r());const i=t.cases.length;for(let o=0;o<i&&(Qn(e,t.cases[o]),o!==i-1);o++)e.push(", ");e.deindent(r()),e.push("])")}}function j0(e,t){t.body?Qn(e,t.body):e.push("null")}function Qn(e,t){const{helper:n}=e;switch(t.type){case 0:j0(e,t);break;case 1:H0(e,t);break;case 2:$0(e,t);break;case 6:B0(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push("".concat(n("interpolate"),"(").concat(n("list"),"(").concat(t.index,"))"),t);break;case 4:e.push("".concat(n("interpolate"),"(").concat(n("named"),"(").concat(JSON.stringify(t.key),"))"),t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const U0=(e,t={})=>{const n=re(t.mode)?t.mode:"normal",r=re(t.filename)?t.filename:"message.intl";t.sourceMap;const i=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":"\n",o=t.needIndent?t.needIndent:n!=="arrow",u=e.helpers||[],c=F0(e,{filename:r,breakLineCode:i,needIndent:o});c.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(o),u.length>0&&(c.push("const { ".concat(Fa(u.map(a=>"".concat(a,": _").concat(a)),", ")," } = ctx")),c.newline()),c.push("return "),Qn(c,e),c.deindent(o),c.push("}"),delete e.helpers;const{code:l,map:s}=c.context();return{ast:e,code:l,map:s?s.toJSON():void 0}};function Y0(e,t={}){const n=We({},t),r=!!n.jit,i=!!n.minify,o=n.optimize==null?!0:n.optimize,c=k0(n).parse(e);return r?(o&&P0(c),i&&qn(c),{ast:c,code:""}):(x0(c,n),U0(c,n))}/*!
  * core-base v11.1.2
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function q0(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Tn().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Tn().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function ds(e){return n=>G0(n,e)}function G0(e,t){const n=W0(t);if(n==null)throw Pr(0);if($a(n)===1){const o=X0(n);return e.plural(o.reduce((u,c)=>[...u,al(e,c)],[]))}else return al(e,n)}const V0=["b","body"];function W0(e){return pn(e,V0)}const z0=["c","cases"];function X0(e){return pn(e,z0,[])}function al(e,t){const n=Q0(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const r=Z0(t).reduce((i,o)=>[...i,oa(e,o)],[]);return e.normalize(r)}}const K0=["s","static"];function Q0(e){return pn(e,K0)}const J0=["i","items"];function Z0(e){return pn(e,J0,[])}function oa(e,t){const n=$a(t);switch(n){case 3:return li(t,n);case 9:return li(t,n);case 4:{const r=t;if(St(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(St(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Pr(n)}case 5:{const r=t;if(St(r,"i")&&Ye(r.i))return e.interpolate(e.list(r.i));if(St(r,"index")&&Ye(r.index))return e.interpolate(e.list(r.index));throw Pr(n)}case 6:{const r=t,i=r1(r),o=o1(r);return e.linked(oa(e,o),i?oa(e,i):void 0,e.type)}case 7:return li(t,n);case 8:return li(t,n);default:throw new Error("unhandled node on format message part: ".concat(n))}}const e1=["t","type"];function $a(e){return pn(e,e1)}const t1=["v","value"];function li(e,t){const n=pn(e,t1);if(n)return n;throw Pr(t)}const n1=["m","modifier"];function r1(e){return pn(e,n1)}const i1=["k","key"];function o1(e){const t=pn(e,i1);if(t)return t;throw Pr(6)}function pn(e,t,n){for(let r=0;r<t.length;r++){const i=t[r];if(St(e,i)&&e[i]!=null)return e[i]}return n}function Pr(e){return new Error("unhandled node type: ".concat(e))}const s1=e=>e;let ci=De();function Jn(e){return Se(e)&&$a(e)===0&&(St(e,"b")||St(e,"body"))}function a1(e,t={}){let n=!1;const r=t.onError||A0;return t.onError=i=>{n=!0,r(i)},{...Y0(e,t),detectError:n}}function u1(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&re(e)){Te(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||s1)(e),i=ci[r];if(i)return i;const{ast:o,detectError:u}=a1(e,{...t,location:!1,jit:!0}),c=ds(o);return u?c:ci[r]=c}else{const n=e.cacheKey;if(n){const r=ci[n];return r||(ci[n]=ds(e))}else return ds(e)}}let Fr=null;function l1(e){Fr=e}function c1(e,t,n){Fr&&Fr.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const f1=d1("function:translate");function d1(e){return t=>Fr&&Fr.emit(e,t)}const zt={INVALID_ARGUMENT:E0,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},h1=24;function Xt(e){return Yo(e,null,void 0)}function Ha(e,t){return t.locale!=null?ul(t.locale):ul(e.locale)}let hs;function ul(e){if(re(e))return e;if(Ne(e)){if(e.resolvedOnce&&hs!=null)return hs;if(e.constructor.name==="Function"){const t=e();if(_0(t))throw Xt(zt.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return hs=t}else throw Xt(zt.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw Xt(zt.NOT_SUPPORT_LOCALE_TYPE)}function m1(e,t,n){return[...new Set([n,...qe(t)?t:Se(t)?Object.keys(t):re(t)?[t]:[n]])]}function uh(e,t,n){const r=re(n)?n:Br,i=e;i.__localeChainCache||(i.__localeChainCache=new Map);let o=i.__localeChainCache.get(r);if(!o){o=[];let u=[n];for(;qe(u);)u=ll(o,u,t);const c=qe(t)||!ye(t)?t:t.default?t.default:null;u=re(c)?[c]:c,qe(u)&&ll(o,u,!1),i.__localeChainCache.set(r,o)}return o}function ll(e,t,n){let r=!0;for(let i=0;i<t.length&&Te(r);i++){const o=t[i];re(o)&&(r=p1(e,t[i],n))}return r}function p1(e,t,n){let r;const i=t.split("-");do{const o=i.join("-");r=g1(e,o,n),i.splice(-1,1)}while(i.length&&r===!0);return r}function g1(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const i=t.replace(/!/g,"");e.push(i),(qe(n)||ye(n))&&n[i]&&(r=n[i])}return r}const gn=[];gn[0]={w:[0],i:[3,0],"[":[4],o:[7]};gn[1]={w:[1],".":[2],"[":[4],o:[7]};gn[2]={w:[2],i:[3,0],0:[3,0]};gn[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};gn[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};gn[5]={"'":[4,0],o:8,l:[5,0]};gn[6]={'"':[4,0],o:8,l:[6,0]};const _1=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function v1(e){return _1.test(e)}function y1(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function b1(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function E1(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:v1(t)?y1(t):"*"+t}function A1(e){const t=[];let n=-1,r=0,i=0,o,u,c,l,s,a,d;const f=[];f[0]=()=>{u===void 0?u=c:u+=c},f[1]=()=>{u!==void 0&&(t.push(u),u=void 0)},f[2]=()=>{f[0](),i++},f[3]=()=>{if(i>0)i--,r=4,f[0]();else{if(i=0,u===void 0||(u=E1(u),u===!1))return!1;f[1]()}};function h(){const p=e[n+1];if(r===5&&p==="'"||r===6&&p==='"')return n++,c="\\"+p,f[0](),!0}for(;r!==null;)if(n++,o=e[n],!(o==="\\"&&h())){if(l=b1(o),d=gn[r],s=d[l]||d.l||8,s===8||(r=s[0],s[1]!==void 0&&(a=f[s[1]],a&&(c=o,a()===!1))))return;if(r===7)return t}}const cl=new Map;function w1(e,t){return Se(e)?e[t]:null}function O1(e,t){if(!Se(e))return null;let n=cl.get(t);if(n||(n=A1(t),n&&cl.set(t,n)),!n)return null;const r=n.length;let i=e,o=0;for(;o<r;){const u=i[n[o]];if(u===void 0||Ne(i))return null;i=u,o++}return i}const T1="11.1.2",qo=-1,Br="en-US",fl="",dl=e=>"".concat(e.charAt(0).toLocaleUpperCase()).concat(e.substr(1));function S1(){return{upper:(e,t)=>t==="text"&&re(e)?e.toUpperCase():t==="vnode"&&Se(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&re(e)?e.toLowerCase():t==="vnode"&&Se(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&re(e)?dl(e):t==="vnode"&&Se(e)&&"__v_isVNode"in e?dl(e.children):e}}let lh;function C1(e){lh=e}let ch;function I1(e){ch=e}let fh;function M1(e){fh=e}let dh=null;const L1=e=>{dh=e},D1=()=>dh;let hh=null;const hl=e=>{hh=e},R1=()=>hh;let ml=0;function k1(e={}){const t=Ne(e.onWarn)?e.onWarn:y0,n=re(e.version)?e.version:T1,r=re(e.locale)||Ne(e.locale)?e.locale:Br,i=Ne(r)?Br:r,o=qe(e.fallbackLocale)||ye(e.fallbackLocale)||re(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:i,u=ye(e.messages)?e.messages:ms(i),c=ye(e.datetimeFormats)?e.datetimeFormats:ms(i),l=ye(e.numberFormats)?e.numberFormats:ms(i),s=We(De(),e.modifiers,S1()),a=e.pluralRules||De(),d=Ne(e.missing)?e.missing:null,f=Te(e.missingWarn)||Kn(e.missingWarn)?e.missingWarn:!0,h=Te(e.fallbackWarn)||Kn(e.fallbackWarn)?e.fallbackWarn:!0,p=!!e.fallbackFormat,m=!!e.unresolving,_=Ne(e.postTranslation)?e.postTranslation:null,g=ye(e.processor)?e.processor:null,v=Te(e.warnHtmlMessage)?e.warnHtmlMessage:!0,b=!!e.escapeParameter,E=Ne(e.messageCompiler)?e.messageCompiler:lh,D=Ne(e.messageResolver)?e.messageResolver:ch||w1,I=Ne(e.localeFallbacker)?e.localeFallbacker:fh||m1,O=Se(e.fallbackContext)?e.fallbackContext:void 0,M=e,T=Se(M.__datetimeFormatters)?M.__datetimeFormatters:new Map,k=Se(M.__numberFormatters)?M.__numberFormatters:new Map,R=Se(M.__meta)?M.__meta:{};ml++;const L={version:n,cid:ml,locale:r,fallbackLocale:o,messages:u,modifiers:s,pluralRules:a,missing:d,missingWarn:f,fallbackWarn:h,fallbackFormat:p,unresolving:m,postTranslation:_,processor:g,warnHtmlMessage:v,escapeParameter:b,messageCompiler:E,messageResolver:D,localeFallbacker:I,fallbackContext:O,onWarn:t,__meta:R};return L.datetimeFormats=c,L.numberFormats=l,L.__datetimeFormatters=T,L.__numberFormatters=k,__INTLIFY_PROD_DEVTOOLS__&&c1(L,n,R),L}const ms=e=>({[e]:De()});function ja(e,t,n,r,i){const{missing:o,onWarn:u}=e;if(o!==null){const c=o(e,n,t,i);return re(c)?c:t}else return t}function sr(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function N1(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function x1(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let r=n+1;r<t.length;r++)if(N1(e,t[r]))return!0;return!1}function pl(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:i,onWarn:o,localeFallbacker:u}=e,{__datetimeFormatters:c}=e,[l,s,a,d]=sa(...t),f=Te(a.missingWarn)?a.missingWarn:e.missingWarn;Te(a.fallbackWarn)?a.fallbackWarn:e.fallbackWarn;const h=!!a.part,p=Ha(e,a),m=u(e,i,p);if(!re(l)||l==="")return new Intl.DateTimeFormat(p,d).format(s);let _={},g,v=null;const b="datetime format";for(let I=0;I<m.length&&(g=m[I],_=n[g]||{},v=_[l],!ye(v));I++)ja(e,l,g,f,b);if(!ye(v)||!re(g))return r?qo:l;let E="".concat(g,"__").concat(l);Uo(d)||(E="".concat(E,"__").concat(JSON.stringify(d)));let D=c.get(E);return D||(D=new Intl.DateTimeFormat(g,We({},v,d)),c.set(E,D)),h?D.formatToParts(s):D.format(s)}const mh=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function sa(...e){const[t,n,r,i]=e,o=De();let u=De(),c;if(re(t)){const l=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!l)throw Xt(zt.INVALID_ISO_DATE_ARGUMENT);const s=l[3]?l[3].trim().startsWith("T")?"".concat(l[1].trim()).concat(l[3].trim()):"".concat(l[1].trim(),"T").concat(l[3].trim()):l[1].trim();c=new Date(s);try{c.toISOString()}catch(a){throw Xt(zt.INVALID_ISO_DATE_ARGUMENT)}}else if(m0(t)){if(isNaN(t.getTime()))throw Xt(zt.INVALID_DATE_ARGUMENT);c=t}else if(Ye(t))c=t;else throw Xt(zt.INVALID_ARGUMENT);return re(n)?o.key=n:ye(n)&&Object.keys(n).forEach(l=>{mh.includes(l)?u[l]=n[l]:o[l]=n[l]}),re(r)?o.locale=r:ye(r)&&(u=r),ye(i)&&(u=i),[o.key||"",c,o,u]}function gl(e,t,n){const r=e;for(const i in n){const o="".concat(t,"__").concat(i);r.__datetimeFormatters.has(o)&&r.__datetimeFormatters.delete(o)}}function _l(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:i,onWarn:o,localeFallbacker:u}=e,{__numberFormatters:c}=e,[l,s,a,d]=aa(...t),f=Te(a.missingWarn)?a.missingWarn:e.missingWarn;Te(a.fallbackWarn)?a.fallbackWarn:e.fallbackWarn;const h=!!a.part,p=Ha(e,a),m=u(e,i,p);if(!re(l)||l==="")return new Intl.NumberFormat(p,d).format(s);let _={},g,v=null;const b="number format";for(let I=0;I<m.length&&(g=m[I],_=n[g]||{},v=_[l],!ye(v));I++)ja(e,l,g,f,b);if(!ye(v)||!re(g))return r?qo:l;let E="".concat(g,"__").concat(l);Uo(d)||(E="".concat(E,"__").concat(JSON.stringify(d)));let D=c.get(E);return D||(D=new Intl.NumberFormat(g,We({},v,d)),c.set(E,D)),h?D.formatToParts(s):D.format(s)}const ph=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function aa(...e){const[t,n,r,i]=e,o=De();let u=De();if(!Ye(t))throw Xt(zt.INVALID_ARGUMENT);const c=t;return re(n)?o.key=n:ye(n)&&Object.keys(n).forEach(l=>{ph.includes(l)?u[l]=n[l]:o[l]=n[l]}),re(r)?o.locale=r:ye(r)&&(u=r),ye(i)&&(u=i),[o.key||"",c,o,u]}function vl(e,t,n){const r=e;for(const i in n){const o="".concat(t,"__").concat(i);r.__numberFormatters.has(o)&&r.__numberFormatters.delete(o)}}const P1=e=>e,F1=e=>"",B1="text",$1=e=>e.length===0?"":Fa(e),H1=v0;function yl(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function j1(e){const t=Ye(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Ye(e.named.count)||Ye(e.named.n))?Ye(e.named.count)?e.named.count:Ye(e.named.n)?e.named.n:t:t}function U1(e,t){t.count||(t.count=e),t.n||(t.n=e)}function Y1(e={}){const t=e.locale,n=j1(e),r=Se(e.pluralRules)&&re(t)&&Ne(e.pluralRules[t])?e.pluralRules[t]:yl,i=Se(e.pluralRules)&&re(t)&&Ne(e.pluralRules[t])?yl:void 0,o=g=>g[r(n,g.length,i)],u=e.list||[],c=g=>u[g],l=e.named||De();Ye(e.pluralIndex)&&U1(n,l);const s=g=>l[g];function a(g,v){const b=Ne(e.messages)?e.messages(g,!!v):Se(e.messages)?e.messages[g]:!1;return b||(e.parent?e.parent.message(g):F1)}const d=g=>e.modifiers?e.modifiers[g]:P1,f=ye(e.processor)&&Ne(e.processor.normalize)?e.processor.normalize:$1,h=ye(e.processor)&&Ne(e.processor.interpolate)?e.processor.interpolate:H1,p=ye(e.processor)&&re(e.processor.type)?e.processor.type:B1,_={list:c,named:s,plural:o,linked:(g,...v)=>{const[b,E]=v;let D="text",I="";v.length===1?Se(b)?(I=b.modifier||I,D=b.type||D):re(b)&&(I=b||I):v.length===2&&(re(b)&&(I=b||I),re(E)&&(D=E||D));const O=a(g,!0)(_),M=D==="vnode"&&qe(O)&&I?O[0]:O;return I?d(I)(M,D):M},message:a,type:p,interpolate:h,normalize:f,values:We(De(),u,l)};return _}const bl=()=>"",bt=e=>Ne(e);function El(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:i,messageCompiler:o,fallbackLocale:u,messages:c}=e,[l,s]=ua(...t),a=Te(s.missingWarn)?s.missingWarn:e.missingWarn,d=Te(s.fallbackWarn)?s.fallbackWarn:e.fallbackWarn,f=Te(s.escapeParameter)?s.escapeParameter:e.escapeParameter,h=!!s.resolvedMessage,p=re(s.default)||Te(s.default)?Te(s.default)?o?l:()=>l:s.default:n?o?l:()=>l:null,m=n||p!=null&&(re(p)||Ne(p)),_=Ha(e,s);f&&q1(s);let[g,v,b]=h?[l,_,c[_]||De()]:gh(e,l,_,u,d,a),E=g,D=l;if(!h&&!(re(E)||Jn(E)||bt(E))&&m&&(E=p,D=E),!h&&(!(re(E)||Jn(E)||bt(E))||!re(v)))return i?qo:l;let I=!1;const O=()=>{I=!0},M=bt(E)?E:_h(e,l,v,E,D,O);if(I)return E;const T=W1(e,v,b,s),k=Y1(T),R=G1(e,M,k),L=r?r(R,l):R;if(__INTLIFY_PROD_DEVTOOLS__){const B={timestamp:Date.now(),key:re(l)?l:bt(E)?E.key:"",locale:v||(bt(E)?E.locale:""),format:re(E)?E:bt(E)?E.source:"",message:L};B.meta=We({},e.__meta,D1()||{}),f1(B)}return L}function q1(e){qe(e.list)?e.list=e.list.map(t=>re(t)?rl(t):t):Se(e.named)&&Object.keys(e.named).forEach(t=>{re(e.named[t])&&(e.named[t]=rl(e.named[t]))})}function gh(e,t,n,r,i,o){const{messages:u,onWarn:c,messageResolver:l,localeFallbacker:s}=e,a=s(e,r,n);let d=De(),f,h=null;const p="translate";for(let m=0;m<a.length&&(f=a[m],d=u[f]||De(),(h=l(d,t))===null&&(h=d[t]),!(re(h)||Jn(h)||bt(h)));m++)if(!x1(f,a)){const _=ja(e,t,f,o,p);_!==t&&(h=_)}return[h,f,d]}function _h(e,t,n,r,i,o){const{messageCompiler:u,warnHtmlMessage:c}=e;if(bt(r)){const s=r;return s.locale=s.locale||n,s.key=s.key||t,s}if(u==null){const s=()=>r;return s.locale=n,s.key=t,s}const l=u(r,V1(e,n,i,r,c,o));return l.locale=n,l.key=t,l.source=r,l}function G1(e,t,n){return t(n)}function ua(...e){const[t,n,r]=e,i=De();if(!re(t)&&!Ye(t)&&!bt(t)&&!Jn(t))throw Xt(zt.INVALID_ARGUMENT);const o=Ye(t)?String(t):(bt(t),t);return Ye(n)?i.plural=n:re(n)?i.default=n:ye(n)&&!Uo(n)?i.named=n:qe(n)&&(i.list=n),Ye(r)?i.plural=r:re(r)?i.default=r:ye(r)&&We(i,r),[o,i]}function V1(e,t,n,r,i,o){return{locale:t,key:n,warnHtmlMessage:i,onError:u=>{throw o&&o(u),u},onCacheKey:u=>d0(t,n,u)}}function W1(e,t,n,r){const{modifiers:i,pluralRules:o,messageResolver:u,fallbackLocale:c,fallbackWarn:l,missingWarn:s,fallbackContext:a}=e,f={locale:t,modifiers:i,pluralRules:o,messages:(h,p)=>{let m=u(n,h);if(m==null&&(a||p)){const[,,_]=gh(a||e,h,t,c,l,s);m=u(_,h)}if(re(m)||Jn(m)){let _=!1;const v=_h(e,h,t,m,h,()=>{_=!0});return _?bl:v}else return bt(m)?m:bl}};return e.processor&&(f.processor=e.processor),r.list&&(f.list=r.list),r.named&&(f.named=r.named),Ye(r.plural)&&(f.pluralIndex=r.plural),f}q0();/*!
  * vue-i18n v11.1.2
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const z1="11.1.2";function X1(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(Tn().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(Tn().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Tn().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Tn().__INTLIFY_PROD_DEVTOOLS__=!1)}const at={UNEXPECTED_RETURN_TYPE:h1,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32};function gt(e,...t){return Yo(e,null,void 0)}const la=mn("__translateVNode"),ca=mn("__datetimeParts"),fa=mn("__numberParts"),vh=mn("__setPluralRules"),yh=mn("__injectWithOption"),da=mn("__dispose");function $r(e){if(!Se(e))return e;for(const t in e)if(St(e,t))if(!t.includes("."))Se(e[t])&&$r(e[t]);else{const n=t.split("."),r=n.length-1;let i=e,o=!1;for(let u=0;u<r;u++){if(n[u]==="__proto__")throw new Error("unsafe key: ".concat(n[u]));if(n[u]in i||(i[n[u]]=De()),!Se(i[n[u]])){o=!0;break}i=i[n[u]]}o||(i[n[r]]=e[t],delete e[t]),Se(i[n[r]])&&$r(i[n[r]])}return e}function Ua(e,t){const{messages:n,__i18n:r,messageResolver:i,flatJson:o}=t,u=ye(n)?n:qe(r)?De():{[e]:De()};if(qe(r)&&r.forEach(c=>{if("locale"in c&&"resource"in c){const{locale:l,resource:s}=c;l?(u[l]=u[l]||De(),ho(s,u[l])):ho(s,u)}else re(c)&&ho(JSON.parse(c),u)}),i==null&&o)for(const c in u)St(u,c)&&$r(u[c]);return u}function bh(e){return e.type}function Eh(e,t,n){let r=Se(t.messages)?t.messages:De();"__i18nGlobal"in n&&(r=Ua(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const i=Object.keys(r);i.length&&i.forEach(o=>{e.mergeLocaleMessage(o,r[o])});{if(Se(t.datetimeFormats)){const o=Object.keys(t.datetimeFormats);o.length&&o.forEach(u=>{e.mergeDateTimeFormat(u,t.datetimeFormats[u])})}if(Se(t.numberFormats)){const o=Object.keys(t.numberFormats);o.length&&o.forEach(u=>{e.mergeNumberFormat(u,t.numberFormats[u])})}}}function Al(e){return be(Gr,null,e,0)}const wl="__INTLIFY_META__",Ol=()=>[],K1=()=>!1;let Tl=0;function Sl(e){return(t,n,r,i)=>e(n,r,fn()||void 0,i)}const Q1=()=>{const e=fn();let t=null;return e&&(t=bh(e)[wl])?{[wl]:t}:null};function Ya(e={}){const{__root:t,__injectWithOption:n}=e,r=t===void 0,i=e.flatJson,o=Oo?_e:ud;let u=Te(e.inheritLocale)?e.inheritLocale:!0;const c=o(t&&u?t.locale.value:re(e.locale)?e.locale:Br),l=o(t&&u?t.fallbackLocale.value:re(e.fallbackLocale)||qe(e.fallbackLocale)||ye(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:c.value),s=o(Ua(c.value,e)),a=o(ye(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),d=o(ye(e.numberFormats)?e.numberFormats:{[c.value]:{}});let f=t?t.missingWarn:Te(e.missingWarn)||Kn(e.missingWarn)?e.missingWarn:!0,h=t?t.fallbackWarn:Te(e.fallbackWarn)||Kn(e.fallbackWarn)?e.fallbackWarn:!0,p=t?t.fallbackRoot:Te(e.fallbackRoot)?e.fallbackRoot:!0,m=!!e.fallbackFormat,_=Ne(e.missing)?e.missing:null,g=Ne(e.missing)?Sl(e.missing):null,v=Ne(e.postTranslation)?e.postTranslation:null,b=t?t.warnHtmlMessage:Te(e.warnHtmlMessage)?e.warnHtmlMessage:!0,E=!!e.escapeParameter;const D=t?t.modifiers:ye(e.modifiers)?e.modifiers:{};let I=e.pluralRules||t&&t.pluralRules,O;O=(()=>{r&&hl(null);const P={version:z1,locale:c.value,fallbackLocale:l.value,messages:s.value,modifiers:D,pluralRules:I,missing:g===null?void 0:g,missingWarn:f,fallbackWarn:h,fallbackFormat:m,unresolving:!0,postTranslation:v===null?void 0:v,warnHtmlMessage:b,escapeParameter:E,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};P.datetimeFormats=a.value,P.numberFormats=d.value,P.__datetimeFormatters=ye(O)?O.__datetimeFormatters:void 0,P.__numberFormatters=ye(O)?O.__numberFormatters:void 0;const q=k1(P);return r&&hl(q),q})(),sr(O,c.value,l.value);function T(){return[c.value,l.value,s.value,a.value,d.value]}const k=de({get:()=>c.value,set:P=>{O.locale=P,c.value=P}}),R=de({get:()=>l.value,set:P=>{O.fallbackLocale=P,l.value=P,sr(O,c.value,P)}}),L=de(()=>s.value),B=de(()=>a.value),N=de(()=>d.value);function G(){return Ne(v)?v:null}function Y(P){v=P,O.postTranslation=P}function W(){return _}function V(P){P!==null&&(g=Sl(P)),_=P,O.missing=g}const J=(P,q,se,ce,ve,ze)=>{T();let Ue;try{__INTLIFY_PROD_DEVTOOLS__,r||(O.fallbackContext=t?R1():void 0),Ue=P(O)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(O.fallbackContext=void 0)}if(se!=="translate exists"&&Ye(Ue)&&Ue===qo||se==="translate exists"&&!Ue){const[vt,Ot]=q();return t&&p?ce(t):ve(vt)}else{if(ze(Ue))return Ue;throw gt(at.UNEXPECTED_RETURN_TYPE)}};function te(...P){return J(q=>Reflect.apply(El,null,[q,...P]),()=>ua(...P),"translate",q=>Reflect.apply(q.t,q,[...P]),q=>q,q=>re(q))}function ie(...P){const[q,se,ce]=P;if(ce&&!Se(ce))throw gt(at.INVALID_ARGUMENT);return te(q,se,We({resolvedMessage:!0},ce||{}))}function ue(...P){return J(q=>Reflect.apply(pl,null,[q,...P]),()=>sa(...P),"datetime format",q=>Reflect.apply(q.d,q,[...P]),()=>fl,q=>re(q))}function me(...P){return J(q=>Reflect.apply(_l,null,[q,...P]),()=>aa(...P),"number format",q=>Reflect.apply(q.n,q,[...P]),()=>fl,q=>re(q))}function Ee(P){return P.map(q=>re(q)||Ye(q)||Te(q)?Al(String(q)):q)}const Ae={normalize:Ee,interpolate:P=>P,type:"vnode"};function U(...P){return J(q=>{let se;const ce=q;try{ce.processor=Ae,se=Reflect.apply(El,null,[ce,...P])}finally{ce.processor=null}return se},()=>ua(...P),"translate",q=>q[la](...P),q=>[Al(q)],q=>qe(q))}function K(...P){return J(q=>Reflect.apply(_l,null,[q,...P]),()=>aa(...P),"number format",q=>q[fa](...P),Ol,q=>re(q)||qe(q))}function X(...P){return J(q=>Reflect.apply(pl,null,[q,...P]),()=>sa(...P),"datetime format",q=>q[ca](...P),Ol,q=>re(q)||qe(q))}function Q(P){I=P,O.pluralRules=I}function fe(P,q){return J(()=>{if(!P)return!1;const se=re(q)?q:c.value,ce=C(se),ve=O.messageResolver(ce,P);return Jn(ve)||bt(ve)||re(ve)},()=>[P],"translate exists",se=>Reflect.apply(se.te,se,[P,q]),K1,se=>Te(se))}function S(P){let q=null;const se=uh(O,l.value,c.value);for(let ce=0;ce<se.length;ce++){const ve=s.value[se[ce]]||{},ze=O.messageResolver(ve,P);if(ze!=null){q=ze;break}}return q}function y(P){const q=S(P);return q!=null?q:t?t.tm(P)||{}:{}}function C(P){return s.value[P]||{}}function F(P,q){if(i){const se={[P]:q};for(const ce in se)St(se,ce)&&$r(se[ce]);q=se[P]}s.value[P]=q,O.messages=s.value}function $(P,q){s.value[P]=s.value[P]||{};const se={[P]:q};if(i)for(const ce in se)St(se,ce)&&$r(se[ce]);q=se[P],ho(q,s.value[P]),O.messages=s.value}function j(P){return a.value[P]||{}}function A(P,q){a.value[P]=q,O.datetimeFormats=a.value,gl(O,P,q)}function w(P,q){a.value[P]=We(a.value[P]||{},q),O.datetimeFormats=a.value,gl(O,P,q)}function x(P){return d.value[P]||{}}function H(P,q){d.value[P]=q,O.numberFormats=d.value,vl(O,P,q)}function ee(P,q){d.value[P]=We(d.value[P]||{},q),O.numberFormats=d.value,vl(O,P,q)}Tl++,t&&Oo&&(Ft(t.locale,P=>{u&&(c.value=P,O.locale=P,sr(O,c.value,l.value))}),Ft(t.fallbackLocale,P=>{u&&(l.value=P,O.fallbackLocale=P,sr(O,c.value,l.value))}));const z={id:Tl,locale:k,fallbackLocale:R,get inheritLocale(){return u},set inheritLocale(P){u=P,P&&t&&(c.value=t.locale.value,l.value=t.fallbackLocale.value,sr(O,c.value,l.value))},get availableLocales(){return Object.keys(s.value).sort()},messages:L,get modifiers(){return D},get pluralRules(){return I||{}},get isGlobal(){return r},get missingWarn(){return f},set missingWarn(P){f=P,O.missingWarn=f},get fallbackWarn(){return h},set fallbackWarn(P){h=P,O.fallbackWarn=h},get fallbackRoot(){return p},set fallbackRoot(P){p=P},get fallbackFormat(){return m},set fallbackFormat(P){m=P,O.fallbackFormat=m},get warnHtmlMessage(){return b},set warnHtmlMessage(P){b=P,O.warnHtmlMessage=P},get escapeParameter(){return E},set escapeParameter(P){E=P,O.escapeParameter=P},t:te,getLocaleMessage:C,setLocaleMessage:F,mergeLocaleMessage:$,getPostTranslationHandler:G,setPostTranslationHandler:Y,getMissingHandler:W,setMissingHandler:V,[vh]:Q};return z.datetimeFormats=B,z.numberFormats=N,z.rt=ie,z.te=fe,z.tm=y,z.d=ue,z.n=me,z.getDateTimeFormat=j,z.setDateTimeFormat=A,z.mergeDateTimeFormat=w,z.getNumberFormat=x,z.setNumberFormat=H,z.mergeNumberFormat=ee,z[yh]=n,z[la]=U,z[ca]=X,z[fa]=K,z}function J1(e){const t=re(e.locale)?e.locale:Br,n=re(e.fallbackLocale)||qe(e.fallbackLocale)||ye(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,r=Ne(e.missing)?e.missing:void 0,i=Te(e.silentTranslationWarn)||Kn(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,o=Te(e.silentFallbackWarn)||Kn(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,u=Te(e.fallbackRoot)?e.fallbackRoot:!0,c=!!e.formatFallbackMessages,l=ye(e.modifiers)?e.modifiers:{},s=e.pluralizationRules,a=Ne(e.postTranslation)?e.postTranslation:void 0,d=re(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,f=!!e.escapeParameterHtml,h=Te(e.sync)?e.sync:!0;let p=e.messages;if(ye(e.sharedMessages)){const D=e.sharedMessages;p=Object.keys(D).reduce((O,M)=>{const T=O[M]||(O[M]={});return We(T,D[M]),O},p||{})}const{__i18n:m,__root:_,__injectWithOption:g}=e,v=e.datetimeFormats,b=e.numberFormats,E=e.flatJson;return{locale:t,fallbackLocale:n,messages:p,flatJson:E,datetimeFormats:v,numberFormats:b,missing:r,missingWarn:i,fallbackWarn:o,fallbackRoot:u,fallbackFormat:c,modifiers:l,pluralRules:s,postTranslation:a,warnHtmlMessage:d,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:h,__i18n:m,__root:_,__injectWithOption:g}}function ha(e={}){const t=Ya(J1(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(i){t.locale.value=i},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(i){t.fallbackLocale.value=i},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(i){t.setMissingHandler(i)},get silentTranslationWarn(){return Te(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(i){t.missingWarn=Te(i)?!i:i},get silentFallbackWarn(){return Te(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(i){t.fallbackWarn=Te(i)?!i:i},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(i){t.fallbackFormat=i},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(i){t.setPostTranslationHandler(i)},get sync(){return t.inheritLocale},set sync(i){t.inheritLocale=i},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(i){t.warnHtmlMessage=i!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(i){t.escapeParameter=i},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...i){return Reflect.apply(t.t,t,[...i])},rt(...i){return Reflect.apply(t.rt,t,[...i])},te(i,o){return t.te(i,o)},tm(i){return t.tm(i)},getLocaleMessage(i){return t.getLocaleMessage(i)},setLocaleMessage(i,o){t.setLocaleMessage(i,o)},mergeLocaleMessage(i,o){t.mergeLocaleMessage(i,o)},d(...i){return Reflect.apply(t.d,t,[...i])},getDateTimeFormat(i){return t.getDateTimeFormat(i)},setDateTimeFormat(i,o){t.setDateTimeFormat(i,o)},mergeDateTimeFormat(i,o){t.mergeDateTimeFormat(i,o)},n(...i){return Reflect.apply(t.n,t,[...i])},getNumberFormat(i){return t.getNumberFormat(i)},setNumberFormat(i,o){t.setNumberFormat(i,o)},mergeNumberFormat(i,o){t.mergeNumberFormat(i,o)}};return r.__extender=n,r}function Z1(e,t,n){return{beforeCreate(){const r=fn();if(!r)throw gt(at.UNEXPECTED_ERROR);const i=this.$options;if(i.i18n){const o=i.i18n;if(i.__i18n&&(o.__i18n=i.__i18n),o.__root=t,this===this.$root)this.$i18n=Cl(e,o);else{o.__injectWithOption=!0,o.__extender=n.__vueI18nExtend,this.$i18n=ha(o);const u=this.$i18n;u.__extender&&(u.__disposer=u.__extender(this.$i18n))}}else if(i.__i18n)if(this===this.$root)this.$i18n=Cl(e,i);else{this.$i18n=ha({__i18n:i.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const o=this.$i18n;o.__extender&&(o.__disposer=o.__extender(this.$i18n))}else this.$i18n=e;i.__i18nGlobal&&Eh(t,i,i),this.$t=(...o)=>this.$i18n.t(...o),this.$rt=(...o)=>this.$i18n.rt(...o),this.$te=(o,u)=>this.$i18n.te(o,u),this.$d=(...o)=>this.$i18n.d(...o),this.$n=(...o)=>this.$i18n.n(...o),this.$tm=o=>this.$i18n.tm(o),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const r=fn();if(!r)throw gt(at.UNEXPECTED_ERROR);const i=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,i.__disposer&&(i.__disposer(),delete i.__disposer,delete i.__extender),n.__deleteInstance(r),delete this.$i18n}}}function Cl(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[vh](t.pluralizationRules||e.pluralizationRules);const n=Ua(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(r=>e.mergeLocaleMessage(r,n[r])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r])),t.numberFormats&&Object.keys(t.numberFormats).forEach(r=>e.mergeNumberFormat(r,t.numberFormats[r])),e}const qa={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function e_({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,i)=>[...r,...i.type===Be?i.children:[i]],[]):t.reduce((n,r)=>{const i=e[r];return i&&(n[r]=i()),n},De())}function Ah(){return Be}const t_=Ge({name:"i18n-t",props:We({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Ye(e)||!isNaN(e)}},qa),setup(e,t){const{slots:n,attrs:r}=t,i=e.i18n||_t({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter(d=>d!=="_"),u=De();e.locale&&(u.locale=e.locale),e.plural!==void 0&&(u.plural=re(e.plural)?+e.plural:e.plural);const c=e_(t,o),l=i[la](e.keypath,c,u),s=We(De(),r),a=re(e.tag)||Se(e.tag)?e.tag:Ah();return Wr(a,s,l)}}}),Il=t_;function n_(e){return qe(e)&&!re(e[0])}function wh(e,t,n,r){const{slots:i,attrs:o}=t;return()=>{const u={part:!0};let c=De();e.locale&&(u.locale=e.locale),re(e.format)?u.key=e.format:Se(e.format)&&(re(e.format.key)&&(u.key=e.format.key),c=Object.keys(e.format).reduce((f,h)=>n.includes(h)?We(De(),f,{[h]:e.format[h]}):f,De()));const l=r(e.value,u,c);let s=[u.key];qe(l)?s=l.map((f,h)=>{const p=i[f.type],m=p?p({[f.type]:f.value,index:h,parts:l}):[f.value];return n_(m)&&(m[0].key="".concat(f.type,"-").concat(h)),m}):re(l)&&(s=[l]);const a=We(De(),o),d=re(e.tag)||Se(e.tag)?e.tag:Ah();return Wr(d,a,s)}}const r_=Ge({name:"i18n-n",props:We({value:{type:Number,required:!0},format:{type:[String,Object]}},qa),setup(e,t){const n=e.i18n||_t({useScope:e.scope,__useComponent:!0});return wh(e,t,ph,(...r)=>n[fa](...r))}}),Ml=r_;function i_(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function o_(e){const t=u=>{const{instance:c,value:l}=u;if(!c||!c.$)throw gt(at.UNEXPECTED_ERROR);const s=i_(e,c.$),a=Ll(l);return[Reflect.apply(s.t,s,[...Dl(a)]),s]};return{created:(u,c)=>{const[l,s]=t(c);Oo&&e.global===s&&(u.__i18nWatcher=Ft(s.locale,()=>{c.instance&&c.instance.$forceUpdate()})),u.__composer=s,u.textContent=l},unmounted:u=>{Oo&&u.__i18nWatcher&&(u.__i18nWatcher(),u.__i18nWatcher=void 0,delete u.__i18nWatcher),u.__composer&&(u.__composer=void 0,delete u.__composer)},beforeUpdate:(u,{value:c})=>{if(u.__composer){const l=u.__composer,s=Ll(c);u.textContent=Reflect.apply(l.t,l,[...Dl(s)])}},getSSRProps:u=>{const[c]=t(u);return{textContent:c}}}}function Ll(e){if(re(e))return{path:e};if(ye(e)){if(!("path"in e))throw gt(at.REQUIRED_VALUE,"path");return e}else throw gt(at.INVALID_VALUE)}function Dl(e){const{path:t,locale:n,args:r,choice:i,plural:o}=e,u={},c=r||{};return re(n)&&(u.locale=n),Ye(i)&&(u.plural=i),Ye(o)&&(u.plural=o),[t,c,u]}function s_(e,t,...n){const r=ye(n[0])?n[0]:{};(Te(r.globalInstall)?r.globalInstall:!0)&&([Il.name,"I18nT"].forEach(o=>e.component(o,Il)),[Ml.name,"I18nN"].forEach(o=>e.component(o,Ml)),[kl.name,"I18nD"].forEach(o=>e.component(o,kl))),e.directive("t",o_(t))}const a_=mn("global-vue-i18n");function Oh(e={}){const t=__VUE_I18N_LEGACY_API__&&Te(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,n=Te(e.globalInjection)?e.globalInjection:!0,r=new Map,[i,o]=u_(e,t),u=mn("");function c(d){return r.get(d)||null}function l(d,f){r.set(d,f)}function s(d){r.delete(d)}const a={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(d,...f){if(d.__VUE_I18N_SYMBOL__=u,d.provide(d.__VUE_I18N_SYMBOL__,a),ye(f[0])){const m=f[0];a.__composerExtend=m.__composerExtend,a.__vueI18nExtend=m.__vueI18nExtend}let h=null;!t&&n&&(h=g_(d,a.global)),__VUE_I18N_FULL_INSTALL__&&s_(d,a,...f),__VUE_I18N_LEGACY_API__&&t&&d.mixin(Z1(o,o.__composer,a));const p=d.unmount;d.unmount=()=>{h&&h(),a.dispose(),p()}},get global(){return o},dispose(){i.stop()},__instances:r,__getInstance:c,__setInstance:l,__deleteInstance:s};return a}function _t(e={}){const t=fn();if(t==null)throw gt(at.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw gt(at.NOT_INSTALLED);const n=l_(t),r=f_(n),i=bh(t),o=c_(e,i);if(o==="global")return Eh(r,e,i),r;if(o==="parent"){let l=d_(n,t,e.__useComponent);return l==null&&(l=r),l}const u=n;let c=u.__getInstance(t);if(c==null){const l=We({},e);"__i18n"in i&&(l.__i18n=i.__i18n),r&&(l.__root=r),c=Ya(l),u.__composerExtend&&(c[da]=u.__composerExtend(c)),m_(u,t,c),u.__setInstance(t,c)}return c}function u_(e,t){const n=Oa(),r=__VUE_I18N_LEGACY_API__&&t?n.run(()=>ha(e)):n.run(()=>Ya(e));if(r==null)throw gt(at.UNEXPECTED_ERROR);return[n,r]}function l_(e){const t=mt(e.isCE?a_:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw gt(e.isCE?at.NOT_INSTALLED_WITH_PROVIDE:at.UNEXPECTED_ERROR);return t}function c_(e,t){return Uo(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function f_(e){return e.mode==="composition"?e.global:e.global.__composer}function d_(e,t,n=!1){let r=null;const i=t.root;let o=h_(t,n);for(;o!=null;){const u=e;if(e.mode==="composition")r=u.__getInstance(o);else if(__VUE_I18N_LEGACY_API__){const c=u.__getInstance(o);c!=null&&(r=c.__composer,n&&r&&!r[yh]&&(r=null))}if(r!=null||i===o)break;o=o.parent}return r}function h_(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function m_(e,t,n){$t(()=>{},t),tr(()=>{const r=n;e.__deleteInstance(t);const i=r[da];i&&(i(),delete r[da])},t)}const p_=["locale","fallbackLocale","availableLocales"],Rl=["t","rt","d","n","tm","te"];function g_(e,t){const n=Object.create(null);return p_.forEach(i=>{const o=Object.getOwnPropertyDescriptor(t,i);if(!o)throw gt(at.UNEXPECTED_ERROR);const u=je(o.value)?{get(){return o.value.value},set(c){o.value.value=c}}:{get(){return o.get&&o.get()}};Object.defineProperty(n,i,u)}),e.config.globalProperties.$i18n=n,Rl.forEach(i=>{const o=Object.getOwnPropertyDescriptor(t,i);if(!o||!o.value)throw gt(at.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,"$".concat(i),o)}),()=>{delete e.config.globalProperties.$i18n,Rl.forEach(i=>{delete e.config.globalProperties["$".concat(i)]})}}const __=Ge({name:"i18n-d",props:We({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},qa),setup(e,t){const n=e.i18n||_t({useScope:e.scope,__useComponent:!0});return wh(e,t,mh,(...r)=>n[ca](...r))}}),kl=__;X1();C1(u1);I1(O1);M1(uh);if(__INTLIFY_PROD_DEVTOOLS__){const e=Tn();e.__INTLIFY__=!0,l1(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}var pt;(function(e){let t;(function(n){n.ENGLISH="en",n.CHINESE_SIMPLIFIED="zh-Hans",n.CHINESE_TRADITIONAL="zh-Hant",n.CHINESE_TRADITIONAL_HK="zh-Hant-HK",n.CHINESE_TRADITIONAL_TW="zh-Hant-TW",n.JAPANESE="ja",n.KOREAN="ko",n.BAHASA_MELAYU="ms",n.BAHASA_INDONESIA="id"})(t=e.Language||(e.Language={})),e.LANGUAGES={DEFAULT:t.ENGLISH,SUPPORTED:[t.ENGLISH,t.CHINESE_SIMPLIFIED,t.CHINESE_TRADITIONAL],FALLBACKS:{[t.CHINESE_TRADITIONAL_HK]:[t.CHINESE_TRADITIONAL_HK,t.CHINESE_TRADITIONAL,t.CHINESE_TRADITIONAL_TW,t.ENGLISH],[t.CHINESE_TRADITIONAL_TW]:[t.CHINESE_TRADITIONAL_TW,t.CHINESE_TRADITIONAL,t.CHINESE_TRADITIONAL_HK,t.CHINESE_SIMPLIFIED,t.ENGLISH],[t.CHINESE_TRADITIONAL]:[t.CHINESE_TRADITIONAL,t.CHINESE_TRADITIONAL_TW,t.CHINESE_TRADITIONAL_HK,t.CHINESE_SIMPLIFIED,t.ENGLISH],[t.CHINESE_SIMPLIFIED]:[t.CHINESE_SIMPLIFIED,t.CHINESE_TRADITIONAL,t.CHINESE_TRADITIONAL_TW,t.CHINESE_TRADITIONAL_HK,t.ENGLISH],[t.BAHASA_MELAYU]:[t.BAHASA_MELAYU,t.BAHASA_INDONESIA,t.ENGLISH],[t.BAHASA_INDONESIA]:[t.BAHASA_INDONESIA,t.BAHASA_MELAYU,t.ENGLISH],[t.JAPANESE]:[t.JAPANESE,t.ENGLISH],[t.KOREAN]:[t.KOREAN,t.ENGLISH],[t.ENGLISH]:[t.ENGLISH],default:[t.ENGLISH]}},function(n){n.TIME="time",n.DATE="date",n.DATETIME="datetime"}(e.DateTimePickerType||(e.DateTimePickerType={})),function(n){n.DARK="dark",n.LIGHT="light"}(e.ColorScheme||(e.ColorScheme={})),e.DefaultTheme={light:{background:"#FFFFFFFF",text:"#1A1A1AFF",primary:"#F99300FF",secondary:"#7B7B7BFF",tertiary:"#999220FF",accent:"#0A84FFFF"},dark:{background:"#3D3D3DFF",text:"#FFFFFFFF",primary:"#FFB300FF",secondary:"#BABABAFF",tertiary:"#FF3F34FF",accent:"#0AA3FFFF"}}})(pt||(pt={}));const v_={button:{back:"Back",cancel:"Cancel",clear:"Clear",confirm:"Confirm",done:"Done",next:"Next",no:"No",no_match:"No Match",ok:"Ok",rejoin:"Rejoin",renew:"Renew",terminate:"Terminate",yes:"Yes",submit:"Submit",delete:"Delete"},form:{search:"search",birth:"birthdate",cardNumber:"card number",createAt:"create at",displayAs:"display as",email:"email",emailOptIn:"email opt in",endTime:"expire",expire:"expire",familyName:"family name",gender:"gender",givenName:"given name",isPaid:"{name} paid",join:"join",mobile:"mobile",mobileOptIn:"mobile opt in",paidTime:"paid time",startTime:"join",terminated:"terminated"},countdown:{before:{prefix:"end on ",suffix:""},after:"Event ended",counting_down:{prefix:"end in ",suffix:""},counting_up:{prefix:"overdue",suffix:""},time_unit:{dd:"day",hh:"hour",mm:"min",ss:"sec"}},error:{oops:"Oops",above_minimum_amount:"must be {amount} or above",below_maximum_amount:"must be {amount} or below",after_maximum_date:"must be {date} or earlier",before_minimum_date:"must be {date} or later",expire:{before_minimum_date:"must from today & join date onwards"},fallback:"Operation error <br/> {{error}}",invalid:"invalid",invalid_date:"wrong date format",invalid_mobile:"Invalid mobile number",invalid_pattern:"wrong format",is_required:"required",join:{after_maximum_date:"must before expire date"},maximum_length:"max. {n} character | max. {n} characters",minimum_length:"min. {n} character | min. {n} characters",maximum_number:"must be {n} or below",minimum_number:"must be {n} or above",message:"Ops, something wrong, please try again later",missing_active_member:"Found an active member, but failed to load the data",offline:"You are offline, please check the network settings",offline_status:"offline",staff_info_missing:"Staff info is missing",timeout:"Operation timeout",all_fields_required:"All fields are required",already_cancelled:"Already cancelled",already_expired:"Already expired",already_terminated:"Already terminated",already_upgraded:"Already upgraded",amount_not_numeric:"Incorrect amount format",cardId_missing:"Card Id is missing",checkinInfo_missing:"Check-in info missing",duplicated_receipt:"Duplicated receipt",endTime_missing:"End time is required",given_and_family_required:"Given name & family name are required",invalid_currency:"Invalid currency",invalid_endTime:"Invalid expiration date",invalid_nameorder:"Invalid name order",location_missing:"Location is disabled or missing",mobile_required:"Mobile is required",not_active_membership:"Not an active membership",not_qualify_extension:"Not qualify extension",not_supported_order:"Not supportted order",ordersummary_or_order_required:"Order / Order Summary is required",past_expire_time:"End time is past expire time",personId_or_profile_required:"Person Id / Profile is required",quantity_not_numeric:"Incorrect quantity format",query_or_cardnumber_required:"Query must not be blank",staff_missing:"Staff is missing",staff_not_found:"Cannot found the staff",config_missing:"Config is missing",websocket:{config_missing:"Config is missing, failed to connect websocket",onmessage_error:"Failed to get websocket data from onMessage event",onmessage_unsubscribe_failed:"Received unknown websocket data, failed to close",onmessage_unsubscribe:"Received unknown websocket data, now closed",onclose_unknown_socket:"Unknown websocket closed",onclose_error:"Failed to get websocket data from onClose event",onerror:"Unknown websocket error",close_failed:"Failed to close event"}}},y_={en:v_,"zh-Hans":{button:{back:"返回",cancel:"取消",clear:"清除",confirm:"确定",done:"完成",next:"下一步",no:"否",no_match:"不匹配",ok:"好",rejoin:"重新加入",renew:"更新",terminate:"终止",yes:"是",submit:"提交",delete:"删除"},form:{search:"搜索",birth:"生日",cardNumber:"卡号",createAt:"创建时间",displayAs:"名字展示",email:"邮件地址",emailOptIn:"邮件订阅",endTime:"到期",expire:"到期",familyName:"姓",gender:"性别",givenName:"名",isPaid:"{name}已付",join:"加入",mobile:"手机号码",mobileOptIn:"手机订阅",paidTime:"付费时间",startTime:"加入",terminated:"已终止"},countdown:{before:{prefix:"",suffix:"结束"},after:"活动已结束",counting_down:{prefix:"",suffix:"后结束"},counting_up:{prefix:"超时",suffix:""},time_unit:{dd:"天",hh:"时",mm:"分",ss:"秒"}},error:{oops:"哎呀",above_minimum_amount:"金额至少为{amount}",below_maximum_amount:"金额最多为{amount}",after_maximum_date:"应该在{date}之前",before_minimum_date:"应该在{date}之后",expire:{before_minimum_date:"选择今天和加入日期之后的日期"},fallback:"操作错误 <br/> {{error}}",invalid:"错误",invalid_date:"时间格式错误",invalid_mobile:"手机号码无效",invalid_pattern:"格式错误",is_required:"必填项",join:{after_maximum_date:"选择过期日期之前的日期"},maximum_length:"最多{n}个字符",minimum_length:"最少{n}个字符",maximum_number:"最多为{n}",minimum_number:"最少为{n}",message:"呃，出错了，请稍后再试",missing_active_member:"找到一个有效会员，但是数据加载失败",offline:"当前离线状态, 请检查网路设置",offline_status:"离线",staff_info_missing:"员工数据缺失",timeout:"操作超时",all_fields_required:"所有字段都必填",already_cancelled:"已经取消了",already_expired:"已经过期了",already_terminated:"已经终止了",already_upgraded:"已经升级了",amount_not_numeric:"金额格式不对",cardId_missing:"Card ID缺失",checkinInfo_missing:"签到数据缺失",duplicated_receipt:"该收据已存在",endTime_missing:"过期时间必填",given_and_family_required:"姓名必填",invalid_currency:"币种错误",invalid_endTime:"过期时间错误",invalid_nameorder:"名字顺序错误",location_missing:"位置数据缺失",mobile_required:"手机号码必填",not_active_membership:"不是有效的会员",not_qualify_extension:"该会籍不符合延期条件",not_supported_order:"不支持该订单",ordersummary_or_order_required:"请提供订单详情",past_expire_time:"结束时间超过过期时间",personId_or_profile_required:"个人 ID/资料缺失",quantity_not_numeric:"数量的格式错误",query_or_cardnumber_required:"请提供需要搜寻的内容或者卡号",staff_missing:"员工资料缺失",staff_not_found:"找不到该员工记录",config_missing:"配置缺失",websocket:{config_missing:"配置缺失, 导致连接 WebSocket 失败",onmessage_error:"从 onMessage 事件中获取 WebSocket 数据失败",onmessage_unsubscribe_failed:"接收到无法识别的 WebSocket 数据, 关闭失败",onmessage_unsubscribe:"接收到无法识别的 WebSocket 数据, 现已关闭",onclose_unknown_socket:"已关闭无法识别的 WebSocket",onclose_error:"从 onClose 事件中获取 WebSocket 数据失败",onerror:"无法识别的 WebSocket 错误",close_failed:"WebSocket 关闭失败"}}},"zh-Hant":{button:{back:"返回",cancel:"取消",clear:"清除",confirm:"確定",done:"完成",next:"下一步",no:"否",no_match:"不匹配",ok:"好",rejoin:"重新加入",renew:"更新",terminate:"终止",yes:"是",submit:"提交",delete:"刪除"},form:{search:"搜寻",birth:"生日",cardNumber:"卡號",createAt:"創建時間",displayAs:"名字展示",email:"郵件地址",emailOptIn:"郵件訂閱",endTime:"到期",expire:"到期",familyName:"姓",gender:"性別",givenName:"名",isPaid:"{name}已付",join:"加入",mobile:"手機號碼",mobileOptIn:"手機訂閱",paidTime:"付費時間",startTime:"加入",terminated:"已終止"},countdown:{before:{prefix:"",suffix:"結束"},after:"活動已結束",counting_down:{prefix:"",suffix:"後結束"},counting_up:{prefix:"超時",suffix:""},time_unit:{dd:"天",hh:"时",mm:"分",ss:"秒"}},error:{oops:"哎呀",above_minimum_amount:"金額至少為{amount}",below_maximum_amount:"金額最多為{amount}",after_maximum_date:"應該在{date}之前",before_minimum_date:"應該在{date}之後",expire:{before_minimum_date:"選擇今天和加入日期之後的日期"},fallback:"操作錯誤 <br/> {{error}}",invalid:"錯誤",invalid_date:"時間格式錯誤",invalid_mobile:"手機號碼無效",invalid_pattern:"格式錯誤",is_required:"必填項",join:{after_maximum_date:"選擇過期日期之前的日期"},maximum_length:"最多{n}個字符",minimum_length:"最少{n}個字符",minimum_number:"最少为{n}",maximum_number:"最多为{n}",message:"呃，出錯了，請稍後再試",missing_active_member:"找到一個有效會員，但是數據加載失敗",offline:"當前離線狀態, 請檢查網路設置",offline_status:"離線",staff_info_missing:"員工數據缺失",timeout:"操作超時",all_fields_required:"所有字段都必填",already_cancelled:"已經取消了",already_expired:"已經過期了",already_terminated:"已經終止了",already_upgraded:"已經升級了",amount_not_numeric:"金額格式不對",cardId_missing:"Card ID缺失",checkinInfo_missing:"簽到數據缺失",duplicated_receipt:"該收據已存在",endTime_missing:"過期時間必填",given_and_family_required:"姓名必填",invalid_currency:"幣種錯誤",invalid_endTime:"過期時間錯誤",invalid_nameorder:"名字順序錯誤",location_missing:"位置數據缺失",mobile_required:"手機號碼必填",not_active_membership:"不是有效的會員",not_qualify_extension:"該會籍不符合延期條件",not_supported_order:"不支持該訂單",ordersummary_or_order_required:"請提供訂單詳情",past_expire_time:"結束時間超過過期時間",personId_or_profile_required:"個人 ID/資料缺失",quantity_not_numeric:"數量的格式錯誤",query_or_cardnumber_required:"請提供需要搜尋的內容或者卡號",staff_missing:"員工資料缺失",staff_not_found:"找不到該員工記錄",config_missing:"配置缺失",websocket:{config_missing:"配置缺失, 導致連線 WebSocket 失敗",onmessage_error:"從 onMessage 事件中取得 WebSocket 資料失敗",onmessage_unsubscribe_failed:"接收到無法辨識的 WebSocket 資料, 關閉失敗",onmessage_unsubscribe:"接收到無法辨識的 WebSocket 資料, 現已關閉",onclose_unknown_socket:"已關閉無法辨識的 WebSocket",onclose_error:"從 onClose 事件中取得 WebSocket 資料失敗",onerror:"無法辨識的 WebSocket 錯誤",close_failed:"WebSocket 關閉失敗"}}}};function Nl(){window.$perkd.do("window.close")}async function b_(){try{const e=await window.$perkd.do("constants"),{constants:t}=e||{};return t}catch(e){return{error:e}}}async function E_(e){const{method:t,base:n,endpoint:r,cardId:i,...o}=e;if(!(n&&r))return{error:{statusMessage:"config_missing"}};const u={method:t,url:"".concat(n,"/").concat(r),cardId:i,credentials:"perkd",...o};try{return await window.$perkd.do("remote.api",u)}catch(c){return await A_("".concat(n,"/").concat(r),c,u),{error:c}}}async function A_(e,t,n){try{await window.$perkd.do("track.watch",{message:e,error:t,data:n})}catch(r){return{error:r}}}var xl;(function(e){e.Achieved="achieved",e.Beep="beep",e.BellChord="bellchord",e.Cash="cash",e.Cashier="cashier",e.Chord="chord",e.Correct="correct",e.Done="done",e.Fail="fail",e.Happy="happy",e.Magic="magic",e.Notify="notify",e.Scan="scan",e.ServiceBell="servicebell",e.Success="success",e.SuccessBell="successbell",e.Upsell="upsell",e.WifiOn="wifion"})(xl||(xl={}));var Pl;(function(e){e.Selection="selection",e.ImpactLight="impactLight",e.ImpactMedium="impactMedium",e.ImpactHeavy="impactHeavy",e.NotificationSuccess="notificationSuccess",e.NotificationWarning="notificationWarning",e.NotificationError="notificationError"})(Pl||(Pl={}));var Fl;(function(e){e.NATIVE="native",e.BROWSER="browser",e.IN_APP="web"})(Fl||(Fl={}));async function w_(e,t={}){try{const n={key:e};return(t==null?void 0:t.id)!==void 0&&(n.id=t.id),await window.$perkd.do("widget.data",n)}catch(n){return{error:n}}}async function O_(e){try{return await window.$perkd.do("form.hoursSettings",{hours:e||{periods:[]}})}catch(t){return{error:t}}}var En={},ar={},Bl;function T_(){if(Bl)return ar;Bl=1,Object.defineProperty(ar,"__esModule",{value:!0}),ar.Contacts=void 0;var e;return function(t){(function(n){n.MOBILE="mobile",n.HOME="home",n.WORK="work",n.OTHERS="others"})(t.Type||(t.Type={})),function(n){n.BIRTH="birth",n.GRADUATE="graduate",n.MARRIED="married",n.BAPTISED="baptised",n.CONTRACT_START="contractstart",n.CONTRACT_END="contractend"}(t.Dates||(t.Dates={})),function(n){n.WEBSITE="website",n.EMAIL="email",n.SOCIAL="social",n.CUSTOM="custom"}(t.UrlKind||(t.UrlKind={}))}(e||(ar.Contacts=e={})),ar}var $l;function S_(){if($l)return En;$l=1,Object.defineProperty(En,"__esModule",{value:!0}),En.Socials=En.Persons=void 0;const e=T_();var t;(function(r){(function(o){o.MALE="m",o.FEMALE="f"})(r.Gender||(r.Gender={}));let i;(function(o){o.FAMILY_GIVEN="familygiven",o.GIVEN_FAMILY="givenfamily"})(i=r.NameOrder||(r.NameOrder={})),function(o){o.PERKD="perkd",o.USER="user",o.CUSTOMER="customer",o.SMARTCOLLECTION="smartcollection",o.NATIONAL="national",o.REGISTRATION="registration",o.PASSPORT="passport",o.DRIVER="driver",o.PET="pet"}(r.Identities||(r.Identities={})),function(o){o.MOBILE="mobile",o.EMAIL="email",o.POSTAL="postal",o.SERVICE_TERMS="serviceTerms",o.PRIVACY_POLICY="privacyPolicy"}(r.PermissionChannel||(r.PermissionChannel={})),function(o){o[o.DO_NOT_DISTURB=-2]="DO_NOT_DISTURB",o[o.OPTOUT=-1]="OPTOUT",o[o.UNKNOWN=0]="UNKNOWN",o[o.OPTIN=1]="OPTIN"}(r.PermissionStatus||(r.PermissionStatus={})),function(o){o.CITIZEN="citizen",o.RESIDENT="resident",o.EMPLOYMENT="employment"}(r.Residency||(r.Residency={})),function(o){o.A_POSITIVE="A+",o.A_NEGATIVE="A-",o.B_POSITIVE="B+",o.B_NEGATIVE="B-",o.O_POSITIVE="O+",o.O_NEGATIVE="O-",o.AB_POSITIVE="AB+",o.AB_NEGATIVE="AB-"}(r.BloodType||(r.BloodType={})),function(o){o.DOG="dog",o.CAT="cat",o.BIRD="bird",o.RABBIT="rabbit",o.RODENT="rodent"}(r.Species||(r.Species={})),r.PROFILE={FAMILY_GIVEN:i.FAMILY_GIVEN,GIVEN_FAMILY:i.GIVEN_FAMILY,NAME_ORDER:[i.FAMILY_GIVEN,i.GIVEN_FAMILY],MOBILE:e.Contacts.Type.MOBILE,BIRTH_DATE:e.Contacts.Dates.BIRTH}})(t||(En.Persons=t={}));var n;return function(r){(function(i){i.FRIEND="friend",i.SPOUSE="spouse",i.PARENT="parent",i.CHILD="child",i.SIBLING="sibling",i.COLLEAGUE="colleague",i.CLASSMATE="classmate",i.PET="pet"})(r.Relationship||(r.Relationship={})),function(i){i.parent="child",i.child="parent",i.pet="owner"}(r.InverseRelationship||(r.InverseRelationship={}))}(n||(En.Socials=n={})),En}var C_=S_(),Hl;(function(e){e.DEFAULT_NAME_ORDER=C_.Persons.NameOrder.GIVEN_FAMILY})(Hl||(Hl={}));var To;(function(e){let t;(function(n){n.QRCODE="QRCODE",n.AZTEC="AZTEC",n.DATAMATRIX="DATAMATRIX",n.CODE128="CODE128"})(t=e.Barcode||(e.Barcode={})),e.DEFAULT_BARCODE_TYPE=t.CODE128,e.CODE_SQUARE=[t.QRCODE,"QR_CODE",t.AZTEC,t.DATAMATRIX]})(To||(To={}));const I_=e=>{const t=/[-a-zA-Z0-9@:%._\\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)?/gi;return!!e&&!!e.match(new RegExp(t))},fi=e=>e&&typeof e=="object"&&!Array.isArray(e),ma=(e,t)=>{if(!fi(e)||!fi(t))return t===void 0?e:t;const n=Array.isArray(e)?[]:{};for(const r in e)e.hasOwnProperty(r)&&(n[r]=fi(e[r])?ma({},e[r]):e[r]);for(const r in t)t.hasOwnProperty(r)&&(Array.isArray(t[r])?n[r]=t[r]==null?e[r]:t[r]:fi(t[r])?r in e?n[r]=ma(e[r],t[r]):n[r]=t[r]:n[r]=t[r]===void 0?e[r]:t[r]);return n};function M_(e,t){const{statusCode:n,statusMessage:r="",code:i,message:o=""}=e||{},u=r||o,c=t("error.".concat(u)),l=!!c&&c!=="error.".concat(u),s=l?c:t("error.message"),a="".concat(n||i||""),d=a&&a!==o.trim()?a:"",f=u!==o||!l?o.trim():"",h="".concat(d," ").concat(f).trim();return"".concat(s).concat(h?": ":"").concat(h)}function L_(e,t=pt.LANGUAGES.SUPPORTED){if(t.includes(e))return e;const{FALLBACKS:n,DEFAULT:r}=pt.LANGUAGES;return Object.keys(n).includes(e)&&(n[e]||n.default).find(o=>t.includes(o))||r}function pa(e){return!!e&&typeof e=="object"&&"error"in e}const D_=e=>{const t=/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(0|1|0?\.\d+)\s*)?\)/,n=e==null?void 0:e.match(t);return n?{r:parseInt(n[1]),g:parseInt(n[2]),b:parseInt(n[3]),o:n[4]?parseFloat(n[4]):1}:{r:0,g:0,b:0,o:1}},Th=e=>{if(!(e&&e.match(/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/)))return{r:0,g:0,b:0,o:1};const n=e.length>3?e:"#"+e.slice(1,e.length).replace(/(.{1})/g,"$1$1"),r=parseInt(n.slice(1,3),16),i=parseInt(n.slice(3,5),16),o=parseInt(n.slice(5,7),16),u=n.slice(7,9)?Math.round(parseInt(n.slice(7,9),16)*100/255)/100:1;return{r,g:i,b:o,o:u}},R_=e=>e.startsWith("#")?Th(e):D_(e);function k_(e,t,n){e/=255,t/=255,n/=255;const r=Math.max(e,t,n),i=Math.min(e,t,n);let o=0,u=0;const c=(r+i)/2;if(r!==i){const l=r-i;switch(u=c>.5?l/(2-r-i):l/(r+i),r){case e:o=(t-n)/l+(t<n?6:0);break;case t:o=(n-e)/l+2;break;case n:o=(e-t)/l+4;break}o/=6}return{h:o*360,s:u,l:c}}const N_=e=>{const{r:t,g:n,b:r}=typeof e=="string"?R_(e):e,i=a=>a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4),u=((a,d,f)=>.2126*i(a)+.7152*i(d)+.0722*i(f))(t/255,n/255,r/255),c=k_(t,n,r);if(c.h>=90&&c.h<=150&&c.l<.3)return"#FFFFFF";if(c.h>40&&c.h<70&&c.l>=.5)return u<.6?"#FFFFFF":"#000000";if(c.h>=0&&c.h<=15&&c.s>.6||c.h>15&&c.h<=40&&c.s>.7||c.h>=90&&c.h<=150&&c.s>.6)return u<.55?"#FFFFFF":"#000000";if(c.h>150&&c.h<190)return u<.48?"#FFFFFF":"#000000";if(c.h>=270&&c.h<=330&&c.s>.4)return u<.42?"#FFFFFF":"#000000";if(c.h>40&&c.h<70&&c.l<.5||c.h>=70&&c.h<90&&c.s>.5||c.h>=90&&c.h<=150&&c.s<=.6||c.h>=190&&c.h<=250&&c.s>.6||c.s<.1||c.h>=20&&c.h<=40&&c.s>=.4&&c.s<=.6&&c.l<=.6)return u<.45?"#FFFFFF":"#000000";const l=(1+.05)/(u+.05),s=(u+.05)/(0+.05);return l>s?"#FFFFFF":"#000000"},x_=e=>{const{DEVICE:t,CARD:n,COUNTRY:r,CARDMASTER:i,CONTENT:o,PERSON:u,FONTCSS:c,FONTPATH:l,LANGUAGE:s,CONTEXT:a}=e||{};return{DEVICE:t,CARD:n,CARDMASTER:i,COUNTRY:r,LANGUAGE:s,CONTENT:o,FONTCSS:c,FONTPATH:l,CONTEXT:a,PERSON:u,deviceScheme:window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?pt.ColorScheme.DARK:pt.ColorScheme.LIGHT}},P_=(e,t)=>{var u,c,l,s,a,d,f,h,p,m,_,g,v,b,E,D,I;const{CARD:n,CARDMASTER:r}=e||{},i=((u=n==null?void 0:n.widgets)==null?void 0:u.find(O=>O.key===t))||{},o=((c=r==null?void 0:r.widgets)==null?void 0:c.find(O=>O.key===t))||{};return{API_BASE:((s=(l=i==null?void 0:i.param)==null?void 0:l.api)==null?void 0:s.baseUrl)||((d=(a=o==null?void 0:o.param)==null?void 0:a.api)==null?void 0:d.baseUrl),API:((f=i==null?void 0:i.param)==null?void 0:f.api)||((h=o==null?void 0:o.param)==null?void 0:h.api),COLOR_SCHEME:((p=i==null?void 0:i.param)==null?void 0:p.colorScheme)||((m=o==null?void 0:o.param)==null?void 0:m.colorScheme),SETTINGS:((_=i==null?void 0:i.param)==null?void 0:_.settings)||((g=o==null?void 0:o.param)==null?void 0:g.settings),MASTER_SETTINGS:(v=o==null?void 0:o.param)==null?void 0:v.settings,MEMBERSHIP_PROGRAMS:((E=(b=i==null?void 0:i.param)==null?void 0:b.settings)==null?void 0:E.programs)||((I=(D=o==null?void 0:o.param)==null?void 0:D.settings)==null?void 0:I.programs)}},F_=(e,t)=>{var k,R;const{DEVICE:n,CARDMASTER:r,deviceScheme:i=pt.ColorScheme.LIGHT}=e,{WIDTH:o,HEIGHT:u,MIN_TOP_SPACE:c,STATUS_BAR_HEIGHT:l,NAV_BAR_HEIGHT:s,MIN_BOTTOM_SPACE:a,BOTTOM_TABS_HEIGHT:d,windowHeight:f,IOS:h,IS_LONG_SCREEN:p,APP:m}=n||{},{VERSION:_}=m||{},g={"--width-screen":o+"px","--height-screen":u+"px","--height-window":f+"px","--height-minTop":(c||20)+"px","--height-minBottom":(a||20)+"px","--height-statusBar":(h?l:0)+"px","--height-navigationBar":s+(h?l:0)+"px","--height-tabBar":d+"px","--device-ios":h,"--device-longScreen":p,"--app-version":_,"--font-size-base":Math.round(((o/320-1)*.8+1)*10)+"px","--size-base":"".concat(Math.round(((o/320-1)*.8+1)*10))};Object.keys(g).forEach(L=>{document.documentElement.style.setProperty(L,g?g[L]:"")});const v=t?((k=r==null?void 0:r.widgets)==null?void 0:k.find(L=>L.key===t))||{}:{},{colorScheme:b}=(v==null?void 0:v.param)||{},{brand:E,theme:D=b||i}=r||{},{light:I,dark:O}=((R=v==null?void 0:v.param)==null?void 0:R.theme)||(E==null?void 0:E.style)||{},M={light:Object.assign({},pt.DefaultTheme.light,I),dark:Object.assign({},pt.DefaultTheme.dark,O)};Object.keys(M[D]).forEach(L=>{const B=M[D][L];if(B){const{r:N,g:G,b:Y,o:W}=Th(B),V=N_(B);document.documentElement.style.setProperty("--color-brand-".concat(L),"rgb(".concat(N,",").concat(G,",").concat(Y,",").concat(W,")")),document.documentElement.style.setProperty("--color-brand-".concat(L,"-contrast"),V)}}),document.documentElement.setAttribute("theme",D)},B_=async(e,t,n)=>{const{ENGLISH:r}=pt.Language,{LANGUAGE:i=r}=e;if(document.documentElement.lang=i,n&&t&&n.locale){const o=t[i]||t[r];n.locale(o)}},$_=e=>ma(y_,e),Rn=f0("appletData",{state:()=>({APPLET_NAME:"manage-hours",START_PAGE:"place-list",ENVIRONMENT:{},WIDGET:{},isOnline:navigator.onLine,app:null,places:[],loading:!1,error:null}),actions:{async fetchData(){const e=await b_();if(pa(e)){console.error("Failed to fetch data:",e.error);return}this.ENVIRONMENT=x_(e),this.WIDGET=P_(this.ENVIRONMENT,this.APPLET_NAME)},async fetchPlaces(){this.loading=!0,this.error=null;try{const e=await w_("place");if(pa(e)){console.error("Failed to fetch places:",e.error),this.error="Failed to load places data";return}if(Array.isArray(e)){this.places=this.adaptPlacesData(e);const t=this.places.filter(n=>!n.id||n.id.trim()==="");t.length>0&&console.error("Found places with empty IDs:",t)}else console.error("Unexpected response format:",e),this.error="Invalid data format received: ".concat(JSON.stringify(e))}catch(e){console.error("Error fetching places:",e),this.error="An unexpected error occurred"}finally{this.loading=!1}},adaptPlacesData(e){return e.map(t=>{var i,o,u,c,l,s,a,d,f,h,p,m,_,g,v;(!t.id||t.id.trim()==="")&&console.error("Place missing ID:",t);const n={general:this.convertPeriodsToDayHours(((i=t.openingHours)==null?void 0:i.periods)||[])};if((o=t.openingHours)!=null&&o.specific&&(n.general.specific=this.normalizeSpecificHours(t.openingHours.specific)),(u=t.dinein)!=null&&u.available){const b=((c=t.dinein.hours)==null?void 0:c.periods)||((l=t.openingHours)==null?void 0:l.periods)||[];n.dinein=this.convertPeriodsToDayHours(b),(s=t.dinein.hours)!=null&&s.specific&&(n.dinein.specific=this.normalizeSpecificHours(t.dinein.hours.specific))}if((a=t.pickup)!=null&&a.available){const b=((d=t.pickup.hours)==null?void 0:d.periods)||((f=t.openingHours)==null?void 0:f.periods)||[];n.pickup=this.convertPeriodsToDayHours(b),(h=t.pickup.hours)!=null&&h.specific&&(n.pickup.specific=this.normalizeSpecificHours(t.pickup.hours.specific))}if((p=t.deliver)!=null&&p.available){const b=((m=t.deliver.hours)==null?void 0:m.periods)||((_=t.openingHours)==null?void 0:_.periods)||[];n.deliver=this.convertPeriodsToDayHours(b),(g=t.deliver.hours)!=null&&g.specific&&(n.deliver.specific=this.normalizeSpecificHours(t.deliver.hours.specific))}return{id:t.id?t.id.trim():"",name:t.name,brand:(v=t.brand)==null?void 0:v.long,hours:n,external:t.external}})},normalizeSpecificHours(e){return Array.isArray(e)?e.map(t=>{const n={...t};return!n.periods||n.periods.length===0?n.periods=[{open:{day:0,time:"0000"},close:{day:0,time:"0000"}}]:n.periods=n.periods.filter(r=>!0),n}):[]},convertPeriodsToDayHours(e){const t=[];for(let n=0;n<7;n++)t[n]={day:n,ranges:[],closed:!0};return!e||e.length===0||e.forEach(n=>{const r=n.open.day-1;if(r<0||r>6){console.error("Invalid day in period: ".concat(JSON.stringify(n)));return}if(n.open.time==="0000"&&n.close.time==="0000"){console.log("Found closed period (00:00-00:00) for day ".concat(r));return}const i=n.open.time.length===4?"".concat(n.open.time.substring(0,2),":").concat(n.open.time.substring(2,4)):n.open.time,o=n.close.time.length===4?"".concat(n.close.time.substring(0,2),":").concat(n.close.time.substring(2,4)):n.close.time,u={open:i,close:o};t[r]&&(t[r].ranges.push(u),t[r].closed=!1)}),t},resetProcessData(){this.places=[],this.loading=!1,this.error=null}}});/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Gn=typeof document<"u";function Sh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function H_(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Sh(e.default)}const Ce=Object.assign;function ps(e,t){const n={};for(const r in t){const i=t[r];n[r]=Lt(i)?i.map(e):e(i)}return n}const Sr=()=>{},Lt=Array.isArray,Ch=/#/g,j_=/&/g,U_=/\//g,Y_=/=/g,q_=/\?/g,Ih=/\+/g,G_=/%5B/g,V_=/%5D/g,Mh=/%5E/g,W_=/%60/g,Lh=/%7B/g,z_=/%7C/g,Dh=/%7D/g,X_=/%20/g;function Ga(e){return encodeURI(""+e).replace(z_,"|").replace(G_,"[").replace(V_,"]")}function K_(e){return Ga(e).replace(Lh,"{").replace(Dh,"}").replace(Mh,"^")}function ga(e){return Ga(e).replace(Ih,"%2B").replace(X_,"+").replace(Ch,"%23").replace(j_,"%26").replace(W_,"`").replace(Lh,"{").replace(Dh,"}").replace(Mh,"^")}function Q_(e){return ga(e).replace(Y_,"%3D")}function J_(e){return Ga(e).replace(Ch,"%23").replace(q_,"%3F")}function Z_(e){return e==null?"":J_(e).replace(U_,"%2F")}function Hr(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const ev=/\/$/,tv=e=>e.replace(ev,"");function gs(e,t,n="/"){let r,i={},o="",u="";const c=t.indexOf("#");let l=t.indexOf("?");return c<l&&c>=0&&(l=-1),l>-1&&(r=t.slice(0,l),o=t.slice(l+1,c>-1?c:t.length),i=e(o)),c>-1&&(r=r||t.slice(0,c),u=t.slice(c,t.length)),r=ov(r!=null?r:t,n),{fullPath:r+(o&&"?")+o+u,path:r,query:i,hash:Hr(u)}}function nv(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function jl(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function rv(e,t,n){const r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&Zn(t.matched[r],n.matched[i])&&Rh(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Zn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Rh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!iv(e[n],t[n]))return!1;return!0}function iv(e,t){return Lt(e)?Ul(e,t):Lt(t)?Ul(t,e):e===t}function Ul(e,t){return Lt(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function ov(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),i=r[r.length-1];(i===".."||i===".")&&r.push("");let o=n.length-1,u,c;for(u=0;u<r.length;u++)if(c=r[u],c!==".")if(c==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(u).join("/")}const en={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var jr;(function(e){e.pop="pop",e.push="push"})(jr||(jr={}));var Cr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Cr||(Cr={}));function sv(e){if(!e)if(Gn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),tv(e)}const av=/^[^#]+#/;function uv(e,t){return e.replace(av,"#")+t}function lv(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Go=()=>({left:window.scrollX,top:window.scrollY});function cv(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=lv(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Yl(e,t){return(history.state?history.state.position-t:-1)+e}const _a=new Map;function fv(e,t){_a.set(e,t)}function dv(e){const t=_a.get(e);return _a.delete(e),t}let hv=()=>location.protocol+"//"+location.host;function kh(e,t){const{pathname:n,search:r,hash:i}=t,o=e.indexOf("#");if(o>-1){let c=i.includes(e.slice(o))?e.slice(o).length:1,l=i.slice(c);return l[0]!=="/"&&(l="/"+l),jl(l,"")}return jl(n,e)+r+i}function mv(e,t,n,r){let i=[],o=[],u=null;const c=({state:f})=>{const h=kh(e,location),p=n.value,m=t.value;let _=0;if(f){if(n.value=h,t.value=f,u&&u===p){u=null;return}_=m?f.position-m.position:0}else r(h);i.forEach(g=>{g(n.value,p,{delta:_,type:jr.pop,direction:_?_>0?Cr.forward:Cr.back:Cr.unknown})})};function l(){u=n.value}function s(f){i.push(f);const h=()=>{const p=i.indexOf(f);p>-1&&i.splice(p,1)};return o.push(h),h}function a(){const{history:f}=window;f.state&&f.replaceState(Ce({},f.state,{scroll:Go()}),"")}function d(){for(const f of o)f();o=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:l,listen:s,destroy:d}}function ql(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?Go():null}}function pv(e){const{history:t,location:n}=window,r={value:kh(e,n)},i={value:t.state};i.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,s,a){const d=e.indexOf("#"),f=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+l:hv()+e+l;try{t[a?"replaceState":"pushState"](s,"",f),i.value=s}catch(h){console.error(h),n[a?"replace":"assign"](f)}}function u(l,s){const a=Ce({},t.state,ql(i.value.back,l,i.value.forward,!0),s,{position:i.value.position});o(l,a,!0),r.value=l}function c(l,s){const a=Ce({},i.value,t.state,{forward:l,scroll:Go()});o(a.current,a,!0);const d=Ce({},ql(r.value,l,null),{position:a.position+1},s);o(l,d,!1),r.value=l}return{location:r,state:i,push:c,replace:u}}function gv(e){e=sv(e);const t=pv(e),n=mv(e,t.state,t.location,t.replace);function r(o,u=!0){u||n.pauseListeners(),history.go(o)}const i=Ce({location:"",base:e,go:r,createHref:uv.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function _v(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),gv(e)}function vv(e){return typeof e=="string"||e&&typeof e=="object"}function Nh(e){return typeof e=="string"||typeof e=="symbol"}const xh=Symbol("");var Gl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Gl||(Gl={}));function er(e,t){return Ce(new Error,{type:e,[xh]:!0},t)}function Yt(e,t){return e instanceof Error&&xh in e&&(t==null||!!(e.type&t))}const Vl="[^/]+?",yv={sensitive:!1,strict:!1,start:!0,end:!0},bv=/[.+*?^${}()[\]/\\]/g;function Ev(e,t){const n=Ce({},yv,t),r=[];let i=n.start?"^":"";const o=[];for(const s of e){const a=s.length?[]:[90];n.strict&&!s.length&&(i+="/");for(let d=0;d<s.length;d++){const f=s[d];let h=40+(n.sensitive?.25:0);if(f.type===0)d||(i+="/"),i+=f.value.replace(bv,"\\$&"),h+=40;else if(f.type===1){const{value:p,repeatable:m,optional:_,regexp:g}=f;o.push({name:p,repeatable:m,optional:_});const v=g||Vl;if(v!==Vl){h+=10;try{new RegExp("(".concat(v,")"))}catch(E){throw new Error('Invalid custom RegExp for param "'.concat(p,'" (').concat(v,"): ")+E.message)}}let b=m?"((?:".concat(v,")(?:/(?:").concat(v,"))*)"):"(".concat(v,")");d||(b=_&&s.length<2?"(?:/".concat(b,")"):"/"+b),_&&(b+="?"),i+=b,h+=20,_&&(h+=-8),m&&(h+=-20),v===".*"&&(h+=-50)}a.push(h)}r.push(a)}if(n.strict&&n.end){const s=r.length-1;r[s][r[s].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const u=new RegExp(i,n.sensitive?"":"i");function c(s){const a=s.match(u),d={};if(!a)return null;for(let f=1;f<a.length;f++){const h=a[f]||"",p=o[f-1];d[p.name]=h&&p.repeatable?h.split("/"):h}return d}function l(s){let a="",d=!1;for(const f of e){(!d||!a.endsWith("/"))&&(a+="/"),d=!1;for(const h of f)if(h.type===0)a+=h.value;else if(h.type===1){const{value:p,repeatable:m,optional:_}=h,g=p in s?s[p]:"";if(Lt(g)&&!m)throw new Error('Provided param "'.concat(p,'" is an array but it is not repeatable (* or + modifiers)'));const v=Lt(g)?g.join("/"):g;if(!v)if(_)f.length<2&&(a.endsWith("/")?a=a.slice(0,-1):d=!0);else throw new Error('Missing required param "'.concat(p,'"'));a+=v}}return a||"/"}return{re:u,score:r,keys:o,parse:c,stringify:l}}function Av(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ph(e,t){let n=0;const r=e.score,i=t.score;for(;n<r.length&&n<i.length;){const o=Av(r[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-r.length)===1){if(Wl(r))return 1;if(Wl(i))return-1}return i.length-r.length}function Wl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const wv={type:0,value:""},Ov=/[a-zA-Z0-9_]/;function Tv(e){if(!e)return[[]];if(e==="/")return[[wv]];if(!e.startsWith("/"))throw new Error('Invalid path "'.concat(e,'"'));function t(h){throw new Error("ERR (".concat(n,')/"').concat(s,'": ').concat(h))}let n=0,r=n;const i=[];let o;function u(){o&&i.push(o),o=[]}let c=0,l,s="",a="";function d(){s&&(n===0?o.push({type:0,value:s}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t("A repeatable param (".concat(s,") must be alone in its segment. eg: '/:ids+.")),o.push({type:1,value:s,regexp:a,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),s="")}function f(){s+=l}for(;c<e.length;){if(l=e[c++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(s&&d(),u()):l===":"?(d(),n=1):f();break;case 4:f(),n=r;break;case 1:l==="("?n=2:Ov.test(l)?f():(d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--);break;case 2:l===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+l:n=3:a+=l;break;case 3:d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--,a="";break;default:t("Unknown state");break}}return n===2&&t('Unfinished custom RegExp for param "'.concat(s,'"')),d(),u(),i}function Sv(e,t,n){const r=Ev(Tv(e.path),n),i=Ce(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function Cv(e,t){const n=[],r=new Map;t=Ql({strict:!1,end:!0,sensitive:!1},t);function i(d){return r.get(d)}function o(d,f,h){const p=!h,m=Xl(d);m.aliasOf=h&&h.record;const _=Ql(t,d),g=[m];if("alias"in d){const E=typeof d.alias=="string"?[d.alias]:d.alias;for(const D of E)g.push(Xl(Ce({},m,{components:h?h.record.components:m.components,path:D,aliasOf:h?h.record:m})))}let v,b;for(const E of g){const{path:D}=E;if(f&&D[0]!=="/"){const I=f.record.path,O=I[I.length-1]==="/"?"":"/";E.path=f.record.path+(D&&O+D)}if(v=Sv(E,f,_),h?h.alias.push(v):(b=b||v,b!==v&&b.alias.push(v),p&&d.name&&!Kl(v)&&u(d.name)),Fh(v)&&l(v),m.children){const I=m.children;for(let O=0;O<I.length;O++)o(I[O],v,h&&h.children[O])}h=h||v}return b?()=>{u(b)}:Sr}function u(d){if(Nh(d)){const f=r.get(d);f&&(r.delete(d),n.splice(n.indexOf(f),1),f.children.forEach(u),f.alias.forEach(u))}else{const f=n.indexOf(d);f>-1&&(n.splice(f,1),d.record.name&&r.delete(d.record.name),d.children.forEach(u),d.alias.forEach(u))}}function c(){return n}function l(d){const f=Lv(d,n);n.splice(f,0,d),d.record.name&&!Kl(d)&&r.set(d.record.name,d)}function s(d,f){let h,p={},m,_;if("name"in d&&d.name){if(h=r.get(d.name),!h)throw er(1,{location:d});_=h.record.name,p=Ce(zl(f.params,h.keys.filter(b=>!b.optional).concat(h.parent?h.parent.keys.filter(b=>b.optional):[]).map(b=>b.name)),d.params&&zl(d.params,h.keys.map(b=>b.name))),m=h.stringify(p)}else if(d.path!=null)m=d.path,h=n.find(b=>b.re.test(m)),h&&(p=h.parse(m),_=h.record.name);else{if(h=f.name?r.get(f.name):n.find(b=>b.re.test(f.path)),!h)throw er(1,{location:d,currentLocation:f});_=h.record.name,p=Ce({},f.params,d.params),m=h.stringify(p)}const g=[];let v=h;for(;v;)g.unshift(v.record),v=v.parent;return{name:_,path:m,params:p,matched:g,meta:Mv(g)}}e.forEach(d=>o(d));function a(){n.length=0,r.clear()}return{addRoute:o,resolve:s,removeRoute:u,clearRoutes:a,getRoutes:c,getRecordMatcher:i}}function zl(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Xl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Iv(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Iv(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Kl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Mv(e){return e.reduce((t,n)=>Ce(t,n.meta),{})}function Ql(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Lv(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Ph(e,t[o])<0?r=o:n=o+1}const i=Dv(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function Dv(e){let t=e;for(;t=t.parent;)if(Fh(t)&&Ph(e,t)===0)return t}function Fh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Rv(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<r.length;++i){const o=r[i].replace(Ih," "),u=o.indexOf("="),c=Hr(u<0?o:o.slice(0,u)),l=u<0?null:Hr(o.slice(u+1));if(c in t){let s=t[c];Lt(s)||(s=t[c]=[s]),s.push(l)}else t[c]=l}return t}function Jl(e){let t="";for(let n in e){const r=e[n];if(n=Q_(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Lt(r)?r.map(o=>o&&ga(o)):[r&&ga(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function kv(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Lt(r)?r.map(i=>i==null?null:""+i):r==null?r:""+r)}return t}const Nv=Symbol(""),Zl=Symbol(""),Vo=Symbol(""),Va=Symbol(""),va=Symbol("");function ur(){let e=[];function t(r){return e.push(r),()=>{const i=e.indexOf(r);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function an(e,t,n,r,i,o=u=>u()){const u=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((c,l)=>{const s=f=>{f===!1?l(er(4,{from:n,to:t})):f instanceof Error?l(f):vv(f)?l(er(2,{from:t,to:f})):(u&&r.enterCallbacks[i]===u&&typeof f=="function"&&u.push(f),c())},a=o(()=>e.call(r&&r.instances[i],t,n,s));let d=Promise.resolve(a);e.length<3&&(d=d.then(s)),d.catch(f=>l(f))})}function _s(e,t,n,r,i=o=>o()){const o=[];for(const u of e)for(const c in u.components){let l=u.components[c];if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(Sh(l)){const a=(l.__vccOpts||l)[t];a&&o.push(an(a,n,r,u,c,i))}else{let s=l();o.push(()=>s.then(a=>{if(!a)throw new Error("Couldn't resolve component \"".concat(c,'" at "').concat(u.path,'"'));const d=H_(a)?a.default:a;u.mods[c]=a,u.components[c]=d;const h=(d.__vccOpts||d)[t];return h&&an(h,n,r,u,c,i)()}))}}return o}function ec(e){const t=mt(Vo),n=mt(Va),r=de(()=>{const l=Z(e.to);return t.resolve(l)}),i=de(()=>{const{matched:l}=r.value,{length:s}=l,a=l[s-1],d=n.matched;if(!a||!d.length)return-1;const f=d.findIndex(Zn.bind(null,a));if(f>-1)return f;const h=tc(l[s-2]);return s>1&&tc(a)===h&&d[d.length-1].path!==h?d.findIndex(Zn.bind(null,l[s-2])):f}),o=de(()=>i.value>-1&&$v(n.params,r.value.params)),u=de(()=>i.value>-1&&i.value===n.matched.length-1&&Rh(n.params,r.value.params));function c(l={}){if(Bv(l)){const s=t[Z(e.replace)?"replace":"push"](Z(e.to)).catch(Sr);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>s),s}return Promise.resolve()}return{route:r,href:de(()=>r.value.href),isActive:o,isExactActive:u,navigate:c}}function xv(e){return e.length===1?e[0]:e}const Pv=Ge({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:ec,setup(e,{slots:t}){const n=In(ec(e)),{options:r}=mt(Vo),i=de(()=>({[nc(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[nc(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&xv(t.default(n));return e.custom?o:Wr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),Fv=Pv;function Bv(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function $v(e,t){for(const n in t){const r=t[n],i=e[n];if(typeof r=="string"){if(r!==i)return!1}else if(!Lt(i)||i.length!==r.length||r.some((o,u)=>o!==i[u]))return!1}return!0}function tc(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const nc=(e,t,n)=>e!=null?e:t!=null?t:n,Hv=Ge({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=mt(va),i=de(()=>e.route||r.value),o=mt(Zl,0),u=de(()=>{let s=Z(o);const{matched:a}=i.value;let d;for(;(d=a[s])&&!d.components;)s++;return s}),c=de(()=>i.value.matched[u.value]);uo(Zl,de(()=>u.value+1)),uo(Nv,c),uo(va,i);const l=_e();return Ft(()=>[l.value,c.value,e.name],([s,a,d],[f,h,p])=>{a&&(a.instances[d]=s,h&&h!==a&&s&&s===f&&(a.leaveGuards.size||(a.leaveGuards=h.leaveGuards),a.updateGuards.size||(a.updateGuards=h.updateGuards))),s&&a&&(!h||!Zn(a,h)||!f)&&(a.enterCallbacks[d]||[]).forEach(m=>m(s))},{flush:"post"}),()=>{const s=i.value,a=e.name,d=c.value,f=d&&d.components[a];if(!f)return rc(n.default,{Component:f,route:s});const h=d.props[a],p=h?h===!0?s.params:typeof h=="function"?h(s):h:null,_=Wr(f,Ce({},p,t,{onVnodeUnmounted:g=>{g.component.isUnmounted&&(d.instances[a]=null)},ref:l}));return rc(n.default,{Component:_,route:s})||_}}});function rc(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Bh=Hv;function jv(e){const t=Cv(e.routes,e),n=e.parseQuery||Rv,r=e.stringifyQuery||Jl,i=e.history,o=ur(),u=ur(),c=ur(),l=ud(en);let s=en;Gn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=ps.bind(null,U=>""+U),d=ps.bind(null,Z_),f=ps.bind(null,Hr);function h(U,K){let X,Q;return Nh(U)?(X=t.getRecordMatcher(U),Q=K):Q=U,t.addRoute(Q,X)}function p(U){const K=t.getRecordMatcher(U);K&&t.removeRoute(K)}function m(){return t.getRoutes().map(U=>U.record)}function _(U){return!!t.getRecordMatcher(U)}function g(U,K){if(K=Ce({},K||l.value),typeof U=="string"){const C=gs(n,U,K.path),F=t.resolve({path:C.path},K),$=i.createHref(C.fullPath);return Ce(C,F,{params:f(F.params),hash:Hr(C.hash),redirectedFrom:void 0,href:$})}let X;if(U.path!=null)X=Ce({},U,{path:gs(n,U.path,K.path).path});else{const C=Ce({},U.params);for(const F in C)C[F]==null&&delete C[F];X=Ce({},U,{params:d(C)}),K.params=d(K.params)}const Q=t.resolve(X,K),fe=U.hash||"";Q.params=a(f(Q.params));const S=nv(r,Ce({},U,{hash:K_(fe),path:Q.path})),y=i.createHref(S);return Ce({fullPath:S,hash:fe,query:r===Jl?kv(U.query):U.query||{}},Q,{redirectedFrom:void 0,href:y})}function v(U){return typeof U=="string"?gs(n,U,l.value.path):Ce({},U)}function b(U,K){if(s!==U)return er(8,{from:K,to:U})}function E(U){return O(U)}function D(U){return E(Ce(v(U),{replace:!0}))}function I(U){const K=U.matched[U.matched.length-1];if(K&&K.redirect){const{redirect:X}=K;let Q=typeof X=="function"?X(U):X;return typeof Q=="string"&&(Q=Q.includes("?")||Q.includes("#")?Q=v(Q):{path:Q},Q.params={}),Ce({query:U.query,hash:U.hash,params:Q.path!=null?{}:U.params},Q)}}function O(U,K){const X=s=g(U),Q=l.value,fe=U.state,S=U.force,y=U.replace===!0,C=I(X);if(C)return O(Ce(v(C),{state:typeof C=="object"?Ce({},fe,C.state):fe,force:S,replace:y}),K||X);const F=X;F.redirectedFrom=K;let $;return!S&&rv(r,Q,X)&&($=er(16,{to:F,from:Q}),ie(Q,Q,!0,!1)),($?Promise.resolve($):k(F,Q)).catch(j=>Yt(j)?Yt(j,2)?j:te(j):V(j,F,Q)).then(j=>{if(j){if(Yt(j,2))return O(Ce({replace:y},v(j.to),{state:typeof j.to=="object"?Ce({},fe,j.to.state):fe,force:S}),K||F)}else j=L(F,Q,!0,y,fe);return R(F,Q,j),j})}function M(U,K){const X=b(U,K);return X?Promise.reject(X):Promise.resolve()}function T(U){const K=Ee.values().next().value;return K&&typeof K.runWithContext=="function"?K.runWithContext(U):U()}function k(U,K){let X;const[Q,fe,S]=Uv(U,K);X=_s(Q.reverse(),"beforeRouteLeave",U,K);for(const C of Q)C.leaveGuards.forEach(F=>{X.push(an(F,U,K))});const y=M.bind(null,U,K);return X.push(y),Ae(X).then(()=>{X=[];for(const C of o.list())X.push(an(C,U,K));return X.push(y),Ae(X)}).then(()=>{X=_s(fe,"beforeRouteUpdate",U,K);for(const C of fe)C.updateGuards.forEach(F=>{X.push(an(F,U,K))});return X.push(y),Ae(X)}).then(()=>{X=[];for(const C of S)if(C.beforeEnter)if(Lt(C.beforeEnter))for(const F of C.beforeEnter)X.push(an(F,U,K));else X.push(an(C.beforeEnter,U,K));return X.push(y),Ae(X)}).then(()=>(U.matched.forEach(C=>C.enterCallbacks={}),X=_s(S,"beforeRouteEnter",U,K,T),X.push(y),Ae(X))).then(()=>{X=[];for(const C of u.list())X.push(an(C,U,K));return X.push(y),Ae(X)}).catch(C=>Yt(C,8)?C:Promise.reject(C))}function R(U,K,X){c.list().forEach(Q=>T(()=>Q(U,K,X)))}function L(U,K,X,Q,fe){const S=b(U,K);if(S)return S;const y=K===en,C=Gn?history.state:{};X&&(Q||y?i.replace(U.fullPath,Ce({scroll:y&&C&&C.scroll},fe)):i.push(U.fullPath,fe)),l.value=U,ie(U,K,X,y),te()}let B;function N(){B||(B=i.listen((U,K,X)=>{if(!Oe.listening)return;const Q=g(U),fe=I(Q);if(fe){O(Ce(fe,{replace:!0,force:!0}),Q).catch(Sr);return}s=Q;const S=l.value;Gn&&fv(Yl(S.fullPath,X.delta),Go()),k(Q,S).catch(y=>Yt(y,12)?y:Yt(y,2)?(O(Ce(v(y.to),{force:!0}),Q).then(C=>{Yt(C,20)&&!X.delta&&X.type===jr.pop&&i.go(-1,!1)}).catch(Sr),Promise.reject()):(X.delta&&i.go(-X.delta,!1),V(y,Q,S))).then(y=>{y=y||L(Q,S,!1),y&&(X.delta&&!Yt(y,8)?i.go(-X.delta,!1):X.type===jr.pop&&Yt(y,20)&&i.go(-1,!1)),R(Q,S,y)}).catch(Sr)}))}let G=ur(),Y=ur(),W;function V(U,K,X){te(U);const Q=Y.list();return Q.length?Q.forEach(fe=>fe(U,K,X)):console.error(U),Promise.reject(U)}function J(){return W&&l.value!==en?Promise.resolve():new Promise((U,K)=>{G.add([U,K])})}function te(U){return W||(W=!U,N(),G.list().forEach(([K,X])=>U?X(U):K()),G.reset()),U}function ie(U,K,X,Q){const{scrollBehavior:fe}=e;if(!Gn||!fe)return Promise.resolve();const S=!X&&dv(Yl(U.fullPath,0))||(Q||!X)&&history.state&&history.state.scroll||null;return xo().then(()=>fe(U,K,S)).then(y=>y&&cv(y)).catch(y=>V(y,U,K))}const ue=U=>i.go(U);let me;const Ee=new Set,Oe={currentRoute:l,listening:!0,addRoute:h,removeRoute:p,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:m,resolve:g,options:e,push:E,replace:D,go:ue,back:()=>ue(-1),forward:()=>ue(1),beforeEach:o.add,beforeResolve:u.add,afterEach:c.add,onError:Y.add,isReady:J,install(U){const K=this;U.component("RouterLink",Fv),U.component("RouterView",Bh),U.config.globalProperties.$router=K,Object.defineProperty(U.config.globalProperties,"$route",{enumerable:!0,get:()=>Z(l)}),Gn&&!me&&l.value===en&&(me=!0,E(i.location).catch(fe=>{}));const X={};for(const fe in en)Object.defineProperty(X,fe,{get:()=>l.value[fe],enumerable:!0});U.provide(Vo,K),U.provide(Va,sd(X)),U.provide(va,l);const Q=U.unmount;Ee.add(U),U.unmount=function(){Ee.delete(U),Ee.size<1&&(s=en,B&&B(),B=null,l.value=en,me=!1,W=!1),Q()}}};function Ae(U){return U.reduce((K,X)=>K.then(()=>T(X)),Promise.resolve())}return Oe}function Uv(e,t){const n=[],r=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let u=0;u<o;u++){const c=t.matched[u];c&&(e.matched.find(s=>Zn(s,c))?r.push(c):n.push(c));const l=e.matched[u];l&&(t.matched.find(s=>Zn(s,l))||i.push(l))}return[n,r,i]}function zr(){return mt(Vo)}function $h(e){return mt(Va)}const Yv=Ge({__name:"App",setup(e){const t=Rn(),n=t.WIDGET.COLOR_SCHEME,r=window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)"),i=()=>{t.isOnline=!1},o=()=>{t.isOnline=!0},u=c=>{t.ENVIRONMENT.deviceScheme=c.matches?pt.ColorScheme.DARK:pt.ColorScheme.LIGHT};return $t(async()=>{window.addEventListener("offline",i),window.addEventListener("online",o),n?(document.documentElement.setAttribute("theme",n),t.ENVIRONMENT.deviceScheme=n):r&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",u)}),Yr(async()=>{window.removeEventListener("offline",i),window.removeEventListener("online",o),!n&&r&&window.matchMedia("(prefers-color-scheme: dark)").removeEventListener("change",u)}),(c,l)=>(ne(),et(Z(Bh),null,{default:He(({Component:s,route:a})=>[be(Or,{name:a.meta.transition},{default:He(()=>[(ne(),et(xp(s)))]),_:2},1032,["name"])]),_:1}))}}),ic=Ge({__name:"UIIcon",props:{name:{type:String,require:!0},color:{type:String,require:!1}},setup(e){return(t,n)=>(ne(),ae("span",{class:$e(["picon","picon-".concat(e.name),e.color||""])},null,2))}}),qv={class:"loading-container"},Gv={key:0,class:"loading-text"},Hh=Ge({__name:"UILoading",props:{colorBackground:{type:Boolean,default:!1},success:{type:Boolean,default:void 0},thickness:{type:String,default:""},size:{type:String,default:"md"},color:{type:String,default:""},emptyColor:{type:String,default:""},successColor:{type:String,default:""},failedColor:{type:String,default:""},text:{type:String,default:""}},setup(e){Pg(a=>({"3b55d032":n.value,"016ed342":r.value,"178fa1ae":i.value,"5b848406":o.value,"4a23360b":u.value,"51a6d6fd":c.value}));const t=e,n=de(()=>s(t.color||(t.colorBackground?"#FFFFFF":"accent"))),r=de(()=>s(t.emptyColor||(t.colorBackground?"rgba(255,255,255,0.2)":"var(--color-background-heavy)"))),i=de(()=>s(t.successColor||(t.colorBackground?"#FFFFFF":"success"))),o=de(()=>s(t.failedColor||(t.colorBackground?"#FFFFFF":"error"))),u=de(()=>{const a=t.thickness||t.size;switch(a){case"xxs":return"2px";case"xs":return"2px";case"sm":return"4px";case"md":return"6px";case"lg":return"8px";case"xl":return"10px";default:return a||"6px"}}),c=de(()=>{switch(t.size){case"xxs":return"1em";case"xs":return"2em";case"sm":return"3em";case"md":return"5em";case"lg":return"7em";case"xl":return"10em";default:return t.size||"5em"}}),l=de(()=>t.success===!0?"success":t.success===!1?"failed":"");function s(a){return["primary","accent","success","warning","error"].indexOf(a)!==-1?"var(--color-background-".concat(a,")"):a}return(a,d)=>(ne(),ae("div",qv,[oe("div",{class:$e("circle-loader ".concat(l.value))},d[0]||(d[0]=[oe("div",{class:"status draw"},null,-1)]),2),e.text?(ne(),ae("div",Gv,Le(e.text),1)):pe("",!0)]))}}),Vv={class:"ripple-container"},Wv=Ge({__name:"UIRipple",setup(e,{expose:t}){const n=_e([]),r=_e([]);function i(o){const u=o.currentTarget.getBoundingClientRect(),c=Math.max(u.width,u.height),l=o.clientX-u.left-c/2,s=o.clientY-u.top-c/2,a=Date.now();n.value.push({key:a,style:{width:"".concat(c,"px"),height:"".concat(c,"px"),top:"".concat(s,"px"),left:"".concat(l,"px")}});const d=setTimeout(()=>{n.value=n.value.filter(f=>f.key!==a)},600);r.value.push(d)}return tr(()=>{r.value.forEach(o=>clearTimeout(o)),r.value=[]}),t({createRipple:i}),(o,u)=>(ne(),ae("div",Vv,[(ne(!0),ae(Be,null,qr(n.value,c=>(ne(),ae("div",{class:"ripple",key:c.key,style:dt(c.style)},null,4))),128))]))}}),zv={key:1,class:"button-title-container"},Xv={key:0,class:"status-container"},Bt=Ge({__name:"UIButton",props:{type:{type:String,default:"solid"},color:{type:String,default:"accent"},icon:{type:Object,required:!1},title:String,titleClass:{type:String,default:""},subtitle:String,subtitleClass:{type:String,default:""},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:t}){const n=e,{loading:r}=ko(n),i=t,o=_e(!1),u=_e(null),c=_e(void 0),l="ontouchstart"in window;let s;$t(()=>{var _,g,v,b,E;l?((_=u.value)==null||_.addEventListener("touchstart",d,{passive:!0}),(g=u.value)==null||g.addEventListener("touchmove",f,{passive:!0}),(v=u.value)==null||v.addEventListener("touchend",h,{passive:!0}),(b=u.value)==null||b.addEventListener("touchcancel",p)):(E=u.value)==null||E.addEventListener("click",a)}),Yr(()=>{var _,g,v,b,E;s&&clearTimeout(s),l?((_=u.value)==null||_.removeEventListener("touchstart",d),(g=u.value)==null||g.removeEventListener("touchmove",f),(v=u.value)==null||v.removeEventListener("touchend",h),(b=u.value)==null||b.removeEventListener("touchcancel",p)):(E=u.value)==null||E.removeEventListener("click",a)});function a(_){var g;_.stopPropagation(),!(o.value||r.value)&&(o.value=!0,(g=c.value)==null||g.createRipple(_),s&&clearTimeout(s),s=setTimeout(()=>{i("click",_),o.value=!1},200))}function d(_){var g;u.value&&!o.value&&!r.value&&(o.value=!0,(g=c.value)==null||g.createRipple(_))}function f(_){if(u.value){const g=_.touches[0],v=u.value.getBoundingClientRect();m(v,g.clientX,g.clientY)||(o.value=!1)}}function h(_){if(_.stopPropagation(),u.value&&o.value){const g=_.changedTouches[0],v=u.value.getBoundingClientRect();m(v,g.clientX,g.clientY)&&i("click",_),o.value=!1}}function p(_){_.stopPropagation(),u.value&&o.value&&(o.value=!1)}function m(_,g,v){const{left:b,top:E,width:D,height:I}=_;return g>=b&&g<=b+D&&v>=E&&v<=E+I}return(_,g)=>(ne(),ae("div",{ref_key:"buttonRef",ref:u,class:$e(["button",e.type,e.color,e.disabled?"disabled":"",e.icon&&!(e.title||e.subtitle)&&e.type==="clear"?"clear-icon":""])},[oe("div",{class:"button-wrapper",style:dt({opacity:Z(r)?0:1})},[e.icon&&e.icon.position!=="right"?(ne(),et(ic,{key:0,name:e.icon.name,class:$e("button-icon ".concat(e.icon.class||""))},null,8,["name","class"])):pe("",!0),e.title||e.subtitle||_.$slots.content?(ne(),ae("span",zv,[e.title?(ne(),ae("span",{key:0,class:$e("button-title "+e.titleClass)},Le(e.title),3)):pe("",!0),e.subtitle?(ne(),ae("span",{key:1,class:$e("button-subtitle "+e.subtitleClass)},Le(e.subtitle),3)):pe("",!0),Qe(_.$slots,"content")])):pe("",!0),e.icon&&e.icon.position==="right"?(ne(),et(ic,{key:2,name:e.icon.name,class:$e("button-icon "+e.icon.class)},null,8,["name","class"])):pe("",!0)],4),be(Or,{name:"fade"},{default:He(()=>[Z(r)?(ne(),ae("div",Xv,[Qe(_.$slots,"status",{},()=>[be(Hh,{size:"xxs",colorBackground:e.type==="solid"},null,8,["colorBackground"])])])):pe("",!0)]),_:3}),be(Wv,{ref_key:"rippleRef",ref:c},null,512)],2))}}),Kv=["theme"],Qv={key:2,class:"status"},Jv={key:3,class:"status"},jh=Ge({__name:"UINavigationBar",props:{navBack:{type:Object},theme:{type:String,default:"perkd"},leftClass:{type:String,default:""},centerClass:{type:String,default:""},rightClass:{type:String,default:""},title:String,titleClass:{type:String,default:""},isOnline:{type:Boolean,default:!0},status:{type:String,default:""}},setup(e){const{t}=_t();return(n,r)=>{var i,o,u,c;return ne(),ae("div",{class:$e(["navigation-bar",e.isOnline?"online":"offline"]),theme:e.theme},[n.$slots.leftContent||e.navBack||!e.isOnline||e.isOnline&&e.status?(ne(),ae("div",{key:0,class:$e("left-container "+e.leftClass)},[e.navBack&&((i=e.navBack)==null?void 0:i.type)==="back"?(ne(),et(Bt,{key:0,type:"clear",icon:{name:"back"},class:"back-button",onClick:(o=e.navBack)==null?void 0:o.onClick},null,8,["onClick"])):pe("",!0),e.navBack&&((u=e.navBack)==null?void 0:u.type)==="cancel"?(ne(),et(Bt,{key:1,type:"clear",title:Z(t)("button.cancel"),onClick:(c=e.navBack)==null?void 0:c.onClick},null,8,["title","onClick"])):pe("",!0),e.isOnline?pe("",!0):(ne(),ae("span",Qv,Le(Z(t)("error.offline_status")),1)),e.isOnline&&e.status?(ne(),ae("span",Jv,Le(e.status),1)):pe("",!0),Qe(n.$slots,"leftContent")],2)):pe("",!0),n.$slots.centerContent||e.title?(ne(),ae("div",{key:1,class:$e("center-container "+e.centerClass)},[e.title?(ne(),ae("div",{key:0,class:$e("navigation-title "+e.titleClass)},Le(e.title),3)):pe("",!0),Qe(n.$slots,"centerContent")],2)):pe("",!0),n.$slots.rightContent?(ne(),ae("div",{key:2,class:$e("right-container "+e.rightClass)},[Qe(n.$slots,"rightContent")],2)):pe("",!0)],10,Kv)}}}),Zv={key:0,theme:"light",class:"notify-container"},ey={key:0,class:"content"},ty={key:0,class:"screen overlay"},ny={key:0,class:"screen overlay"},ry=Ge({__name:"UIScreen",props:{title:{type:String,default:""},titleClass:{type:String,default:""},navigationBarTheme:String,disableNavBack:{type:Boolean,default:!1},isOnline:{type:Boolean,default:!0},status:{type:String,default:""},showClose:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},onContentScroll:{type:Function}},emits:["goPreviousPage","closeWindow"],setup(e,{expose:t,emit:n}){const r=Bp(),i=e,{title:o,titleClass:u,navigationBarTheme:c,isOnline:l,status:s,showClose:a,loading:d}=ko(i),f=window.innerHeight/3,h=window.innerHeight/2,p=_e(0),m=_e(void 0),_=_e(void 0);let g;const v=n,b=de(()=>{var N;return(N=window.history.state)!=null&&N.back&&!i.disableNavBack?{type:"back",onClick:()=>v("goPreviousPage")}:void 0}),E=de(()=>({title:o.value,titleClass:u.value,theme:c==null?void 0:c.value,isOnline:l.value,status:s.value,navBack:b.value})),D=de(()=>{const N=[];return(r.navigationBar||o.value||b.value||a.value)&&N.push("with-navigation-bar"),r.tabBar&&N.push("with-tab-bar"),N});$t(()=>{var N;i.onContentScroll&&((N=m.value)==null||N.addEventListener("scroll",I))}),Yr(()=>{var N;i.onContentScroll&&((N=m.value)==null||N.removeEventListener("scroll",I))});function I(N){i.onContentScroll&&i.onContentScroll(N)}function O(N,G){g&&(clearTimeout(g),g=void 0),G?M(N):T()}function M(N){const G=N.target;p.value=G.getBoundingClientRect().top;const Y=p.value-f;_.value&&(_.value.classList.remove("close"),_.value.style.height=h+"px"),setTimeout(()=>{var W;(W=m.value)==null||W.scrollBy({top:Y,behavior:"smooth"})},0)}function T(){g=setTimeout(()=>{_.value&&_.value.classList.add("close")},500)}function k(N,G){var Y;const W={behavior:"smooth"};N!==void 0&&Object.assign(W,{top:N}),G!==void 0&&Object.assign(W,{left:G}),(Y=m.value)==null||Y.scrollBy(W)}function R(N){var G;(G=m.value)==null||G.scrollTo({top:N||0,behavior:"smooth"})}function L(N){var G;(G=m.value)==null||G.scrollTo({left:N||0,behavior:"smooth"})}function B(){v("closeWindow")}return t({scrollBy:k,scrollToTop:R,scrollToLeft:L}),(N,G)=>{var Y;return ne(),ae(Be,null,[oe("div",bo({class:["screen",...D.value]},N.$attrs),[Qe(N.$slots,"navigationBar",{},()=>[Z(o)||b.value||Z(a)?(ne(),et(jh,Hf(bo({key:0},E.value)),Ln({_:2},[Z(a)?{name:"rightContent",fn:He(()=>[be(Bt,{type:"circle",icon:{name:"close"},onClick:B})]),key:"0"}:void 0]),1040)):pe("",!0)]),oe("div",{ref_key:"screenContentRef",ref:m,class:$e("screen-content ".concat(Z(r).footer?"screen-content-with-footer":""))},[be(Or,{name:"swipe-down"},{default:He(()=>[Z(r).notify?(ne(),ae("div",Zv,[Qe(N.$slots,"notify")])):pe("",!0)]),_:3}),Z(r).content?(ne(),ae("div",ey,[Qe(N.$slots,"content",{focusChange:O})])):pe("",!0),Z(r).footer?(ne(),ae("div",{key:1,class:$e("footer ".concat((Y=_.value)!=null&&Y.style.height?"footer-before-keyboard":""))},[Qe(N.$slots,"footer")],2)):pe("",!0),oe("div",{ref_key:"keyboardRef",ref:_,class:"screen-keyboard"},null,512)],2),Qe(N.$slots,"tabBar")],16),be(Or,{name:"fade"},{default:He(()=>[Z(d)?(ne(),ae("div",ty,[Qe(N.$slots,"loading",{},()=>[be(Hh)])])):pe("",!0)]),_:3}),be(Or,{name:"fade"},{default:He(()=>[Z(r).dialog?(ne(),ae("div",ny,[Qe(N.$slots,"dialog")])):pe("",!0)]),_:3})],64)}}}),iy={class:"dialog-container"},oy={key:0,class:"dialog-title"},sy={key:1,class:"dialog-desc"},ay={key:2,class:"dialog-content"},uy={class:"actions-container"},Wo=Ge({__name:"UIDialog",props:{title:{type:String,default:""},description:{type:String,default:""}},setup(e){const{t}=_t();return(n,r)=>(ne(),ae("div",iy,[e.title?(ne(),ae("div",oy,Le(e.title),1)):pe("",!0),e.description?(ne(),ae("div",sy,Le(e.description),1)):pe("",!0),n.$slots.content?(ne(),ae("div",ay,[Qe(n.$slots,"content")])):pe("",!0),oe("div",uy,[Qe(n.$slots,"buttons",{},()=>[be(Bt,{type:"clear",title:Z(t)("button.ok"),onClick:r[0]||(r[0]=i=>n.$emit("closeDialog"))},null,8,["title"])])])]))}});var di={},lr={},hi={},oc;function wt(){if(oc)return hi;oc=1,Object.defineProperty(hi,"__esModule",{value:!0});function e(n,r){if(!(n instanceof r))throw new TypeError("Cannot call a class as a function")}var t=function n(r,i){e(this,n),this.data=r,this.text=i.text||r,this.options=i};return hi.default=t,hi}var sc;function ly(){if(sc)return lr;sc=1,Object.defineProperty(lr,"__esModule",{value:!0}),lr.CODE39=void 0;var e=function(){function m(_,g){for(var v=0;v<g.length;v++){var b=g[v];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(_,b.key,b)}}return function(_,g,v){return g&&m(_.prototype,g),v&&m(_,v),_}}(),t=wt(),n=r(t);function r(m){return m&&m.__esModule?m:{default:m}}function i(m,_){if(!(m instanceof _))throw new TypeError("Cannot call a class as a function")}function o(m,_){if(!m)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:m}function u(m,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);m.prototype=Object.create(_&&_.prototype,{constructor:{value:m,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(m,_):m.__proto__=_)}var c=function(m){u(_,m);function _(g,v){return i(this,_),g=g.toUpperCase(),v.mod43&&(g+=f(p(g))),o(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,g,v))}return e(_,[{key:"encode",value:function(){for(var v=a("*"),b=0;b<this.data.length;b++)v+=a(this.data[b])+"0";return v+=a("*"),{data:v,text:this.text}}},{key:"valid",value:function(){return this.data.search(/^[0-9A-Z\-\.\ \$\/\+\%]+$/)!==-1}}]),_}(n.default),l=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","*"],s=[20957,29783,23639,30485,20951,29813,23669,20855,29789,23645,29975,23831,30533,22295,30149,24005,21623,29981,23837,22301,30023,23879,30545,22343,30161,24017,21959,30065,23921,22385,29015,18263,29141,17879,29045,18293,17783,29021,18269,17477,17489,17681,20753,35770];function a(m){return d(h(m))}function d(m){return s[m].toString(2)}function f(m){return l[m]}function h(m){return l.indexOf(m)}function p(m){for(var _=0,g=0;g<m.length;g++)_+=h(m[g]);return _=_%43,_}return lr.CODE39=c,lr}var Tt={},mi={},pi={},Fe={},ac;function Xr(){if(ac)return Fe;ac=1,Object.defineProperty(Fe,"__esModule",{value:!0});var e;function t(l,s,a){return s in l?Object.defineProperty(l,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):l[s]=a,l}var n=Fe.SET_A=0,r=Fe.SET_B=1,i=Fe.SET_C=2;Fe.SHIFT=98;var o=Fe.START_A=103,u=Fe.START_B=104,c=Fe.START_C=105;return Fe.MODULO=103,Fe.STOP=106,Fe.FNC1=207,Fe.SET_BY_CODE=(e={},t(e,o,n),t(e,u,r),t(e,c,i),e),Fe.SWAP={101:n,100:r,99:i},Fe.A_START_CHAR="Ð",Fe.B_START_CHAR="Ñ",Fe.C_START_CHAR="Ò",Fe.A_CHARS="[\0-_È-Ï]",Fe.B_CHARS="[ -È-Ï]",Fe.C_CHARS="(Ï*[0-9]{2}Ï*)",Fe.BARS=[11011001100,11001101100,11001100110,10010011e3,10010001100,10001001100,10011001e3,10011000100,10001100100,11001001e3,11001000100,11000100100,10110011100,10011011100,10011001110,10111001100,10011101100,10011100110,11001110010,11001011100,11001001110,11011100100,11001110100,11101101110,11101001100,11100101100,11100100110,11101100100,11100110100,11100110010,11011011e3,11011000110,11000110110,10100011e3,10001011e3,10001000110,10110001e3,10001101e3,10001100010,11010001e3,11000101e3,11000100010,10110111e3,10110001110,10001101110,10111011e3,10111000110,10001110110,11101110110,11010001110,11000101110,11011101e3,11011100010,11011101110,11101011e3,11101000110,11100010110,11101101e3,11101100010,11100011010,11101111010,11001000010,11110001010,1010011e4,10100001100,1001011e4,10010000110,10000101100,10000100110,1011001e4,10110000100,1001101e4,10011000010,10000110100,10000110010,11000010010,1100101e4,11110111010,11000010100,10001111010,10100111100,10010111100,10010011110,10111100100,10011110100,10011110010,11110100100,11110010100,11110010010,11011011110,11011110110,11110110110,10101111e3,10100011110,10001011110,10111101e3,10111100010,11110101e3,11110100010,10111011110,10111101110,11101011110,11110101110,11010000100,1101001e4,11010011100,1100011101011],Fe}var uc;function zo(){if(uc)return pi;uc=1,Object.defineProperty(pi,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=wt(),n=i(t),r=Xr();function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){o(this,a);var h=u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,d.substring(1),f));return h.bytes=d.split("").map(function(p){return p.charCodeAt(0)}),h}return e(a,[{key:"valid",value:function(){return/^[\x00-\x7F\xC8-\xD3]+$/.test(this.data)}},{key:"encode",value:function(){var f=this.bytes,h=f.shift()-105,p=r.SET_BY_CODE[h];if(p===void 0)throw new RangeError("The encoding does not start with a start character.");this.shouldEncodeAsEan128()===!0&&f.unshift(r.FNC1);var m=a.next(f,1,p);return{text:this.text===this.data?this.text.replace(/[^\x20-\x7E]/g,""):this.text,data:a.getBar(h)+m.result+a.getBar((m.checksum+h)%r.MODULO)+a.getBar(r.STOP)}}},{key:"shouldEncodeAsEan128",value:function(){var f=this.options.ean128||!1;return typeof f=="string"&&(f=f.toLowerCase()==="true"),f}}],[{key:"getBar",value:function(f){return r.BARS[f]?r.BARS[f].toString():""}},{key:"correctIndex",value:function(f,h){if(h===r.SET_A){var p=f.shift();return p<32?p+64:p-32}else return h===r.SET_B?f.shift()-32:(f.shift()-48)*10+f.shift()-48}},{key:"next",value:function(f,h,p){if(!f.length)return{result:"",checksum:0};var m=void 0,_=void 0;if(f[0]>=200){_=f.shift()-105;var g=r.SWAP[_];g!==void 0?m=a.next(f,h+1,g):((p===r.SET_A||p===r.SET_B)&&_===r.SHIFT&&(f[0]=p===r.SET_A?f[0]>95?f[0]-96:f[0]:f[0]<32?f[0]+96:f[0]),m=a.next(f,h+1,p))}else _=a.correctIndex(f,p),m=a.next(f,h+1,p);var v=a.getBar(_),b=_*h;return{result:v+m.result,checksum:b+m.checksum}}}]),a}(n.default);return pi.default=l,pi}var gi={},lc;function cy(){if(lc)return gi;lc=1,Object.defineProperty(gi,"__esModule",{value:!0});var e=Xr(),t=function(c){return c.match(new RegExp("^"+e.A_CHARS+"*"))[0].length},n=function(c){return c.match(new RegExp("^"+e.B_CHARS+"*"))[0].length},r=function(c){return c.match(new RegExp("^"+e.C_CHARS+"*"))[0]};function i(u,c){var l=c?e.A_CHARS:e.B_CHARS,s=u.match(new RegExp("^("+l+"+?)(([0-9]{2}){2,})([^0-9]|$)"));if(s)return s[1]+"Ì"+o(u.substring(s[1].length));var a=u.match(new RegExp("^"+l+"+"))[0];return a.length===u.length?u:a+String.fromCharCode(c?205:206)+i(u.substring(a.length),!c)}function o(u){var c=r(u),l=c.length;if(l===u.length)return u;u=u.substring(l);var s=t(u)>=n(u);return c+String.fromCharCode(s?206:205)+i(u,s)}return gi.default=function(u){var c=void 0,l=r(u).length;if(l>=2)c=e.C_START_CHAR+o(u);else{var s=t(u)>n(u);c=(s?e.A_START_CHAR:e.B_START_CHAR)+i(u,s)}return c.replace(/[\xCD\xCE]([^])[\xCD\xCE]/,function(a,d){return"Ë"+d})},gi}var cc;function fy(){if(cc)return mi;cc=1,Object.defineProperty(mi,"__esModule",{value:!0});var e=zo(),t=i(e),n=cy(),r=i(n);function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){if(o(this,a),/^[\x00-\x7F\xC8-\xD3]+$/.test(d))var h=u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,(0,r.default)(d),f));else var h=u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,d,f));return u(h)}return a}(t.default);return mi.default=l,mi}var _i={},fc;function dy(){if(fc)return _i;fc=1,Object.defineProperty(_i,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=zo(),n=i(t),r=Xr();function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){return o(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,r.A_START_CHAR+d,f))}return e(a,[{key:"valid",value:function(){return new RegExp("^"+r.A_CHARS+"+$").test(this.data)}}]),a}(n.default);return _i.default=l,_i}var vi={},dc;function hy(){if(dc)return vi;dc=1,Object.defineProperty(vi,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=zo(),n=i(t),r=Xr();function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){return o(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,r.B_START_CHAR+d,f))}return e(a,[{key:"valid",value:function(){return new RegExp("^"+r.B_CHARS+"+$").test(this.data)}}]),a}(n.default);return vi.default=l,vi}var yi={},hc;function my(){if(hc)return yi;hc=1,Object.defineProperty(yi,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=zo(),n=i(t),r=Xr();function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){return o(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,r.C_START_CHAR+d,f))}return e(a,[{key:"valid",value:function(){return new RegExp("^"+r.C_CHARS+"+$").test(this.data)}}]),a}(n.default);return yi.default=l,yi}var mc;function py(){if(mc)return Tt;mc=1,Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.CODE128C=Tt.CODE128B=Tt.CODE128A=Tt.CODE128=void 0;var e=fy(),t=l(e),n=dy(),r=l(n),i=hy(),o=l(i),u=my(),c=l(u);function l(s){return s&&s.__esModule?s:{default:s}}return Tt.CODE128=t.default,Tt.CODE128A=r.default,Tt.CODE128B=o.default,Tt.CODE128C=c.default,Tt}var tt={},bi={},qt={},pc;function Kr(){return pc||(pc=1,Object.defineProperty(qt,"__esModule",{value:!0}),qt.SIDE_BIN="101",qt.MIDDLE_BIN="01010",qt.BINARIES={L:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],G:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"],R:["1110010","1100110","1101100","1000010","1011100","1001110","1010000","1000100","1001000","1110100"],O:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],E:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"]},qt.EAN2_STRUCTURE=["LL","LG","GL","GG"],qt.EAN5_STRUCTURE=["GGLLL","GLGLL","GLLGL","GLLLG","LGGLL","LLGGL","LLLGG","LGLGL","LGLLG","LLGLG"],qt.EAN13_STRUCTURE=["LLLLLL","LLGLGG","LLGGLG","LLGGGL","LGLLGG","LGGLLG","LGGGLL","LGLGLG","LGLGGL","LGGLGL"]),qt}var Ei={},Ai={},gc;function Qr(){if(gc)return Ai;gc=1,Object.defineProperty(Ai,"__esModule",{value:!0});var e=Kr(),t=function(r,i,o){var u=r.split("").map(function(l,s){return e.BINARIES[i[s]]}).map(function(l,s){return l?l[r[s]]:""});if(o){var c=r.length-1;u=u.map(function(l,s){return s<c?l+o:l})}return u.join("")};return Ai.default=t,Ai}var _c;function Uh(){if(_c)return Ei;_c=1,Object.defineProperty(Ei,"__esModule",{value:!0});var e=function(){function d(f,h){for(var p=0;p<h.length;p++){var m=h[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(f,m.key,m)}}return function(f,h,p){return h&&d(f.prototype,h),p&&d(f,p),f}}(),t=Kr(),n=Qr(),r=u(n),i=wt(),o=u(i);function u(d){return d&&d.__esModule?d:{default:d}}function c(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function l(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function s(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var a=function(d){s(f,d);function f(h,p){c(this,f);var m=l(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,h,p));return m.fontSize=!p.flat&&p.fontSize>p.width*10?p.width*10:p.fontSize,m.guardHeight=p.height+m.fontSize/2+p.textMargin,m}return e(f,[{key:"encode",value:function(){return this.options.flat?this.encodeFlat():this.encodeGuarded()}},{key:"leftText",value:function(p,m){return this.text.substr(p,m)}},{key:"leftEncode",value:function(p,m){return(0,r.default)(p,m)}},{key:"rightText",value:function(p,m){return this.text.substr(p,m)}},{key:"rightEncode",value:function(p,m){return(0,r.default)(p,m)}},{key:"encodeGuarded",value:function(){var p={fontSize:this.fontSize},m={height:this.guardHeight};return[{data:t.SIDE_BIN,options:m},{data:this.leftEncode(),text:this.leftText(),options:p},{data:t.MIDDLE_BIN,options:m},{data:this.rightEncode(),text:this.rightText(),options:p},{data:t.SIDE_BIN,options:m}]}},{key:"encodeFlat",value:function(){var p=[t.SIDE_BIN,this.leftEncode(),t.MIDDLE_BIN,this.rightEncode(),t.SIDE_BIN];return{data:p.join(""),text:this.text}}}]),f}(o.default);return Ei.default=a,Ei}var vc;function gy(){if(vc)return bi;vc=1,Object.defineProperty(bi,"__esModule",{value:!0});var e=function(){function d(f,h){for(var p=0;p<h.length;p++){var m=h[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(f,m.key,m)}}return function(f,h,p){return h&&d(f.prototype,h),p&&d(f,p),f}}(),t=function d(f,h,p){f===null&&(f=Function.prototype);var m=Object.getOwnPropertyDescriptor(f,h);if(m===void 0){var _=Object.getPrototypeOf(f);return _===null?void 0:d(_,h,p)}else{if("value"in m)return m.value;var g=m.get;return g===void 0?void 0:g.call(p)}},n=Kr(),r=Uh(),i=o(r);function o(d){return d&&d.__esModule?d:{default:d}}function u(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function c(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function l(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var s=function(f){var h=f.substr(0,12).split("").map(function(p){return+p}).reduce(function(p,m,_){return _%2?p+m*3:p+m},0);return(10-h%10)%10},a=function(d){l(f,d);function f(h,p){u(this,f),h.search(/^[0-9]{12}$/)!==-1&&(h+=s(h));var m=c(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,h,p));return m.lastChar=p.lastChar,m}return e(f,[{key:"valid",value:function(){return this.data.search(/^[0-9]{13}$/)!==-1&&+this.data[12]===s(this.data)}},{key:"leftText",value:function(){return t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"leftText",this).call(this,1,6)}},{key:"leftEncode",value:function(){var p=this.data.substr(1,6),m=n.EAN13_STRUCTURE[this.data[0]];return t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"leftEncode",this).call(this,p,m)}},{key:"rightText",value:function(){return t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"rightText",this).call(this,7,6)}},{key:"rightEncode",value:function(){var p=this.data.substr(7,6);return t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"rightEncode",this).call(this,p,"RRRRRR")}},{key:"encodeGuarded",value:function(){var p=t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"encodeGuarded",this).call(this);return this.options.displayValue&&(p.unshift({data:"000000000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),this.options.lastChar&&(p.push({data:"00"}),p.push({data:"00000",text:this.options.lastChar,options:{fontSize:this.fontSize}}))),p}}]),f}(i.default);return bi.default=a,bi}var wi={},yc;function _y(){if(yc)return wi;yc=1,Object.defineProperty(wi,"__esModule",{value:!0});var e=function(){function a(d,f){for(var h=0;h<f.length;h++){var p=f[h];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(d,p.key,p)}}return function(d,f,h){return f&&a(d.prototype,f),h&&a(d,h),d}}(),t=function a(d,f,h){d===null&&(d=Function.prototype);var p=Object.getOwnPropertyDescriptor(d,f);if(p===void 0){var m=Object.getPrototypeOf(d);return m===null?void 0:a(m,f,h)}else{if("value"in p)return p.value;var _=p.get;return _===void 0?void 0:_.call(h)}},n=Uh(),r=i(n);function i(a){return a&&a.__esModule?a:{default:a}}function o(a,d){if(!(a instanceof d))throw new TypeError("Cannot call a class as a function")}function u(a,d){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:a}function c(a,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);a.prototype=Object.create(d&&d.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(a,d):a.__proto__=d)}var l=function(d){var f=d.substr(0,7).split("").map(function(h){return+h}).reduce(function(h,p,m){return m%2?h+p:h+p*3},0);return(10-f%10)%10},s=function(a){c(d,a);function d(f,h){return o(this,d),f.search(/^[0-9]{7}$/)!==-1&&(f+=l(f)),u(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,f,h))}return e(d,[{key:"valid",value:function(){return this.data.search(/^[0-9]{8}$/)!==-1&&+this.data[7]===l(this.data)}},{key:"leftText",value:function(){return t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"leftText",this).call(this,0,4)}},{key:"leftEncode",value:function(){var h=this.data.substr(0,4);return t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"leftEncode",this).call(this,h,"LLLL")}},{key:"rightText",value:function(){return t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"rightText",this).call(this,4,4)}},{key:"rightEncode",value:function(){var h=this.data.substr(4,4);return t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"rightEncode",this).call(this,h,"RRRR")}}]),d}(r.default);return wi.default=s,wi}var Oi={},bc;function vy(){if(bc)return Oi;bc=1,Object.defineProperty(Oi,"__esModule",{value:!0});var e=function(){function f(h,p){for(var m=0;m<p.length;m++){var _=p[m];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(h,_.key,_)}}return function(h,p,m){return p&&f(h.prototype,p),m&&f(h,m),h}}(),t=Kr(),n=Qr(),r=u(n),i=wt(),o=u(i);function u(f){return f&&f.__esModule?f:{default:f}}function c(f,h){if(!(f instanceof h))throw new TypeError("Cannot call a class as a function")}function l(f,h){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:f}function s(f,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);f.prototype=Object.create(h&&h.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(f,h):f.__proto__=h)}var a=function(h){var p=h.split("").map(function(m){return+m}).reduce(function(m,_,g){return g%2?m+_*9:m+_*3},0);return p%10},d=function(f){s(h,f);function h(p,m){return c(this,h),l(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,p,m))}return e(h,[{key:"valid",value:function(){return this.data.search(/^[0-9]{5}$/)!==-1}},{key:"encode",value:function(){var m=t.EAN5_STRUCTURE[a(this.data)];return{data:"1011"+(0,r.default)(this.data,m,"01"),text:this.text}}}]),h}(o.default);return Oi.default=d,Oi}var Ti={},Ec;function yy(){if(Ec)return Ti;Ec=1,Object.defineProperty(Ti,"__esModule",{value:!0});var e=function(){function d(f,h){for(var p=0;p<h.length;p++){var m=h[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(f,m.key,m)}}return function(f,h,p){return h&&d(f.prototype,h),p&&d(f,p),f}}(),t=Kr(),n=Qr(),r=u(n),i=wt(),o=u(i);function u(d){return d&&d.__esModule?d:{default:d}}function c(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function l(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function s(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var a=function(d){s(f,d);function f(h,p){return c(this,f),l(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,h,p))}return e(f,[{key:"valid",value:function(){return this.data.search(/^[0-9]{2}$/)!==-1}},{key:"encode",value:function(){var p=t.EAN2_STRUCTURE[parseInt(this.data)%4];return{data:"1011"+(0,r.default)(this.data,p,"01"),text:this.text}}}]),f}(o.default);return Ti.default=a,Ti}var cr={},Ac;function Yh(){if(Ac)return cr;Ac=1,Object.defineProperty(cr,"__esModule",{value:!0});var e=function(){function d(f,h){for(var p=0;p<h.length;p++){var m=h[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(f,m.key,m)}}return function(f,h,p){return h&&d(f.prototype,h),p&&d(f,p),f}}();cr.checksum=a;var t=Qr(),n=o(t),r=wt(),i=o(r);function o(d){return d&&d.__esModule?d:{default:d}}function u(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function c(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function l(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var s=function(d){l(f,d);function f(h,p){u(this,f),h.search(/^[0-9]{11}$/)!==-1&&(h+=a(h));var m=c(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,h,p));return m.displayValue=p.displayValue,p.fontSize>p.width*10?m.fontSize=p.width*10:m.fontSize=p.fontSize,m.guardHeight=p.height+m.fontSize/2+p.textMargin,m}return e(f,[{key:"valid",value:function(){return this.data.search(/^[0-9]{12}$/)!==-1&&this.data[11]==a(this.data)}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var p="";return p+="101",p+=(0,n.default)(this.data.substr(0,6),"LLLLLL"),p+="01010",p+=(0,n.default)(this.data.substr(6,6),"RRRRRR"),p+="101",{data:p,text:this.text}}},{key:"guardedEncoding",value:function(){var p=[];return this.displayValue&&p.push({data:"00000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),p.push({data:"101"+(0,n.default)(this.data[0],"L"),options:{height:this.guardHeight}}),p.push({data:(0,n.default)(this.data.substr(1,5),"LLLLL"),text:this.text.substr(1,5),options:{fontSize:this.fontSize}}),p.push({data:"01010",options:{height:this.guardHeight}}),p.push({data:(0,n.default)(this.data.substr(6,5),"RRRRR"),text:this.text.substr(6,5),options:{fontSize:this.fontSize}}),p.push({data:(0,n.default)(this.data[11],"R")+"101",options:{height:this.guardHeight}}),this.displayValue&&p.push({data:"00000000",text:this.text.substr(11,1),options:{textAlign:"right",fontSize:this.fontSize}}),p}}]),f}(i.default);function a(d){var f=0,h;for(h=1;h<11;h+=2)f+=parseInt(d[h]);for(h=0;h<11;h+=2)f+=parseInt(d[h])*3;return(10-f%10)%10}return cr.default=s,cr}var Si={},wc;function by(){if(wc)return Si;wc=1,Object.defineProperty(Si,"__esModule",{value:!0});var e=function(){function p(m,_){for(var g=0;g<_.length;g++){var v=_[g];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(m,v.key,v)}}return function(m,_,g){return _&&p(m.prototype,_),g&&p(m,g),m}}(),t=Qr(),n=u(t),r=wt(),i=u(r),o=Yh();function u(p){return p&&p.__esModule?p:{default:p}}function c(p,m){if(!(p instanceof m))throw new TypeError("Cannot call a class as a function")}function l(p,m){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:p}function s(p,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);p.prototype=Object.create(m&&m.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(p,m):p.__proto__=m)}var a=["XX00000XXX","XX10000XXX","XX20000XXX","XXX00000XX","XXXX00000X","XXXXX00005","XXXXX00006","XXXXX00007","XXXXX00008","XXXXX00009"],d=[["EEEOOO","OOOEEE"],["EEOEOO","OOEOEE"],["EEOOEO","OOEEOE"],["EEOOOE","OOEEEO"],["EOEEOO","OEOOEE"],["EOOEEO","OEEOOE"],["EOOOEE","OEEEOO"],["EOEOEO","OEOEOE"],["EOEOOE","OEOEEO"],["EOOEOE","OEEOEO"]],f=function(p){s(m,p);function m(_,g){c(this,m);var v=l(this,(m.__proto__||Object.getPrototypeOf(m)).call(this,_,g));if(v.isValid=!1,_.search(/^[0-9]{6}$/)!==-1)v.middleDigits=_,v.upcA=h(_,"0"),v.text=g.text||""+v.upcA[0]+_+v.upcA[v.upcA.length-1],v.isValid=!0;else if(_.search(/^[01][0-9]{7}$/)!==-1)if(v.middleDigits=_.substring(1,_.length-1),v.upcA=h(v.middleDigits,_[0]),v.upcA[v.upcA.length-1]===_[_.length-1])v.isValid=!0;else return l(v);else return l(v);return v.displayValue=g.displayValue,g.fontSize>g.width*10?v.fontSize=g.width*10:v.fontSize=g.fontSize,v.guardHeight=g.height+v.fontSize/2+g.textMargin,v}return e(m,[{key:"valid",value:function(){return this.isValid}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var g="";return g+="101",g+=this.encodeMiddleDigits(),g+="010101",{data:g,text:this.text}}},{key:"guardedEncoding",value:function(){var g=[];return this.displayValue&&g.push({data:"00000000",text:this.text[0],options:{textAlign:"left",fontSize:this.fontSize}}),g.push({data:"101",options:{height:this.guardHeight}}),g.push({data:this.encodeMiddleDigits(),text:this.text.substring(1,7),options:{fontSize:this.fontSize}}),g.push({data:"010101",options:{height:this.guardHeight}}),this.displayValue&&g.push({data:"00000000",text:this.text[7],options:{textAlign:"right",fontSize:this.fontSize}}),g}},{key:"encodeMiddleDigits",value:function(){var g=this.upcA[0],v=this.upcA[this.upcA.length-1],b=d[parseInt(v)][parseInt(g)];return(0,n.default)(this.middleDigits,b)}}]),m}(i.default);function h(p,m){for(var _=parseInt(p[p.length-1]),g=a[_],v="",b=0,E=0;E<g.length;E++){var D=g[E];D==="X"?v+=p[b++]:v+=D}return v=""+m+v,""+v+(0,o.checksum)(v)}return Si.default=f,Si}var Oc;function Ey(){if(Oc)return tt;Oc=1,Object.defineProperty(tt,"__esModule",{value:!0}),tt.UPCE=tt.UPC=tt.EAN2=tt.EAN5=tt.EAN8=tt.EAN13=void 0;var e=gy(),t=f(e),n=_y(),r=f(n),i=vy(),o=f(i),u=yy(),c=f(u),l=Yh(),s=f(l),a=by(),d=f(a);function f(h){return h&&h.__esModule?h:{default:h}}return tt.EAN13=t.default,tt.EAN8=r.default,tt.EAN5=o.default,tt.EAN2=c.default,tt.UPC=s.default,tt.UPCE=d.default,tt}var An={},Ci={},$n={},Tc;function Ay(){return Tc||(Tc=1,Object.defineProperty($n,"__esModule",{value:!0}),$n.START_BIN="1010",$n.END_BIN="11101",$n.BINARIES=["00110","10001","01001","11000","00101","10100","01100","00011","10010","01010"]),$n}var Sc;function qh(){if(Sc)return Ci;Sc=1,Object.defineProperty(Ci,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=Ay(),n=wt(),r=i(n);function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(){return o(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return e(a,[{key:"valid",value:function(){return this.data.search(/^([0-9]{2})+$/)!==-1}},{key:"encode",value:function(){var f=this,h=this.data.match(/.{2}/g).map(function(p){return f.encodePair(p)}).join("");return{data:t.START_BIN+h+t.END_BIN,text:this.text}}},{key:"encodePair",value:function(f){var h=t.BINARIES[f[1]];return t.BINARIES[f[0]].split("").map(function(p,m){return(p==="1"?"111":"1")+(h[m]==="1"?"000":"0")}).join("")}}]),a}(r.default);return Ci.default=l,Ci}var Ii={},Cc;function wy(){if(Cc)return Ii;Cc=1,Object.defineProperty(Ii,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=qh(),n=r(t);function r(s){return s&&s.__esModule?s:{default:s}}function i(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function o(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function u(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var c=function(a){var d=a.substr(0,13).split("").map(function(f){return parseInt(f,10)}).reduce(function(f,h,p){return f+h*(3-p%2*2)},0);return Math.ceil(d/10)*10-d},l=function(s){u(a,s);function a(d,f){return i(this,a),d.search(/^[0-9]{13}$/)!==-1&&(d+=c(d)),o(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,d,f))}return e(a,[{key:"valid",value:function(){return this.data.search(/^[0-9]{14}$/)!==-1&&+this.data[13]===c(this.data)}}]),a}(n.default);return Ii.default=l,Ii}var Ic;function Oy(){if(Ic)return An;Ic=1,Object.defineProperty(An,"__esModule",{value:!0}),An.ITF14=An.ITF=void 0;var e=qh(),t=i(e),n=wy(),r=i(n);function i(o){return o&&o.__esModule?o:{default:o}}return An.ITF=t.default,An.ITF14=r.default,An}var lt={},Mi={},Mc;function Jr(){if(Mc)return Mi;Mc=1,Object.defineProperty(Mi,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=wt(),n=r(t);function r(s){return s&&s.__esModule?s:{default:s}}function i(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function o(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function u(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var c=function(s){u(a,s);function a(d,f){return i(this,a),o(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,d,f))}return e(a,[{key:"encode",value:function(){for(var f="110",h=0;h<this.data.length;h++){var p=parseInt(this.data[h]),m=p.toString(2);m=l(m,4-m.length);for(var _=0;_<m.length;_++)f+=m[_]=="0"?"100":"110"}return f+="1001",{data:f,text:this.text}}},{key:"valid",value:function(){return this.data.search(/^[0-9]+$/)!==-1}}]),a}(n.default);function l(s,a){for(var d=0;d<a;d++)s="0"+s;return s}return Mi.default=c,Mi}var Li={},fr={},Lc;function Xo(){if(Lc)return fr;Lc=1,Object.defineProperty(fr,"__esModule",{value:!0}),fr.mod10=e,fr.mod11=t;function e(n){for(var r=0,i=0;i<n.length;i++){var o=parseInt(n[i]);(i+n.length)%2===0?r+=o:r+=o*2%10+Math.floor(o*2/10)}return(10-r%10)%10}function t(n){for(var r=0,i=[2,3,4,5,6,7],o=0;o<n.length;o++){var u=parseInt(n[n.length-1-o]);r+=i[o%i.length]*u}return(11-r%11)%11}return fr}var Dc;function Ty(){if(Dc)return Li;Dc=1,Object.defineProperty(Li,"__esModule",{value:!0});var e=Jr(),t=r(e),n=Xo();function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a+(0,n.mod10)(a),d))}return s}(t.default);return Li.default=c,Li}var Di={},Rc;function Sy(){if(Rc)return Di;Rc=1,Object.defineProperty(Di,"__esModule",{value:!0});var e=Jr(),t=r(e),n=Xo();function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a+(0,n.mod11)(a),d))}return s}(t.default);return Di.default=c,Di}var Ri={},kc;function Cy(){if(kc)return Ri;kc=1,Object.defineProperty(Ri,"__esModule",{value:!0});var e=Jr(),t=r(e),n=Xo();function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),a+=(0,n.mod10)(a),a+=(0,n.mod10)(a),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a,d))}return s}(t.default);return Ri.default=c,Ri}var ki={},Nc;function Iy(){if(Nc)return ki;Nc=1,Object.defineProperty(ki,"__esModule",{value:!0});var e=Jr(),t=r(e),n=Xo();function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),a+=(0,n.mod11)(a),a+=(0,n.mod10)(a),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a,d))}return s}(t.default);return ki.default=c,ki}var xc;function My(){if(xc)return lt;xc=1,Object.defineProperty(lt,"__esModule",{value:!0}),lt.MSI1110=lt.MSI1010=lt.MSI11=lt.MSI10=lt.MSI=void 0;var e=Jr(),t=a(e),n=Ty(),r=a(n),i=Sy(),o=a(i),u=Cy(),c=a(u),l=Iy(),s=a(l);function a(d){return d&&d.__esModule?d:{default:d}}return lt.MSI=t.default,lt.MSI10=r.default,lt.MSI11=o.default,lt.MSI1010=c.default,lt.MSI1110=s.default,lt}var dr={},Pc;function Ly(){if(Pc)return dr;Pc=1,Object.defineProperty(dr,"__esModule",{value:!0}),dr.pharmacode=void 0;var e=function(){function l(s,a){for(var d=0;d<a.length;d++){var f=a[d];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(s,f.key,f)}}return function(s,a,d){return a&&l(s.prototype,a),d&&l(s,d),s}}(),t=wt(),n=r(t);function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){i(this,s);var f=o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a,d));return f.number=parseInt(a,10),f}return e(s,[{key:"encode",value:function(){for(var d=this.number,f="";!isNaN(d)&&d!=0;)d%2===0?(f="11100"+f,d=(d-2)/2):(f="100"+f,d=(d-1)/2);return f=f.slice(0,-2),{data:f,text:this.text}}},{key:"valid",value:function(){return this.number>=3&&this.number<=131070}}]),s}(n.default);return dr.pharmacode=c,dr}var hr={},Fc;function Dy(){if(Fc)return hr;Fc=1,Object.defineProperty(hr,"__esModule",{value:!0}),hr.codabar=void 0;var e=function(){function l(s,a){for(var d=0;d<a.length;d++){var f=a[d];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(s,f.key,f)}}return function(s,a,d){return a&&l(s.prototype,a),d&&l(s,d),s}}(),t=wt(),n=r(t);function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){i(this,s),a.search(/^[0-9\-\$\:\.\+\/]+$/)===0&&(a="A"+a+"A");var f=o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a.toUpperCase(),d));return f.text=f.options.text||f.text.replace(/[A-D]/g,""),f}return e(s,[{key:"valid",value:function(){return this.data.search(/^[A-D][0-9\-\$\:\.\+\/]+[A-D]$/)!==-1}},{key:"encode",value:function(){for(var d=[],f=this.getEncodings(),h=0;h<this.data.length;h++)d.push(f[this.data.charAt(h)]),h!==this.data.length-1&&d.push("0");return{text:this.text,data:d.join("")}}},{key:"getEncodings",value:function(){return{0:"101010011",1:"101011001",2:"101001011",3:"110010101",4:"101101001",5:"110101001",6:"100101011",7:"100101101",8:"100110101",9:"110100101","-":"101001101",$:"101100101",":":"1101011011","/":"1101101011",".":"1101101101","+":"1011011011",A:"1011001001",B:"1001001011",C:"1010010011",D:"1010011001"}}}]),s}(n.default);return hr.codabar=c,hr}var mr={},Bc;function Ry(){if(Bc)return mr;Bc=1,Object.defineProperty(mr,"__esModule",{value:!0}),mr.GenericBarcode=void 0;var e=function(){function l(s,a){for(var d=0;d<a.length;d++){var f=a[d];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(s,f.key,f)}}return function(s,a,d){return a&&l(s.prototype,a),d&&l(s,d),s}}(),t=wt(),n=r(t);function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a,d))}return e(s,[{key:"encode",value:function(){return{data:"10101010101010101010101010101010101010101",text:this.text}}},{key:"valid",value:function(){return!0}}]),s}(n.default);return mr.GenericBarcode=c,mr}var $c;function ky(){if($c)return di;$c=1,Object.defineProperty(di,"__esModule",{value:!0});var e=ly(),t=py(),n=Ey(),r=Oy(),i=My(),o=Ly(),u=Dy(),c=Ry();return di.default={CODE39:e.CODE39,CODE128:t.CODE128,CODE128A:t.CODE128A,CODE128B:t.CODE128B,CODE128C:t.CODE128C,EAN13:n.EAN13,EAN8:n.EAN8,EAN5:n.EAN5,EAN2:n.EAN2,UPC:n.UPC,UPCE:n.UPCE,ITF14:r.ITF14,ITF:r.ITF,MSI:i.MSI,MSI10:i.MSI10,MSI11:i.MSI11,MSI1010:i.MSI1010,MSI1110:i.MSI1110,pharmacode:o.pharmacode,codabar:u.codabar,GenericBarcode:c.GenericBarcode},di}var Ni={},Hc;function Ko(){if(Hc)return Ni;Hc=1,Object.defineProperty(Ni,"__esModule",{value:!0});var e=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t};return Ni.default=function(t,n){return e({},t,n)},Ni}var xi={},jc;function Ny(){if(jc)return xi;jc=1,Object.defineProperty(xi,"__esModule",{value:!0}),xi.default=e;function e(t){var n=[];function r(i){if(Array.isArray(i))for(var o=0;o<i.length;o++)r(i[o]);else i.text=i.text||"",i.data=i.data||"",n.push(i)}return r(t),n}return xi}var Pi={},Uc;function xy(){if(Uc)return Pi;Uc=1,Object.defineProperty(Pi,"__esModule",{value:!0}),Pi.default=e;function e(t){return t.marginTop=t.marginTop||t.margin,t.marginBottom=t.marginBottom||t.margin,t.marginRight=t.marginRight||t.margin,t.marginLeft=t.marginLeft||t.margin,t}return Pi}var Fi={},Bi={},$i={},Yc;function Gh(){if(Yc)return $i;Yc=1,Object.defineProperty($i,"__esModule",{value:!0}),$i.default=e;function e(t){var n=["width","height","textMargin","fontSize","margin","marginTop","marginBottom","marginLeft","marginRight"];for(var r in n)n.hasOwnProperty(r)&&(r=n[r],typeof t[r]=="string"&&(t[r]=parseInt(t[r],10)));return typeof t.displayValue=="string"&&(t.displayValue=t.displayValue!="false"),t}return $i}var Hi={},qc;function Vh(){if(qc)return Hi;qc=1,Object.defineProperty(Hi,"__esModule",{value:!0});var e={width:2,height:100,format:"auto",displayValue:!0,fontOptions:"",font:"monospace",text:void 0,textAlign:"center",textPosition:"bottom",textMargin:2,fontSize:20,background:"#ffffff",lineColor:"#000000",margin:10,marginTop:void 0,marginBottom:void 0,marginLeft:void 0,marginRight:void 0,valid:function(){}};return Hi.default=e,Hi}var Gc;function Py(){if(Gc)return Bi;Gc=1,Object.defineProperty(Bi,"__esModule",{value:!0});var e=Gh(),t=i(e),n=Vh(),r=i(n);function i(u){return u&&u.__esModule?u:{default:u}}function o(u){var c={};for(var l in r.default)r.default.hasOwnProperty(l)&&(u.hasAttribute("jsbarcode-"+l.toLowerCase())&&(c[l]=u.getAttribute("jsbarcode-"+l.toLowerCase())),u.hasAttribute("data-"+l.toLowerCase())&&(c[l]=u.getAttribute("data-"+l.toLowerCase())));return c.value=u.getAttribute("jsbarcode-value")||u.getAttribute("data-value"),c=(0,t.default)(c),c}return Bi.default=o,Bi}var ji={},Ui={},ct={},Vc;function Wh(){if(Vc)return ct;Vc=1,Object.defineProperty(ct,"__esModule",{value:!0}),ct.getTotalWidthOfEncodings=ct.calculateEncodingAttributes=ct.getBarcodePadding=ct.getEncodingHeight=ct.getMaximumHeightOfEncodings=void 0;var e=Ko(),t=n(e);function n(s){return s&&s.__esModule?s:{default:s}}function r(s,a){return a.height+(a.displayValue&&s.text.length>0?a.fontSize+a.textMargin:0)+a.marginTop+a.marginBottom}function i(s,a,d){if(d.displayValue&&a<s){if(d.textAlign=="center")return Math.floor((s-a)/2);if(d.textAlign=="left")return 0;if(d.textAlign=="right")return Math.floor(s-a)}return 0}function o(s,a,d){for(var f=0;f<s.length;f++){var h=s[f],p=(0,t.default)(a,h.options),m;p.displayValue?m=l(h.text,p,d):m=0;var _=h.data.length*p.width;h.width=Math.ceil(Math.max(m,_)),h.height=r(h,p),h.barcodePadding=i(m,_,p)}}function u(s){for(var a=0,d=0;d<s.length;d++)a+=s[d].width;return a}function c(s){for(var a=0,d=0;d<s.length;d++)s[d].height>a&&(a=s[d].height);return a}function l(s,a,d){var f;if(d)f=d;else if(typeof document<"u")f=document.createElement("canvas").getContext("2d");else return 0;f.font=a.fontOptions+" "+a.fontSize+"px "+a.font;var h=f.measureText(s);if(!h)return 0;var p=h.width;return p}return ct.getMaximumHeightOfEncodings=c,ct.getEncodingHeight=r,ct.getBarcodePadding=i,ct.calculateEncodingAttributes=o,ct.getTotalWidthOfEncodings=u,ct}var Wc;function Fy(){if(Wc)return Ui;Wc=1,Object.defineProperty(Ui,"__esModule",{value:!0});var e=function(){function c(l,s){for(var a=0;a<s.length;a++){var d=s[a];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(l,d.key,d)}}return function(l,s,a){return s&&c(l.prototype,s),a&&c(l,a),l}}(),t=Ko(),n=i(t),r=Wh();function i(c){return c&&c.__esModule?c:{default:c}}function o(c,l){if(!(c instanceof l))throw new TypeError("Cannot call a class as a function")}var u=function(){function c(l,s,a){o(this,c),this.canvas=l,this.encodings=s,this.options=a}return e(c,[{key:"render",value:function(){if(!this.canvas.getContext)throw new Error("The browser does not support canvas.");this.prepareCanvas();for(var s=0;s<this.encodings.length;s++){var a=(0,n.default)(this.options,this.encodings[s].options);this.drawCanvasBarcode(a,this.encodings[s]),this.drawCanvasText(a,this.encodings[s]),this.moveCanvasDrawing(this.encodings[s])}this.restoreCanvas()}},{key:"prepareCanvas",value:function(){var s=this.canvas.getContext("2d");s.save(),(0,r.calculateEncodingAttributes)(this.encodings,this.options,s);var a=(0,r.getTotalWidthOfEncodings)(this.encodings),d=(0,r.getMaximumHeightOfEncodings)(this.encodings);this.canvas.width=a+this.options.marginLeft+this.options.marginRight,this.canvas.height=d,s.clearRect(0,0,this.canvas.width,this.canvas.height),this.options.background&&(s.fillStyle=this.options.background,s.fillRect(0,0,this.canvas.width,this.canvas.height)),s.translate(this.options.marginLeft,0)}},{key:"drawCanvasBarcode",value:function(s,a){var d=this.canvas.getContext("2d"),f=a.data,h;s.textPosition=="top"?h=s.marginTop+s.fontSize+s.textMargin:h=s.marginTop,d.fillStyle=s.lineColor;for(var p=0;p<f.length;p++){var m=p*s.width+a.barcodePadding;f[p]==="1"?d.fillRect(m,h,s.width,s.height):f[p]&&d.fillRect(m,h,s.width,s.height*f[p])}}},{key:"drawCanvasText",value:function(s,a){var d=this.canvas.getContext("2d"),f=s.fontOptions+" "+s.fontSize+"px "+s.font;if(s.displayValue){var h,p;s.textPosition=="top"?p=s.marginTop+s.fontSize-s.textMargin:p=s.height+s.textMargin+s.marginTop+s.fontSize,d.font=f,s.textAlign=="left"||a.barcodePadding>0?(h=0,d.textAlign="left"):s.textAlign=="right"?(h=a.width-1,d.textAlign="right"):(h=a.width/2,d.textAlign="center"),d.fillText(a.text,h,p)}}},{key:"moveCanvasDrawing",value:function(s){var a=this.canvas.getContext("2d");a.translate(s.width,0)}},{key:"restoreCanvas",value:function(){var s=this.canvas.getContext("2d");s.restore()}}]),c}();return Ui.default=u,Ui}var Yi={},zc;function By(){if(zc)return Yi;zc=1,Object.defineProperty(Yi,"__esModule",{value:!0});var e=function(){function l(s,a){for(var d=0;d<a.length;d++){var f=a[d];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(s,f.key,f)}}return function(s,a,d){return a&&l(s.prototype,a),d&&l(s,d),s}}(),t=Ko(),n=i(t),r=Wh();function i(l){return l&&l.__esModule?l:{default:l}}function o(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}var u="http://www.w3.org/2000/svg",c=function(){function l(s,a,d){o(this,l),this.svg=s,this.encodings=a,this.options=d,this.document=d.xmlDocument||document}return e(l,[{key:"render",value:function(){var a=this.options.marginLeft;this.prepareSVG();for(var d=0;d<this.encodings.length;d++){var f=this.encodings[d],h=(0,n.default)(this.options,f.options),p=this.createGroup(a,h.marginTop,this.svg);this.setGroupOptions(p,h),this.drawSvgBarcode(p,h,f),this.drawSVGText(p,h,f),a+=f.width}}},{key:"prepareSVG",value:function(){for(;this.svg.firstChild;)this.svg.removeChild(this.svg.firstChild);(0,r.calculateEncodingAttributes)(this.encodings,this.options);var a=(0,r.getTotalWidthOfEncodings)(this.encodings),d=(0,r.getMaximumHeightOfEncodings)(this.encodings),f=a+this.options.marginLeft+this.options.marginRight;this.setSvgAttributes(f,d),this.options.background&&this.drawRect(0,0,f,d,this.svg).setAttribute("style","fill:"+this.options.background+";")}},{key:"drawSvgBarcode",value:function(a,d,f){var h=f.data,p;d.textPosition=="top"?p=d.fontSize+d.textMargin:p=0;for(var m=0,_=0,g=0;g<h.length;g++)_=g*d.width+f.barcodePadding,h[g]==="1"?m++:m>0&&(this.drawRect(_-d.width*m,p,d.width*m,d.height,a),m=0);m>0&&this.drawRect(_-d.width*(m-1),p,d.width*m,d.height,a)}},{key:"drawSVGText",value:function(a,d,f){var h=this.document.createElementNS(u,"text");if(d.displayValue){var p,m;h.setAttribute("style","font:"+d.fontOptions+" "+d.fontSize+"px "+d.font),d.textPosition=="top"?m=d.fontSize-d.textMargin:m=d.height+d.textMargin+d.fontSize,d.textAlign=="left"||f.barcodePadding>0?(p=0,h.setAttribute("text-anchor","start")):d.textAlign=="right"?(p=f.width-1,h.setAttribute("text-anchor","end")):(p=f.width/2,h.setAttribute("text-anchor","middle")),h.setAttribute("x",p),h.setAttribute("y",m),h.appendChild(this.document.createTextNode(f.text)),a.appendChild(h)}}},{key:"setSvgAttributes",value:function(a,d){var f=this.svg;f.setAttribute("width",a+"px"),f.setAttribute("height",d+"px"),f.setAttribute("x","0px"),f.setAttribute("y","0px"),f.setAttribute("viewBox","0 0 "+a+" "+d),f.setAttribute("xmlns",u),f.setAttribute("version","1.1"),f.setAttribute("style","transform: translate(0,0)")}},{key:"createGroup",value:function(a,d,f){var h=this.document.createElementNS(u,"g");return h.setAttribute("transform","translate("+a+", "+d+")"),f.appendChild(h),h}},{key:"setGroupOptions",value:function(a,d){a.setAttribute("style","fill:"+d.lineColor+";")}},{key:"drawRect",value:function(a,d,f,h,p){var m=this.document.createElementNS(u,"rect");return m.setAttribute("x",a),m.setAttribute("y",d),m.setAttribute("width",f),m.setAttribute("height",h),p.appendChild(m),m}}]),l}();return Yi.default=c,Yi}var qi={},Xc;function $y(){if(Xc)return qi;Xc=1,Object.defineProperty(qi,"__esModule",{value:!0});var e=function(){function r(i,o){for(var u=0;u<o.length;u++){var c=o[u];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(i,c.key,c)}}return function(i,o,u){return o&&r(i.prototype,o),u&&r(i,u),i}}();function t(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}var n=function(){function r(i,o,u){t(this,r),this.object=i,this.encodings=o,this.options=u}return e(r,[{key:"render",value:function(){this.object.encodings=this.encodings}}]),r}();return qi.default=n,qi}var Kc;function Hy(){if(Kc)return ji;Kc=1,Object.defineProperty(ji,"__esModule",{value:!0});var e=Fy(),t=u(e),n=By(),r=u(n),i=$y(),o=u(i);function u(c){return c&&c.__esModule?c:{default:c}}return ji.default={CanvasRenderer:t.default,SVGRenderer:r.default,ObjectRenderer:o.default},ji}var Hn={},Qc;function zh(){if(Qc)return Hn;Qc=1,Object.defineProperty(Hn,"__esModule",{value:!0});function e(u,c){if(!(u instanceof c))throw new TypeError("Cannot call a class as a function")}function t(u,c){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return c&&(typeof c=="object"||typeof c=="function")?c:u}function n(u,c){if(typeof c!="function"&&c!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof c);u.prototype=Object.create(c&&c.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),c&&(Object.setPrototypeOf?Object.setPrototypeOf(u,c):u.__proto__=c)}var r=function(u){n(c,u);function c(l,s){e(this,c);var a=t(this,(c.__proto__||Object.getPrototypeOf(c)).call(this));return a.name="InvalidInputException",a.symbology=l,a.input=s,a.message='"'+a.input+'" is not a valid input for '+a.symbology,a}return c}(Error),i=function(u){n(c,u);function c(){e(this,c);var l=t(this,(c.__proto__||Object.getPrototypeOf(c)).call(this));return l.name="InvalidElementException",l.message="Not supported type to render on",l}return c}(Error),o=function(u){n(c,u);function c(){e(this,c);var l=t(this,(c.__proto__||Object.getPrototypeOf(c)).call(this));return l.name="NoElementException",l.message="No element to render on.",l}return c}(Error);return Hn.InvalidInputException=r,Hn.InvalidElementException=i,Hn.NoElementException=o,Hn}var Jc;function jy(){if(Jc)return Fi;Jc=1,Object.defineProperty(Fi,"__esModule",{value:!0});var e=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},t=Py(),n=u(t),r=Hy(),i=u(r),o=zh();function u(a){return a&&a.__esModule?a:{default:a}}function c(a){if(typeof a=="string")return l(a);if(Array.isArray(a)){for(var d=[],f=0;f<a.length;f++)d.push(c(a[f]));return d}else{if(typeof HTMLCanvasElement<"u"&&a instanceof HTMLImageElement)return s(a);if(a&&a.nodeName&&a.nodeName.toLowerCase()==="svg"||typeof SVGElement<"u"&&a instanceof SVGElement)return{element:a,options:(0,n.default)(a),renderer:i.default.SVGRenderer};if(typeof HTMLCanvasElement<"u"&&a instanceof HTMLCanvasElement)return{element:a,options:(0,n.default)(a),renderer:i.default.CanvasRenderer};if(a&&a.getContext)return{element:a,renderer:i.default.CanvasRenderer};if(a&&(typeof a>"u"?"undefined":e(a))==="object"&&!a.nodeName)return{element:a,renderer:i.default.ObjectRenderer};throw new o.InvalidElementException}}function l(a){var d=document.querySelectorAll(a);if(d.length!==0){for(var f=[],h=0;h<d.length;h++)f.push(c(d[h]));return f}}function s(a){var d=document.createElement("canvas");return{element:d,options:(0,n.default)(a),renderer:i.default.CanvasRenderer,afterRender:function(){a.setAttribute("src",d.toDataURL())}}}return Fi.default=c,Fi}var Gi={},Zc;function Uy(){if(Zc)return Gi;Zc=1,Object.defineProperty(Gi,"__esModule",{value:!0});var e=function(){function r(i,o){for(var u=0;u<o.length;u++){var c=o[u];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(i,c.key,c)}}return function(i,o,u){return o&&r(i.prototype,o),u&&r(i,u),i}}();function t(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}var n=function(){function r(i){t(this,r),this.api=i}return e(r,[{key:"handleCatch",value:function(o){if(o.name==="InvalidInputException")if(this.api._options.valid!==this.api._defaults.valid)this.api._options.valid(!1);else throw o.message;else throw o;this.api.render=function(){}}},{key:"wrapBarcodeCall",value:function(o){try{var u=o.apply(void 0,arguments);return this.api._options.valid(!0),u}catch(c){return this.handleCatch(c),this.api}}}]),r}();return Gi.default=n,Gi}var vs,ef;function Yy(){if(ef)return vs;ef=1;var e=ky(),t=g(e),n=Ko(),r=g(n),i=Ny(),o=g(i),u=xy(),c=g(u),l=jy(),s=g(l),a=Gh(),d=g(a),f=Uy(),h=g(f),p=zh(),m=Vh(),_=g(m);function g(T){return T&&T.__esModule?T:{default:T}}var v=function(){},b=function(k,R,L){var B=new v;if(typeof k>"u")throw Error("No element to render on was provided.");return B._renderProperties=(0,s.default)(k),B._encodings=[],B._options=_.default,B._errorHandler=new h.default(B),typeof R<"u"&&(L=L||{},L.format||(L.format=O()),B.options(L)[L.format](R,L).render()),B};b.getModule=function(T){return t.default[T]};for(var E in t.default)t.default.hasOwnProperty(E)&&D(t.default,E);function D(T,k){v.prototype[k]=v.prototype[k.toUpperCase()]=v.prototype[k.toLowerCase()]=function(R,L){var B=this;return B._errorHandler.wrapBarcodeCall(function(){L.text=typeof L.text>"u"?void 0:""+L.text;var N=(0,r.default)(B._options,L);N=(0,d.default)(N);var G=T[k],Y=I(R,G,N);return B._encodings.push(Y),B})}}function I(T,k,R){T=""+T;var L=new k(T,R);if(!L.valid())throw new p.InvalidInputException(L.constructor.name,T);var B=L.encode();B=(0,o.default)(B);for(var N=0;N<B.length;N++)B[N].options=(0,r.default)(R,B[N].options);return B}function O(){return t.default.CODE128?"CODE128":Object.keys(t.default)[0]}v.prototype.options=function(T){return this._options=(0,r.default)(this._options,T),this},v.prototype.blank=function(T){var k=new Array(T+1).join("0");return this._encodings.push({data:k}),this},v.prototype.init=function(){if(this._renderProperties){Array.isArray(this._renderProperties)||(this._renderProperties=[this._renderProperties]);var T;for(var k in this._renderProperties){T=this._renderProperties[k];var R=(0,r.default)(this._options,T.options);R.format=="auto"&&(R.format=O()),this._errorHandler.wrapBarcodeCall(function(){var L=R.value,B=t.default[R.format.toUpperCase()],N=I(L,B,R);M(T,N,R)})}}},v.prototype.render=function(){if(!this._renderProperties)throw new p.NoElementException;if(Array.isArray(this._renderProperties))for(var T=0;T<this._renderProperties.length;T++)M(this._renderProperties[T],this._encodings,this._options);else M(this._renderProperties,this._encodings,this._options);return this},v.prototype._defaults=_.default;function M(T,k,R){k=(0,o.default)(k);for(var L=0;L<k.length;L++)k[L].options=(0,r.default)(R,k[L].options),(0,c.default)(k[L].options);(0,c.default)(R);var B=T.renderer,N=new B(T.element,k,R);N.render(),T.afterRender&&T.afterRender()}return typeof window<"u"&&(window.JsBarcode=b),typeof jQuery<"u"&&(jQuery.fn.JsBarcode=function(T,k){var R=[];return jQuery(this).each(function(){R.push(this)}),b(R,T,k)}),vs=b,vs}var qy=Yy();const Gy=Rf(qy);var jn={},ys,tf;function Vy(){return tf||(tf=1,ys=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),ys}var bs={},tn={},nf;function kn(){if(nf)return tn;nf=1;let e;const t=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return tn.getSymbolSize=function(r){if(!r)throw new Error('"version" cannot be null or undefined');if(r<1||r>40)throw new Error('"version" should be in range from 1 to 40');return r*4+17},tn.getSymbolTotalCodewords=function(r){return t[r]},tn.getBCHDigit=function(n){let r=0;for(;n!==0;)r++,n>>>=1;return r},tn.setToSJISFunction=function(r){if(typeof r!="function")throw new Error('"toSJISFunc" is not a valid function.');e=r},tn.isKanjiModeEnabled=function(){return typeof e<"u"},tn.toSJIS=function(r){return e(r)},tn}var Es={},rf;function Wa(){return rf||(rf=1,function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2};function t(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+n)}}e.isValid=function(r){return r&&typeof r.bit<"u"&&r.bit>=0&&r.bit<4},e.from=function(r,i){if(e.isValid(r))return r;try{return t(r)}catch(o){return i}}}(Es)),Es}var As,of;function Wy(){if(of)return As;of=1;function e(){this.buffer=[],this.length=0}return e.prototype={get:function(t){const n=Math.floor(t/8);return(this.buffer[n]>>>7-t%8&1)===1},put:function(t,n){for(let r=0;r<n;r++)this.putBit((t>>>n-r-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(t){const n=Math.floor(this.length/8);this.buffer.length<=n&&this.buffer.push(0),t&&(this.buffer[n]|=128>>>this.length%8),this.length++}},As=e,As}var ws,sf;function zy(){if(sf)return ws;sf=1;function e(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}return e.prototype.set=function(t,n,r,i){const o=t*this.size+n;this.data[o]=r,i&&(this.reservedBit[o]=!0)},e.prototype.get=function(t,n){return this.data[t*this.size+n]},e.prototype.xor=function(t,n,r){this.data[t*this.size+n]^=r},e.prototype.isReserved=function(t,n){return this.reservedBit[t*this.size+n]},ws=e,ws}var Os={},af;function Xy(){return af||(af=1,function(e){const t=kn().getSymbolSize;e.getRowColCoords=function(r){if(r===1)return[];const i=Math.floor(r/7)+2,o=t(r),u=o===145?26:Math.ceil((o-13)/(2*i-2))*2,c=[o-7];for(let l=1;l<i-1;l++)c[l]=c[l-1]-u;return c.push(6),c.reverse()},e.getPositions=function(r){const i=[],o=e.getRowColCoords(r),u=o.length;for(let c=0;c<u;c++)for(let l=0;l<u;l++)c===0&&l===0||c===0&&l===u-1||c===u-1&&l===0||i.push([o[c],o[l]]);return i}}(Os)),Os}var Ts={},uf;function Ky(){if(uf)return Ts;uf=1;const e=kn().getSymbolSize,t=7;return Ts.getPositions=function(r){const i=e(r);return[[0,0],[i-t,0],[0,i-t]]},Ts}var Ss={},lf;function Qy(){return lf||(lf=1,function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};e.isValid=function(i){return i!=null&&i!==""&&!isNaN(i)&&i>=0&&i<=7},e.from=function(i){return e.isValid(i)?parseInt(i,10):void 0},e.getPenaltyN1=function(i){const o=i.size;let u=0,c=0,l=0,s=null,a=null;for(let d=0;d<o;d++){c=l=0,s=a=null;for(let f=0;f<o;f++){let h=i.get(d,f);h===s?c++:(c>=5&&(u+=t.N1+(c-5)),s=h,c=1),h=i.get(f,d),h===a?l++:(l>=5&&(u+=t.N1+(l-5)),a=h,l=1)}c>=5&&(u+=t.N1+(c-5)),l>=5&&(u+=t.N1+(l-5))}return u},e.getPenaltyN2=function(i){const o=i.size;let u=0;for(let c=0;c<o-1;c++)for(let l=0;l<o-1;l++){const s=i.get(c,l)+i.get(c,l+1)+i.get(c+1,l)+i.get(c+1,l+1);(s===4||s===0)&&u++}return u*t.N2},e.getPenaltyN3=function(i){const o=i.size;let u=0,c=0,l=0;for(let s=0;s<o;s++){c=l=0;for(let a=0;a<o;a++)c=c<<1&2047|i.get(s,a),a>=10&&(c===1488||c===93)&&u++,l=l<<1&2047|i.get(a,s),a>=10&&(l===1488||l===93)&&u++}return u*t.N3},e.getPenaltyN4=function(i){let o=0;const u=i.data.length;for(let l=0;l<u;l++)o+=i.data[l];return Math.abs(Math.ceil(o*100/u/5)-10)*t.N4};function n(r,i,o){switch(r){case e.Patterns.PATTERN000:return(i+o)%2===0;case e.Patterns.PATTERN001:return i%2===0;case e.Patterns.PATTERN010:return o%3===0;case e.Patterns.PATTERN011:return(i+o)%3===0;case e.Patterns.PATTERN100:return(Math.floor(i/2)+Math.floor(o/3))%2===0;case e.Patterns.PATTERN101:return i*o%2+i*o%3===0;case e.Patterns.PATTERN110:return(i*o%2+i*o%3)%2===0;case e.Patterns.PATTERN111:return(i*o%3+(i+o)%2)%2===0;default:throw new Error("bad maskPattern:"+r)}}e.applyMask=function(i,o){const u=o.size;for(let c=0;c<u;c++)for(let l=0;l<u;l++)o.isReserved(l,c)||o.xor(l,c,n(i,l,c))},e.getBestMask=function(i,o){const u=Object.keys(e.Patterns).length;let c=0,l=1/0;for(let s=0;s<u;s++){o(s),e.applyMask(s,i);const a=e.getPenaltyN1(i)+e.getPenaltyN2(i)+e.getPenaltyN3(i)+e.getPenaltyN4(i);e.applyMask(s,i),a<l&&(l=a,c=s)}return c}}(Ss)),Ss}var Vi={},cf;function Xh(){if(cf)return Vi;cf=1;const e=Wa(),t=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],n=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return Vi.getBlocksCount=function(i,o){switch(o){case e.L:return t[(i-1)*4+0];case e.M:return t[(i-1)*4+1];case e.Q:return t[(i-1)*4+2];case e.H:return t[(i-1)*4+3];default:return}},Vi.getTotalCodewordsCount=function(i,o){switch(o){case e.L:return n[(i-1)*4+0];case e.M:return n[(i-1)*4+1];case e.Q:return n[(i-1)*4+2];case e.H:return n[(i-1)*4+3];default:return}},Vi}var Cs={},pr={},ff;function Jy(){if(ff)return pr;ff=1;const e=new Uint8Array(512),t=new Uint8Array(256);return function(){let r=1;for(let i=0;i<255;i++)e[i]=r,t[r]=i,r<<=1,r&256&&(r^=285);for(let i=255;i<512;i++)e[i]=e[i-255]}(),pr.log=function(r){if(r<1)throw new Error("log("+r+")");return t[r]},pr.exp=function(r){return e[r]},pr.mul=function(r,i){return r===0||i===0?0:e[t[r]+t[i]]},pr}var df;function Zy(){return df||(df=1,function(e){const t=Jy();e.mul=function(r,i){const o=new Uint8Array(r.length+i.length-1);for(let u=0;u<r.length;u++)for(let c=0;c<i.length;c++)o[u+c]^=t.mul(r[u],i[c]);return o},e.mod=function(r,i){let o=new Uint8Array(r);for(;o.length-i.length>=0;){const u=o[0];for(let l=0;l<i.length;l++)o[l]^=t.mul(i[l],u);let c=0;for(;c<o.length&&o[c]===0;)c++;o=o.slice(c)}return o},e.generateECPolynomial=function(r){let i=new Uint8Array([1]);for(let o=0;o<r;o++)i=e.mul(i,new Uint8Array([1,t.exp(o)]));return i}}(Cs)),Cs}var Is,hf;function eb(){if(hf)return Is;hf=1;const e=Zy();function t(n){this.genPoly=void 0,this.degree=n,this.degree&&this.initialize(this.degree)}return t.prototype.initialize=function(r){this.degree=r,this.genPoly=e.generateECPolynomial(this.degree)},t.prototype.encode=function(r){if(!this.genPoly)throw new Error("Encoder not initialized");const i=new Uint8Array(r.length+this.degree);i.set(r);const o=e.mod(i,this.genPoly),u=this.degree-o.length;if(u>0){const c=new Uint8Array(this.degree);return c.set(o,u),c}return o},Is=t,Is}var Ms={},Ls={},Ds={},mf;function Kh(){return mf||(mf=1,Ds.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}),Ds}var Nt={},pf;function Qh(){if(pf)return Nt;pf=1;const e="[0-9]+",t="[A-Z $%*+\\-./:]+";let n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";n=n.replace(/u/g,"\\u");const r="(?:(?![A-Z0-9 $%*+\\-./:]|"+n+")(?:.|[\r\n]))+";Nt.KANJI=new RegExp(n,"g"),Nt.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),Nt.BYTE=new RegExp(r,"g"),Nt.NUMERIC=new RegExp(e,"g"),Nt.ALPHANUMERIC=new RegExp(t,"g");const i=new RegExp("^"+n+"$"),o=new RegExp("^"+e+"$"),u=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return Nt.testKanji=function(l){return i.test(l)},Nt.testNumeric=function(l){return o.test(l)},Nt.testAlphanumeric=function(l){return u.test(l)},Nt}var gf;function Nn(){return gf||(gf=1,function(e){const t=Kh(),n=Qh();e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(o,u){if(!o.ccBits)throw new Error("Invalid mode: "+o);if(!t.isValid(u))throw new Error("Invalid version: "+u);return u>=1&&u<10?o.ccBits[0]:u<27?o.ccBits[1]:o.ccBits[2]},e.getBestModeForData=function(o){return n.testNumeric(o)?e.NUMERIC:n.testAlphanumeric(o)?e.ALPHANUMERIC:n.testKanji(o)?e.KANJI:e.BYTE},e.toString=function(o){if(o&&o.id)return o.id;throw new Error("Invalid mode")},e.isValid=function(o){return o&&o.bit&&o.ccBits};function r(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+i)}}e.from=function(o,u){if(e.isValid(o))return o;try{return r(o)}catch(c){return u}}}(Ls)),Ls}var _f;function tb(){return _f||(_f=1,function(e){const t=kn(),n=Xh(),r=Wa(),i=Nn(),o=Kh(),u=7973,c=t.getBCHDigit(u);function l(f,h,p){for(let m=1;m<=40;m++)if(h<=e.getCapacity(m,p,f))return m}function s(f,h){return i.getCharCountIndicator(f,h)+4}function a(f,h){let p=0;return f.forEach(function(m){const _=s(m.mode,h);p+=_+m.getBitsLength()}),p}function d(f,h){for(let p=1;p<=40;p++)if(a(f,p)<=e.getCapacity(p,h,i.MIXED))return p}e.from=function(h,p){return o.isValid(h)?parseInt(h,10):p},e.getCapacity=function(h,p,m){if(!o.isValid(h))throw new Error("Invalid QR Code version");typeof m>"u"&&(m=i.BYTE);const _=t.getSymbolTotalCodewords(h),g=n.getTotalCodewordsCount(h,p),v=(_-g)*8;if(m===i.MIXED)return v;const b=v-s(m,h);switch(m){case i.NUMERIC:return Math.floor(b/10*3);case i.ALPHANUMERIC:return Math.floor(b/11*2);case i.KANJI:return Math.floor(b/13);case i.BYTE:default:return Math.floor(b/8)}},e.getBestVersionForData=function(h,p){let m;const _=r.from(p,r.M);if(Array.isArray(h)){if(h.length>1)return d(h,_);if(h.length===0)return 1;m=h[0]}else m=h;return l(m.mode,m.getLength(),_)},e.getEncodedBits=function(h){if(!o.isValid(h)||h<7)throw new Error("Invalid QR Code version");let p=h<<12;for(;t.getBCHDigit(p)-c>=0;)p^=u<<t.getBCHDigit(p)-c;return h<<12|p}}(Ms)),Ms}var Rs={},vf;function nb(){if(vf)return Rs;vf=1;const e=kn(),t=1335,n=21522,r=e.getBCHDigit(t);return Rs.getEncodedBits=function(o,u){const c=o.bit<<3|u;let l=c<<10;for(;e.getBCHDigit(l)-r>=0;)l^=t<<e.getBCHDigit(l)-r;return(c<<10|l)^n},Rs}var ks={},Ns,yf;function rb(){if(yf)return Ns;yf=1;const e=Nn();function t(n){this.mode=e.NUMERIC,this.data=n.toString()}return t.getBitsLength=function(r){return 10*Math.floor(r/3)+(r%3?r%3*3+1:0)},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(r){let i,o,u;for(i=0;i+3<=this.data.length;i+=3)o=this.data.substr(i,3),u=parseInt(o,10),r.put(u,10);const c=this.data.length-i;c>0&&(o=this.data.substr(i),u=parseInt(o,10),r.put(u,c*3+1))},Ns=t,Ns}var xs,bf;function ib(){if(bf)return xs;bf=1;const e=Nn(),t=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function n(r){this.mode=e.ALPHANUMERIC,this.data=r}return n.getBitsLength=function(i){return 11*Math.floor(i/2)+6*(i%2)},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(i){let o;for(o=0;o+2<=this.data.length;o+=2){let u=t.indexOf(this.data[o])*45;u+=t.indexOf(this.data[o+1]),i.put(u,11)}this.data.length%2&&i.put(t.indexOf(this.data[o]),6)},xs=n,xs}var Ps,Ef;function ob(){if(Ef)return Ps;Ef=1;const e=Nn();function t(n){this.mode=e.BYTE,typeof n=="string"?this.data=new TextEncoder().encode(n):this.data=new Uint8Array(n)}return t.getBitsLength=function(r){return r*8},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(n){for(let r=0,i=this.data.length;r<i;r++)n.put(this.data[r],8)},Ps=t,Ps}var Fs,Af;function sb(){if(Af)return Fs;Af=1;const e=Nn(),t=kn();function n(r){this.mode=e.KANJI,this.data=r}return n.getBitsLength=function(i){return i*13},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(r){let i;for(i=0;i<this.data.length;i++){let o=t.toSJIS(this.data[i]);if(o>=33088&&o<=40956)o-=33088;else if(o>=57408&&o<=60351)o-=49472;else throw new Error("Invalid SJIS character: "+this.data[i]+"\nMake sure your charset is UTF-8");o=(o>>>8&255)*192+(o&255),r.put(o,13)}},Fs=n,Fs}var Bs={exports:{}},wf;function ab(){return wf||(wf=1,function(e){var t={single_source_shortest_paths:function(n,r,i){var o={},u={};u[r]=0;var c=t.PriorityQueue.make();c.push(r,0);for(var l,s,a,d,f,h,p,m,_;!c.empty();){l=c.pop(),s=l.value,d=l.cost,f=n[s]||{};for(a in f)f.hasOwnProperty(a)&&(h=f[a],p=d+h,m=u[a],_=typeof u[a]>"u",(_||m>p)&&(u[a]=p,c.push(a,p),o[a]=s))}if(typeof i<"u"&&typeof u[i]>"u"){var g=["Could not find a path from ",r," to ",i,"."].join("");throw new Error(g)}return o},extract_shortest_path_from_predecessor_list:function(n,r){for(var i=[],o=r;o;)i.push(o),n[o],o=n[o];return i.reverse(),i},find_path:function(n,r,i){var o=t.single_source_shortest_paths(n,r,i);return t.extract_shortest_path_from_predecessor_list(o,i)},PriorityQueue:{make:function(n){var r=t.PriorityQueue,i={},o;n=n||{};for(o in r)r.hasOwnProperty(o)&&(i[o]=r[o]);return i.queue=[],i.sorter=n.sorter||r.default_sorter,i},default_sorter:function(n,r){return n.cost-r.cost},push:function(n,r){var i={value:n,cost:r};this.queue.push(i),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};e.exports=t}(Bs)),Bs.exports}var Of;function ub(){return Of||(Of=1,function(e){const t=Nn(),n=rb(),r=ib(),i=ob(),o=sb(),u=Qh(),c=kn(),l=ab();function s(g){return unescape(encodeURIComponent(g)).length}function a(g,v,b){const E=[];let D;for(;(D=g.exec(b))!==null;)E.push({data:D[0],index:D.index,mode:v,length:D[0].length});return E}function d(g){const v=a(u.NUMERIC,t.NUMERIC,g),b=a(u.ALPHANUMERIC,t.ALPHANUMERIC,g);let E,D;return c.isKanjiModeEnabled()?(E=a(u.BYTE,t.BYTE,g),D=a(u.KANJI,t.KANJI,g)):(E=a(u.BYTE_KANJI,t.BYTE,g),D=[]),v.concat(b,E,D).sort(function(O,M){return O.index-M.index}).map(function(O){return{data:O.data,mode:O.mode,length:O.length}})}function f(g,v){switch(v){case t.NUMERIC:return n.getBitsLength(g);case t.ALPHANUMERIC:return r.getBitsLength(g);case t.KANJI:return o.getBitsLength(g);case t.BYTE:return i.getBitsLength(g)}}function h(g){return g.reduce(function(v,b){const E=v.length-1>=0?v[v.length-1]:null;return E&&E.mode===b.mode?(v[v.length-1].data+=b.data,v):(v.push(b),v)},[])}function p(g){const v=[];for(let b=0;b<g.length;b++){const E=g[b];switch(E.mode){case t.NUMERIC:v.push([E,{data:E.data,mode:t.ALPHANUMERIC,length:E.length},{data:E.data,mode:t.BYTE,length:E.length}]);break;case t.ALPHANUMERIC:v.push([E,{data:E.data,mode:t.BYTE,length:E.length}]);break;case t.KANJI:v.push([E,{data:E.data,mode:t.BYTE,length:s(E.data)}]);break;case t.BYTE:v.push([{data:E.data,mode:t.BYTE,length:s(E.data)}])}}return v}function m(g,v){const b={},E={start:{}};let D=["start"];for(let I=0;I<g.length;I++){const O=g[I],M=[];for(let T=0;T<O.length;T++){const k=O[T],R=""+I+T;M.push(R),b[R]={node:k,lastCount:0},E[R]={};for(let L=0;L<D.length;L++){const B=D[L];b[B]&&b[B].node.mode===k.mode?(E[B][R]=f(b[B].lastCount+k.length,k.mode)-f(b[B].lastCount,k.mode),b[B].lastCount+=k.length):(b[B]&&(b[B].lastCount=k.length),E[B][R]=f(k.length,k.mode)+4+t.getCharCountIndicator(k.mode,v))}}D=M}for(let I=0;I<D.length;I++)E[D[I]].end=0;return{map:E,table:b}}function _(g,v){let b;const E=t.getBestModeForData(g);if(b=t.from(v,E),b!==t.BYTE&&b.bit<E.bit)throw new Error('"'+g+'" cannot be encoded with mode '+t.toString(b)+".\n Suggested mode is: "+t.toString(E));switch(b===t.KANJI&&!c.isKanjiModeEnabled()&&(b=t.BYTE),b){case t.NUMERIC:return new n(g);case t.ALPHANUMERIC:return new r(g);case t.KANJI:return new o(g);case t.BYTE:return new i(g)}}e.fromArray=function(v){return v.reduce(function(b,E){return typeof E=="string"?b.push(_(E,null)):E.data&&b.push(_(E.data,E.mode)),b},[])},e.fromString=function(v,b){const E=d(v,c.isKanjiModeEnabled()),D=p(E),I=m(D,b),O=l.find_path(I.map,"start","end"),M=[];for(let T=1;T<O.length-1;T++)M.push(I.table[O[T]].node);return e.fromArray(h(M))},e.rawSplit=function(v){return e.fromArray(d(v,c.isKanjiModeEnabled()))}}(ks)),ks}var Tf;function lb(){if(Tf)return bs;Tf=1;const e=kn(),t=Wa(),n=Wy(),r=zy(),i=Xy(),o=Ky(),u=Qy(),c=Xh(),l=eb(),s=tb(),a=nb(),d=Nn(),f=ub();function h(I,O){const M=I.size,T=o.getPositions(O);for(let k=0;k<T.length;k++){const R=T[k][0],L=T[k][1];for(let B=-1;B<=7;B++)if(!(R+B<=-1||M<=R+B))for(let N=-1;N<=7;N++)L+N<=-1||M<=L+N||(B>=0&&B<=6&&(N===0||N===6)||N>=0&&N<=6&&(B===0||B===6)||B>=2&&B<=4&&N>=2&&N<=4?I.set(R+B,L+N,!0,!0):I.set(R+B,L+N,!1,!0))}}function p(I){const O=I.size;for(let M=8;M<O-8;M++){const T=M%2===0;I.set(M,6,T,!0),I.set(6,M,T,!0)}}function m(I,O){const M=i.getPositions(O);for(let T=0;T<M.length;T++){const k=M[T][0],R=M[T][1];for(let L=-2;L<=2;L++)for(let B=-2;B<=2;B++)L===-2||L===2||B===-2||B===2||L===0&&B===0?I.set(k+L,R+B,!0,!0):I.set(k+L,R+B,!1,!0)}}function _(I,O){const M=I.size,T=s.getEncodedBits(O);let k,R,L;for(let B=0;B<18;B++)k=Math.floor(B/3),R=B%3+M-8-3,L=(T>>B&1)===1,I.set(k,R,L,!0),I.set(R,k,L,!0)}function g(I,O,M){const T=I.size,k=a.getEncodedBits(O,M);let R,L;for(R=0;R<15;R++)L=(k>>R&1)===1,R<6?I.set(R,8,L,!0):R<8?I.set(R+1,8,L,!0):I.set(T-15+R,8,L,!0),R<8?I.set(8,T-R-1,L,!0):R<9?I.set(8,15-R-1+1,L,!0):I.set(8,15-R-1,L,!0);I.set(T-8,8,1,!0)}function v(I,O){const M=I.size;let T=-1,k=M-1,R=7,L=0;for(let B=M-1;B>0;B-=2)for(B===6&&B--;;){for(let N=0;N<2;N++)if(!I.isReserved(k,B-N)){let G=!1;L<O.length&&(G=(O[L]>>>R&1)===1),I.set(k,B-N,G),R--,R===-1&&(L++,R=7)}if(k+=T,k<0||M<=k){k-=T,T=-T;break}}}function b(I,O,M){const T=new n;M.forEach(function(N){T.put(N.mode.bit,4),T.put(N.getLength(),d.getCharCountIndicator(N.mode,I)),N.write(T)});const k=e.getSymbolTotalCodewords(I),R=c.getTotalCodewordsCount(I,O),L=(k-R)*8;for(T.getLengthInBits()+4<=L&&T.put(0,4);T.getLengthInBits()%8!==0;)T.putBit(0);const B=(L-T.getLengthInBits())/8;for(let N=0;N<B;N++)T.put(N%2?17:236,8);return E(T,I,O)}function E(I,O,M){const T=e.getSymbolTotalCodewords(O),k=c.getTotalCodewordsCount(O,M),R=T-k,L=c.getBlocksCount(O,M),B=T%L,N=L-B,G=Math.floor(T/L),Y=Math.floor(R/L),W=Y+1,V=G-Y,J=new l(V);let te=0;const ie=new Array(L),ue=new Array(L);let me=0;const Ee=new Uint8Array(I.buffer);for(let X=0;X<L;X++){const Q=X<N?Y:W;ie[X]=Ee.slice(te,te+Q),ue[X]=J.encode(ie[X]),te+=Q,me=Math.max(me,Q)}const Oe=new Uint8Array(T);let Ae=0,U,K;for(U=0;U<me;U++)for(K=0;K<L;K++)U<ie[K].length&&(Oe[Ae++]=ie[K][U]);for(U=0;U<V;U++)for(K=0;K<L;K++)Oe[Ae++]=ue[K][U];return Oe}function D(I,O,M,T){let k;if(Array.isArray(I))k=f.fromArray(I);else if(typeof I=="string"){let G=O;if(!G){const Y=f.rawSplit(I);G=s.getBestVersionForData(Y,M)}k=f.fromString(I,G||40)}else throw new Error("Invalid data");const R=s.getBestVersionForData(k,M);if(!R)throw new Error("The amount of data is too big to be stored in a QR Code");if(!O)O=R;else if(O<R)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+R+".\n");const L=b(O,M,k),B=e.getSymbolSize(O),N=new r(B);return h(N,O),p(N),m(N,O),g(N,M,0),O>=7&&_(N,O),v(N,L),isNaN(T)&&(T=u.getBestMask(N,g.bind(null,N,M))),u.applyMask(T,N),g(N,M,T),{modules:N,version:O,errorCorrectionLevel:M,maskPattern:T,segments:k}}return bs.create=function(O,M){if(typeof O>"u"||O==="")throw new Error("No input text");let T=t.M,k,R;return typeof M<"u"&&(T=t.from(M.errorCorrectionLevel,t.M),k=s.from(M.version),R=u.from(M.maskPattern),M.toSJISFunc&&e.setToSJISFunction(M.toSJISFunc)),D(O,k,T,R)},bs}var $s={},Hs={},Sf;function Jh(){return Sf||(Sf=1,function(e){function t(n){if(typeof n=="number"&&(n=n.toString()),typeof n!="string")throw new Error("Color should be defined as hex string");let r=n.slice().replace("#","").split("");if(r.length<3||r.length===5||r.length>8)throw new Error("Invalid hex color: "+n);(r.length===3||r.length===4)&&(r=Array.prototype.concat.apply([],r.map(function(o){return[o,o]}))),r.length===6&&r.push("F","F");const i=parseInt(r.join(""),16);return{r:i>>24&255,g:i>>16&255,b:i>>8&255,a:i&255,hex:"#"+r.slice(0,6).join("")}}e.getOptions=function(r){r||(r={}),r.color||(r.color={});const i=typeof r.margin>"u"||r.margin===null||r.margin<0?4:r.margin,o=r.width&&r.width>=21?r.width:void 0,u=r.scale||4;return{width:o,scale:o?4:u,margin:i,color:{dark:t(r.color.dark||"#000000ff"),light:t(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},e.getScale=function(r,i){return i.width&&i.width>=r+i.margin*2?i.width/(r+i.margin*2):i.scale},e.getImageWidth=function(r,i){const o=e.getScale(r,i);return Math.floor((r+i.margin*2)*o)},e.qrToImageData=function(r,i,o){const u=i.modules.size,c=i.modules.data,l=e.getScale(u,o),s=Math.floor((u+o.margin*2)*l),a=o.margin*l,d=[o.color.light,o.color.dark];for(let f=0;f<s;f++)for(let h=0;h<s;h++){let p=(f*s+h)*4,m=o.color.light;if(f>=a&&h>=a&&f<s-a&&h<s-a){const _=Math.floor((f-a)/l),g=Math.floor((h-a)/l);m=d[c[_*u+g]?1:0]}r[p++]=m.r,r[p++]=m.g,r[p++]=m.b,r[p]=m.a}}}(Hs)),Hs}var Cf;function cb(){return Cf||(Cf=1,function(e){const t=Jh();function n(i,o,u){i.clearRect(0,0,o.width,o.height),o.style||(o.style={}),o.height=u,o.width=u,o.style.height=u+"px",o.style.width=u+"px"}function r(){try{return document.createElement("canvas")}catch(i){throw new Error("You need to specify a canvas element")}}e.render=function(o,u,c){let l=c,s=u;typeof l>"u"&&(!u||!u.getContext)&&(l=u,u=void 0),u||(s=r()),l=t.getOptions(l);const a=t.getImageWidth(o.modules.size,l),d=s.getContext("2d"),f=d.createImageData(a,a);return t.qrToImageData(f.data,o,l),n(d,s,a),d.putImageData(f,0,0),s},e.renderToDataURL=function(o,u,c){let l=c;typeof l>"u"&&(!u||!u.getContext)&&(l=u,u=void 0),l||(l={});const s=e.render(o,u,l),a=l.type||"image/png",d=l.rendererOpts||{};return s.toDataURL(a,d.quality)}}($s)),$s}var js={},If;function fb(){if(If)return js;If=1;const e=Jh();function t(i,o){const u=i.a/255,c=o+'="'+i.hex+'"';return u<1?c+" "+o+'-opacity="'+u.toFixed(2).slice(1)+'"':c}function n(i,o,u){let c=i+o;return typeof u<"u"&&(c+=" "+u),c}function r(i,o,u){let c="",l=0,s=!1,a=0;for(let d=0;d<i.length;d++){const f=Math.floor(d%o),h=Math.floor(d/o);!f&&!s&&(s=!0),i[d]?(a++,d>0&&f>0&&i[d-1]||(c+=s?n("M",f+u,.5+h+u):n("m",l,0),l=0,s=!1),f+1<o&&i[d+1]||(c+=n("h",a),a=0)):l++}return c}return js.render=function(o,u,c){const l=e.getOptions(u),s=o.modules.size,a=o.modules.data,d=s+l.margin*2,f=l.color.light.a?"<path "+t(l.color.light,"fill")+' d="M0 0h'+d+"v"+d+'H0z"/>':"",h="<path "+t(l.color.dark,"stroke")+' d="'+r(a,s,l.margin)+'"/>',p='viewBox="0 0 '+d+" "+d+'"',_='<svg xmlns="http://www.w3.org/2000/svg" '+(l.width?'width="'+l.width+'" height="'+l.width+'" ':"")+p+' shape-rendering="crispEdges">'+f+h+"</svg>\n";return typeof c=="function"&&c(null,_),_},js}var Mf;function db(){if(Mf)return jn;Mf=1;const e=Vy(),t=lb(),n=cb(),r=fb();function i(o,u,c,l,s){const a=[].slice.call(arguments,1),d=a.length,f=typeof a[d-1]=="function";if(!f&&!e())throw new Error("Callback required as last argument");if(f){if(d<2)throw new Error("Too few arguments provided");d===2?(s=c,c=u,u=l=void 0):d===3&&(u.getContext&&typeof s>"u"?(s=l,l=void 0):(s=l,l=c,c=u,u=void 0))}else{if(d<1)throw new Error("Too few arguments provided");return d===1?(c=u,u=l=void 0):d===2&&!u.getContext&&(l=c,c=u,u=void 0),new Promise(function(h,p){try{const m=t.create(c,l);h(o(m,u,l))}catch(m){p(m)}})}try{const h=t.create(c,l);s(null,o(h,u,l))}catch(h){s(h)}}return jn.create=t.create,jn.toCanvas=i.bind(null,n.render),jn.toDataURL=i.bind(null,n.renderToDataURL),jn.toString=i.bind(null,function(o,u,c){return r.render(o,c)}),jn}var hb=db();const mb=Rf(hb),pb="data:image/png;base64,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",gb=["src"],_b=["src"],vb=["id"],yb=["innerHTML"],bb={key:0,class:"card-date-join"},Eb={class:"label"},Ab={class:"date"},wb={class:"label"},Ob={class:"date"};parseFloat(getComputedStyle(document.documentElement).getPropertyValue("--width-card"));var ya;(function(e){(function(t){t.CONNECT="remote.connect",t.SEND="remote.send",t.CLOSE="remote.close"})(e.Remote||(e.Remote={}))})(ya||(ya={}));const{CONNECT:IE,SEND:ME,CLOSE:LE}=ya.Remote,Tb={button:{ok:"Ok",cancel:"Cancel",submit:"Submit",delete:"Delete"},form:{search:"search",join:"join",expire:"expire"},countdown:{before:{prefix:"end on ",suffix:""},after:"Event ended",counting_down:{prefix:"end in ",suffix:""},counting_up:{prefix:"overdue",suffix:""},time_unit:{dd:"day",hh:"hour",mm:"min",ss:"sec"}},error:{offline_status:"offline",failed_to_get_server_time:"Failed to get server time",is_required:"required",minimum_length:"min. {n} character | min. {n} characters",maximum_length:"max. {n} character | max. {n} characters",minimum_number:"must be {n} or above",maximum_number:"must be {n} or below",invalid_pattern:"wrong format",invalid:"invalid",invalid_date:"wrong date format",before_minimum_date:"must be {date} or later",after_maximum_date:"must be {date} or earlier",above_minimum_amount:"must be {amount} or above",below_maximum_amount:"must be {amount} or below"}},Sb={en:Tb,"zh-Hans":{button:{ok:"好",cancel:"取消",submit:"提交",delete:"删除"},form:{search:"搜索",join:"加入",expire:"到期"},countdown:{before:{prefix:"",suffix:"结束"},after:"活动已结束",counting_down:{prefix:"",suffix:"后结束"},counting_up:{prefix:"超时",suffix:""},time_unit:{dd:"天",hh:"时",mm:"分",ss:"秒"}},error:{offline_status:"离线",failed_to_get_server_time:"服务器时间获取失败",is_required:"必填项",minimum_length:"最少{n}个字符",maximum_length:"最多{n}个字符",minimum_number:"最少为{n}",maximum_number:"最多为{n}",invalid_pattern:"格式错误",invalid:"错误",invalid_date:"时间格式错误",before_minimum_date:"应该在{date}或之后",after_maximum_date:"应该在{date}或之前",above_minimum_amount:"金额至少为{amount}",below_maximum_amount:"金额最多为{amount}"}},"zh-Hant":{button:{ok:"好",cancel:"取消",submit:"提交",delete:"刪除"},form:{search:"搜寻",join:"加入",expire:"到期"},countdown:{before:{prefix:"",suffix:"結束"},after:"活動已結束",counting_down:{prefix:"",suffix:"後結束"},counting_up:{prefix:"超時",suffix:""},time_unit:{dd:"天",hh:"时",mm:"分",ss:"秒"}},error:{offline_status:"離線",failed_to_get_server_time:"服務器時間獲取失敗",is_required:"必填項",minimum_length:"最少{n}個字符",maximum_length:"最多{n}個字符",minimum_number:"最少为{n}",maximum_number:"最多为{n}",invalid_pattern:"格式錯誤",invalid:"錯誤",invalid_date:"時間格式錯誤",before_minimum_date:"應該在{date}之後",after_maximum_date:"應該在{date}之前",above_minimum_amount:"金額至少為{amount}",below_maximum_amount:"金額最多為{amount}"}}};Oh({legacy:!1,locale:"en",messages:Sb});const Qo=Ge({__name:"UIScreen",setup(e,{expose:t}){const{t:n}=_t(),r=$p(),i=Rn(),{START_PAGE:o}=i,u=$h(),c=zr(),l=de(()=>r.title||""),s=_e(void 0),a=de(()=>u.name===o),d={get(m,_){if(_ in m)return typeof m[_]=="function"?m[_].bind(m):m[_];console.warn("Method ".concat(_," is not defined on UIScreen."))}},f=new Proxy({},d);function h(){c.go(-1)}$t(()=>{s.value&&Object.setPrototypeOf(f,s.value)});function p(){var m;(m=i.app)==null||m.unmount(),Nl()}return t(f),(m,_)=>(ne(),et(Z(ry),bo({ref_key:"screenRef",ref:s,disableNavBack:a.value,"page-transition":!1},Z(r),{onCloseWindow:p,isOnline:Z(i).isOnline}),Ln({_:2},[m.$slots.navigationBar?{name:"navigationBar",fn:He(()=>[Qe(m.$slots,"navigationBar")]),key:"0"}:{name:"navigationBar",fn:He(()=>[be(Z(jh),{title:l.value,class:$e(Z(i).ENVIRONMENT.DEVICE.IOS?"ios":"android")},Ln({rightContent:He(()=>[be(Z(Bt),{type:"circle",icon:{name:"close"},onClick:Z(Nl)},null,8,["onClick"])]),_:2},[a.value?void 0:{name:"leftContent",fn:He(()=>[be(Z(Bt),{type:"clear",icon:{name:"back"},class:"back-button",onClick:h})]),key:"0"}]),1032,["title","class"])]),key:"1"},qr(m.$slots,(g,v)=>({name:v,fn:He(b=>[Qe(m.$slots,v,Hf(Xd(b)))])}))]),1040,["disableNavBack","isOnline"]))}}),Cb={class:"screen-container"},Ib=Ge({__name:"index",setup(e){const t=zr(),{t:n}=_t(),r=Rn(),{WIDGET:i,ENVIRONMENT:o,APPLET_NAME:u}=r,c=_e(!1),l=_e(void 0),s=_e(""),a=_e(void 0);Bo(async()=>{c.value=!0,await d(),c.value=!1});const d=async()=>{c.value=!0;const f=await new Promise(h=>{const p=Math.random();setTimeout(()=>{h(p>.5?{familyName:"Test",givenName:"Name"}:{error:{code:"404",statusMessage:"timeout"}})},2e3)});c.value=!1,pa(f)&&(s.value=M_(f.error,n)),a.value=f};return(f,h)=>(ne(),ae("div",Cb,[be(Qo,{ref_key:"screenRef",ref:l,class:"welcome-page",title:Z(n)("welcome_page.title"),loading:c.value},Ln({content:He(()=>{var p,m;return[oe("h1",null,"Welcome Page, let's start "+Le(Z(u)),1),oe("h2",null,"Family Name: "+Le((p=a.value)==null?void 0:p.familyName),1),oe("h2",null,"Given Name: "+Le((m=a.value)==null?void 0:m.givenName),1),be(Z(Bt),{onClick:d,title:Z(n)("button.fetch_data")},null,8,["title"]),be(Z(Bt),{onClick:h[0]||(h[0]=_=>Z(t).push("/about-page")),title:Z(n)("button.next")},null,8,["title"])]}),_:2},[s.value?{name:"dialog",fn:He(()=>[s.value?(ne(),et(Z(Wo),{key:0,title:Z(n)("error.oops"),description:s.value,onCloseDialog:h[1]||(h[1]=p=>s.value="")},null,8,["title","description"])):pe("",!0)]),key:"0"}:void 0]),1032,["title","loading"])]))}}),Mb={class:"screen-container"},Lb=Ge({__name:"index",setup(e){zr();const{t}=_t(),n=Rn(),{WIDGET:r,ENVIRONMENT:i,APPLET_NAME:o}=n,u=_e(!1),c=_e(void 0),l=_e("");return(s,a)=>(ne(),ae("div",Mb,[be(Qo,{ref_key:"screenRef",ref:c,class:"about-page",title:Z(t)("about_page.title"),loading:u.value},Ln({content:He(()=>[a[1]||(a[1]=oe("h1",null,"About Page",-1))]),_:2},[l.value?{name:"dialog",fn:He(()=>[l.value?(ne(),et(Z(Wo),{key:0,description:l.value,onCloseDialog:a[0]||(a[0]=d=>l.value="")},null,8,["description"])):pe("",!0)]),key:"0"}:void 0]),1032,["title","loading"])]))}}),Db={class:"screen-container"},Rb={class:"place-selection"},kb={class:"search-container"},Nb={class:"search-input-wrapper"},xb=["placeholder"],Pb={class:"places-list"},Fb=["onClick"],Bb={class:"place-content"},$b={class:"place-name"},Hb={class:"status-indicator"},jb={class:"status-text"},Ub={key:0,class:"no-results"},Yb=Ge({__name:"index",setup(e){const t=zr(),{t:n}=_t(),r=Rn(),i=_e(!1),o=_e(""),u=_e(void 0),c=_e("");Bo(async()=>{i.value=!0,await l(),i.value=!1});const l=async()=>{i.value=!0,await r.fetchPlaces(),i.value=!1,r.error&&(c.value=r.error)},s=de(()=>{if(!o.value.trim())return r.places;const f=o.value.toLowerCase();return r.places.filter(h=>h.name.toLowerCase().includes(f))});function a(f){var b;if(!((b=f.hours)!=null&&b.general)||f.hours.general.length===0)return!1;const h=new Date,p=(h.getDay()+6)%7,m=h.getHours(),_=h.getMinutes(),g="".concat(m.toString().padStart(2,"0"),":").concat(_.toString().padStart(2,"0")),v=f.hours.general.find(E=>E.day===p);return!v||v.closed||v.ranges.length===0?!1:v.ranges.some(E=>g>=E.open&&g<=E.close)}function d(f){t.push("/service-type/".concat(f.id))}return(f,h)=>(ne(),ae("div",Db,[be(Qo,{ref_key:"screenRef",ref:u,class:"place-list-view",title:Z(n)("place_list.title"),loading:i.value},Ln({content:He(()=>[oe("div",Rb,[oe("div",kb,[oe("div",Nb,[h[4]||(h[4]=oe("svg",{class:"search-icon",xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[oe("circle",{cx:"11",cy:"11",r:"8"}),oe("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"})],-1)),Ws(oe("input",{type:"text","onUpdate:modelValue":h[0]||(h[0]=p=>o.value=p),placeholder:Z(n)("place_list.searchPlaceholder"),class:"search-input"},null,8,xb),[[Kg,o.value]]),o.value?(ne(),ae("button",{key:0,onClick:h[1]||(h[1]=p=>o.value=""),class:"clear-button","aria-label":"Clear search"},h[3]||(h[3]=[oe("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[oe("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),oe("line",{x1:"6",y1:"6",x2:"18",y2:"18"})],-1)]))):pe("",!0)])]),oe("div",Pb,[(ne(!0),ae(Be,null,qr(s.value,p=>(ne(),ae("div",{key:p.id,class:"place-item",onClick:m=>d(p)},[oe("div",Bb,[oe("h3",$b,Le(p.name),1)]),oe("div",{class:$e(["place-status",{"is-open":a(p)}])},[oe("div",Hb,[oe("span",jb,Le(a(p)?Z(n)("place_list.openNow"):Z(n)("place_list.closed")),1)])],2),h[5]||(h[5]=oe("svg",{class:"chevron-icon",xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[oe("polyline",{points:"9 18 15 12 9 6"})],-1))],8,Fb))),128)),s.value.length===0?(ne(),ae("div",Ub,[h[6]||(h[6]=oe("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[oe("circle",{cx:"12",cy:"12",r:"10"}),oe("line",{x1:"8",y1:"12",x2:"16",y2:"12"})],-1)),oe("p",null,Le(Z(n)("place_list.noResults")),1)])):pe("",!0)])])]),_:2},[c.value?{name:"dialog",fn:He(()=>[c.value?(ne(),et(Z(Wo),{key:0,title:Z(n)("error.oops"),description:c.value,onCloseDialog:h[2]||(h[2]=p=>c.value="")},null,8,["title","description"])):pe("",!0)]),key:"0"}:void 0]),1032,["title","loading"])]))}}),Zh={API:{updateHours:{method:"put",path:"places/{placeId}/hours"}},updateHours:(e,t,n,r)=>{const{method:i,path:o}=Zh.API.updateHours;return E_({method:i,endpoint:o.replace("{placeId}",n||""),base:e,cardId:t,body:r})}};var _r=(e=>(e.GENERAL="general",e.DINEIN="dinein",e.PICKUP="pickup",e.DELIVER="deliver",e))(_r||{});const Wi=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];function Lf(e){if(e==="24:00")return"12am";const[t,n]=e.split(":").map(Number),r=t>=12?"pm":"am",i=t%12||12;return"".concat(i).concat(n>0?":".concat(n.toString().padStart(2,"0")):"").concat(r)}function qb(e){return e.open==="00:00"&&e.close==="00:00"?"Closed":e.open==="00:00"&&e.close==="24:00"?"Open 24 hours":"".concat(Lf(e.open),"-").concat(Lf(e.close))}function Gb(e){const t=[];return e.forEach(n=>{const r=t[t.length-1];r&&n.closed===r.closed&&JSON.stringify(n.ranges)===JSON.stringify(r.ranges)?r.days.push(n.day):t.push({days:[n.day],ranges:n.ranges,closed:n.closed})}),t}function Vb(e){return e.length===1?Wi[e[0]].slice(0,3):e.every((n,r)=>r===0||n===e[r-1]+1)?"".concat(Wi[e[0]].slice(0,3),"-").concat(Wi[e[e.length-1]].slice(0,3)):e.map(n=>Wi[n].slice(0,3)).join(", ")}function Wb(e,t){return e===5||e===6}function zb(e){const t=new Date,n=t.getMonth(),r=t.getDate();return n===0&&r===1||n===11&&r===25}function Xb(e){if(!e||e.length===0)return"No hours set";const t=[...e].sort((r,i)=>r.day-i.day);return Gb(t).map(r=>{const i=Vb(r.days);if(r.closed||r.ranges.length===0)return"".concat(i,": Closed");const o=r.ranges.map(qb).join(", "),u=r.days.some(l=>Wb(l)),c=r.days.some(zb);return u?'<span class="special-day">'.concat(i,"</span>: ").concat(o):c?'<span class="holiday">'.concat(i,"</span>: ").concat(o):"".concat(i,": ").concat(o)}).join(" • ")}const Kb={class:"card-content"},Qb={class:"card-left"},Jb={class:"service-icon"},Zb=["innerHTML"],eE={class:"service-info"},tE={class:"service-title"},nE={key:0,class:"toggle-container"},rE={class:"toggle-label"},iE=["checked"],oE={class:"hours-summary"},sE={key:0,class:"placeholder"},aE=["innerHTML"],uE={key:0,class:"specific-hours"},lE={class:"specific-hours-title"},cE={class:"specific-hours-list"},fE={class:"specific-date"},dE={class:"specific-time"},hE=Ge({__name:"ServiceCard",props:{serviceType:{},title:{},hours:{},hasCustomHours:{type:Boolean},showToggle:{type:Boolean},specific:{}},emits:["card-tap","toggle-tap"],setup(e,{emit:t}){const{t:n}=_t(),r=e,i=t,o=de(()=>r.hours&&r.hours.length>0),u=_e([]),c=de(()=>{if(!r.specific||!Array.isArray(r.specific)||r.specific.length===0)return u.value=[],!1;const p=new Date;p.setHours(0,0,0,0);const m=r.specific.filter(_=>{if(!_.date||typeof _.date!="object")return!1;try{const{year:g,month:v,day:b}=_.date,E=new Date(g,v-1,b);return E.setHours(0,0,0,0),E>=p}catch(g){return!1}});return u.value=m,m.length>0}),l=p=>{try{const m=new Date(p.year,p.month-1,p.day);return new Intl.DateTimeFormat(navigator.language,{month:"short",day:"numeric",year:"numeric"}).format(m)}catch(m){return"Invalid date"}},s=p=>{try{if(p.length!==4)return p;const m=parseInt(p.substring(0,2),10),_=p.substring(2,4),g=m>=12?"PM":"AM",v=m%12||12;return"".concat(v,":").concat(_," ").concat(g)}catch(m){return p}},a=de(()=>{if(!c.value)return[];try{return u.value.map(p=>{const m=l(p.date);let _="";return!p.periods||p.periods.length===0?_=n("service_type.closed","Closed"):_=p.periods.map(v=>v.open.time==="0000"&&v.close.time==="0000"?n("service_type.closed","Closed"):"".concat(s(v.open.time)," - ").concat(s(v.close.time))).join(", "),{date:m,hours:_}})}catch(p){return[]}}),d=p=>{switch(p){case"general":return'\n        <circle cx="12" cy="12" r="10"></circle>\n        <polyline points="12 6 12 12 16 14"></polyline>\n      ';case"dinein":return'\n        <path d="M3 5h1m0 0v14c0 .6.4 1 1 1h2c.6 0 1-.4 1-1V5m0 0h1m0 0h1m0 0h1M9 5h11c.6 0 1 .4 1 1v3c0 1.7-1.3 3-3 3h-5"></path>\n        <path d="M13 12v8c0 .6.4 1 1 1h2c.6 0 1-.4 1-1v-8"></path>\n      ';case"pickup":return'\n        <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>\n        <line x1="3" y1="6" x2="21" y2="6"></line>\n        <path d="M16 10a4 4 0 0 1-8 0"></path>\n      ';case"deliver":return'\n        <rect x="1" y="3" width="15" height="13"></rect>\n        <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>\n        <circle cx="5.5" cy="18.5" r="2.5"></circle>\n        <circle cx="18.5" cy="18.5" r="2.5"></circle>\n      ';default:return""}},f=p=>{i("card-tap",r.serviceType,p)},h=p=>{i("toggle-tap",r.serviceType,p)};return(p,m)=>(ne(),ae("div",{class:$e(["service-card",{"uses-general":!p.hasCustomHours}]),onClick:f},[oe("div",Kb,[oe("div",Qb,[oe("div",Jb,[(ne(),ae("svg",{xmlns:"http://www.w3.org/2000/svg",width:"26",height:"26",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",innerHTML:d(p.serviceType)},null,8,Zb))]),oe("div",eE,[oe("h3",tE,Le(p.title),1),p.showToggle?(ne(),ae("div",nE,[oe("span",rE,Le(Z(n)("service_type.differentFromGeneral")),1),oe("label",{class:"toggle-switch",onClick:Zg(h,["stop","prevent"])},[oe("input",{type:"checkbox",checked:p.hasCustomHours},null,8,iE),m[0]||(m[0]=oe("span",{class:"toggle-slider"},null,-1))])])):pe("",!0),oe("div",oE,[o.value?(ne(),ae(Be,{key:1},[oe("div",{class:"hours-display",innerHTML:Z(Xb)(p.hours)},null,8,aE),c.value?(ne(),ae("div",uE,[oe("h4",lE,Le(Z(n)("service_type.specialHours","Special Hours")),1),oe("ul",cE,[(ne(!0),ae(Be,null,qr(a.value,(_,g)=>(ne(),ae("li",{key:g,class:"specific-hours-item"},[oe("span",fE,Le(_.date)+":",1),oe("span",dE,Le(_.hours),1)]))),128))])])):pe("",!0)],64)):(ne(),ae("p",sE,Le(p.serviceType==="general"?Z(n)("service_type.noHours"):Z(n)("service_type.tapToSet")),1))])])])])],2))}}),mE=(e,t)=>{const n=e.__vccOpts||e;for(const[r,i]of t)n[r]=i;return n},Df=mE(hE,[["__scopeId","data-v-6ae6f034"]]);function pE(){const{t:e}=_t(),t=_e(!1),n=_e({visible:!1,type:null,data:null,title:"",description:"",cancelText:"",confirmText:""}),r=(s,a=null,d=!1)=>{t.value=d,n.value={visible:!0,type:s,data:a,title:o(s),description:u(s,a),cancelText:c(s),confirmText:l(s)}},i=()=>{n.value.visible=!1,n.value.type=null,n.value.data=null,t.value=!1},o=s=>{switch(s){case"error":return e("error.oops");case"confirmation":return e("service_type.saveChanges");case"createCustom":return e("service_type.createCustomHours");case"removeCustom":return e("service_type.removeCustomHours");case"test":return"Test Dialog";default:return""}},u=(s,a)=>{switch(s){case"error":return a||e("error.unknownError");case"confirmation":return t.value?e("service_type.unsavedChangesMessage"):e("service_type.saveChangesMessage");case"createCustom":return e("service_type.createCustomHoursMessage",{type:a});case"removeCustom":return e("service_type.removeCustomHoursMessage",{type:a});case"test":return"This is a test dialog to check if dialogs are working correctly.";default:return""}},c=s=>{switch(s){case"confirmation":return t.value?e("service_type.discardAndLeave"):e("service_type.discard");case"createCustom":case"removeCustom":case"test":return e("service_type.cancel");default:return""}},l=s=>{switch(s){case"confirmation":return e("service_type.save");case"createCustom":return e("service_type.create");case"removeCustom":return e("service_type.remove");case"test":return"OK";default:return""}};return{dialogState:n,openDialog:r,closeDialog:i}}function gE(e,t){const n=_e(!1),r=_e(null);let i=null;return{isNavigating:n,pendingNavigation:r,setupNavigationGuard:c=>{i=e.beforeEach((l,s,a)=>{s.name==="service-type"&&t.value?(n.value=!0,r.value=()=>a(),c&&c(),a(!1)):a()})},cleanupNavigationGuard:()=>{i&&i()}}}const _E={class:"screen-container"},vE={class:"service-type-selection"},yE={class:"service-cards"},bE={key:0,class:"action-buttons"},EE=["disabled"],AE=Ge({__name:"index",setup(e){const t=zr(),n=$h(),{t:r}=_t(),i=Rn(),{WIDGET:o,ENVIRONMENT:u}=i,{API:c}=o,{CARD:l}=u,s=_e(!1),a=_e(void 0),d=_e(""),f=_e(null),h=In(new Map),p=In(new Map),m=_e(null),_=de(()=>h.size>0||Array.from(p.entries()).some(([y,C])=>{const F=k(y);return C!==F})),{dialogState:g,openDialog:v,closeDialog:b}=pE(),{isNavigating:E,pendingNavigation:D,setupNavigationGuard:I,cleanupNavigationGuard:O}=gE(t,_),M=de(()=>["general","dinein","pickup","deliver"]),T=de(()=>M.value.filter(y=>{if(y==="general")return!0;if(!f.value)return!1;const C=y in(f.value.hours||{}),F=f.value[y],$=F&&F.available===!0;return console.log("Service type ".concat(y,": hasHoursEntry=").concat(C,", isAvailableInOriginalData=").concat($)),C||$})),k=y=>{var C,F,$,j,A;return!!((F=(C=f.value)==null?void 0:C.hours)!=null&&F[y]&&((A=(j=($=f.value)==null?void 0:$.hours)==null?void 0:j[y])==null?void 0:A.length)>0)},R=()=>{f.value&&T.value.forEach(y=>{const C=k(y);p.set(y,C)})},L=y=>{const C=[];return y.forEach(F=>{F.closed||F.ranges.forEach($=>{C.push({open:{day:F.day+1,time:$.open},close:{day:F.day+1,time:$.close}})})}),C},B=(y,C)=>{var $,j;if(C&&C.target){const A=C.target;if(A.classList.contains("toggle-switch")||A.classList.contains("toggle-slider")||(($=A.parentElement)==null?void 0:$.classList.contains("toggle-switch"))||((j=A.parentElement)==null?void 0:j.classList.contains("toggle-container")))return}if(y==="general"){te("general");return}p.get(y)||!1?te(y):(m.value=y,v("createCustom",y))},N=(y,C)=>{if(C&&(C.stopPropagation(),C.preventDefault(),C.stopImmediatePropagation()),y==="general")return!1;const F=p.get(y)||!1;return m.value=y,v(F?"removeCustom":"createCustom",y),!1},G=()=>{if(!(!m.value||!f.value)&&f.value.hours&&f.value.hours.general){const y=f.value.hours.general,C=L(y);h.set(m.value,{periods:C}),p.set(m.value,!0);const F=m.value;b(),setTimeout(()=>{te(F)},100)}},Y=()=>{m.value&&(h.set(m.value,{periods:[]}),f.value&&f.value.hours&&(f.value.hours[m.value]=[]),p.set(m.value,!1))},W=async()=>{s.value=!0;try{if(i.places.length===0&&(await i.fetchPlaces(),i.error)){d.value=i.error;return}const y=n.params.placeId;f.value=i.places.find(C=>C.id===y)||null,f.value||(d.value=r("error.placeNotFound"),v("error"))}catch(y){X(y,"error.loadingPlaces")}finally{s.value=!1}},V=y=>{var C,F;if(h.has(y)){const $=h.get(y);if($&&$.periods)return i.convertPeriodsToDayHours($.periods)}return((F=(C=f.value)==null?void 0:C.hours)==null?void 0:F[y])||[]},J=y=>{var F,$;let C;if(h.has(y)){const j=h.get(y);C=j==null?void 0:j.specific}else($=(F=f.value)==null?void 0:F.hours)!=null&&$[y]&&(C=f.value.hours[y].specific);if(C){if(Array.isArray(C)){const j=new Date;j.setHours(0,0,0,0);const A=C.filter(w=>{if(!w.date||typeof w.date!="object")return!1;try{const{year:x,month:H,day:ee}=w.date,z=new Date(x,H-1,ee);return z.setHours(0,0,0,0),z>=j}catch(x){return!1}}).map(w=>{const x={...w};return(!x.periods||x.periods.length===0)&&(x.periods=[{open:{day:0,time:"0000"},close:{day:0,time:"0000"}}]),x});return A.length>0?A:void 0}return C}},te=async y=>{if(f.value){if(y!=="general"&&!p.get(y)){m.value=y,v("createCustom",y);return}try{s.value=!0;const C=ie(y),F=J(y),$={periods:C};F&&($.specific=F);const j=await O_($);j&&typeof j=="object"&&"periods"in j&&(h.set(y,j),y!=="general"&&p.set(y,!0))}catch(C){X(C,"error.editingHours")}finally{s.value=!1}}},ie=y=>{var C,F,$;if(h.has(y)&&((C=h.get(y))!=null&&C.periods))return h.get(y).periods;{const j=ue(y),A=(($=(F=f.value)==null?void 0:F.hours)==null?void 0:$[j])||[];return L(A)}},ue=y=>{switch(y){case"general":case _r.GENERAL:return"general";case"dinein":case _r.DINEIN:return"dinein";case"pickup":case _r.PICKUP:return"pickup";case"deliver":case _r.DELIVER:return"deliver";default:return"general"}},me=()=>{if(h.clear(),f.value&&R(),E.value&&D.value){const y=D.value;E.value=!1,D.value=null,y()}else t.go(-1)},Ee=async()=>{const y=E.value&&D.value,C=D.value;E.value=!1,D.value=null;const F=await Oe();y&&C&&F&&C()},Oe=async()=>{var y,C;if(!f.value||h.size===0)return!1;try{Ae(),s.value=!0;const{requestBody:F,pendingChangesCopy:$}=U(),j=((C=(y=f.value.external)==null?void 0:y.crm)==null?void 0:C.storeId)||f.value.id.trim(),A=await Zh.updateHours(c.baseUrl,l.id,j,F);if("error"in A){const w=A.error.message||r("error.savingHours");throw new Error(w)}return K($),h.clear(),R(),!0}catch(F){return X(F,"error.savingHours"),!1}finally{s.value=!1}},Ae=()=>{var y;if(!c)throw new Error(r("error.initializingAPI"));if(!((y=f.value)!=null&&y.id)||f.value.id.trim()==="")throw new Error(r("error.invalidPlaceId"))},U=()=>{const y={},C=new Map;return h.forEach((F,$)=>{C.set($,JSON.parse(JSON.stringify(F)));const j=F.periods.length===0?[]:F.periods,A=F.specific||[];switch($){case"general":y.openingHours={periods:j,specific:A};break;case"dinein":y.dinein={periods:j,specific:A};break;case"pickup":y.pickup={periods:j,specific:A};break;case"deliver":y.deliver={periods:j,specific:A};break}}),{requestBody:y,pendingChangesCopy:C}},K=y=>{f.value&&(f.value.hours||(f.value.hours={}),y.forEach((C,F)=>{if(C.periods.length===0)f.value&&f.value.hours&&(f.value.hours[F]=[]);else{const $=i.convertPeriodsToDayHours(C.periods);f.value&&f.value.hours&&(f.value.hours[F]=$,C.specific&&(f.value.hours[F].specific=C.specific))}}))},X=(y,C)=>{const F=y instanceof Error?y.message:r(C);console.error("Error: ".concat(F),y),d.value=F,v("error")},Q=y=>{switch(y){case"cancel":fe();break;case"confirm":S();break;default:b()}},fe=()=>{switch(g.value.type){case"confirmation":me();break}b()},S=()=>{switch(g.value.type){case"confirmation":Ee();break;case"createCustom":G();break;case"removeCustom":Y();break}b()};return Bo(async()=>{s.value=!0;try{await W(),R()}catch(y){X(y,"error.initialization")}finally{s.value=!1}}),$t(()=>{R(),I(()=>v("confirmation"))}),tr(()=>{O()}),(y,C)=>{var F;return ne(),ae("div",_E,[be(Qo,{ref_key:"screenRef",ref:a,class:"service-type-view",title:((F=f.value)==null?void 0:F.name)||Z(r)("service_type.title"),loading:s.value},Ln({content:He(()=>[oe("div",vE,[oe("div",yE,[be(Df,{"service-type":"general",title:Z(r)("service_type.general"),hours:V("general"),specific:J("general"),"has-custom-hours":!0,"show-toggle":!1,onCardTap:B},null,8,["title","hours","specific"]),(ne(!0),ae(Be,null,qr(T.value.filter($=>$!=="general"),$=>(ne(),et(Df,{key:$,"service-type":$,title:Z(r)("service_type.".concat($)),hours:V($),specific:J($),"has-custom-hours":p.get($)||!1,"show-toggle":!0,onCardTap:B,onToggleTap:N},null,8,["service-type","title","hours","specific","has-custom-hours"]))),128))]),_.value?(ne(),ae("div",bE,[oe("button",{class:"save-button",onClick:Oe,disabled:s.value},Le(s.value?Z(r)("service_type.saving"):Z(r)("service_type.save")),9,EE),oe("button",{class:"cancel-button",onClick:me},Le(Z(r)("service_type.cancel")),1)])):pe("",!0)])]),_:2},[Z(g).visible?{name:"dialog",fn:He(()=>[be(Z(Wo),{title:Z(g).title,description:Z(g).description,onCloseDialog:Z(b),showCancel:Z(g).type!=="error",style:{"z-index":"9999",position:"relative"}},{buttons:He(()=>[Z(g).type!=="error"?(ne(),et(Z(Bt),{key:0,type:"clear",class:"cancel-button",onClick:C[0]||(C[0]=$=>Q("cancel")),title:Z(g).cancelText},null,8,["title"])):pe("",!0),be(Z(Bt),{type:"clear",class:"confirm-button",onClick:C[1]||(C[1]=$=>Q("confirm")),title:Z(g).confirmText},null,8,["title"])]),_:1},8,["title","description","onCloseDialog","showCancel"])]),key:"0"}:void 0]),1032,["title","loading"])])}}}),em=jv({history:_v("./"),routes:[{path:"/",name:"root",redirect:"/place-list"},{path:"/welcome-page",name:"welcome-page",component:Ib},{path:"/about-page",name:"about-page",component:Lb},{path:"/place-list",name:"place-list",component:Yb},{path:"/service-type/:placeId",name:"service-type",component:AE,props:!0}]});em.beforeEach((e,t)=>{const n=!t.name,i=window.history.state.forward===t.fullPath?"swipe-left":"swipe-right";e.meta.transition=n?"":i});const wE={button:{next:"Next",fetch_data:"Simulate Fetch Data"},welcome_page:{title:"Welcome Page"},about_page:{title:"About Page"},place_list:{title:"Select a Place",searchPlaceholder:"Search places...",openNow:"Open",closed:"Closed",noResults:"No places found"},service_type:{title:"Service Hours",general:"General",dinein:"Dine-In",pickup:"Pickup",deliver:"Delivery",edit:"Edit",save:"Save",saving:"Saving...",cancel:"Cancel",usesGeneral:"Uses General",noHours:"No hours set",tapToSet:"Tap to set specific hours",saveChanges:"Save Changes?",saveChangesMessage:"You have unsaved changes. Would you like to save them before leaving?",unsavedChangesMessage:"You have unsaved changes. Would you like to save them before navigating away?",discard:"Discard",discardAndLeave:"Discard & Leave",createCustomHours:"Create Custom Hours",createCustomHoursMessage:"Do you want to create custom hours for {type}? This will start with a copy of the general hours.",removeCustomHours:"Remove Custom Hours",removeCustomHoursMessage:"Do you want to remove custom hours for {type}? This will revert to using general hours.",create:"Create",remove:"Remove",differentFromGeneral:"Different from General?"},error:{oops:"Oops!",placeNotFound:"Place not found",editingHours:"Error editing hours",savingHours:"Error saving hours",initializingAPI:"Error initializing API",invalidPlaceId:"Invalid place ID"}},OE={en:wE,"zh-Hans":{button:{next:"下一步",fetch_data:"模拟获取数据"},welcome_page:{title:"欢迎页面"},about_page:{title:"关于页面"},place_list:{title:"选择地点",searchPlaceholder:"搜索地点...",openNow:"营业",closed:"已关闭",noResults:"未找到地点"},service_type:{title:"服务时间",general:"一般营业时间",dinein:"堂食",pickup:"自取",deliver:"外送",edit:"编辑",save:"保存",saving:"保存中...",cancel:"取消",usesGeneral:"使用一般时间",noHours:"未设置时间",tapToSet:"点击设置特定时间",saveChanges:"保存更改？",saveChangesMessage:"您有未保存的更改。离开前是否要保存？",unsavedChangesMessage:"您有未保存的更改。在离开页面前是否要保存？",discard:"放弃",discardAndLeave:"放弃并离开",createCustomHours:"创建自定义时间",createCustomHoursMessage:"您想为{type}创建自定义时间吗？这将从一般营业时间的副本开始。",removeCustomHours:"移除自定义时间",removeCustomHoursMessage:"您想移除{type}的自定义时间吗？这将恢复使用一般营业时间。",create:"创建",remove:"移除",differentFromGeneral:"与一般营业时间不同?"},error:{oops:"出错了！",placeNotFound:"未找到地点",editingHours:"编辑时间时出错",savingHours:"保存时间时出错",initializingAPI:"初始化API时出错",invalidPlaceId:"无效的地点ID"}},"zh-Hant":{button:{next:"下一步",fetch_data:"模擬獲取數據"},welcome_page:{title:"歡迎頁面"},about_page:{title:"關於頁面"},place_list:{title:"選擇地點",searchPlaceholder:"搜索地點...",openNow:"營業",closed:"已關閉",noResults:"未找到地點"},service_type:{title:"服務時間",general:"一般營業時間",dinein:"堂食",pickup:"自取",deliver:"外送",edit:"編輯",save:"保存",saving:"保存中...",cancel:"取消",usesGeneral:"使用一般時間",noHours:"未設置時間",tapToSet:"點擊設置特定時間",saveChanges:"保存更改？",saveChangesMessage:"您有未保存的更改。離開前是否要保存？",unsavedChangesMessage:"您有未保存的更改。在離開頁面前是否要保存？",discard:"放棄",discardAndLeave:"放棄並離開",createCustomHours:"創建自定義時間",createCustomHoursMessage:"您想為{type}創建自定義時間嗎？這將從一般營業時間的副本開始。",removeCustomHours:"移除自定義時間",removeCustomHoursMessage:"您想移除{type}的自定義時間嗎？這將恢復使用一般營業時間。",create:"創建",remove:"移除",differentFromGeneral:"與一般營業時間不同?"},error:{oops:"出錯了！",placeNotFound:"未找到地點",editingHours:"編輯時間時出錯",savingHours:"保存時間時出錯",initializingAPI:"初始化API時出錯",invalidPlaceId:"無效的地點ID"}}};async function TE(){const e=n0(Yv),t=o0();e.use(t);const n=Rn();await n.fetchData();const{ENVIRONMENT:r,APPLET_NAME:i}=n,o=Oh({locale:L_(r.LANGUAGE),fallbackLocale:pt.LANGUAGES.DEFAULT,messages:$_(OE),legacy:!1});e.use(o),e.use(em),F_(r,i),B_(r,vr.LOCALE_LANGUAGE,vr.formatDateTime),e.mount("#app"),n.app=e}TE();export{CE as __vite_legacy_guard};
