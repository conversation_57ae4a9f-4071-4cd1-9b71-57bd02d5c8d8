var em=Object.defineProperty;var tm=(e,t,n)=>t in e?em(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Qo=(e,t,n)=>tm(e,typeof t!="symbol"?t+"":t,n);function IE(){import.meta.url,import("_").catch(()=>1),async function*(){}().next()}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const u of o.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&r(u)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();(function(){const e=()=>Math.floor((1+Math.random())*16777216).toString(16).substring(1),t=()=>e()+e(),n={},r={},i={},o=s=>{Object.assign(r,s),Object.keys(r).forEach(function(a){typeof window.$data[a]<"u"||Object.defineProperty(window.$data,a,{get(){return r[a]},set(d){r[a]=d},enumerable:!0})}),window.dispatchEvent(new CustomEvent("data.changed"))};window.$perkd={onMessage(s){const a=n[s.id];a?s.error?a.reject(s.error):a.resolve(s.data):s.name==="data.changed"?o(s.data):s.name==="bag.changed"?(Object.assign(i,s.data),window.dispatchEvent(new CustomEvent("bag.changed"))):window.dispatchEvent(new CustomEvent(s.name,{detail:s.data}))},emit(s,a){const d=t(),f=JSON.stringify({id:d,name:s,data:a});return window.ReactNativeWebView?window.ReactNativeWebView.postMessage(f):window.parent.postMessage(JSON.parse(f),"*"),d},do(s,a){const d=window.$perkd.emit("do",{action:s,param:a});return new Promise(function(f,h){n[d]={resolve:f,reject:h}})}};class u{save(){window.$perkd.do("data.save",r)}add(a){Object.defineProperty(this,a,{get(){return r[a]},set(d){r[a]=d},enumerable:!0})}}window.$data=new u;class c{constructor(){Qo(this,"items");Qo(this,"amount")}addItems(a){window.$perkd.do("bag.addItems",{items:a})}updateItems(a){window.$perkd.do("bag.updateItems",{items:a})}removeItems(a){window.$perkd.do("bag.removeItems",{items:a})}}window.$bag=new c,["items","amount"].forEach(s=>Object.defineProperty(window.$bag,s,{get(){return i[s]},enumerable:!0})),window.$perkd.do("init").then(o)})();function Lf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Zr={},Xa;function Rf(){if(Xa)return Zr;Xa=1,Object.defineProperty(Zr,"__esModule",{value:!0});const e={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),weekStart:1,weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),ordinal:t=>{const n=["th","st","nd","rd"],r=t%100;return"[".concat(t).concat(n[(r-20)%10]||n[r]||n[0],"]")},formats:{LT:"h:mma",LTS:"ha",L:"D MMM, LTX",LL:"D MMM YYYY, LTX",LLL:"dddd, D MMM, LTX",LLLL:"dddd, D MMM YYYY, LTX",l:"D MMM",ll:"D MMM YYYY",lll:"dddd, D MMM",llll:"dddd, D MMM YYYY"},calendar:{lastDay:"[yesterday]",sameDay:"[today]",nextDay:"[tomorrow]",lastWeek:"[last] dddd",sameWeek:"dddd",nextWeek:"[next] dddd",sameYear:"l",sameElse:"ll",timeFormat:"%c, LTX"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},humane:{daysToRelative:0,daysToCalendar:1,skipFromUnit:"second",startFrom:{value:30,unit:"second"},soon:"in few %u",justnow:"just now",s:"1 sec",ss:"%d secs",m:"1 min",mm:"%d mins",h:"1 hour",hh:"%d hours",d:"1 day",dd:"%d days",M:"1 month",MM:"%d months",y:"1 year",yy:"%d years"},period:{daysToCalendar:1,showSameDayToday:!1,sameYear:{startDate:"l",endDate:"lyx",startTime:"LYX",endTime:"LYX",format:"%ds - %de"},sameMonth:{startDate:"D",endDate:"lyx",startTime:"LYX",endTime:"LYX",format:"%ds - %de"},sameDay:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds - %de"},sameMeridiem:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds - %de"},others:{startDate:"ll",endDate:"ll",startTime:"LL",endTime:"LL",format:"%ds - %de"}}};return Zr.default=e,Zr}Rf();var ei={},ge={},Wi={exports:{}},nm=Wi.exports,Ka;function kf(){return Ka||(Ka=1,function(e,t){(function(n,r){e.exports=r()})(nm,function(){var n=1e3,r=6e4,i=36e5,o="millisecond",u="second",c="minute",l="hour",s="day",a="week",d="month",f="quarter",h="year",p="date",m="Invalid Date",_=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,g=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,v={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(N){var G=["th","st","nd","rd"],Y=N%100;return"["+N+(G[(Y-20)%10]||G[Y]||G[0])+"]"}},y=function(N,G,Y){var V=String(N);return!V||V.length>=G?N:""+Array(G+1-V.length).join(Y)+N},E={s:y,z:function(N){var G=-N.utcOffset(),Y=Math.abs(G),V=Math.floor(Y/60),W=Y%60;return(G<=0?"+":"-")+y(V,2,"0")+":"+y(W,2,"0")},m:function N(G,Y){if(G.date()<Y.date())return-N(Y,G);var V=12*(Y.year()-G.year())+(Y.month()-G.month()),W=G.clone().add(V,d),J=Y-W<0,te=G.clone().add(V+(J?-1:1),d);return+(-(V+(Y-W)/(J?W-te:te-W))||0)},a:function(N){return N<0?Math.ceil(N)||0:Math.floor(N)},p:function(N){return{M:d,y:h,w:a,d:s,D:p,h:l,m:c,s:u,ms:o,Q:f}[N]||String(N||"").toLowerCase().replace(/s$/,"")},u:function(N){return N===void 0}},L="en",I={};I[L]=v;var w="$isDayjsObject",D=function(N){return N instanceof M||!(!N||!N[w])},T=function N(G,Y,V){var W;if(!G)return L;if(typeof G=="string"){var J=G.toLowerCase();I[J]&&(W=J),Y&&(I[J]=Y,W=J);var te=G.split("-");if(!W&&te.length>1)return N(te[0])}else{var ie=G.name;I[ie]=G,W=ie}return!V&&W&&(L=W),W||!V&&L},R=function(N,G){if(D(N))return N.clone();var Y=typeof G=="object"?G:{};return Y.date=N,Y.args=arguments,new M(Y)},k=E;k.l=T,k.i=D,k.w=function(N,G){return R(N,{locale:G.$L,utc:G.$u,x:G.$x,$offset:G.$offset})};var M=function(){function N(Y){this.$L=T(Y.locale,null,!0),this.parse(Y),this.$x=this.$x||Y.x||{},this[w]=!0}var G=N.prototype;return G.parse=function(Y){this.$d=function(V){var W=V.date,J=V.utc;if(W===null)return new Date(NaN);if(k.u(W))return new Date;if(W instanceof Date)return new Date(W);if(typeof W=="string"&&!/Z$/i.test(W)){var te=W.match(_);if(te){var ie=te[2]-1||0,ue=(te[7]||"0").substring(0,3);return J?new Date(Date.UTC(te[1],ie,te[3]||1,te[4]||0,te[5]||0,te[6]||0,ue)):new Date(te[1],ie,te[3]||1,te[4]||0,te[5]||0,te[6]||0,ue)}}return new Date(W)}(Y),this.init()},G.init=function(){var Y=this.$d;this.$y=Y.getFullYear(),this.$M=Y.getMonth(),this.$D=Y.getDate(),this.$W=Y.getDay(),this.$H=Y.getHours(),this.$m=Y.getMinutes(),this.$s=Y.getSeconds(),this.$ms=Y.getMilliseconds()},G.$utils=function(){return k},G.isValid=function(){return this.$d.toString()!==m},G.isSame=function(Y,V){var W=R(Y);return this.startOf(V)<=W&&W<=this.endOf(V)},G.isAfter=function(Y,V){return R(Y)<this.startOf(V)},G.isBefore=function(Y,V){return this.endOf(V)<R(Y)},G.$g=function(Y,V,W){return k.u(Y)?this[V]:this.set(W,Y)},G.unix=function(){return Math.floor(this.valueOf()/1e3)},G.valueOf=function(){return this.$d.getTime()},G.startOf=function(Y,V){var W=this,J=!!k.u(V)||V,te=k.p(Y),ie=function(X,Q){var fe=k.w(W.$u?Date.UTC(W.$y,Q,X):new Date(W.$y,Q,X),W);return J?fe:fe.endOf(s)},ue=function(X,Q){return k.w(W.toDate()[X].apply(W.toDate("s"),(J?[0,0,0,0]:[23,59,59,999]).slice(Q)),W)},me=this.$W,ve=this.$M,Te=this.$D,Ae="set"+(this.$u?"UTC":"");switch(te){case h:return J?ie(1,0):ie(31,11);case d:return J?ie(1,ve):ie(0,ve+1);case a:var U=this.$locale().weekStart||0,K=(me<U?me+7:me)-U;return ie(J?Te-K:Te+(6-K),ve);case s:case p:return ue(Ae+"Hours",0);case l:return ue(Ae+"Minutes",1);case c:return ue(Ae+"Seconds",2);case u:return ue(Ae+"Milliseconds",3);default:return this.clone()}},G.endOf=function(Y){return this.startOf(Y,!1)},G.$set=function(Y,V){var W,J=k.p(Y),te="set"+(this.$u?"UTC":""),ie=(W={},W[s]=te+"Date",W[p]=te+"Date",W[d]=te+"Month",W[h]=te+"FullYear",W[l]=te+"Hours",W[c]=te+"Minutes",W[u]=te+"Seconds",W[o]=te+"Milliseconds",W)[J],ue=J===s?this.$D+(V-this.$W):V;if(J===d||J===h){var me=this.clone().set(p,1);me.$d[ie](ue),me.init(),this.$d=me.set(p,Math.min(this.$D,me.daysInMonth())).$d}else ie&&this.$d[ie](ue);return this.init(),this},G.set=function(Y,V){return this.clone().$set(Y,V)},G.get=function(Y){return this[k.p(Y)]()},G.add=function(Y,V){var W,J=this;Y=Number(Y);var te=k.p(V),ie=function(ve){var Te=R(J);return k.w(Te.date(Te.date()+Math.round(ve*Y)),J)};if(te===d)return this.set(d,this.$M+Y);if(te===h)return this.set(h,this.$y+Y);if(te===s)return ie(1);if(te===a)return ie(7);var ue=(W={},W[c]=r,W[l]=i,W[u]=n,W)[te]||1,me=this.$d.getTime()+Y*ue;return k.w(me,this)},G.subtract=function(Y,V){return this.add(-1*Y,V)},G.format=function(Y){var V=this,W=this.$locale();if(!this.isValid())return W.invalidDate||m;var J=Y||"YYYY-MM-DDTHH:mm:ssZ",te=k.z(this),ie=this.$H,ue=this.$m,me=this.$M,ve=W.weekdays,Te=W.months,Ae=W.meridiem,U=function(Q,fe,S,C){return Q&&(Q[fe]||Q(V,J))||S[fe].slice(0,C)},K=function(Q){return k.s(ie%12||12,Q,"0")},X=Ae||function(Q,fe,S){var C=Q<12?"AM":"PM";return S?C.toLowerCase():C};return J.replace(g,function(Q,fe){return fe||function(S){switch(S){case"YY":return String(V.$y).slice(-2);case"YYYY":return k.s(V.$y,4,"0");case"M":return me+1;case"MM":return k.s(me+1,2,"0");case"MMM":return U(W.monthsShort,me,Te,3);case"MMMM":return U(Te,me);case"D":return V.$D;case"DD":return k.s(V.$D,2,"0");case"d":return String(V.$W);case"dd":return U(W.weekdaysMin,V.$W,ve,2);case"ddd":return U(W.weekdaysShort,V.$W,ve,3);case"dddd":return ve[V.$W];case"H":return String(ie);case"HH":return k.s(ie,2,"0");case"h":return K(1);case"hh":return K(2);case"a":return X(ie,ue,!0);case"A":return X(ie,ue,!1);case"m":return String(ue);case"mm":return k.s(ue,2,"0");case"s":return String(V.$s);case"ss":return k.s(V.$s,2,"0");case"SSS":return k.s(V.$ms,3,"0");case"Z":return te}return null}(Q)||te.replace(":","")})},G.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},G.diff=function(Y,V,W){var J,te=this,ie=k.p(V),ue=R(Y),me=(ue.utcOffset()-this.utcOffset())*r,ve=this-ue,Te=function(){return k.m(te,ue)};switch(ie){case h:J=Te()/12;break;case d:J=Te();break;case f:J=Te()/3;break;case a:J=(ve-me)/6048e5;break;case s:J=(ve-me)/864e5;break;case l:J=ve/i;break;case c:J=ve/r;break;case u:J=ve/n;break;default:J=ve}return W?J:k.a(J)},G.daysInMonth=function(){return this.endOf(d).$D},G.$locale=function(){return I[this.$L]},G.locale=function(Y,V){if(!Y)return this.$L;var W=this.clone(),J=T(Y,V,!0);return J&&(W.$L=J),W},G.clone=function(){return k.w(this.$d,this)},G.toDate=function(){return new Date(this.valueOf())},G.toJSON=function(){return this.isValid()?this.toISOString():null},G.toISOString=function(){return this.$d.toISOString()},G.toString=function(){return this.$d.toUTCString()},N}(),H=M.prototype;return R.prototype=H,[["$ms",o],["$s",u],["$m",c],["$H",l],["$W",s],["$M",d],["$y",h],["$D",p]].forEach(function(N){H[N[1]]=function(G){return this.$g(G,N[0],N[1])}}),R.extend=function(N,G){return N.$i||(N(G,M,R),N.$i=!0),R},R.locale=T,R.isDayjs=D,R.unix=function(N){return R(1e3*N)},R.en=I[L],R.Ls=I,R.p={},R})}(Wi)),Wi.exports}var zi={exports:{}},rm=zi.exports,Qa;function im(){return Qa||(Qa=1,function(e,t){(function(n,r){e.exports=r()})(rm,function(){return function(n,r,i){i.updateLocale=function(o,u){var c=i.Ls[o];if(c)return(u?Object.keys(u):[]).forEach(function(l){c[l]=u[l]}),c}}})}(zi)),zi.exports}var Xi={exports:{}},om=Xi.exports,Ja;function sm(){return Ja||(Ja=1,function(e,t){(function(n,r){e.exports=r()})(om,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(r,i,o){var u=i.prototype,c=u.format;o.en.formats=n,u.format=function(l){l===void 0&&(l="YYYY-MM-DDTHH:mm:ssZ");var s=this.$locale().formats,a=function(d,f){return d.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(h,p,m){var _=m&&m.toUpperCase();return p||f[m]||n[m]||f[_].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(g,v,y){return v||y.slice(1)})})}(l,s===void 0?{}:s);return c.call(this,a)}}})}(Xi)),Xi.exports}var Ki={exports:{}},am=Ki.exports,Za;function um(){return Za||(Za=1,function(e,t){(function(n,r){e.exports=r()})(am,function(){var n="day";return function(r,i,o){var u=function(s){return s.add(4-s.isoWeekday(),n)},c=i.prototype;c.isoWeekYear=function(){return u(this).year()},c.isoWeek=function(s){if(!this.$utils().u(s))return this.add(7*(s-this.isoWeek()),n);var a,d,f,h,p=u(this),m=(a=this.isoWeekYear(),d=this.$u,f=(d?o.utc:o)().year(a).startOf("year"),h=4-f.isoWeekday(),f.isoWeekday()>4&&(h+=7),f.add(h,n));return p.diff(m,"week")+1},c.isoWeekday=function(s){return this.$utils().u(s)?this.day()||7:this.day(this.day()%7?s:s-7)};var l=c.startOf;c.startOf=function(s,a){var d=this.$utils(),f=!!d.u(a)||a;return d.p(s)==="isoweek"?f?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):l.bind(this)(s,a)}}})}(Ki)),Ki.exports}var Qi={exports:{}},lm=Qi.exports,eu;function cm(){return eu||(eu=1,function(e,t){(function(n,r){e.exports=r()})(lm,function(){return function(n,r){r.prototype.weekday=function(i){var o=this.$locale().weekStart||0,u=this.$W,c=(u<o?u+7:u)-o;return this.$utils().u(i)?c:this.subtract(c,"day").add(i,"day")}}})}(Qi)),Qi.exports}var Ji={exports:{}},fm=Ji.exports,tu;function dm(){return tu||(tu=1,function(e,t){(function(n,r){e.exports=r()})(fm,function(){return function(n,r,i){n=n||{};var o=r.prototype,u={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function c(s,a,d,f){return o.fromToBase(s,a,d,f)}i.en.relativeTime=u,o.fromToBase=function(s,a,d,f,h){for(var p,m,_,g=d.$locale().relativeTime||u,v=n.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],y=v.length,E=0;E<y;E+=1){var L=v[E];L.d&&(p=f?i(s).diff(d,L.d,!0):d.diff(s,L.d,!0));var I=(n.rounding||Math.round)(Math.abs(p));if(_=p>0,I<=L.r||!L.r){I<=1&&E>0&&(L=v[E-1]);var w=g[L.l];h&&(I=h(""+I)),m=typeof w=="string"?w.replace("%d",I):w(I,a,L.l,_);break}}if(a)return m;var D=_?g.future:g.past;return typeof D=="function"?D(m):D.replace("%s",m)},o.to=function(s,a){return c(s,a,this,!0)},o.from=function(s,a){return c(s,a,this)};var l=function(s){return s.$u?i.utc():i()};o.toNow=function(s){return this.to(l(this),s)},o.fromNow=function(s){return this.from(l(this),s)}}})}(Ji)),Ji.exports}var Zi={exports:{}},hm=Zi.exports,nu;function mm(){return nu||(nu=1,function(e,t){(function(n,r){e.exports=r()})(hm,function(){return function(n,r,i){r.prototype.isBetween=function(o,u,c,l){var s=i(o),a=i(u),d=(l=l||"()")[0]==="(",f=l[1]===")";return(d?this.isAfter(s,c):!this.isBefore(s,c))&&(f?this.isBefore(a,c):!this.isAfter(a,c))||(d?this.isBefore(s,c):!this.isAfter(s,c))&&(f?this.isAfter(a,c):!this.isBefore(a,c))}}})}(Zi)),Zi.exports}var eo={exports:{}},pm=eo.exports,ru;function gm(){return ru||(ru=1,function(e,t){(function(n,r){e.exports=r()})(pm,function(){var n,r,i=1e3,o=6e4,u=36e5,c=864e5,l=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,s=31536e6,a=2628e6,d=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,f={years:s,months:a,days:c,hours:u,minutes:o,seconds:i,milliseconds:1,weeks:6048e5},h=function(I){return I instanceof E},p=function(I,w,D){return new E(I,D,w.$l)},m=function(I){return r.p(I)+"s"},_=function(I){return I<0},g=function(I){return _(I)?Math.ceil(I):Math.floor(I)},v=function(I){return Math.abs(I)},y=function(I,w){return I?_(I)?{negative:!0,format:""+v(I)+w}:{negative:!1,format:""+I+w}:{negative:!1,format:""}},E=function(){function I(D,T,R){var k=this;if(this.$d={},this.$l=R,D===void 0&&(this.$ms=0,this.parseFromMilliseconds()),T)return p(D*f[m(T)],this);if(typeof D=="number")return this.$ms=D,this.parseFromMilliseconds(),this;if(typeof D=="object")return Object.keys(D).forEach(function(N){k.$d[m(N)]=D[N]}),this.calMilliseconds(),this;if(typeof D=="string"){var M=D.match(d);if(M){var H=M.slice(2).map(function(N){return N!=null?Number(N):0});return this.$d.years=H[0],this.$d.months=H[1],this.$d.weeks=H[2],this.$d.days=H[3],this.$d.hours=H[4],this.$d.minutes=H[5],this.$d.seconds=H[6],this.calMilliseconds(),this}}return this}var w=I.prototype;return w.calMilliseconds=function(){var D=this;this.$ms=Object.keys(this.$d).reduce(function(T,R){return T+(D.$d[R]||0)*f[R]},0)},w.parseFromMilliseconds=function(){var D=this.$ms;this.$d.years=g(D/s),D%=s,this.$d.months=g(D/a),D%=a,this.$d.days=g(D/c),D%=c,this.$d.hours=g(D/u),D%=u,this.$d.minutes=g(D/o),D%=o,this.$d.seconds=g(D/i),D%=i,this.$d.milliseconds=D},w.toISOString=function(){var D=y(this.$d.years,"Y"),T=y(this.$d.months,"M"),R=+this.$d.days||0;this.$d.weeks&&(R+=7*this.$d.weeks);var k=y(R,"D"),M=y(this.$d.hours,"H"),H=y(this.$d.minutes,"M"),N=this.$d.seconds||0;this.$d.milliseconds&&(N+=this.$d.milliseconds/1e3,N=Math.round(1e3*N)/1e3);var G=y(N,"S"),Y=D.negative||T.negative||k.negative||M.negative||H.negative||G.negative,V=M.format||H.format||G.format?"T":"",W=(Y?"-":"")+"P"+D.format+T.format+k.format+V+M.format+H.format+G.format;return W==="P"||W==="-P"?"P0D":W},w.toJSON=function(){return this.toISOString()},w.format=function(D){var T=D||"YYYY-MM-DDTHH:mm:ss",R={Y:this.$d.years,YY:r.s(this.$d.years,2,"0"),YYYY:r.s(this.$d.years,4,"0"),M:this.$d.months,MM:r.s(this.$d.months,2,"0"),D:this.$d.days,DD:r.s(this.$d.days,2,"0"),H:this.$d.hours,HH:r.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:r.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:r.s(this.$d.seconds,2,"0"),SSS:r.s(this.$d.milliseconds,3,"0")};return T.replace(l,function(k,M){return M||String(R[k])})},w.as=function(D){return this.$ms/f[m(D)]},w.get=function(D){var T=this.$ms,R=m(D);return R==="milliseconds"?T%=1e3:T=R==="weeks"?g(T/f[R]):this.$d[R],T||0},w.add=function(D,T,R){var k;return k=T?D*f[m(T)]:h(D)?D.$ms:p(D,this).$ms,p(this.$ms+k*(R?-1:1),this)},w.subtract=function(D,T){return this.add(D,T,!0)},w.locale=function(D){var T=this.clone();return T.$l=D,T},w.clone=function(){return p(this.$ms,this)},w.humanize=function(D){return n().add(this.$ms,"ms").locale(this.$l).fromNow(!D)},w.valueOf=function(){return this.asMilliseconds()},w.milliseconds=function(){return this.get("milliseconds")},w.asMilliseconds=function(){return this.as("milliseconds")},w.seconds=function(){return this.get("seconds")},w.asSeconds=function(){return this.as("seconds")},w.minutes=function(){return this.get("minutes")},w.asMinutes=function(){return this.as("minutes")},w.hours=function(){return this.get("hours")},w.asHours=function(){return this.as("hours")},w.days=function(){return this.get("days")},w.asDays=function(){return this.as("days")},w.weeks=function(){return this.get("weeks")},w.asWeeks=function(){return this.as("weeks")},w.months=function(){return this.get("months")},w.asMonths=function(){return this.as("months")},w.years=function(){return this.get("years")},w.asYears=function(){return this.as("years")},I}(),L=function(I,w,D){return I.add(w.years()*D,"y").add(w.months()*D,"M").add(w.days()*D,"d").add(w.hours()*D,"h").add(w.minutes()*D,"m").add(w.seconds()*D,"s").add(w.milliseconds()*D,"ms")};return function(I,w,D){n=D,r=D().$utils(),D.duration=function(k,M){var H=D.locale();return p(k,{$l:H},M)},D.isDuration=h;var T=w.prototype.add,R=w.prototype.subtract;w.prototype.add=function(k,M){return h(k)?L(this,k,1):T.bind(this)(k,M)},w.prototype.subtract=function(k,M){return h(k)?L(this,k,-1):R.bind(this)(k,M)}}})}(eo)),eo.exports}var to={exports:{}},_m=to.exports,iu;function vm(){return iu||(iu=1,function(e,t){(function(n,r){e.exports=r()})(_m,function(){var n="minute",r=/[+-]\d\d(?::?\d\d)?/g,i=/([+-]|\d\d)/g;return function(o,u,c){var l=u.prototype;c.utc=function(m){var _={date:m,utc:!0,args:arguments};return new u(_)},l.utc=function(m){var _=c(this.toDate(),{locale:this.$L,utc:!0});return m?_.add(this.utcOffset(),n):_},l.local=function(){return c(this.toDate(),{locale:this.$L,utc:!1})};var s=l.parse;l.parse=function(m){m.utc&&(this.$u=!0),this.$utils().u(m.$offset)||(this.$offset=m.$offset),s.call(this,m)};var a=l.init;l.init=function(){if(this.$u){var m=this.$d;this.$y=m.getUTCFullYear(),this.$M=m.getUTCMonth(),this.$D=m.getUTCDate(),this.$W=m.getUTCDay(),this.$H=m.getUTCHours(),this.$m=m.getUTCMinutes(),this.$s=m.getUTCSeconds(),this.$ms=m.getUTCMilliseconds()}else a.call(this)};var d=l.utcOffset;l.utcOffset=function(m,_){var g=this.$utils().u;if(g(m))return this.$u?0:g(this.$offset)?d.call(this):this.$offset;if(typeof m=="string"&&(m=function(L){L===void 0&&(L="");var I=L.match(r);if(!I)return null;var w=(""+I[0]).match(i)||["-",0,0],D=w[0],T=60*+w[1]+ +w[2];return T===0?0:D==="+"?T:-T}(m),m===null))return this;var v=Math.abs(m)<=16?60*m:m,y=this;if(_)return y.$offset=v,y.$u=m===0,y;if(m!==0){var E=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(y=this.local().add(v+E,n)).$offset=v,y.$x.$localOffset=E}else y=this.utc();return y};var f=l.format;l.format=function(m){var _=m||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return f.call(this,_)},l.valueOf=function(){var m=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*m},l.isUTC=function(){return!!this.$u},l.toISOString=function(){return this.toDate().toISOString()},l.toString=function(){return this.toDate().toUTCString()};var h=l.toDate;l.toDate=function(m){return m==="s"&&this.$offset?c(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():h.call(this)};var p=l.diff;l.diff=function(m,_,g){if(m&&this.$u===m.$u)return p.call(this,m,_,g);var v=this.local(),y=c(m).local();return p.call(v,y,_,g)}}})}(to)),to.exports}var no={exports:{}},ym=no.exports,ou;function bm(){return ou||(ou=1,function(e,t){(function(n,r){e.exports=r()})(ym,function(){var n={year:0,month:1,day:2,hour:3,minute:4,second:5},r={};return function(i,o,u){var c,l=function(f,h,p){p===void 0&&(p={});var m=new Date(f),_=function(g,v){v===void 0&&(v={});var y=v.timeZoneName||"short",E=g+"|"+y,L=r[E];return L||(L=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:g,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:y}),r[E]=L),L}(h,p);return _.formatToParts(m)},s=function(f,h){for(var p=l(f,h),m=[],_=0;_<p.length;_+=1){var g=p[_],v=g.type,y=g.value,E=n[v];E>=0&&(m[E]=parseInt(y,10))}var L=m[3],I=L===24?0:L,w=m[0]+"-"+m[1]+"-"+m[2]+" "+I+":"+m[4]+":"+m[5]+":000",D=+f;return(u.utc(w).valueOf()-(D-=D%1e3))/6e4},a=o.prototype;a.tz=function(f,h){f===void 0&&(f=c);var p,m=this.utcOffset(),_=this.toDate(),g=_.toLocaleString("en-US",{timeZone:f}),v=Math.round((_-new Date(g))/1e3/60),y=15*-Math.round(_.getTimezoneOffset()/15)-v;if(!Number(y))p=this.utcOffset(0,h);else if(p=u(g,{locale:this.$L}).$set("millisecond",this.$ms).utcOffset(y,!0),h){var E=p.utcOffset();p=p.add(m-E,"minute")}return p.$x.$timezone=f,p},a.offsetName=function(f){var h=this.$x.$timezone||u.tz.guess(),p=l(this.valueOf(),h,{timeZoneName:f}).find(function(m){return m.type.toLowerCase()==="timezonename"});return p&&p.value};var d=a.startOf;a.startOf=function(f,h){if(!this.$x||!this.$x.$timezone)return d.call(this,f,h);var p=u(this.format("YYYY-MM-DD HH:mm:ss:SSS"),{locale:this.$L});return d.call(p,f,h).tz(this.$x.$timezone,!0)},u.tz=function(f,h,p){var m=p&&h,_=p||h||c,g=s(+u(),_);if(typeof f!="string")return u(f).tz(_);var v=function(I,w,D){var T=I-60*w*1e3,R=s(T,D);if(w===R)return[T,w];var k=s(T-=60*(R-w)*1e3,D);return R===k?[T,R]:[I-60*Math.min(R,k)*1e3,Math.max(R,k)]}(u.utc(f,m).valueOf(),g,_),y=v[0],E=v[1],L=u(y).utcOffset(E);return L.$x.$timezone=_,L},u.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},u.tz.setDefault=function(f){c=f}}})}(no)),no.exports}var ro={exports:{}},Em=ro.exports,su;function Am(){return su||(su=1,function(e,t){(function(n,r){e.exports=r()})(Em,function(){var n="month",r="quarter";return function(i,o){var u=o.prototype;u.quarter=function(s){return this.$utils().u(s)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(s-1))};var c=u.add;u.add=function(s,a){return s=Number(s),this.$utils().p(a)===r?this.add(3*s,n):c.bind(this)(s,a)};var l=u.startOf;u.startOf=function(s,a){var d=this.$utils(),f=!!d.u(a)||a;if(d.p(s)===r){var h=this.quarter()-1;return f?this.month(3*h).startOf(n).startOf("day"):this.month(3*h+2).endOf(n).endOf("day")}return l.bind(this)(s,a)}}})}(ro)),ro.exports}var io={exports:{}},wm=io.exports,au;function Om(){return au||(au=1,function(e,t){(function(n,r){e.exports=r()})(wm,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,i=/\d/,o=/\d\d/,u=/\d\d?/,c=/\d*[^-_:/,()\s\d]+/,l={},s=function(_){return(_=+_)+(_>68?1900:2e3)},a=function(_){return function(g){this[_]=+g}},d=[/[+-]\d\d:?(\d\d)?|Z/,function(_){(this.zone||(this.zone={})).offset=function(g){if(!g||g==="Z")return 0;var v=g.match(/([+-]|\d\d)/g),y=60*v[1]+(+v[2]||0);return y===0?0:v[0]==="+"?-y:y}(_)}],f=function(_){var g=l[_];return g&&(g.indexOf?g:g.s.concat(g.f))},h=function(_,g){var v,y=l.meridiem;if(y){for(var E=1;E<=24;E+=1)if(_.indexOf(y(E,0,g))>-1){v=E>12;break}}else v=_===(g?"pm":"PM");return v},p={A:[c,function(_){this.afternoon=h(_,!1)}],a:[c,function(_){this.afternoon=h(_,!0)}],Q:[i,function(_){this.month=3*(_-1)+1}],S:[i,function(_){this.milliseconds=100*+_}],SS:[o,function(_){this.milliseconds=10*+_}],SSS:[/\d{3}/,function(_){this.milliseconds=+_}],s:[u,a("seconds")],ss:[u,a("seconds")],m:[u,a("minutes")],mm:[u,a("minutes")],H:[u,a("hours")],h:[u,a("hours")],HH:[u,a("hours")],hh:[u,a("hours")],D:[u,a("day")],DD:[o,a("day")],Do:[c,function(_){var g=l.ordinal,v=_.match(/\d+/);if(this.day=v[0],g)for(var y=1;y<=31;y+=1)g(y).replace(/\[|\]/g,"")===_&&(this.day=y)}],w:[u,a("week")],ww:[o,a("week")],M:[u,a("month")],MM:[o,a("month")],MMM:[c,function(_){var g=f("months"),v=(f("monthsShort")||g.map(function(y){return y.slice(0,3)})).indexOf(_)+1;if(v<1)throw new Error;this.month=v%12||v}],MMMM:[c,function(_){var g=f("months").indexOf(_)+1;if(g<1)throw new Error;this.month=g%12||g}],Y:[/[+-]?\d+/,a("year")],YY:[o,function(_){this.year=s(_)}],YYYY:[/\d{4}/,a("year")],Z:d,ZZ:d};function m(_){var g,v;g=_,v=l&&l.formats;for(var y=(_=g.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(R,k,M){var H=M&&M.toUpperCase();return k||v[M]||n[M]||v[H].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(N,G,Y){return G||Y.slice(1)})})).match(r),E=y.length,L=0;L<E;L+=1){var I=y[L],w=p[I],D=w&&w[0],T=w&&w[1];y[L]=T?{regex:D,parser:T}:I.replace(/^\[|\]$/g,"")}return function(R){for(var k={},M=0,H=0;M<E;M+=1){var N=y[M];if(typeof N=="string")H+=N.length;else{var G=N.regex,Y=N.parser,V=R.slice(H),W=G.exec(V)[0];Y.call(k,W),R=R.replace(W,"")}}return function(J){var te=J.afternoon;if(te!==void 0){var ie=J.hours;te?ie<12&&(J.hours+=12):ie===12&&(J.hours=0),delete J.afternoon}}(k),k}}return function(_,g,v){v.p.customParseFormat=!0,_&&_.parseTwoDigitYear&&(s=_.parseTwoDigitYear);var y=g.prototype,E=y.parse;y.parse=function(L){var I=L.date,w=L.utc,D=L.args;this.$u=w;var T=D[1];if(typeof T=="string"){var R=D[2]===!0,k=D[3]===!0,M=R||k,H=D[2];k&&(H=D[2]),l=this.$locale(),!R&&H&&(l=v.Ls[H]),this.$d=function(V,W,J,te){try{if(["x","X"].indexOf(W)>-1)return new Date((W==="X"?1e3:1)*V);var ie=m(W)(V),ue=ie.year,me=ie.month,ve=ie.day,Te=ie.hours,Ae=ie.minutes,U=ie.seconds,K=ie.milliseconds,X=ie.zone,Q=ie.week,fe=new Date,S=ve||(ue||me?1:fe.getDate()),C=ue||fe.getFullYear(),O=0;ue&&!me||(O=me>0?me-1:fe.getMonth());var x,B=Te||0,$=Ae||0,b=U||0,A=K||0;return X?new Date(Date.UTC(C,O,S,B,$,b,A+60*X.offset*1e3)):J?new Date(Date.UTC(C,O,S,B,$,b,A)):(x=new Date(C,O,S,B,$,b,A),Q&&(x=te(x).week(Q).toDate()),x)}catch(P){return new Date("")}}(I,T,w,v),this.init(),H&&H!==!0&&(this.$L=this.locale(H).$L),M&&I!=this.format(T)&&(this.$d=new Date("")),l={}}else if(T instanceof Array)for(var N=T.length,G=1;G<=N;G+=1){D[1]=T[G-1];var Y=v.apply(this,D);if(Y.isValid()){this.$d=Y.$d,this.$L=Y.$L,this.init();break}G===N&&(this.$d=new Date(""))}else E.call(this,L)}}})}(io)),io.exports}var oo={exports:{}},Tm=oo.exports,uu;function Sm(){return uu||(uu=1,function(e,t){(function(n,r){e.exports=r()})(Tm,function(){return function(n,r,i){var o=r.prototype,u=function(f){var h,p=f.date,m=f.utc,_={};if(!((h=p)===null||h instanceof Date||h instanceof Array||o.$utils().u(h)||h.constructor.name!=="Object")){if(!Object.keys(p).length)return new Date;var g=m?i.utc():i();Object.keys(p).forEach(function(T){var R,k;_[R=T,k=o.$utils().p(R),k==="date"?"day":k]=p[T]});var v=_.day||(_.year||_.month>=0?1:g.date()),y=_.year||g.year(),E=_.month>=0?_.month:_.year||_.day?0:g.month(),L=_.hour||0,I=_.minute||0,w=_.second||0,D=_.millisecond||0;return m?new Date(Date.UTC(y,E,v,L,I,w,D)):new Date(y,E,v,L,I,w,D)}return p},c=o.parse;o.parse=function(f){f.date=u.bind(this)(f),c.bind(this)(f)};var l=o.set,s=o.add,a=o.subtract,d=function(f,h,p,m){m===void 0&&(m=1);var _=Object.keys(h),g=this;return _.forEach(function(v){g=f.bind(g)(h[v]*m,v)}),g};o.set=function(f,h){return h=h===void 0?f:h,f.constructor.name==="Object"?d.bind(this)(function(p,m){return l.bind(this)(m,p)},h,f):l.bind(this)(f,h)},o.add=function(f,h){return f.constructor.name==="Object"?d.bind(this)(s,f,h):s.bind(this)(f,h)},o.subtract=function(f,h){return f.constructor.name==="Object"?d.bind(this)(s,f,h,-1):a.bind(this)(f,h)}}})}(oo)),oo.exports}var xn={},lu;function Cm(){if(lu)return xn;lu=1;var e=xn&&xn.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(xn,"__esModule",{value:!0});const t=e(kf());return xn.default=(n,r,i)=>{const o=r.prototype,u=o.format;o.format=function(c){if(!this.isValid())return u.bind(this)(c);const l=c||"YYYY-MM-DDTHH:mm:ssZ",{formats:s={}}=this.$locale(),a=l.replace(/\[([^\]]+)]|LT[S|X|Z]?|lyx|LYX|L{1,4}$/g,d=>{const f=this.minute(),p=this.year()===(0,t.default)().year(),m=f===0?"LTS":"LT";switch(d){case"LTZ":return s[m].replace("A","").replace("a","");case"LTX":return m;case"L":case"LL":case"LLL":case"LLLL":return s[c].replace("LTX",m);case"lyx":return s[p?"l":"ll"];case"LYX":return s[p?"L":"LL"].replace("LTX",m);default:return d}});return u.bind(this)(a)}},xn}var ti={},cu;function Im(){return cu||(cu=1,Object.defineProperty(ti,"__esModule",{value:!0}),ti.default=(e,t,n)=>{const r={lastDay:"[yesterday]",sameDay:"[today]",nextDay:"[tomorrow]",lastWeek:"[last] dddd",sameWeek:"on dddd",nextWeek:"[next] dddd",sameYear:"l",sameElse:"ll",timeFormat:"%c, LTX"};t.prototype.calendar=function(i=void 0,o=!1,u){const c=Object.assign({},this.$locale().calendar||r,u),l=n(i||void 0).startOf("d"),s=this.startOf("d").diff(l,"d"),a=this.isoWeek()===l.isoWeek(),d=this.isoWeek()===l.isoWeek()-1,f=this.isoWeek()===l.isoWeek()+1,h=this.year()===l.year(),m=s===0?"sameDay":s===-1?"lastDay":s===1?"nextDay":a?"sameWeek":d?"lastWeek":f?"nextWeek":h?"sameYear":"sameElse",_=c[m]||r[m],g=typeof _=="function"?_.call(this,n()):this.format(_);return o?this.format(c.timeFormat).replace("%c",g):g},n.calendarFormat=function(i,o){const u=n(o||void 0).startOf("d"),c=n(i||void 0).startOf("d"),l=c.diff(u,"days",!0),s=c.isoWeek()===u.isoWeek(),a=c.isoWeek()===u.isoWeek()-1,d=c.isoWeek()===u.isoWeek()+1,f=c.year()===u.year();return l===0?"sameDay":l===-1?"lastDay":l===1?"nextDay":s?"sameWeek":a?"lastWeek":d?"nextWeek":f?"sameYear":"sameElse"}}),ti}var Pn={},fu;function Dm(){if(fu)return Pn;fu=1;var e=Pn&&Pn.__rest||function(r,i){var o={};for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&i.indexOf(u)<0&&(o[u]=r[u]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,u=Object.getOwnPropertySymbols(r);c<u.length;c++)i.indexOf(u[c])<0&&Object.prototype.propertyIsEnumerable.call(r,u[c])&&(o[u[c]]=r[u[c]]);return o};Object.defineProperty(Pn,"__esModule",{value:!0});const t={year:0,month:0,day:0,hour:0,minute:0,second:0,firstDateWasLater:!1},n={nodiff:"",year:"y",years:"yy",month:"M",months:"MM",day:"d",days:"dd",hour:"h",hours:"hh",minute:"m",minutes:"mm",second:"s",seconds:"ss",delimiter:" "};return Pn.default=(r,i,o)=>{function u(c,l,s){const a=n[l+(c===1?"":"s")];return s[a].replace("%d",c)}i.prototype.humane=function(c=void 0,l=!0,s){const a=Object.assign({},this.$locale().humane||o.Ls.en.humane,s),{daysToRelative:d,startFrom:f,justnow:h,soon:p}=a,m=o(c),_=o.getDuration(this,m),{firstDateWasLater:g}=_,v=e(_,["firstDateWasLater"]),y=Math.abs(this.startOf("day").diff(m.startOf("day"),"day",!0)),E=Math.abs(this.diff(m,f.unit,!0))<=f.value,L=Object.keys(v).findIndex(R=>R===f.unit),I=L>=0?Object.keys(v)[L]:"second",w=a[n["".concat(I,"s")]].replace("%d","").trim(),D=g?p.replace("%u",w):h;return y<=d?E?D:o.durationToString(_,a):this.smartDateTime(m,l,a)},i.prototype.smartDateTime=function(c=void 0,l=!0,s){const a=Object.assign({},this.$locale().humane||o.Ls.en.humane,s),{daysToCalendar:d}=a,f=o(c),p=this.isSame(f,"year")?"l":"ll",m=Math.abs(this.startOf("day").diff(f.startOf("day"),"day",!0));return d>=m?this.calendar(f,l):this.format(l?p.toUpperCase():p)},o.durationToString=function(c,l){const s=Object.assign({},o.Ls[o.locale()].humane||o.Ls.en.humane,l),{skipFromUnit:a}=s,d=Object.assign({},t,c),{firstDateWasLater:f}=d,h=e(d,["firstDateWasLater"]),p=o.Ls[o.locale()].relativeTime||o.Ls.en.relativeTime,m=f?"future":"past",_=Object.keys(h).findIndex(y=>a.includes(y)),v=Object.keys(h).reduce((y,E,L)=>{const I=h[E];return I===0||y.length>0&&_!==-1&&_<=L||y.push(u(I,E,s)),y},[]).join(n.delimiter);return v?p[m].replace("%s",v):s.justnow},o.getDuration=function(c,l){let s=o(c),a=o(l),d;if(s.add(a.utcOffset()-s.utcOffset(),"minutes"),s.isSame(a))return{year:0,month:0,day:0,hour:0,minute:0,second:0,firstDateWasLater:!1};if(s.isAfter(a)){const v=s;s=a,a=v,d=!0}else d=!1;let f=a.year()-s.year(),h=a.month()-s.month(),p=a.date()-s.date(),m=a.hour()-s.hour(),_=a.minute()-s.minute(),g=a.second()-s.second();if(g<0&&(g=60+g,_--),_<0&&(_=60+_,m--),m<0&&(m=24+m,p--),p<0){const v=o("".concat(a.year(),"-").concat(a.month()+1>9?"":"0").concat(a.month()+1),"YYYY-MM").subtract(1,"M").daysInMonth();v<s.date()?p=v+p+(s.date()-v):p=v+p,h--}return h<0&&(h=12+h,f--),{year:f,month:h,day:p,hour:m,minute:_,second:g,firstDateWasLater:d}},o.getHumanePeriod=function(c,l,s,a=!0,d){var f;const h=o(s),p=o(c),m=o(l);if(!c&&!l)return"";if(!c&&l||c&&!l)return(c?p:m).smartDateTime(h,a);const _=Object.assign({},o.Ls[o.locale()].period||o.Ls.en.period,d),g=p.isSame(m,"day"),v=p.isSame(h,"day"),y=Math.abs(p.startOf("day").diff(h.startOf("day"),"day"))<=_.daysToCalendar,E=Math.abs(m.startOf("day").diff(h.startOf("day"),"day"))<=_.daysToCalendar,L=g&&p.format("a")===m.format("a")?"sameMeridiem":g?"sameDay":!(y||E)&&p.isSame(m,"month")?"sameMonth":p.isSame(m,"year")?"sameYear":"others",{startDate:I,endDate:w,startTime:D,endTime:T,format:R}=_[L];let k=a?D:I;const M=a?T:w,H=["LTZ","LTX","LTS","LT"],N=new RegExp("".concat(H.join("|")),"g"),G=(J,te)=>J.replace(/yx/gi,te?"":J[0]);g&&v&&!_.showSameDayToday&&(k=((f=(o.Ls[o.locale()]||o.Ls.en).formats[G(k,!0)].match(N))===null||f===void 0?void 0:f[0])||k);const Y=(J,te,ie)=>{const ue=G(te,J.isSame(h,"y"));if(H.includes(ue)||!ie)return J.format(ue);const ve=ue.match(N),Te=o.Ls[o.locale()].calendar.timeFormat||o.Ls.en.calendar.timeFormat,Ae=Te.match(N),U=ve&&Ae?{timeFormat:Te.replace(Ae[0],ve[0])}:void 0;return J.calendar(h,a,U)},V=k?Y(p,k,y):"",W=M?Y(m,M,E):"";return V&&W?R.replace("%ds",V).replace("%de",W):V||W}},Pn}var du;function To(){if(du)return ge;du=1;var e=ge&&ge.__importDefault||function(v){return v&&v.__esModule?v:{default:v}};Object.defineProperty(ge,"__esModule",{value:!0}),ge.formatDateTime=ge.humane=ge.calendar=ge.customFormat=ge.objectSupport=ge.customParseFormat=ge.quarterOfYear=ge.timezone=ge.utc=ge.duration=ge.isBetween=ge.relativeTime=ge.weekday=ge.isoWeek=ge.localizedFormat=ge.updateLocale=ge.LOCALE_LANGUAGE=void 0;const t=e(kf());ge.formatDateTime=t.default;const n=e(im());ge.updateLocale=n.default;const r=e(sm());ge.localizedFormat=r.default;const i=e(um());ge.isoWeek=i.default;const o=e(cm());ge.weekday=o.default;const u=e(dm());ge.relativeTime=u.default;const c=e(mm());ge.isBetween=c.default;const l=e(gm());ge.duration=l.default;const s=e(vm());ge.utc=s.default;const a=e(bm());ge.timezone=a.default;const d=e(Am());ge.quarterOfYear=d.default;const f=e(Om());ge.customParseFormat=f.default;const h=e(Sm());ge.objectSupport=h.default;const p=e(Cm());ge.customFormat=p.default;const m=e(Im());ge.calendar=m.default;const _=e(Dm());ge.humane=_.default;const g=e(Rf());return t.default.extend(o.default),t.default.extend(n.default),t.default.extend(r.default),t.default.extend(i.default),t.default.extend(u.default,{thresholds:[{l:"s",r:1},{l:"m",r:1},{l:"mm",r:59,d:"minute"},{l:"h",r:1},{l:"hh",r:23,d:"hour"},{l:"d",r:1},{l:"dd",r:29,d:"day"},{l:"M",r:1},{l:"MM",r:11,d:"month"},{l:"y",r:1},{l:"yy",d:"year"}]}),t.default.extend(c.default),t.default.extend(l.default),t.default.extend(s.default),t.default.extend(a.default),t.default.extend(d.default),t.default.extend(f.default),t.default.extend(h.default),t.default.extend(p.default),t.default.extend(m.default),t.default.extend(_.default),t.default.Ls.en=g.default,ge.LOCALE_LANGUAGE={en:"en",ms:"ms",id:"id",ja:"ja",ko:"ko","zh-Hans":"zh-cn","zh-Hant":"zh-tw","zh-Hant-TW":"zh-tw","zh-Hant-HK":"zh-hk"},ge}var hu;function Mm(){if(hu)return ei;hu=1,Object.defineProperty(ei,"__esModule",{value:!0});const e=To(),t={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:(n,r)=>{switch(r){case"W":return"".concat(n,"周");default:return"".concat(n,"日")}},weekStart:1,yearStart:4,formats:{LTS:"Ah点",LT:"Ah点mm分",L:"M月D日LTX",LL:"YYYY年M月D日LTX",LLL:"M月D日ddddLTX",LLLL:"YYYY年M月D日ddddLTX",l:"M月D日",ll:"YYYY年M月D日",lll:"M月D日dddd",llll:"YYYY年M月D日dddd"},calendar:{lastDay:"[昨天]",sameDay:"[今天]",nextDay:"[明天]",lastWeek:"[上]dddd",sameWeek:"dddd",nextWeek:"[下]dddd",sameYear:"l",sameElse:"ll",timeFormat:"%cLTX"},relativeTime:{future:"%s后",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},humane:{daysToRelative:0,daysToCalendar:1,skipFromUnit:"second",startFrom:{value:30,unit:"second"},soon:"几%u后",justnow:"刚刚",s:"1 秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},period:{daysToCalendar:1,showSameDayToday:!1,sameYear:{startDate:"lyx",endDate:"l",startTime:"LYX",endTime:"L",format:"%ds-%de"},sameMonth:{startDate:"lyx",endDate:"D日",startTime:"LYX",endTime:"D日LTX",format:"%ds-%de"},sameDay:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds-%de"},sameMeridiem:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTZ",format:"%ds-%de"},others:{startDate:"ll",endDate:"ll",startTime:"LL",endTime:"LL",format:"%ds-%de"}},meridiem:(n,r)=>{const i=n*100+r;return i<600?"凌晨":i<900?"早上":i<1100?"上午":i<1300?"中午":i<1800?"下午":"晚上"}};return e.formatDateTime.locale(t,null,!0),ei.default=t,ei}Mm();var ni={},mu;function Lm(){if(mu)return ni;mu=1,Object.defineProperty(ni,"__esModule",{value:!0});const e=To(),t={name:"zh-hk",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:(n,r)=>{switch(r){case"W":return"".concat(n,"週");default:return"".concat(n,"日")}},formats:{LTS:"Ah點",LT:"Ah點mm分",L:"M月D日LTX",LL:"YYYY年M月D日LTX",LLL:"M月D日ddddLTX",LLLL:"YYYY年M月D日ddddLTX",l:"M月D日",ll:"YYYY年M月D日",lll:"M月D日dddd",llll:"YYYY年M月D日dddd"},calendar:{lastDay:"[昨日]",sameDay:"[今日]",nextDay:"[明日]",lastWeek:"[上]dddd",sameWeek:"dddd",nextWeek:"[下]dddd",sameYear:"l",sameElse:"ll",timeFormat:"%cLTX"},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},humane:{daysToRelative:0,daysToCalendar:1,skipFromUnit:"second",startFrom:{value:30,unit:"second"},soon:"幾%u後",justnow:"啱啱",s:"1 秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},period:{daysToCalendar:1,showSameDayToday:!1,sameYear:{startDate:"lyx",endDate:"l",startTime:"LYX",endTime:"L",format:"%ds-%de"},sameMonth:{startDate:"lyx",endDate:"D日",startTime:"LYX",endTime:"D日LTX",format:"%ds-%de"},sameDay:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds-%de"},sameMeridiem:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTZ",format:"%ds-%de"},others:{startDate:"ll",endDate:"ll",startTime:"LL",endTime:"LL",format:"%ds-%de"}},meridiem:(n,r)=>{const i=n*100+r;return i<600?"凌晨":i<900?"早晨":i<1100?"上午":i<1300?"中午":i<1800?"下午":"晚上"}};return e.formatDateTime.locale(t,null,!0),ni.default=t,ni}Lm();var ri={},pu;function Rm(){if(pu)return ri;pu=1,Object.defineProperty(ri,"__esModule",{value:!0});const e=To(),t={name:"zh-tw",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:(n,r)=>{switch(r){case"W":return"".concat(n,"週");default:return"".concat(n,"日")}},formats:{LTS:"Ah點",LT:"Ah點mm分",L:"M月D日LTX",LL:"YYYY年M月D日LTX",LLL:"M月D日ddddLTX",LLLL:"YYYY年M月D日ddddLTX",l:"M月D日",ll:"YYYY年M月D日",lll:"M月D日dddd",llll:"YYYY年M月D日dddd"},calendar:{lastDay:"[昨天]",sameDay:"[今天]",nextDay:"[明天]",lastWeek:"[上]dddd",sameWeek:"dddd",nextWeek:"[下]dddd",sameYear:"l",sameElse:"ll",timeFormat:"%cLTX"},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},humane:{daysToRelative:0,daysToCalendar:1,skipFromUnit:"second",startFrom:{value:30,unit:"second"},soon:"幾%u後",justnow:"剛剛",s:"1 秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"},period:{daysToCalendar:1,showSameDayToday:!1,sameYear:{startDate:"lyx",endDate:"l",startTime:"LYX",endTime:"L",format:"%ds-%de"},sameMonth:{startDate:"lyx",endDate:"D日",startTime:"LYX",endTime:"D日LTX",format:"%ds-%de"},sameDay:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTX",format:"%ds-%de"},sameMeridiem:{startDate:"lyx",endDate:"",startTime:"LYX",endTime:"LTZ",format:"%ds-%de"},others:{startDate:"ll",endDate:"ll",startTime:"LL",endTime:"LL",format:"%ds-%de"}},meridiem:(n,r)=>{const i=n*100+r;return i<600?"凌晨":i<900?"早上":i<1100?"上午":i<1300?"中午":i<1800?"下午":"晚上"}};return e.formatDateTime.locale(t,null,!0),ri.default=t,ri}Rm();var vr=To();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ya(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Re={},Vn=[],Ct=()=>{},km=()=>!1,So=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ba=e=>e.startsWith("onUpdate:"),Ke=Object.assign,Ea=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Nm=Object.prototype.hasOwnProperty,Ie=(e,t)=>Nm.call(e,t),le=Array.isArray,Wn=e=>Co(e)==="[object Map]",Nf=e=>Co(e)==="[object Set]",he=e=>typeof e=="function",Pe=e=>typeof e=="string",Kt=e=>typeof e=="symbol",xe=e=>e!==null&&typeof e=="object",xf=e=>(xe(e)||he(e))&&he(e.then)&&he(e.catch),Pf=Object.prototype.toString,Co=e=>Pf.call(e),xm=e=>Co(e).slice(8,-1),Ff=e=>Co(e)==="[object Object]",Aa=e=>Pe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,yr=ya(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Io=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Pm=/-(\w)/g,At=Io(e=>e.replace(Pm,(t,n)=>n?n.toUpperCase():"")),Fm=/\B([A-Z])/g,Ln=Io(e=>e.replace(Fm,"-$1").toLowerCase()),Do=Io(e=>e.charAt(0).toUpperCase()+e.slice(1)),Jo=Io(e=>e?"on".concat(Do(e)):""),un=(e,t)=>!Object.is(e,t),so=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Bf=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},js=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Bm=e=>{const t=Pe(e)?Number(e):NaN;return isNaN(t)?e:t};let gu;const Mo=()=>gu||(gu=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function dt(e){if(le(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],i=Pe(r)?Um(r):dt(r);if(i)for(const o in i)t[o]=i[o]}return t}else if(Pe(e)||xe(e))return e}const $m=/;(?![^(]*\))/g,Hm=/:([^]+)/,jm=/\/\*[^]*?\*\//g;function Um(e){const t={};return e.replace(jm,"").split($m).forEach(n=>{if(n){const r=n.split(Hm);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function $e(e){let t="";if(Pe(e))t=e;else if(le(e))for(let n=0;n<e.length;n++){const r=$e(e[n]);r&&(t+=r+" ")}else if(xe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function $f(e){if(!e)return null;let{class:t,style:n}=e;return t&&!Pe(t)&&(e.class=$e(t)),n&&(e.style=dt(n)),e}const Ym="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",qm=ya(Ym);function Hf(e){return!!e||e===""}const jf=e=>!!(e&&e.__v_isRef===!0),Me=e=>Pe(e)?e:e==null?"":le(e)||xe(e)&&(e.toString===Pf||!he(e.toString))?jf(e)?Me(e.value):JSON.stringify(e,Uf,2):String(e),Uf=(e,t)=>jf(t)?Uf(e,t.value):Wn(t)?{["Map(".concat(t.size,")")]:[...t.entries()].reduce((n,[r,i],o)=>(n[Zo(r,o)+" =>"]=i,n),{})}:Nf(t)?{["Set(".concat(t.size,")")]:[...t.values()].map(n=>Zo(n))}:Kt(t)?Zo(t):xe(t)&&!le(t)&&!Ff(t)?String(t):t,Zo=(e,t="")=>{var n;return Kt(e)?"Symbol(".concat((n=e.description)!=null?n:t,")"):e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let it;class Yf{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=it,!t&&it&&(this.index=(it.scopes||(it.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=it;try{return it=this,t()}finally{it=n}}}on(){it=this}off(){it=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function wa(e){return new Yf(e)}function qf(){return it}function Gm(e,t=!1){it&&it.cleanups.push(e)}let ke;const es=new WeakSet;class Gf{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,it&&it.active&&it.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,es.has(this)&&(es.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Wf(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,_u(this),zf(this);const t=ke,n=It;ke=this,It=!0;try{return this.fn()}finally{Xf(this),ke=t,It=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Sa(t);this.deps=this.depsTail=void 0,_u(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?es.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Us(this)&&this.run()}get dirty(){return Us(this)}}let Vf=0,br,Er;function Wf(e,t=!1){if(e.flags|=8,t){e.next=Er,Er=e;return}e.next=br,br=e}function Oa(){Vf++}function Ta(){if(--Vf>0)return;if(Er){let t=Er;for(Er=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;br;){let t=br;for(br=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function zf(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Xf(e){let t,n=e.depsTail,r=n;for(;r;){const i=r.prevDep;r.version===-1?(r===n&&(n=i),Sa(r),Vm(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=i}e.deps=t,e.depsTail=n}function Us(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Kf(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Kf(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ir))return;e.globalVersion=Ir;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Us(e)){e.flags&=-3;return}const n=ke,r=It;ke=e,It=!0;try{zf(e);const i=e.fn(e._value);(t.version===0||un(i,e._value))&&(e._value=i,t.version++)}catch(i){throw t.version++,i}finally{ke=n,It=r,Xf(e),e.flags&=-3}}function Sa(e,t=!1){const{dep:n,prevSub:r,nextSub:i}=e;if(r&&(r.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Sa(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Vm(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let It=!0;const Qf=[];function hn(){Qf.push(It),It=!1}function mn(){const e=Qf.pop();It=e===void 0?!0:e}function _u(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ke;ke=void 0;try{t()}finally{ke=n}}}let Ir=0;class Wm{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ca{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ke||!It||ke===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ke)n=this.activeLink=new Wm(ke,this),ke.deps?(n.prevDep=ke.depsTail,ke.depsTail.nextDep=n,ke.depsTail=n):ke.deps=ke.depsTail=n,Jf(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ke.depsTail,n.nextDep=void 0,ke.depsTail.nextDep=n,ke.depsTail=n,ke.deps===n&&(ke.deps=r)}return n}trigger(t){this.version++,Ir++,this.notify(t)}notify(t){Oa();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ta()}}}function Jf(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Jf(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const ho=new WeakMap,Cn=Symbol(""),Ys=Symbol(""),Dr=Symbol("");function Je(e,t,n){if(It&&ke){let r=ho.get(e);r||ho.set(e,r=new Map);let i=r.get(n);i||(r.set(n,i=new Ca),i.map=r,i.key=n),i.track()}}function Vt(e,t,n,r,i,o){const u=ho.get(e);if(!u){Ir++;return}const c=l=>{l&&l.trigger()};if(Oa(),t==="clear")u.forEach(c);else{const l=le(e),s=l&&Aa(n);if(l&&n==="length"){const a=Number(r);u.forEach((d,f)=>{(f==="length"||f===Dr||!Kt(f)&&f>=a)&&c(d)})}else switch((n!==void 0||u.has(void 0))&&c(u.get(n)),s&&c(u.get(Dr)),t){case"add":l?s&&c(u.get("length")):(c(u.get(Cn)),Wn(e)&&c(u.get(Ys)));break;case"delete":l||(c(u.get(Cn)),Wn(e)&&c(u.get(Ys)));break;case"set":Wn(e)&&c(u.get(Cn));break}}Ta()}function zm(e,t){const n=ho.get(e);return n&&n.get(t)}function Fn(e){const t=we(e);return t===e?t:(Je(t,"iterate",Dr),Et(e)?t:t.map(Ze))}function Lo(e){return Je(e=we(e),"iterate",Dr),e}const Xm={__proto__:null,[Symbol.iterator](){return ts(this,Symbol.iterator,Ze)},concat(...e){return Fn(this).concat(...e.map(t=>le(t)?Fn(t):t))},entries(){return ts(this,"entries",e=>(e[1]=Ze(e[1]),e))},every(e,t){return Ht(this,"every",e,t,void 0,arguments)},filter(e,t){return Ht(this,"filter",e,t,n=>n.map(Ze),arguments)},find(e,t){return Ht(this,"find",e,t,Ze,arguments)},findIndex(e,t){return Ht(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ht(this,"findLast",e,t,Ze,arguments)},findLastIndex(e,t){return Ht(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ht(this,"forEach",e,t,void 0,arguments)},includes(...e){return ns(this,"includes",e)},indexOf(...e){return ns(this,"indexOf",e)},join(e){return Fn(this).join(e)},lastIndexOf(...e){return ns(this,"lastIndexOf",e)},map(e,t){return Ht(this,"map",e,t,void 0,arguments)},pop(){return rr(this,"pop")},push(...e){return rr(this,"push",e)},reduce(e,...t){return vu(this,"reduce",e,t)},reduceRight(e,...t){return vu(this,"reduceRight",e,t)},shift(){return rr(this,"shift")},some(e,t){return Ht(this,"some",e,t,void 0,arguments)},splice(...e){return rr(this,"splice",e)},toReversed(){return Fn(this).toReversed()},toSorted(e){return Fn(this).toSorted(e)},toSpliced(...e){return Fn(this).toSpliced(...e)},unshift(...e){return rr(this,"unshift",e)},values(){return ts(this,"values",Ze)}};function ts(e,t,n){const r=Lo(e),i=r[t]();return r!==e&&!Et(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=n(o.value)),o}),i}const Km=Array.prototype;function Ht(e,t,n,r,i,o){const u=Lo(e),c=u!==e&&!Et(e),l=u[t];if(l!==Km[t]){const d=l.apply(e,o);return c?Ze(d):d}let s=n;u!==e&&(c?s=function(d,f){return n.call(this,Ze(d),f,e)}:n.length>2&&(s=function(d,f){return n.call(this,d,f,e)}));const a=l.call(u,s,r);return c&&i?i(a):a}function vu(e,t,n,r){const i=Lo(e);let o=n;return i!==e&&(Et(e)?n.length>3&&(o=function(u,c,l){return n.call(this,u,c,l,e)}):o=function(u,c,l){return n.call(this,u,Ze(c),l,e)}),i[t](o,...r)}function ns(e,t,n){const r=we(e);Je(r,"iterate",Dr);const i=r[t](...n);return(i===-1||i===!1)&&Ma(n[0])?(n[0]=we(n[0]),r[t](...n)):i}function rr(e,t,n=[]){hn(),Oa();const r=we(e)[t].apply(e,n);return Ta(),mn(),r}const Qm=ya("__proto__,__v_isRef,__isVue"),Zf=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Kt));function Jm(e){Kt(e)||(e=String(e));const t=we(this);return Je(t,"has",e),t.hasOwnProperty(e)}class ed{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(i?o?up:id:o?rd:nd).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const u=le(t);if(!i){let l;if(u&&(l=Xm[n]))return l;if(n==="hasOwnProperty")return Jm}const c=Reflect.get(t,n,je(t)?t:r);return(Kt(n)?Zf.has(n):Qm(n))||(i||Je(t,"get",n),o)?c:je(c)?u&&Aa(n)?c:c.value:xe(c)?i?sd(c):ln(c):c}}class td extends ed{constructor(t=!1){super(!1,t)}set(t,n,r,i){let o=t[n];if(!this._isShallow){const l=Dn(o);if(!Et(r)&&!Dn(r)&&(o=we(o),r=we(r)),!le(t)&&je(o)&&!je(r))return l?!1:(o.value=r,!0)}const u=le(t)&&Aa(n)?Number(n)<t.length:Ie(t,n),c=Reflect.set(t,n,r,je(t)?t:i);return t===we(i)&&(u?un(r,o)&&Vt(t,"set",n,r):Vt(t,"add",n,r)),c}deleteProperty(t,n){const r=Ie(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&r&&Vt(t,"delete",n,void 0),i}has(t,n){const r=Reflect.has(t,n);return(!Kt(n)||!Zf.has(n))&&Je(t,"has",n),r}ownKeys(t){return Je(t,"iterate",le(t)?"length":Cn),Reflect.ownKeys(t)}}class Zm extends ed{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ep=new td,tp=new Zm,np=new td(!0);const qs=e=>e,ii=e=>Reflect.getPrototypeOf(e);function rp(e,t,n){return function(...r){const i=this.__v_raw,o=we(i),u=Wn(o),c=e==="entries"||e===Symbol.iterator&&u,l=e==="keys"&&u,s=i[e](...r),a=n?qs:t?Gs:Ze;return!t&&Je(o,"iterate",l?Ys:Cn),{next(){const{value:d,done:f}=s.next();return f?{value:d,done:f}:{value:c?[a(d[0]),a(d[1])]:a(d),done:f}},[Symbol.iterator](){return this}}}}function oi(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ip(e,t){const n={get(i){const o=this.__v_raw,u=we(o),c=we(i);e||(un(i,c)&&Je(u,"get",i),Je(u,"get",c));const{has:l}=ii(u),s=t?qs:e?Gs:Ze;if(l.call(u,i))return s(o.get(i));if(l.call(u,c))return s(o.get(c));o!==u&&o.get(i)},get size(){const i=this.__v_raw;return!e&&Je(we(i),"iterate",Cn),Reflect.get(i,"size",i)},has(i){const o=this.__v_raw,u=we(o),c=we(i);return e||(un(i,c)&&Je(u,"has",i),Je(u,"has",c)),i===c?o.has(i):o.has(i)||o.has(c)},forEach(i,o){const u=this,c=u.__v_raw,l=we(c),s=t?qs:e?Gs:Ze;return!e&&Je(l,"iterate",Cn),c.forEach((a,d)=>i.call(o,s(a),s(d),u))}};return Ke(n,e?{add:oi("add"),set:oi("set"),delete:oi("delete"),clear:oi("clear")}:{add(i){!t&&!Et(i)&&!Dn(i)&&(i=we(i));const o=we(this);return ii(o).has.call(o,i)||(o.add(i),Vt(o,"add",i,i)),this},set(i,o){!t&&!Et(o)&&!Dn(o)&&(o=we(o));const u=we(this),{has:c,get:l}=ii(u);let s=c.call(u,i);s||(i=we(i),s=c.call(u,i));const a=l.call(u,i);return u.set(i,o),s?un(o,a)&&Vt(u,"set",i,o):Vt(u,"add",i,o),this},delete(i){const o=we(this),{has:u,get:c}=ii(o);let l=u.call(o,i);l||(i=we(i),l=u.call(o,i)),c&&c.call(o,i);const s=o.delete(i);return l&&Vt(o,"delete",i,void 0),s},clear(){const i=we(this),o=i.size!==0,u=i.clear();return o&&Vt(i,"clear",void 0,void 0),u}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=rp(i,e,t)}),n}function Ia(e,t){const n=ip(e,t);return(r,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?r:Reflect.get(Ie(n,i)&&i in r?n:r,i,o)}const op={get:Ia(!1,!1)},sp={get:Ia(!1,!0)},ap={get:Ia(!0,!1)};const nd=new WeakMap,rd=new WeakMap,id=new WeakMap,up=new WeakMap;function lp(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function cp(e){return e.__v_skip||!Object.isExtensible(e)?0:lp(xm(e))}function ln(e){return Dn(e)?e:Da(e,!1,ep,op,nd)}function od(e){return Da(e,!1,np,sp,rd)}function sd(e){return Da(e,!0,tp,ap,id)}function Da(e,t,n,r,i){if(!xe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=i.get(e);if(o)return o;const u=cp(e);if(u===0)return e;const c=new Proxy(e,u===2?r:n);return i.set(e,c),c}function cn(e){return Dn(e)?cn(e.__v_raw):!!(e&&e.__v_isReactive)}function Dn(e){return!!(e&&e.__v_isReadonly)}function Et(e){return!!(e&&e.__v_isShallow)}function Ma(e){return e?!!e.__v_raw:!1}function we(e){const t=e&&e.__v_raw;return t?we(t):e}function La(e){return!Ie(e,"__v_skip")&&Object.isExtensible(e)&&Bf(e,"__v_skip",!0),e}const Ze=e=>xe(e)?ln(e):e,Gs=e=>xe(e)?sd(e):e;function je(e){return e?e.__v_isRef===!0:!1}function _e(e){return ud(e,!1)}function ad(e){return ud(e,!0)}function ud(e,t){return je(e)?e:new fp(e,t)}class fp{constructor(t,n){this.dep=new Ca,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:we(t),this._value=n?t:Ze(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||Et(t)||Dn(t);t=r?t:we(t),un(t,n)&&(this._rawValue=t,this._value=r?t:Ze(t),this.dep.trigger())}}function Z(e){return je(e)?e.value:e}const dp={get:(e,t,n)=>t==="__v_raw"?e:Z(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const i=e[t];return je(i)&&!je(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};function ld(e){return cn(e)?e:new Proxy(e,dp)}function Ro(e){const t=le(e)?new Array(e.length):{};for(const n in e)t[n]=mp(e,n);return t}class hp{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return zm(we(this._object),this._key)}}function mp(e,t,n){const r=e[t];return je(r)?r:new hp(e,t,n)}class pp{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ca(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ir-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ke!==this)return Wf(this,!0),!0}get value(){const t=this.dep.track();return Kf(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function gp(e,t,n=!1){let r,i;return he(e)?r=e:(r=e.get,i=e.set),new pp(r,i,n)}const si={},mo=new WeakMap;let On;function _p(e,t=!1,n=On){if(n){let r=mo.get(n);r||mo.set(n,r=[]),r.push(e)}}function vp(e,t,n=Re){const{immediate:r,deep:i,once:o,scheduler:u,augmentJob:c,call:l}=n,s=E=>i?E:Et(E)||i===!1||i===0?Wt(E,1):Wt(E);let a,d,f,h,p=!1,m=!1;if(je(e)?(d=()=>e.value,p=Et(e)):cn(e)?(d=()=>s(e),p=!0):le(e)?(m=!0,p=e.some(E=>cn(E)||Et(E)),d=()=>e.map(E=>{if(je(E))return E.value;if(cn(E))return s(E);if(he(E))return l?l(E,2):E()})):he(e)?t?d=l?()=>l(e,2):e:d=()=>{if(f){hn();try{f()}finally{mn()}}const E=On;On=a;try{return l?l(e,3,[h]):e(h)}finally{On=E}}:d=Ct,t&&i){const E=d,L=i===!0?1/0:i;d=()=>Wt(E(),L)}const _=qf(),g=()=>{a.stop(),_&&_.active&&Ea(_.effects,a)};if(o&&t){const E=t;t=(...L)=>{E(...L),g()}}let v=m?new Array(e.length).fill(si):si;const y=E=>{if(!(!(a.flags&1)||!a.dirty&&!E))if(t){const L=a.run();if(i||p||(m?L.some((I,w)=>un(I,v[w])):un(L,v))){f&&f();const I=On;On=a;try{const w=[L,v===si?void 0:m&&v[0]===si?[]:v,h];l?l(t,3,w):t(...w),v=L}finally{On=I}}}else a.run()};return c&&c(y),a=new Gf(d),a.scheduler=u?()=>u(y,!1):y,h=E=>_p(E,!1,a),f=a.onStop=()=>{const E=mo.get(a);if(E){if(l)l(E,4);else for(const L of E)L();mo.delete(a)}},t?r?y(!0):v=a.run():u?u(y.bind(null,!0),!0):a.run(),g.pause=a.pause.bind(a),g.resume=a.resume.bind(a),g.stop=g,g}function Wt(e,t=1/0,n){if(t<=0||!xe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,je(e))Wt(e.value,t,n);else if(le(e))for(let r=0;r<e.length;r++)Wt(e[r],t,n);else if(Nf(e)||Wn(e))e.forEach(r=>{Wt(r,t,n)});else if(Ff(e)){for(const r in e)Wt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Wt(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ur(e,t,n,r){try{return r?e(...r):e()}catch(i){ko(i,t,n)}}function Dt(e,t,n,r){if(he(e)){const i=Ur(e,t,n,r);return i&&xf(i)&&i.catch(o=>{ko(o,t,n)}),i}if(le(e)){const i=[];for(let o=0;o<e.length;o++)i.push(Dt(e[o],t,n,r));return i}}function ko(e,t,n,r=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||Re;if(t){let c=t.parent;const l=t.proxy,s="https://vuejs.org/error-reference/#runtime-".concat(n);for(;c;){const a=c.ec;if(a){for(let d=0;d<a.length;d++)if(a[d](e,l,s)===!1)return}c=c.parent}if(o){hn(),Ur(o,null,10,[e,l,s]),mn();return}}yp(e,n,i,r,u)}function yp(e,t,n,r=!0,i=!1){if(i)throw e;console.error(e)}const ot=[];let xt=-1;const zn=[];let rn=null,Un=0;const cd=Promise.resolve();let po=null;function No(e){const t=po||cd;return e?t.then(this?e.bind(this):e):t}function bp(e){let t=xt+1,n=ot.length;for(;t<n;){const r=t+n>>>1,i=ot[r],o=Mr(i);o<e||o===e&&i.flags&2?t=r+1:n=r}return t}function Ra(e){if(!(e.flags&1)){const t=Mr(e),n=ot[ot.length-1];!n||!(e.flags&2)&&t>=Mr(n)?ot.push(e):ot.splice(bp(t),0,e),e.flags|=1,fd()}}function fd(){po||(po=cd.then(md))}function dd(e){le(e)?zn.push(...e):rn&&e.id===-1?rn.splice(Un+1,0,e):e.flags&1||(zn.push(e),e.flags|=1),fd()}function yu(e,t,n=xt+1){for(;n<ot.length;n++){const r=ot[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;ot.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function hd(e){if(zn.length){const t=[...new Set(zn)].sort((n,r)=>Mr(n)-Mr(r));if(zn.length=0,rn){rn.push(...t);return}for(rn=t,Un=0;Un<rn.length;Un++){const n=rn[Un];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}rn=null,Un=0}}const Mr=e=>e.id==null?e.flags&2?-1:1/0:e.id;function md(e){try{for(xt=0;xt<ot.length;xt++){const t=ot[xt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ur(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;xt<ot.length;xt++){const t=ot[xt];t&&(t.flags&=-2)}xt=-1,ot.length=0,hd(),po=null,(ot.length||zn.length)&&md()}}let Ve=null,pd=null;function go(e){const t=Ve;return Ve=e,pd=e&&e.type.__scopeId||null,t}function He(e,t=Ve,n){if(!t||e._n)return e;const r=(...i)=>{r._d&&Mu(-1);const o=go(t);let u;try{u=e(...i)}finally{go(o),r._d&&Mu(1)}return u};return r._n=!0,r._c=!0,r._d=!0,r}function Vs(e,t){if(Ve===null)return e;const n=$o(Ve),r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,u,c,l=Re]=t[i];o&&(he(o)&&(o={mounted:o,updated:o}),o.deep&&Wt(u),r.push({dir:o,instance:n,value:u,oldValue:void 0,arg:c,modifiers:l}))}return e}function vn(e,t,n,r){const i=e.dirs,o=t&&t.dirs;for(let u=0;u<i.length;u++){const c=i[u];o&&(c.oldValue=o[u].value);let l=c.dir[r];l&&(hn(),Dt(l,n,8,[e.el,c,e,t]),mn())}}const Ep=Symbol("_vte"),gd=e=>e.__isTeleport,on=Symbol("_leaveCb"),ai=Symbol("_enterCb");function Ap(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return $t(()=>{e.isMounted=!0}),Yr(()=>{e.isUnmounting=!0}),e}const yt=[Function,Array],_d={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:yt,onEnter:yt,onAfterEnter:yt,onEnterCancelled:yt,onBeforeLeave:yt,onLeave:yt,onAfterLeave:yt,onLeaveCancelled:yt,onBeforeAppear:yt,onAppear:yt,onAfterAppear:yt,onAppearCancelled:yt},vd=e=>{const t=e.subTree;return t.component?vd(t.component):t},wp={name:"BaseTransition",props:_d,setup(e,{slots:t}){const n=dn(),r=Ap();return()=>{const i=t.default&&Ed(t.default(),!0);if(!i||!i.length)return;const o=yd(i),u=we(e),{mode:c}=u;if(r.isLeaving)return rs(o);const l=bu(o);if(!l)return rs(o);let s=Ws(l,u,r,n,d=>s=d);l.type!==st&&Lr(l,s);let a=n.subTree&&bu(n.subTree);if(a&&a.type!==st&&!Tn(l,a)&&vd(n).type!==st){let d=Ws(a,u,r,n);if(Lr(a,d),c==="out-in"&&l.type!==st)return r.isLeaving=!0,d.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete d.afterLeave,a=void 0},rs(o);c==="in-out"&&l.type!==st?d.delayLeave=(f,h,p)=>{const m=bd(r,a);m[String(a.key)]=a,f[on]=()=>{h(),f[on]=void 0,delete s.delayedLeave,a=void 0},s.delayedLeave=()=>{p(),delete s.delayedLeave,a=void 0}}:a=void 0}else a&&(a=void 0);return o}}};function yd(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==st){t=n;break}}return t}const Op=wp;function bd(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Ws(e,t,n,r,i){const{appear:o,mode:u,persisted:c=!1,onBeforeEnter:l,onEnter:s,onAfterEnter:a,onEnterCancelled:d,onBeforeLeave:f,onLeave:h,onAfterLeave:p,onLeaveCancelled:m,onBeforeAppear:_,onAppear:g,onAfterAppear:v,onAppearCancelled:y}=t,E=String(e.key),L=bd(n,e),I=(T,R)=>{T&&Dt(T,r,9,R)},w=(T,R)=>{const k=R[1];I(T,R),le(T)?T.every(M=>M.length<=1)&&k():T.length<=1&&k()},D={mode:u,persisted:c,beforeEnter(T){let R=l;if(!n.isMounted)if(o)R=_||l;else return;T[on]&&T[on](!0);const k=L[E];k&&Tn(e,k)&&k.el[on]&&k.el[on](),I(R,[T])},enter(T){let R=s,k=a,M=d;if(!n.isMounted)if(o)R=g||s,k=v||a,M=y||d;else return;let H=!1;const N=T[ai]=G=>{H||(H=!0,G?I(M,[T]):I(k,[T]),D.delayedLeave&&D.delayedLeave(),T[ai]=void 0)};R?w(R,[T,N]):N()},leave(T,R){const k=String(e.key);if(T[ai]&&T[ai](!0),n.isUnmounting)return R();I(f,[T]);let M=!1;const H=T[on]=N=>{M||(M=!0,R(),N?I(m,[T]):I(p,[T]),T[on]=void 0,L[k]===e&&delete L[k])};L[k]=e,h?w(h,[T,H]):H()},clone(T){const R=Ws(T,t,n,r,i);return i&&i(R),R}};return D}function rs(e){if(xo(e))return e=fn(e),e.children=null,e}function bu(e){if(!xo(e))return gd(e.type)&&e.children?yd(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&he(n.default))return n.default()}}function Lr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Lr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ed(e,t=!1,n){let r=[],i=0;for(let o=0;o<e.length;o++){let u=e[o];const c=n==null?u.key:String(n)+String(u.key!=null?u.key:o);u.type===Be?(u.patchFlag&128&&i++,r=r.concat(Ed(u.children,t,c))):(t||u.type!==st)&&r.push(c!=null?fn(u,{key:c}):u)}if(i>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Ge(e,t){return he(e)?Ke({name:e.name},t,{setup:e}):e}function Ad(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function _o(e,t,n,r,i=!1){if(le(e)){e.forEach((p,m)=>_o(p,t&&(le(t)?t[m]:t),n,r,i));return}if(Xn(r)&&!i){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&_o(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?$o(r.component):r.el,u=i?null:o,{i:c,r:l}=e,s=t&&t.r,a=c.refs===Re?c.refs={}:c.refs,d=c.setupState,f=we(d),h=d===Re?()=>!1:p=>Ie(f,p);if(s!=null&&s!==l&&(Pe(s)?(a[s]=null,h(s)&&(d[s]=null)):je(s)&&(s.value=null)),he(l))Ur(l,c,12,[u,a]);else{const p=Pe(l),m=je(l);if(p||m){const _=()=>{if(e.f){const g=p?h(l)?d[l]:a[l]:l.value;i?le(g)&&Ea(g,o):le(g)?g.includes(o)||g.push(o):p?(a[l]=[o],h(l)&&(d[l]=a[l])):(l.value=[o],e.k&&(a[e.k]=l.value))}else p?(a[l]=u,h(l)&&(d[l]=u)):m&&(l.value=u,e.k&&(a[e.k]=u))};u?(_.id=-1,ft(_,n)):_()}}}Mo().requestIdleCallback;Mo().cancelIdleCallback;const Xn=e=>!!e.type.__asyncLoader,xo=e=>e.type.__isKeepAlive;function Tp(e,t){wd(e,"a",t)}function Sp(e,t){wd(e,"da",t)}function wd(e,t,n=Xe){const r=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Po(t,r,n),n){let i=n.parent;for(;i&&i.parent;)xo(i.parent.vnode)&&Cp(r,t,n,i),i=i.parent}}function Cp(e,t,n,r){const i=Po(t,e,r,!0);tr(()=>{Ea(r[t],i)},n)}function Po(e,t,n=Xe,r=!1){if(n){const i=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...u)=>{hn();const c=Vr(n),l=Dt(t,n,e,u);return c(),mn(),l});return r?i.unshift(o):i.push(o),o}}const Qt=e=>(t,n=Xe)=>{(!Nr||e==="sp")&&Po(e,(...r)=>t(...r),n)},Fo=Qt("bm"),$t=Qt("m"),Od=Qt("bu"),Ip=Qt("u"),Yr=Qt("bum"),tr=Qt("um"),Dp=Qt("sp"),Mp=Qt("rtg"),Lp=Qt("rtc");function Rp(e,t=Xe){Po("ec",e,t)}const kp="components",Td=Symbol.for("v-ndc");function Np(e){return Pe(e)?xp(kp,e,!1)||e:e||Td}function xp(e,t,n=!0,r=!1){const i=Ve||Xe;if(i){const o=i.type;{const c=Ag(o,!1);if(c&&(c===t||c===At(t)||c===Do(At(t))))return o}const u=Eu(i[e]||o[e],t)||Eu(i.appContext[e],t);return!u&&r?o:u}}function Eu(e,t){return e&&(e[t]||e[At(t)]||e[Do(At(t))])}function qr(e,t,n,r){let i;const o=n,u=le(e);if(u||Pe(e)){const c=u&&cn(e);let l=!1;c&&(l=!Et(e),e=Lo(e)),i=new Array(e.length);for(let s=0,a=e.length;s<a;s++)i[s]=t(l?Ze(e[s]):e[s],s,void 0,o)}else if(typeof e=="number"){i=new Array(e);for(let c=0;c<e;c++)i[c]=t(c+1,c,void 0,o)}else if(xe(e))if(e[Symbol.iterator])i=Array.from(e,(c,l)=>t(c,l,void 0,o));else{const c=Object.keys(e);i=new Array(c.length);for(let l=0,s=c.length;l<s;l++){const a=c[l];i[l]=t(e[a],a,l,o)}}else i=[];return i}function Mn(e,t){for(let n=0;n<t.length;n++){const r=t[n];if(le(r))for(let i=0;i<r.length;i++)e[r[i].name]=r[i].fn;else r&&(e[r.name]=r.key?(...i)=>{const o=r.fn(...i);return o&&(o.key=r.key),o}:r.fn)}return e}function Qe(e,t,n={},r,i){if(Ve.ce||Ve.parent&&Xn(Ve.parent)&&Ve.parent.ce)return t!=="default"&&(n.name=t),ne(),et(Be,null,[Ee("slot",n,r&&r())],64);let o=e[t];o&&o._c&&(o._d=!1),ne();const u=o&&Sd(o(n)),c=n.key||u&&u.key,l=et(Be,{key:(c&&!Kt(c)?c:"_".concat(t))+(!u&&r?"_fb":"")},u||(r?r():[]),u&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function Sd(e){return e.some(t=>kr(t)?!(t.type===st||t.type===Be&&!Sd(t.children)):!0)?e:null}const zs=e=>e?Xd(e)?$o(e):zs(e.parent):null,Ar=Ke(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>zs(e.parent),$root:e=>zs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Dd(e),$forceUpdate:e=>e.f||(e.f=()=>{Ra(e.update)}),$nextTick:e=>e.n||(e.n=No.bind(e.proxy)),$watch:e=>og.bind(e)}),is=(e,t)=>e!==Re&&!e.__isScriptSetup&&Ie(e,t),Pp={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:i,props:o,accessCache:u,type:c,appContext:l}=e;let s;if(t[0]!=="$"){const h=u[t];if(h!==void 0)switch(h){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return o[t]}else{if(is(r,t))return u[t]=1,r[t];if(i!==Re&&Ie(i,t))return u[t]=2,i[t];if((s=e.propsOptions[0])&&Ie(s,t))return u[t]=3,o[t];if(n!==Re&&Ie(n,t))return u[t]=4,n[t];Xs&&(u[t]=0)}}const a=Ar[t];let d,f;if(a)return t==="$attrs"&&Je(e.attrs,"get",""),a(e);if((d=c.__cssModules)&&(d=d[t]))return d;if(n!==Re&&Ie(n,t))return u[t]=4,n[t];if(f=l.config.globalProperties,Ie(f,t))return f[t]},set({_:e},t,n){const{data:r,setupState:i,ctx:o}=e;return is(i,t)?(i[t]=n,!0):r!==Re&&Ie(r,t)?(r[t]=n,!0):Ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:o}},u){let c;return!!n[u]||e!==Re&&Ie(e,u)||is(t,u)||(c=o[0])&&Ie(c,u)||Ie(r,u)||Ie(Ar,u)||Ie(i.config.globalProperties,u)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Ie(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Fp(){return Cd().slots}function Bp(){return Cd().attrs}function Cd(){const e=dn();return e.setupContext||(e.setupContext=Qd(e))}function Au(e){return le(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Xs=!0;function $p(e){const t=Dd(e),n=e.proxy,r=e.ctx;Xs=!1,t.beforeCreate&&wu(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:u,watch:c,provide:l,inject:s,created:a,beforeMount:d,mounted:f,beforeUpdate:h,updated:p,activated:m,deactivated:_,beforeDestroy:g,beforeUnmount:v,destroyed:y,unmounted:E,render:L,renderTracked:I,renderTriggered:w,errorCaptured:D,serverPrefetch:T,expose:R,inheritAttrs:k,components:M,directives:H,filters:N}=t;if(s&&Hp(s,r,null),u)for(const V in u){const W=u[V];he(W)&&(r[V]=W.bind(n))}if(i){const V=i.call(n,n);xe(V)&&(e.data=ln(V))}if(Xs=!0,o)for(const V in o){const W=o[V],J=he(W)?W.bind(n,n):he(W.get)?W.get.bind(n,n):Ct,te=!he(W)&&he(W.set)?W.set.bind(n):Ct,ie=de({get:J,set:te});Object.defineProperty(r,V,{enumerable:!0,configurable:!0,get:()=>ie.value,set:ue=>ie.value=ue})}if(c)for(const V in c)Id(c[V],r,n,V);if(l){const V=he(l)?l.call(n):l;Reflect.ownKeys(V).forEach(W=>{ao(W,V[W])})}a&&wu(a,e,"c");function Y(V,W){le(W)?W.forEach(J=>V(J.bind(n))):W&&V(W.bind(n))}if(Y(Fo,d),Y($t,f),Y(Od,h),Y(Ip,p),Y(Tp,m),Y(Sp,_),Y(Rp,D),Y(Lp,I),Y(Mp,w),Y(Yr,v),Y(tr,E),Y(Dp,T),le(R))if(R.length){const V=e.exposed||(e.exposed={});R.forEach(W=>{Object.defineProperty(V,W,{get:()=>n[W],set:J=>n[W]=J})})}else e.exposed||(e.exposed={});L&&e.render===Ct&&(e.render=L),k!=null&&(e.inheritAttrs=k),M&&(e.components=M),H&&(e.directives=H),T&&Ad(e)}function Hp(e,t,n=Ct){le(e)&&(e=Ks(e));for(const r in e){const i=e[r];let o;xe(i)?"default"in i?o=mt(i.from||r,i.default,!0):o=mt(i.from||r):o=mt(i),je(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:u=>o.value=u}):t[r]=o}}function wu(e,t,n){Dt(le(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Id(e,t,n,r){let i=r.includes(".")?Yd(n,r):()=>n[r];if(Pe(e)){const o=t[e];he(o)&&Ft(i,o)}else if(he(e))Ft(i,e.bind(n));else if(xe(e))if(le(e))e.forEach(o=>Id(o,t,n,r));else{const o=he(e.handler)?e.handler.bind(n):t[e.handler];he(o)&&Ft(i,o,e)}}function Dd(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:u}}=e.appContext,c=o.get(t);let l;return c?l=c:!i.length&&!n&&!r?l=t:(l={},i.length&&i.forEach(s=>vo(l,s,u,!0)),vo(l,t,u)),xe(t)&&o.set(t,l),l}function vo(e,t,n,r=!1){const{mixins:i,extends:o}=t;o&&vo(e,o,n,!0),i&&i.forEach(u=>vo(e,u,n,!0));for(const u in t)if(!(r&&u==="expose")){const c=jp[u]||n&&n[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const jp={data:Ou,props:Tu,emits:Tu,methods:gr,computed:gr,beforeCreate:nt,created:nt,beforeMount:nt,mounted:nt,beforeUpdate:nt,updated:nt,beforeDestroy:nt,beforeUnmount:nt,destroyed:nt,unmounted:nt,activated:nt,deactivated:nt,errorCaptured:nt,serverPrefetch:nt,components:gr,directives:gr,watch:Yp,provide:Ou,inject:Up};function Ou(e,t){return t?e?function(){return Ke(he(e)?e.call(this,this):e,he(t)?t.call(this,this):t)}:t:e}function Up(e,t){return gr(Ks(e),Ks(t))}function Ks(e){if(le(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function nt(e,t){return e?[...new Set([].concat(e,t))]:t}function gr(e,t){return e?Ke(Object.create(null),e,t):t}function Tu(e,t){return e?le(e)&&le(t)?[...new Set([...e,...t])]:Ke(Object.create(null),Au(e),Au(t!=null?t:{})):t}function Yp(e,t){if(!e)return t;if(!t)return e;const n=Ke(Object.create(null),e);for(const r in t)n[r]=nt(e[r],t[r]);return n}function Md(){return{app:null,config:{isNativeTag:km,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let qp=0;function Gp(e,t){return function(r,i=null){he(r)||(r=Ke({},r)),i!=null&&!xe(i)&&(i=null);const o=Md(),u=new WeakSet,c=[];let l=!1;const s=o.app={_uid:qp++,_component:r,_props:i,_container:null,_context:o,_instance:null,version:Og,get config(){return o.config},set config(a){},use(a,...d){return u.has(a)||(a&&he(a.install)?(u.add(a),a.install(s,...d)):he(a)&&(u.add(a),a(s,...d))),s},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),s},component(a,d){return d?(o.components[a]=d,s):o.components[a]},directive(a,d){return d?(o.directives[a]=d,s):o.directives[a]},mount(a,d,f){if(!l){const h=s._ceVNode||Ee(r,i);return h.appContext=o,f===!0?f="svg":f===!1&&(f=void 0),e(h,a,f),l=!0,s._container=a,a.__vue_app__=s,$o(h.component)}},onUnmount(a){c.push(a)},unmount(){l&&(Dt(c,s._instance,16),e(null,s._container),delete s._container.__vue_app__)},provide(a,d){return o.provides[a]=d,s},runWithContext(a){const d=In;In=s;try{return a()}finally{In=d}}};return s}}let In=null;function ao(e,t){if(Xe){let n=Xe.provides;const r=Xe.parent&&Xe.parent.provides;r===n&&(n=Xe.provides=Object.create(r)),n[e]=t}}function mt(e,t,n=!1){const r=Xe||Ve;if(r||In){const i=In?In._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&he(t)?t.call(r&&r.proxy):t}}function Vp(){return!!(Xe||Ve||In)}const Ld={},Rd=()=>Object.create(Ld),kd=e=>Object.getPrototypeOf(e)===Ld;function Wp(e,t,n,r=!1){const i={},o=Rd();e.propsDefaults=Object.create(null),Nd(e,t,i,o);for(const u in e.propsOptions[0])u in i||(i[u]=void 0);n?e.props=r?i:od(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function zp(e,t,n,r){const{props:i,attrs:o,vnode:{patchFlag:u}}=e,c=we(i),[l]=e.propsOptions;let s=!1;if((r||u>0)&&!(u&16)){if(u&8){const a=e.vnode.dynamicProps;for(let d=0;d<a.length;d++){let f=a[d];if(Bo(e.emitsOptions,f))continue;const h=t[f];if(l)if(Ie(o,f))h!==o[f]&&(o[f]=h,s=!0);else{const p=At(f);i[p]=Qs(l,c,p,h,e,!1)}else h!==o[f]&&(o[f]=h,s=!0)}}}else{Nd(e,t,i,o)&&(s=!0);let a;for(const d in c)(!t||!Ie(t,d)&&((a=Ln(d))===d||!Ie(t,a)))&&(l?n&&(n[d]!==void 0||n[a]!==void 0)&&(i[d]=Qs(l,c,d,void 0,e,!0)):delete i[d]);if(o!==c)for(const d in o)(!t||!Ie(t,d))&&(delete o[d],s=!0)}s&&Vt(e.attrs,"set","")}function Nd(e,t,n,r){const[i,o]=e.propsOptions;let u=!1,c;if(t)for(let l in t){if(yr(l))continue;const s=t[l];let a;i&&Ie(i,a=At(l))?!o||!o.includes(a)?n[a]=s:(c||(c={}))[a]=s:Bo(e.emitsOptions,l)||(!(l in r)||s!==r[l])&&(r[l]=s,u=!0)}if(o){const l=we(n),s=c||Re;for(let a=0;a<o.length;a++){const d=o[a];n[d]=Qs(i,l,d,s[d],e,!Ie(s,d))}}return u}function Qs(e,t,n,r,i,o){const u=e[n];if(u!=null){const c=Ie(u,"default");if(c&&r===void 0){const l=u.default;if(u.type!==Function&&!u.skipFactory&&he(l)){const{propsDefaults:s}=i;if(n in s)r=s[n];else{const a=Vr(i);r=s[n]=l.call(null,t),a()}}else r=l;i.ce&&i.ce._setProp(n,r)}u[0]&&(o&&!c?r=!1:u[1]&&(r===""||r===Ln(n))&&(r=!0))}return r}const Xp=new WeakMap;function xd(e,t,n=!1){const r=n?Xp:t.propsCache,i=r.get(e);if(i)return i;const o=e.props,u={},c=[];let l=!1;if(!he(e)){const a=d=>{l=!0;const[f,h]=xd(d,t,!0);Ke(u,f),h&&c.push(...h)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!l)return xe(e)&&r.set(e,Vn),Vn;if(le(o))for(let a=0;a<o.length;a++){const d=At(o[a]);Su(d)&&(u[d]=Re)}else if(o)for(const a in o){const d=At(a);if(Su(d)){const f=o[a],h=u[d]=le(f)||he(f)?{type:f}:Ke({},f),p=h.type;let m=!1,_=!0;if(le(p))for(let g=0;g<p.length;++g){const v=p[g],y=he(v)&&v.name;if(y==="Boolean"){m=!0;break}else y==="String"&&(_=!1)}else m=he(p)&&p.name==="Boolean";h[0]=m,h[1]=_,(m||Ie(h,"default"))&&c.push(d)}}const s=[u,c];return xe(e)&&r.set(e,s),s}function Su(e){return e[0]!=="$"&&!yr(e)}const Pd=e=>e[0]==="_"||e==="$stable",ka=e=>le(e)?e.map(Pt):[Pt(e)],Kp=(e,t,n)=>{if(t._n)return t;const r=He((...i)=>ka(t(...i)),n);return r._c=!1,r},Fd=(e,t,n)=>{const r=e._ctx;for(const i in e){if(Pd(i))continue;const o=e[i];if(he(o))t[i]=Kp(i,o,r);else if(o!=null){const u=ka(o);t[i]=()=>u}}},Bd=(e,t)=>{const n=ka(t);e.slots.default=()=>n},$d=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},Qp=(e,t,n)=>{const r=e.slots=Rd();if(e.vnode.shapeFlag&32){const i=t._;i?($d(r,t,n),n&&Bf(r,"_",i,!0)):Fd(t,r)}else t&&Bd(e,t)},Jp=(e,t,n)=>{const{vnode:r,slots:i}=e;let o=!0,u=Re;if(r.shapeFlag&32){const c=t._;c?n&&c===1?o=!1:$d(i,t,n):(o=!t.$stable,Fd(t,i)),u=t}else t&&(Bd(e,t),u={default:1});if(o)for(const c in i)!Pd(c)&&u[c]==null&&delete i[c]},ft=dg;function Zp(e){return eg(e)}function eg(e,t){const n=Mo();n.__VUE__=!0;const{insert:r,remove:i,patchProp:o,createElement:u,createText:c,createComment:l,setText:s,setElementText:a,parentNode:d,nextSibling:f,setScopeId:h=Ct,insertStaticContent:p}=e,m=(S,C,O,x=null,B=null,$=null,b=void 0,A=null,P=!!C.dynamicChildren)=>{if(S===C)return;S&&!Tn(S,C)&&(x=U(S),ue(S,B,$,!0),S=null),C.patchFlag===-2&&(P=!1,C.dynamicChildren=null);const{type:j,ref:ee,shapeFlag:z}=C;switch(j){case Gr:_(S,C,O,x);break;case st:g(S,C,O,x);break;case uo:S==null&&v(C,O,x,b);break;case Be:M(S,C,O,x,B,$,b,A,P);break;default:z&1?L(S,C,O,x,B,$,b,A,P):z&6?H(S,C,O,x,B,$,b,A,P):(z&64||z&128)&&j.process(S,C,O,x,B,$,b,A,P,Q)}ee!=null&&B&&_o(ee,S&&S.ref,$,C||S,!C)},_=(S,C,O,x)=>{if(S==null)r(C.el=c(C.children),O,x);else{const B=C.el=S.el;C.children!==S.children&&s(B,C.children)}},g=(S,C,O,x)=>{S==null?r(C.el=l(C.children||""),O,x):C.el=S.el},v=(S,C,O,x)=>{[S.el,S.anchor]=p(S.children,C,O,x,S.el,S.anchor)},y=({el:S,anchor:C},O,x)=>{let B;for(;S&&S!==C;)B=f(S),r(S,O,x),S=B;r(C,O,x)},E=({el:S,anchor:C})=>{let O;for(;S&&S!==C;)O=f(S),i(S),S=O;i(C)},L=(S,C,O,x,B,$,b,A,P)=>{C.type==="svg"?b="svg":C.type==="math"&&(b="mathml"),S==null?I(C,O,x,B,$,b,A,P):T(S,C,B,$,b,A,P)},I=(S,C,O,x,B,$,b,A)=>{let P,j;const{props:ee,shapeFlag:z,transition:F,dirs:q}=S;if(P=S.el=u(S.type,$,ee&&ee.is,ee),z&8?a(P,S.children):z&16&&D(S.children,P,null,x,B,os(S,$),b,A),q&&vn(S,null,x,"created"),w(P,S,S.scopeId,b,x),ee){for(const ce in ee)ce!=="value"&&!yr(ce)&&o(P,ce,null,ee[ce],$,x);"value"in ee&&o(P,"value",null,ee.value,$),(j=ee.onVnodeBeforeMount)&&Rt(j,x,S)}q&&vn(S,null,x,"beforeMount");const se=tg(B,F);se&&F.beforeEnter(P),r(P,C,O),((j=ee&&ee.onVnodeMounted)||se||q)&&ft(()=>{j&&Rt(j,x,S),se&&F.enter(P),q&&vn(S,null,x,"mounted")},B)},w=(S,C,O,x,B)=>{if(O&&h(S,O),x)for(let $=0;$<x.length;$++)h(S,x[$]);if(B){let $=B.subTree;if(C===$||Gd($.type)&&($.ssContent===C||$.ssFallback===C)){const b=B.vnode;w(S,b,b.scopeId,b.slotScopeIds,B.parent)}}},D=(S,C,O,x,B,$,b,A,P=0)=>{for(let j=P;j<S.length;j++){const ee=S[j]=A?sn(S[j]):Pt(S[j]);m(null,ee,C,O,x,B,$,b,A)}},T=(S,C,O,x,B,$,b)=>{const A=C.el=S.el;let{patchFlag:P,dynamicChildren:j,dirs:ee}=C;P|=S.patchFlag&16;const z=S.props||Re,F=C.props||Re;let q;if(O&&yn(O,!1),(q=F.onVnodeBeforeUpdate)&&Rt(q,O,C,S),ee&&vn(C,S,O,"beforeUpdate"),O&&yn(O,!0),(z.innerHTML&&F.innerHTML==null||z.textContent&&F.textContent==null)&&a(A,""),j?R(S.dynamicChildren,j,A,O,x,os(C,B),$):b||W(S,C,A,null,O,x,os(C,B),$,!1),P>0){if(P&16)k(A,z,F,O,B);else if(P&2&&z.class!==F.class&&o(A,"class",null,F.class,B),P&4&&o(A,"style",z.style,F.style,B),P&8){const se=C.dynamicProps;for(let ce=0;ce<se.length;ce++){const ye=se[ce],ze=z[ye],Ue=F[ye];(Ue!==ze||ye==="value")&&o(A,ye,ze,Ue,B,O)}}P&1&&S.children!==C.children&&a(A,C.children)}else!b&&j==null&&k(A,z,F,O,B);((q=F.onVnodeUpdated)||ee)&&ft(()=>{q&&Rt(q,O,C,S),ee&&vn(C,S,O,"updated")},x)},R=(S,C,O,x,B,$,b)=>{for(let A=0;A<C.length;A++){const P=S[A],j=C[A],ee=P.el&&(P.type===Be||!Tn(P,j)||P.shapeFlag&70)?d(P.el):O;m(P,j,ee,null,x,B,$,b,!0)}},k=(S,C,O,x,B)=>{if(C!==O){if(C!==Re)for(const $ in C)!yr($)&&!($ in O)&&o(S,$,C[$],null,B,x);for(const $ in O){if(yr($))continue;const b=O[$],A=C[$];b!==A&&$!=="value"&&o(S,$,A,b,B,x)}"value"in O&&o(S,"value",C.value,O.value,B)}},M=(S,C,O,x,B,$,b,A,P)=>{const j=C.el=S?S.el:c(""),ee=C.anchor=S?S.anchor:c("");let{patchFlag:z,dynamicChildren:F,slotScopeIds:q}=C;q&&(A=A?A.concat(q):q),S==null?(r(j,O,x),r(ee,O,x),D(C.children||[],O,ee,B,$,b,A,P)):z>0&&z&64&&F&&S.dynamicChildren?(R(S.dynamicChildren,F,O,B,$,b,A),(C.key!=null||B&&C===B.subTree)&&Hd(S,C,!0)):W(S,C,O,ee,B,$,b,A,P)},H=(S,C,O,x,B,$,b,A,P)=>{C.slotScopeIds=A,S==null?C.shapeFlag&512?B.ctx.activate(C,O,x,b,P):N(C,O,x,B,$,b,P):G(S,C,P)},N=(S,C,O,x,B,$,b)=>{const A=S.component=vg(S,x,B);if(xo(S)&&(A.ctx.renderer=Q),yg(A,!1,b),A.asyncDep){if(B&&B.registerDep(A,Y,b),!S.el){const P=A.subTree=Ee(st);g(null,P,C,O)}}else Y(A,S,C,O,B,$,b)},G=(S,C,O)=>{const x=C.component=S.component;if(cg(S,C,O))if(x.asyncDep&&!x.asyncResolved){V(x,C,O);return}else x.next=C,x.update();else C.el=S.el,x.vnode=C},Y=(S,C,O,x,B,$,b)=>{const A=()=>{if(S.isMounted){let{next:z,bu:F,u:q,parent:se,vnode:ce}=S;{const Ot=jd(S);if(Ot){z&&(z.el=ce.el,V(S,z,b)),Ot.asyncDep.then(()=>{S.isUnmounted||A()});return}}let ye=z,ze;yn(S,!1),z?(z.el=ce.el,V(S,z,b)):z=ce,F&&so(F),(ze=z.props&&z.props.onVnodeBeforeUpdate)&&Rt(ze,se,z,ce),yn(S,!0);const Ue=Iu(S),vt=S.subTree;S.subTree=Ue,m(vt,Ue,d(vt.el),U(vt),S,B,$),z.el=Ue.el,ye===null&&fg(S,Ue.el),q&&ft(q,B),(ze=z.props&&z.props.onVnodeUpdated)&&ft(()=>Rt(ze,se,z,ce),B)}else{let z;const{el:F,props:q}=C,{bm:se,m:ce,parent:ye,root:ze,type:Ue}=S,vt=Xn(C);yn(S,!1),se&&so(se),!vt&&(z=q&&q.onVnodeBeforeMount)&&Rt(z,ye,C),yn(S,!0);{ze.ce&&ze.ce._injectChildStyle(Ue);const Ot=S.subTree=Iu(S);m(null,Ot,O,x,S,B,$),C.el=Ot.el}if(ce&&ft(ce,B),!vt&&(z=q&&q.onVnodeMounted)){const Ot=C;ft(()=>Rt(z,ye,Ot),B)}(C.shapeFlag&256||ye&&Xn(ye.vnode)&&ye.vnode.shapeFlag&256)&&S.a&&ft(S.a,B),S.isMounted=!0,C=O=x=null}};S.scope.on();const P=S.effect=new Gf(A);S.scope.off();const j=S.update=P.run.bind(P),ee=S.job=P.runIfDirty.bind(P);ee.i=S,ee.id=S.uid,P.scheduler=()=>Ra(ee),yn(S,!0),j()},V=(S,C,O)=>{C.component=S;const x=S.vnode.props;S.vnode=C,S.next=null,zp(S,C.props,x,O),Jp(S,C.children,O),hn(),yu(S),mn()},W=(S,C,O,x,B,$,b,A,P=!1)=>{const j=S&&S.children,ee=S?S.shapeFlag:0,z=C.children,{patchFlag:F,shapeFlag:q}=C;if(F>0){if(F&128){te(j,z,O,x,B,$,b,A,P);return}else if(F&256){J(j,z,O,x,B,$,b,A,P);return}}q&8?(ee&16&&Ae(j,B,$),z!==j&&a(O,z)):ee&16?q&16?te(j,z,O,x,B,$,b,A,P):Ae(j,B,$,!0):(ee&8&&a(O,""),q&16&&D(z,O,x,B,$,b,A,P))},J=(S,C,O,x,B,$,b,A,P)=>{S=S||Vn,C=C||Vn;const j=S.length,ee=C.length,z=Math.min(j,ee);let F;for(F=0;F<z;F++){const q=C[F]=P?sn(C[F]):Pt(C[F]);m(S[F],q,O,null,B,$,b,A,P)}j>ee?Ae(S,B,$,!0,!1,z):D(C,O,x,B,$,b,A,P,z)},te=(S,C,O,x,B,$,b,A,P)=>{let j=0;const ee=C.length;let z=S.length-1,F=ee-1;for(;j<=z&&j<=F;){const q=S[j],se=C[j]=P?sn(C[j]):Pt(C[j]);if(Tn(q,se))m(q,se,O,null,B,$,b,A,P);else break;j++}for(;j<=z&&j<=F;){const q=S[z],se=C[F]=P?sn(C[F]):Pt(C[F]);if(Tn(q,se))m(q,se,O,null,B,$,b,A,P);else break;z--,F--}if(j>z){if(j<=F){const q=F+1,se=q<ee?C[q].el:x;for(;j<=F;)m(null,C[j]=P?sn(C[j]):Pt(C[j]),O,se,B,$,b,A,P),j++}}else if(j>F)for(;j<=z;)ue(S[j],B,$,!0),j++;else{const q=j,se=j,ce=new Map;for(j=se;j<=F;j++){const ut=C[j]=P?sn(C[j]):Pt(C[j]);ut.key!=null&&ce.set(ut.key,j)}let ye,ze=0;const Ue=F-se+1;let vt=!1,Ot=0;const nr=new Array(Ue);for(j=0;j<Ue;j++)nr[j]=0;for(j=q;j<=z;j++){const ut=S[j];if(ze>=Ue){ue(ut,B,$,!0);continue}let Lt;if(ut.key!=null)Lt=ce.get(ut.key);else for(ye=se;ye<=F;ye++)if(nr[ye-se]===0&&Tn(ut,C[ye])){Lt=ye;break}Lt===void 0?ue(ut,B,$,!0):(nr[Lt-se]=j+1,Lt>=Ot?Ot=Lt:vt=!0,m(ut,C[Lt],O,null,B,$,b,A,P),ze++)}const Wa=vt?ng(nr):Vn;for(ye=Wa.length-1,j=Ue-1;j>=0;j--){const ut=se+j,Lt=C[ut],za=ut+1<ee?C[ut+1].el:x;nr[j]===0?m(null,Lt,O,za,B,$,b,A,P):vt&&(ye<0||j!==Wa[ye]?ie(Lt,O,za,2):ye--)}}},ie=(S,C,O,x,B=null)=>{const{el:$,type:b,transition:A,children:P,shapeFlag:j}=S;if(j&6){ie(S.component.subTree,C,O,x);return}if(j&128){S.suspense.move(C,O,x);return}if(j&64){b.move(S,C,O,Q);return}if(b===Be){r($,C,O);for(let z=0;z<P.length;z++)ie(P[z],C,O,x);r(S.anchor,C,O);return}if(b===uo){y(S,C,O);return}if(x!==2&&j&1&&A)if(x===0)A.beforeEnter($),r($,C,O),ft(()=>A.enter($),B);else{const{leave:z,delayLeave:F,afterLeave:q}=A,se=()=>r($,C,O),ce=()=>{z($,()=>{se(),q&&q()})};F?F($,se,ce):ce()}else r($,C,O)},ue=(S,C,O,x=!1,B=!1)=>{const{type:$,props:b,ref:A,children:P,dynamicChildren:j,shapeFlag:ee,patchFlag:z,dirs:F,cacheIndex:q}=S;if(z===-2&&(B=!1),A!=null&&_o(A,null,O,S,!0),q!=null&&(C.renderCache[q]=void 0),ee&256){C.ctx.deactivate(S);return}const se=ee&1&&F,ce=!Xn(S);let ye;if(ce&&(ye=b&&b.onVnodeBeforeUnmount)&&Rt(ye,C,S),ee&6)Te(S.component,O,x);else{if(ee&128){S.suspense.unmount(O,x);return}se&&vn(S,null,C,"beforeUnmount"),ee&64?S.type.remove(S,C,O,Q,x):j&&!j.hasOnce&&($!==Be||z>0&&z&64)?Ae(j,C,O,!1,!0):($===Be&&z&384||!B&&ee&16)&&Ae(P,C,O),x&&me(S)}(ce&&(ye=b&&b.onVnodeUnmounted)||se)&&ft(()=>{ye&&Rt(ye,C,S),se&&vn(S,null,C,"unmounted")},O)},me=S=>{const{type:C,el:O,anchor:x,transition:B}=S;if(C===Be){ve(O,x);return}if(C===uo){E(S);return}const $=()=>{i(O),B&&!B.persisted&&B.afterLeave&&B.afterLeave()};if(S.shapeFlag&1&&B&&!B.persisted){const{leave:b,delayLeave:A}=B,P=()=>b(O,$);A?A(S.el,$,P):P()}else $()},ve=(S,C)=>{let O;for(;S!==C;)O=f(S),i(S),S=O;i(C)},Te=(S,C,O)=>{const{bum:x,scope:B,job:$,subTree:b,um:A,m:P,a:j}=S;Cu(P),Cu(j),x&&so(x),B.stop(),$&&($.flags|=8,ue(b,S,C,O)),A&&ft(A,C),ft(()=>{S.isUnmounted=!0},C),C&&C.pendingBranch&&!C.isUnmounted&&S.asyncDep&&!S.asyncResolved&&S.suspenseId===C.pendingId&&(C.deps--,C.deps===0&&C.resolve())},Ae=(S,C,O,x=!1,B=!1,$=0)=>{for(let b=$;b<S.length;b++)ue(S[b],C,O,x,B)},U=S=>{if(S.shapeFlag&6)return U(S.component.subTree);if(S.shapeFlag&128)return S.suspense.next();const C=f(S.anchor||S.el),O=C&&C[Ep];return O?f(O):C};let K=!1;const X=(S,C,O)=>{S==null?C._vnode&&ue(C._vnode,null,null,!0):m(C._vnode||null,S,C,null,null,null,O),C._vnode=S,K||(K=!0,yu(),hd(),K=!1)},Q={p:m,um:ue,m:ie,r:me,mt:N,mc:D,pc:W,pbc:R,n:U,o:e};return{render:X,hydrate:void 0,createApp:Gp(X)}}function os({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function yn({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function tg(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Hd(e,t,n=!1){const r=e.children,i=t.children;if(le(r)&&le(i))for(let o=0;o<r.length;o++){const u=r[o];let c=i[o];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=i[o]=sn(i[o]),c.el=u.el),!n&&c.patchFlag!==-2&&Hd(u,c)),c.type===Gr&&(c.el=u.el)}}function ng(e){const t=e.slice(),n=[0];let r,i,o,u,c;const l=e.length;for(r=0;r<l;r++){const s=e[r];if(s!==0){if(i=n[n.length-1],e[i]<s){t[r]=i,n.push(r);continue}for(o=0,u=n.length-1;o<u;)c=o+u>>1,e[n[c]]<s?o=c+1:u=c;s<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,u=n[o-1];o-- >0;)n[o]=u,u=t[u];return n}function jd(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:jd(t)}function Cu(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const rg=Symbol.for("v-scx"),ig=()=>mt(rg);function Ft(e,t,n){return Ud(e,t,n)}function Ud(e,t,n=Re){const{immediate:r,deep:i,flush:o,once:u}=n,c=Ke({},n),l=t&&r||!t&&o!=="post";let s;if(Nr){if(o==="sync"){const h=ig();s=h.__watcherHandles||(h.__watcherHandles=[])}else if(!l){const h=()=>{};return h.stop=Ct,h.resume=Ct,h.pause=Ct,h}}const a=Xe;c.call=(h,p,m)=>Dt(h,a,p,m);let d=!1;o==="post"?c.scheduler=h=>{ft(h,a&&a.suspense)}:o!=="sync"&&(d=!0,c.scheduler=(h,p)=>{p?h():Ra(h)}),c.augmentJob=h=>{t&&(h.flags|=4),d&&(h.flags|=2,a&&(h.id=a.uid,h.i=a))};const f=vp(e,t,c);return Nr&&(s?s.push(f):l&&f()),f}function og(e,t,n){const r=this.proxy,i=Pe(e)?e.includes(".")?Yd(r,e):()=>r[e]:e.bind(r,r);let o;he(t)?o=t:(o=t.handler,n=t);const u=Vr(this),c=Ud(i,o.bind(r),n);return u(),c}function Yd(e,t){const n=t.split(".");return()=>{let r=e;for(let i=0;i<n.length&&r;i++)r=r[n[i]];return r}}const sg=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e["".concat(t,"Modifiers")]||e["".concat(At(t),"Modifiers")]||e["".concat(Ln(t),"Modifiers")];function ag(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Re;let i=n;const o=t.startsWith("update:"),u=o&&sg(r,t.slice(7));u&&(u.trim&&(i=n.map(a=>Pe(a)?a.trim():a)),u.number&&(i=n.map(js)));let c,l=r[c=Jo(t)]||r[c=Jo(At(t))];!l&&o&&(l=r[c=Jo(Ln(t))]),l&&Dt(l,e,6,i);const s=r[c+"Once"];if(s){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Dt(s,e,6,i)}}function qd(e,t,n=!1){const r=t.emitsCache,i=r.get(e);if(i!==void 0)return i;const o=e.emits;let u={},c=!1;if(!he(e)){const l=s=>{const a=qd(s,t,!0);a&&(c=!0,Ke(u,a))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!c?(xe(e)&&r.set(e,null),null):(le(o)?o.forEach(l=>u[l]=null):Ke(u,o),xe(e)&&r.set(e,u),u)}function Bo(e,t){return!e||!So(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ie(e,t[0].toLowerCase()+t.slice(1))||Ie(e,Ln(t))||Ie(e,t))}function Iu(e){const{type:t,vnode:n,proxy:r,withProxy:i,propsOptions:[o],slots:u,attrs:c,emit:l,render:s,renderCache:a,props:d,data:f,setupState:h,ctx:p,inheritAttrs:m}=e,_=go(e);let g,v;try{if(n.shapeFlag&4){const E=i||r,L=E;g=Pt(s.call(L,E,a,d,h,f,p)),v=c}else{const E=t;g=Pt(E.length>1?E(d,{attrs:c,slots:u,emit:l}):E(d,null)),v=t.props?c:ug(c)}}catch(E){wr.length=0,ko(E,e,1),g=Ee(st)}let y=g;if(v&&m!==!1){const E=Object.keys(v),{shapeFlag:L}=y;E.length&&L&7&&(o&&E.some(ba)&&(v=lg(v,o)),y=fn(y,v,!1,!0))}return n.dirs&&(y=fn(y,null,!1,!0),y.dirs=y.dirs?y.dirs.concat(n.dirs):n.dirs),n.transition&&Lr(y,n.transition),g=y,go(_),g}const ug=e=>{let t;for(const n in e)(n==="class"||n==="style"||So(n))&&((t||(t={}))[n]=e[n]);return t},lg=(e,t)=>{const n={};for(const r in e)(!ba(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function cg(e,t,n){const{props:r,children:i,component:o}=e,{props:u,children:c,patchFlag:l}=t,s=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?Du(r,u,s):!!u;if(l&8){const a=t.dynamicProps;for(let d=0;d<a.length;d++){const f=a[d];if(u[f]!==r[f]&&!Bo(s,f))return!0}}}else return(i||c)&&(!c||!c.$stable)?!0:r===u?!1:r?u?Du(r,u,s):!0:!!u;return!1}function Du(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let i=0;i<r.length;i++){const o=r[i];if(t[o]!==e[o]&&!Bo(n,o))return!0}return!1}function fg({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Gd=e=>e.__isSuspense;function dg(e,t){t&&t.pendingBranch?le(e)?t.effects.push(...e):t.effects.push(e):dd(e)}const Be=Symbol.for("v-fgt"),Gr=Symbol.for("v-txt"),st=Symbol.for("v-cmt"),uo=Symbol.for("v-stc"),wr=[];let ht=null;function ne(e=!1){wr.push(ht=e?null:[])}function hg(){wr.pop(),ht=wr[wr.length-1]||null}let Rr=1;function Mu(e,t=!1){Rr+=e,e<0&&ht&&t&&(ht.hasOnce=!0)}function Vd(e){return e.dynamicChildren=Rr>0?ht||Vn:null,hg(),Rr>0&&ht&&ht.push(e),e}function ae(e,t,n,r,i,o){return Vd(oe(e,t,n,r,i,o,!0))}function et(e,t,n,r,i){return Vd(Ee(e,t,n,r,i,!0))}function kr(e){return e?e.__v_isVNode===!0:!1}function Tn(e,t){return e.type===t.type&&e.key===t.key}const Wd=({key:e})=>e!=null?e:null,lo=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Pe(e)||je(e)||he(e)?{i:Ve,r:e,k:t,f:!!n}:e:null);function oe(e,t=null,n=null,r=0,i=null,o=e===Be?0:1,u=!1,c=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Wd(t),ref:t&&lo(t),scopeId:pd,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Ve};return c?(Na(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=Pe(n)?8:16),Rr>0&&!u&&ht&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&ht.push(l),l}const Ee=mg;function mg(e,t=null,n=null,r=0,i=null,o=!1){if((!e||e===Td)&&(e=st),kr(e)){const c=fn(e,t,!0);return n&&Na(c,n),Rr>0&&!o&&ht&&(c.shapeFlag&6?ht[ht.indexOf(e)]=c:ht.push(c)),c.patchFlag=-2,c}if(wg(e)&&(e=e.__vccOpts),t){t=zd(t);let{class:c,style:l}=t;c&&!Pe(c)&&(t.class=$e(c)),xe(l)&&(Ma(l)&&!le(l)&&(l=Ke({},l)),t.style=dt(l))}const u=Pe(e)?1:Gd(e)?128:gd(e)?64:xe(e)?4:he(e)?2:0;return oe(e,t,n,r,i,u,o,!0)}function zd(e){return e?Ma(e)||kd(e)?Ke({},e):e:null}function fn(e,t,n=!1,r=!1){const{props:i,ref:o,patchFlag:u,children:c,transition:l}=e,s=t?yo(i||{},t):i,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&Wd(s),ref:t&&t.ref?n&&o?le(o)?o.concat(lo(t)):[o,lo(t)]:lo(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Be?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&fn(e.ssContent),ssFallback:e.ssFallback&&fn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&Lr(a,l.clone(a)),a}function pg(e=" ",t=0){return Ee(Gr,null,e,t)}function pe(e="",t=!1){return t?(ne(),et(st,null,e)):Ee(st,null,e)}function Pt(e){return e==null||typeof e=="boolean"?Ee(st):le(e)?Ee(Be,null,e.slice()):kr(e)?sn(e):Ee(Gr,null,String(e))}function sn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:fn(e)}function Na(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(le(t))n=16;else if(typeof t=="object")if(r&65){const i=t.default;i&&(i._c&&(i._d=!1),Na(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!kd(t)?t._ctx=Ve:i===3&&Ve&&(Ve.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else he(t)?(t={default:t,_ctx:Ve},n=32):(t=String(t),r&64?(n=16,t=[pg(t)]):n=8);e.children=t,e.shapeFlag|=n}function yo(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const i in r)if(i==="class")t.class!==r.class&&(t.class=$e([t.class,r.class]));else if(i==="style")t.style=dt([t.style,r.style]);else if(So(i)){const o=t[i],u=r[i];u&&o!==u&&!(le(o)&&o.includes(u))&&(t[i]=o?[].concat(o,u):u)}else i!==""&&(t[i]=r[i])}return t}function Rt(e,t,n,r=null){Dt(e,t,7,[n,r])}const gg=Md();let _g=0;function vg(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||gg,o={uid:_g++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Yf(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:xd(r,i),emitsOptions:qd(r,i),emit:null,emitted:null,propsDefaults:Re,inheritAttrs:r.inheritAttrs,ctx:Re,data:Re,props:Re,attrs:Re,slots:Re,refs:Re,setupState:Re,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ag.bind(null,o),e.ce&&e.ce(o),o}let Xe=null;const dn=()=>Xe||Ve;let bo,Js;{const e=Mo(),t=(n,r)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(r),o=>{i.length>1?i.forEach(u=>u(o)):i[0](o)}};bo=t("__VUE_INSTANCE_SETTERS__",n=>Xe=n),Js=t("__VUE_SSR_SETTERS__",n=>Nr=n)}const Vr=e=>{const t=Xe;return bo(e),e.scope.on(),()=>{e.scope.off(),bo(t)}},Lu=()=>{Xe&&Xe.scope.off(),bo(null)};function Xd(e){return e.vnode.shapeFlag&4}let Nr=!1;function yg(e,t=!1,n=!1){t&&Js(t);const{props:r,children:i}=e.vnode,o=Xd(e);Wp(e,r,o,t),Qp(e,i,n);const u=o?bg(e,t):void 0;return t&&Js(!1),u}function bg(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Pp);const{setup:r}=n;if(r){hn();const i=e.setupContext=r.length>1?Qd(e):null,o=Vr(e),u=Ur(r,e,0,[e.props,i]),c=xf(u);if(mn(),o(),(c||e.sp)&&!Xn(e)&&Ad(e),c){if(u.then(Lu,Lu),t)return u.then(l=>{Ru(e,l)}).catch(l=>{ko(l,e,0)});e.asyncDep=u}else Ru(e,u)}else Kd(e)}function Ru(e,t,n){he(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:xe(t)&&(e.setupState=ld(t)),Kd(e)}function Kd(e,t,n){const r=e.type;e.render||(e.render=r.render||Ct);{const i=Vr(e);hn();try{$p(e)}finally{mn(),i()}}}const Eg={get(e,t){return Je(e,"get",""),e[t]}};function Qd(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Eg),slots:e.slots,emit:e.emit,expose:t}}function $o(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ld(La(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ar)return Ar[n](e)},has(t,n){return n in t||n in Ar}})):e.proxy}function Ag(e,t=!0){return he(e)?e.displayName||e.name:e.name||t&&e.__name}function wg(e){return he(e)&&"__vccOpts"in e}const de=(e,t)=>gp(e,t,Nr);function Wr(e,t,n){const r=arguments.length;return r===2?xe(t)&&!le(t)?kr(t)?Ee(e,null,[t]):Ee(e,t):Ee(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&kr(n)&&(n=[n]),Ee(e,t,n))}const Og="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Zs;const ku=typeof window<"u"&&window.trustedTypes;if(ku)try{Zs=ku.createPolicy("vue",{createHTML:e=>e})}catch(e){}const Jd=Zs?e=>Zs.createHTML(e):e=>e,Tg="http://www.w3.org/2000/svg",Sg="http://www.w3.org/1998/Math/MathML",Gt=typeof document<"u"?document:null,Nu=Gt&&Gt.createElement("template"),Cg={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const i=t==="svg"?Gt.createElementNS(Tg,e):t==="mathml"?Gt.createElementNS(Sg,e):n?Gt.createElement(e,{is:n}):Gt.createElement(e);return e==="select"&&r&&r.multiple!=null&&i.setAttribute("multiple",r.multiple),i},createText:e=>Gt.createTextNode(e),createComment:e=>Gt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Gt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,i,o){const u=n?n.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===o||!(i=i.nextSibling)););else{Nu.innerHTML=Jd(r==="svg"?"<svg>".concat(e,"</svg>"):r==="mathml"?"<math>".concat(e,"</math>"):e);const c=Nu.content;if(r==="svg"||r==="mathml"){const l=c.firstChild;for(;l.firstChild;)c.appendChild(l.firstChild);c.removeChild(l)}t.insertBefore(c,n)}return[u?u.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Jt="transition",ir="animation",xr=Symbol("_vtc"),Zd={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Ig=Ke({},_d,Zd),Dg=e=>(e.displayName="Transition",e.props=Ig,e),Or=Dg((e,{slots:t})=>Wr(Op,Mg(e),t)),bn=(e,t=[])=>{le(e)?e.forEach(n=>n(...t)):e&&e(...t)},xu=e=>e?le(e)?e.some(t=>t.length>1):e.length>1:!1;function Mg(e){const t={};for(const M in e)M in Zd||(t[M]=e[M]);if(e.css===!1)return t;const{name:n="v",type:r,duration:i,enterFromClass:o="".concat(n,"-enter-from"),enterActiveClass:u="".concat(n,"-enter-active"),enterToClass:c="".concat(n,"-enter-to"),appearFromClass:l=o,appearActiveClass:s=u,appearToClass:a=c,leaveFromClass:d="".concat(n,"-leave-from"),leaveActiveClass:f="".concat(n,"-leave-active"),leaveToClass:h="".concat(n,"-leave-to")}=e,p=Lg(i),m=p&&p[0],_=p&&p[1],{onBeforeEnter:g,onEnter:v,onEnterCancelled:y,onLeave:E,onLeaveCancelled:L,onBeforeAppear:I=g,onAppear:w=v,onAppearCancelled:D=y}=t,T=(M,H,N,G)=>{M._enterCancelled=G,En(M,H?a:c),En(M,H?s:u),N&&N()},R=(M,H)=>{M._isLeaving=!1,En(M,d),En(M,h),En(M,f),H&&H()},k=M=>(H,N)=>{const G=M?w:v,Y=()=>T(H,M,N);bn(G,[H,Y]),Pu(()=>{En(H,M?l:o),jt(H,M?a:c),xu(G)||Fu(H,r,m,Y)})};return Ke(t,{onBeforeEnter(M){bn(g,[M]),jt(M,o),jt(M,u)},onBeforeAppear(M){bn(I,[M]),jt(M,l),jt(M,s)},onEnter:k(!1),onAppear:k(!0),onLeave(M,H){M._isLeaving=!0;const N=()=>R(M,H);jt(M,d),M._enterCancelled?(jt(M,f),Hu()):(Hu(),jt(M,f)),Pu(()=>{M._isLeaving&&(En(M,d),jt(M,h),xu(E)||Fu(M,r,_,N))}),bn(E,[M,N])},onEnterCancelled(M){T(M,!1,void 0,!0),bn(y,[M])},onAppearCancelled(M){T(M,!0,void 0,!0),bn(D,[M])},onLeaveCancelled(M){R(M),bn(L,[M])}})}function Lg(e){if(e==null)return null;if(xe(e))return[ss(e.enter),ss(e.leave)];{const t=ss(e);return[t,t]}}function ss(e){return Bm(e)}function jt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[xr]||(e[xr]=new Set)).add(t)}function En(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[xr];n&&(n.delete(t),n.size||(e[xr]=void 0))}function Pu(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Rg=0;function Fu(e,t,n,r){const i=e._endId=++Rg,o=()=>{i===e._endId&&r()};if(n!=null)return setTimeout(o,n);const{type:u,timeout:c,propCount:l}=kg(e,t);if(!u)return r();const s=u+"end";let a=0;const d=()=>{e.removeEventListener(s,f),o()},f=h=>{h.target===e&&++a>=l&&d()};setTimeout(()=>{a<l&&d()},c+1),e.addEventListener(s,f)}function kg(e,t){const n=window.getComputedStyle(e),r=p=>(n[p]||"").split(", "),i=r("".concat(Jt,"Delay")),o=r("".concat(Jt,"Duration")),u=Bu(i,o),c=r("".concat(ir,"Delay")),l=r("".concat(ir,"Duration")),s=Bu(c,l);let a=null,d=0,f=0;t===Jt?u>0&&(a=Jt,d=u,f=o.length):t===ir?s>0&&(a=ir,d=s,f=l.length):(d=Math.max(u,s),a=d>0?u>s?Jt:ir:null,f=a?a===Jt?o.length:l.length:0);const h=a===Jt&&/\b(transform|all)(,|$)/.test(r("".concat(Jt,"Property")).toString());return{type:a,timeout:d,propCount:f,hasTransform:h}}function Bu(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>$u(n)+$u(e[r])))}function $u(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Hu(){return document.body.offsetHeight}function Ng(e,t,n){const r=e[xr];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Eo=Symbol("_vod"),eh=Symbol("_vsh"),ju={beforeMount(e,{value:t},{transition:n}){e[Eo]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):or(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),or(e,!0),r.enter(e)):r.leave(e,()=>{or(e,!1)}):or(e,t))},beforeUnmount(e,{value:t}){or(e,t)}};function or(e,t){e.style.display=t?e[Eo]:"none",e[eh]=!t}const th=Symbol("");function xg(e){const t=dn();if(!t)return;const n=t.ut=(i=e(t.proxy))=>{Array.from(document.querySelectorAll('[data-v-owner="'.concat(t.uid,'"]'))).forEach(o=>Ao(o,i))},r=()=>{const i=e(t.proxy);t.ce?Ao(t.ce,i):ea(t.subTree,i),n(i)};Od(()=>{dd(r)}),$t(()=>{Ft(r,Ct,{flush:"post"});const i=new MutationObserver(r);i.observe(t.subTree.el.parentNode,{childList:!0}),tr(()=>i.disconnect())})}function ea(e,t){if(e.shapeFlag&128){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{ea(n.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)Ao(e.el,t);else if(e.type===Be)e.children.forEach(n=>ea(n,t));else if(e.type===uo){let{el:n,anchor:r}=e;for(;n&&(Ao(n,t),n!==r);)n=n.nextSibling}}function Ao(e,t){if(e.nodeType===1){const n=e.style;let r="";for(const i in t)n.setProperty("--".concat(i),t[i]),r+="--".concat(i,": ").concat(t[i],";");n[th]=r}}const Pg=/(^|;)\s*display\s*:/;function Fg(e,t,n){const r=e.style,i=Pe(n);let o=!1;if(n&&!i){if(t)if(Pe(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();n[c]==null&&co(r,c,"")}else for(const u in t)n[u]==null&&co(r,u,"");for(const u in n)u==="display"&&(o=!0),co(r,u,n[u])}else if(i){if(t!==n){const u=r[th];u&&(n+=";"+u),r.cssText=n,o=Pg.test(n)}}else t&&e.removeAttribute("style");Eo in e&&(e[Eo]=o?r.display:"",e[eh]&&(r.display="none"))}const Uu=/\s*!important$/;function co(e,t,n){if(le(n))n.forEach(r=>co(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Bg(e,t);Uu.test(n)?e.setProperty(Ln(r),n.replace(Uu,""),"important"):e[r]=n}}const Yu=["Webkit","Moz","ms"],as={};function Bg(e,t){const n=as[t];if(n)return n;let r=At(t);if(r!=="filter"&&r in e)return as[t]=r;r=Do(r);for(let i=0;i<Yu.length;i++){const o=Yu[i]+r;if(o in e)return as[t]=o}return t}const qu="http://www.w3.org/1999/xlink";function Gu(e,t,n,r,i,o=qm(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(qu,t.slice(6,t.length)):e.setAttributeNS(qu,t,n):n==null||o&&!Hf(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Kt(n)?String(n):n)}function Vu(e,t,n,r,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Jd(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const c=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(c!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let u=!1;if(n===""||n==null){const c=typeof e[t];c==="boolean"?n=Hf(n):n==null&&c==="string"?(n="",u=!0):c==="number"&&(n=0,u=!0)}try{e[t]=n}catch(c){}u&&e.removeAttribute(i||t)}function Yn(e,t,n,r){e.addEventListener(t,n,r)}function $g(e,t,n,r){e.removeEventListener(t,n,r)}const Wu=Symbol("_vei");function Hg(e,t,n,r,i=null){const o=e[Wu]||(e[Wu]={}),u=o[t];if(r&&u)u.value=r;else{const[c,l]=jg(t);if(r){const s=o[t]=qg(r,i);Yn(e,c,s,l)}else u&&($g(e,c,u,l),o[t]=void 0)}}const zu=/(?:Once|Passive|Capture)$/;function jg(e){let t;if(zu.test(e)){t={};let r;for(;r=e.match(zu);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Ln(e.slice(2)),t]}let us=0;const Ug=Promise.resolve(),Yg=()=>us||(Ug.then(()=>us=0),us=Date.now());function qg(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Dt(Gg(r,n.value),t,5,[r])};return n.value=e,n.attached=Yg(),n}function Gg(e,t){if(le(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>i=>!i._stopped&&r&&r(i))}else return t}const Xu=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Vg=(e,t,n,r,i,o)=>{const u=i==="svg";t==="class"?Ng(e,r,u):t==="style"?Fg(e,n,r):So(t)?ba(t)||Hg(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Wg(e,t,r,u))?(Vu(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Gu(e,t,r,u,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Pe(r))?Vu(e,At(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Gu(e,t,r,u))};function Wg(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Xu(t)&&he(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Xu(t)&&Pe(n)?!1:t in e}const Ku=e=>{const t=e.props["onUpdate:modelValue"]||!1;return le(t)?n=>so(t,n):t};function zg(e){e.target.composing=!0}function Qu(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ls=Symbol("_assign"),Xg={created(e,{modifiers:{lazy:t,trim:n,number:r}},i){e[ls]=Ku(i);const o=r||i.props&&i.props.type==="number";Yn(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;n&&(c=c.trim()),o&&(c=js(c)),e[ls](c)}),n&&Yn(e,"change",()=>{e.value=e.value.trim()}),t||(Yn(e,"compositionstart",zg),Yn(e,"compositionend",Qu),Yn(e,"change",Qu))},mounted(e,{value:t}){e.value=t==null?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:i,number:o}},u){if(e[ls]=Ku(u),e.composing)return;const c=(o||e.type==="number")&&!/^0\d/.test(e.value)?js(e.value):e.value,l=t==null?"":t;c!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||i&&e.value.trim()===l)||(e.value=l))}},Kg=["ctrl","shift","alt","meta"],Qg={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Kg.some(n=>e["".concat(n,"Key")]&&!t.includes(n))},Jg=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(i,...o)=>{for(let u=0;u<t.length;u++){const c=Qg[t[u]];if(c&&c(i,t))return}return e(i,...o)})},Zg=Ke({patchProp:Vg},Cg);let Ju;function e0(){return Ju||(Ju=Zp(Zg))}const t0=(...e)=>{const t=e0().createApp(...e),{mount:n}=t;return t.mount=r=>{const i=r0(r);if(!i)return;const o=t._component;!he(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const u=n(i,!1,n0(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),u},t};function n0(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function r0(e){return Pe(e)?document.querySelector(e):e}/*!
 * pinia v3.0.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let nh;const Ho=e=>nh=e,rh=Symbol();function ta(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Tr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Tr||(Tr={}));function i0(){const e=wa(!0),t=e.run(()=>_e({}));let n=[],r=[];const i=La({install(o){Ho(i),i._a=o,o.provide(rh,i),o.config.globalProperties.$pinia=i,r.forEach(u=>n.push(u)),r=[]},use(o){return this._a?n.push(o):r.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}const ih=()=>{};function Zu(e,t,n,r=ih){e.push(t);const i=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&qf()&&Gm(i),i}function Bn(e,...t){e.slice().forEach(n=>{n(...t)})}const o0=e=>e(),el=Symbol(),cs=Symbol();function na(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],i=e[n];ta(i)&&ta(r)&&e.hasOwnProperty(n)&&!je(r)&&!cn(r)?e[n]=na(i,r):e[n]=r}return e}const s0=Symbol();function a0(e){return!ta(e)||!e.hasOwnProperty(s0)}const{assign:nn}=Object;function u0(e){return!!(je(e)&&e.effect)}function l0(e,t,n,r){const{state:i,actions:o,getters:u}=t,c=n.state.value[e];let l;function s(){c||(n.state.value[e]=i?i():{});const a=Ro(n.state.value[e]);return nn(a,o,Object.keys(u||{}).reduce((d,f)=>(d[f]=La(de(()=>{Ho(n);const h=n._s.get(e);return u[f].call(h,h)})),d),{}))}return l=oh(e,s,t,n,r,!0),l}function oh(e,t,n={},r,i,o){let u;const c=nn({actions:{}},n),l={deep:!0};let s,a,d=[],f=[],h;const p=r.state.value[e];!o&&!p&&(r.state.value[e]={}),_e({});let m;function _(D){let T;s=a=!1,typeof D=="function"?(D(r.state.value[e]),T={type:Tr.patchFunction,storeId:e,events:h}):(na(r.state.value[e],D),T={type:Tr.patchObject,payload:D,storeId:e,events:h});const R=m=Symbol();No().then(()=>{m===R&&(s=!0)}),a=!0,Bn(d,T,r.state.value[e])}const g=o?function(){const{state:T}=n,R=T?T():{};this.$patch(k=>{nn(k,R)})}:ih;function v(){u.stop(),d=[],f=[],r._s.delete(e)}const y=(D,T="")=>{if(el in D)return D[cs]=T,D;const R=function(){Ho(r);const k=Array.from(arguments),M=[],H=[];function N(V){M.push(V)}function G(V){H.push(V)}Bn(f,{args:k,name:R[cs],store:L,after:N,onError:G});let Y;try{Y=D.apply(this&&this.$id===e?this:L,k)}catch(V){throw Bn(H,V),V}return Y instanceof Promise?Y.then(V=>(Bn(M,V),V)).catch(V=>(Bn(H,V),Promise.reject(V))):(Bn(M,Y),Y)};return R[el]=!0,R[cs]=T,R},E={_p:r,$id:e,$onAction:Zu.bind(null,f),$patch:_,$reset:g,$subscribe(D,T={}){const R=Zu(d,D,T.detached,()=>k()),k=u.run(()=>Ft(()=>r.state.value[e],M=>{(T.flush==="sync"?a:s)&&D({storeId:e,type:Tr.direct,events:h},M)},nn({},l,T)));return R},$dispose:v},L=ln(E);r._s.set(e,L);const w=(r._a&&r._a.runWithContext||o0)(()=>r._e.run(()=>(u=wa()).run(()=>t({action:y}))));for(const D in w){const T=w[D];if(je(T)&&!u0(T)||cn(T))o||(p&&a0(T)&&(je(T)?T.value=p[D]:na(T,p[D])),r.state.value[e][D]=T);else if(typeof T=="function"){const R=y(T,D);w[D]=R,c.actions[D]=T}}return nn(L,w),nn(we(L),w),Object.defineProperty(L,"$state",{get:()=>r.state.value[e],set:D=>{_(T=>{nn(T,D)})}}),r._p.forEach(D=>{nn(L,u.run(()=>D({store:L,app:r._a,pinia:r,options:c})))}),p&&o&&n.hydrate&&n.hydrate(L.$state,p),s=!0,a=!0,L}/*! #__NO_SIDE_EFFECTS__ */function c0(e,t,n){let r;const i=typeof t=="function";r=i?n:t;function o(u,c){const l=Vp();return u=u||(l?mt(rh,null):null),u&&Ho(u),u=nh,u._s.has(e)||(i?oh(e,t,r,u):l0(e,r,u)),u._s.get(e)}return o.$id=e,o}/*!
  * shared v11.1.2
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const wo=typeof window<"u",pn=(e,t=!1)=>t?Symbol.for(e):Symbol(e),f0=(e,t,n)=>d0({l:e,k:t,s:n}),d0=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Ye=e=>typeof e=="number"&&isFinite(e),h0=e=>xa(e)==="[object Date]",Kn=e=>xa(e)==="[object RegExp]",jo=e=>be(e)&&Object.keys(e).length===0,We=Object.assign,m0=Object.create,Le=(e=null)=>m0(e);let tl;const Sn=()=>tl||(tl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:Le());function nl(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const p0=Object.prototype.hasOwnProperty;function St(e,t){return p0.call(e,t)}const qe=Array.isArray,Ne=e=>typeof e=="function",re=e=>typeof e=="string",Oe=e=>typeof e=="boolean",Se=e=>e!==null&&typeof e=="object",g0=e=>Se(e)&&Ne(e.then)&&Ne(e.catch),sh=Object.prototype.toString,xa=e=>sh.call(e),be=e=>xa(e)==="[object Object]",_0=e=>e==null?"":qe(e)||be(e)&&e.toString===sh?JSON.stringify(e,null,2):String(e);function Pa(e,t=""){return e.reduce((n,r,i)=>i===0?n+r:n+t+r,"")}function v0(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const ui=e=>!Se(e)||qe(e);function fo(e,t){if(ui(e)||ui(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:r,des:i}=n.pop();Object.keys(r).forEach(o=>{o!=="__proto__"&&(Se(r[o])&&!Se(i[o])&&(i[o]=Array.isArray(r[o])?[]:Le()),ui(i[o])||ui(r[o])?i[o]=r[o]:n.push({src:r[o],des:i[o]}))})}}/*!
  * message-compiler v11.1.2
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function y0(e,t,n){return{line:e,column:t,offset:n}}function ra(e,t,n){return{start:e,end:t}}const De={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14},b0=17;function Uo(e,t,n={}){const{domain:r,messages:i,args:o}=n,u=e,c=new SyntaxError(String(u));return c.code=e,t&&(c.location=t),c.domain=r,c}function E0(e){throw e}const Ut=" ",A0="\r",rt="\n",w0="\u2028",O0="\u2029";function T0(e){const t=e;let n=0,r=1,i=1,o=0;const u=w=>t[w]===A0&&t[w+1]===rt,c=w=>t[w]===rt,l=w=>t[w]===O0,s=w=>t[w]===w0,a=w=>u(w)||c(w)||l(w)||s(w),d=()=>n,f=()=>r,h=()=>i,p=()=>o,m=w=>u(w)||l(w)||s(w)?rt:t[w],_=()=>m(n),g=()=>m(n+o);function v(){return o=0,a(n)&&(r++,i=0),u(n)&&n++,n++,i++,t[n]}function y(){return u(n+o)&&o++,o++,t[n+o]}function E(){n=0,r=1,i=1,o=0}function L(w=0){o=w}function I(){const w=n+o;for(;w!==n;)v();o=0}return{index:d,line:f,column:h,peekOffset:p,charAt:m,currentChar:_,currentPeek:g,next:v,peek:y,reset:E,resetPeek:L,skipToPeek:I}}const Zt=void 0,S0=".",rl="'",C0="tokenizer";function I0(e,t={}){const n=t.location!==!1,r=T0(e),i=()=>r.index(),o=()=>y0(r.line(),r.column(),r.index()),u=o(),c=i(),l={currentType:13,offset:c,startLoc:u,endLoc:u,lastType:13,lastOffset:c,lastStartLoc:u,lastEndLoc:u,braceNest:0,inLinked:!1,text:""},s=()=>l,{onError:a}=t;function d(b,A,P,...j){const ee=s();if(A.column+=P,A.offset+=P,a){const z=n?ra(ee.startLoc,A):null,F=Uo(b,z,{domain:C0,args:j});a(F)}}function f(b,A,P){b.endLoc=o(),b.currentType=A;const j={type:A};return n&&(j.loc=ra(b.startLoc,b.endLoc)),P!=null&&(j.value=P),j}const h=b=>f(b,13);function p(b,A){return b.currentChar()===A?(b.next(),A):(d(De.EXPECTED_TOKEN,o(),0,A),"")}function m(b){let A="";for(;b.currentPeek()===Ut||b.currentPeek()===rt;)A+=b.currentPeek(),b.peek();return A}function _(b){const A=m(b);return b.skipToPeek(),A}function g(b){if(b===Zt)return!1;const A=b.charCodeAt(0);return A>=97&&A<=122||A>=65&&A<=90||A===95}function v(b){if(b===Zt)return!1;const A=b.charCodeAt(0);return A>=48&&A<=57}function y(b,A){const{currentType:P}=A;if(P!==2)return!1;m(b);const j=g(b.currentPeek());return b.resetPeek(),j}function E(b,A){const{currentType:P}=A;if(P!==2)return!1;m(b);const j=b.currentPeek()==="-"?b.peek():b.currentPeek(),ee=v(j);return b.resetPeek(),ee}function L(b,A){const{currentType:P}=A;if(P!==2)return!1;m(b);const j=b.currentPeek()===rl;return b.resetPeek(),j}function I(b,A){const{currentType:P}=A;if(P!==7)return!1;m(b);const j=b.currentPeek()===".";return b.resetPeek(),j}function w(b,A){const{currentType:P}=A;if(P!==8)return!1;m(b);const j=g(b.currentPeek());return b.resetPeek(),j}function D(b,A){const{currentType:P}=A;if(!(P===7||P===11))return!1;m(b);const j=b.currentPeek()===":";return b.resetPeek(),j}function T(b,A){const{currentType:P}=A;if(P!==9)return!1;const j=()=>{const z=b.currentPeek();return z==="{"?g(b.peek()):z==="@"||z==="|"||z===":"||z==="."||z===Ut||!z?!1:z===rt?(b.peek(),j()):k(b,!1)},ee=j();return b.resetPeek(),ee}function R(b){m(b);const A=b.currentPeek()==="|";return b.resetPeek(),A}function k(b,A=!0){const P=(ee=!1,z="")=>{const F=b.currentPeek();return F==="{"||F==="@"||!F?ee:F==="|"?!(z===Ut||z===rt):F===Ut?(b.peek(),P(!0,Ut)):F===rt?(b.peek(),P(!0,rt)):!0},j=P();return A&&b.resetPeek(),j}function M(b,A){const P=b.currentChar();return P===Zt?Zt:A(P)?(b.next(),P):null}function H(b){const A=b.charCodeAt(0);return A>=97&&A<=122||A>=65&&A<=90||A>=48&&A<=57||A===95||A===36}function N(b){return M(b,H)}function G(b){const A=b.charCodeAt(0);return A>=97&&A<=122||A>=65&&A<=90||A>=48&&A<=57||A===95||A===36||A===45}function Y(b){return M(b,G)}function V(b){const A=b.charCodeAt(0);return A>=48&&A<=57}function W(b){return M(b,V)}function J(b){const A=b.charCodeAt(0);return A>=48&&A<=57||A>=65&&A<=70||A>=97&&A<=102}function te(b){return M(b,J)}function ie(b){let A="",P="";for(;A=W(b);)P+=A;return P}function ue(b){let A="";for(;;){const P=b.currentChar();if(P==="{"||P==="}"||P==="@"||P==="|"||!P)break;if(P===Ut||P===rt)if(k(b))A+=P,b.next();else{if(R(b))break;A+=P,b.next()}else A+=P,b.next()}return A}function me(b){_(b);let A="",P="";for(;A=Y(b);)P+=A;return b.currentChar()===Zt&&d(De.UNTERMINATED_CLOSING_BRACE,o(),0),P}function ve(b){_(b);let A="";return b.currentChar()==="-"?(b.next(),A+="-".concat(ie(b))):A+=ie(b),b.currentChar()===Zt&&d(De.UNTERMINATED_CLOSING_BRACE,o(),0),A}function Te(b){return b!==rl&&b!==rt}function Ae(b){_(b),p(b,"'");let A="",P="";for(;A=M(b,Te);)A==="\\"?P+=U(b):P+=A;const j=b.currentChar();return j===rt||j===Zt?(d(De.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),0),j===rt&&(b.next(),p(b,"'")),P):(p(b,"'"),P)}function U(b){const A=b.currentChar();switch(A){case"\\":case"'":return b.next(),"\\".concat(A);case"u":return K(b,A,4);case"U":return K(b,A,6);default:return d(De.UNKNOWN_ESCAPE_SEQUENCE,o(),0,A),""}}function K(b,A,P){p(b,A);let j="";for(let ee=0;ee<P;ee++){const z=te(b);if(!z){d(De.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),0,"\\".concat(A).concat(j).concat(b.currentChar()));break}j+=z}return"\\".concat(A).concat(j)}function X(b){return b!=="{"&&b!=="}"&&b!==Ut&&b!==rt}function Q(b){_(b);let A="",P="";for(;A=M(b,X);)P+=A;return P}function fe(b){let A="",P="";for(;A=N(b);)P+=A;return P}function S(b){const A=P=>{const j=b.currentChar();return j==="{"||j==="@"||j==="|"||j==="("||j===")"||!j||j===Ut?P:(P+=j,b.next(),A(P))};return A("")}function C(b){_(b);const A=p(b,"|");return _(b),A}function O(b,A){let P=null;switch(b.currentChar()){case"{":return A.braceNest>=1&&d(De.NOT_ALLOW_NEST_PLACEHOLDER,o(),0),b.next(),P=f(A,2,"{"),_(b),A.braceNest++,P;case"}":return A.braceNest>0&&A.currentType===2&&d(De.EMPTY_PLACEHOLDER,o(),0),b.next(),P=f(A,3,"}"),A.braceNest--,A.braceNest>0&&_(b),A.inLinked&&A.braceNest===0&&(A.inLinked=!1),P;case"@":return A.braceNest>0&&d(De.UNTERMINATED_CLOSING_BRACE,o(),0),P=x(b,A)||h(A),A.braceNest=0,P;default:{let ee=!0,z=!0,F=!0;if(R(b))return A.braceNest>0&&d(De.UNTERMINATED_CLOSING_BRACE,o(),0),P=f(A,1,C(b)),A.braceNest=0,A.inLinked=!1,P;if(A.braceNest>0&&(A.currentType===4||A.currentType===5||A.currentType===6))return d(De.UNTERMINATED_CLOSING_BRACE,o(),0),A.braceNest=0,B(b,A);if(ee=y(b,A))return P=f(A,4,me(b)),_(b),P;if(z=E(b,A))return P=f(A,5,ve(b)),_(b),P;if(F=L(b,A))return P=f(A,6,Ae(b)),_(b),P;if(!ee&&!z&&!F)return P=f(A,12,Q(b)),d(De.INVALID_TOKEN_IN_PLACEHOLDER,o(),0,P.value),_(b),P;break}}return P}function x(b,A){const{currentType:P}=A;let j=null;const ee=b.currentChar();switch((P===7||P===8||P===11||P===9)&&(ee===rt||ee===Ut)&&d(De.INVALID_LINKED_FORMAT,o(),0),ee){case"@":return b.next(),j=f(A,7,"@"),A.inLinked=!0,j;case".":return _(b),b.next(),f(A,8,".");case":":return _(b),b.next(),f(A,9,":");default:return R(b)?(j=f(A,1,C(b)),A.braceNest=0,A.inLinked=!1,j):I(b,A)||D(b,A)?(_(b),x(b,A)):w(b,A)?(_(b),f(A,11,fe(b))):T(b,A)?(_(b),ee==="{"?O(b,A)||j:f(A,10,S(b))):(P===7&&d(De.INVALID_LINKED_FORMAT,o(),0),A.braceNest=0,A.inLinked=!1,B(b,A))}}function B(b,A){let P={type:13};if(A.braceNest>0)return O(b,A)||h(A);if(A.inLinked)return x(b,A)||h(A);switch(b.currentChar()){case"{":return O(b,A)||h(A);case"}":return d(De.UNBALANCED_CLOSING_BRACE,o(),0),b.next(),f(A,3,"}");case"@":return x(b,A)||h(A);default:{if(R(b))return P=f(A,1,C(b)),A.braceNest=0,A.inLinked=!1,P;if(k(b))return f(A,0,ue(b));break}}return P}function $(){const{currentType:b,offset:A,startLoc:P,endLoc:j}=l;return l.lastType=b,l.lastOffset=A,l.lastStartLoc=P,l.lastEndLoc=j,l.offset=i(),l.startLoc=o(),r.currentChar()===Zt?f(l,13):B(r,l)}return{nextToken:$,currentOffset:i,currentPosition:o,context:s}}const D0="parser",M0=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function L0(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"�"}}}function R0(e={}){const t=e.location!==!1,{onError:n}=e;function r(g,v,y,E,...L){const I=g.currentPosition();if(I.offset+=E,I.column+=E,n){const w=t?ra(y,I):null,D=Uo(v,w,{domain:D0,args:L});n(D)}}function i(g,v,y){const E={type:g};return t&&(E.start=v,E.end=v,E.loc={start:y,end:y}),E}function o(g,v,y,E){t&&(g.end=v,g.loc&&(g.loc.end=y))}function u(g,v){const y=g.context(),E=i(3,y.offset,y.startLoc);return E.value=v,o(E,g.currentOffset(),g.currentPosition()),E}function c(g,v){const y=g.context(),{lastOffset:E,lastStartLoc:L}=y,I=i(5,E,L);return I.index=parseInt(v,10),g.nextToken(),o(I,g.currentOffset(),g.currentPosition()),I}function l(g,v){const y=g.context(),{lastOffset:E,lastStartLoc:L}=y,I=i(4,E,L);return I.key=v,g.nextToken(),o(I,g.currentOffset(),g.currentPosition()),I}function s(g,v){const y=g.context(),{lastOffset:E,lastStartLoc:L}=y,I=i(9,E,L);return I.value=v.replace(M0,L0),g.nextToken(),o(I,g.currentOffset(),g.currentPosition()),I}function a(g){const v=g.nextToken(),y=g.context(),{lastOffset:E,lastStartLoc:L}=y,I=i(8,E,L);return v.type!==11?(r(g,De.UNEXPECTED_EMPTY_LINKED_MODIFIER,y.lastStartLoc,0),I.value="",o(I,E,L),{nextConsumeToken:v,node:I}):(v.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,kt(v)),I.value=v.value||"",o(I,g.currentOffset(),g.currentPosition()),{node:I})}function d(g,v){const y=g.context(),E=i(7,y.offset,y.startLoc);return E.value=v,o(E,g.currentOffset(),g.currentPosition()),E}function f(g){const v=g.context(),y=i(6,v.offset,v.startLoc);let E=g.nextToken();if(E.type===8){const L=a(g);y.modifier=L.node,E=L.nextConsumeToken||g.nextToken()}switch(E.type!==9&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),E=g.nextToken(),E.type===2&&(E=g.nextToken()),E.type){case 10:E.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),y.key=d(g,E.value||"");break;case 4:E.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),y.key=l(g,E.value||"");break;case 5:E.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),y.key=c(g,E.value||"");break;case 6:E.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(E)),y.key=s(g,E.value||"");break;default:{r(g,De.UNEXPECTED_EMPTY_LINKED_KEY,v.lastStartLoc,0);const L=g.context(),I=i(7,L.offset,L.startLoc);return I.value="",o(I,L.offset,L.startLoc),y.key=I,o(y,L.offset,L.startLoc),{nextConsumeToken:E,node:y}}}return o(y,g.currentOffset(),g.currentPosition()),{node:y}}function h(g){const v=g.context(),y=v.currentType===1?g.currentOffset():v.offset,E=v.currentType===1?v.endLoc:v.startLoc,L=i(2,y,E);L.items=[];let I=null;do{const T=I||g.nextToken();switch(I=null,T.type){case 0:T.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(T)),L.items.push(u(g,T.value||""));break;case 5:T.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(T)),L.items.push(c(g,T.value||""));break;case 4:T.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(T)),L.items.push(l(g,T.value||""));break;case 6:T.value==null&&r(g,De.UNEXPECTED_LEXICAL_ANALYSIS,v.lastStartLoc,0,kt(T)),L.items.push(s(g,T.value||""));break;case 7:{const R=f(g);L.items.push(R.node),I=R.nextConsumeToken||null;break}}}while(v.currentType!==13&&v.currentType!==1);const w=v.currentType===1?v.lastOffset:g.currentOffset(),D=v.currentType===1?v.lastEndLoc:g.currentPosition();return o(L,w,D),L}function p(g,v,y,E){const L=g.context();let I=E.items.length===0;const w=i(1,v,y);w.cases=[],w.cases.push(E);do{const D=h(g);I||(I=D.items.length===0),w.cases.push(D)}while(L.currentType!==13);return I&&r(g,De.MUST_HAVE_MESSAGES_IN_PLURAL,y,0),o(w,g.currentOffset(),g.currentPosition()),w}function m(g){const v=g.context(),{offset:y,startLoc:E}=v,L=h(g);return v.currentType===13?L:p(g,y,E,L)}function _(g){const v=I0(g,We({},e)),y=v.context(),E=i(0,y.offset,y.startLoc);return t&&E.loc&&(E.loc.source=g),E.body=m(v),e.onCacheKey&&(E.cacheKey=e.onCacheKey(g)),y.currentType!==13&&r(v,De.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,g[y.offset]||""),o(E,v.currentOffset(),v.currentPosition()),E}return{parse:_}}function kt(e){if(e.type===13)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function k0(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:o=>(n.helpers.add(o),o)}}function il(e,t){for(let n=0;n<e.length;n++)Fa(e[n],t)}function Fa(e,t){switch(e.type){case 1:il(e.cases,t),t.helper("plural");break;case 2:il(e.items,t);break;case 6:{Fa(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function N0(e,t={}){const n=k0(e);n.helper("normalize"),e.body&&Fa(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function x0(e){const t=e.body;return t.type===2?ol(t):t.cases.forEach(n=>ol(n)),e}function ol(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=Pa(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}function qn(e){switch(e.t=e.type,e.type){case 0:{const t=e;qn(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let r=0;r<n.length;r++)qn(n[r]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let r=0;r<n.length;r++)qn(n[r]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;qn(t.key),t.k=t.key,delete t.key,t.modifier&&(qn(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function P0(e,t){const{filename:n,breakLineCode:r,needIndent:i}=t,o=t.location!==!1,u={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:i,indentLevel:0};o&&e.loc&&(u.source=e.loc.source);const c=()=>u;function l(m,_){u.code+=m}function s(m,_=!0){const g=_?r:"";l(i?g+"  ".repeat(m):g)}function a(m=!0){const _=++u.indentLevel;m&&s(_)}function d(m=!0){const _=--u.indentLevel;m&&s(_)}function f(){s(u.indentLevel)}return{context:c,push:l,indent:a,deindent:d,newline:f,helper:m=>"_".concat(m),needIndent:()=>u.needIndent}}function F0(e,t){const{helper:n}=e;e.push("".concat(n("linked"),"(")),Qn(e,t.key),t.modifier?(e.push(", "),Qn(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function B0(e,t){const{helper:n,needIndent:r}=e;e.push("".concat(n("normalize"),"([")),e.indent(r());const i=t.items.length;for(let o=0;o<i&&(Qn(e,t.items[o]),o!==i-1);o++)e.push(", ");e.deindent(r()),e.push("])")}function $0(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push("".concat(n("plural"),"([")),e.indent(r());const i=t.cases.length;for(let o=0;o<i&&(Qn(e,t.cases[o]),o!==i-1);o++)e.push(", ");e.deindent(r()),e.push("])")}}function H0(e,t){t.body?Qn(e,t.body):e.push("null")}function Qn(e,t){const{helper:n}=e;switch(t.type){case 0:H0(e,t);break;case 1:$0(e,t);break;case 2:B0(e,t);break;case 6:F0(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push("".concat(n("interpolate"),"(").concat(n("list"),"(").concat(t.index,"))"),t);break;case 4:e.push("".concat(n("interpolate"),"(").concat(n("named"),"(").concat(JSON.stringify(t.key),"))"),t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const j0=(e,t={})=>{const n=re(t.mode)?t.mode:"normal",r=re(t.filename)?t.filename:"message.intl";t.sourceMap;const i=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":"\n",o=t.needIndent?t.needIndent:n!=="arrow",u=e.helpers||[],c=P0(e,{filename:r,breakLineCode:i,needIndent:o});c.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(o),u.length>0&&(c.push("const { ".concat(Pa(u.map(a=>"".concat(a,": _").concat(a)),", ")," } = ctx")),c.newline()),c.push("return "),Qn(c,e),c.deindent(o),c.push("}"),delete e.helpers;const{code:l,map:s}=c.context();return{ast:e,code:l,map:s?s.toJSON():void 0}};function U0(e,t={}){const n=We({},t),r=!!n.jit,i=!!n.minify,o=n.optimize==null?!0:n.optimize,c=R0(n).parse(e);return r?(o&&x0(c),i&&qn(c),{ast:c,code:""}):(N0(c,n),j0(c,n))}/*!
  * core-base v11.1.2
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Y0(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Sn().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Sn().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function fs(e){return n=>q0(n,e)}function q0(e,t){const n=V0(t);if(n==null)throw Pr(0);if(Ba(n)===1){const o=z0(n);return e.plural(o.reduce((u,c)=>[...u,sl(e,c)],[]))}else return sl(e,n)}const G0=["b","body"];function V0(e){return gn(e,G0)}const W0=["c","cases"];function z0(e){return gn(e,W0,[])}function sl(e,t){const n=K0(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const r=J0(t).reduce((i,o)=>[...i,ia(e,o)],[]);return e.normalize(r)}}const X0=["s","static"];function K0(e){return gn(e,X0)}const Q0=["i","items"];function J0(e){return gn(e,Q0,[])}function ia(e,t){const n=Ba(t);switch(n){case 3:return li(t,n);case 9:return li(t,n);case 4:{const r=t;if(St(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(St(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Pr(n)}case 5:{const r=t;if(St(r,"i")&&Ye(r.i))return e.interpolate(e.list(r.i));if(St(r,"index")&&Ye(r.index))return e.interpolate(e.list(r.index));throw Pr(n)}case 6:{const r=t,i=n1(r),o=i1(r);return e.linked(ia(e,o),i?ia(e,i):void 0,e.type)}case 7:return li(t,n);case 8:return li(t,n);default:throw new Error("unhandled node on format message part: ".concat(n))}}const Z0=["t","type"];function Ba(e){return gn(e,Z0)}const e1=["v","value"];function li(e,t){const n=gn(e,e1);if(n)return n;throw Pr(t)}const t1=["m","modifier"];function n1(e){return gn(e,t1)}const r1=["k","key"];function i1(e){const t=gn(e,r1);if(t)return t;throw Pr(6)}function gn(e,t,n){for(let r=0;r<t.length;r++){const i=t[r];if(St(e,i)&&e[i]!=null)return e[i]}return n}function Pr(e){return new Error("unhandled node type: ".concat(e))}const o1=e=>e;let ci=Le();function Jn(e){return Se(e)&&Ba(e)===0&&(St(e,"b")||St(e,"body"))}function s1(e,t={}){let n=!1;const r=t.onError||E0;return t.onError=i=>{n=!0,r(i)},{...U0(e,t),detectError:n}}function a1(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&re(e)){Oe(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||o1)(e),i=ci[r];if(i)return i;const{ast:o,detectError:u}=s1(e,{...t,location:!1,jit:!0}),c=fs(o);return u?c:ci[r]=c}else{const n=e.cacheKey;if(n){const r=ci[n];return r||(ci[n]=fs(e))}else return fs(e)}}let Fr=null;function u1(e){Fr=e}function l1(e,t,n){Fr&&Fr.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const c1=f1("function:translate");function f1(e){return t=>Fr&&Fr.emit(e,t)}const zt={INVALID_ARGUMENT:b0,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},d1=24;function Xt(e){return Uo(e,null,void 0)}function $a(e,t){return t.locale!=null?al(t.locale):al(e.locale)}let ds;function al(e){if(re(e))return e;if(Ne(e)){if(e.resolvedOnce&&ds!=null)return ds;if(e.constructor.name==="Function"){const t=e();if(g0(t))throw Xt(zt.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return ds=t}else throw Xt(zt.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw Xt(zt.NOT_SUPPORT_LOCALE_TYPE)}function h1(e,t,n){return[...new Set([n,...qe(t)?t:Se(t)?Object.keys(t):re(t)?[t]:[n]])]}function ah(e,t,n){const r=re(n)?n:Br,i=e;i.__localeChainCache||(i.__localeChainCache=new Map);let o=i.__localeChainCache.get(r);if(!o){o=[];let u=[n];for(;qe(u);)u=ul(o,u,t);const c=qe(t)||!be(t)?t:t.default?t.default:null;u=re(c)?[c]:c,qe(u)&&ul(o,u,!1),i.__localeChainCache.set(r,o)}return o}function ul(e,t,n){let r=!0;for(let i=0;i<t.length&&Oe(r);i++){const o=t[i];re(o)&&(r=m1(e,t[i],n))}return r}function m1(e,t,n){let r;const i=t.split("-");do{const o=i.join("-");r=p1(e,o,n),i.splice(-1,1)}while(i.length&&r===!0);return r}function p1(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const i=t.replace(/!/g,"");e.push(i),(qe(n)||be(n))&&n[i]&&(r=n[i])}return r}const _n=[];_n[0]={w:[0],i:[3,0],"[":[4],o:[7]};_n[1]={w:[1],".":[2],"[":[4],o:[7]};_n[2]={w:[2],i:[3,0],0:[3,0]};_n[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};_n[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};_n[5]={"'":[4,0],o:8,l:[5,0]};_n[6]={'"':[4,0],o:8,l:[6,0]};const g1=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function _1(e){return g1.test(e)}function v1(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function y1(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function b1(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:_1(t)?v1(t):"*"+t}function E1(e){const t=[];let n=-1,r=0,i=0,o,u,c,l,s,a,d;const f=[];f[0]=()=>{u===void 0?u=c:u+=c},f[1]=()=>{u!==void 0&&(t.push(u),u=void 0)},f[2]=()=>{f[0](),i++},f[3]=()=>{if(i>0)i--,r=4,f[0]();else{if(i=0,u===void 0||(u=b1(u),u===!1))return!1;f[1]()}};function h(){const p=e[n+1];if(r===5&&p==="'"||r===6&&p==='"')return n++,c="\\"+p,f[0](),!0}for(;r!==null;)if(n++,o=e[n],!(o==="\\"&&h())){if(l=y1(o),d=_n[r],s=d[l]||d.l||8,s===8||(r=s[0],s[1]!==void 0&&(a=f[s[1]],a&&(c=o,a()===!1))))return;if(r===7)return t}}const ll=new Map;function A1(e,t){return Se(e)?e[t]:null}function w1(e,t){if(!Se(e))return null;let n=ll.get(t);if(n||(n=E1(t),n&&ll.set(t,n)),!n)return null;const r=n.length;let i=e,o=0;for(;o<r;){const u=i[n[o]];if(u===void 0||Ne(i))return null;i=u,o++}return i}const O1="11.1.2",Yo=-1,Br="en-US",cl="",fl=e=>"".concat(e.charAt(0).toLocaleUpperCase()).concat(e.substr(1));function T1(){return{upper:(e,t)=>t==="text"&&re(e)?e.toUpperCase():t==="vnode"&&Se(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&re(e)?e.toLowerCase():t==="vnode"&&Se(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&re(e)?fl(e):t==="vnode"&&Se(e)&&"__v_isVNode"in e?fl(e.children):e}}let uh;function S1(e){uh=e}let lh;function C1(e){lh=e}let ch;function I1(e){ch=e}let fh=null;const D1=e=>{fh=e},M1=()=>fh;let dh=null;const dl=e=>{dh=e},L1=()=>dh;let hl=0;function R1(e={}){const t=Ne(e.onWarn)?e.onWarn:v0,n=re(e.version)?e.version:O1,r=re(e.locale)||Ne(e.locale)?e.locale:Br,i=Ne(r)?Br:r,o=qe(e.fallbackLocale)||be(e.fallbackLocale)||re(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:i,u=be(e.messages)?e.messages:hs(i),c=be(e.datetimeFormats)?e.datetimeFormats:hs(i),l=be(e.numberFormats)?e.numberFormats:hs(i),s=We(Le(),e.modifiers,T1()),a=e.pluralRules||Le(),d=Ne(e.missing)?e.missing:null,f=Oe(e.missingWarn)||Kn(e.missingWarn)?e.missingWarn:!0,h=Oe(e.fallbackWarn)||Kn(e.fallbackWarn)?e.fallbackWarn:!0,p=!!e.fallbackFormat,m=!!e.unresolving,_=Ne(e.postTranslation)?e.postTranslation:null,g=be(e.processor)?e.processor:null,v=Oe(e.warnHtmlMessage)?e.warnHtmlMessage:!0,y=!!e.escapeParameter,E=Ne(e.messageCompiler)?e.messageCompiler:uh,L=Ne(e.messageResolver)?e.messageResolver:lh||A1,I=Ne(e.localeFallbacker)?e.localeFallbacker:ch||h1,w=Se(e.fallbackContext)?e.fallbackContext:void 0,D=e,T=Se(D.__datetimeFormatters)?D.__datetimeFormatters:new Map,R=Se(D.__numberFormatters)?D.__numberFormatters:new Map,k=Se(D.__meta)?D.__meta:{};hl++;const M={version:n,cid:hl,locale:r,fallbackLocale:o,messages:u,modifiers:s,pluralRules:a,missing:d,missingWarn:f,fallbackWarn:h,fallbackFormat:p,unresolving:m,postTranslation:_,processor:g,warnHtmlMessage:v,escapeParameter:y,messageCompiler:E,messageResolver:L,localeFallbacker:I,fallbackContext:w,onWarn:t,__meta:k};return M.datetimeFormats=c,M.numberFormats=l,M.__datetimeFormatters=T,M.__numberFormatters=R,__INTLIFY_PROD_DEVTOOLS__&&l1(M,n,k),M}const hs=e=>({[e]:Le()});function Ha(e,t,n,r,i){const{missing:o,onWarn:u}=e;if(o!==null){const c=o(e,n,t,i);return re(c)?c:t}else return t}function sr(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function k1(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function N1(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let r=n+1;r<t.length;r++)if(k1(e,t[r]))return!0;return!1}function ml(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:i,onWarn:o,localeFallbacker:u}=e,{__datetimeFormatters:c}=e,[l,s,a,d]=oa(...t),f=Oe(a.missingWarn)?a.missingWarn:e.missingWarn;Oe(a.fallbackWarn)?a.fallbackWarn:e.fallbackWarn;const h=!!a.part,p=$a(e,a),m=u(e,i,p);if(!re(l)||l==="")return new Intl.DateTimeFormat(p,d).format(s);let _={},g,v=null;const y="datetime format";for(let I=0;I<m.length&&(g=m[I],_=n[g]||{},v=_[l],!be(v));I++)Ha(e,l,g,f,y);if(!be(v)||!re(g))return r?Yo:l;let E="".concat(g,"__").concat(l);jo(d)||(E="".concat(E,"__").concat(JSON.stringify(d)));let L=c.get(E);return L||(L=new Intl.DateTimeFormat(g,We({},v,d)),c.set(E,L)),h?L.formatToParts(s):L.format(s)}const hh=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function oa(...e){const[t,n,r,i]=e,o=Le();let u=Le(),c;if(re(t)){const l=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!l)throw Xt(zt.INVALID_ISO_DATE_ARGUMENT);const s=l[3]?l[3].trim().startsWith("T")?"".concat(l[1].trim()).concat(l[3].trim()):"".concat(l[1].trim(),"T").concat(l[3].trim()):l[1].trim();c=new Date(s);try{c.toISOString()}catch(a){throw Xt(zt.INVALID_ISO_DATE_ARGUMENT)}}else if(h0(t)){if(isNaN(t.getTime()))throw Xt(zt.INVALID_DATE_ARGUMENT);c=t}else if(Ye(t))c=t;else throw Xt(zt.INVALID_ARGUMENT);return re(n)?o.key=n:be(n)&&Object.keys(n).forEach(l=>{hh.includes(l)?u[l]=n[l]:o[l]=n[l]}),re(r)?o.locale=r:be(r)&&(u=r),be(i)&&(u=i),[o.key||"",c,o,u]}function pl(e,t,n){const r=e;for(const i in n){const o="".concat(t,"__").concat(i);r.__datetimeFormatters.has(o)&&r.__datetimeFormatters.delete(o)}}function gl(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:i,onWarn:o,localeFallbacker:u}=e,{__numberFormatters:c}=e,[l,s,a,d]=sa(...t),f=Oe(a.missingWarn)?a.missingWarn:e.missingWarn;Oe(a.fallbackWarn)?a.fallbackWarn:e.fallbackWarn;const h=!!a.part,p=$a(e,a),m=u(e,i,p);if(!re(l)||l==="")return new Intl.NumberFormat(p,d).format(s);let _={},g,v=null;const y="number format";for(let I=0;I<m.length&&(g=m[I],_=n[g]||{},v=_[l],!be(v));I++)Ha(e,l,g,f,y);if(!be(v)||!re(g))return r?Yo:l;let E="".concat(g,"__").concat(l);jo(d)||(E="".concat(E,"__").concat(JSON.stringify(d)));let L=c.get(E);return L||(L=new Intl.NumberFormat(g,We({},v,d)),c.set(E,L)),h?L.formatToParts(s):L.format(s)}const mh=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function sa(...e){const[t,n,r,i]=e,o=Le();let u=Le();if(!Ye(t))throw Xt(zt.INVALID_ARGUMENT);const c=t;return re(n)?o.key=n:be(n)&&Object.keys(n).forEach(l=>{mh.includes(l)?u[l]=n[l]:o[l]=n[l]}),re(r)?o.locale=r:be(r)&&(u=r),be(i)&&(u=i),[o.key||"",c,o,u]}function _l(e,t,n){const r=e;for(const i in n){const o="".concat(t,"__").concat(i);r.__numberFormatters.has(o)&&r.__numberFormatters.delete(o)}}const x1=e=>e,P1=e=>"",F1="text",B1=e=>e.length===0?"":Pa(e),$1=_0;function vl(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function H1(e){const t=Ye(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Ye(e.named.count)||Ye(e.named.n))?Ye(e.named.count)?e.named.count:Ye(e.named.n)?e.named.n:t:t}function j1(e,t){t.count||(t.count=e),t.n||(t.n=e)}function U1(e={}){const t=e.locale,n=H1(e),r=Se(e.pluralRules)&&re(t)&&Ne(e.pluralRules[t])?e.pluralRules[t]:vl,i=Se(e.pluralRules)&&re(t)&&Ne(e.pluralRules[t])?vl:void 0,o=g=>g[r(n,g.length,i)],u=e.list||[],c=g=>u[g],l=e.named||Le();Ye(e.pluralIndex)&&j1(n,l);const s=g=>l[g];function a(g,v){const y=Ne(e.messages)?e.messages(g,!!v):Se(e.messages)?e.messages[g]:!1;return y||(e.parent?e.parent.message(g):P1)}const d=g=>e.modifiers?e.modifiers[g]:x1,f=be(e.processor)&&Ne(e.processor.normalize)?e.processor.normalize:B1,h=be(e.processor)&&Ne(e.processor.interpolate)?e.processor.interpolate:$1,p=be(e.processor)&&re(e.processor.type)?e.processor.type:F1,_={list:c,named:s,plural:o,linked:(g,...v)=>{const[y,E]=v;let L="text",I="";v.length===1?Se(y)?(I=y.modifier||I,L=y.type||L):re(y)&&(I=y||I):v.length===2&&(re(y)&&(I=y||I),re(E)&&(L=E||L));const w=a(g,!0)(_),D=L==="vnode"&&qe(w)&&I?w[0]:w;return I?d(I)(D,L):D},message:a,type:p,interpolate:h,normalize:f,values:We(Le(),u,l)};return _}const yl=()=>"",bt=e=>Ne(e);function bl(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:i,messageCompiler:o,fallbackLocale:u,messages:c}=e,[l,s]=aa(...t),a=Oe(s.missingWarn)?s.missingWarn:e.missingWarn,d=Oe(s.fallbackWarn)?s.fallbackWarn:e.fallbackWarn,f=Oe(s.escapeParameter)?s.escapeParameter:e.escapeParameter,h=!!s.resolvedMessage,p=re(s.default)||Oe(s.default)?Oe(s.default)?o?l:()=>l:s.default:n?o?l:()=>l:null,m=n||p!=null&&(re(p)||Ne(p)),_=$a(e,s);f&&Y1(s);let[g,v,y]=h?[l,_,c[_]||Le()]:ph(e,l,_,u,d,a),E=g,L=l;if(!h&&!(re(E)||Jn(E)||bt(E))&&m&&(E=p,L=E),!h&&(!(re(E)||Jn(E)||bt(E))||!re(v)))return i?Yo:l;let I=!1;const w=()=>{I=!0},D=bt(E)?E:gh(e,l,v,E,L,w);if(I)return E;const T=V1(e,v,y,s),R=U1(T),k=q1(e,D,R),M=r?r(k,l):k;if(__INTLIFY_PROD_DEVTOOLS__){const H={timestamp:Date.now(),key:re(l)?l:bt(E)?E.key:"",locale:v||(bt(E)?E.locale:""),format:re(E)?E:bt(E)?E.source:"",message:M};H.meta=We({},e.__meta,M1()||{}),c1(H)}return M}function Y1(e){qe(e.list)?e.list=e.list.map(t=>re(t)?nl(t):t):Se(e.named)&&Object.keys(e.named).forEach(t=>{re(e.named[t])&&(e.named[t]=nl(e.named[t]))})}function ph(e,t,n,r,i,o){const{messages:u,onWarn:c,messageResolver:l,localeFallbacker:s}=e,a=s(e,r,n);let d=Le(),f,h=null;const p="translate";for(let m=0;m<a.length&&(f=a[m],d=u[f]||Le(),(h=l(d,t))===null&&(h=d[t]),!(re(h)||Jn(h)||bt(h)));m++)if(!N1(f,a)){const _=Ha(e,t,f,o,p);_!==t&&(h=_)}return[h,f,d]}function gh(e,t,n,r,i,o){const{messageCompiler:u,warnHtmlMessage:c}=e;if(bt(r)){const s=r;return s.locale=s.locale||n,s.key=s.key||t,s}if(u==null){const s=()=>r;return s.locale=n,s.key=t,s}const l=u(r,G1(e,n,i,r,c,o));return l.locale=n,l.key=t,l.source=r,l}function q1(e,t,n){return t(n)}function aa(...e){const[t,n,r]=e,i=Le();if(!re(t)&&!Ye(t)&&!bt(t)&&!Jn(t))throw Xt(zt.INVALID_ARGUMENT);const o=Ye(t)?String(t):(bt(t),t);return Ye(n)?i.plural=n:re(n)?i.default=n:be(n)&&!jo(n)?i.named=n:qe(n)&&(i.list=n),Ye(r)?i.plural=r:re(r)?i.default=r:be(r)&&We(i,r),[o,i]}function G1(e,t,n,r,i,o){return{locale:t,key:n,warnHtmlMessage:i,onError:u=>{throw o&&o(u),u},onCacheKey:u=>f0(t,n,u)}}function V1(e,t,n,r){const{modifiers:i,pluralRules:o,messageResolver:u,fallbackLocale:c,fallbackWarn:l,missingWarn:s,fallbackContext:a}=e,f={locale:t,modifiers:i,pluralRules:o,messages:(h,p)=>{let m=u(n,h);if(m==null&&(a||p)){const[,,_]=ph(a||e,h,t,c,l,s);m=u(_,h)}if(re(m)||Jn(m)){let _=!1;const v=gh(e,h,t,m,h,()=>{_=!0});return _?yl:v}else return bt(m)?m:yl}};return e.processor&&(f.processor=e.processor),r.list&&(f.list=r.list),r.named&&(f.named=r.named),Ye(r.plural)&&(f.pluralIndex=r.plural),f}Y0();/*!
  * vue-i18n v11.1.2
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const W1="11.1.2";function z1(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(Sn().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(Sn().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Sn().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Sn().__INTLIFY_PROD_DEVTOOLS__=!1)}const at={UNEXPECTED_RETURN_TYPE:d1,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32};function gt(e,...t){return Uo(e,null,void 0)}const ua=pn("__translateVNode"),la=pn("__datetimeParts"),ca=pn("__numberParts"),_h=pn("__setPluralRules"),vh=pn("__injectWithOption"),fa=pn("__dispose");function $r(e){if(!Se(e))return e;for(const t in e)if(St(e,t))if(!t.includes("."))Se(e[t])&&$r(e[t]);else{const n=t.split("."),r=n.length-1;let i=e,o=!1;for(let u=0;u<r;u++){if(n[u]==="__proto__")throw new Error("unsafe key: ".concat(n[u]));if(n[u]in i||(i[n[u]]=Le()),!Se(i[n[u]])){o=!0;break}i=i[n[u]]}o||(i[n[r]]=e[t],delete e[t]),Se(i[n[r]])&&$r(i[n[r]])}return e}function ja(e,t){const{messages:n,__i18n:r,messageResolver:i,flatJson:o}=t,u=be(n)?n:qe(r)?Le():{[e]:Le()};if(qe(r)&&r.forEach(c=>{if("locale"in c&&"resource"in c){const{locale:l,resource:s}=c;l?(u[l]=u[l]||Le(),fo(s,u[l])):fo(s,u)}else re(c)&&fo(JSON.parse(c),u)}),i==null&&o)for(const c in u)St(u,c)&&$r(u[c]);return u}function yh(e){return e.type}function bh(e,t,n){let r=Se(t.messages)?t.messages:Le();"__i18nGlobal"in n&&(r=ja(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const i=Object.keys(r);i.length&&i.forEach(o=>{e.mergeLocaleMessage(o,r[o])});{if(Se(t.datetimeFormats)){const o=Object.keys(t.datetimeFormats);o.length&&o.forEach(u=>{e.mergeDateTimeFormat(u,t.datetimeFormats[u])})}if(Se(t.numberFormats)){const o=Object.keys(t.numberFormats);o.length&&o.forEach(u=>{e.mergeNumberFormat(u,t.numberFormats[u])})}}}function El(e){return Ee(Gr,null,e,0)}const Al="__INTLIFY_META__",wl=()=>[],X1=()=>!1;let Ol=0;function Tl(e){return(t,n,r,i)=>e(n,r,dn()||void 0,i)}const K1=()=>{const e=dn();let t=null;return e&&(t=yh(e)[Al])?{[Al]:t}:null};function Ua(e={}){const{__root:t,__injectWithOption:n}=e,r=t===void 0,i=e.flatJson,o=wo?_e:ad;let u=Oe(e.inheritLocale)?e.inheritLocale:!0;const c=o(t&&u?t.locale.value:re(e.locale)?e.locale:Br),l=o(t&&u?t.fallbackLocale.value:re(e.fallbackLocale)||qe(e.fallbackLocale)||be(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:c.value),s=o(ja(c.value,e)),a=o(be(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),d=o(be(e.numberFormats)?e.numberFormats:{[c.value]:{}});let f=t?t.missingWarn:Oe(e.missingWarn)||Kn(e.missingWarn)?e.missingWarn:!0,h=t?t.fallbackWarn:Oe(e.fallbackWarn)||Kn(e.fallbackWarn)?e.fallbackWarn:!0,p=t?t.fallbackRoot:Oe(e.fallbackRoot)?e.fallbackRoot:!0,m=!!e.fallbackFormat,_=Ne(e.missing)?e.missing:null,g=Ne(e.missing)?Tl(e.missing):null,v=Ne(e.postTranslation)?e.postTranslation:null,y=t?t.warnHtmlMessage:Oe(e.warnHtmlMessage)?e.warnHtmlMessage:!0,E=!!e.escapeParameter;const L=t?t.modifiers:be(e.modifiers)?e.modifiers:{};let I=e.pluralRules||t&&t.pluralRules,w;w=(()=>{r&&dl(null);const F={version:W1,locale:c.value,fallbackLocale:l.value,messages:s.value,modifiers:L,pluralRules:I,missing:g===null?void 0:g,missingWarn:f,fallbackWarn:h,fallbackFormat:m,unresolving:!0,postTranslation:v===null?void 0:v,warnHtmlMessage:y,escapeParameter:E,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};F.datetimeFormats=a.value,F.numberFormats=d.value,F.__datetimeFormatters=be(w)?w.__datetimeFormatters:void 0,F.__numberFormatters=be(w)?w.__numberFormatters:void 0;const q=R1(F);return r&&dl(q),q})(),sr(w,c.value,l.value);function T(){return[c.value,l.value,s.value,a.value,d.value]}const R=de({get:()=>c.value,set:F=>{w.locale=F,c.value=F}}),k=de({get:()=>l.value,set:F=>{w.fallbackLocale=F,l.value=F,sr(w,c.value,F)}}),M=de(()=>s.value),H=de(()=>a.value),N=de(()=>d.value);function G(){return Ne(v)?v:null}function Y(F){v=F,w.postTranslation=F}function V(){return _}function W(F){F!==null&&(g=Tl(F)),_=F,w.missing=g}const J=(F,q,se,ce,ye,ze)=>{T();let Ue;try{__INTLIFY_PROD_DEVTOOLS__,r||(w.fallbackContext=t?L1():void 0),Ue=F(w)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(w.fallbackContext=void 0)}if(se!=="translate exists"&&Ye(Ue)&&Ue===Yo||se==="translate exists"&&!Ue){const[vt,Ot]=q();return t&&p?ce(t):ye(vt)}else{if(ze(Ue))return Ue;throw gt(at.UNEXPECTED_RETURN_TYPE)}};function te(...F){return J(q=>Reflect.apply(bl,null,[q,...F]),()=>aa(...F),"translate",q=>Reflect.apply(q.t,q,[...F]),q=>q,q=>re(q))}function ie(...F){const[q,se,ce]=F;if(ce&&!Se(ce))throw gt(at.INVALID_ARGUMENT);return te(q,se,We({resolvedMessage:!0},ce||{}))}function ue(...F){return J(q=>Reflect.apply(ml,null,[q,...F]),()=>oa(...F),"datetime format",q=>Reflect.apply(q.d,q,[...F]),()=>cl,q=>re(q))}function me(...F){return J(q=>Reflect.apply(gl,null,[q,...F]),()=>sa(...F),"number format",q=>Reflect.apply(q.n,q,[...F]),()=>cl,q=>re(q))}function ve(F){return F.map(q=>re(q)||Ye(q)||Oe(q)?El(String(q)):q)}const Ae={normalize:ve,interpolate:F=>F,type:"vnode"};function U(...F){return J(q=>{let se;const ce=q;try{ce.processor=Ae,se=Reflect.apply(bl,null,[ce,...F])}finally{ce.processor=null}return se},()=>aa(...F),"translate",q=>q[ua](...F),q=>[El(q)],q=>qe(q))}function K(...F){return J(q=>Reflect.apply(gl,null,[q,...F]),()=>sa(...F),"number format",q=>q[ca](...F),wl,q=>re(q)||qe(q))}function X(...F){return J(q=>Reflect.apply(ml,null,[q,...F]),()=>oa(...F),"datetime format",q=>q[la](...F),wl,q=>re(q)||qe(q))}function Q(F){I=F,w.pluralRules=I}function fe(F,q){return J(()=>{if(!F)return!1;const se=re(q)?q:c.value,ce=O(se),ye=w.messageResolver(ce,F);return Jn(ye)||bt(ye)||re(ye)},()=>[F],"translate exists",se=>Reflect.apply(se.te,se,[F,q]),X1,se=>Oe(se))}function S(F){let q=null;const se=ah(w,l.value,c.value);for(let ce=0;ce<se.length;ce++){const ye=s.value[se[ce]]||{},ze=w.messageResolver(ye,F);if(ze!=null){q=ze;break}}return q}function C(F){const q=S(F);return q!=null?q:t?t.tm(F)||{}:{}}function O(F){return s.value[F]||{}}function x(F,q){if(i){const se={[F]:q};for(const ce in se)St(se,ce)&&$r(se[ce]);q=se[F]}s.value[F]=q,w.messages=s.value}function B(F,q){s.value[F]=s.value[F]||{};const se={[F]:q};if(i)for(const ce in se)St(se,ce)&&$r(se[ce]);q=se[F],fo(q,s.value[F]),w.messages=s.value}function $(F){return a.value[F]||{}}function b(F,q){a.value[F]=q,w.datetimeFormats=a.value,pl(w,F,q)}function A(F,q){a.value[F]=We(a.value[F]||{},q),w.datetimeFormats=a.value,pl(w,F,q)}function P(F){return d.value[F]||{}}function j(F,q){d.value[F]=q,w.numberFormats=d.value,_l(w,F,q)}function ee(F,q){d.value[F]=We(d.value[F]||{},q),w.numberFormats=d.value,_l(w,F,q)}Ol++,t&&wo&&(Ft(t.locale,F=>{u&&(c.value=F,w.locale=F,sr(w,c.value,l.value))}),Ft(t.fallbackLocale,F=>{u&&(l.value=F,w.fallbackLocale=F,sr(w,c.value,l.value))}));const z={id:Ol,locale:R,fallbackLocale:k,get inheritLocale(){return u},set inheritLocale(F){u=F,F&&t&&(c.value=t.locale.value,l.value=t.fallbackLocale.value,sr(w,c.value,l.value))},get availableLocales(){return Object.keys(s.value).sort()},messages:M,get modifiers(){return L},get pluralRules(){return I||{}},get isGlobal(){return r},get missingWarn(){return f},set missingWarn(F){f=F,w.missingWarn=f},get fallbackWarn(){return h},set fallbackWarn(F){h=F,w.fallbackWarn=h},get fallbackRoot(){return p},set fallbackRoot(F){p=F},get fallbackFormat(){return m},set fallbackFormat(F){m=F,w.fallbackFormat=m},get warnHtmlMessage(){return y},set warnHtmlMessage(F){y=F,w.warnHtmlMessage=F},get escapeParameter(){return E},set escapeParameter(F){E=F,w.escapeParameter=F},t:te,getLocaleMessage:O,setLocaleMessage:x,mergeLocaleMessage:B,getPostTranslationHandler:G,setPostTranslationHandler:Y,getMissingHandler:V,setMissingHandler:W,[_h]:Q};return z.datetimeFormats=H,z.numberFormats=N,z.rt=ie,z.te=fe,z.tm=C,z.d=ue,z.n=me,z.getDateTimeFormat=$,z.setDateTimeFormat=b,z.mergeDateTimeFormat=A,z.getNumberFormat=P,z.setNumberFormat=j,z.mergeNumberFormat=ee,z[vh]=n,z[ua]=U,z[la]=X,z[ca]=K,z}function Q1(e){const t=re(e.locale)?e.locale:Br,n=re(e.fallbackLocale)||qe(e.fallbackLocale)||be(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,r=Ne(e.missing)?e.missing:void 0,i=Oe(e.silentTranslationWarn)||Kn(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,o=Oe(e.silentFallbackWarn)||Kn(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,u=Oe(e.fallbackRoot)?e.fallbackRoot:!0,c=!!e.formatFallbackMessages,l=be(e.modifiers)?e.modifiers:{},s=e.pluralizationRules,a=Ne(e.postTranslation)?e.postTranslation:void 0,d=re(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,f=!!e.escapeParameterHtml,h=Oe(e.sync)?e.sync:!0;let p=e.messages;if(be(e.sharedMessages)){const L=e.sharedMessages;p=Object.keys(L).reduce((w,D)=>{const T=w[D]||(w[D]={});return We(T,L[D]),w},p||{})}const{__i18n:m,__root:_,__injectWithOption:g}=e,v=e.datetimeFormats,y=e.numberFormats,E=e.flatJson;return{locale:t,fallbackLocale:n,messages:p,flatJson:E,datetimeFormats:v,numberFormats:y,missing:r,missingWarn:i,fallbackWarn:o,fallbackRoot:u,fallbackFormat:c,modifiers:l,pluralRules:s,postTranslation:a,warnHtmlMessage:d,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:h,__i18n:m,__root:_,__injectWithOption:g}}function da(e={}){const t=Ua(Q1(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(i){t.locale.value=i},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(i){t.fallbackLocale.value=i},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(i){t.setMissingHandler(i)},get silentTranslationWarn(){return Oe(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(i){t.missingWarn=Oe(i)?!i:i},get silentFallbackWarn(){return Oe(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(i){t.fallbackWarn=Oe(i)?!i:i},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(i){t.fallbackFormat=i},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(i){t.setPostTranslationHandler(i)},get sync(){return t.inheritLocale},set sync(i){t.inheritLocale=i},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(i){t.warnHtmlMessage=i!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(i){t.escapeParameter=i},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...i){return Reflect.apply(t.t,t,[...i])},rt(...i){return Reflect.apply(t.rt,t,[...i])},te(i,o){return t.te(i,o)},tm(i){return t.tm(i)},getLocaleMessage(i){return t.getLocaleMessage(i)},setLocaleMessage(i,o){t.setLocaleMessage(i,o)},mergeLocaleMessage(i,o){t.mergeLocaleMessage(i,o)},d(...i){return Reflect.apply(t.d,t,[...i])},getDateTimeFormat(i){return t.getDateTimeFormat(i)},setDateTimeFormat(i,o){t.setDateTimeFormat(i,o)},mergeDateTimeFormat(i,o){t.mergeDateTimeFormat(i,o)},n(...i){return Reflect.apply(t.n,t,[...i])},getNumberFormat(i){return t.getNumberFormat(i)},setNumberFormat(i,o){t.setNumberFormat(i,o)},mergeNumberFormat(i,o){t.mergeNumberFormat(i,o)}};return r.__extender=n,r}function J1(e,t,n){return{beforeCreate(){const r=dn();if(!r)throw gt(at.UNEXPECTED_ERROR);const i=this.$options;if(i.i18n){const o=i.i18n;if(i.__i18n&&(o.__i18n=i.__i18n),o.__root=t,this===this.$root)this.$i18n=Sl(e,o);else{o.__injectWithOption=!0,o.__extender=n.__vueI18nExtend,this.$i18n=da(o);const u=this.$i18n;u.__extender&&(u.__disposer=u.__extender(this.$i18n))}}else if(i.__i18n)if(this===this.$root)this.$i18n=Sl(e,i);else{this.$i18n=da({__i18n:i.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const o=this.$i18n;o.__extender&&(o.__disposer=o.__extender(this.$i18n))}else this.$i18n=e;i.__i18nGlobal&&bh(t,i,i),this.$t=(...o)=>this.$i18n.t(...o),this.$rt=(...o)=>this.$i18n.rt(...o),this.$te=(o,u)=>this.$i18n.te(o,u),this.$d=(...o)=>this.$i18n.d(...o),this.$n=(...o)=>this.$i18n.n(...o),this.$tm=o=>this.$i18n.tm(o),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const r=dn();if(!r)throw gt(at.UNEXPECTED_ERROR);const i=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,i.__disposer&&(i.__disposer(),delete i.__disposer,delete i.__extender),n.__deleteInstance(r),delete this.$i18n}}}function Sl(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[_h](t.pluralizationRules||e.pluralizationRules);const n=ja(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(r=>e.mergeLocaleMessage(r,n[r])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r])),t.numberFormats&&Object.keys(t.numberFormats).forEach(r=>e.mergeNumberFormat(r,t.numberFormats[r])),e}const Ya={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function Z1({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,i)=>[...r,...i.type===Be?i.children:[i]],[]):t.reduce((n,r)=>{const i=e[r];return i&&(n[r]=i()),n},Le())}function Eh(){return Be}const e_=Ge({name:"i18n-t",props:We({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Ye(e)||!isNaN(e)}},Ya),setup(e,t){const{slots:n,attrs:r}=t,i=e.i18n||_t({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter(d=>d!=="_"),u=Le();e.locale&&(u.locale=e.locale),e.plural!==void 0&&(u.plural=re(e.plural)?+e.plural:e.plural);const c=Z1(t,o),l=i[ua](e.keypath,c,u),s=We(Le(),r),a=re(e.tag)||Se(e.tag)?e.tag:Eh();return Wr(a,s,l)}}}),Cl=e_;function t_(e){return qe(e)&&!re(e[0])}function Ah(e,t,n,r){const{slots:i,attrs:o}=t;return()=>{const u={part:!0};let c=Le();e.locale&&(u.locale=e.locale),re(e.format)?u.key=e.format:Se(e.format)&&(re(e.format.key)&&(u.key=e.format.key),c=Object.keys(e.format).reduce((f,h)=>n.includes(h)?We(Le(),f,{[h]:e.format[h]}):f,Le()));const l=r(e.value,u,c);let s=[u.key];qe(l)?s=l.map((f,h)=>{const p=i[f.type],m=p?p({[f.type]:f.value,index:h,parts:l}):[f.value];return t_(m)&&(m[0].key="".concat(f.type,"-").concat(h)),m}):re(l)&&(s=[l]);const a=We(Le(),o),d=re(e.tag)||Se(e.tag)?e.tag:Eh();return Wr(d,a,s)}}const n_=Ge({name:"i18n-n",props:We({value:{type:Number,required:!0},format:{type:[String,Object]}},Ya),setup(e,t){const n=e.i18n||_t({useScope:e.scope,__useComponent:!0});return Ah(e,t,mh,(...r)=>n[ca](...r))}}),Il=n_;function r_(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function i_(e){const t=u=>{const{instance:c,value:l}=u;if(!c||!c.$)throw gt(at.UNEXPECTED_ERROR);const s=r_(e,c.$),a=Dl(l);return[Reflect.apply(s.t,s,[...Ml(a)]),s]};return{created:(u,c)=>{const[l,s]=t(c);wo&&e.global===s&&(u.__i18nWatcher=Ft(s.locale,()=>{c.instance&&c.instance.$forceUpdate()})),u.__composer=s,u.textContent=l},unmounted:u=>{wo&&u.__i18nWatcher&&(u.__i18nWatcher(),u.__i18nWatcher=void 0,delete u.__i18nWatcher),u.__composer&&(u.__composer=void 0,delete u.__composer)},beforeUpdate:(u,{value:c})=>{if(u.__composer){const l=u.__composer,s=Dl(c);u.textContent=Reflect.apply(l.t,l,[...Ml(s)])}},getSSRProps:u=>{const[c]=t(u);return{textContent:c}}}}function Dl(e){if(re(e))return{path:e};if(be(e)){if(!("path"in e))throw gt(at.REQUIRED_VALUE,"path");return e}else throw gt(at.INVALID_VALUE)}function Ml(e){const{path:t,locale:n,args:r,choice:i,plural:o}=e,u={},c=r||{};return re(n)&&(u.locale=n),Ye(i)&&(u.plural=i),Ye(o)&&(u.plural=o),[t,c,u]}function o_(e,t,...n){const r=be(n[0])?n[0]:{};(Oe(r.globalInstall)?r.globalInstall:!0)&&([Cl.name,"I18nT"].forEach(o=>e.component(o,Cl)),[Il.name,"I18nN"].forEach(o=>e.component(o,Il)),[Rl.name,"I18nD"].forEach(o=>e.component(o,Rl))),e.directive("t",i_(t))}const s_=pn("global-vue-i18n");function wh(e={}){const t=__VUE_I18N_LEGACY_API__&&Oe(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,n=Oe(e.globalInjection)?e.globalInjection:!0,r=new Map,[i,o]=a_(e,t),u=pn("");function c(d){return r.get(d)||null}function l(d,f){r.set(d,f)}function s(d){r.delete(d)}const a={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(d,...f){if(d.__VUE_I18N_SYMBOL__=u,d.provide(d.__VUE_I18N_SYMBOL__,a),be(f[0])){const m=f[0];a.__composerExtend=m.__composerExtend,a.__vueI18nExtend=m.__vueI18nExtend}let h=null;!t&&n&&(h=p_(d,a.global)),__VUE_I18N_FULL_INSTALL__&&o_(d,a,...f),__VUE_I18N_LEGACY_API__&&t&&d.mixin(J1(o,o.__composer,a));const p=d.unmount;d.unmount=()=>{h&&h(),a.dispose(),p()}},get global(){return o},dispose(){i.stop()},__instances:r,__getInstance:c,__setInstance:l,__deleteInstance:s};return a}function _t(e={}){const t=dn();if(t==null)throw gt(at.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw gt(at.NOT_INSTALLED);const n=u_(t),r=c_(n),i=yh(t),o=l_(e,i);if(o==="global")return bh(r,e,i),r;if(o==="parent"){let l=f_(n,t,e.__useComponent);return l==null&&(l=r),l}const u=n;let c=u.__getInstance(t);if(c==null){const l=We({},e);"__i18n"in i&&(l.__i18n=i.__i18n),r&&(l.__root=r),c=Ua(l),u.__composerExtend&&(c[fa]=u.__composerExtend(c)),h_(u,t,c),u.__setInstance(t,c)}return c}function a_(e,t){const n=wa(),r=__VUE_I18N_LEGACY_API__&&t?n.run(()=>da(e)):n.run(()=>Ua(e));if(r==null)throw gt(at.UNEXPECTED_ERROR);return[n,r]}function u_(e){const t=mt(e.isCE?s_:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw gt(e.isCE?at.NOT_INSTALLED_WITH_PROVIDE:at.UNEXPECTED_ERROR);return t}function l_(e,t){return jo(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function c_(e){return e.mode==="composition"?e.global:e.global.__composer}function f_(e,t,n=!1){let r=null;const i=t.root;let o=d_(t,n);for(;o!=null;){const u=e;if(e.mode==="composition")r=u.__getInstance(o);else if(__VUE_I18N_LEGACY_API__){const c=u.__getInstance(o);c!=null&&(r=c.__composer,n&&r&&!r[vh]&&(r=null))}if(r!=null||i===o)break;o=o.parent}return r}function d_(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function h_(e,t,n){$t(()=>{},t),tr(()=>{const r=n;e.__deleteInstance(t);const i=r[fa];i&&(i(),delete r[fa])},t)}const m_=["locale","fallbackLocale","availableLocales"],Ll=["t","rt","d","n","tm","te"];function p_(e,t){const n=Object.create(null);return m_.forEach(i=>{const o=Object.getOwnPropertyDescriptor(t,i);if(!o)throw gt(at.UNEXPECTED_ERROR);const u=je(o.value)?{get(){return o.value.value},set(c){o.value.value=c}}:{get(){return o.get&&o.get()}};Object.defineProperty(n,i,u)}),e.config.globalProperties.$i18n=n,Ll.forEach(i=>{const o=Object.getOwnPropertyDescriptor(t,i);if(!o||!o.value)throw gt(at.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,"$".concat(i),o)}),()=>{delete e.config.globalProperties.$i18n,Ll.forEach(i=>{delete e.config.globalProperties["$".concat(i)]})}}const g_=Ge({name:"i18n-d",props:We({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Ya),setup(e,t){const n=e.i18n||_t({useScope:e.scope,__useComponent:!0});return Ah(e,t,hh,(...r)=>n[la](...r))}}),Rl=g_;z1();S1(a1);C1(w1);I1(ah);if(__INTLIFY_PROD_DEVTOOLS__){const e=Sn();e.__INTLIFY__=!0,u1(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}var pt;(function(e){let t;(function(n){n.ENGLISH="en",n.CHINESE_SIMPLIFIED="zh-Hans",n.CHINESE_TRADITIONAL="zh-Hant",n.CHINESE_TRADITIONAL_HK="zh-Hant-HK",n.CHINESE_TRADITIONAL_TW="zh-Hant-TW",n.JAPANESE="ja",n.KOREAN="ko",n.BAHASA_MELAYU="ms",n.BAHASA_INDONESIA="id"})(t=e.Language||(e.Language={})),e.LANGUAGES={DEFAULT:t.ENGLISH,SUPPORTED:[t.ENGLISH,t.CHINESE_SIMPLIFIED,t.CHINESE_TRADITIONAL],FALLBACKS:{[t.CHINESE_TRADITIONAL_HK]:[t.CHINESE_TRADITIONAL_HK,t.CHINESE_TRADITIONAL,t.CHINESE_TRADITIONAL_TW,t.ENGLISH],[t.CHINESE_TRADITIONAL_TW]:[t.CHINESE_TRADITIONAL_TW,t.CHINESE_TRADITIONAL,t.CHINESE_TRADITIONAL_HK,t.CHINESE_SIMPLIFIED,t.ENGLISH],[t.CHINESE_TRADITIONAL]:[t.CHINESE_TRADITIONAL,t.CHINESE_TRADITIONAL_TW,t.CHINESE_TRADITIONAL_HK,t.CHINESE_SIMPLIFIED,t.ENGLISH],[t.CHINESE_SIMPLIFIED]:[t.CHINESE_SIMPLIFIED,t.CHINESE_TRADITIONAL,t.CHINESE_TRADITIONAL_TW,t.CHINESE_TRADITIONAL_HK,t.ENGLISH],[t.BAHASA_MELAYU]:[t.BAHASA_MELAYU,t.BAHASA_INDONESIA,t.ENGLISH],[t.BAHASA_INDONESIA]:[t.BAHASA_INDONESIA,t.BAHASA_MELAYU,t.ENGLISH],[t.JAPANESE]:[t.JAPANESE,t.ENGLISH],[t.KOREAN]:[t.KOREAN,t.ENGLISH],[t.ENGLISH]:[t.ENGLISH],default:[t.ENGLISH]}},function(n){n.TIME="time",n.DATE="date",n.DATETIME="datetime"}(e.DateTimePickerType||(e.DateTimePickerType={})),function(n){n.DARK="dark",n.LIGHT="light"}(e.ColorScheme||(e.ColorScheme={})),e.DefaultTheme={light:{background:"#FFFFFFFF",text:"#1A1A1AFF",primary:"#F99300FF",secondary:"#7B7B7BFF",tertiary:"#999220FF",accent:"#0A84FFFF"},dark:{background:"#3D3D3DFF",text:"#FFFFFFFF",primary:"#FFB300FF",secondary:"#BABABAFF",tertiary:"#FF3F34FF",accent:"#0AA3FFFF"}}})(pt||(pt={}));const __={button:{back:"Back",cancel:"Cancel",clear:"Clear",confirm:"Confirm",done:"Done",next:"Next",no:"No",no_match:"No Match",ok:"Ok",rejoin:"Rejoin",renew:"Renew",terminate:"Terminate",yes:"Yes",submit:"Submit",delete:"Delete"},form:{search:"search",birth:"birthdate",cardNumber:"card number",createAt:"create at",displayAs:"display as",email:"email",emailOptIn:"email opt in",endTime:"expire",expire:"expire",familyName:"family name",gender:"gender",givenName:"given name",isPaid:"{name} paid",join:"join",mobile:"mobile",mobileOptIn:"mobile opt in",paidTime:"paid time",startTime:"join",terminated:"terminated"},countdown:{before:{prefix:"end on ",suffix:""},after:"Event ended",counting_down:{prefix:"end in ",suffix:""},counting_up:{prefix:"overdue",suffix:""},time_unit:{dd:"day",hh:"hour",mm:"min",ss:"sec"}},error:{oops:"Oops",above_minimum_amount:"must be {amount} or above",below_maximum_amount:"must be {amount} or below",after_maximum_date:"must be {date} or earlier",before_minimum_date:"must be {date} or later",expire:{before_minimum_date:"must from today & join date onwards"},fallback:"Operation error <br/> {{error}}",invalid:"invalid",invalid_date:"wrong date format",invalid_mobile:"Invalid mobile number",invalid_pattern:"wrong format",is_required:"required",join:{after_maximum_date:"must before expire date"},maximum_length:"max. {n} character | max. {n} characters",minimum_length:"min. {n} character | min. {n} characters",maximum_number:"must be {n} or below",minimum_number:"must be {n} or above",message:"Ops, something wrong, please try again later",missing_active_member:"Found an active member, but failed to load the data",offline:"You are offline, please check the network settings",offline_status:"offline",staff_info_missing:"Staff info is missing",timeout:"Operation timeout",all_fields_required:"All fields are required",already_cancelled:"Already cancelled",already_expired:"Already expired",already_terminated:"Already terminated",already_upgraded:"Already upgraded",amount_not_numeric:"Incorrect amount format",cardId_missing:"Card Id is missing",checkinInfo_missing:"Check-in info missing",duplicated_receipt:"Duplicated receipt",endTime_missing:"End time is required",given_and_family_required:"Given name & family name are required",invalid_currency:"Invalid currency",invalid_endTime:"Invalid expiration date",invalid_nameorder:"Invalid name order",location_missing:"Location is disabled or missing",mobile_required:"Mobile is required",not_active_membership:"Not an active membership",not_qualify_extension:"Not qualify extension",not_supported_order:"Not supportted order",ordersummary_or_order_required:"Order / Order Summary is required",past_expire_time:"End time is past expire time",personId_or_profile_required:"Person Id / Profile is required",quantity_not_numeric:"Incorrect quantity format",query_or_cardnumber_required:"Query must not be blank",staff_missing:"Staff is missing",staff_not_found:"Cannot found the staff",config_missing:"Config is missing",websocket:{config_missing:"Config is missing, failed to connect websocket",onmessage_error:"Failed to get websocket data from onMessage event",onmessage_unsubscribe_failed:"Received unknown websocket data, failed to close",onmessage_unsubscribe:"Received unknown websocket data, now closed",onclose_unknown_socket:"Unknown websocket closed",onclose_error:"Failed to get websocket data from onClose event",onerror:"Unknown websocket error",close_failed:"Failed to close event"}}},v_={en:__,"zh-Hans":{button:{back:"返回",cancel:"取消",clear:"清除",confirm:"确定",done:"完成",next:"下一步",no:"否",no_match:"不匹配",ok:"好",rejoin:"重新加入",renew:"更新",terminate:"终止",yes:"是",submit:"提交",delete:"删除"},form:{search:"搜索",birth:"生日",cardNumber:"卡号",createAt:"创建时间",displayAs:"名字展示",email:"邮件地址",emailOptIn:"邮件订阅",endTime:"到期",expire:"到期",familyName:"姓",gender:"性别",givenName:"名",isPaid:"{name}已付",join:"加入",mobile:"手机号码",mobileOptIn:"手机订阅",paidTime:"付费时间",startTime:"加入",terminated:"已终止"},countdown:{before:{prefix:"",suffix:"结束"},after:"活动已结束",counting_down:{prefix:"",suffix:"后结束"},counting_up:{prefix:"超时",suffix:""},time_unit:{dd:"天",hh:"时",mm:"分",ss:"秒"}},error:{oops:"哎呀",above_minimum_amount:"金额至少为{amount}",below_maximum_amount:"金额最多为{amount}",after_maximum_date:"应该在{date}之前",before_minimum_date:"应该在{date}之后",expire:{before_minimum_date:"选择今天和加入日期之后的日期"},fallback:"操作错误 <br/> {{error}}",invalid:"错误",invalid_date:"时间格式错误",invalid_mobile:"手机号码无效",invalid_pattern:"格式错误",is_required:"必填项",join:{after_maximum_date:"选择过期日期之前的日期"},maximum_length:"最多{n}个字符",minimum_length:"最少{n}个字符",maximum_number:"最多为{n}",minimum_number:"最少为{n}",message:"呃，出错了，请稍后再试",missing_active_member:"找到一个有效会员，但是数据加载失败",offline:"当前离线状态, 请检查网路设置",offline_status:"离线",staff_info_missing:"员工数据缺失",timeout:"操作超时",all_fields_required:"所有字段都必填",already_cancelled:"已经取消了",already_expired:"已经过期了",already_terminated:"已经终止了",already_upgraded:"已经升级了",amount_not_numeric:"金额格式不对",cardId_missing:"Card ID缺失",checkinInfo_missing:"签到数据缺失",duplicated_receipt:"该收据已存在",endTime_missing:"过期时间必填",given_and_family_required:"姓名必填",invalid_currency:"币种错误",invalid_endTime:"过期时间错误",invalid_nameorder:"名字顺序错误",location_missing:"位置数据缺失",mobile_required:"手机号码必填",not_active_membership:"不是有效的会员",not_qualify_extension:"该会籍不符合延期条件",not_supported_order:"不支持该订单",ordersummary_or_order_required:"请提供订单详情",past_expire_time:"结束时间超过过期时间",personId_or_profile_required:"个人 ID/资料缺失",quantity_not_numeric:"数量的格式错误",query_or_cardnumber_required:"请提供需要搜寻的内容或者卡号",staff_missing:"员工资料缺失",staff_not_found:"找不到该员工记录",config_missing:"配置缺失",websocket:{config_missing:"配置缺失, 导致连接 WebSocket 失败",onmessage_error:"从 onMessage 事件中获取 WebSocket 数据失败",onmessage_unsubscribe_failed:"接收到无法识别的 WebSocket 数据, 关闭失败",onmessage_unsubscribe:"接收到无法识别的 WebSocket 数据, 现已关闭",onclose_unknown_socket:"已关闭无法识别的 WebSocket",onclose_error:"从 onClose 事件中获取 WebSocket 数据失败",onerror:"无法识别的 WebSocket 错误",close_failed:"WebSocket 关闭失败"}}},"zh-Hant":{button:{back:"返回",cancel:"取消",clear:"清除",confirm:"確定",done:"完成",next:"下一步",no:"否",no_match:"不匹配",ok:"好",rejoin:"重新加入",renew:"更新",terminate:"终止",yes:"是",submit:"提交",delete:"刪除"},form:{search:"搜寻",birth:"生日",cardNumber:"卡號",createAt:"創建時間",displayAs:"名字展示",email:"郵件地址",emailOptIn:"郵件訂閱",endTime:"到期",expire:"到期",familyName:"姓",gender:"性別",givenName:"名",isPaid:"{name}已付",join:"加入",mobile:"手機號碼",mobileOptIn:"手機訂閱",paidTime:"付費時間",startTime:"加入",terminated:"已終止"},countdown:{before:{prefix:"",suffix:"結束"},after:"活動已結束",counting_down:{prefix:"",suffix:"後結束"},counting_up:{prefix:"超時",suffix:""},time_unit:{dd:"天",hh:"时",mm:"分",ss:"秒"}},error:{oops:"哎呀",above_minimum_amount:"金額至少為{amount}",below_maximum_amount:"金額最多為{amount}",after_maximum_date:"應該在{date}之前",before_minimum_date:"應該在{date}之後",expire:{before_minimum_date:"選擇今天和加入日期之後的日期"},fallback:"操作錯誤 <br/> {{error}}",invalid:"錯誤",invalid_date:"時間格式錯誤",invalid_mobile:"手機號碼無效",invalid_pattern:"格式錯誤",is_required:"必填項",join:{after_maximum_date:"選擇過期日期之前的日期"},maximum_length:"最多{n}個字符",minimum_length:"最少{n}個字符",minimum_number:"最少为{n}",maximum_number:"最多为{n}",message:"呃，出錯了，請稍後再試",missing_active_member:"找到一個有效會員，但是數據加載失敗",offline:"當前離線狀態, 請檢查網路設置",offline_status:"離線",staff_info_missing:"員工數據缺失",timeout:"操作超時",all_fields_required:"所有字段都必填",already_cancelled:"已經取消了",already_expired:"已經過期了",already_terminated:"已經終止了",already_upgraded:"已經升級了",amount_not_numeric:"金額格式不對",cardId_missing:"Card ID缺失",checkinInfo_missing:"簽到數據缺失",duplicated_receipt:"該收據已存在",endTime_missing:"過期時間必填",given_and_family_required:"姓名必填",invalid_currency:"幣種錯誤",invalid_endTime:"過期時間錯誤",invalid_nameorder:"名字順序錯誤",location_missing:"位置數據缺失",mobile_required:"手機號碼必填",not_active_membership:"不是有效的會員",not_qualify_extension:"該會籍不符合延期條件",not_supported_order:"不支持該訂單",ordersummary_or_order_required:"請提供訂單詳情",past_expire_time:"結束時間超過過期時間",personId_or_profile_required:"個人 ID/資料缺失",quantity_not_numeric:"數量的格式錯誤",query_or_cardnumber_required:"請提供需要搜尋的內容或者卡號",staff_missing:"員工資料缺失",staff_not_found:"找不到該員工記錄",config_missing:"配置缺失",websocket:{config_missing:"配置缺失, 導致連線 WebSocket 失敗",onmessage_error:"從 onMessage 事件中取得 WebSocket 資料失敗",onmessage_unsubscribe_failed:"接收到無法辨識的 WebSocket 資料, 關閉失敗",onmessage_unsubscribe:"接收到無法辨識的 WebSocket 資料, 現已關閉",onclose_unknown_socket:"已關閉無法辨識的 WebSocket",onclose_error:"從 onClose 事件中取得 WebSocket 資料失敗",onerror:"無法辨識的 WebSocket 錯誤",close_failed:"WebSocket 關閉失敗"}}}};function kl(){window.$perkd.do("window.close")}async function y_(){try{const e=await window.$perkd.do("constants"),{constants:t}=e||{};return t}catch(e){return{error:e}}}async function b_(e){const{method:t,base:n,endpoint:r,cardId:i,...o}=e;if(!(n&&r))return{error:{statusMessage:"config_missing"}};const u={method:t,url:"".concat(n,"/").concat(r),cardId:i,credentials:"perkd",...o};try{return await window.$perkd.do("remote.api",u)}catch(c){return await E_("".concat(n,"/").concat(r),c,u),{error:c}}}async function E_(e,t,n){try{await window.$perkd.do("track.watch",{message:e,error:t,data:n})}catch(r){return{error:r}}}var Nl;(function(e){e.Achieved="achieved",e.Beep="beep",e.BellChord="bellchord",e.Cash="cash",e.Cashier="cashier",e.Chord="chord",e.Correct="correct",e.Done="done",e.Fail="fail",e.Happy="happy",e.Magic="magic",e.Notify="notify",e.Scan="scan",e.ServiceBell="servicebell",e.Success="success",e.SuccessBell="successbell",e.Upsell="upsell",e.WifiOn="wifion"})(Nl||(Nl={}));var xl;(function(e){e.Selection="selection",e.ImpactLight="impactLight",e.ImpactMedium="impactMedium",e.ImpactHeavy="impactHeavy",e.NotificationSuccess="notificationSuccess",e.NotificationWarning="notificationWarning",e.NotificationError="notificationError"})(xl||(xl={}));var Pl;(function(e){e.NATIVE="native",e.BROWSER="browser",e.IN_APP="web"})(Pl||(Pl={}));async function A_(e,t={}){try{const n={key:e};return(t==null?void 0:t.id)!==void 0&&(n.id=t.id),await window.$perkd.do("widget.data",n)}catch(n){return{error:n}}}async function w_(e){try{return await window.$perkd.do("form.hoursSettings",{hours:e||{periods:[]}})}catch(t){return{error:t}}}var An={},ar={},Fl;function O_(){if(Fl)return ar;Fl=1,Object.defineProperty(ar,"__esModule",{value:!0}),ar.Contacts=void 0;var e;return function(t){(function(n){n.MOBILE="mobile",n.HOME="home",n.WORK="work",n.OTHERS="others"})(t.Type||(t.Type={})),function(n){n.BIRTH="birth",n.GRADUATE="graduate",n.MARRIED="married",n.BAPTISED="baptised",n.CONTRACT_START="contractstart",n.CONTRACT_END="contractend"}(t.Dates||(t.Dates={})),function(n){n.WEBSITE="website",n.EMAIL="email",n.SOCIAL="social",n.CUSTOM="custom"}(t.UrlKind||(t.UrlKind={}))}(e||(ar.Contacts=e={})),ar}var Bl;function T_(){if(Bl)return An;Bl=1,Object.defineProperty(An,"__esModule",{value:!0}),An.Socials=An.Persons=void 0;const e=O_();var t;(function(r){(function(o){o.MALE="m",o.FEMALE="f"})(r.Gender||(r.Gender={}));let i;(function(o){o.FAMILY_GIVEN="familygiven",o.GIVEN_FAMILY="givenfamily"})(i=r.NameOrder||(r.NameOrder={})),function(o){o.PERKD="perkd",o.USER="user",o.CUSTOMER="customer",o.SMARTCOLLECTION="smartcollection",o.NATIONAL="national",o.REGISTRATION="registration",o.PASSPORT="passport",o.DRIVER="driver",o.PET="pet"}(r.Identities||(r.Identities={})),function(o){o.MOBILE="mobile",o.EMAIL="email",o.POSTAL="postal",o.SERVICE_TERMS="serviceTerms",o.PRIVACY_POLICY="privacyPolicy"}(r.PermissionChannel||(r.PermissionChannel={})),function(o){o[o.DO_NOT_DISTURB=-2]="DO_NOT_DISTURB",o[o.OPTOUT=-1]="OPTOUT",o[o.UNKNOWN=0]="UNKNOWN",o[o.OPTIN=1]="OPTIN"}(r.PermissionStatus||(r.PermissionStatus={})),function(o){o.CITIZEN="citizen",o.RESIDENT="resident",o.EMPLOYMENT="employment"}(r.Residency||(r.Residency={})),function(o){o.A_POSITIVE="A+",o.A_NEGATIVE="A-",o.B_POSITIVE="B+",o.B_NEGATIVE="B-",o.O_POSITIVE="O+",o.O_NEGATIVE="O-",o.AB_POSITIVE="AB+",o.AB_NEGATIVE="AB-"}(r.BloodType||(r.BloodType={})),function(o){o.DOG="dog",o.CAT="cat",o.BIRD="bird",o.RABBIT="rabbit",o.RODENT="rodent"}(r.Species||(r.Species={})),r.PROFILE={FAMILY_GIVEN:i.FAMILY_GIVEN,GIVEN_FAMILY:i.GIVEN_FAMILY,NAME_ORDER:[i.FAMILY_GIVEN,i.GIVEN_FAMILY],MOBILE:e.Contacts.Type.MOBILE,BIRTH_DATE:e.Contacts.Dates.BIRTH}})(t||(An.Persons=t={}));var n;return function(r){(function(i){i.FRIEND="friend",i.SPOUSE="spouse",i.PARENT="parent",i.CHILD="child",i.SIBLING="sibling",i.COLLEAGUE="colleague",i.CLASSMATE="classmate",i.PET="pet"})(r.Relationship||(r.Relationship={})),function(i){i.parent="child",i.child="parent",i.pet="owner"}(r.InverseRelationship||(r.InverseRelationship={}))}(n||(An.Socials=n={})),An}var S_=T_(),$l;(function(e){e.DEFAULT_NAME_ORDER=S_.Persons.NameOrder.GIVEN_FAMILY})($l||($l={}));var Oo;(function(e){let t;(function(n){n.QRCODE="QRCODE",n.AZTEC="AZTEC",n.DATAMATRIX="DATAMATRIX",n.CODE128="CODE128"})(t=e.Barcode||(e.Barcode={})),e.DEFAULT_BARCODE_TYPE=t.CODE128,e.CODE_SQUARE=[t.QRCODE,"QR_CODE",t.AZTEC,t.DATAMATRIX]})(Oo||(Oo={}));const C_=e=>{const t=/[-a-zA-Z0-9@:%._\\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)?/gi;return!!e&&!!e.match(new RegExp(t))},fi=e=>e&&typeof e=="object"&&!Array.isArray(e),ha=(e,t)=>{if(!fi(e)||!fi(t))return t===void 0?e:t;const n=Array.isArray(e)?[]:{};for(const r in e)e.hasOwnProperty(r)&&(n[r]=fi(e[r])?ha({},e[r]):e[r]);for(const r in t)t.hasOwnProperty(r)&&(Array.isArray(t[r])?n[r]=t[r]==null?e[r]:t[r]:fi(t[r])?r in e?n[r]=ha(e[r],t[r]):n[r]=t[r]:n[r]=t[r]===void 0?e[r]:t[r]);return n};function I_(e,t){const{statusCode:n,statusMessage:r="",code:i,message:o=""}=e||{},u=r||o,c=t("error.".concat(u)),l=!!c&&c!=="error.".concat(u),s=l?c:t("error.message"),a="".concat(n||i||""),d=a&&a!==o.trim()?a:"",f=u!==o||!l?o.trim():"",h="".concat(d," ").concat(f).trim();return"".concat(s).concat(h?": ":"").concat(h)}function D_(e,t=pt.LANGUAGES.SUPPORTED){if(t.includes(e))return e;const{FALLBACKS:n,DEFAULT:r}=pt.LANGUAGES;return Object.keys(n).includes(e)&&(n[e]||n.default).find(o=>t.includes(o))||r}function ma(e){return!!e&&typeof e=="object"&&"error"in e}const M_=e=>{const t=/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(0|1|0?\.\d+)\s*)?\)/,n=e==null?void 0:e.match(t);return n?{r:parseInt(n[1]),g:parseInt(n[2]),b:parseInt(n[3]),o:n[4]?parseFloat(n[4]):1}:{r:0,g:0,b:0,o:1}},Oh=e=>{if(!(e&&e.match(/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/)))return{r:0,g:0,b:0,o:1};const n=e.length>3?e:"#"+e.slice(1,e.length).replace(/(.{1})/g,"$1$1"),r=parseInt(n.slice(1,3),16),i=parseInt(n.slice(3,5),16),o=parseInt(n.slice(5,7),16),u=n.slice(7,9)?Math.round(parseInt(n.slice(7,9),16)*100/255)/100:1;return{r,g:i,b:o,o:u}},L_=e=>e.startsWith("#")?Oh(e):M_(e);function R_(e,t,n){e/=255,t/=255,n/=255;const r=Math.max(e,t,n),i=Math.min(e,t,n);let o=0,u=0;const c=(r+i)/2;if(r!==i){const l=r-i;switch(u=c>.5?l/(2-r-i):l/(r+i),r){case e:o=(t-n)/l+(t<n?6:0);break;case t:o=(n-e)/l+2;break;case n:o=(e-t)/l+4;break}o/=6}return{h:o*360,s:u,l:c}}const k_=e=>{const{r:t,g:n,b:r}=typeof e=="string"?L_(e):e,i=a=>a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4),u=((a,d,f)=>.2126*i(a)+.7152*i(d)+.0722*i(f))(t/255,n/255,r/255),c=R_(t,n,r);if(c.h>=90&&c.h<=150&&c.l<.3)return"#FFFFFF";if(c.h>40&&c.h<70&&c.l>=.5)return u<.6?"#FFFFFF":"#000000";if(c.h>=0&&c.h<=15&&c.s>.6||c.h>15&&c.h<=40&&c.s>.7||c.h>=90&&c.h<=150&&c.s>.6)return u<.55?"#FFFFFF":"#000000";if(c.h>150&&c.h<190)return u<.48?"#FFFFFF":"#000000";if(c.h>=270&&c.h<=330&&c.s>.4)return u<.42?"#FFFFFF":"#000000";if(c.h>40&&c.h<70&&c.l<.5||c.h>=70&&c.h<90&&c.s>.5||c.h>=90&&c.h<=150&&c.s<=.6||c.h>=190&&c.h<=250&&c.s>.6||c.s<.1||c.h>=20&&c.h<=40&&c.s>=.4&&c.s<=.6&&c.l<=.6)return u<.45?"#FFFFFF":"#000000";const l=(1+.05)/(u+.05),s=(u+.05)/(0+.05);return l>s?"#FFFFFF":"#000000"},N_=e=>{const{DEVICE:t,CARD:n,COUNTRY:r,CARDMASTER:i,CONTENT:o,PERSON:u,FONTCSS:c,FONTPATH:l,LANGUAGE:s,CONTEXT:a}=e||{};return{DEVICE:t,CARD:n,CARDMASTER:i,COUNTRY:r,LANGUAGE:s,CONTENT:o,FONTCSS:c,FONTPATH:l,CONTEXT:a,PERSON:u,deviceScheme:window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?pt.ColorScheme.DARK:pt.ColorScheme.LIGHT}},x_=(e,t)=>{var u,c,l,s,a,d,f,h,p,m,_,g,v,y,E,L,I;const{CARD:n,CARDMASTER:r}=e||{},i=((u=n==null?void 0:n.widgets)==null?void 0:u.find(w=>w.key===t))||{},o=((c=r==null?void 0:r.widgets)==null?void 0:c.find(w=>w.key===t))||{};return{API_BASE:((s=(l=i==null?void 0:i.param)==null?void 0:l.api)==null?void 0:s.baseUrl)||((d=(a=o==null?void 0:o.param)==null?void 0:a.api)==null?void 0:d.baseUrl),API:((f=i==null?void 0:i.param)==null?void 0:f.api)||((h=o==null?void 0:o.param)==null?void 0:h.api),COLOR_SCHEME:((p=i==null?void 0:i.param)==null?void 0:p.colorScheme)||((m=o==null?void 0:o.param)==null?void 0:m.colorScheme),SETTINGS:((_=i==null?void 0:i.param)==null?void 0:_.settings)||((g=o==null?void 0:o.param)==null?void 0:g.settings),MASTER_SETTINGS:(v=o==null?void 0:o.param)==null?void 0:v.settings,MEMBERSHIP_PROGRAMS:((E=(y=i==null?void 0:i.param)==null?void 0:y.settings)==null?void 0:E.programs)||((I=(L=o==null?void 0:o.param)==null?void 0:L.settings)==null?void 0:I.programs)}},P_=(e,t)=>{var R,k;const{DEVICE:n,CARDMASTER:r,deviceScheme:i=pt.ColorScheme.LIGHT}=e,{WIDTH:o,HEIGHT:u,MIN_TOP_SPACE:c,STATUS_BAR_HEIGHT:l,NAV_BAR_HEIGHT:s,MIN_BOTTOM_SPACE:a,BOTTOM_TABS_HEIGHT:d,windowHeight:f,IOS:h,IS_LONG_SCREEN:p,APP:m}=n||{},{VERSION:_}=m||{},g={"--width-screen":o+"px","--height-screen":u+"px","--height-window":f+"px","--height-minTop":(c||20)+"px","--height-minBottom":(a||20)+"px","--height-statusBar":(h?l:0)+"px","--height-navigationBar":s+(h?l:0)+"px","--height-tabBar":d+"px","--device-ios":h,"--device-longScreen":p,"--app-version":_,"--font-size-base":Math.round(((o/320-1)*.8+1)*10)+"px","--size-base":"".concat(Math.round(((o/320-1)*.8+1)*10))};Object.keys(g).forEach(M=>{document.documentElement.style.setProperty(M,g?g[M]:"")});const v=t?((R=r==null?void 0:r.widgets)==null?void 0:R.find(M=>M.key===t))||{}:{},{colorScheme:y}=(v==null?void 0:v.param)||{},{brand:E,theme:L=y||i}=r||{},{light:I,dark:w}=((k=v==null?void 0:v.param)==null?void 0:k.theme)||(E==null?void 0:E.style)||{},D={light:Object.assign({},pt.DefaultTheme.light,I),dark:Object.assign({},pt.DefaultTheme.dark,w)};Object.keys(D[L]).forEach(M=>{const H=D[L][M];if(H){const{r:N,g:G,b:Y,o:V}=Oh(H),W=k_(H);document.documentElement.style.setProperty("--color-brand-".concat(M),"rgb(".concat(N,",").concat(G,",").concat(Y,",").concat(V,")")),document.documentElement.style.setProperty("--color-brand-".concat(M,"-contrast"),W)}}),document.documentElement.setAttribute("theme",L)},F_=async(e,t,n)=>{const{ENGLISH:r}=pt.Language,{LANGUAGE:i=r}=e;if(document.documentElement.lang=i,n&&t&&n.locale){const o=t[i]||t[r];n.locale(o)}},B_=e=>ha(v_,e),Rn=c0("appletData",{state:()=>({APPLET_NAME:"manage-hours",START_PAGE:"place-list",ENVIRONMENT:{},WIDGET:{},isOnline:navigator.onLine,app:null,places:[],loading:!1,error:null}),actions:{async fetchData(){const e=await y_();if(ma(e)){console.error("Failed to fetch data:",e.error);return}this.ENVIRONMENT=N_(e),this.WIDGET=x_(this.ENVIRONMENT,this.APPLET_NAME)},async fetchPlaces(){this.loading=!0,this.error=null;try{const e=await A_("place");if(ma(e)){console.error("Failed to fetch places:",e.error),this.error="Failed to load places data";return}if(Array.isArray(e)){this.places=this.adaptPlacesData(e);const t=this.places.filter(n=>!n.id||n.id.trim()==="");t.length>0&&console.error("Found places with empty IDs:",t)}else console.error("Unexpected response format:",e),this.error="Invalid data format received: ".concat(JSON.stringify(e))}catch(e){console.error("Error fetching places:",e),this.error="An unexpected error occurred"}finally{this.loading=!1}},adaptPlacesData(e){return e.map(t=>{var i,o,u,c,l,s,a,d,f,h,p,m,_,g,v;(!t.id||t.id.trim()==="")&&console.error("Place missing ID:",t);const n={general:this.convertPeriodsToDayHours(((i=t.openingHours)==null?void 0:i.periods)||[])};if((o=t.openingHours)!=null&&o.specific&&(n.general.specific=this.normalizeSpecificHours(t.openingHours.specific)),(u=t.dinein)!=null&&u.available){const y=((c=t.dinein.hours)==null?void 0:c.periods)||((l=t.openingHours)==null?void 0:l.periods)||[];n.dinein=this.convertPeriodsToDayHours(y),(s=t.dinein.hours)!=null&&s.specific&&(n.dinein.specific=this.normalizeSpecificHours(t.dinein.hours.specific))}if((a=t.pickup)!=null&&a.available){const y=((d=t.pickup.hours)==null?void 0:d.periods)||((f=t.openingHours)==null?void 0:f.periods)||[];n.pickup=this.convertPeriodsToDayHours(y),(h=t.pickup.hours)!=null&&h.specific&&(n.pickup.specific=this.normalizeSpecificHours(t.pickup.hours.specific))}if((p=t.deliver)!=null&&p.available){const y=((m=t.deliver.hours)==null?void 0:m.periods)||((_=t.openingHours)==null?void 0:_.periods)||[];n.deliver=this.convertPeriodsToDayHours(y),(g=t.deliver.hours)!=null&&g.specific&&(n.deliver.specific=this.normalizeSpecificHours(t.deliver.hours.specific))}return{id:t.id?t.id.trim():"",name:t.name,brand:(v=t.brand)==null?void 0:v.long,hours:n,external:t.external}})},normalizeSpecificHours(e){return Array.isArray(e)?e.map(t=>{const n={...t};return!n.periods||n.periods.length===0?n.periods=[{open:{day:0,time:"0000"},close:{day:0,time:"0000"}}]:n.periods=n.periods.filter(r=>!0),n}):[]},convertPeriodsToDayHours(e){const t=[];for(let n=0;n<7;n++)t[n]={day:n,ranges:[],closed:!0};return!e||e.length===0||e.forEach(n=>{const r=n.open.day-1;if(r<0||r>6){console.error("Invalid day in period: ".concat(JSON.stringify(n)));return}if(n.open.time==="0000"&&n.close.time==="0000"){console.log("Found closed period (00:00-00:00) for day ".concat(r));return}const i=n.open.time.length===4?"".concat(n.open.time.substring(0,2),":").concat(n.open.time.substring(2,4)):n.open.time,o=n.close.time.length===4?"".concat(n.close.time.substring(0,2),":").concat(n.close.time.substring(2,4)):n.close.time,u={open:i,close:o};t[r]&&(t[r].ranges.push(u),t[r].closed=!1)}),t},resetProcessData(){this.places=[],this.loading=!1,this.error=null}}});/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Gn=typeof document<"u";function Th(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function $_(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Th(e.default)}const Ce=Object.assign;function ms(e,t){const n={};for(const r in t){const i=t[r];n[r]=Mt(i)?i.map(e):e(i)}return n}const Sr=()=>{},Mt=Array.isArray,Sh=/#/g,H_=/&/g,j_=/\//g,U_=/=/g,Y_=/\?/g,Ch=/\+/g,q_=/%5B/g,G_=/%5D/g,Ih=/%5E/g,V_=/%60/g,Dh=/%7B/g,W_=/%7C/g,Mh=/%7D/g,z_=/%20/g;function qa(e){return encodeURI(""+e).replace(W_,"|").replace(q_,"[").replace(G_,"]")}function X_(e){return qa(e).replace(Dh,"{").replace(Mh,"}").replace(Ih,"^")}function pa(e){return qa(e).replace(Ch,"%2B").replace(z_,"+").replace(Sh,"%23").replace(H_,"%26").replace(V_,"`").replace(Dh,"{").replace(Mh,"}").replace(Ih,"^")}function K_(e){return pa(e).replace(U_,"%3D")}function Q_(e){return qa(e).replace(Sh,"%23").replace(Y_,"%3F")}function J_(e){return e==null?"":Q_(e).replace(j_,"%2F")}function Hr(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Z_=/\/$/,ev=e=>e.replace(Z_,"");function ps(e,t,n="/"){let r,i={},o="",u="";const c=t.indexOf("#");let l=t.indexOf("?");return c<l&&c>=0&&(l=-1),l>-1&&(r=t.slice(0,l),o=t.slice(l+1,c>-1?c:t.length),i=e(o)),c>-1&&(r=r||t.slice(0,c),u=t.slice(c,t.length)),r=iv(r!=null?r:t,n),{fullPath:r+(o&&"?")+o+u,path:r,query:i,hash:Hr(u)}}function tv(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Hl(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function nv(e,t,n){const r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&Zn(t.matched[r],n.matched[i])&&Lh(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Zn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Lh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!rv(e[n],t[n]))return!1;return!0}function rv(e,t){return Mt(e)?jl(e,t):Mt(t)?jl(t,e):e===t}function jl(e,t){return Mt(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function iv(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),i=r[r.length-1];(i===".."||i===".")&&r.push("");let o=n.length-1,u,c;for(u=0;u<r.length;u++)if(c=r[u],c!==".")if(c==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(u).join("/")}const en={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var jr;(function(e){e.pop="pop",e.push="push"})(jr||(jr={}));var Cr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Cr||(Cr={}));function ov(e){if(!e)if(Gn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ev(e)}const sv=/^[^#]+#/;function av(e,t){return e.replace(sv,"#")+t}function uv(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const qo=()=>({left:window.scrollX,top:window.scrollY});function lv(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=uv(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ul(e,t){return(history.state?history.state.position-t:-1)+e}const ga=new Map;function cv(e,t){ga.set(e,t)}function fv(e){const t=ga.get(e);return ga.delete(e),t}let dv=()=>location.protocol+"//"+location.host;function Rh(e,t){const{pathname:n,search:r,hash:i}=t,o=e.indexOf("#");if(o>-1){let c=i.includes(e.slice(o))?e.slice(o).length:1,l=i.slice(c);return l[0]!=="/"&&(l="/"+l),Hl(l,"")}return Hl(n,e)+r+i}function hv(e,t,n,r){let i=[],o=[],u=null;const c=({state:f})=>{const h=Rh(e,location),p=n.value,m=t.value;let _=0;if(f){if(n.value=h,t.value=f,u&&u===p){u=null;return}_=m?f.position-m.position:0}else r(h);i.forEach(g=>{g(n.value,p,{delta:_,type:jr.pop,direction:_?_>0?Cr.forward:Cr.back:Cr.unknown})})};function l(){u=n.value}function s(f){i.push(f);const h=()=>{const p=i.indexOf(f);p>-1&&i.splice(p,1)};return o.push(h),h}function a(){const{history:f}=window;f.state&&f.replaceState(Ce({},f.state,{scroll:qo()}),"")}function d(){for(const f of o)f();o=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:l,listen:s,destroy:d}}function Yl(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?qo():null}}function mv(e){const{history:t,location:n}=window,r={value:Rh(e,n)},i={value:t.state};i.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,s,a){const d=e.indexOf("#"),f=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+l:dv()+e+l;try{t[a?"replaceState":"pushState"](s,"",f),i.value=s}catch(h){console.error(h),n[a?"replace":"assign"](f)}}function u(l,s){const a=Ce({},t.state,Yl(i.value.back,l,i.value.forward,!0),s,{position:i.value.position});o(l,a,!0),r.value=l}function c(l,s){const a=Ce({},i.value,t.state,{forward:l,scroll:qo()});o(a.current,a,!0);const d=Ce({},Yl(r.value,l,null),{position:a.position+1},s);o(l,d,!1),r.value=l}return{location:r,state:i,push:c,replace:u}}function pv(e){e=ov(e);const t=mv(e),n=hv(e,t.state,t.location,t.replace);function r(o,u=!0){u||n.pauseListeners(),history.go(o)}const i=Ce({location:"",base:e,go:r,createHref:av.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function gv(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),pv(e)}function _v(e){return typeof e=="string"||e&&typeof e=="object"}function kh(e){return typeof e=="string"||typeof e=="symbol"}const Nh=Symbol("");var ql;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ql||(ql={}));function er(e,t){return Ce(new Error,{type:e,[Nh]:!0},t)}function Yt(e,t){return e instanceof Error&&Nh in e&&(t==null||!!(e.type&t))}const Gl="[^/]+?",vv={sensitive:!1,strict:!1,start:!0,end:!0},yv=/[.+*?^${}()[\]/\\]/g;function bv(e,t){const n=Ce({},vv,t),r=[];let i=n.start?"^":"";const o=[];for(const s of e){const a=s.length?[]:[90];n.strict&&!s.length&&(i+="/");for(let d=0;d<s.length;d++){const f=s[d];let h=40+(n.sensitive?.25:0);if(f.type===0)d||(i+="/"),i+=f.value.replace(yv,"\\$&"),h+=40;else if(f.type===1){const{value:p,repeatable:m,optional:_,regexp:g}=f;o.push({name:p,repeatable:m,optional:_});const v=g||Gl;if(v!==Gl){h+=10;try{new RegExp("(".concat(v,")"))}catch(E){throw new Error('Invalid custom RegExp for param "'.concat(p,'" (').concat(v,"): ")+E.message)}}let y=m?"((?:".concat(v,")(?:/(?:").concat(v,"))*)"):"(".concat(v,")");d||(y=_&&s.length<2?"(?:/".concat(y,")"):"/"+y),_&&(y+="?"),i+=y,h+=20,_&&(h+=-8),m&&(h+=-20),v===".*"&&(h+=-50)}a.push(h)}r.push(a)}if(n.strict&&n.end){const s=r.length-1;r[s][r[s].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const u=new RegExp(i,n.sensitive?"":"i");function c(s){const a=s.match(u),d={};if(!a)return null;for(let f=1;f<a.length;f++){const h=a[f]||"",p=o[f-1];d[p.name]=h&&p.repeatable?h.split("/"):h}return d}function l(s){let a="",d=!1;for(const f of e){(!d||!a.endsWith("/"))&&(a+="/"),d=!1;for(const h of f)if(h.type===0)a+=h.value;else if(h.type===1){const{value:p,repeatable:m,optional:_}=h,g=p in s?s[p]:"";if(Mt(g)&&!m)throw new Error('Provided param "'.concat(p,'" is an array but it is not repeatable (* or + modifiers)'));const v=Mt(g)?g.join("/"):g;if(!v)if(_)f.length<2&&(a.endsWith("/")?a=a.slice(0,-1):d=!0);else throw new Error('Missing required param "'.concat(p,'"'));a+=v}}return a||"/"}return{re:u,score:r,keys:o,parse:c,stringify:l}}function Ev(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function xh(e,t){let n=0;const r=e.score,i=t.score;for(;n<r.length&&n<i.length;){const o=Ev(r[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-r.length)===1){if(Vl(r))return 1;if(Vl(i))return-1}return i.length-r.length}function Vl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Av={type:0,value:""},wv=/[a-zA-Z0-9_]/;function Ov(e){if(!e)return[[]];if(e==="/")return[[Av]];if(!e.startsWith("/"))throw new Error('Invalid path "'.concat(e,'"'));function t(h){throw new Error("ERR (".concat(n,')/"').concat(s,'": ').concat(h))}let n=0,r=n;const i=[];let o;function u(){o&&i.push(o),o=[]}let c=0,l,s="",a="";function d(){s&&(n===0?o.push({type:0,value:s}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t("A repeatable param (".concat(s,") must be alone in its segment. eg: '/:ids+.")),o.push({type:1,value:s,regexp:a,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),s="")}function f(){s+=l}for(;c<e.length;){if(l=e[c++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(s&&d(),u()):l===":"?(d(),n=1):f();break;case 4:f(),n=r;break;case 1:l==="("?n=2:wv.test(l)?f():(d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--);break;case 2:l===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+l:n=3:a+=l;break;case 3:d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--,a="";break;default:t("Unknown state");break}}return n===2&&t('Unfinished custom RegExp for param "'.concat(s,'"')),d(),u(),i}function Tv(e,t,n){const r=bv(Ov(e.path),n),i=Ce(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function Sv(e,t){const n=[],r=new Map;t=Kl({strict:!1,end:!0,sensitive:!1},t);function i(d){return r.get(d)}function o(d,f,h){const p=!h,m=zl(d);m.aliasOf=h&&h.record;const _=Kl(t,d),g=[m];if("alias"in d){const E=typeof d.alias=="string"?[d.alias]:d.alias;for(const L of E)g.push(zl(Ce({},m,{components:h?h.record.components:m.components,path:L,aliasOf:h?h.record:m})))}let v,y;for(const E of g){const{path:L}=E;if(f&&L[0]!=="/"){const I=f.record.path,w=I[I.length-1]==="/"?"":"/";E.path=f.record.path+(L&&w+L)}if(v=Tv(E,f,_),h?h.alias.push(v):(y=y||v,y!==v&&y.alias.push(v),p&&d.name&&!Xl(v)&&u(d.name)),Ph(v)&&l(v),m.children){const I=m.children;for(let w=0;w<I.length;w++)o(I[w],v,h&&h.children[w])}h=h||v}return y?()=>{u(y)}:Sr}function u(d){if(kh(d)){const f=r.get(d);f&&(r.delete(d),n.splice(n.indexOf(f),1),f.children.forEach(u),f.alias.forEach(u))}else{const f=n.indexOf(d);f>-1&&(n.splice(f,1),d.record.name&&r.delete(d.record.name),d.children.forEach(u),d.alias.forEach(u))}}function c(){return n}function l(d){const f=Dv(d,n);n.splice(f,0,d),d.record.name&&!Xl(d)&&r.set(d.record.name,d)}function s(d,f){let h,p={},m,_;if("name"in d&&d.name){if(h=r.get(d.name),!h)throw er(1,{location:d});_=h.record.name,p=Ce(Wl(f.params,h.keys.filter(y=>!y.optional).concat(h.parent?h.parent.keys.filter(y=>y.optional):[]).map(y=>y.name)),d.params&&Wl(d.params,h.keys.map(y=>y.name))),m=h.stringify(p)}else if(d.path!=null)m=d.path,h=n.find(y=>y.re.test(m)),h&&(p=h.parse(m),_=h.record.name);else{if(h=f.name?r.get(f.name):n.find(y=>y.re.test(f.path)),!h)throw er(1,{location:d,currentLocation:f});_=h.record.name,p=Ce({},f.params,d.params),m=h.stringify(p)}const g=[];let v=h;for(;v;)g.unshift(v.record),v=v.parent;return{name:_,path:m,params:p,matched:g,meta:Iv(g)}}e.forEach(d=>o(d));function a(){n.length=0,r.clear()}return{addRoute:o,resolve:s,removeRoute:u,clearRoutes:a,getRoutes:c,getRecordMatcher:i}}function Wl(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function zl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Cv(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Cv(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Xl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Iv(e){return e.reduce((t,n)=>Ce(t,n.meta),{})}function Kl(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Dv(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;xh(e,t[o])<0?r=o:n=o+1}const i=Mv(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function Mv(e){let t=e;for(;t=t.parent;)if(Ph(t)&&xh(e,t)===0)return t}function Ph({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Lv(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<r.length;++i){const o=r[i].replace(Ch," "),u=o.indexOf("="),c=Hr(u<0?o:o.slice(0,u)),l=u<0?null:Hr(o.slice(u+1));if(c in t){let s=t[c];Mt(s)||(s=t[c]=[s]),s.push(l)}else t[c]=l}return t}function Ql(e){let t="";for(let n in e){const r=e[n];if(n=K_(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Mt(r)?r.map(o=>o&&pa(o)):[r&&pa(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Rv(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Mt(r)?r.map(i=>i==null?null:""+i):r==null?r:""+r)}return t}const kv=Symbol(""),Jl=Symbol(""),Go=Symbol(""),Ga=Symbol(""),_a=Symbol("");function ur(){let e=[];function t(r){return e.push(r),()=>{const i=e.indexOf(r);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function an(e,t,n,r,i,o=u=>u()){const u=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((c,l)=>{const s=f=>{f===!1?l(er(4,{from:n,to:t})):f instanceof Error?l(f):_v(f)?l(er(2,{from:t,to:f})):(u&&r.enterCallbacks[i]===u&&typeof f=="function"&&u.push(f),c())},a=o(()=>e.call(r&&r.instances[i],t,n,s));let d=Promise.resolve(a);e.length<3&&(d=d.then(s)),d.catch(f=>l(f))})}function gs(e,t,n,r,i=o=>o()){const o=[];for(const u of e)for(const c in u.components){let l=u.components[c];if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(Th(l)){const a=(l.__vccOpts||l)[t];a&&o.push(an(a,n,r,u,c,i))}else{let s=l();o.push(()=>s.then(a=>{if(!a)throw new Error("Couldn't resolve component \"".concat(c,'" at "').concat(u.path,'"'));const d=$_(a)?a.default:a;u.mods[c]=a,u.components[c]=d;const h=(d.__vccOpts||d)[t];return h&&an(h,n,r,u,c,i)()}))}}return o}function Zl(e){const t=mt(Go),n=mt(Ga),r=de(()=>{const l=Z(e.to);return t.resolve(l)}),i=de(()=>{const{matched:l}=r.value,{length:s}=l,a=l[s-1],d=n.matched;if(!a||!d.length)return-1;const f=d.findIndex(Zn.bind(null,a));if(f>-1)return f;const h=ec(l[s-2]);return s>1&&ec(a)===h&&d[d.length-1].path!==h?d.findIndex(Zn.bind(null,l[s-2])):f}),o=de(()=>i.value>-1&&Bv(n.params,r.value.params)),u=de(()=>i.value>-1&&i.value===n.matched.length-1&&Lh(n.params,r.value.params));function c(l={}){if(Fv(l)){const s=t[Z(e.replace)?"replace":"push"](Z(e.to)).catch(Sr);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>s),s}return Promise.resolve()}return{route:r,href:de(()=>r.value.href),isActive:o,isExactActive:u,navigate:c}}function Nv(e){return e.length===1?e[0]:e}const xv=Ge({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Zl,setup(e,{slots:t}){const n=ln(Zl(e)),{options:r}=mt(Go),i=de(()=>({[tc(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[tc(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Nv(t.default(n));return e.custom?o:Wr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),Pv=xv;function Fv(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Bv(e,t){for(const n in t){const r=t[n],i=e[n];if(typeof r=="string"){if(r!==i)return!1}else if(!Mt(i)||i.length!==r.length||r.some((o,u)=>o!==i[u]))return!1}return!0}function ec(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const tc=(e,t,n)=>e!=null?e:t!=null?t:n,$v=Ge({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=mt(_a),i=de(()=>e.route||r.value),o=mt(Jl,0),u=de(()=>{let s=Z(o);const{matched:a}=i.value;let d;for(;(d=a[s])&&!d.components;)s++;return s}),c=de(()=>i.value.matched[u.value]);ao(Jl,de(()=>u.value+1)),ao(kv,c),ao(_a,i);const l=_e();return Ft(()=>[l.value,c.value,e.name],([s,a,d],[f,h,p])=>{a&&(a.instances[d]=s,h&&h!==a&&s&&s===f&&(a.leaveGuards.size||(a.leaveGuards=h.leaveGuards),a.updateGuards.size||(a.updateGuards=h.updateGuards))),s&&a&&(!h||!Zn(a,h)||!f)&&(a.enterCallbacks[d]||[]).forEach(m=>m(s))},{flush:"post"}),()=>{const s=i.value,a=e.name,d=c.value,f=d&&d.components[a];if(!f)return nc(n.default,{Component:f,route:s});const h=d.props[a],p=h?h===!0?s.params:typeof h=="function"?h(s):h:null,_=Wr(f,Ce({},p,t,{onVnodeUnmounted:g=>{g.component.isUnmounted&&(d.instances[a]=null)},ref:l}));return nc(n.default,{Component:_,route:s})||_}}});function nc(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Fh=$v;function Hv(e){const t=Sv(e.routes,e),n=e.parseQuery||Lv,r=e.stringifyQuery||Ql,i=e.history,o=ur(),u=ur(),c=ur(),l=ad(en);let s=en;Gn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=ms.bind(null,U=>""+U),d=ms.bind(null,J_),f=ms.bind(null,Hr);function h(U,K){let X,Q;return kh(U)?(X=t.getRecordMatcher(U),Q=K):Q=U,t.addRoute(Q,X)}function p(U){const K=t.getRecordMatcher(U);K&&t.removeRoute(K)}function m(){return t.getRoutes().map(U=>U.record)}function _(U){return!!t.getRecordMatcher(U)}function g(U,K){if(K=Ce({},K||l.value),typeof U=="string"){const O=ps(n,U,K.path),x=t.resolve({path:O.path},K),B=i.createHref(O.fullPath);return Ce(O,x,{params:f(x.params),hash:Hr(O.hash),redirectedFrom:void 0,href:B})}let X;if(U.path!=null)X=Ce({},U,{path:ps(n,U.path,K.path).path});else{const O=Ce({},U.params);for(const x in O)O[x]==null&&delete O[x];X=Ce({},U,{params:d(O)}),K.params=d(K.params)}const Q=t.resolve(X,K),fe=U.hash||"";Q.params=a(f(Q.params));const S=tv(r,Ce({},U,{hash:X_(fe),path:Q.path})),C=i.createHref(S);return Ce({fullPath:S,hash:fe,query:r===Ql?Rv(U.query):U.query||{}},Q,{redirectedFrom:void 0,href:C})}function v(U){return typeof U=="string"?ps(n,U,l.value.path):Ce({},U)}function y(U,K){if(s!==U)return er(8,{from:K,to:U})}function E(U){return w(U)}function L(U){return E(Ce(v(U),{replace:!0}))}function I(U){const K=U.matched[U.matched.length-1];if(K&&K.redirect){const{redirect:X}=K;let Q=typeof X=="function"?X(U):X;return typeof Q=="string"&&(Q=Q.includes("?")||Q.includes("#")?Q=v(Q):{path:Q},Q.params={}),Ce({query:U.query,hash:U.hash,params:Q.path!=null?{}:U.params},Q)}}function w(U,K){const X=s=g(U),Q=l.value,fe=U.state,S=U.force,C=U.replace===!0,O=I(X);if(O)return w(Ce(v(O),{state:typeof O=="object"?Ce({},fe,O.state):fe,force:S,replace:C}),K||X);const x=X;x.redirectedFrom=K;let B;return!S&&nv(r,Q,X)&&(B=er(16,{to:x,from:Q}),ie(Q,Q,!0,!1)),(B?Promise.resolve(B):R(x,Q)).catch($=>Yt($)?Yt($,2)?$:te($):W($,x,Q)).then($=>{if($){if(Yt($,2))return w(Ce({replace:C},v($.to),{state:typeof $.to=="object"?Ce({},fe,$.to.state):fe,force:S}),K||x)}else $=M(x,Q,!0,C,fe);return k(x,Q,$),$})}function D(U,K){const X=y(U,K);return X?Promise.reject(X):Promise.resolve()}function T(U){const K=ve.values().next().value;return K&&typeof K.runWithContext=="function"?K.runWithContext(U):U()}function R(U,K){let X;const[Q,fe,S]=jv(U,K);X=gs(Q.reverse(),"beforeRouteLeave",U,K);for(const O of Q)O.leaveGuards.forEach(x=>{X.push(an(x,U,K))});const C=D.bind(null,U,K);return X.push(C),Ae(X).then(()=>{X=[];for(const O of o.list())X.push(an(O,U,K));return X.push(C),Ae(X)}).then(()=>{X=gs(fe,"beforeRouteUpdate",U,K);for(const O of fe)O.updateGuards.forEach(x=>{X.push(an(x,U,K))});return X.push(C),Ae(X)}).then(()=>{X=[];for(const O of S)if(O.beforeEnter)if(Mt(O.beforeEnter))for(const x of O.beforeEnter)X.push(an(x,U,K));else X.push(an(O.beforeEnter,U,K));return X.push(C),Ae(X)}).then(()=>(U.matched.forEach(O=>O.enterCallbacks={}),X=gs(S,"beforeRouteEnter",U,K,T),X.push(C),Ae(X))).then(()=>{X=[];for(const O of u.list())X.push(an(O,U,K));return X.push(C),Ae(X)}).catch(O=>Yt(O,8)?O:Promise.reject(O))}function k(U,K,X){c.list().forEach(Q=>T(()=>Q(U,K,X)))}function M(U,K,X,Q,fe){const S=y(U,K);if(S)return S;const C=K===en,O=Gn?history.state:{};X&&(Q||C?i.replace(U.fullPath,Ce({scroll:C&&O&&O.scroll},fe)):i.push(U.fullPath,fe)),l.value=U,ie(U,K,X,C),te()}let H;function N(){H||(H=i.listen((U,K,X)=>{if(!Te.listening)return;const Q=g(U),fe=I(Q);if(fe){w(Ce(fe,{replace:!0,force:!0}),Q).catch(Sr);return}s=Q;const S=l.value;Gn&&cv(Ul(S.fullPath,X.delta),qo()),R(Q,S).catch(C=>Yt(C,12)?C:Yt(C,2)?(w(Ce(v(C.to),{force:!0}),Q).then(O=>{Yt(O,20)&&!X.delta&&X.type===jr.pop&&i.go(-1,!1)}).catch(Sr),Promise.reject()):(X.delta&&i.go(-X.delta,!1),W(C,Q,S))).then(C=>{C=C||M(Q,S,!1),C&&(X.delta&&!Yt(C,8)?i.go(-X.delta,!1):X.type===jr.pop&&Yt(C,20)&&i.go(-1,!1)),k(Q,S,C)}).catch(Sr)}))}let G=ur(),Y=ur(),V;function W(U,K,X){te(U);const Q=Y.list();return Q.length?Q.forEach(fe=>fe(U,K,X)):console.error(U),Promise.reject(U)}function J(){return V&&l.value!==en?Promise.resolve():new Promise((U,K)=>{G.add([U,K])})}function te(U){return V||(V=!U,N(),G.list().forEach(([K,X])=>U?X(U):K()),G.reset()),U}function ie(U,K,X,Q){const{scrollBehavior:fe}=e;if(!Gn||!fe)return Promise.resolve();const S=!X&&fv(Ul(U.fullPath,0))||(Q||!X)&&history.state&&history.state.scroll||null;return No().then(()=>fe(U,K,S)).then(C=>C&&lv(C)).catch(C=>W(C,U,K))}const ue=U=>i.go(U);let me;const ve=new Set,Te={currentRoute:l,listening:!0,addRoute:h,removeRoute:p,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:m,resolve:g,options:e,push:E,replace:L,go:ue,back:()=>ue(-1),forward:()=>ue(1),beforeEach:o.add,beforeResolve:u.add,afterEach:c.add,onError:Y.add,isReady:J,install(U){const K=this;U.component("RouterLink",Pv),U.component("RouterView",Fh),U.config.globalProperties.$router=K,Object.defineProperty(U.config.globalProperties,"$route",{enumerable:!0,get:()=>Z(l)}),Gn&&!me&&l.value===en&&(me=!0,E(i.location).catch(fe=>{}));const X={};for(const fe in en)Object.defineProperty(X,fe,{get:()=>l.value[fe],enumerable:!0});U.provide(Go,K),U.provide(Ga,od(X)),U.provide(_a,l);const Q=U.unmount;ve.add(U),U.unmount=function(){ve.delete(U),ve.size<1&&(s=en,H&&H(),H=null,l.value=en,me=!1,V=!1),Q()}}};function Ae(U){return U.reduce((K,X)=>K.then(()=>T(X)),Promise.resolve())}return Te}function jv(e,t){const n=[],r=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let u=0;u<o;u++){const c=t.matched[u];c&&(e.matched.find(s=>Zn(s,c))?r.push(c):n.push(c));const l=e.matched[u];l&&(t.matched.find(s=>Zn(s,l))||i.push(l))}return[n,r,i]}function zr(){return mt(Go)}function Bh(e){return mt(Ga)}const Uv=Ge({__name:"App",setup(e){const t=Rn(),n=t.WIDGET.COLOR_SCHEME,r=window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)"),i=()=>{t.isOnline=!1},o=()=>{t.isOnline=!0},u=c=>{t.ENVIRONMENT.deviceScheme=c.matches?pt.ColorScheme.DARK:pt.ColorScheme.LIGHT};return $t(async()=>{window.addEventListener("offline",i),window.addEventListener("online",o),n?(document.documentElement.setAttribute("theme",n),t.ENVIRONMENT.deviceScheme=n):r&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",u)}),Yr(async()=>{window.removeEventListener("offline",i),window.removeEventListener("online",o),!n&&r&&window.matchMedia("(prefers-color-scheme: dark)").removeEventListener("change",u)}),(c,l)=>(ne(),et(Z(Fh),null,{default:He(({Component:s,route:a})=>[Ee(Or,{name:a.meta.transition},{default:He(()=>[(ne(),et(Np(s)))]),_:2},1032,["name"])]),_:1}))}}),rc=Ge({__name:"UIIcon",props:{name:{type:String,require:!0},color:{type:String,require:!1}},setup(e){return(t,n)=>(ne(),ae("span",{class:$e(["picon","picon-".concat(e.name),e.color||""])},null,2))}}),Yv={class:"loading-container"},qv={key:0,class:"loading-text"},$h=Ge({__name:"UILoading",props:{colorBackground:{type:Boolean,default:!1},success:{type:Boolean,default:void 0},thickness:{type:String,default:""},size:{type:String,default:"md"},color:{type:String,default:""},emptyColor:{type:String,default:""},successColor:{type:String,default:""},failedColor:{type:String,default:""},text:{type:String,default:""}},setup(e){xg(a=>({"3b55d032":n.value,"016ed342":r.value,"178fa1ae":i.value,"5b848406":o.value,"4a23360b":u.value,"51a6d6fd":c.value}));const t=e,n=de(()=>s(t.color||(t.colorBackground?"#FFFFFF":"accent"))),r=de(()=>s(t.emptyColor||(t.colorBackground?"rgba(255,255,255,0.2)":"var(--color-background-heavy)"))),i=de(()=>s(t.successColor||(t.colorBackground?"#FFFFFF":"success"))),o=de(()=>s(t.failedColor||(t.colorBackground?"#FFFFFF":"error"))),u=de(()=>{const a=t.thickness||t.size;switch(a){case"xxs":return"2px";case"xs":return"2px";case"sm":return"4px";case"md":return"6px";case"lg":return"8px";case"xl":return"10px";default:return a||"6px"}}),c=de(()=>{switch(t.size){case"xxs":return"1em";case"xs":return"2em";case"sm":return"3em";case"md":return"5em";case"lg":return"7em";case"xl":return"10em";default:return t.size||"5em"}}),l=de(()=>t.success===!0?"success":t.success===!1?"failed":"");function s(a){return["primary","accent","success","warning","error"].indexOf(a)!==-1?"var(--color-background-".concat(a,")"):a}return(a,d)=>(ne(),ae("div",Yv,[oe("div",{class:$e("circle-loader ".concat(l.value))},d[0]||(d[0]=[oe("div",{class:"status draw"},null,-1)]),2),e.text?(ne(),ae("div",qv,Me(e.text),1)):pe("",!0)]))}}),Gv={class:"ripple-container"},Vv=Ge({__name:"UIRipple",setup(e,{expose:t}){const n=_e([]),r=_e([]);function i(o){const u=o.currentTarget.getBoundingClientRect(),c=Math.max(u.width,u.height),l=o.clientX-u.left-c/2,s=o.clientY-u.top-c/2,a=Date.now();n.value.push({key:a,style:{width:"".concat(c,"px"),height:"".concat(c,"px"),top:"".concat(s,"px"),left:"".concat(l,"px")}});const d=setTimeout(()=>{n.value=n.value.filter(f=>f.key!==a)},600);r.value.push(d)}return tr(()=>{r.value.forEach(o=>clearTimeout(o)),r.value=[]}),t({createRipple:i}),(o,u)=>(ne(),ae("div",Gv,[(ne(!0),ae(Be,null,qr(n.value,c=>(ne(),ae("div",{class:"ripple",key:c.key,style:dt(c.style)},null,4))),128))]))}}),Wv={key:1,class:"button-title-container"},zv={key:0,class:"status-container"},Bt=Ge({__name:"UIButton",props:{type:{type:String,default:"solid"},color:{type:String,default:"accent"},icon:{type:Object,required:!1},title:String,titleClass:{type:String,default:""},subtitle:String,subtitleClass:{type:String,default:""},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:t}){const n=e,{loading:r}=Ro(n),i=t,o=_e(!1),u=_e(null),c=_e(void 0),l="ontouchstart"in window;let s;$t(()=>{var _,g,v,y,E;l?((_=u.value)==null||_.addEventListener("touchstart",d,{passive:!0}),(g=u.value)==null||g.addEventListener("touchmove",f,{passive:!0}),(v=u.value)==null||v.addEventListener("touchend",h,{passive:!0}),(y=u.value)==null||y.addEventListener("touchcancel",p)):(E=u.value)==null||E.addEventListener("click",a)}),Yr(()=>{var _,g,v,y,E;s&&clearTimeout(s),l?((_=u.value)==null||_.removeEventListener("touchstart",d),(g=u.value)==null||g.removeEventListener("touchmove",f),(v=u.value)==null||v.removeEventListener("touchend",h),(y=u.value)==null||y.removeEventListener("touchcancel",p)):(E=u.value)==null||E.removeEventListener("click",a)});function a(_){var g;_.stopPropagation(),!(o.value||r.value)&&(o.value=!0,(g=c.value)==null||g.createRipple(_),s&&clearTimeout(s),s=setTimeout(()=>{i("click",_),o.value=!1},200))}function d(_){var g;u.value&&!o.value&&!r.value&&(o.value=!0,(g=c.value)==null||g.createRipple(_))}function f(_){if(u.value){const g=_.touches[0],v=u.value.getBoundingClientRect();m(v,g.clientX,g.clientY)||(o.value=!1)}}function h(_){if(_.stopPropagation(),u.value&&o.value){const g=_.changedTouches[0],v=u.value.getBoundingClientRect();m(v,g.clientX,g.clientY)&&i("click",_),o.value=!1}}function p(_){_.stopPropagation(),u.value&&o.value&&(o.value=!1)}function m(_,g,v){const{left:y,top:E,width:L,height:I}=_;return g>=y&&g<=y+L&&v>=E&&v<=E+I}return(_,g)=>(ne(),ae("div",{ref_key:"buttonRef",ref:u,class:$e(["button",e.type,e.color,e.disabled?"disabled":"",e.icon&&!(e.title||e.subtitle)&&e.type==="clear"?"clear-icon":""])},[oe("div",{class:"button-wrapper",style:dt({opacity:Z(r)?0:1})},[e.icon&&e.icon.position!=="right"?(ne(),et(rc,{key:0,name:e.icon.name,class:$e("button-icon ".concat(e.icon.class||""))},null,8,["name","class"])):pe("",!0),e.title||e.subtitle||_.$slots.content?(ne(),ae("span",Wv,[e.title?(ne(),ae("span",{key:0,class:$e("button-title "+e.titleClass)},Me(e.title),3)):pe("",!0),e.subtitle?(ne(),ae("span",{key:1,class:$e("button-subtitle "+e.subtitleClass)},Me(e.subtitle),3)):pe("",!0),Qe(_.$slots,"content")])):pe("",!0),e.icon&&e.icon.position==="right"?(ne(),et(rc,{key:2,name:e.icon.name,class:$e("button-icon "+e.icon.class)},null,8,["name","class"])):pe("",!0)],4),Ee(Or,{name:"fade"},{default:He(()=>[Z(r)?(ne(),ae("div",zv,[Qe(_.$slots,"status",{},()=>[Ee($h,{size:"xxs",colorBackground:e.type==="solid"},null,8,["colorBackground"])])])):pe("",!0)]),_:3}),Ee(Vv,{ref_key:"rippleRef",ref:c},null,512)],2))}}),Xv=["theme"],Kv={key:2,class:"status"},Qv={key:3,class:"status"},Hh=Ge({__name:"UINavigationBar",props:{navBack:{type:Object},theme:{type:String,default:"perkd"},leftClass:{type:String,default:""},centerClass:{type:String,default:""},rightClass:{type:String,default:""},title:String,titleClass:{type:String,default:""},isOnline:{type:Boolean,default:!0},status:{type:String,default:""}},setup(e){const{t}=_t();return(n,r)=>{var i,o,u,c;return ne(),ae("div",{class:$e(["navigation-bar",e.isOnline?"online":"offline"]),theme:e.theme},[n.$slots.leftContent||e.navBack||!e.isOnline||e.isOnline&&e.status?(ne(),ae("div",{key:0,class:$e("left-container "+e.leftClass)},[e.navBack&&((i=e.navBack)==null?void 0:i.type)==="back"?(ne(),et(Bt,{key:0,type:"clear",icon:{name:"back"},class:"back-button",onClick:(o=e.navBack)==null?void 0:o.onClick},null,8,["onClick"])):pe("",!0),e.navBack&&((u=e.navBack)==null?void 0:u.type)==="cancel"?(ne(),et(Bt,{key:1,type:"clear",title:Z(t)("button.cancel"),onClick:(c=e.navBack)==null?void 0:c.onClick},null,8,["title","onClick"])):pe("",!0),e.isOnline?pe("",!0):(ne(),ae("span",Kv,Me(Z(t)("error.offline_status")),1)),e.isOnline&&e.status?(ne(),ae("span",Qv,Me(e.status),1)):pe("",!0),Qe(n.$slots,"leftContent")],2)):pe("",!0),n.$slots.centerContent||e.title?(ne(),ae("div",{key:1,class:$e("center-container "+e.centerClass)},[e.title?(ne(),ae("div",{key:0,class:$e("navigation-title "+e.titleClass)},Me(e.title),3)):pe("",!0),Qe(n.$slots,"centerContent")],2)):pe("",!0),n.$slots.rightContent?(ne(),ae("div",{key:2,class:$e("right-container "+e.rightClass)},[Qe(n.$slots,"rightContent")],2)):pe("",!0)],10,Xv)}}}),Jv={key:0,theme:"light",class:"notify-container"},Zv={key:0,class:"content"},ey={key:0,class:"screen overlay"},ty={key:0,class:"screen overlay"},ny=Ge({__name:"UIScreen",props:{title:{type:String,default:""},titleClass:{type:String,default:""},navigationBarTheme:String,disableNavBack:{type:Boolean,default:!1},isOnline:{type:Boolean,default:!0},status:{type:String,default:""},showClose:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},onContentScroll:{type:Function}},emits:["goPreviousPage","closeWindow"],setup(e,{expose:t,emit:n}){const r=Fp(),i=e,{title:o,titleClass:u,navigationBarTheme:c,isOnline:l,status:s,showClose:a,loading:d}=Ro(i),f=window.innerHeight/3,h=window.innerHeight/2,p=_e(0),m=_e(void 0),_=_e(void 0);let g;const v=n,y=de(()=>{var N;return(N=window.history.state)!=null&&N.back&&!i.disableNavBack?{type:"back",onClick:()=>v("goPreviousPage")}:void 0}),E=de(()=>({title:o.value,titleClass:u.value,theme:c==null?void 0:c.value,isOnline:l.value,status:s.value,navBack:y.value})),L=de(()=>{const N=[];return(r.navigationBar||o.value||y.value||a.value)&&N.push("with-navigation-bar"),r.tabBar&&N.push("with-tab-bar"),N});$t(()=>{var N;i.onContentScroll&&((N=m.value)==null||N.addEventListener("scroll",I))}),Yr(()=>{var N;i.onContentScroll&&((N=m.value)==null||N.removeEventListener("scroll",I))});function I(N){i.onContentScroll&&i.onContentScroll(N)}function w(N,G){g&&(clearTimeout(g),g=void 0),G?D(N):T()}function D(N){const G=N.target;p.value=G.getBoundingClientRect().top;const Y=p.value-f;_.value&&(_.value.classList.remove("close"),_.value.style.height=h+"px"),setTimeout(()=>{var V;(V=m.value)==null||V.scrollBy({top:Y,behavior:"smooth"})},0)}function T(){g=setTimeout(()=>{_.value&&_.value.classList.add("close")},500)}function R(N,G){var Y;const V={behavior:"smooth"};N!==void 0&&Object.assign(V,{top:N}),G!==void 0&&Object.assign(V,{left:G}),(Y=m.value)==null||Y.scrollBy(V)}function k(N){var G;(G=m.value)==null||G.scrollTo({top:N||0,behavior:"smooth"})}function M(N){var G;(G=m.value)==null||G.scrollTo({left:N||0,behavior:"smooth"})}function H(){v("closeWindow")}return t({scrollBy:R,scrollToTop:k,scrollToLeft:M}),(N,G)=>{var Y;return ne(),ae(Be,null,[oe("div",yo({class:["screen",...L.value]},N.$attrs),[Qe(N.$slots,"navigationBar",{},()=>[Z(o)||y.value||Z(a)?(ne(),et(Hh,$f(yo({key:0},E.value)),Mn({_:2},[Z(a)?{name:"rightContent",fn:He(()=>[Ee(Bt,{type:"circle",icon:{name:"close"},onClick:H})]),key:"0"}:void 0]),1040)):pe("",!0)]),oe("div",{ref_key:"screenContentRef",ref:m,class:$e("screen-content ".concat(Z(r).footer?"screen-content-with-footer":""))},[Ee(Or,{name:"swipe-down"},{default:He(()=>[Z(r).notify?(ne(),ae("div",Jv,[Qe(N.$slots,"notify")])):pe("",!0)]),_:3}),Z(r).content?(ne(),ae("div",Zv,[Qe(N.$slots,"content",{focusChange:w})])):pe("",!0),Z(r).footer?(ne(),ae("div",{key:1,class:$e("footer ".concat((Y=_.value)!=null&&Y.style.height?"footer-before-keyboard":""))},[Qe(N.$slots,"footer")],2)):pe("",!0),oe("div",{ref_key:"keyboardRef",ref:_,class:"screen-keyboard"},null,512)],2),Qe(N.$slots,"tabBar")],16),Ee(Or,{name:"fade"},{default:He(()=>[Z(d)?(ne(),ae("div",ey,[Qe(N.$slots,"loading",{},()=>[Ee($h)])])):pe("",!0)]),_:3}),Ee(Or,{name:"fade"},{default:He(()=>[Z(r).dialog?(ne(),ae("div",ty,[Qe(N.$slots,"dialog")])):pe("",!0)]),_:3})],64)}}}),ry={class:"dialog-container"},iy={key:0,class:"dialog-title"},oy={key:1,class:"dialog-desc"},sy={key:2,class:"dialog-content"},ay={class:"actions-container"},Vo=Ge({__name:"UIDialog",props:{title:{type:String,default:""},description:{type:String,default:""}},setup(e){const{t}=_t();return(n,r)=>(ne(),ae("div",ry,[e.title?(ne(),ae("div",iy,Me(e.title),1)):pe("",!0),e.description?(ne(),ae("div",oy,Me(e.description),1)):pe("",!0),n.$slots.content?(ne(),ae("div",sy,[Qe(n.$slots,"content")])):pe("",!0),oe("div",ay,[Qe(n.$slots,"buttons",{},()=>[Ee(Bt,{type:"clear",title:Z(t)("button.ok"),onClick:r[0]||(r[0]=i=>n.$emit("closeDialog"))},null,8,["title"])])])]))}});var di={},lr={},hi={},ic;function wt(){if(ic)return hi;ic=1,Object.defineProperty(hi,"__esModule",{value:!0});function e(n,r){if(!(n instanceof r))throw new TypeError("Cannot call a class as a function")}var t=function n(r,i){e(this,n),this.data=r,this.text=i.text||r,this.options=i};return hi.default=t,hi}var oc;function uy(){if(oc)return lr;oc=1,Object.defineProperty(lr,"__esModule",{value:!0}),lr.CODE39=void 0;var e=function(){function m(_,g){for(var v=0;v<g.length;v++){var y=g[v];y.enumerable=y.enumerable||!1,y.configurable=!0,"value"in y&&(y.writable=!0),Object.defineProperty(_,y.key,y)}}return function(_,g,v){return g&&m(_.prototype,g),v&&m(_,v),_}}(),t=wt(),n=r(t);function r(m){return m&&m.__esModule?m:{default:m}}function i(m,_){if(!(m instanceof _))throw new TypeError("Cannot call a class as a function")}function o(m,_){if(!m)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:m}function u(m,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);m.prototype=Object.create(_&&_.prototype,{constructor:{value:m,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(m,_):m.__proto__=_)}var c=function(m){u(_,m);function _(g,v){return i(this,_),g=g.toUpperCase(),v.mod43&&(g+=f(p(g))),o(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,g,v))}return e(_,[{key:"encode",value:function(){for(var v=a("*"),y=0;y<this.data.length;y++)v+=a(this.data[y])+"0";return v+=a("*"),{data:v,text:this.text}}},{key:"valid",value:function(){return this.data.search(/^[0-9A-Z\-\.\ \$\/\+\%]+$/)!==-1}}]),_}(n.default),l=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","*"],s=[20957,29783,23639,30485,20951,29813,23669,20855,29789,23645,29975,23831,30533,22295,30149,24005,21623,29981,23837,22301,30023,23879,30545,22343,30161,24017,21959,30065,23921,22385,29015,18263,29141,17879,29045,18293,17783,29021,18269,17477,17489,17681,20753,35770];function a(m){return d(h(m))}function d(m){return s[m].toString(2)}function f(m){return l[m]}function h(m){return l.indexOf(m)}function p(m){for(var _=0,g=0;g<m.length;g++)_+=h(m[g]);return _=_%43,_}return lr.CODE39=c,lr}var Tt={},mi={},pi={},Fe={},sc;function Xr(){if(sc)return Fe;sc=1,Object.defineProperty(Fe,"__esModule",{value:!0});var e;function t(l,s,a){return s in l?Object.defineProperty(l,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):l[s]=a,l}var n=Fe.SET_A=0,r=Fe.SET_B=1,i=Fe.SET_C=2;Fe.SHIFT=98;var o=Fe.START_A=103,u=Fe.START_B=104,c=Fe.START_C=105;return Fe.MODULO=103,Fe.STOP=106,Fe.FNC1=207,Fe.SET_BY_CODE=(e={},t(e,o,n),t(e,u,r),t(e,c,i),e),Fe.SWAP={101:n,100:r,99:i},Fe.A_START_CHAR="Ð",Fe.B_START_CHAR="Ñ",Fe.C_START_CHAR="Ò",Fe.A_CHARS="[\0-_È-Ï]",Fe.B_CHARS="[ -È-Ï]",Fe.C_CHARS="(Ï*[0-9]{2}Ï*)",Fe.BARS=[11011001100,11001101100,11001100110,10010011e3,10010001100,10001001100,10011001e3,10011000100,10001100100,11001001e3,11001000100,11000100100,10110011100,10011011100,10011001110,10111001100,10011101100,10011100110,11001110010,11001011100,11001001110,11011100100,11001110100,11101101110,11101001100,11100101100,11100100110,11101100100,11100110100,11100110010,11011011e3,11011000110,11000110110,10100011e3,10001011e3,10001000110,10110001e3,10001101e3,10001100010,11010001e3,11000101e3,11000100010,10110111e3,10110001110,10001101110,10111011e3,10111000110,10001110110,11101110110,11010001110,11000101110,11011101e3,11011100010,11011101110,11101011e3,11101000110,11100010110,11101101e3,11101100010,11100011010,11101111010,11001000010,11110001010,1010011e4,10100001100,1001011e4,10010000110,10000101100,10000100110,1011001e4,10110000100,1001101e4,10011000010,10000110100,10000110010,11000010010,1100101e4,11110111010,11000010100,10001111010,10100111100,10010111100,10010011110,10111100100,10011110100,10011110010,11110100100,11110010100,11110010010,11011011110,11011110110,11110110110,10101111e3,10100011110,10001011110,10111101e3,10111100010,11110101e3,11110100010,10111011110,10111101110,11101011110,11110101110,11010000100,1101001e4,11010011100,1100011101011],Fe}var ac;function Wo(){if(ac)return pi;ac=1,Object.defineProperty(pi,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=wt(),n=i(t),r=Xr();function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){o(this,a);var h=u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,d.substring(1),f));return h.bytes=d.split("").map(function(p){return p.charCodeAt(0)}),h}return e(a,[{key:"valid",value:function(){return/^[\x00-\x7F\xC8-\xD3]+$/.test(this.data)}},{key:"encode",value:function(){var f=this.bytes,h=f.shift()-105,p=r.SET_BY_CODE[h];if(p===void 0)throw new RangeError("The encoding does not start with a start character.");this.shouldEncodeAsEan128()===!0&&f.unshift(r.FNC1);var m=a.next(f,1,p);return{text:this.text===this.data?this.text.replace(/[^\x20-\x7E]/g,""):this.text,data:a.getBar(h)+m.result+a.getBar((m.checksum+h)%r.MODULO)+a.getBar(r.STOP)}}},{key:"shouldEncodeAsEan128",value:function(){var f=this.options.ean128||!1;return typeof f=="string"&&(f=f.toLowerCase()==="true"),f}}],[{key:"getBar",value:function(f){return r.BARS[f]?r.BARS[f].toString():""}},{key:"correctIndex",value:function(f,h){if(h===r.SET_A){var p=f.shift();return p<32?p+64:p-32}else return h===r.SET_B?f.shift()-32:(f.shift()-48)*10+f.shift()-48}},{key:"next",value:function(f,h,p){if(!f.length)return{result:"",checksum:0};var m=void 0,_=void 0;if(f[0]>=200){_=f.shift()-105;var g=r.SWAP[_];g!==void 0?m=a.next(f,h+1,g):((p===r.SET_A||p===r.SET_B)&&_===r.SHIFT&&(f[0]=p===r.SET_A?f[0]>95?f[0]-96:f[0]:f[0]<32?f[0]+96:f[0]),m=a.next(f,h+1,p))}else _=a.correctIndex(f,p),m=a.next(f,h+1,p);var v=a.getBar(_),y=_*h;return{result:v+m.result,checksum:y+m.checksum}}}]),a}(n.default);return pi.default=l,pi}var gi={},uc;function ly(){if(uc)return gi;uc=1,Object.defineProperty(gi,"__esModule",{value:!0});var e=Xr(),t=function(c){return c.match(new RegExp("^"+e.A_CHARS+"*"))[0].length},n=function(c){return c.match(new RegExp("^"+e.B_CHARS+"*"))[0].length},r=function(c){return c.match(new RegExp("^"+e.C_CHARS+"*"))[0]};function i(u,c){var l=c?e.A_CHARS:e.B_CHARS,s=u.match(new RegExp("^("+l+"+?)(([0-9]{2}){2,})([^0-9]|$)"));if(s)return s[1]+"Ì"+o(u.substring(s[1].length));var a=u.match(new RegExp("^"+l+"+"))[0];return a.length===u.length?u:a+String.fromCharCode(c?205:206)+i(u.substring(a.length),!c)}function o(u){var c=r(u),l=c.length;if(l===u.length)return u;u=u.substring(l);var s=t(u)>=n(u);return c+String.fromCharCode(s?206:205)+i(u,s)}return gi.default=function(u){var c=void 0,l=r(u).length;if(l>=2)c=e.C_START_CHAR+o(u);else{var s=t(u)>n(u);c=(s?e.A_START_CHAR:e.B_START_CHAR)+i(u,s)}return c.replace(/[\xCD\xCE]([^])[\xCD\xCE]/,function(a,d){return"Ë"+d})},gi}var lc;function cy(){if(lc)return mi;lc=1,Object.defineProperty(mi,"__esModule",{value:!0});var e=Wo(),t=i(e),n=ly(),r=i(n);function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){if(o(this,a),/^[\x00-\x7F\xC8-\xD3]+$/.test(d))var h=u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,(0,r.default)(d),f));else var h=u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,d,f));return u(h)}return a}(t.default);return mi.default=l,mi}var _i={},cc;function fy(){if(cc)return _i;cc=1,Object.defineProperty(_i,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=Wo(),n=i(t),r=Xr();function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){return o(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,r.A_START_CHAR+d,f))}return e(a,[{key:"valid",value:function(){return new RegExp("^"+r.A_CHARS+"+$").test(this.data)}}]),a}(n.default);return _i.default=l,_i}var vi={},fc;function dy(){if(fc)return vi;fc=1,Object.defineProperty(vi,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=Wo(),n=i(t),r=Xr();function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){return o(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,r.B_START_CHAR+d,f))}return e(a,[{key:"valid",value:function(){return new RegExp("^"+r.B_CHARS+"+$").test(this.data)}}]),a}(n.default);return vi.default=l,vi}var yi={},dc;function hy(){if(dc)return yi;dc=1,Object.defineProperty(yi,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=Wo(),n=i(t),r=Xr();function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(d,f){return o(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,r.C_START_CHAR+d,f))}return e(a,[{key:"valid",value:function(){return new RegExp("^"+r.C_CHARS+"+$").test(this.data)}}]),a}(n.default);return yi.default=l,yi}var hc;function my(){if(hc)return Tt;hc=1,Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.CODE128C=Tt.CODE128B=Tt.CODE128A=Tt.CODE128=void 0;var e=cy(),t=l(e),n=fy(),r=l(n),i=dy(),o=l(i),u=hy(),c=l(u);function l(s){return s&&s.__esModule?s:{default:s}}return Tt.CODE128=t.default,Tt.CODE128A=r.default,Tt.CODE128B=o.default,Tt.CODE128C=c.default,Tt}var tt={},bi={},qt={},mc;function Kr(){return mc||(mc=1,Object.defineProperty(qt,"__esModule",{value:!0}),qt.SIDE_BIN="101",qt.MIDDLE_BIN="01010",qt.BINARIES={L:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],G:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"],R:["1110010","1100110","1101100","1000010","1011100","1001110","1010000","1000100","1001000","1110100"],O:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],E:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"]},qt.EAN2_STRUCTURE=["LL","LG","GL","GG"],qt.EAN5_STRUCTURE=["GGLLL","GLGLL","GLLGL","GLLLG","LGGLL","LLGGL","LLLGG","LGLGL","LGLLG","LLGLG"],qt.EAN13_STRUCTURE=["LLLLLL","LLGLGG","LLGGLG","LLGGGL","LGLLGG","LGGLLG","LGGGLL","LGLGLG","LGLGGL","LGGLGL"]),qt}var Ei={},Ai={},pc;function Qr(){if(pc)return Ai;pc=1,Object.defineProperty(Ai,"__esModule",{value:!0});var e=Kr(),t=function(r,i,o){var u=r.split("").map(function(l,s){return e.BINARIES[i[s]]}).map(function(l,s){return l?l[r[s]]:""});if(o){var c=r.length-1;u=u.map(function(l,s){return s<c?l+o:l})}return u.join("")};return Ai.default=t,Ai}var gc;function jh(){if(gc)return Ei;gc=1,Object.defineProperty(Ei,"__esModule",{value:!0});var e=function(){function d(f,h){for(var p=0;p<h.length;p++){var m=h[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(f,m.key,m)}}return function(f,h,p){return h&&d(f.prototype,h),p&&d(f,p),f}}(),t=Kr(),n=Qr(),r=u(n),i=wt(),o=u(i);function u(d){return d&&d.__esModule?d:{default:d}}function c(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function l(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function s(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var a=function(d){s(f,d);function f(h,p){c(this,f);var m=l(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,h,p));return m.fontSize=!p.flat&&p.fontSize>p.width*10?p.width*10:p.fontSize,m.guardHeight=p.height+m.fontSize/2+p.textMargin,m}return e(f,[{key:"encode",value:function(){return this.options.flat?this.encodeFlat():this.encodeGuarded()}},{key:"leftText",value:function(p,m){return this.text.substr(p,m)}},{key:"leftEncode",value:function(p,m){return(0,r.default)(p,m)}},{key:"rightText",value:function(p,m){return this.text.substr(p,m)}},{key:"rightEncode",value:function(p,m){return(0,r.default)(p,m)}},{key:"encodeGuarded",value:function(){var p={fontSize:this.fontSize},m={height:this.guardHeight};return[{data:t.SIDE_BIN,options:m},{data:this.leftEncode(),text:this.leftText(),options:p},{data:t.MIDDLE_BIN,options:m},{data:this.rightEncode(),text:this.rightText(),options:p},{data:t.SIDE_BIN,options:m}]}},{key:"encodeFlat",value:function(){var p=[t.SIDE_BIN,this.leftEncode(),t.MIDDLE_BIN,this.rightEncode(),t.SIDE_BIN];return{data:p.join(""),text:this.text}}}]),f}(o.default);return Ei.default=a,Ei}var _c;function py(){if(_c)return bi;_c=1,Object.defineProperty(bi,"__esModule",{value:!0});var e=function(){function d(f,h){for(var p=0;p<h.length;p++){var m=h[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(f,m.key,m)}}return function(f,h,p){return h&&d(f.prototype,h),p&&d(f,p),f}}(),t=function d(f,h,p){f===null&&(f=Function.prototype);var m=Object.getOwnPropertyDescriptor(f,h);if(m===void 0){var _=Object.getPrototypeOf(f);return _===null?void 0:d(_,h,p)}else{if("value"in m)return m.value;var g=m.get;return g===void 0?void 0:g.call(p)}},n=Kr(),r=jh(),i=o(r);function o(d){return d&&d.__esModule?d:{default:d}}function u(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function c(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function l(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var s=function(f){var h=f.substr(0,12).split("").map(function(p){return+p}).reduce(function(p,m,_){return _%2?p+m*3:p+m},0);return(10-h%10)%10},a=function(d){l(f,d);function f(h,p){u(this,f),h.search(/^[0-9]{12}$/)!==-1&&(h+=s(h));var m=c(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,h,p));return m.lastChar=p.lastChar,m}return e(f,[{key:"valid",value:function(){return this.data.search(/^[0-9]{13}$/)!==-1&&+this.data[12]===s(this.data)}},{key:"leftText",value:function(){return t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"leftText",this).call(this,1,6)}},{key:"leftEncode",value:function(){var p=this.data.substr(1,6),m=n.EAN13_STRUCTURE[this.data[0]];return t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"leftEncode",this).call(this,p,m)}},{key:"rightText",value:function(){return t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"rightText",this).call(this,7,6)}},{key:"rightEncode",value:function(){var p=this.data.substr(7,6);return t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"rightEncode",this).call(this,p,"RRRRRR")}},{key:"encodeGuarded",value:function(){var p=t(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"encodeGuarded",this).call(this);return this.options.displayValue&&(p.unshift({data:"000000000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),this.options.lastChar&&(p.push({data:"00"}),p.push({data:"00000",text:this.options.lastChar,options:{fontSize:this.fontSize}}))),p}}]),f}(i.default);return bi.default=a,bi}var wi={},vc;function gy(){if(vc)return wi;vc=1,Object.defineProperty(wi,"__esModule",{value:!0});var e=function(){function a(d,f){for(var h=0;h<f.length;h++){var p=f[h];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(d,p.key,p)}}return function(d,f,h){return f&&a(d.prototype,f),h&&a(d,h),d}}(),t=function a(d,f,h){d===null&&(d=Function.prototype);var p=Object.getOwnPropertyDescriptor(d,f);if(p===void 0){var m=Object.getPrototypeOf(d);return m===null?void 0:a(m,f,h)}else{if("value"in p)return p.value;var _=p.get;return _===void 0?void 0:_.call(h)}},n=jh(),r=i(n);function i(a){return a&&a.__esModule?a:{default:a}}function o(a,d){if(!(a instanceof d))throw new TypeError("Cannot call a class as a function")}function u(a,d){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return d&&(typeof d=="object"||typeof d=="function")?d:a}function c(a,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof d);a.prototype=Object.create(d&&d.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),d&&(Object.setPrototypeOf?Object.setPrototypeOf(a,d):a.__proto__=d)}var l=function(d){var f=d.substr(0,7).split("").map(function(h){return+h}).reduce(function(h,p,m){return m%2?h+p:h+p*3},0);return(10-f%10)%10},s=function(a){c(d,a);function d(f,h){return o(this,d),f.search(/^[0-9]{7}$/)!==-1&&(f+=l(f)),u(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,f,h))}return e(d,[{key:"valid",value:function(){return this.data.search(/^[0-9]{8}$/)!==-1&&+this.data[7]===l(this.data)}},{key:"leftText",value:function(){return t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"leftText",this).call(this,0,4)}},{key:"leftEncode",value:function(){var h=this.data.substr(0,4);return t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"leftEncode",this).call(this,h,"LLLL")}},{key:"rightText",value:function(){return t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"rightText",this).call(this,4,4)}},{key:"rightEncode",value:function(){var h=this.data.substr(4,4);return t(d.prototype.__proto__||Object.getPrototypeOf(d.prototype),"rightEncode",this).call(this,h,"RRRR")}}]),d}(r.default);return wi.default=s,wi}var Oi={},yc;function _y(){if(yc)return Oi;yc=1,Object.defineProperty(Oi,"__esModule",{value:!0});var e=function(){function f(h,p){for(var m=0;m<p.length;m++){var _=p[m];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(h,_.key,_)}}return function(h,p,m){return p&&f(h.prototype,p),m&&f(h,m),h}}(),t=Kr(),n=Qr(),r=u(n),i=wt(),o=u(i);function u(f){return f&&f.__esModule?f:{default:f}}function c(f,h){if(!(f instanceof h))throw new TypeError("Cannot call a class as a function")}function l(f,h){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:f}function s(f,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);f.prototype=Object.create(h&&h.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(f,h):f.__proto__=h)}var a=function(h){var p=h.split("").map(function(m){return+m}).reduce(function(m,_,g){return g%2?m+_*9:m+_*3},0);return p%10},d=function(f){s(h,f);function h(p,m){return c(this,h),l(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,p,m))}return e(h,[{key:"valid",value:function(){return this.data.search(/^[0-9]{5}$/)!==-1}},{key:"encode",value:function(){var m=t.EAN5_STRUCTURE[a(this.data)];return{data:"1011"+(0,r.default)(this.data,m,"01"),text:this.text}}}]),h}(o.default);return Oi.default=d,Oi}var Ti={},bc;function vy(){if(bc)return Ti;bc=1,Object.defineProperty(Ti,"__esModule",{value:!0});var e=function(){function d(f,h){for(var p=0;p<h.length;p++){var m=h[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(f,m.key,m)}}return function(f,h,p){return h&&d(f.prototype,h),p&&d(f,p),f}}(),t=Kr(),n=Qr(),r=u(n),i=wt(),o=u(i);function u(d){return d&&d.__esModule?d:{default:d}}function c(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function l(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function s(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var a=function(d){s(f,d);function f(h,p){return c(this,f),l(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,h,p))}return e(f,[{key:"valid",value:function(){return this.data.search(/^[0-9]{2}$/)!==-1}},{key:"encode",value:function(){var p=t.EAN2_STRUCTURE[parseInt(this.data)%4];return{data:"1011"+(0,r.default)(this.data,p,"01"),text:this.text}}}]),f}(o.default);return Ti.default=a,Ti}var cr={},Ec;function Uh(){if(Ec)return cr;Ec=1,Object.defineProperty(cr,"__esModule",{value:!0});var e=function(){function d(f,h){for(var p=0;p<h.length;p++){var m=h[p];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(f,m.key,m)}}return function(f,h,p){return h&&d(f.prototype,h),p&&d(f,p),f}}();cr.checksum=a;var t=Qr(),n=o(t),r=wt(),i=o(r);function o(d){return d&&d.__esModule?d:{default:d}}function u(d,f){if(!(d instanceof f))throw new TypeError("Cannot call a class as a function")}function c(d,f){if(!d)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:d}function l(d,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);d.prototype=Object.create(f&&f.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(d,f):d.__proto__=f)}var s=function(d){l(f,d);function f(h,p){u(this,f),h.search(/^[0-9]{11}$/)!==-1&&(h+=a(h));var m=c(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,h,p));return m.displayValue=p.displayValue,p.fontSize>p.width*10?m.fontSize=p.width*10:m.fontSize=p.fontSize,m.guardHeight=p.height+m.fontSize/2+p.textMargin,m}return e(f,[{key:"valid",value:function(){return this.data.search(/^[0-9]{12}$/)!==-1&&this.data[11]==a(this.data)}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var p="";return p+="101",p+=(0,n.default)(this.data.substr(0,6),"LLLLLL"),p+="01010",p+=(0,n.default)(this.data.substr(6,6),"RRRRRR"),p+="101",{data:p,text:this.text}}},{key:"guardedEncoding",value:function(){var p=[];return this.displayValue&&p.push({data:"00000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),p.push({data:"101"+(0,n.default)(this.data[0],"L"),options:{height:this.guardHeight}}),p.push({data:(0,n.default)(this.data.substr(1,5),"LLLLL"),text:this.text.substr(1,5),options:{fontSize:this.fontSize}}),p.push({data:"01010",options:{height:this.guardHeight}}),p.push({data:(0,n.default)(this.data.substr(6,5),"RRRRR"),text:this.text.substr(6,5),options:{fontSize:this.fontSize}}),p.push({data:(0,n.default)(this.data[11],"R")+"101",options:{height:this.guardHeight}}),this.displayValue&&p.push({data:"00000000",text:this.text.substr(11,1),options:{textAlign:"right",fontSize:this.fontSize}}),p}}]),f}(i.default);function a(d){var f=0,h;for(h=1;h<11;h+=2)f+=parseInt(d[h]);for(h=0;h<11;h+=2)f+=parseInt(d[h])*3;return(10-f%10)%10}return cr.default=s,cr}var Si={},Ac;function yy(){if(Ac)return Si;Ac=1,Object.defineProperty(Si,"__esModule",{value:!0});var e=function(){function p(m,_){for(var g=0;g<_.length;g++){var v=_[g];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(m,v.key,v)}}return function(m,_,g){return _&&p(m.prototype,_),g&&p(m,g),m}}(),t=Qr(),n=u(t),r=wt(),i=u(r),o=Uh();function u(p){return p&&p.__esModule?p:{default:p}}function c(p,m){if(!(p instanceof m))throw new TypeError("Cannot call a class as a function")}function l(p,m){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:p}function s(p,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);p.prototype=Object.create(m&&m.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(p,m):p.__proto__=m)}var a=["XX00000XXX","XX10000XXX","XX20000XXX","XXX00000XX","XXXX00000X","XXXXX00005","XXXXX00006","XXXXX00007","XXXXX00008","XXXXX00009"],d=[["EEEOOO","OOOEEE"],["EEOEOO","OOEOEE"],["EEOOEO","OOEEOE"],["EEOOOE","OOEEEO"],["EOEEOO","OEOOEE"],["EOOEEO","OEEOOE"],["EOOOEE","OEEEOO"],["EOEOEO","OEOEOE"],["EOEOOE","OEOEEO"],["EOOEOE","OEEOEO"]],f=function(p){s(m,p);function m(_,g){c(this,m);var v=l(this,(m.__proto__||Object.getPrototypeOf(m)).call(this,_,g));if(v.isValid=!1,_.search(/^[0-9]{6}$/)!==-1)v.middleDigits=_,v.upcA=h(_,"0"),v.text=g.text||""+v.upcA[0]+_+v.upcA[v.upcA.length-1],v.isValid=!0;else if(_.search(/^[01][0-9]{7}$/)!==-1)if(v.middleDigits=_.substring(1,_.length-1),v.upcA=h(v.middleDigits,_[0]),v.upcA[v.upcA.length-1]===_[_.length-1])v.isValid=!0;else return l(v);else return l(v);return v.displayValue=g.displayValue,g.fontSize>g.width*10?v.fontSize=g.width*10:v.fontSize=g.fontSize,v.guardHeight=g.height+v.fontSize/2+g.textMargin,v}return e(m,[{key:"valid",value:function(){return this.isValid}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var g="";return g+="101",g+=this.encodeMiddleDigits(),g+="010101",{data:g,text:this.text}}},{key:"guardedEncoding",value:function(){var g=[];return this.displayValue&&g.push({data:"00000000",text:this.text[0],options:{textAlign:"left",fontSize:this.fontSize}}),g.push({data:"101",options:{height:this.guardHeight}}),g.push({data:this.encodeMiddleDigits(),text:this.text.substring(1,7),options:{fontSize:this.fontSize}}),g.push({data:"010101",options:{height:this.guardHeight}}),this.displayValue&&g.push({data:"00000000",text:this.text[7],options:{textAlign:"right",fontSize:this.fontSize}}),g}},{key:"encodeMiddleDigits",value:function(){var g=this.upcA[0],v=this.upcA[this.upcA.length-1],y=d[parseInt(v)][parseInt(g)];return(0,n.default)(this.middleDigits,y)}}]),m}(i.default);function h(p,m){for(var _=parseInt(p[p.length-1]),g=a[_],v="",y=0,E=0;E<g.length;E++){var L=g[E];L==="X"?v+=p[y++]:v+=L}return v=""+m+v,""+v+(0,o.checksum)(v)}return Si.default=f,Si}var wc;function by(){if(wc)return tt;wc=1,Object.defineProperty(tt,"__esModule",{value:!0}),tt.UPCE=tt.UPC=tt.EAN2=tt.EAN5=tt.EAN8=tt.EAN13=void 0;var e=py(),t=f(e),n=gy(),r=f(n),i=_y(),o=f(i),u=vy(),c=f(u),l=Uh(),s=f(l),a=yy(),d=f(a);function f(h){return h&&h.__esModule?h:{default:h}}return tt.EAN13=t.default,tt.EAN8=r.default,tt.EAN5=o.default,tt.EAN2=c.default,tt.UPC=s.default,tt.UPCE=d.default,tt}var wn={},Ci={},$n={},Oc;function Ey(){return Oc||(Oc=1,Object.defineProperty($n,"__esModule",{value:!0}),$n.START_BIN="1010",$n.END_BIN="11101",$n.BINARIES=["00110","10001","01001","11000","00101","10100","01100","00011","10010","01010"]),$n}var Tc;function Yh(){if(Tc)return Ci;Tc=1,Object.defineProperty(Ci,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=Ey(),n=wt(),r=i(n);function i(s){return s&&s.__esModule?s:{default:s}}function o(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function u(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function c(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var l=function(s){c(a,s);function a(){return o(this,a),u(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return e(a,[{key:"valid",value:function(){return this.data.search(/^([0-9]{2})+$/)!==-1}},{key:"encode",value:function(){var f=this,h=this.data.match(/.{2}/g).map(function(p){return f.encodePair(p)}).join("");return{data:t.START_BIN+h+t.END_BIN,text:this.text}}},{key:"encodePair",value:function(f){var h=t.BINARIES[f[1]];return t.BINARIES[f[0]].split("").map(function(p,m){return(p==="1"?"111":"1")+(h[m]==="1"?"000":"0")}).join("")}}]),a}(r.default);return Ci.default=l,Ci}var Ii={},Sc;function Ay(){if(Sc)return Ii;Sc=1,Object.defineProperty(Ii,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=Yh(),n=r(t);function r(s){return s&&s.__esModule?s:{default:s}}function i(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function o(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function u(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var c=function(a){var d=a.substr(0,13).split("").map(function(f){return parseInt(f,10)}).reduce(function(f,h,p){return f+h*(3-p%2*2)},0);return Math.ceil(d/10)*10-d},l=function(s){u(a,s);function a(d,f){return i(this,a),d.search(/^[0-9]{13}$/)!==-1&&(d+=c(d)),o(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,d,f))}return e(a,[{key:"valid",value:function(){return this.data.search(/^[0-9]{14}$/)!==-1&&+this.data[13]===c(this.data)}}]),a}(n.default);return Ii.default=l,Ii}var Cc;function wy(){if(Cc)return wn;Cc=1,Object.defineProperty(wn,"__esModule",{value:!0}),wn.ITF14=wn.ITF=void 0;var e=Yh(),t=i(e),n=Ay(),r=i(n);function i(o){return o&&o.__esModule?o:{default:o}}return wn.ITF=t.default,wn.ITF14=r.default,wn}var lt={},Di={},Ic;function Jr(){if(Ic)return Di;Ic=1,Object.defineProperty(Di,"__esModule",{value:!0});var e=function(){function s(a,d){for(var f=0;f<d.length;f++){var h=d[f];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(a,h.key,h)}}return function(a,d,f){return d&&s(a.prototype,d),f&&s(a,f),a}}(),t=wt(),n=r(t);function r(s){return s&&s.__esModule?s:{default:s}}function i(s,a){if(!(s instanceof a))throw new TypeError("Cannot call a class as a function")}function o(s,a){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:s}function u(s,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);s.prototype=Object.create(a&&a.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(s,a):s.__proto__=a)}var c=function(s){u(a,s);function a(d,f){return i(this,a),o(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,d,f))}return e(a,[{key:"encode",value:function(){for(var f="110",h=0;h<this.data.length;h++){var p=parseInt(this.data[h]),m=p.toString(2);m=l(m,4-m.length);for(var _=0;_<m.length;_++)f+=m[_]=="0"?"100":"110"}return f+="1001",{data:f,text:this.text}}},{key:"valid",value:function(){return this.data.search(/^[0-9]+$/)!==-1}}]),a}(n.default);function l(s,a){for(var d=0;d<a;d++)s="0"+s;return s}return Di.default=c,Di}var Mi={},fr={},Dc;function zo(){if(Dc)return fr;Dc=1,Object.defineProperty(fr,"__esModule",{value:!0}),fr.mod10=e,fr.mod11=t;function e(n){for(var r=0,i=0;i<n.length;i++){var o=parseInt(n[i]);(i+n.length)%2===0?r+=o:r+=o*2%10+Math.floor(o*2/10)}return(10-r%10)%10}function t(n){for(var r=0,i=[2,3,4,5,6,7],o=0;o<n.length;o++){var u=parseInt(n[n.length-1-o]);r+=i[o%i.length]*u}return(11-r%11)%11}return fr}var Mc;function Oy(){if(Mc)return Mi;Mc=1,Object.defineProperty(Mi,"__esModule",{value:!0});var e=Jr(),t=r(e),n=zo();function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a+(0,n.mod10)(a),d))}return s}(t.default);return Mi.default=c,Mi}var Li={},Lc;function Ty(){if(Lc)return Li;Lc=1,Object.defineProperty(Li,"__esModule",{value:!0});var e=Jr(),t=r(e),n=zo();function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a+(0,n.mod11)(a),d))}return s}(t.default);return Li.default=c,Li}var Ri={},Rc;function Sy(){if(Rc)return Ri;Rc=1,Object.defineProperty(Ri,"__esModule",{value:!0});var e=Jr(),t=r(e),n=zo();function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),a+=(0,n.mod10)(a),a+=(0,n.mod10)(a),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a,d))}return s}(t.default);return Ri.default=c,Ri}var ki={},kc;function Cy(){if(kc)return ki;kc=1,Object.defineProperty(ki,"__esModule",{value:!0});var e=Jr(),t=r(e),n=zo();function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),a+=(0,n.mod11)(a),a+=(0,n.mod10)(a),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a,d))}return s}(t.default);return ki.default=c,ki}var Nc;function Iy(){if(Nc)return lt;Nc=1,Object.defineProperty(lt,"__esModule",{value:!0}),lt.MSI1110=lt.MSI1010=lt.MSI11=lt.MSI10=lt.MSI=void 0;var e=Jr(),t=a(e),n=Oy(),r=a(n),i=Ty(),o=a(i),u=Sy(),c=a(u),l=Cy(),s=a(l);function a(d){return d&&d.__esModule?d:{default:d}}return lt.MSI=t.default,lt.MSI10=r.default,lt.MSI11=o.default,lt.MSI1010=c.default,lt.MSI1110=s.default,lt}var dr={},xc;function Dy(){if(xc)return dr;xc=1,Object.defineProperty(dr,"__esModule",{value:!0}),dr.pharmacode=void 0;var e=function(){function l(s,a){for(var d=0;d<a.length;d++){var f=a[d];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(s,f.key,f)}}return function(s,a,d){return a&&l(s.prototype,a),d&&l(s,d),s}}(),t=wt(),n=r(t);function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){i(this,s);var f=o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a,d));return f.number=parseInt(a,10),f}return e(s,[{key:"encode",value:function(){for(var d=this.number,f="";!isNaN(d)&&d!=0;)d%2===0?(f="11100"+f,d=(d-2)/2):(f="100"+f,d=(d-1)/2);return f=f.slice(0,-2),{data:f,text:this.text}}},{key:"valid",value:function(){return this.number>=3&&this.number<=131070}}]),s}(n.default);return dr.pharmacode=c,dr}var hr={},Pc;function My(){if(Pc)return hr;Pc=1,Object.defineProperty(hr,"__esModule",{value:!0}),hr.codabar=void 0;var e=function(){function l(s,a){for(var d=0;d<a.length;d++){var f=a[d];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(s,f.key,f)}}return function(s,a,d){return a&&l(s.prototype,a),d&&l(s,d),s}}(),t=wt(),n=r(t);function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){i(this,s),a.search(/^[0-9\-\$\:\.\+\/]+$/)===0&&(a="A"+a+"A");var f=o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a.toUpperCase(),d));return f.text=f.options.text||f.text.replace(/[A-D]/g,""),f}return e(s,[{key:"valid",value:function(){return this.data.search(/^[A-D][0-9\-\$\:\.\+\/]+[A-D]$/)!==-1}},{key:"encode",value:function(){for(var d=[],f=this.getEncodings(),h=0;h<this.data.length;h++)d.push(f[this.data.charAt(h)]),h!==this.data.length-1&&d.push("0");return{text:this.text,data:d.join("")}}},{key:"getEncodings",value:function(){return{0:"101010011",1:"101011001",2:"101001011",3:"110010101",4:"101101001",5:"110101001",6:"100101011",7:"100101101",8:"100110101",9:"110100101","-":"101001101",$:"101100101",":":"1101011011","/":"1101101011",".":"1101101101","+":"1011011011",A:"1011001001",B:"1001001011",C:"1010010011",D:"1010011001"}}}]),s}(n.default);return hr.codabar=c,hr}var mr={},Fc;function Ly(){if(Fc)return mr;Fc=1,Object.defineProperty(mr,"__esModule",{value:!0}),mr.GenericBarcode=void 0;var e=function(){function l(s,a){for(var d=0;d<a.length;d++){var f=a[d];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(s,f.key,f)}}return function(s,a,d){return a&&l(s.prototype,a),d&&l(s,d),s}}(),t=wt(),n=r(t);function r(l){return l&&l.__esModule?l:{default:l}}function i(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}function o(l,s){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:l}function u(l,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);l.prototype=Object.create(s&&s.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(l,s):l.__proto__=s)}var c=function(l){u(s,l);function s(a,d){return i(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,a,d))}return e(s,[{key:"encode",value:function(){return{data:"10101010101010101010101010101010101010101",text:this.text}}},{key:"valid",value:function(){return!0}}]),s}(n.default);return mr.GenericBarcode=c,mr}var Bc;function Ry(){if(Bc)return di;Bc=1,Object.defineProperty(di,"__esModule",{value:!0});var e=uy(),t=my(),n=by(),r=wy(),i=Iy(),o=Dy(),u=My(),c=Ly();return di.default={CODE39:e.CODE39,CODE128:t.CODE128,CODE128A:t.CODE128A,CODE128B:t.CODE128B,CODE128C:t.CODE128C,EAN13:n.EAN13,EAN8:n.EAN8,EAN5:n.EAN5,EAN2:n.EAN2,UPC:n.UPC,UPCE:n.UPCE,ITF14:r.ITF14,ITF:r.ITF,MSI:i.MSI,MSI10:i.MSI10,MSI11:i.MSI11,MSI1010:i.MSI1010,MSI1110:i.MSI1110,pharmacode:o.pharmacode,codabar:u.codabar,GenericBarcode:c.GenericBarcode},di}var Ni={},$c;function Xo(){if($c)return Ni;$c=1,Object.defineProperty(Ni,"__esModule",{value:!0});var e=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t};return Ni.default=function(t,n){return e({},t,n)},Ni}var xi={},Hc;function ky(){if(Hc)return xi;Hc=1,Object.defineProperty(xi,"__esModule",{value:!0}),xi.default=e;function e(t){var n=[];function r(i){if(Array.isArray(i))for(var o=0;o<i.length;o++)r(i[o]);else i.text=i.text||"",i.data=i.data||"",n.push(i)}return r(t),n}return xi}var Pi={},jc;function Ny(){if(jc)return Pi;jc=1,Object.defineProperty(Pi,"__esModule",{value:!0}),Pi.default=e;function e(t){return t.marginTop=t.marginTop||t.margin,t.marginBottom=t.marginBottom||t.margin,t.marginRight=t.marginRight||t.margin,t.marginLeft=t.marginLeft||t.margin,t}return Pi}var Fi={},Bi={},$i={},Uc;function qh(){if(Uc)return $i;Uc=1,Object.defineProperty($i,"__esModule",{value:!0}),$i.default=e;function e(t){var n=["width","height","textMargin","fontSize","margin","marginTop","marginBottom","marginLeft","marginRight"];for(var r in n)n.hasOwnProperty(r)&&(r=n[r],typeof t[r]=="string"&&(t[r]=parseInt(t[r],10)));return typeof t.displayValue=="string"&&(t.displayValue=t.displayValue!="false"),t}return $i}var Hi={},Yc;function Gh(){if(Yc)return Hi;Yc=1,Object.defineProperty(Hi,"__esModule",{value:!0});var e={width:2,height:100,format:"auto",displayValue:!0,fontOptions:"",font:"monospace",text:void 0,textAlign:"center",textPosition:"bottom",textMargin:2,fontSize:20,background:"#ffffff",lineColor:"#000000",margin:10,marginTop:void 0,marginBottom:void 0,marginLeft:void 0,marginRight:void 0,valid:function(){}};return Hi.default=e,Hi}var qc;function xy(){if(qc)return Bi;qc=1,Object.defineProperty(Bi,"__esModule",{value:!0});var e=qh(),t=i(e),n=Gh(),r=i(n);function i(u){return u&&u.__esModule?u:{default:u}}function o(u){var c={};for(var l in r.default)r.default.hasOwnProperty(l)&&(u.hasAttribute("jsbarcode-"+l.toLowerCase())&&(c[l]=u.getAttribute("jsbarcode-"+l.toLowerCase())),u.hasAttribute("data-"+l.toLowerCase())&&(c[l]=u.getAttribute("data-"+l.toLowerCase())));return c.value=u.getAttribute("jsbarcode-value")||u.getAttribute("data-value"),c=(0,t.default)(c),c}return Bi.default=o,Bi}var ji={},Ui={},ct={},Gc;function Vh(){if(Gc)return ct;Gc=1,Object.defineProperty(ct,"__esModule",{value:!0}),ct.getTotalWidthOfEncodings=ct.calculateEncodingAttributes=ct.getBarcodePadding=ct.getEncodingHeight=ct.getMaximumHeightOfEncodings=void 0;var e=Xo(),t=n(e);function n(s){return s&&s.__esModule?s:{default:s}}function r(s,a){return a.height+(a.displayValue&&s.text.length>0?a.fontSize+a.textMargin:0)+a.marginTop+a.marginBottom}function i(s,a,d){if(d.displayValue&&a<s){if(d.textAlign=="center")return Math.floor((s-a)/2);if(d.textAlign=="left")return 0;if(d.textAlign=="right")return Math.floor(s-a)}return 0}function o(s,a,d){for(var f=0;f<s.length;f++){var h=s[f],p=(0,t.default)(a,h.options),m;p.displayValue?m=l(h.text,p,d):m=0;var _=h.data.length*p.width;h.width=Math.ceil(Math.max(m,_)),h.height=r(h,p),h.barcodePadding=i(m,_,p)}}function u(s){for(var a=0,d=0;d<s.length;d++)a+=s[d].width;return a}function c(s){for(var a=0,d=0;d<s.length;d++)s[d].height>a&&(a=s[d].height);return a}function l(s,a,d){var f;if(d)f=d;else if(typeof document<"u")f=document.createElement("canvas").getContext("2d");else return 0;f.font=a.fontOptions+" "+a.fontSize+"px "+a.font;var h=f.measureText(s);if(!h)return 0;var p=h.width;return p}return ct.getMaximumHeightOfEncodings=c,ct.getEncodingHeight=r,ct.getBarcodePadding=i,ct.calculateEncodingAttributes=o,ct.getTotalWidthOfEncodings=u,ct}var Vc;function Py(){if(Vc)return Ui;Vc=1,Object.defineProperty(Ui,"__esModule",{value:!0});var e=function(){function c(l,s){for(var a=0;a<s.length;a++){var d=s[a];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(l,d.key,d)}}return function(l,s,a){return s&&c(l.prototype,s),a&&c(l,a),l}}(),t=Xo(),n=i(t),r=Vh();function i(c){return c&&c.__esModule?c:{default:c}}function o(c,l){if(!(c instanceof l))throw new TypeError("Cannot call a class as a function")}var u=function(){function c(l,s,a){o(this,c),this.canvas=l,this.encodings=s,this.options=a}return e(c,[{key:"render",value:function(){if(!this.canvas.getContext)throw new Error("The browser does not support canvas.");this.prepareCanvas();for(var s=0;s<this.encodings.length;s++){var a=(0,n.default)(this.options,this.encodings[s].options);this.drawCanvasBarcode(a,this.encodings[s]),this.drawCanvasText(a,this.encodings[s]),this.moveCanvasDrawing(this.encodings[s])}this.restoreCanvas()}},{key:"prepareCanvas",value:function(){var s=this.canvas.getContext("2d");s.save(),(0,r.calculateEncodingAttributes)(this.encodings,this.options,s);var a=(0,r.getTotalWidthOfEncodings)(this.encodings),d=(0,r.getMaximumHeightOfEncodings)(this.encodings);this.canvas.width=a+this.options.marginLeft+this.options.marginRight,this.canvas.height=d,s.clearRect(0,0,this.canvas.width,this.canvas.height),this.options.background&&(s.fillStyle=this.options.background,s.fillRect(0,0,this.canvas.width,this.canvas.height)),s.translate(this.options.marginLeft,0)}},{key:"drawCanvasBarcode",value:function(s,a){var d=this.canvas.getContext("2d"),f=a.data,h;s.textPosition=="top"?h=s.marginTop+s.fontSize+s.textMargin:h=s.marginTop,d.fillStyle=s.lineColor;for(var p=0;p<f.length;p++){var m=p*s.width+a.barcodePadding;f[p]==="1"?d.fillRect(m,h,s.width,s.height):f[p]&&d.fillRect(m,h,s.width,s.height*f[p])}}},{key:"drawCanvasText",value:function(s,a){var d=this.canvas.getContext("2d"),f=s.fontOptions+" "+s.fontSize+"px "+s.font;if(s.displayValue){var h,p;s.textPosition=="top"?p=s.marginTop+s.fontSize-s.textMargin:p=s.height+s.textMargin+s.marginTop+s.fontSize,d.font=f,s.textAlign=="left"||a.barcodePadding>0?(h=0,d.textAlign="left"):s.textAlign=="right"?(h=a.width-1,d.textAlign="right"):(h=a.width/2,d.textAlign="center"),d.fillText(a.text,h,p)}}},{key:"moveCanvasDrawing",value:function(s){var a=this.canvas.getContext("2d");a.translate(s.width,0)}},{key:"restoreCanvas",value:function(){var s=this.canvas.getContext("2d");s.restore()}}]),c}();return Ui.default=u,Ui}var Yi={},Wc;function Fy(){if(Wc)return Yi;Wc=1,Object.defineProperty(Yi,"__esModule",{value:!0});var e=function(){function l(s,a){for(var d=0;d<a.length;d++){var f=a[d];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(s,f.key,f)}}return function(s,a,d){return a&&l(s.prototype,a),d&&l(s,d),s}}(),t=Xo(),n=i(t),r=Vh();function i(l){return l&&l.__esModule?l:{default:l}}function o(l,s){if(!(l instanceof s))throw new TypeError("Cannot call a class as a function")}var u="http://www.w3.org/2000/svg",c=function(){function l(s,a,d){o(this,l),this.svg=s,this.encodings=a,this.options=d,this.document=d.xmlDocument||document}return e(l,[{key:"render",value:function(){var a=this.options.marginLeft;this.prepareSVG();for(var d=0;d<this.encodings.length;d++){var f=this.encodings[d],h=(0,n.default)(this.options,f.options),p=this.createGroup(a,h.marginTop,this.svg);this.setGroupOptions(p,h),this.drawSvgBarcode(p,h,f),this.drawSVGText(p,h,f),a+=f.width}}},{key:"prepareSVG",value:function(){for(;this.svg.firstChild;)this.svg.removeChild(this.svg.firstChild);(0,r.calculateEncodingAttributes)(this.encodings,this.options);var a=(0,r.getTotalWidthOfEncodings)(this.encodings),d=(0,r.getMaximumHeightOfEncodings)(this.encodings),f=a+this.options.marginLeft+this.options.marginRight;this.setSvgAttributes(f,d),this.options.background&&this.drawRect(0,0,f,d,this.svg).setAttribute("style","fill:"+this.options.background+";")}},{key:"drawSvgBarcode",value:function(a,d,f){var h=f.data,p;d.textPosition=="top"?p=d.fontSize+d.textMargin:p=0;for(var m=0,_=0,g=0;g<h.length;g++)_=g*d.width+f.barcodePadding,h[g]==="1"?m++:m>0&&(this.drawRect(_-d.width*m,p,d.width*m,d.height,a),m=0);m>0&&this.drawRect(_-d.width*(m-1),p,d.width*m,d.height,a)}},{key:"drawSVGText",value:function(a,d,f){var h=this.document.createElementNS(u,"text");if(d.displayValue){var p,m;h.setAttribute("style","font:"+d.fontOptions+" "+d.fontSize+"px "+d.font),d.textPosition=="top"?m=d.fontSize-d.textMargin:m=d.height+d.textMargin+d.fontSize,d.textAlign=="left"||f.barcodePadding>0?(p=0,h.setAttribute("text-anchor","start")):d.textAlign=="right"?(p=f.width-1,h.setAttribute("text-anchor","end")):(p=f.width/2,h.setAttribute("text-anchor","middle")),h.setAttribute("x",p),h.setAttribute("y",m),h.appendChild(this.document.createTextNode(f.text)),a.appendChild(h)}}},{key:"setSvgAttributes",value:function(a,d){var f=this.svg;f.setAttribute("width",a+"px"),f.setAttribute("height",d+"px"),f.setAttribute("x","0px"),f.setAttribute("y","0px"),f.setAttribute("viewBox","0 0 "+a+" "+d),f.setAttribute("xmlns",u),f.setAttribute("version","1.1"),f.setAttribute("style","transform: translate(0,0)")}},{key:"createGroup",value:function(a,d,f){var h=this.document.createElementNS(u,"g");return h.setAttribute("transform","translate("+a+", "+d+")"),f.appendChild(h),h}},{key:"setGroupOptions",value:function(a,d){a.setAttribute("style","fill:"+d.lineColor+";")}},{key:"drawRect",value:function(a,d,f,h,p){var m=this.document.createElementNS(u,"rect");return m.setAttribute("x",a),m.setAttribute("y",d),m.setAttribute("width",f),m.setAttribute("height",h),p.appendChild(m),m}}]),l}();return Yi.default=c,Yi}var qi={},zc;function By(){if(zc)return qi;zc=1,Object.defineProperty(qi,"__esModule",{value:!0});var e=function(){function r(i,o){for(var u=0;u<o.length;u++){var c=o[u];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(i,c.key,c)}}return function(i,o,u){return o&&r(i.prototype,o),u&&r(i,u),i}}();function t(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}var n=function(){function r(i,o,u){t(this,r),this.object=i,this.encodings=o,this.options=u}return e(r,[{key:"render",value:function(){this.object.encodings=this.encodings}}]),r}();return qi.default=n,qi}var Xc;function $y(){if(Xc)return ji;Xc=1,Object.defineProperty(ji,"__esModule",{value:!0});var e=Py(),t=u(e),n=Fy(),r=u(n),i=By(),o=u(i);function u(c){return c&&c.__esModule?c:{default:c}}return ji.default={CanvasRenderer:t.default,SVGRenderer:r.default,ObjectRenderer:o.default},ji}var Hn={},Kc;function Wh(){if(Kc)return Hn;Kc=1,Object.defineProperty(Hn,"__esModule",{value:!0});function e(u,c){if(!(u instanceof c))throw new TypeError("Cannot call a class as a function")}function t(u,c){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return c&&(typeof c=="object"||typeof c=="function")?c:u}function n(u,c){if(typeof c!="function"&&c!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof c);u.prototype=Object.create(c&&c.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),c&&(Object.setPrototypeOf?Object.setPrototypeOf(u,c):u.__proto__=c)}var r=function(u){n(c,u);function c(l,s){e(this,c);var a=t(this,(c.__proto__||Object.getPrototypeOf(c)).call(this));return a.name="InvalidInputException",a.symbology=l,a.input=s,a.message='"'+a.input+'" is not a valid input for '+a.symbology,a}return c}(Error),i=function(u){n(c,u);function c(){e(this,c);var l=t(this,(c.__proto__||Object.getPrototypeOf(c)).call(this));return l.name="InvalidElementException",l.message="Not supported type to render on",l}return c}(Error),o=function(u){n(c,u);function c(){e(this,c);var l=t(this,(c.__proto__||Object.getPrototypeOf(c)).call(this));return l.name="NoElementException",l.message="No element to render on.",l}return c}(Error);return Hn.InvalidInputException=r,Hn.InvalidElementException=i,Hn.NoElementException=o,Hn}var Qc;function Hy(){if(Qc)return Fi;Qc=1,Object.defineProperty(Fi,"__esModule",{value:!0});var e=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},t=xy(),n=u(t),r=$y(),i=u(r),o=Wh();function u(a){return a&&a.__esModule?a:{default:a}}function c(a){if(typeof a=="string")return l(a);if(Array.isArray(a)){for(var d=[],f=0;f<a.length;f++)d.push(c(a[f]));return d}else{if(typeof HTMLCanvasElement<"u"&&a instanceof HTMLImageElement)return s(a);if(a&&a.nodeName&&a.nodeName.toLowerCase()==="svg"||typeof SVGElement<"u"&&a instanceof SVGElement)return{element:a,options:(0,n.default)(a),renderer:i.default.SVGRenderer};if(typeof HTMLCanvasElement<"u"&&a instanceof HTMLCanvasElement)return{element:a,options:(0,n.default)(a),renderer:i.default.CanvasRenderer};if(a&&a.getContext)return{element:a,renderer:i.default.CanvasRenderer};if(a&&(typeof a>"u"?"undefined":e(a))==="object"&&!a.nodeName)return{element:a,renderer:i.default.ObjectRenderer};throw new o.InvalidElementException}}function l(a){var d=document.querySelectorAll(a);if(d.length!==0){for(var f=[],h=0;h<d.length;h++)f.push(c(d[h]));return f}}function s(a){var d=document.createElement("canvas");return{element:d,options:(0,n.default)(a),renderer:i.default.CanvasRenderer,afterRender:function(){a.setAttribute("src",d.toDataURL())}}}return Fi.default=c,Fi}var Gi={},Jc;function jy(){if(Jc)return Gi;Jc=1,Object.defineProperty(Gi,"__esModule",{value:!0});var e=function(){function r(i,o){for(var u=0;u<o.length;u++){var c=o[u];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(i,c.key,c)}}return function(i,o,u){return o&&r(i.prototype,o),u&&r(i,u),i}}();function t(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}var n=function(){function r(i){t(this,r),this.api=i}return e(r,[{key:"handleCatch",value:function(o){if(o.name==="InvalidInputException")if(this.api._options.valid!==this.api._defaults.valid)this.api._options.valid(!1);else throw o.message;else throw o;this.api.render=function(){}}},{key:"wrapBarcodeCall",value:function(o){try{var u=o.apply(void 0,arguments);return this.api._options.valid(!0),u}catch(c){return this.handleCatch(c),this.api}}}]),r}();return Gi.default=n,Gi}var _s,Zc;function Uy(){if(Zc)return _s;Zc=1;var e=Ry(),t=g(e),n=Xo(),r=g(n),i=ky(),o=g(i),u=Ny(),c=g(u),l=Hy(),s=g(l),a=qh(),d=g(a),f=jy(),h=g(f),p=Wh(),m=Gh(),_=g(m);function g(T){return T&&T.__esModule?T:{default:T}}var v=function(){},y=function(R,k,M){var H=new v;if(typeof R>"u")throw Error("No element to render on was provided.");return H._renderProperties=(0,s.default)(R),H._encodings=[],H._options=_.default,H._errorHandler=new h.default(H),typeof k<"u"&&(M=M||{},M.format||(M.format=w()),H.options(M)[M.format](k,M).render()),H};y.getModule=function(T){return t.default[T]};for(var E in t.default)t.default.hasOwnProperty(E)&&L(t.default,E);function L(T,R){v.prototype[R]=v.prototype[R.toUpperCase()]=v.prototype[R.toLowerCase()]=function(k,M){var H=this;return H._errorHandler.wrapBarcodeCall(function(){M.text=typeof M.text>"u"?void 0:""+M.text;var N=(0,r.default)(H._options,M);N=(0,d.default)(N);var G=T[R],Y=I(k,G,N);return H._encodings.push(Y),H})}}function I(T,R,k){T=""+T;var M=new R(T,k);if(!M.valid())throw new p.InvalidInputException(M.constructor.name,T);var H=M.encode();H=(0,o.default)(H);for(var N=0;N<H.length;N++)H[N].options=(0,r.default)(k,H[N].options);return H}function w(){return t.default.CODE128?"CODE128":Object.keys(t.default)[0]}v.prototype.options=function(T){return this._options=(0,r.default)(this._options,T),this},v.prototype.blank=function(T){var R=new Array(T+1).join("0");return this._encodings.push({data:R}),this},v.prototype.init=function(){if(this._renderProperties){Array.isArray(this._renderProperties)||(this._renderProperties=[this._renderProperties]);var T;for(var R in this._renderProperties){T=this._renderProperties[R];var k=(0,r.default)(this._options,T.options);k.format=="auto"&&(k.format=w()),this._errorHandler.wrapBarcodeCall(function(){var M=k.value,H=t.default[k.format.toUpperCase()],N=I(M,H,k);D(T,N,k)})}}},v.prototype.render=function(){if(!this._renderProperties)throw new p.NoElementException;if(Array.isArray(this._renderProperties))for(var T=0;T<this._renderProperties.length;T++)D(this._renderProperties[T],this._encodings,this._options);else D(this._renderProperties,this._encodings,this._options);return this},v.prototype._defaults=_.default;function D(T,R,k){R=(0,o.default)(R);for(var M=0;M<R.length;M++)R[M].options=(0,r.default)(k,R[M].options),(0,c.default)(R[M].options);(0,c.default)(k);var H=T.renderer,N=new H(T.element,R,k);N.render(),T.afterRender&&T.afterRender()}return typeof window<"u"&&(window.JsBarcode=y),typeof jQuery<"u"&&(jQuery.fn.JsBarcode=function(T,R){var k=[];return jQuery(this).each(function(){k.push(this)}),y(k,T,R)}),_s=y,_s}var Yy=Uy();const qy=Lf(Yy);var jn={},vs,ef;function Gy(){return ef||(ef=1,vs=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),vs}var ys={},tn={},tf;function kn(){if(tf)return tn;tf=1;let e;const t=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return tn.getSymbolSize=function(r){if(!r)throw new Error('"version" cannot be null or undefined');if(r<1||r>40)throw new Error('"version" should be in range from 1 to 40');return r*4+17},tn.getSymbolTotalCodewords=function(r){return t[r]},tn.getBCHDigit=function(n){let r=0;for(;n!==0;)r++,n>>>=1;return r},tn.setToSJISFunction=function(r){if(typeof r!="function")throw new Error('"toSJISFunc" is not a valid function.');e=r},tn.isKanjiModeEnabled=function(){return typeof e<"u"},tn.toSJIS=function(r){return e(r)},tn}var bs={},nf;function Va(){return nf||(nf=1,function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2};function t(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+n)}}e.isValid=function(r){return r&&typeof r.bit<"u"&&r.bit>=0&&r.bit<4},e.from=function(r,i){if(e.isValid(r))return r;try{return t(r)}catch(o){return i}}}(bs)),bs}var Es,rf;function Vy(){if(rf)return Es;rf=1;function e(){this.buffer=[],this.length=0}return e.prototype={get:function(t){const n=Math.floor(t/8);return(this.buffer[n]>>>7-t%8&1)===1},put:function(t,n){for(let r=0;r<n;r++)this.putBit((t>>>n-r-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(t){const n=Math.floor(this.length/8);this.buffer.length<=n&&this.buffer.push(0),t&&(this.buffer[n]|=128>>>this.length%8),this.length++}},Es=e,Es}var As,of;function Wy(){if(of)return As;of=1;function e(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}return e.prototype.set=function(t,n,r,i){const o=t*this.size+n;this.data[o]=r,i&&(this.reservedBit[o]=!0)},e.prototype.get=function(t,n){return this.data[t*this.size+n]},e.prototype.xor=function(t,n,r){this.data[t*this.size+n]^=r},e.prototype.isReserved=function(t,n){return this.reservedBit[t*this.size+n]},As=e,As}var ws={},sf;function zy(){return sf||(sf=1,function(e){const t=kn().getSymbolSize;e.getRowColCoords=function(r){if(r===1)return[];const i=Math.floor(r/7)+2,o=t(r),u=o===145?26:Math.ceil((o-13)/(2*i-2))*2,c=[o-7];for(let l=1;l<i-1;l++)c[l]=c[l-1]-u;return c.push(6),c.reverse()},e.getPositions=function(r){const i=[],o=e.getRowColCoords(r),u=o.length;for(let c=0;c<u;c++)for(let l=0;l<u;l++)c===0&&l===0||c===0&&l===u-1||c===u-1&&l===0||i.push([o[c],o[l]]);return i}}(ws)),ws}var Os={},af;function Xy(){if(af)return Os;af=1;const e=kn().getSymbolSize,t=7;return Os.getPositions=function(r){const i=e(r);return[[0,0],[i-t,0],[0,i-t]]},Os}var Ts={},uf;function Ky(){return uf||(uf=1,function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};e.isValid=function(i){return i!=null&&i!==""&&!isNaN(i)&&i>=0&&i<=7},e.from=function(i){return e.isValid(i)?parseInt(i,10):void 0},e.getPenaltyN1=function(i){const o=i.size;let u=0,c=0,l=0,s=null,a=null;for(let d=0;d<o;d++){c=l=0,s=a=null;for(let f=0;f<o;f++){let h=i.get(d,f);h===s?c++:(c>=5&&(u+=t.N1+(c-5)),s=h,c=1),h=i.get(f,d),h===a?l++:(l>=5&&(u+=t.N1+(l-5)),a=h,l=1)}c>=5&&(u+=t.N1+(c-5)),l>=5&&(u+=t.N1+(l-5))}return u},e.getPenaltyN2=function(i){const o=i.size;let u=0;for(let c=0;c<o-1;c++)for(let l=0;l<o-1;l++){const s=i.get(c,l)+i.get(c,l+1)+i.get(c+1,l)+i.get(c+1,l+1);(s===4||s===0)&&u++}return u*t.N2},e.getPenaltyN3=function(i){const o=i.size;let u=0,c=0,l=0;for(let s=0;s<o;s++){c=l=0;for(let a=0;a<o;a++)c=c<<1&2047|i.get(s,a),a>=10&&(c===1488||c===93)&&u++,l=l<<1&2047|i.get(a,s),a>=10&&(l===1488||l===93)&&u++}return u*t.N3},e.getPenaltyN4=function(i){let o=0;const u=i.data.length;for(let l=0;l<u;l++)o+=i.data[l];return Math.abs(Math.ceil(o*100/u/5)-10)*t.N4};function n(r,i,o){switch(r){case e.Patterns.PATTERN000:return(i+o)%2===0;case e.Patterns.PATTERN001:return i%2===0;case e.Patterns.PATTERN010:return o%3===0;case e.Patterns.PATTERN011:return(i+o)%3===0;case e.Patterns.PATTERN100:return(Math.floor(i/2)+Math.floor(o/3))%2===0;case e.Patterns.PATTERN101:return i*o%2+i*o%3===0;case e.Patterns.PATTERN110:return(i*o%2+i*o%3)%2===0;case e.Patterns.PATTERN111:return(i*o%3+(i+o)%2)%2===0;default:throw new Error("bad maskPattern:"+r)}}e.applyMask=function(i,o){const u=o.size;for(let c=0;c<u;c++)for(let l=0;l<u;l++)o.isReserved(l,c)||o.xor(l,c,n(i,l,c))},e.getBestMask=function(i,o){const u=Object.keys(e.Patterns).length;let c=0,l=1/0;for(let s=0;s<u;s++){o(s),e.applyMask(s,i);const a=e.getPenaltyN1(i)+e.getPenaltyN2(i)+e.getPenaltyN3(i)+e.getPenaltyN4(i);e.applyMask(s,i),a<l&&(l=a,c=s)}return c}}(Ts)),Ts}var Vi={},lf;function zh(){if(lf)return Vi;lf=1;const e=Va(),t=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],n=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return Vi.getBlocksCount=function(i,o){switch(o){case e.L:return t[(i-1)*4+0];case e.M:return t[(i-1)*4+1];case e.Q:return t[(i-1)*4+2];case e.H:return t[(i-1)*4+3];default:return}},Vi.getTotalCodewordsCount=function(i,o){switch(o){case e.L:return n[(i-1)*4+0];case e.M:return n[(i-1)*4+1];case e.Q:return n[(i-1)*4+2];case e.H:return n[(i-1)*4+3];default:return}},Vi}var Ss={},pr={},cf;function Qy(){if(cf)return pr;cf=1;const e=new Uint8Array(512),t=new Uint8Array(256);return function(){let r=1;for(let i=0;i<255;i++)e[i]=r,t[r]=i,r<<=1,r&256&&(r^=285);for(let i=255;i<512;i++)e[i]=e[i-255]}(),pr.log=function(r){if(r<1)throw new Error("log("+r+")");return t[r]},pr.exp=function(r){return e[r]},pr.mul=function(r,i){return r===0||i===0?0:e[t[r]+t[i]]},pr}var ff;function Jy(){return ff||(ff=1,function(e){const t=Qy();e.mul=function(r,i){const o=new Uint8Array(r.length+i.length-1);for(let u=0;u<r.length;u++)for(let c=0;c<i.length;c++)o[u+c]^=t.mul(r[u],i[c]);return o},e.mod=function(r,i){let o=new Uint8Array(r);for(;o.length-i.length>=0;){const u=o[0];for(let l=0;l<i.length;l++)o[l]^=t.mul(i[l],u);let c=0;for(;c<o.length&&o[c]===0;)c++;o=o.slice(c)}return o},e.generateECPolynomial=function(r){let i=new Uint8Array([1]);for(let o=0;o<r;o++)i=e.mul(i,new Uint8Array([1,t.exp(o)]));return i}}(Ss)),Ss}var Cs,df;function Zy(){if(df)return Cs;df=1;const e=Jy();function t(n){this.genPoly=void 0,this.degree=n,this.degree&&this.initialize(this.degree)}return t.prototype.initialize=function(r){this.degree=r,this.genPoly=e.generateECPolynomial(this.degree)},t.prototype.encode=function(r){if(!this.genPoly)throw new Error("Encoder not initialized");const i=new Uint8Array(r.length+this.degree);i.set(r);const o=e.mod(i,this.genPoly),u=this.degree-o.length;if(u>0){const c=new Uint8Array(this.degree);return c.set(o,u),c}return o},Cs=t,Cs}var Is={},Ds={},Ms={},hf;function Xh(){return hf||(hf=1,Ms.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}),Ms}var Nt={},mf;function Kh(){if(mf)return Nt;mf=1;const e="[0-9]+",t="[A-Z $%*+\\-./:]+";let n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";n=n.replace(/u/g,"\\u");const r="(?:(?![A-Z0-9 $%*+\\-./:]|"+n+")(?:.|[\r\n]))+";Nt.KANJI=new RegExp(n,"g"),Nt.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),Nt.BYTE=new RegExp(r,"g"),Nt.NUMERIC=new RegExp(e,"g"),Nt.ALPHANUMERIC=new RegExp(t,"g");const i=new RegExp("^"+n+"$"),o=new RegExp("^"+e+"$"),u=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return Nt.testKanji=function(l){return i.test(l)},Nt.testNumeric=function(l){return o.test(l)},Nt.testAlphanumeric=function(l){return u.test(l)},Nt}var pf;function Nn(){return pf||(pf=1,function(e){const t=Xh(),n=Kh();e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(o,u){if(!o.ccBits)throw new Error("Invalid mode: "+o);if(!t.isValid(u))throw new Error("Invalid version: "+u);return u>=1&&u<10?o.ccBits[0]:u<27?o.ccBits[1]:o.ccBits[2]},e.getBestModeForData=function(o){return n.testNumeric(o)?e.NUMERIC:n.testAlphanumeric(o)?e.ALPHANUMERIC:n.testKanji(o)?e.KANJI:e.BYTE},e.toString=function(o){if(o&&o.id)return o.id;throw new Error("Invalid mode")},e.isValid=function(o){return o&&o.bit&&o.ccBits};function r(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+i)}}e.from=function(o,u){if(e.isValid(o))return o;try{return r(o)}catch(c){return u}}}(Ds)),Ds}var gf;function eb(){return gf||(gf=1,function(e){const t=kn(),n=zh(),r=Va(),i=Nn(),o=Xh(),u=7973,c=t.getBCHDigit(u);function l(f,h,p){for(let m=1;m<=40;m++)if(h<=e.getCapacity(m,p,f))return m}function s(f,h){return i.getCharCountIndicator(f,h)+4}function a(f,h){let p=0;return f.forEach(function(m){const _=s(m.mode,h);p+=_+m.getBitsLength()}),p}function d(f,h){for(let p=1;p<=40;p++)if(a(f,p)<=e.getCapacity(p,h,i.MIXED))return p}e.from=function(h,p){return o.isValid(h)?parseInt(h,10):p},e.getCapacity=function(h,p,m){if(!o.isValid(h))throw new Error("Invalid QR Code version");typeof m>"u"&&(m=i.BYTE);const _=t.getSymbolTotalCodewords(h),g=n.getTotalCodewordsCount(h,p),v=(_-g)*8;if(m===i.MIXED)return v;const y=v-s(m,h);switch(m){case i.NUMERIC:return Math.floor(y/10*3);case i.ALPHANUMERIC:return Math.floor(y/11*2);case i.KANJI:return Math.floor(y/13);case i.BYTE:default:return Math.floor(y/8)}},e.getBestVersionForData=function(h,p){let m;const _=r.from(p,r.M);if(Array.isArray(h)){if(h.length>1)return d(h,_);if(h.length===0)return 1;m=h[0]}else m=h;return l(m.mode,m.getLength(),_)},e.getEncodedBits=function(h){if(!o.isValid(h)||h<7)throw new Error("Invalid QR Code version");let p=h<<12;for(;t.getBCHDigit(p)-c>=0;)p^=u<<t.getBCHDigit(p)-c;return h<<12|p}}(Is)),Is}var Ls={},_f;function tb(){if(_f)return Ls;_f=1;const e=kn(),t=1335,n=21522,r=e.getBCHDigit(t);return Ls.getEncodedBits=function(o,u){const c=o.bit<<3|u;let l=c<<10;for(;e.getBCHDigit(l)-r>=0;)l^=t<<e.getBCHDigit(l)-r;return(c<<10|l)^n},Ls}var Rs={},ks,vf;function nb(){if(vf)return ks;vf=1;const e=Nn();function t(n){this.mode=e.NUMERIC,this.data=n.toString()}return t.getBitsLength=function(r){return 10*Math.floor(r/3)+(r%3?r%3*3+1:0)},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(r){let i,o,u;for(i=0;i+3<=this.data.length;i+=3)o=this.data.substr(i,3),u=parseInt(o,10),r.put(u,10);const c=this.data.length-i;c>0&&(o=this.data.substr(i),u=parseInt(o,10),r.put(u,c*3+1))},ks=t,ks}var Ns,yf;function rb(){if(yf)return Ns;yf=1;const e=Nn(),t=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function n(r){this.mode=e.ALPHANUMERIC,this.data=r}return n.getBitsLength=function(i){return 11*Math.floor(i/2)+6*(i%2)},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(i){let o;for(o=0;o+2<=this.data.length;o+=2){let u=t.indexOf(this.data[o])*45;u+=t.indexOf(this.data[o+1]),i.put(u,11)}this.data.length%2&&i.put(t.indexOf(this.data[o]),6)},Ns=n,Ns}var xs,bf;function ib(){if(bf)return xs;bf=1;const e=Nn();function t(n){this.mode=e.BYTE,typeof n=="string"?this.data=new TextEncoder().encode(n):this.data=new Uint8Array(n)}return t.getBitsLength=function(r){return r*8},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(n){for(let r=0,i=this.data.length;r<i;r++)n.put(this.data[r],8)},xs=t,xs}var Ps,Ef;function ob(){if(Ef)return Ps;Ef=1;const e=Nn(),t=kn();function n(r){this.mode=e.KANJI,this.data=r}return n.getBitsLength=function(i){return i*13},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(r){let i;for(i=0;i<this.data.length;i++){let o=t.toSJIS(this.data[i]);if(o>=33088&&o<=40956)o-=33088;else if(o>=57408&&o<=60351)o-=49472;else throw new Error("Invalid SJIS character: "+this.data[i]+"\nMake sure your charset is UTF-8");o=(o>>>8&255)*192+(o&255),r.put(o,13)}},Ps=n,Ps}var Fs={exports:{}},Af;function sb(){return Af||(Af=1,function(e){var t={single_source_shortest_paths:function(n,r,i){var o={},u={};u[r]=0;var c=t.PriorityQueue.make();c.push(r,0);for(var l,s,a,d,f,h,p,m,_;!c.empty();){l=c.pop(),s=l.value,d=l.cost,f=n[s]||{};for(a in f)f.hasOwnProperty(a)&&(h=f[a],p=d+h,m=u[a],_=typeof u[a]>"u",(_||m>p)&&(u[a]=p,c.push(a,p),o[a]=s))}if(typeof i<"u"&&typeof u[i]>"u"){var g=["Could not find a path from ",r," to ",i,"."].join("");throw new Error(g)}return o},extract_shortest_path_from_predecessor_list:function(n,r){for(var i=[],o=r;o;)i.push(o),n[o],o=n[o];return i.reverse(),i},find_path:function(n,r,i){var o=t.single_source_shortest_paths(n,r,i);return t.extract_shortest_path_from_predecessor_list(o,i)},PriorityQueue:{make:function(n){var r=t.PriorityQueue,i={},o;n=n||{};for(o in r)r.hasOwnProperty(o)&&(i[o]=r[o]);return i.queue=[],i.sorter=n.sorter||r.default_sorter,i},default_sorter:function(n,r){return n.cost-r.cost},push:function(n,r){var i={value:n,cost:r};this.queue.push(i),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};e.exports=t}(Fs)),Fs.exports}var wf;function ab(){return wf||(wf=1,function(e){const t=Nn(),n=nb(),r=rb(),i=ib(),o=ob(),u=Kh(),c=kn(),l=sb();function s(g){return unescape(encodeURIComponent(g)).length}function a(g,v,y){const E=[];let L;for(;(L=g.exec(y))!==null;)E.push({data:L[0],index:L.index,mode:v,length:L[0].length});return E}function d(g){const v=a(u.NUMERIC,t.NUMERIC,g),y=a(u.ALPHANUMERIC,t.ALPHANUMERIC,g);let E,L;return c.isKanjiModeEnabled()?(E=a(u.BYTE,t.BYTE,g),L=a(u.KANJI,t.KANJI,g)):(E=a(u.BYTE_KANJI,t.BYTE,g),L=[]),v.concat(y,E,L).sort(function(w,D){return w.index-D.index}).map(function(w){return{data:w.data,mode:w.mode,length:w.length}})}function f(g,v){switch(v){case t.NUMERIC:return n.getBitsLength(g);case t.ALPHANUMERIC:return r.getBitsLength(g);case t.KANJI:return o.getBitsLength(g);case t.BYTE:return i.getBitsLength(g)}}function h(g){return g.reduce(function(v,y){const E=v.length-1>=0?v[v.length-1]:null;return E&&E.mode===y.mode?(v[v.length-1].data+=y.data,v):(v.push(y),v)},[])}function p(g){const v=[];for(let y=0;y<g.length;y++){const E=g[y];switch(E.mode){case t.NUMERIC:v.push([E,{data:E.data,mode:t.ALPHANUMERIC,length:E.length},{data:E.data,mode:t.BYTE,length:E.length}]);break;case t.ALPHANUMERIC:v.push([E,{data:E.data,mode:t.BYTE,length:E.length}]);break;case t.KANJI:v.push([E,{data:E.data,mode:t.BYTE,length:s(E.data)}]);break;case t.BYTE:v.push([{data:E.data,mode:t.BYTE,length:s(E.data)}])}}return v}function m(g,v){const y={},E={start:{}};let L=["start"];for(let I=0;I<g.length;I++){const w=g[I],D=[];for(let T=0;T<w.length;T++){const R=w[T],k=""+I+T;D.push(k),y[k]={node:R,lastCount:0},E[k]={};for(let M=0;M<L.length;M++){const H=L[M];y[H]&&y[H].node.mode===R.mode?(E[H][k]=f(y[H].lastCount+R.length,R.mode)-f(y[H].lastCount,R.mode),y[H].lastCount+=R.length):(y[H]&&(y[H].lastCount=R.length),E[H][k]=f(R.length,R.mode)+4+t.getCharCountIndicator(R.mode,v))}}L=D}for(let I=0;I<L.length;I++)E[L[I]].end=0;return{map:E,table:y}}function _(g,v){let y;const E=t.getBestModeForData(g);if(y=t.from(v,E),y!==t.BYTE&&y.bit<E.bit)throw new Error('"'+g+'" cannot be encoded with mode '+t.toString(y)+".\n Suggested mode is: "+t.toString(E));switch(y===t.KANJI&&!c.isKanjiModeEnabled()&&(y=t.BYTE),y){case t.NUMERIC:return new n(g);case t.ALPHANUMERIC:return new r(g);case t.KANJI:return new o(g);case t.BYTE:return new i(g)}}e.fromArray=function(v){return v.reduce(function(y,E){return typeof E=="string"?y.push(_(E,null)):E.data&&y.push(_(E.data,E.mode)),y},[])},e.fromString=function(v,y){const E=d(v,c.isKanjiModeEnabled()),L=p(E),I=m(L,y),w=l.find_path(I.map,"start","end"),D=[];for(let T=1;T<w.length-1;T++)D.push(I.table[w[T]].node);return e.fromArray(h(D))},e.rawSplit=function(v){return e.fromArray(d(v,c.isKanjiModeEnabled()))}}(Rs)),Rs}var Of;function ub(){if(Of)return ys;Of=1;const e=kn(),t=Va(),n=Vy(),r=Wy(),i=zy(),o=Xy(),u=Ky(),c=zh(),l=Zy(),s=eb(),a=tb(),d=Nn(),f=ab();function h(I,w){const D=I.size,T=o.getPositions(w);for(let R=0;R<T.length;R++){const k=T[R][0],M=T[R][1];for(let H=-1;H<=7;H++)if(!(k+H<=-1||D<=k+H))for(let N=-1;N<=7;N++)M+N<=-1||D<=M+N||(H>=0&&H<=6&&(N===0||N===6)||N>=0&&N<=6&&(H===0||H===6)||H>=2&&H<=4&&N>=2&&N<=4?I.set(k+H,M+N,!0,!0):I.set(k+H,M+N,!1,!0))}}function p(I){const w=I.size;for(let D=8;D<w-8;D++){const T=D%2===0;I.set(D,6,T,!0),I.set(6,D,T,!0)}}function m(I,w){const D=i.getPositions(w);for(let T=0;T<D.length;T++){const R=D[T][0],k=D[T][1];for(let M=-2;M<=2;M++)for(let H=-2;H<=2;H++)M===-2||M===2||H===-2||H===2||M===0&&H===0?I.set(R+M,k+H,!0,!0):I.set(R+M,k+H,!1,!0)}}function _(I,w){const D=I.size,T=s.getEncodedBits(w);let R,k,M;for(let H=0;H<18;H++)R=Math.floor(H/3),k=H%3+D-8-3,M=(T>>H&1)===1,I.set(R,k,M,!0),I.set(k,R,M,!0)}function g(I,w,D){const T=I.size,R=a.getEncodedBits(w,D);let k,M;for(k=0;k<15;k++)M=(R>>k&1)===1,k<6?I.set(k,8,M,!0):k<8?I.set(k+1,8,M,!0):I.set(T-15+k,8,M,!0),k<8?I.set(8,T-k-1,M,!0):k<9?I.set(8,15-k-1+1,M,!0):I.set(8,15-k-1,M,!0);I.set(T-8,8,1,!0)}function v(I,w){const D=I.size;let T=-1,R=D-1,k=7,M=0;for(let H=D-1;H>0;H-=2)for(H===6&&H--;;){for(let N=0;N<2;N++)if(!I.isReserved(R,H-N)){let G=!1;M<w.length&&(G=(w[M]>>>k&1)===1),I.set(R,H-N,G),k--,k===-1&&(M++,k=7)}if(R+=T,R<0||D<=R){R-=T,T=-T;break}}}function y(I,w,D){const T=new n;D.forEach(function(N){T.put(N.mode.bit,4),T.put(N.getLength(),d.getCharCountIndicator(N.mode,I)),N.write(T)});const R=e.getSymbolTotalCodewords(I),k=c.getTotalCodewordsCount(I,w),M=(R-k)*8;for(T.getLengthInBits()+4<=M&&T.put(0,4);T.getLengthInBits()%8!==0;)T.putBit(0);const H=(M-T.getLengthInBits())/8;for(let N=0;N<H;N++)T.put(N%2?17:236,8);return E(T,I,w)}function E(I,w,D){const T=e.getSymbolTotalCodewords(w),R=c.getTotalCodewordsCount(w,D),k=T-R,M=c.getBlocksCount(w,D),H=T%M,N=M-H,G=Math.floor(T/M),Y=Math.floor(k/M),V=Y+1,W=G-Y,J=new l(W);let te=0;const ie=new Array(M),ue=new Array(M);let me=0;const ve=new Uint8Array(I.buffer);for(let X=0;X<M;X++){const Q=X<N?Y:V;ie[X]=ve.slice(te,te+Q),ue[X]=J.encode(ie[X]),te+=Q,me=Math.max(me,Q)}const Te=new Uint8Array(T);let Ae=0,U,K;for(U=0;U<me;U++)for(K=0;K<M;K++)U<ie[K].length&&(Te[Ae++]=ie[K][U]);for(U=0;U<W;U++)for(K=0;K<M;K++)Te[Ae++]=ue[K][U];return Te}function L(I,w,D,T){let R;if(Array.isArray(I))R=f.fromArray(I);else if(typeof I=="string"){let G=w;if(!G){const Y=f.rawSplit(I);G=s.getBestVersionForData(Y,D)}R=f.fromString(I,G||40)}else throw new Error("Invalid data");const k=s.getBestVersionForData(R,D);if(!k)throw new Error("The amount of data is too big to be stored in a QR Code");if(!w)w=k;else if(w<k)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+k+".\n");const M=y(w,D,R),H=e.getSymbolSize(w),N=new r(H);return h(N,w),p(N),m(N,w),g(N,D,0),w>=7&&_(N,w),v(N,M),isNaN(T)&&(T=u.getBestMask(N,g.bind(null,N,D))),u.applyMask(T,N),g(N,D,T),{modules:N,version:w,errorCorrectionLevel:D,maskPattern:T,segments:R}}return ys.create=function(w,D){if(typeof w>"u"||w==="")throw new Error("No input text");let T=t.M,R,k;return typeof D<"u"&&(T=t.from(D.errorCorrectionLevel,t.M),R=s.from(D.version),k=u.from(D.maskPattern),D.toSJISFunc&&e.setToSJISFunction(D.toSJISFunc)),L(w,R,T,k)},ys}var Bs={},$s={},Tf;function Qh(){return Tf||(Tf=1,function(e){function t(n){if(typeof n=="number"&&(n=n.toString()),typeof n!="string")throw new Error("Color should be defined as hex string");let r=n.slice().replace("#","").split("");if(r.length<3||r.length===5||r.length>8)throw new Error("Invalid hex color: "+n);(r.length===3||r.length===4)&&(r=Array.prototype.concat.apply([],r.map(function(o){return[o,o]}))),r.length===6&&r.push("F","F");const i=parseInt(r.join(""),16);return{r:i>>24&255,g:i>>16&255,b:i>>8&255,a:i&255,hex:"#"+r.slice(0,6).join("")}}e.getOptions=function(r){r||(r={}),r.color||(r.color={});const i=typeof r.margin>"u"||r.margin===null||r.margin<0?4:r.margin,o=r.width&&r.width>=21?r.width:void 0,u=r.scale||4;return{width:o,scale:o?4:u,margin:i,color:{dark:t(r.color.dark||"#000000ff"),light:t(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},e.getScale=function(r,i){return i.width&&i.width>=r+i.margin*2?i.width/(r+i.margin*2):i.scale},e.getImageWidth=function(r,i){const o=e.getScale(r,i);return Math.floor((r+i.margin*2)*o)},e.qrToImageData=function(r,i,o){const u=i.modules.size,c=i.modules.data,l=e.getScale(u,o),s=Math.floor((u+o.margin*2)*l),a=o.margin*l,d=[o.color.light,o.color.dark];for(let f=0;f<s;f++)for(let h=0;h<s;h++){let p=(f*s+h)*4,m=o.color.light;if(f>=a&&h>=a&&f<s-a&&h<s-a){const _=Math.floor((f-a)/l),g=Math.floor((h-a)/l);m=d[c[_*u+g]?1:0]}r[p++]=m.r,r[p++]=m.g,r[p++]=m.b,r[p]=m.a}}}($s)),$s}var Sf;function lb(){return Sf||(Sf=1,function(e){const t=Qh();function n(i,o,u){i.clearRect(0,0,o.width,o.height),o.style||(o.style={}),o.height=u,o.width=u,o.style.height=u+"px",o.style.width=u+"px"}function r(){try{return document.createElement("canvas")}catch(i){throw new Error("You need to specify a canvas element")}}e.render=function(o,u,c){let l=c,s=u;typeof l>"u"&&(!u||!u.getContext)&&(l=u,u=void 0),u||(s=r()),l=t.getOptions(l);const a=t.getImageWidth(o.modules.size,l),d=s.getContext("2d"),f=d.createImageData(a,a);return t.qrToImageData(f.data,o,l),n(d,s,a),d.putImageData(f,0,0),s},e.renderToDataURL=function(o,u,c){let l=c;typeof l>"u"&&(!u||!u.getContext)&&(l=u,u=void 0),l||(l={});const s=e.render(o,u,l),a=l.type||"image/png",d=l.rendererOpts||{};return s.toDataURL(a,d.quality)}}(Bs)),Bs}var Hs={},Cf;function cb(){if(Cf)return Hs;Cf=1;const e=Qh();function t(i,o){const u=i.a/255,c=o+'="'+i.hex+'"';return u<1?c+" "+o+'-opacity="'+u.toFixed(2).slice(1)+'"':c}function n(i,o,u){let c=i+o;return typeof u<"u"&&(c+=" "+u),c}function r(i,o,u){let c="",l=0,s=!1,a=0;for(let d=0;d<i.length;d++){const f=Math.floor(d%o),h=Math.floor(d/o);!f&&!s&&(s=!0),i[d]?(a++,d>0&&f>0&&i[d-1]||(c+=s?n("M",f+u,.5+h+u):n("m",l,0),l=0,s=!1),f+1<o&&i[d+1]||(c+=n("h",a),a=0)):l++}return c}return Hs.render=function(o,u,c){const l=e.getOptions(u),s=o.modules.size,a=o.modules.data,d=s+l.margin*2,f=l.color.light.a?"<path "+t(l.color.light,"fill")+' d="M0 0h'+d+"v"+d+'H0z"/>':"",h="<path "+t(l.color.dark,"stroke")+' d="'+r(a,s,l.margin)+'"/>',p='viewBox="0 0 '+d+" "+d+'"',_='<svg xmlns="http://www.w3.org/2000/svg" '+(l.width?'width="'+l.width+'" height="'+l.width+'" ':"")+p+' shape-rendering="crispEdges">'+f+h+"</svg>\n";return typeof c=="function"&&c(null,_),_},Hs}var If;function fb(){if(If)return jn;If=1;const e=Gy(),t=ub(),n=lb(),r=cb();function i(o,u,c,l,s){const a=[].slice.call(arguments,1),d=a.length,f=typeof a[d-1]=="function";if(!f&&!e())throw new Error("Callback required as last argument");if(f){if(d<2)throw new Error("Too few arguments provided");d===2?(s=c,c=u,u=l=void 0):d===3&&(u.getContext&&typeof s>"u"?(s=l,l=void 0):(s=l,l=c,c=u,u=void 0))}else{if(d<1)throw new Error("Too few arguments provided");return d===1?(c=u,u=l=void 0):d===2&&!u.getContext&&(l=c,c=u,u=void 0),new Promise(function(h,p){try{const m=t.create(c,l);h(o(m,u,l))}catch(m){p(m)}})}try{const h=t.create(c,l);s(null,o(h,u,l))}catch(h){s(h)}}return jn.create=t.create,jn.toCanvas=i.bind(null,n.render),jn.toDataURL=i.bind(null,n.renderToDataURL),jn.toString=i.bind(null,function(o,u,c){return r.render(o,c)}),jn}var db=fb();const hb=Lf(db),mb="data:image/png;base64,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",pb=["src"],gb=["src"],_b=["id"],vb=["innerHTML"],yb={key:0,class:"card-date-join"},bb={class:"label"},Eb={class:"date"},Ab={class:"label"},wb={class:"date"};parseFloat(getComputedStyle(document.documentElement).getPropertyValue("--width-card"));var va;(function(e){(function(t){t.CONNECT="remote.connect",t.SEND="remote.send",t.CLOSE="remote.close"})(e.Remote||(e.Remote={}))})(va||(va={}));const{CONNECT:DE,SEND:ME,CLOSE:LE}=va.Remote,Ob={button:{ok:"Ok",cancel:"Cancel",submit:"Submit",delete:"Delete"},form:{search:"search",join:"join",expire:"expire"},countdown:{before:{prefix:"end on ",suffix:""},after:"Event ended",counting_down:{prefix:"end in ",suffix:""},counting_up:{prefix:"overdue",suffix:""},time_unit:{dd:"day",hh:"hour",mm:"min",ss:"sec"}},error:{offline_status:"offline",failed_to_get_server_time:"Failed to get server time",is_required:"required",minimum_length:"min. {n} character | min. {n} characters",maximum_length:"max. {n} character | max. {n} characters",minimum_number:"must be {n} or above",maximum_number:"must be {n} or below",invalid_pattern:"wrong format",invalid:"invalid",invalid_date:"wrong date format",before_minimum_date:"must be {date} or later",after_maximum_date:"must be {date} or earlier",above_minimum_amount:"must be {amount} or above",below_maximum_amount:"must be {amount} or below"}},Tb={en:Ob,"zh-Hans":{button:{ok:"好",cancel:"取消",submit:"提交",delete:"删除"},form:{search:"搜索",join:"加入",expire:"到期"},countdown:{before:{prefix:"",suffix:"结束"},after:"活动已结束",counting_down:{prefix:"",suffix:"后结束"},counting_up:{prefix:"超时",suffix:""},time_unit:{dd:"天",hh:"时",mm:"分",ss:"秒"}},error:{offline_status:"离线",failed_to_get_server_time:"服务器时间获取失败",is_required:"必填项",minimum_length:"最少{n}个字符",maximum_length:"最多{n}个字符",minimum_number:"最少为{n}",maximum_number:"最多为{n}",invalid_pattern:"格式错误",invalid:"错误",invalid_date:"时间格式错误",before_minimum_date:"应该在{date}或之后",after_maximum_date:"应该在{date}或之前",above_minimum_amount:"金额至少为{amount}",below_maximum_amount:"金额最多为{amount}"}},"zh-Hant":{button:{ok:"好",cancel:"取消",submit:"提交",delete:"刪除"},form:{search:"搜寻",join:"加入",expire:"到期"},countdown:{before:{prefix:"",suffix:"結束"},after:"活動已結束",counting_down:{prefix:"",suffix:"後結束"},counting_up:{prefix:"超時",suffix:""},time_unit:{dd:"天",hh:"时",mm:"分",ss:"秒"}},error:{offline_status:"離線",failed_to_get_server_time:"服務器時間獲取失敗",is_required:"必填項",minimum_length:"最少{n}個字符",maximum_length:"最多{n}個字符",minimum_number:"最少为{n}",maximum_number:"最多为{n}",invalid_pattern:"格式錯誤",invalid:"錯誤",invalid_date:"時間格式錯誤",before_minimum_date:"應該在{date}之後",after_maximum_date:"應該在{date}之前",above_minimum_amount:"金額至少為{amount}",below_maximum_amount:"金額最多為{amount}"}}};wh({legacy:!1,locale:"en",messages:Tb});const Ko=Ge({__name:"UIScreen",setup(e,{expose:t}){const{t:n}=_t(),r=Bp(),i=Rn(),{START_PAGE:o}=i,u=Bh(),c=zr(),l=de(()=>r.title||""),s=_e(void 0),a=de(()=>u.name===o),d={get(m,_){if(_ in m)return typeof m[_]=="function"?m[_].bind(m):m[_];console.warn("Method ".concat(_," is not defined on UIScreen."))}},f=new Proxy({},d);function h(){c.go(-1)}$t(()=>{s.value&&Object.setPrototypeOf(f,s.value)});function p(){var m;(m=i.app)==null||m.unmount(),kl()}return t(f),(m,_)=>(ne(),et(Z(ny),yo({ref_key:"screenRef",ref:s,disableNavBack:a.value,"page-transition":!1},Z(r),{onCloseWindow:p,isOnline:Z(i).isOnline}),Mn({_:2},[m.$slots.navigationBar?{name:"navigationBar",fn:He(()=>[Qe(m.$slots,"navigationBar")]),key:"0"}:{name:"navigationBar",fn:He(()=>[Ee(Z(Hh),{title:l.value,class:$e(Z(i).ENVIRONMENT.DEVICE.IOS?"ios":"android")},Mn({rightContent:He(()=>[Ee(Z(Bt),{type:"circle",icon:{name:"close"},onClick:Z(kl)},null,8,["onClick"])]),_:2},[a.value?void 0:{name:"leftContent",fn:He(()=>[Ee(Z(Bt),{type:"clear",icon:{name:"back"},class:"back-button",onClick:h})]),key:"0"}]),1032,["title","class"])]),key:"1"},qr(m.$slots,(g,v)=>({name:v,fn:He(y=>[Qe(m.$slots,v,$f(zd(y)))])}))]),1040,["disableNavBack","isOnline"]))}}),Sb={class:"screen-container"},Cb=Ge({__name:"index",setup(e){const t=zr(),{t:n}=_t(),r=Rn(),{WIDGET:i,ENVIRONMENT:o,APPLET_NAME:u}=r,c=_e(!1),l=_e(void 0),s=_e(""),a=_e(void 0);Fo(async()=>{c.value=!0,await d(),c.value=!1});const d=async()=>{c.value=!0;const f=await new Promise(h=>{const p=Math.random();setTimeout(()=>{h(p>.5?{familyName:"Test",givenName:"Name"}:{error:{code:"404",statusMessage:"timeout"}})},2e3)});c.value=!1,ma(f)&&(s.value=I_(f.error,n)),a.value=f};return(f,h)=>(ne(),ae("div",Sb,[Ee(Ko,{ref_key:"screenRef",ref:l,class:"welcome-page",title:Z(n)("welcome_page.title"),loading:c.value},Mn({content:He(()=>{var p,m;return[oe("h1",null,"Welcome Page, let's start "+Me(Z(u)),1),oe("h2",null,"Family Name: "+Me((p=a.value)==null?void 0:p.familyName),1),oe("h2",null,"Given Name: "+Me((m=a.value)==null?void 0:m.givenName),1),Ee(Z(Bt),{onClick:d,title:Z(n)("button.fetch_data")},null,8,["title"]),Ee(Z(Bt),{onClick:h[0]||(h[0]=_=>Z(t).push("/about-page")),title:Z(n)("button.next")},null,8,["title"])]}),_:2},[s.value?{name:"dialog",fn:He(()=>[s.value?(ne(),et(Z(Vo),{key:0,title:Z(n)("error.oops"),description:s.value,onCloseDialog:h[1]||(h[1]=p=>s.value="")},null,8,["title","description"])):pe("",!0)]),key:"0"}:void 0]),1032,["title","loading"])]))}}),Ib={class:"screen-container"},Db=Ge({__name:"index",setup(e){zr();const{t}=_t(),n=Rn(),{WIDGET:r,ENVIRONMENT:i,APPLET_NAME:o}=n,u=_e(!1),c=_e(void 0),l=_e("");return(s,a)=>(ne(),ae("div",Ib,[Ee(Ko,{ref_key:"screenRef",ref:c,class:"about-page",title:Z(t)("about_page.title"),loading:u.value},Mn({content:He(()=>[a[1]||(a[1]=oe("h1",null,"About Page",-1))]),_:2},[l.value?{name:"dialog",fn:He(()=>[l.value?(ne(),et(Z(Vo),{key:0,description:l.value,onCloseDialog:a[0]||(a[0]=d=>l.value="")},null,8,["description"])):pe("",!0)]),key:"0"}:void 0]),1032,["title","loading"])]))}}),Mb={class:"screen-container"},Lb={class:"place-selection"},Rb={class:"search-container"},kb={class:"search-input-wrapper"},Nb=["placeholder"],xb={class:"places-list"},Pb=["onClick"],Fb={class:"place-content"},Bb={class:"place-name"},$b={class:"status-indicator"},Hb={class:"status-text"},jb={key:0,class:"no-results"},Ub=Ge({__name:"index",setup(e){const t=zr(),{t:n}=_t(),r=Rn(),i=_e(!1),o=_e(""),u=_e(void 0),c=_e("");Fo(async()=>{i.value=!0,await l(),i.value=!1});const l=async()=>{i.value=!0,await r.fetchPlaces(),i.value=!1,r.error&&(c.value=r.error)},s=de(()=>{if(!o.value.trim())return r.places;const f=o.value.toLowerCase();return r.places.filter(h=>h.name.toLowerCase().includes(f))});function a(f){var y;if(!((y=f.hours)!=null&&y.general)||f.hours.general.length===0)return!1;const h=new Date,p=(h.getDay()+6)%7,m=h.getHours(),_=h.getMinutes(),g="".concat(m.toString().padStart(2,"0"),":").concat(_.toString().padStart(2,"0")),v=f.hours.general.find(E=>E.day===p);return!v||v.closed||v.ranges.length===0?!1:v.ranges.some(E=>g>=E.open&&g<=E.close)}function d(f){t.push("/service-type/".concat(f.id))}return(f,h)=>(ne(),ae("div",Mb,[Ee(Ko,{ref_key:"screenRef",ref:u,class:"place-list-view",title:Z(n)("place_list.title"),loading:i.value},Mn({content:He(()=>[oe("div",Lb,[oe("div",Rb,[oe("div",kb,[h[4]||(h[4]=oe("svg",{class:"search-icon",xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[oe("circle",{cx:"11",cy:"11",r:"8"}),oe("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"})],-1)),Vs(oe("input",{type:"text","onUpdate:modelValue":h[0]||(h[0]=p=>o.value=p),placeholder:Z(n)("place_list.searchPlaceholder"),class:"search-input"},null,8,Nb),[[Xg,o.value]]),o.value?(ne(),ae("button",{key:0,onClick:h[1]||(h[1]=p=>o.value=""),class:"clear-button","aria-label":"Clear search"},h[3]||(h[3]=[oe("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[oe("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),oe("line",{x1:"6",y1:"6",x2:"18",y2:"18"})],-1)]))):pe("",!0)])]),oe("div",xb,[(ne(!0),ae(Be,null,qr(s.value,p=>(ne(),ae("div",{key:p.id,class:"place-item",onClick:m=>d(p)},[oe("div",Fb,[oe("h3",Bb,Me(p.name),1)]),oe("div",{class:$e(["place-status",{"is-open":a(p)}])},[oe("div",$b,[oe("span",Hb,Me(a(p)?Z(n)("place_list.openNow"):Z(n)("place_list.closed")),1)])],2),h[5]||(h[5]=oe("svg",{class:"chevron-icon",xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[oe("polyline",{points:"9 18 15 12 9 6"})],-1))],8,Pb))),128)),s.value.length===0?(ne(),ae("div",jb,[h[6]||(h[6]=oe("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[oe("circle",{cx:"12",cy:"12",r:"10"}),oe("line",{x1:"8",y1:"12",x2:"16",y2:"12"})],-1)),oe("p",null,Me(Z(n)("place_list.noResults")),1)])):pe("",!0)])])]),_:2},[c.value?{name:"dialog",fn:He(()=>[c.value?(ne(),et(Z(Vo),{key:0,title:Z(n)("error.oops"),description:c.value,onCloseDialog:h[2]||(h[2]=p=>c.value="")},null,8,["title","description"])):pe("",!0)]),key:"0"}:void 0]),1032,["title","loading"])]))}}),Jh={API:{updateHours:{method:"put",path:"places/{placeId}/hours"}},updateHours:(e,t,n,r)=>{const{method:i,path:o}=Jh.API.updateHours;return b_({method:i,endpoint:o.replace("{placeId}",n||""),base:e,cardId:t,body:r})}};var _r=(e=>(e.GENERAL="general",e.DINEIN="dinein",e.PICKUP="pickup",e.DELIVER="deliver",e))(_r||{});const Yb=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];function qb(e){if(!e)return Yb.map(r=>r.slice(0,3));const t=new Date(2024,0,1),n=[];for(let r=0;r<7;r++){const i=new Date(t);i.setDate(t.getDate()+r),n.push(new Intl.DateTimeFormat(e,{weekday:"short"}).format(i))}return n}function Df(e,t){if(e==="24:00"){const o=new Date;return o.setHours(0,0,0,0),new Intl.DateTimeFormat(t||navigator.language,{hour:"numeric",minute:"2-digit",hour12:!0}).format(o)}const[n,r]=e.split(":").map(Number),i=new Date;return i.setHours(n,r,0,0),new Intl.DateTimeFormat(t||navigator.language,{hour:"numeric",minute:r>0?"2-digit":void 0,hour12:!0}).format(i)}function Gb(e,t,n,r){return e.open==="00:00"&&e.close==="00:00"?n||"Closed":e.open==="00:00"&&e.close==="24:00"?r||"Open 24 hours":"".concat(Df(e.open,t),"-").concat(Df(e.close,t))}function Vb(e){const t=[];return e.forEach(n=>{const r=t[t.length-1];r&&n.closed===r.closed&&JSON.stringify(n.ranges)===JSON.stringify(r.ranges)?r.days.push(n.day):t.push({days:[n.day],ranges:n.ranges,closed:n.closed})}),t}function Wb(e,t){const n=qb(t);return e.length===1?n[e[0]]:e.every((i,o)=>o===0||i===e[o-1]+1)?"".concat(n[e[0]],"-").concat(n[e[e.length-1]]):e.map(i=>n[i]).join(", ")}function zb(e,t){return e===5||e===6}function Xb(e){const t=new Date,n=t.getMonth(),r=t.getDate();return n===0&&r===1||n===11&&r===25}function Kb(e,t,n){if(!e||e.length===0)return n||"No hours set";const r=[...e].sort((o,u)=>o.day-u.day);return Vb(r).map(o=>{const u=Wb(o.days,t);if(o.closed||o.ranges.length===0)return"".concat(u,": ").concat(n||"Closed");const c=o.ranges.map(a=>Gb(a,t,n)).join(", "),l=o.days.some(a=>zb(a)),s=o.days.some(Xb);return l?'<span class="special-day">'.concat(u,"</span>: ").concat(c):s?'<span class="holiday">'.concat(u,"</span>: ").concat(c):"".concat(u,": ").concat(c)}).join(" • ")}const Qb={class:"card-content"},Jb={class:"card-left"},Zb={class:"service-icon"},eE=["innerHTML"],tE={class:"service-info"},nE={class:"service-title"},rE={key:0,class:"toggle-container"},iE={class:"toggle-label"},oE=["checked"],sE={class:"hours-summary"},aE={key:0,class:"placeholder"},uE=["innerHTML"],lE={key:0,class:"specific-hours"},cE={class:"specific-hours-title"},fE={class:"specific-hours-list"},dE={class:"specific-date"},hE={class:"specific-time"},mE=Ge({__name:"ServiceCard",props:{serviceType:{},title:{},hours:{},hasCustomHours:{type:Boolean},showToggle:{type:Boolean},specific:{}},emits:["card-tap","toggle-tap"],setup(e,{emit:t}){const{t:n}=_t(),r=e,i=t,o=de(()=>r.hours&&r.hours.length>0),u=_e([]),c=de(()=>{if(!r.specific||!Array.isArray(r.specific)||r.specific.length===0)return u.value=[],!1;const p=new Date;p.setHours(0,0,0,0);const m=r.specific.filter(_=>{if(!_.date||typeof _.date!="object")return!1;try{const{year:g,month:v,day:y}=_.date,E=new Date(g,v-1,y);return E.setHours(0,0,0,0),E>=p}catch(g){return!1}});return u.value=m,m.length>0}),l=p=>{try{const m=new Date(p.year,p.month-1,p.day),_=n("locale")||"en";return new Intl.DateTimeFormat(_,{month:"short",day:"numeric",year:"numeric"}).format(m)}catch(m){return n("error.invalidDate","Invalid date")}},s=p=>{try{if(p.length!==4)return p;const m=parseInt(p.substring(0,2),10),_=parseInt(p.substring(2,4),10),g=new Date;g.setHours(m,_,0,0);const v=n("locale")||"en";return new Intl.DateTimeFormat(v,{hour:"numeric",minute:_>0?"2-digit":void 0,hour12:!0}).format(g)}catch(m){return p}},a=de(()=>{if(!c.value)return[];try{return u.value.map(p=>{const m=l(p.date);let _="";return!p.periods||p.periods.length===0?_=n("service_type.closed","Closed"):_=p.periods.map(v=>v.open.time==="0000"&&v.close.time==="0000"?n("service_type.closed","Closed"):"".concat(s(v.open.time)," - ").concat(s(v.close.time))).join(", "),{date:m,hours:_}})}catch(p){return[]}}),d=p=>{switch(p){case"general":return'\n        <circle cx="12" cy="12" r="10"></circle>\n        <polyline points="12 6 12 12 16 14"></polyline>\n      ';case"dinein":return'\n        <path d="M3 5h1m0 0v14c0 .6.4 1 1 1h2c.6 0 1-.4 1-1V5m0 0h1m0 0h1m0 0h1M9 5h11c.6 0 1 .4 1 1v3c0 1.7-1.3 3-3 3h-5"></path>\n        <path d="M13 12v8c0 .6.4 1 1 1h2c.6 0 1-.4 1-1v-8"></path>\n      ';case"pickup":return'\n        <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>\n        <line x1="3" y1="6" x2="21" y2="6"></line>\n        <path d="M16 10a4 4 0 0 1-8 0"></path>\n      ';case"deliver":return'\n        <rect x="1" y="3" width="15" height="13"></rect>\n        <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>\n        <circle cx="5.5" cy="18.5" r="2.5"></circle>\n        <circle cx="18.5" cy="18.5" r="2.5"></circle>\n      ';default:return""}},f=p=>{i("card-tap",r.serviceType,p)},h=p=>{i("toggle-tap",r.serviceType,p)};return(p,m)=>(ne(),ae("div",{class:$e(["service-card",{"uses-general":!p.hasCustomHours}]),onClick:f},[oe("div",Qb,[oe("div",Jb,[oe("div",Zb,[(ne(),ae("svg",{xmlns:"http://www.w3.org/2000/svg",width:"26",height:"26",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",innerHTML:d(p.serviceType)},null,8,eE))]),oe("div",tE,[oe("h3",nE,Me(p.title),1),p.showToggle?(ne(),ae("div",rE,[oe("span",iE,Me(Z(n)("service_type.differentFromGeneral")),1),oe("label",{class:"toggle-switch",onClick:Jg(h,["stop","prevent"])},[oe("input",{type:"checkbox",checked:p.hasCustomHours},null,8,oE),m[0]||(m[0]=oe("span",{class:"toggle-slider"},null,-1))])])):pe("",!0),oe("div",sE,[o.value?(ne(),ae(Be,{key:1},[oe("div",{class:"hours-display",innerHTML:Z(Kb)(p.hours,Z(n)("locale"),Z(n)("service_type.closed"))},null,8,uE),c.value?(ne(),ae("div",lE,[oe("h4",cE,Me(Z(n)("service_type.specialHours","Special Hours")),1),oe("ul",fE,[(ne(!0),ae(Be,null,qr(a.value,(_,g)=>(ne(),ae("li",{key:g,class:"specific-hours-item"},[oe("span",dE,Me(_.date)+":",1),oe("span",hE,Me(_.hours),1)]))),128))])])):pe("",!0)],64)):(ne(),ae("p",aE,Me(p.serviceType==="general"?Z(n)("service_type.noHours"):Z(n)("service_type.tapToSet")),1))])])])])],2))}}),pE=(e,t)=>{const n=e.__vccOpts||e;for(const[r,i]of t)n[r]=i;return n},Mf=pE(mE,[["__scopeId","data-v-16c81222"]]);function gE(){const{t:e}=_t(),t=_e(!1),n=_e({visible:!1,type:null,data:null,title:"",description:"",cancelText:"",confirmText:""}),r=(s,a=null,d=!1)=>{t.value=d,n.value={visible:!0,type:s,data:a,title:o(s),description:u(s,a),cancelText:c(s),confirmText:l(s)}},i=()=>{n.value.visible=!1,n.value.type=null,n.value.data=null,t.value=!1},o=s=>{switch(s){case"error":return e("error.oops");case"confirmation":return e("service_type.saveChanges");case"createCustom":return e("service_type.createCustomHours");case"removeCustom":return e("service_type.removeCustomHours");case"test":return"Test Dialog";default:return""}},u=(s,a)=>{switch(s){case"error":return a||e("error.unknownError");case"confirmation":return t.value?e("service_type.unsavedChangesMessage"):e("service_type.saveChangesMessage");case"createCustom":return e("service_type.createCustomHoursMessage",{type:a});case"removeCustom":return e("service_type.removeCustomHoursMessage",{type:a});case"test":return"This is a test dialog to check if dialogs are working correctly.";default:return""}},c=s=>{switch(s){case"confirmation":return t.value?e("service_type.discardAndLeave"):e("service_type.discard");case"createCustom":case"removeCustom":case"test":return e("service_type.cancel");default:return""}},l=s=>{switch(s){case"confirmation":return e("service_type.save");case"createCustom":return e("service_type.create");case"removeCustom":return e("service_type.remove");case"test":return"OK";default:return""}};return{dialogState:n,openDialog:r,closeDialog:i}}function _E(e,t){const n=_e(!1),r=_e(null);let i=null;return{isNavigating:n,pendingNavigation:r,setupNavigationGuard:c=>{i=e.beforeEach((l,s,a)=>{s.name==="service-type"&&t.value?(n.value=!0,r.value=()=>a(),c&&c(),a(!1)):a()})},cleanupNavigationGuard:()=>{i&&i()}}}const vE={class:"screen-container"},yE={class:"service-type-selection"},bE={class:"service-cards"},EE={key:0,class:"action-buttons"},AE=["disabled"],wE=Ge({__name:"index",setup(e){const t=zr(),n=Bh(),{t:r}=_t(),i=Rn(),{WIDGET:o,ENVIRONMENT:u}=i,{API:c}=o,{CARD:l}=u,s=_e(!1),a=_e(void 0),d=_e(""),f=_e(null),h=ln(new Map),p=ln(new Map),m=_e(null),_=ln(new Map),g=de(()=>h.size>0?!0:Array.from(p.entries()).some(([O,x])=>{const B=_.get(O);return B!==void 0&&x!==B})),{dialogState:v,openDialog:y,closeDialog:E}=gE(),{isNavigating:L,pendingNavigation:I,setupNavigationGuard:w,cleanupNavigationGuard:D}=_E(t,g),T=de(()=>["general","dinein","pickup","deliver"]),R=de(()=>T.value.filter(O=>{if(O==="general")return!0;if(!f.value)return!1;const x=O in(f.value.hours||{}),B=f.value[O],$=B&&B.available===!0;return console.log("Service type ".concat(O,": hasHoursEntry=").concat(x,", isAvailableInOriginalData=").concat($)),x||$})),k=O=>{var x,B,$,b,A;return!!((B=(x=f.value)==null?void 0:x.hours)!=null&&B[O]&&((A=(b=($=f.value)==null?void 0:$.hours)==null?void 0:b[O])==null?void 0:A.length)>0)},M=()=>{f.value&&(p.clear(),_.clear(),R.value.forEach(O=>{const x=k(O);p.set(O,x),_.set(O,x)}))},H=O=>{const x=[];return O.forEach(B=>{B.closed||B.ranges.forEach($=>{x.push({open:{day:B.day+1,time:$.open},close:{day:B.day+1,time:$.close}})})}),x},N=(O,x)=>{var $,b;if(x&&x.target){const A=x.target;if(A.classList.contains("toggle-switch")||A.classList.contains("toggle-slider")||(($=A.parentElement)==null?void 0:$.classList.contains("toggle-switch"))||((b=A.parentElement)==null?void 0:b.classList.contains("toggle-container")))return}if(O==="general"){ie("general");return}p.get(O)||!1?ie(O):(m.value=O,y("createCustom",O))},G=(O,x)=>{if(x&&(x.stopPropagation(),x.preventDefault(),x.stopImmediatePropagation()),O==="general")return!1;const B=p.get(O)||!1;return m.value=O,y(B?"removeCustom":"createCustom",O),!1},Y=()=>{if(!(!m.value||!f.value)&&f.value.hours&&f.value.hours.general){const O=f.value.hours.general,x=H(O);h.set(m.value,{periods:x}),p.set(m.value,!0);const B=m.value;E(),setTimeout(()=>{ie(B)},100)}},V=()=>{m.value&&(h.set(m.value,{periods:[]}),f.value&&f.value.hours&&(f.value.hours[m.value]=[]),p.set(m.value,!1))},W=async()=>{s.value=!0;try{if(i.places.length===0&&(await i.fetchPlaces(),i.error)){d.value=i.error;return}const O=n.params.placeId;f.value=i.places.find(x=>x.id===O)||null,f.value||(d.value=r("error.placeNotFound"),y("error"))}catch(O){Q(O,"error.loadingPlaces")}finally{s.value=!1}},J=O=>{var x,B;if(h.has(O)){const $=h.get(O);if($&&$.periods)return i.convertPeriodsToDayHours($.periods)}return((B=(x=f.value)==null?void 0:x.hours)==null?void 0:B[O])||[]},te=O=>{var B,$;let x;if(h.has(O)){const b=h.get(O);x=b==null?void 0:b.specific}else($=(B=f.value)==null?void 0:B.hours)!=null&&$[O]&&(x=f.value.hours[O].specific);if(x){if(Array.isArray(x)){const b=new Date;b.setHours(0,0,0,0);const A=x.filter(P=>{if(!P.date||typeof P.date!="object")return!1;try{const{year:j,month:ee,day:z}=P.date,F=new Date(j,ee-1,z);return F.setHours(0,0,0,0),F>=b}catch(j){return!1}}).map(P=>{const j={...P};return(!j.periods||j.periods.length===0)&&(j.periods=[{open:{day:0,time:"0000"},close:{day:0,time:"0000"}}]),j});return A.length>0?A:void 0}return x}},ie=async O=>{if(f.value){if(O!=="general"&&!p.get(O)){m.value=O,y("createCustom",O);return}try{s.value=!0;const x=ue(O),B=te(O),$={periods:x};B&&($.specific=B);const b=await w_($);b&&typeof b=="object"&&"periods"in b&&(h.set(O,b),O!=="general"&&p.set(O,!0))}catch(x){Q(x,"error.editingHours")}finally{s.value=!1}}},ue=O=>{var x,B,$;if(h.has(O)&&((x=h.get(O))!=null&&x.periods))return h.get(O).periods;{const b=me(O),A=(($=(B=f.value)==null?void 0:B.hours)==null?void 0:$[b])||[];return H(A)}},me=O=>{switch(O){case"general":case _r.GENERAL:return"general";case"dinein":case _r.DINEIN:return"dinein";case"pickup":case _r.PICKUP:return"pickup";case"deliver":case _r.DELIVER:return"deliver";default:return"general"}},ve=()=>{if(h.clear(),f.value&&M(),L.value&&I.value){const O=I.value;L.value=!1,I.value=null,O()}else t.go(-1)},Te=async()=>{const O=L.value&&I.value,x=I.value;L.value=!1,I.value=null;const B=await Ae();O&&x&&B&&x()},Ae=async()=>{var O,x;if(!f.value||h.size===0)return!1;try{U(),s.value=!0;const{requestBody:B,pendingChangesCopy:$}=K(),b=((x=(O=f.value.external)==null?void 0:O.crm)==null?void 0:x.storeId)||f.value.id.trim(),A=await Jh.updateHours(c.baseUrl,l.id,b,B);if("error"in A){const P=A.error.message||r("error.savingHours");throw new Error(P)}return X($),h.clear(),M(),!0}catch(B){return Q(B,"error.savingHours"),!1}finally{s.value=!1}},U=()=>{var O;if(!c)throw new Error(r("error.initializingAPI"));if(!((O=f.value)!=null&&O.id)||f.value.id.trim()==="")throw new Error(r("error.invalidPlaceId"))},K=()=>{const O={},x=new Map;return h.forEach((B,$)=>{x.set($,JSON.parse(JSON.stringify(B)));const b=B.periods.length===0?[]:B.periods,A=B.specific||[];switch($){case"general":O.openingHours={periods:b,specific:A};break;case"dinein":O.dinein={periods:b,specific:A};break;case"pickup":O.pickup={periods:b,specific:A};break;case"deliver":O.deliver={periods:b,specific:A};break}}),{requestBody:O,pendingChangesCopy:x}},X=O=>{f.value&&(f.value.hours||(f.value.hours={}),O.forEach((x,B)=>{if(x.periods.length===0)f.value&&f.value.hours&&(f.value.hours[B]=[]);else{const $=i.convertPeriodsToDayHours(x.periods);f.value&&f.value.hours&&(f.value.hours[B]=$,x.specific&&(f.value.hours[B].specific=x.specific))}}))},Q=(O,x)=>{const B=O instanceof Error?O.message:r(x);console.error("Error: ".concat(B),O),d.value=B,y("error")},fe=O=>{switch(O){case"cancel":S();break;case"confirm":C();break;default:E()}},S=()=>{switch(v.value.type){case"confirmation":ve();break}E()},C=()=>{switch(v.value.type){case"confirmation":Te();break;case"createCustom":Y();break;case"removeCustom":V();break}E()};return Fo(async()=>{s.value=!0;try{await W(),M()}catch(O){Q(O,"error.initialization")}finally{s.value=!1}}),$t(()=>{M(),w(()=>y("confirmation"))}),tr(()=>{D()}),(O,x)=>{var B;return ne(),ae("div",vE,[Ee(Ko,{ref_key:"screenRef",ref:a,class:"service-type-view",title:((B=f.value)==null?void 0:B.name)||Z(r)("service_type.title"),loading:s.value},Mn({content:He(()=>[oe("div",yE,[oe("div",bE,[Ee(Mf,{"service-type":"general",title:Z(r)("service_type.general"),hours:J("general"),specific:te("general"),"has-custom-hours":!0,"show-toggle":!1,onCardTap:N},null,8,["title","hours","specific"]),(ne(!0),ae(Be,null,qr(R.value.filter($=>$!=="general"),$=>(ne(),et(Mf,{key:$,"service-type":$,title:Z(r)("service_type.".concat($)),hours:J($),specific:te($),"has-custom-hours":p.get($)||!1,"show-toggle":!0,onCardTap:N,onToggleTap:G},null,8,["service-type","title","hours","specific","has-custom-hours"]))),128))]),g.value?(ne(),ae("div",EE,[oe("button",{class:"save-button",onClick:Ae,disabled:s.value},Me(s.value?Z(r)("service_type.saving"):Z(r)("service_type.save")),9,AE),oe("button",{class:"cancel-button",onClick:ve},Me(Z(r)("service_type.cancel")),1)])):pe("",!0)])]),_:2},[Z(v).visible?{name:"dialog",fn:He(()=>[Ee(Z(Vo),{title:Z(v).title,description:Z(v).description,onCloseDialog:Z(E),showCancel:Z(v).type!=="error",style:{"z-index":"9999",position:"relative"}},{buttons:He(()=>[Z(v).type!=="error"?(ne(),et(Z(Bt),{key:0,type:"clear",class:"cancel-button",onClick:x[0]||(x[0]=$=>fe("cancel")),title:Z(v).cancelText},null,8,["title"])):pe("",!0),Ee(Z(Bt),{type:"clear",class:"confirm-button",onClick:x[1]||(x[1]=$=>fe("confirm")),title:Z(v).confirmText},null,8,["title"])]),_:1},8,["title","description","onCloseDialog","showCancel"])]),key:"0"}:void 0]),1032,["title","loading"])])}}}),Zh=Hv({history:gv("./"),routes:[{path:"/",name:"root",redirect:"/place-list"},{path:"/welcome-page",name:"welcome-page",component:Cb},{path:"/about-page",name:"about-page",component:Db},{path:"/place-list",name:"place-list",component:Ub},{path:"/service-type/:placeId",name:"service-type",component:wE,props:!0}]});Zh.beforeEach((e,t)=>{const n=!t.name,i=window.history.state.forward===t.fullPath?"swipe-left":"swipe-right";e.meta.transition=n?"":i});const OE={locale:"en",button:{next:"Next",fetch_data:"Simulate Fetch Data"},welcome_page:{title:"Welcome Page"},about_page:{title:"About Page"},place_list:{title:"Select a Place",searchPlaceholder:"Search places...",openNow:"Open",closed:"Closed",noResults:"No places found"},service_type:{title:"Service Hours",general:"General",dinein:"Dine-In",pickup:"Pickup",deliver:"Delivery",edit:"Edit",save:"Save",saving:"Saving...",cancel:"Cancel",usesGeneral:"Uses General",noHours:"No hours set",tapToSet:"Tap to set specific hours",saveChanges:"Save Changes?",saveChangesMessage:"You have unsaved changes. Would you like to save them before leaving?",unsavedChangesMessage:"You have unsaved changes. Would you like to save them before navigating away?",discard:"Discard",discardAndLeave:"Discard & Leave",createCustomHours:"Create Custom Hours",createCustomHoursMessage:"Do you want to create custom hours for {type}? This will start with a copy of the general hours.",removeCustomHours:"Remove Custom Hours",removeCustomHoursMessage:"Do you want to remove custom hours for {type}? This will revert to using general hours.",create:"Create",remove:"Remove",differentFromGeneral:"Different from General?"},error:{oops:"Oops!",placeNotFound:"Place not found",editingHours:"Error editing hours",savingHours:"Error saving hours",initializingAPI:"Error initializing API",invalidPlaceId:"Invalid place ID",invalidDate:"Invalid date"}},TE={en:OE,"zh-Hans":{locale:"zh-Hans",button:{next:"下一步",fetch_data:"模拟获取数据"},welcome_page:{title:"欢迎页面"},about_page:{title:"关于页面"},place_list:{title:"选择地点",searchPlaceholder:"搜索地点...",openNow:"营业",closed:"已关闭",noResults:"未找到地点"},service_type:{title:"服务时间",general:"一般营业时间",dinein:"堂食",pickup:"自取",deliver:"外送",edit:"编辑",save:"保存",saving:"保存中...",cancel:"取消",usesGeneral:"使用一般时间",noHours:"未设置时间",tapToSet:"点击设置特定时间",saveChanges:"保存更改？",saveChangesMessage:"您有未保存的更改。离开前是否要保存？",unsavedChangesMessage:"您有未保存的更改。在离开页面前是否要保存？",discard:"放弃",discardAndLeave:"放弃并离开",createCustomHours:"创建自定义时间",createCustomHoursMessage:"您想为{type}创建自定义时间吗？这将从一般营业时间的副本开始。",removeCustomHours:"移除自定义时间",removeCustomHoursMessage:"您想移除{type}的自定义时间吗？这将恢复使用一般营业时间。",create:"创建",remove:"移除",differentFromGeneral:"与一般营业时间不同?"},error:{oops:"出错了！",placeNotFound:"未找到地点",editingHours:"编辑时间时出错",savingHours:"保存时间时出错",initializingAPI:"初始化API时出错",invalidPlaceId:"无效的地点ID",invalidDate:"无效日期"}},"zh-Hant":{locale:"zh-Hant",button:{next:"下一步",fetch_data:"模擬獲取數據"},welcome_page:{title:"歡迎頁面"},about_page:{title:"關於頁面"},place_list:{title:"選擇地點",searchPlaceholder:"搜索地點...",openNow:"營業",closed:"已關閉",noResults:"未找到地點"},service_type:{title:"服務時間",general:"一般營業時間",dinein:"堂食",pickup:"自取",deliver:"外送",edit:"編輯",save:"保存",saving:"保存中...",cancel:"取消",usesGeneral:"使用一般時間",noHours:"未設置時間",tapToSet:"點擊設置特定時間",saveChanges:"保存更改？",saveChangesMessage:"您有未保存的更改。離開前是否要保存？",unsavedChangesMessage:"您有未保存的更改。在離開頁面前是否要保存？",discard:"放棄",discardAndLeave:"放棄並離開",createCustomHours:"創建自定義時間",createCustomHoursMessage:"您想為{type}創建自定義時間嗎？這將從一般營業時間的副本開始。",removeCustomHours:"移除自定義時間",removeCustomHoursMessage:"您想移除{type}的自定義時間嗎？這將恢復使用一般營業時間。",create:"創建",remove:"移除",differentFromGeneral:"與一般營業時間不同?"},error:{oops:"出錯了！",placeNotFound:"未找到地點",editingHours:"編輯時間時出錯",savingHours:"保存時間時出錯",initializingAPI:"初始化API時出錯",invalidPlaceId:"無效的地點ID",invalidDate:"無效日期"}}};async function SE(){const e=t0(Uv),t=i0();e.use(t);const n=Rn();await n.fetchData();const{ENVIRONMENT:r,APPLET_NAME:i}=n,o=wh({locale:D_(r.LANGUAGE),fallbackLocale:pt.LANGUAGES.DEFAULT,messages:B_(TE),legacy:!1});e.use(o),e.use(Zh),P_(r,i),F_(r,vr.LOCALE_LANGUAGE,vr.formatDateTime),e.mount("#app"),n.app=e}SE();export{IE as __vite_legacy_guard};
