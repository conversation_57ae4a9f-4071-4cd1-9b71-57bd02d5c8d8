<!DOCTYPE html>
<html>
	<head>
	<meta charset="UTF-8">
	<meta name="viewport" id="viewport"
		content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
	<title>manage-hours</title>
		<script type="module" crossorigin src="./assets/index-DtpBUfMO.js"></script>
		<link rel="stylesheet" crossorigin href="./assets/index-Bi7Ex3MR.css">
		<script type="module">import.meta.url;import("_").catch(()=>1);(async function*(){})().next();if(location.protocol!="file:"){window.__vite_is_modern_browser=true}</script>
		<script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
	</head>
	<body>
		<div id="app"></div>
		<script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
		<script nomodule crossorigin id="vite-legacy-polyfill" src="./assets/polyfills-legacy-CO6HmLTU.js"></script>
		<script nomodule crossorigin id="vite-legacy-entry" data-src="./assets/index-legacy-Czc3XJyJ.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
	</body>
</html>
