import type { DayHours, TimeRange } from '../types/index.ts';

// Define types for the place and hours data structure
interface Place {
  id: string;
  name: string;
  hours?: Record<string, any>;
}

// Internationalized day names and abbreviations
// These will be replaced by proper i18n functions when called
const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
const DAY_ABBREVIATIONS: Record<string, string> = {
  monday: 'Mon',
  tuesday: 'Tue',
  wednesday: 'Wed',
  thursday: 'Thu',
  friday: 'Fri',
  saturday: 'Sat',
  sunday: 'Sun'
};

// Helper function to get localized day names
function getLocalizedDayNames(locale?: string): string[] {
  if (!locale) {
    return DAYS;
  }

  // Create a date for each day of the week (starting from Monday)
  const baseDate = new Date(2024, 0, 1); // January 1, 2024 was a Monday
  const dayNames: string[] = [];

  for (let i = 0; i < 7; i++) {
    const date = new Date(baseDate);
    date.setDate(baseDate.getDate() + i);
    dayNames.push(new Intl.DateTimeFormat(locale, { weekday: 'long' }).format(date));
  }

  return dayNames;
}

// Helper function to get localized day abbreviations
function getLocalizedDayAbbreviations(locale?: string): string[] {
  if (!locale) {
    return DAYS.map(day => day.slice(0, 3));
  }

  // Create a date for each day of the week (starting from Monday)
  const baseDate = new Date(2024, 0, 1); // January 1, 2024 was a Monday
  const dayAbbreviations: string[] = [];

  for (let i = 0; i < 7; i++) {
    const date = new Date(baseDate);
    date.setDate(baseDate.getDate() + i);
    dayAbbreviations.push(new Intl.DateTimeFormat(locale, { weekday: 'short' }).format(date));
  }

  return dayAbbreviations;
}

// Format time from 24-hour format to 12-hour format with internationalization support
export function formatTime(time: string, locale?: string): string {
  // Special case for 24:00 which should be displayed as 12am (end of day)
  if (time === '24:00') {
    // Use Intl.DateTimeFormat for proper localization
    const date = new Date();
    date.setHours(0, 0, 0, 0); // Set to midnight
    return new Intl.DateTimeFormat(locale || navigator.language, {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }).format(date);
  }

  const [hours, minutes] = time.split(':').map(Number);

  // Create a date object for proper time formatting
  const date = new Date();
  date.setHours(hours, minutes, 0, 0);

  // Use Intl.DateTimeFormat for proper localization
  return new Intl.DateTimeFormat(locale || navigator.language, {
    hour: 'numeric',
    minute: minutes > 0 ? '2-digit' : undefined,
    hour12: true
  }).format(date);
}

// Format a single time range with internationalization support
export function formatTimeRange(range: TimeRange, locale?: string, closedText?: string, open24HoursText?: string): string {
  // Special case for 00:00-00:00 which means closed
  if (range.open === '00:00' && range.close === '00:00') {
    return closedText || 'Closed';
  }

  // Special case for 24-hour periods (00:00-24:00)
  if (range.open === '00:00' && range.close === '24:00') {
    return open24HoursText || 'Open 24 hours';
  }

  return `${formatTime(range.open, locale)}-${formatTime(range.close, locale)}`;
}

// Group consecutive days with the same hours
function groupDaysByHours(hours: DayHours[]): { days: number[], ranges: TimeRange[], closed: boolean }[] {
  const groups: { days: number[], ranges: TimeRange[], closed: boolean }[] = [];
  
  hours.forEach((dayHour) => {
    const lastGroup = groups[groups.length - 1];
    
    // Check if this day has the same hours as the last group
    const sameAsLastGroup = lastGroup && 
      dayHour.closed === lastGroup.closed &&
      JSON.stringify(dayHour.ranges) === JSON.stringify(lastGroup.ranges);
    
    if (sameAsLastGroup) {
      lastGroup.days.push(dayHour.day);
    } else {
      groups.push({
        days: [dayHour.day],
        ranges: dayHour.ranges,
        closed: dayHour.closed
      });
    }
  });
  
  return groups;
}

// Format day ranges (e.g., "Mon-Wed" or "Mon, Wed, Fri") with internationalization support
function formatDayRange(days: number[], locale?: string): string {
  const dayAbbreviations = getLocalizedDayAbbreviations(locale);

  if (days.length === 1) {
    return dayAbbreviations[days[0]];
  }

  // Check if days are consecutive
  const isConsecutive = days.every((day, index) =>
    index === 0 || day === days[index - 1] + 1
  );

  if (isConsecutive) {
    return `${dayAbbreviations[days[0]]}-${dayAbbreviations[days[days.length - 1]]}`;
  } else {
    return days.map(day => dayAbbreviations[day]).join(', ');
  }
}

// Check if a day is a special day (e.g., different from regular hours)
function isSpecialDay(day: number, hours: DayHours[]): boolean {
  // For this example, we'll consider Saturday and Sunday as special days
  return day === 5 || day === 6; // Saturday = 5, Sunday = 6
}

// Check if a day is a holiday
function isHoliday(day: number): boolean {
  // For this example, we'll consider specific dates as holidays
  // In a real app, this would check against a holiday database
  const today = new Date();
  const month = today.getMonth();
  const date = today.getDate();
  
  // Example holidays (simplified)
  // January 1 (New Year's Day)
  if (month === 0 && date === 1) return true;
  // December 25 (Christmas)
  if (month === 11 && date === 25) return true;
  
  return false;
}

// Format hours for display in the summary (array-based version) with internationalization support
export function formatHoursSummary(hours: DayHours[], locale?: string, closedText?: string): string {
  if (!hours || hours.length === 0) {
    return closedText || 'No hours set';
  }

  // Sort hours by day
  const sortedHours = [...hours].sort((a, b) => a.day - b.day);

  // Group days with the same hours
  const groups = groupDaysByHours(sortedHours);

  // Format each group
  return groups.map(group => {
    const dayRange = formatDayRange(group.days, locale);

    if (group.closed) {
      return `${dayRange}: ${closedText || 'Closed'}`;
    }

    if (group.ranges.length === 0) {
      return `${dayRange}: ${closedText || 'Closed'}`;
    }

    const timeRanges = group.ranges.map(range => formatTimeRange(range, locale, closedText)).join(', ');

    // Check if any day in the group is special or a holiday
    const hasSpecialDay = group.days.some(day => isSpecialDay(day, hours));
    const hasHoliday = group.days.some(isHoliday);

    // Add special day or holiday class
    if (hasSpecialDay) {
      return `<span class="special-day">${dayRange}</span>: ${timeRanges}`;
    } else if (hasHoliday) {
      return `<span class="holiday">${dayRange}</span>: ${timeRanges}`;
    } else {
      return `${dayRange}: ${timeRanges}`;
    }
  }).join(' • ');
}

// Format hours for display in the summary (object-based version)
export function formatHoursSummaryFromObject(hours: Record<string, any>): string | null {
  if (!hours || Object.keys(hours).length === 0) {
    return null;
  }
  
  // Group days with the same hours
  const hoursBySchedule: Record<string, string[]> = {};
  
  Object.entries(hours).forEach(([day, dayHours]: [string, any]) => {
    if (!dayHours.isOpen) {
      return; // Skip closed days
    }
    
    const key = dayHours.ranges.map((range: any) => 
      `${range.from}-${range.to}`
    ).join(',');
    
    if (!hoursBySchedule[key]) {
      hoursBySchedule[key] = [];
    }
    
    hoursBySchedule[key].push(DAY_ABBREVIATIONS[day]);
  });
  
  // Format the grouped days
  return Object.entries(hoursBySchedule).map(([schedule, daysArray]) => {
    // Find consecutive days and format them as ranges
    let formattedDays = '';
    let start: string | null = null;
    let prev: string | null = null;
    
    daysArray.sort((a, b) => 
      Object.values(DAY_ABBREVIATIONS).indexOf(a) - Object.values(DAY_ABBREVIATIONS).indexOf(b)
    ).forEach((day, i) => {
      const dayIndex = Object.values(DAY_ABBREVIATIONS).indexOf(day);
      
      if (prev === null || dayIndex !== Object.values(DAY_ABBREVIATIONS).indexOf(prev) + 1) {
        if (start !== null) {
          formattedDays += formattedDays ? ', ' : '';
          formattedDays += start === prev ? start : `${start}-${prev}`;
        }
        start = day;
      }
      
      prev = day;
      
      if (i === daysArray.length - 1) {
        formattedDays += formattedDays ? ', ' : '';
        formattedDays += start === prev ? start : `${start}-${prev}`;
      }
    });
    
    // Format the time ranges
    const timeRanges = schedule.split(',').map(range => {
      const [from, to] = range.split('-');
      return `${formatTime(from)}-${formatTime(to)}`;
    }).join(', ');
    
    return `${formattedDays}: ${timeRanges}`;
  }).join('; ');
}

// Check if a service is using General hours
export function isUsingGeneralHours(place: Place, serviceTypeId: string): boolean {
  if (serviceTypeId === 'general') return false;
  
  const serviceHours = place.hours?.[serviceTypeId];
  return !serviceHours || Object.keys(serviceHours).length === 0;
}

// Prepare hours data for the native component
export function prepareHoursData(place: Place, serviceTypeId: string): {
  placeId: string;
  placeName: string;
  serviceType: string;
  currentHours: Record<string, any>;
  generalHours: Record<string, any>;
} {
  return {
    placeId: place.id,
    placeName: place.name,
    serviceType: serviceTypeId,
    currentHours: place.hours?.[serviceTypeId] || {},
    generalHours: place.hours?.general || {}
  };
}
