<script setup lang="ts">
import { onBeforeMount, ref, computed, reactive, watch, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter, useRoute } from 'vue-router'
import { Icon, Dialog, But<PERSON> } from '@perkd/vue-components'
import Screen from '@/components/UIScreen.vue'
import { useDataStore } from '@/stores/data'
import { formatHoursSummary } from '@/utils/hoursUtils'
import { openHoursSettings as openHoursSettingsAction } from '@perkd/applet-common/actions'
import { Place } from '@perkd/applet-common/api-request'
import type { Place as PlaceType, ServiceTypeKey, DayHours, TimeRange } from '@/types/index'
import { ServiceType } from '@/types/index'
import ServiceCard from './components/ServiceCard.vue'
import { useDialogState } from './composables/useDialogState'
import type { DialogType } from './composables/useDialogState'
import { useNavigationGuard } from './composables/useNavigationGuard'

// Types
interface Period {
  open: { day: number; time: string };
  close: { day: number; time: string };
}

interface HoursObject {
  periods: Period[];
  specific?: any;
}

// Setup
const router = useRouter()
const route = useRoute()
const { t } = useI18n()

const $ = useDataStore()
const { WIDGET, ENVIRONMENT } = $
const { API } = WIDGET
const { CARD } = ENVIRONMENT
const loading = ref(false)

const screenRef = ref<InstanceType<typeof Screen> | undefined>(undefined)
const errorMessage = ref('')
const selectedPlace = ref<PlaceType | null>(null)
const pendingChangesMap = reactive(new Map<ServiceTypeKey, HoursObject>())
const customHoursToggle = reactive(new Map<ServiceTypeKey, boolean>())
const currentServiceType = ref<ServiceTypeKey | null>(null)

// Store the initial toggle states to compare against
const initialToggleStates = reactive(new Map<ServiceTypeKey, boolean>())

// Computed properties
const hasPendingChanges = computed(() => {
  // Check if there are any pending changes in the map
  if (pendingChangesMap.size > 0) {
    return true;
  }

  // Check if any toggle states have changed from their initial values
  return Array.from(customHoursToggle.entries()).some(([key, value]) => {
    const initialValue = initialToggleStates.get(key);
    // Only consider it a change if we have an initial value and it's different
    return initialValue !== undefined && value !== initialValue;
  });
})

// Use composables
const { dialogState, openDialog, closeDialog } = useDialogState()
const { isNavigating, pendingNavigation, setupNavigationGuard, cleanupNavigationGuard } = 
  useNavigationGuard(router, hasPendingChanges)

const serviceTypes = computed<ServiceTypeKey[]>(() => ['general', 'dinein', 'pickup', 'deliver'])

// Add a new computed property to filter available service types
const availableServiceTypes = computed<ServiceTypeKey[]>(() => {
  return serviceTypes.value.filter(type => {
    // General hours are always available
    if (type === 'general') return true;
    
    // If no place is selected, only show general hours
    if (!selectedPlace.value) return false;
    
    // For service types other than general, check if:
    // 1. They have hours in the hours object (even empty array), OR
    // 2. They exist in the original place data with available=true
    
    // Check if the service type has an entry in the hours object
    const hasHoursEntry = type in (selectedPlace.value.hours || {});
    
    // Check if the service type is available in the original place data
    const serviceTypeData = selectedPlace.value[type as keyof typeof selectedPlace.value] as any;
    const isAvailableInOriginalData = serviceTypeData && serviceTypeData.available === true;
    
    // Debug logging to help troubleshoot
    console.log(`Service type ${type}: hasHoursEntry=${hasHoursEntry}, isAvailableInOriginalData=${isAvailableInOriginalData}`);
    
    return hasHoursEntry || isAvailableInOriginalData;
  });
});

// Helper functions
const hasServiceTypeHours = (serviceType: ServiceTypeKey): boolean => {
  return !!(selectedPlace.value?.hours?.[serviceType] && 
           selectedPlace.value?.hours?.[serviceType]?.length > 0);
}

// Initialize toggle states based on whether service types have custom hours
const initializeToggleStates = (): void => {
  if (!selectedPlace.value) return;

  // Clear existing states
  customHoursToggle.clear();
  initialToggleStates.clear();

  // Only initialize toggle states for available service types
  availableServiceTypes.value.forEach(type => {
    const hasCustomHours = hasServiceTypeHours(type);
    customHoursToggle.set(type, hasCustomHours);
    // Store the initial state for comparison
    initialToggleStates.set(type, hasCustomHours);
  });
}

// Helper function to convert DayHours to periods format
const convertDayHoursToPeriods = (dayHours: DayHours[]): Period[] => {
  const periods: Period[] = [];
  
  dayHours.forEach((dayHour: DayHours) => {
    if (!dayHour.closed) {
      dayHour.ranges.forEach((range: TimeRange) => {
        periods.push({
          open: {
            day: dayHour.day + 1, // Convert 0-based to 1-based day
            time: range.open
          },
          close: {
            day: dayHour.day + 1, // Convert 0-based to 1-based day
            time: range.close
          }
        });
      });
    }
  });
  
  return periods;
}

// Service card event handlers
const handleServiceCardTap = (serviceType: ServiceTypeKey, event?: Event): void => {  
  // Check if the click originated from a toggle element
  if (event && event.target) {
    const target = event.target as HTMLElement;
    const isToggleElement = 
      target.classList.contains('toggle-switch') || 
      target.classList.contains('toggle-slider') ||
      target.parentElement?.classList.contains('toggle-switch') ||
      target.parentElement?.classList.contains('toggle-container');
    
    if (isToggleElement) {
      return;
    }
  }
  
  if (serviceType === 'general') {
    // General hours always open settings directly
    openHoursSettings('general');
    return;
  }
  
  const hasCustomHours = customHoursToggle.get(serviceType) || false;
  
  if (hasCustomHours) {
    // If already has custom hours, open settings directly
    openHoursSettings(serviceType);
  } else {
    // If using general hours, show confirmation dialog
    currentServiceType.value = serviceType;
    openDialog('createCustom', serviceType);
  }
}

const handleToggleTap = (serviceType: ServiceTypeKey, event: Event): boolean => {
  // Prevent event propagation to avoid triggering card tap
  if (event) {
    event.stopPropagation();
    event.preventDefault();
    event.stopImmediatePropagation();
  }
  
  if (serviceType === 'general') {
    return false; // Can't toggle general hours
  }
  
  const hasCustomHours = customHoursToggle.get(serviceType) || false;
  
  // Set current service type for dialog
  currentServiceType.value = serviceType;
  
  if (hasCustomHours) {
    // If turning OFF, show confirmation
    openDialog('removeCustom', serviceType);
  } else {
    // If turning ON, show confirmation
    openDialog('createCustom', serviceType);
  }
  
  // Return false to prevent default behavior and bubbling
  return false;
}

// Custom hours management
const confirmCreateCustomHours = (): void => {
  if (!currentServiceType.value || !selectedPlace.value) return;
  
  // Create custom hours by copying general hours
  if (selectedPlace.value.hours && selectedPlace.value.hours.general) {
    // Copy general hours to this service type
    const generalHours = selectedPlace.value.hours.general;
    const periods = convertDayHoursToPeriods(generalHours);
    
    // Store in pending changes
    pendingChangesMap.set(currentServiceType.value, { periods });
    
    // Update toggle state
    customHoursToggle.set(currentServiceType.value, true);
    // Don't update initial state here - we want to track this as a change
    
    // Store the service type in a local variable before closing dialog
    const serviceType = currentServiceType.value;
    
    // Close dialog first to prevent UI issues
    closeDialog();
    
    // Open hours settings with a slight delay to ensure dialog is closed
    setTimeout(() => {
      openHoursSettings(serviceType);
    }, 100);
  }
}

const confirmRemoveCustomHours = (): void => {
  if (!currentServiceType.value) return;
  
  // Add an entry with empty periods to ensure hasPendingChanges detects the change
  pendingChangesMap.set(currentServiceType.value, { periods: [] });
  
  // Clear existing hours
  if (selectedPlace.value && selectedPlace.value.hours) {
    // Instead of deleting the property completely, set it to an empty array
    // This keeps the service type in the availableServiceTypes list
    selectedPlace.value.hours[currentServiceType.value] = [];
  }
  
  // Update toggle state
  customHoursToggle.set(currentServiceType.value, false);
  // Don't update initial state here - we want to track this as a change
}

// Data loading
const loadPlaces = async (): Promise<void> => {
  loading.value = true;
  try {
    // Check if places data already exists in the store
    if ($.places.length === 0) {
      // Only fetch places if the store doesn't have any
      await $.fetchPlaces();
      
      if ($.error) {
        errorMessage.value = $.error;
        return;
      }
    }
    
    // Find the selected place by ID from the route params
    const placeId = route.params.placeId as string;
    selectedPlace.value = $.places.find(p => p.id === placeId) || null;
    
    if (!selectedPlace.value) {
      errorMessage.value = t('error.placeNotFound');
      openDialog('error');
    }
  } catch (error) {
    handleError(error, 'error.loadingPlaces');
  } finally {
    loading.value = false;
  }
}

// Helper function to get the hours for a service type, considering pending changes
const getServiceHours = (serviceType: ServiceTypeKey): DayHours[] => {
  // If there are pending changes for this service type, convert them to DayHours format
  if (pendingChangesMap.has(serviceType)) {
    const pendingHours = pendingChangesMap.get(serviceType);
    if (pendingHours && pendingHours.periods) {
      return $.convertPeriodsToDayHours(pendingHours.periods);
    }
  }
  
  // Otherwise, return the original hours
  return selectedPlace.value?.hours?.[serviceType] || [];
}

// New function to get specific data for a service type
const getSpecificData = (serviceType: ServiceTypeKey): any => {
  let specificData;
  
  // If there are pending changes for this service type, return the specific data
  if (pendingChangesMap.has(serviceType)) {
    const pendingHours = pendingChangesMap.get(serviceType);
    specificData = pendingHours?.specific;
  } 
  // Otherwise, check if the place has hours data for this service type
  else if (selectedPlace.value?.hours?.[serviceType]) {
    // Access the specific property from the hours array
    specificData = (selectedPlace.value.hours[serviceType] as any).specific;
  }
  
  // If no specific data found
  if (!specificData) {
    return undefined;
  }
  
  // Filter out past dates and normalize periods
  if (Array.isArray(specificData)) {
    // Get current date (without time)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const filteredSpecificData = specificData.filter(item => {
      if (!item.date || typeof item.date !== 'object') return false;
      
      try {
        const { year, month, day } = item.date;
        // Create date object (month is 0-indexed in JS Date)
        const specificDate = new Date(year, month - 1, day);
        specificDate.setHours(0, 0, 0, 0);
        
        // Keep only dates that are today or in the future
        return specificDate >= today;
      } catch (e) {
        return false;
      }
    }).map(item => {
      // Create a copy of the item to avoid modifying the original
      const itemCopy = { ...item };
      
      // If periods is empty, add a closed period (0000-0000)
      if (!itemCopy.periods || itemCopy.periods.length === 0) {
        itemCopy.periods = [{
          open: { day: 0, time: '0000' },
          close: { day: 0, time: '0000' }
        }];
      }
      
      return itemCopy;
    });
    
    return filteredSpecificData.length > 0 ? filteredSpecificData : undefined;
  }
  
  return specificData;
}

// Hours settings management
const openHoursSettings = async (serviceType: ServiceTypeKey): Promise<void> => {
  if (!selectedPlace.value) return;
  
  // If this is not the general hours and the toggle is off, don't open hours settings
  // as this service type is using general hours
  if (serviceType !== 'general' && !(customHoursToggle.get(serviceType) || false)) {
    // Show confirmation dialog instead of automatically toggling
    currentServiceType.value = serviceType;
    openDialog('createCustom', serviceType);
    return;
  }
  
  try {
    loading.value = true;
    
    // Get hours from pending changes if available, otherwise from the place
    const periods = getPeriods(serviceType);
    
    // Get specific hours (already filtered for future dates by getSpecificData)
    const specific = getSpecificData(serviceType);
    
    // Create the Hours object with periods and specific hours
    const hoursObject: HoursObject = { periods };
    if (specific) {
      hoursObject.specific = specific;
    }
    
    // Use the imported action function with properly formatted data
    const updatedHours = await openHoursSettingsAction(hoursObject);
    
    // If we received updated hours data, add to pending changes
    if (updatedHours && typeof updatedHours === 'object' && 'periods' in updatedHours) {
      // Store the updated hours in the pending changes map
      pendingChangesMap.set(serviceType, updatedHours as HoursObject);
      
      // If this is a service type other than general, make sure the toggle is on
      if (serviceType !== 'general') {
        customHoursToggle.set(serviceType, true);
      }
    }
  } catch (error) {
    handleError(error, 'error.editingHours');
  } finally {
    loading.value = false;
  }
}

// Helper function to get periods for a service type
const getPeriods = (serviceType: ServiceTypeKey): Period[] => {
  if (pendingChangesMap.has(serviceType) && pendingChangesMap.get(serviceType)?.periods) {
    return pendingChangesMap.get(serviceType)!.periods;
  } else {
    // Map the service type string to the Hours property
    const hoursProperty = mapServiceTypeToProperty(serviceType);
    
    // Convert DayHours[] to Hours format with periods
    const dayHours = selectedPlace.value?.hours?.[hoursProperty] || [];
    return convertDayHoursToPeriods(dayHours);
  }
}

// Helper function to map service type to property
const mapServiceTypeToProperty = (serviceType: ServiceTypeKey): ServiceTypeKey => {
  switch(serviceType) {
    case 'general':
    case ServiceType.GENERAL:
      return 'general';
    case 'dinein':
    case ServiceType.DINEIN:
      return 'dinein';
    case 'pickup':
    case ServiceType.PICKUP:
      return 'pickup';
    case 'deliver':
    case ServiceType.DELIVER:
      return 'deliver';
    default:
      return 'general';
  }
}

// Changes management
const cancelHoursChanges = (): void => {
  // Clear all pending changes
  pendingChangesMap.clear();

  // Reset toggle states to match the actual hours and update initial states
  if (selectedPlace.value) {
    initializeToggleStates();
  }
  
  // If we were in the middle of navigation, continue after clearing changes
  if (isNavigating.value && pendingNavigation.value) {
    // Store the navigation function locally before resetting state
    const navigate = pendingNavigation.value;
    
    // Reset navigation state
    isNavigating.value = false;
    pendingNavigation.value = null;
    
    // Execute the navigation
    navigate();
  } else {
    // This is a direct cancel (not from navigation), go back
    router.go(-1);
  }
}

const saveHoursChanges = async (): Promise<void> => {
  // Store navigation function before async operation
  const shouldNavigate = isNavigating.value && pendingNavigation.value;
  const navigate = pendingNavigation.value;
  
  // Reset navigation state
  isNavigating.value = false;
  pendingNavigation.value = null;
  
  // Save changes
  const saveSuccess = await saveAllChanges();
  
  // If we were in the middle of navigation, continue after saving
  if (shouldNavigate && navigate && saveSuccess) {
    navigate();
  }
}

const saveAllChanges = async (): Promise<boolean> => {
  if (!selectedPlace.value || pendingChangesMap.size === 0) {
    return false;
  }
  
  try {
    validatePrerequisites();
    
    // Show loading indicator
    loading.value = true;
    
    // Prepare the request body and store a copy of pending changes
    const { requestBody, pendingChangesCopy } = prepareRequestData();
    
    // Use external.crm.storeId for the API call, with fallback to id
    const placeId = selectedPlace.value.external?.crm?.storeId || selectedPlace.value.id.trim();
    
    // Use the standard Place.updateHours method
    const response = await Place.updateHours(
      API.baseUrl,
      CARD.id,
      placeId,
      requestBody
    );
    
    if ('error' in response) {
      const errorMsg = response.error.message || t('error.savingHours');
      throw new Error(errorMsg);
    }
    
    // Update local data model with new hours
    updateLocalHours(pendingChangesCopy);
    
    // Now clear pending changes
    pendingChangesMap.clear();
    
    // Reset toggle states to match the actual hours after saving
    initializeToggleStates();
    
    return true;
  } catch (error) {
    handleError(error, 'error.savingHours');
    return false;
  } finally {
    loading.value = false;
  }
}

// Helper functions for saveAllChanges
const validatePrerequisites = (): void => {
  // Check if WIDGET is properly initialized
  if (!API) {
    throw new Error(t('error.initializingAPI'));
  }

  // Validate place ID
  if (!selectedPlace.value?.id || selectedPlace.value.id.trim() === '') {
    throw new Error(t('error.invalidPlaceId'));
  }
}

const prepareRequestData = (): { requestBody: any, pendingChangesCopy: Map<ServiceTypeKey, any> } => {
  const requestBody: any = {};
  const pendingChangesCopy = new Map<ServiceTypeKey, any>();
  
  // Add all pending changes to the request body
  pendingChangesMap.forEach((hours, serviceType) => {
    // Store a deep copy of the hours
    pendingChangesCopy.set(serviceType, JSON.parse(JSON.stringify(hours)));
    
    // If periods is empty, it means we're toggling OFF custom hours
    // We should explicitly set it to null to indicate "use general hours"
    const periodsValue = hours.periods.length === 0 ? [] : hours.periods;
    const specificValue = hours.specific || [];
    
    switch(serviceType) {
      case 'general':
        requestBody.openingHours = {
          periods: periodsValue,
          specific: specificValue
        };
        break;
      case 'dinein':
        requestBody.dinein = {
          periods: periodsValue,
          specific: specificValue
        };
        break;
      case 'pickup':
        requestBody.pickup = {
          periods: periodsValue,
          specific: specificValue
        };
        break;
      case 'deliver':
        requestBody.deliver = {
          periods: periodsValue,
          specific: specificValue
        };
        break;
    }
  });
  
  return { requestBody, pendingChangesCopy };
}

const updateLocalHours = (pendingChangesCopy: Map<ServiceTypeKey, any>): void => {
  if (!selectedPlace.value) return;
  
  // Initialize hours object if it doesn't exist
  if (!selectedPlace.value.hours) {
    selectedPlace.value.hours = {} as Record<ServiceTypeKey, DayHours[]>;
  }
  
  pendingChangesCopy.forEach((hours, serviceType) => {
    // If hours has empty periods, set to empty array instead of removing completely
    // This keeps the service type in the availableServiceTypes list
    if (hours.periods.length === 0) {
      if (selectedPlace.value && selectedPlace.value.hours) {
        selectedPlace.value.hours[serviceType] = [];
      }
    } else {
      // Convert the periods format back to the app's internal format
      const updatedDayHours = $.convertPeriodsToDayHours(hours.periods);
      
      // Update the hours in the place object
      if (selectedPlace.value && selectedPlace.value.hours) {
        // Store the hours array
        selectedPlace.value.hours[serviceType] = updatedDayHours;
        
        // If there's specific data, store it as well
        if (hours.specific) {
          // Add the specific data to the hours array
          (selectedPlace.value.hours[serviceType] as any).specific = hours.specific;
        }
      }
    }
  });
}

// Error handling
const handleError = (error: unknown, defaultMessageKey: string): void => {
  const errorMsg = error instanceof Error ? error.message : t(defaultMessageKey);
  console.error(`Error: ${errorMsg}`, error);
  errorMessage.value = errorMsg;
  openDialog('error');
}

// Dialog action handlers
const handleDialogAction = (action: string): void => {
  switch (action) {
    case 'cancel':
      handleDialogCancel();
      break;
    case 'confirm':
      handleDialogConfirm();
      break;
    default:
      closeDialog();
  }
}

const handleDialogCancel = (): void => {
  switch (dialogState.value.type) {
    case 'confirmation':
      cancelHoursChanges();
      break;
    case 'createCustom':
    case 'removeCustom':
    case 'test':
      // Do nothing for these dialogs
      break;
  }
  closeDialog();
}

const handleDialogConfirm = (): void => {
  switch (dialogState.value.type) {
    case 'confirmation':
      saveHoursChanges();
      break;
    case 'createCustom':
      confirmCreateCustomHours();
      break;
    case 'removeCustom':
      confirmRemoveCustomHours();
      break;
    case 'test':
      // Do nothing for test dialog
      break;
  }
  closeDialog();
}

// Lifecycle hooks
onBeforeMount(async () => {
  loading.value = true;
  try {
    await loadPlaces();
    initializeToggleStates();
  } catch (error) {
    handleError(error, 'error.initialization');
  } finally {
    loading.value = false;
  }
});

onMounted(() => {
  initializeToggleStates();
  setupNavigationGuard(() => openDialog('confirmation'));
});

onUnmounted(() => {
  cleanupNavigationGuard();
});
</script>

<template>
  <div class="screen-container">
    <Screen 
      ref="screenRef" 
      class="service-type-view" 
      :title="selectedPlace?.name || t('service_type.title')" 
      :loading="loading"
    >
      <template #content>
        <div class="service-type-selection">
          <div class="service-cards">
            <!-- General hours card -->
            <ServiceCard
              service-type="general"
              :title="t('service_type.general')"
              :hours="getServiceHours('general')"
              :specific="getSpecificData('general')"
              :has-custom-hours="true"
              :show-toggle="false"
              @card-tap="handleServiceCardTap"
            />
            
            <!-- Service-specific cards - only show available services -->
            <ServiceCard
              v-for="type in availableServiceTypes.filter(t => t !== 'general')"
              :key="type"
              :service-type="type as ServiceTypeKey"
              :title="t(`service_type.${type}`)"
              :hours="getServiceHours(type as ServiceTypeKey)"
              :specific="getSpecificData(type as ServiceTypeKey)"
              :has-custom-hours="customHoursToggle.get(type as ServiceTypeKey) || false"
              :show-toggle="true"
              @card-tap="handleServiceCardTap"
              @toggle-tap="handleToggleTap"
            />
          </div>
          
          <div v-if="hasPendingChanges" class="action-buttons">
            <button class="save-button" @click="saveAllChanges" :disabled="loading">
              {{ loading ? t('service_type.saving') : t('service_type.save') }}
            </button>
            <button class="cancel-button" @click="cancelHoursChanges">
              {{ t('service_type.cancel') }}
            </button>
          </div>
        </div>
      </template>
      
      <template #dialog v-if="dialogState.visible">
        <Dialog
          :title="dialogState.title"
          :description="dialogState.description"
          @closeDialog="closeDialog"
          :showCancel="dialogState.type !== 'error'"
          style="z-index: 9999; position: relative;"
        >
          <template #buttons>
            <Button 
              v-if="dialogState.type !== 'error'" 
              type="clear" 
              class="cancel-button" 
              @click="handleDialogAction('cancel')"
              :title="dialogState.cancelText"
            />
            <Button 
              type="clear" 
              class="confirm-button" 
              @click="handleDialogAction('confirm')"
              :title="dialogState.confirmText"
            />
          </template>
        </Dialog>
      </template>
    </Screen>
  </div>
</template>

<style lang="scss">
@use './styles.scss';
</style>
