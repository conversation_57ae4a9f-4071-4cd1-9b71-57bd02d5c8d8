<script setup lang="ts">
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import type { ServiceTypeKey, DayHours } from '@/types/index';
import { formatHoursSummary } from '@/utils/hoursUtils';

const { t } = useI18n();

const props = defineProps<{
  serviceType: ServiceTypeKey;
  title: string;
  hours: DayHours[];
  hasCustomHours: boolean;
  showToggle: boolean;
  specific?: any;
}>();

const emit = defineEmits<{
  (e: 'card-tap', serviceType: ServiceTypeKey, event?: Event): void;
  (e: 'toggle-tap', serviceType: ServiceTypeKey, event: Event): void;
}>();

const hasHours = computed(() => props.hours && props.hours.length > 0);

// Store filtered future dates in a ref to avoid modifying props
const filteredFutureDates = ref<any[]>([]);

const hasSpecificHours = computed(() => {
  // First check if specific hours exist at all
  if (!props.specific || !Array.isArray(props.specific) || props.specific.length === 0) {
    filteredFutureDates.value = [];
    return false;
  }
  
  // Get current date (without time)
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // Filter specific hours to only include future dates
  const futureSpecificHours = props.specific.filter(item => {
    if (!item.date || typeof item.date !== 'object') return false;
    
    try {
      const { year, month, day } = item.date;
      // Create date object (month is 0-indexed in JS Date)
      const specificDate = new Date(year, month - 1, day);
      specificDate.setHours(0, 0, 0, 0);
      
      // Keep only dates that are today or in the future
      return specificDate >= today;
    } catch (e) {
      return false;
    }
  });
  
  // Store the filtered future dates for use in formatSpecificHours
  filteredFutureDates.value = futureSpecificHours;
  
  return futureSpecificHours.length > 0;
});

// Format date from YearMonthDay object using i18n locale
const formatDate = (date: { year: number; month: number; day: number }) => {
  try {
    const dateObj = new Date(date.year, date.month - 1, date.day);
    // Use the current i18n locale instead of navigator.language
    const currentLocale = t('locale') || 'en';
    return new Intl.DateTimeFormat(currentLocale, {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(dateObj);
  } catch (e) {
    return t('error.invalidDate', 'Invalid date');
  }
};

// Format time from string (e.g., "0900" to "9:00 AM") using i18n locale
const formatTime = (time: string) => {
  try {
    if (time.length !== 4) return time;

    const hours = parseInt(time.substring(0, 2), 10);
    const minutes = parseInt(time.substring(2, 4), 10);

    // Create a date object for proper time formatting
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);

    // Use the current i18n locale instead of hardcoded AM/PM
    const currentLocale = t('locale') || 'en';
    return new Intl.DateTimeFormat(currentLocale, {
      hour: 'numeric',
      minute: minutes > 0 ? '2-digit' : undefined,
      hour12: true
    }).format(date);
  } catch (e) {
    return time;
  }
};

// Format specific hours for display
const formatSpecificHours = computed(() => {
  if (!hasSpecificHours.value) {
    return [];
  }
  
  try {
    // Use the filtered future dates from our ref
    return filteredFutureDates.value.map((item: any) => {
      const formattedDate = formatDate(item.date);
      
      // Format the periods for this specific date
      let hoursText = '';
      if (!item.periods || item.periods.length === 0) {
        hoursText = t('service_type.closed', 'Closed');
      } else {
        const formattedPeriods = item.periods.map((period: any) => {
          // Check if this is a closed period (0000-0000)
          if (period.open.time === '0000' && period.close.time === '0000') {
            return t('service_type.closed', 'Closed');
          }
          return `${formatTime(period.open.time)} - ${formatTime(period.close.time)}`;
        }).join(', ');
        
        hoursText = formattedPeriods;
      }
      
      return {
        date: formattedDate,
        hours: hoursText
      };
    });
  } catch (e) {
    return [];
  }
});

const getServiceIcon = (type: ServiceTypeKey) => {
  switch (type) {
    case 'general':
      return `
        <circle cx="12" cy="12" r="10"></circle>
        <polyline points="12 6 12 12 16 14"></polyline>
      `;
    case 'dinein':
      return `
        <path d="M3 5h1m0 0v14c0 .6.4 1 1 1h2c.6 0 1-.4 1-1V5m0 0h1m0 0h1m0 0h1M9 5h11c.6 0 1 .4 1 1v3c0 1.7-1.3 3-3 3h-5"></path>
        <path d="M13 12v8c0 .6.4 1 1 1h2c.6 0 1-.4 1-1v-8"></path>
      `;
    case 'pickup':
      return `
        <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
        <line x1="3" y1="6" x2="21" y2="6"></line>
        <path d="M16 10a4 4 0 0 1-8 0"></path>
      `;
    case 'deliver':
      return `
        <rect x="1" y="3" width="15" height="13"></rect>
        <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>
        <circle cx="5.5" cy="18.5" r="2.5"></circle>
        <circle cx="18.5" cy="18.5" r="2.5"></circle>
      `;
    default:
      return '';
  }
};

const handleCardTap = (event: Event) => {
  emit('card-tap', props.serviceType, event);
};

const handleToggleTap = (event: Event) => {
  emit('toggle-tap', props.serviceType, event);
};
</script>

<template>
  <div 
    class="service-card" 
    :class="{ 'uses-general': !hasCustomHours }"
    @click="handleCardTap"
  >
    <div class="card-content">
      <div class="card-left">
        <div class="service-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" v-html="getServiceIcon(serviceType)">
          </svg>
        </div>
        
        <div class="service-info">
          <h3 class="service-title">{{ title }}</h3>
          
          <div v-if="showToggle" class="toggle-container">
            <span class="toggle-label">{{ t('service_type.differentFromGeneral') }}</span>
            <label class="toggle-switch" @click.stop.prevent="handleToggleTap">
              <input 
                type="checkbox" 
                :checked="hasCustomHours" 
              >
              <span class="toggle-slider"></span>
            </label>
          </div>
          
          <div class="hours-summary">
            <template v-if="!hasHours">
              <p class="placeholder">
                {{ serviceType === 'general' ? t('service_type.noHours') : t('service_type.tapToSet') }}
              </p>
            </template>
            <template v-else>
              <div class="hours-display" v-html="formatHoursSummary(hours, t('locale'), t('service_type.closed'))">
              </div>
              
              <!-- Special hours section -->
              <div v-if="hasSpecificHours" class="specific-hours">
                <h4 class="specific-hours-title">{{ t('service_type.specialHours', 'Special Hours') }}</h4>
                <ul class="specific-hours-list">
                  <li v-for="(item, index) in formatSpecificHours" :key="index" class="specific-hours-item">
                    <span class="specific-date">{{ item.date }}:</span>
                    <span class="specific-time">{{ item.hours }}</span>
                  </li>
                </ul>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.specific-hours {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid var(--border-color, #eee);
}

.specific-hours-title {
  font-size: 1em; /* Increased from 0.9em (30% larger) */
  font-weight: 600;
  margin: 0 0 6px 0;
  color: var(--text-primary, #333);
}

.specific-hours-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.specific-hours-item {
  font-size: 1em; /* Increased from 0.85em (30% larger) */
  margin-bottom: 4px;
  color: var(--text-secondary, #666);
  display: flex;
  flex-wrap: wrap;
}

.specific-date {
  font-weight: 500;
  margin-right: 6px;
}

.specific-time {
  flex: 1;
}
</style>
