{"en": {"locale": "en", "button": {"next": "Next", "fetch_data": "Simulate Fetch Data"}, "welcome_page": {"title": "Welcome Page"}, "about_page": {"title": "About Page"}, "place_list": {"title": "Select a Place", "searchPlaceholder": "Search places...", "openNow": "Open", "closed": "Closed", "noResults": "No places found"}, "service_type": {"title": "Service Hours", "general": "General", "dinein": "<PERSON><PERSON>-<PERSON>", "pickup": "Pickup", "deliver": "Delivery", "edit": "Edit", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "usesGeneral": "Uses General", "noHours": "No hours set", "tapToSet": "Tap to set specific hours", "saveChanges": "Save Changes?", "saveChangesMessage": "You have unsaved changes. Would you like to save them before leaving?", "unsavedChangesMessage": "You have unsaved changes. Would you like to save them before navigating away?", "discard": "Discard", "discardAndLeave": "Discard & Leave", "createCustomHours": "Create Custom Hours", "createCustomHoursMessage": "Do you want to create custom hours for {type}? This will start with a copy of the general hours.", "removeCustomHours": "Remove Custom Hours", "removeCustomHoursMessage": "Do you want to remove custom hours for {type}? This will revert to using general hours.", "create": "Create", "remove": "Remove", "differentFromGeneral": "Different from General?"}, "error": {"oops": "Oops!", "placeNotFound": "Place not found", "editingHours": "Error editing hours", "savingHours": "Error saving hours", "initializingAPI": "Error initializing API", "invalidPlaceId": "Invalid place ID", "invalidDate": "Invalid date"}}, "zh-Hans": {"locale": "zh-Hans", "button": {"next": "下一步", "fetch_data": "模拟获取数据"}, "welcome_page": {"title": "欢迎页面"}, "about_page": {"title": "关于页面"}, "place_list": {"title": "选择地点", "searchPlaceholder": "搜索地点...", "openNow": "营业", "closed": "已关闭", "noResults": "未找到地点"}, "service_type": {"title": "服务时间", "general": "一般营业时间", "dinein": "堂食", "pickup": "自取", "deliver": "外送", "edit": "编辑", "save": "保存", "saving": "保存中...", "cancel": "取消", "usesGeneral": "使用一般时间", "noHours": "未设置时间", "tapToSet": "点击设置特定时间", "saveChanges": "保存更改？", "saveChangesMessage": "您有未保存的更改。离开前是否要保存？", "unsavedChangesMessage": "您有未保存的更改。在离开页面前是否要保存？", "discard": "放弃", "discardAndLeave": "放弃并离开", "createCustomHours": "创建自定义时间", "createCustomHoursMessage": "您想为{type}创建自定义时间吗？这将从一般营业时间的副本开始。", "removeCustomHours": "移除自定义时间", "removeCustomHoursMessage": "您想移除{type}的自定义时间吗？这将恢复使用一般营业时间。", "create": "创建", "remove": "移除", "differentFromGeneral": "与一般营业时间不同?"}, "error": {"oops": "出错了！", "placeNotFound": "未找到地点", "editingHours": "编辑时间时出错", "savingHours": "保存时间时出错", "initializingAPI": "初始化API时出错", "invalidPlaceId": "无效的地点ID", "invalidDate": "无效日期"}}, "zh-Hant": {"locale": "zh-Han<PERSON>", "button": {"next": "下一步", "fetch_data": "模擬獲取數據"}, "welcome_page": {"title": "歡迎頁面"}, "about_page": {"title": "關於頁面"}, "place_list": {"title": "選擇地點", "searchPlaceholder": "搜索地點...", "openNow": "營業", "closed": "已關閉", "noResults": "未找到地點"}, "service_type": {"title": "服務時間", "general": "一般營業時間", "dinein": "堂食", "pickup": "自取", "deliver": "外送", "edit": "編輯", "save": "保存", "saving": "保存中...", "cancel": "取消", "usesGeneral": "使用一般時間", "noHours": "未設置時間", "tapToSet": "點擊設置特定時間", "saveChanges": "保存更改？", "saveChangesMessage": "您有未保存的更改。離開前是否要保存？", "unsavedChangesMessage": "您有未保存的更改。在離開頁面前是否要保存？", "discard": "放棄", "discardAndLeave": "放棄並離開", "createCustomHours": "創建自定義時間", "createCustomHoursMessage": "您想為{type}創建自定義時間嗎？這將從一般營業時間的副本開始。", "removeCustomHours": "移除自定義時間", "removeCustomHoursMessage": "您想移除{type}的自定義時間嗎？這將恢復使用一般營業時間。", "create": "創建", "remove": "移除", "differentFromGeneral": "與一般營業時間不同?"}, "error": {"oops": "出錯了！", "placeNotFound": "未找到地點", "editingHours": "編輯時間時出錯", "savingHours": "保存時間時出錯", "initializingAPI": "初始化API時出錯", "invalidPlaceId": "無效的地點ID", "invalidDate": "無效日期"}}}