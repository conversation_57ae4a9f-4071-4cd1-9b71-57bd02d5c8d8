# Developer Guide - Manage Hours Applet

## Project Overview

The **Manage Hours** applet is a Vue.js-based application designed to help businesses manage their operating hours for different service types across multiple locations. It provides an intuitive interface for configuring general operating hours and service-specific hours for dine-in, pickup, and delivery services.

### Key Features
- **Multi-location Management**: View and manage hours for multiple business locations
- **Service-specific Hours**: Configure different hours for general operations, dine-in, pickup, and delivery
- **Flexible Time Configuration**: Support for multiple time ranges per day and closed days
- **Real-time Status**: Visual indicators showing which locations are currently open/closed
- **Internationalization**: Multi-language support (English, Simplified Chinese, Traditional Chinese)
- **Responsive Design**: Works across various device sizes

## Architecture

### Technology Stack
- **Frontend Framework**: Vue 3 with Composition API
- **Language**: TypeScript
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Build Tool**: Vite
- **Styling**: SCSS with modern CSS features
- **Internationalization**: Vue I18n
- **Package Manager**: Yarn 4

### Core Dependencies
- **@perkd/applet-common**: Core applet framework and utilities
- **@perkd/vue-components**: Shared Vue components library
- **@perkd/format-datetime**: Date/time formatting utilities
- **vue**: Vue.js framework (v3.5.13)
- **vue-router**: Client-side routing (v4.5.0)
- **pinia**: State management (v3.0.1)
- **vue-i18n**: Internationalization (v11.1.2)

### Development Dependencies
- **TypeScript**: Type checking and compilation
- **Vite**: Build tool and development server
- **ESLint**: Code linting with Vue and TypeScript support
- **Prettier**: Code formatting
- **Sass**: CSS preprocessing
- **Vue TSC**: Vue TypeScript compiler

## Setup Instructions

### Prerequisites
- Node.js (version 22 or higher recommended)
- Yarn 4.7.0+ (specified in packageManager field)

### Installation
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd applet-manage-hours
   ```

2. **Install dependencies**
   ```bash
   yarn install
   ```

3. **Start development server**
   ```bash
   yarn dev
   ```
   The application will be available at `http://localhost:5173`

### Environment Configuration
The project uses Vite environment variables:
- `VITE_SINGLE_PAGE`: Controls single-page mode (set to `false` for applet mode)

## Development Workflow

### Available Scripts

| Script | Command | Description |
|--------|---------|-------------|
| `dev` | `vite` | Start development server with hot reload |
| `build` | `npm run prebuild && run-p type-check build-only && npm run postbuild` | Build for production |
| `build-only` | `vite build` | Build without type checking |
| `type-check` | `vue-tsc --noEmit -p tsconfig.app.json --composite false` | Run TypeScript type checking |
| `preview` | `vite preview` | Preview production build locally |
| `format` | `prettier --write src/` | Format source code |
| `set-env` | `cross-env VITE_SINGLE_PAGE=false` | Set environment variables |

### Build Process
1. **Pre-build**: Sets environment variables and runs applet-common prebuild script
2. **Type Check**: Validates TypeScript types across the codebase
3. **Build**: Compiles and bundles the application
4. **Post-build**: Runs applet-common postbuild script for final processing

### Development Server
```bash
yarn dev
```
- Hot module replacement enabled
- TypeScript compilation on-the-fly
- SCSS preprocessing
- Vue SFC compilation

## Code Structure

```
src/
├── assets/           # Static assets and global styles
│   └── main.scss    # Global SCSS styles
├── components/      # Reusable Vue components
│   └── UIScreen.vue # Screen wrapper component
├── router/          # Vue Router configuration
│   └── index.ts     # Route definitions and navigation guards
├── stores/          # Pinia state management
│   └── data.ts      # Main data store for places and hours
├── types/           # TypeScript type definitions
│   └── index.ts     # Core types for hours, places, and services
├── utils/           # Utility functions
│   └── hoursUtils.ts # Hours formatting and manipulation utilities
├── views/           # Page components
│   ├── AboutPage/   # About page
│   ├── PlaceListView/ # Main place selection view
│   ├── ServiceTypeView/ # Hours management interface
│   └── WelcomePage/ # Welcome/landing page
├── App.vue          # Root application component
├── i18n.json        # Internationalization translations
└── main.ts          # Application entry point
```

### Key Files

#### `src/main.ts`
Application bootstrap file that:
- Initializes Vue app with Pinia and Vue Router
- Sets up internationalization
- Configures PerkD applet framework integration
- Handles theme, font, and language setup

#### `src/stores/data.ts`
Main Pinia store containing:
- Application state (places, loading states, errors)
- Data fetching logic for places from PerkD API
- Data transformation utilities for hours format conversion
- Error handling and state management

#### `src/types/index.ts`
Core TypeScript definitions:
- `Place`: Business location with hours and service availability
- `Hours`: Operating hours structure for different service types
- `DayHours`: Daily hours with time ranges and closed status
- `ServiceType`: Enum for service types (general, dinein, pickup, deliver)

#### `src/utils/hoursUtils.ts`
Utility functions for:
- Time formatting (24-hour to 12-hour conversion)
- Hours display formatting and grouping
- Day range formatting (consecutive days)
- Special day and holiday handling

## Configuration

### TypeScript Configuration
- **tsconfig.app.json**: Application-specific TypeScript settings
- **Path aliases**: `@/*` maps to `./src/*` for clean imports
- **Target**: Modern DOM environment with Vue SFC support

### Vite Configuration (`vite.config.ts`)
- **Vue plugin**: SFC compilation and hot reload
- **Legacy plugin**: Browser compatibility (Chrome 49+, Safari 12+)
- **SCSS preprocessing**: Modern Sass API
- **Asset handling**: Preserves original filenames for CSS
- **Base path**: Relative paths for deployment flexibility

### Yarn Configuration (`.yarnrc.yml`)
- **Node linker**: Uses traditional node_modules structure
- **Package manager**: Yarn 4.7.0 with specific SHA for reproducibility

### Internationalization (`src/i18n.json`)
Supports three languages:
- **English** (`en`): Default language
- **Simplified Chinese** (`zh-Hans`): Mainland China
- **Traditional Chinese** (`zh-Hant`): Taiwan/Hong Kong

## Testing

### Test Structure
- **test/test-service-types.js**: Service type availability testing
- Tests service type filtering logic and availability checks
- Validates data transformation and business logic

### Running Tests
Currently uses Node.js scripts for testing. To run:
```bash
node test/test-service-types.js
```

### Testing Strategy
- **Unit tests**: For utility functions and data transformations
- **Integration tests**: For component interactions and API calls
- **Manual testing**: Through development server and applet viewer

## Contributing Guidelines

### Code Style
- **TypeScript**: Strict type checking enabled
- **Vue 3**: Composition API preferred over Options API
- **ESLint**: Vue and TypeScript rules enforced
- **Prettier**: Automatic code formatting on save

### Commit Guidelines
- Use descriptive commit messages
- Include type prefixes: `feat:`, `fix:`, `docs:`, `refactor:`
- Reference issue numbers when applicable

### Pull Request Process
1. Create feature branch from main
2. Implement changes with proper TypeScript types
3. Add/update tests for new functionality
4. Run `yarn type-check` and `yarn format`
5. Test in applet viewer environment
6. Submit PR with detailed description

### Development Best Practices
- **Type Safety**: Always define proper TypeScript interfaces
- **Component Structure**: Use Composition API with `<script setup>`
- **State Management**: Use Pinia stores for shared state
- **Internationalization**: Add translations for all user-facing text
- **Error Handling**: Implement proper error boundaries and user feedback

## Troubleshooting

### Common Issues

#### Build Failures
**Problem**: TypeScript compilation errors
**Solution**: 
```bash
yarn type-check
# Fix reported type errors
```

**Problem**: Missing dependencies
**Solution**:
```bash
yarn install --frozen-lockfile
```

#### Development Server Issues
**Problem**: Hot reload not working
**Solution**: 
- Check file permissions
- Restart development server
- Clear browser cache

**Problem**: SCSS compilation errors
**Solution**: 
- Verify SCSS syntax
- Check import paths
- Update Sass version if needed

#### Applet Integration Issues
**Problem**: PerkD applet framework errors
**Solution**:
- Verify `@perkd/*` package versions
- Check applet configuration
- Test in applet viewer environment

#### Data Loading Issues
**Problem**: Places not loading
**Solution**:
- Check API connectivity
- Verify data format in browser console
- Test with sample data

### Performance Optimization
- Use `yarn build` to check bundle size
- Implement code splitting for large components
- Optimize images and assets
- Use Vue DevTools for performance profiling

### Debugging Tips
- Use Vue DevTools browser extension
- Enable TypeScript strict mode for better error detection
- Use browser console for API response inspection
- Test across different screen sizes and devices

---

For additional support, refer to the PerkD applet documentation or contact the development team.
