{"name": "@vitejs/plugin-legacy", "version": "6.0.2", "type": "module", "license": "MIT", "author": "<PERSON>", "files": ["dist"], "keywords": ["frontend", "vite", "vite-plugin", "@vitejs/plugin-legacy"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/plugin-legacy"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/packages/plugin-legacy#readme", "funding": "https://github.com/vitejs/vite?sponsor=1", "dependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "browserslist": "^4.24.4", "browserslist-to-esbuild": "^2.1.1", "core-js": "^3.40.0", "magic-string": "^0.30.17", "regenerator-runtime": "^0.14.1", "systemjs": "^6.15.1"}, "peerDependencies": {"terser": "^5.16.0", "vite": "^6.0.0"}, "devDependencies": {"acorn": "^8.14.0", "picocolors": "^1.1.1", "unbuild": "^3.3.1", "vite": "6.2.0"}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs", "patch-cjs": "tsx ../../scripts/patchCJS.ts"}}