import {
  i18n_default
} from "./chunk-ZD7BQ6ST.js";
import {
  Applets
} from "./chunk-6B6YBE7R.js";

// node_modules/@perkd/applet-common/dist/actions.js
function closeWindow() {
  window.$perkd.do("window.close");
}
async function getConstants() {
  try {
    const response = await window.$perkd.do("constants");
    const { constants } = response || {};
    return constants;
  } catch (error) {
    return { error };
  }
}
async function openSlotPicker(param) {
  const { value, theme = Applets.ColorScheme.LIGHT, options } = param || {};
  try {
    const response = await window.$perkd.do("form.showpicker", {
      type: "slotPicker",
      theme,
      options,
      value
    });
    return response;
  } catch (error) {
    return { error };
  }
}
async function openDurationPicker(param) {
  const { value, theme = Applets.ColorScheme.LIGHT, options } = param || {};
  try {
    const response = await window.$perkd.do("form.showpicker", {
      type: "durationPicker",
      theme,
      options,
      value
    });
    return response;
  } catch (error) {
    return { error };
  }
}
async function openDateTimePicker({ value = "", mode = Applets.DateTimePickerType.DATE, theme = Applets.ColorScheme.LIGHT }) {
  try {
    const response = await window.$perkd.do("form.showpicker", {
      type: "dateTimePicker",
      theme,
      mode: mode === Applets.DateTimePickerType.DATETIME ? "dateTime" : mode,
      value
    });
    return response;
  } catch (error) {
    return { error };
  }
}
async function openScanner(payload) {
  try {
    const response = await window.$perkd.do("media.scan", payload);
    return response;
  } catch (error) {
    return { error };
  }
}
async function navToApplet(appletName, cardInstanceId, params = {}) {
  try {
    await window.$perkd.do("app.navto", {
      route: [
        { "card": { "id": cardInstanceId } },
        { [appletName]: params }
      ]
    });
  } catch (error) {
    return { error };
  }
}
function openDialog(params) {
  var _a;
  const { title, message, buttons, lang = "en" } = params;
  return window.$perkd.do("interact.dialog", {
    title,
    message,
    buttons: buttons && buttons.length > 0 ? buttons : [{
      text: (_a = i18n_default[lang] || i18n_default.en) == null ? void 0 : _a.button.ok,
      action: { object: "window", action: "close" },
      style: "default"
    }],
    cancelable: true
  });
}
async function emailTo(email, subject) {
  try {
    await window.$perkd.do("communicate.email", {
      address: email,
      cc: "<EMAIL>",
      subject
    });
  } catch (error) {
    return { error };
  }
}
async function callTo(number) {
  try {
    await window.$perkd.do("communicate.call", { number });
  } catch (error) {
    return { error };
  }
}
async function copyText(text) {
  try {
    await window.$perkd.do("system.toclipboard", { text });
    await window.$perkd.do("interact.toast", { message: text });
  } catch (error) {
    return { error };
  }
}
async function showToast(text) {
  try {
    await window.$perkd.do("interact.toast", { message: text });
  } catch (error) {
    return { error };
  }
}
async function addToBag(params) {
  try {
    const items = params.items.map((item) => {
      const { channel, variantId, productId, sku, title, quantity, status, unitPrice, price, amount, discount, fulfillments, paymentMethods, taxes, images, tags, properties, options, ttl, attributes, kind, unit, custom, expiresAt, capacity, admit, unitPriceMeasure, units, description, inventory } = item;
      return {
        channel,
        variantId,
        productId,
        sku,
        title,
        quantity,
        status,
        unitPrice,
        price,
        amount,
        discount,
        fulfillments,
        paymentMethods,
        taxes,
        images,
        tags,
        properties,
        options,
        ttl,
        attributes,
        kind,
        unit,
        custom,
        expiresAt,
        capacity,
        admit,
        unitPriceMeasure,
        units,
        description,
        inventory
      };
    });
    const response = await window.$perkd.do("bag.additems", { ...params, items });
    return response;
  } catch (error) {
    return { error };
  }
}
async function openBag() {
  try {
    await window.$perkd.do("bag.open");
  } catch (error) {
    return { error };
  }
}
async function orderProducts(params) {
  try {
    const response = await window.$perkd.do("order.products", params);
    return response;
  } catch (error) {
    return { error };
  }
}
async function reorderFromReceipt(params) {
  try {
    const response = await window.$perkd.do("bag.from", params);
    return response;
  } catch (error) {
    return { error };
  }
}
async function perkdRemote(data) {
  const { method, base, endpoint, cardId, ...rest } = data;
  if (!(base && endpoint)) {
    return { error: { statusMessage: "config_missing" } };
  }
  const payload = {
    method,
    url: `${base}/${endpoint}`,
    cardId,
    credentials: "perkd",
    ...rest
  };
  try {
    const response = await window.$perkd.do("remote.api", payload);
    return response;
  } catch (error) {
    await trackWatch(`${base}/${endpoint}`, error, payload);
    return { error };
  }
}
async function writeNFC(payload) {
  try {
    const response = await window.$perkd.do("nfc.write", payload);
    return response;
  } catch (error) {
    return { error };
  }
}
async function readNFC(payload) {
  try {
    const response = await window.$perkd.do("nfc.read", payload);
    return response;
  } catch (error) {
    return { error };
  }
}
async function lockNFC(password, options) {
  try {
    const response = await window.$perkd.do("nfc.setpassword", { password, options });
    return response;
  } catch (error) {
    return { error };
  }
}
async function fileShare(file) {
  try {
    const response = await window.$perkd.do("file.share", { file });
    return response;
  } catch (error) {
    return { error };
  }
}
async function trackWatch(message, error, data) {
  try {
    await window.$perkd.do("track.watch", { message, error, data });
  } catch (error2) {
    return { error: error2 };
  }
}
var SoundEffect;
(function(SoundEffect2) {
  SoundEffect2["Achieved"] = "achieved";
  SoundEffect2["Beep"] = "beep";
  SoundEffect2["BellChord"] = "bellchord";
  SoundEffect2["Cash"] = "cash";
  SoundEffect2["Cashier"] = "cashier";
  SoundEffect2["Chord"] = "chord";
  SoundEffect2["Correct"] = "correct";
  SoundEffect2["Done"] = "done";
  SoundEffect2["Fail"] = "fail";
  SoundEffect2["Happy"] = "happy";
  SoundEffect2["Magic"] = "magic";
  SoundEffect2["Notify"] = "notify";
  SoundEffect2["Scan"] = "scan";
  SoundEffect2["ServiceBell"] = "servicebell";
  SoundEffect2["Success"] = "success";
  SoundEffect2["SuccessBell"] = "successbell";
  SoundEffect2["Upsell"] = "upsell";
  SoundEffect2["WifiOn"] = "wifion";
})(SoundEffect || (SoundEffect = {}));
async function playSound(name) {
  try {
    await window.$perkd.do("interact.playsound", { name });
  } catch (error) {
    return { error };
  }
}
var VibrateEffect;
(function(VibrateEffect2) {
  VibrateEffect2["Selection"] = "selection";
  VibrateEffect2["ImpactLight"] = "impactLight";
  VibrateEffect2["ImpactMedium"] = "impactMedium";
  VibrateEffect2["ImpactHeavy"] = "impactHeavy";
  VibrateEffect2["NotificationSuccess"] = "notificationSuccess";
  VibrateEffect2["NotificationWarning"] = "notificationWarning";
  VibrateEffect2["NotificationError"] = "notificationError";
})(VibrateEffect || (VibrateEffect = {}));
async function vibrate(type) {
  try {
    await window.$perkd.do("interact.vibrate", { type });
  } catch (error) {
    return { error };
  }
}
var WebMethod;
(function(WebMethod2) {
  WebMethod2["NATIVE"] = "native";
  WebMethod2["BROWSER"] = "browser";
  WebMethod2["IN_APP"] = "web";
})(WebMethod || (WebMethod = {}));
async function openLink(url, method = WebMethod.BROWSER, context) {
  try {
    const param = { [method]: url };
    if (context) {
      param.context = context;
    }
    await window.$perkd.do("web.open", { param });
  } catch (error) {
    return { error };
  }
}
async function getWidgetData(widgetName, params = {}) {
  try {
    const payload = {
      key: widgetName
    };
    if ((params == null ? void 0 : params.id) !== void 0) {
      payload.id = params.id;
    }
    const response = await window.$perkd.do("widget.data", payload);
    return response;
  } catch (error) {
    return { error };
  }
}
async function openHoursSettings(hours) {
  try {
    const response = await window.$perkd.do("form.hoursSettings", { hours: hours || { periods: [] } });
    return response;
  } catch (error) {
    return { error };
  }
}

export {
  closeWindow,
  getConstants,
  openSlotPicker,
  openDurationPicker,
  openDateTimePicker,
  openScanner,
  navToApplet,
  openDialog,
  emailTo,
  callTo,
  copyText,
  showToast,
  addToBag,
  openBag,
  orderProducts,
  reorderFromReceipt,
  perkdRemote,
  writeNFC,
  readNFC,
  lockNFC,
  fileShare,
  trackWatch,
  SoundEffect,
  playSound,
  VibrateEffect,
  vibrate,
  WebMethod,
  openLink,
  getWidgetData,
  openHoursSettings
};
//# sourceMappingURL=chunk-B4GMYPGS.js.map
