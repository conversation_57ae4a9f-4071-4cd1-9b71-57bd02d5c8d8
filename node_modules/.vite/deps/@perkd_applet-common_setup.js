import {
  getContrastColor,
  hexToRgbo
} from "./chunk-BA27V7OM.js";
import {
  i18n_default
} from "./chunk-ZD7BQ6ST.js";
import {
  mergeObject
} from "./chunk-QVDR62FV.js";
import {
  Applets
} from "./chunk-6B6YBE7R.js";
import "./chunk-EQCVQC35.js";

// node_modules/@perkd/applet-common/dist/setup.js
var setupEnvironment = (constants) => {
  const { DEVICE, CARD, COUNTRY, CARDMASTER, CONTENT, PERSON, FONTCSS, FONTPATH, LANGUAGE, CONTEXT } = constants || {};
  return {
    DEVICE,
    CARD,
    CARDMASTER,
    COUNTRY,
    LANGUAGE,
    CONTENT,
    FONTCSS,
    FONTPATH,
    CONTEXT,
    PERSON,
    deviceScheme: window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches ? Applets.ColorScheme.DARK : Applets.ColorScheme.LIGHT
  };
};
var setupWidget = (data, appletName) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q;
  const { CARD, CARDMASTER } = data || {};
  const WIDGET_INSTANCE = ((_a = CARD == null ? void 0 : CARD.widgets) == null ? void 0 : _a.find((w) => w.key === appletName)) || {};
  const WIDGET_MASTER = ((_b = CARDMASTER == null ? void 0 : CARDMASTER.widgets) == null ? void 0 : _b.find((w) => w.key === appletName)) || {};
  return {
    API_BASE: ((_d = (_c = WIDGET_INSTANCE == null ? void 0 : WIDGET_INSTANCE.param) == null ? void 0 : _c.api) == null ? void 0 : _d.baseUrl) || ((_f = (_e = WIDGET_MASTER == null ? void 0 : WIDGET_MASTER.param) == null ? void 0 : _e.api) == null ? void 0 : _f.baseUrl),
    API: ((_g = WIDGET_INSTANCE == null ? void 0 : WIDGET_INSTANCE.param) == null ? void 0 : _g.api) || ((_h = WIDGET_MASTER == null ? void 0 : WIDGET_MASTER.param) == null ? void 0 : _h.api),
    COLOR_SCHEME: ((_i = WIDGET_INSTANCE == null ? void 0 : WIDGET_INSTANCE.param) == null ? void 0 : _i.colorScheme) || ((_j = WIDGET_MASTER == null ? void 0 : WIDGET_MASTER.param) == null ? void 0 : _j.colorScheme),
    SETTINGS: ((_k = WIDGET_INSTANCE == null ? void 0 : WIDGET_INSTANCE.param) == null ? void 0 : _k.settings) || ((_l = WIDGET_MASTER == null ? void 0 : WIDGET_MASTER.param) == null ? void 0 : _l.settings),
    MASTER_SETTINGS: (_m = WIDGET_MASTER == null ? void 0 : WIDGET_MASTER.param) == null ? void 0 : _m.settings,
    MEMBERSHIP_PROGRAMS: ((_o = (_n = WIDGET_INSTANCE == null ? void 0 : WIDGET_INSTANCE.param) == null ? void 0 : _n.settings) == null ? void 0 : _o.programs) || ((_q = (_p = WIDGET_MASTER == null ? void 0 : WIDGET_MASTER.param) == null ? void 0 : _p.settings) == null ? void 0 : _q.programs)
  };
};
var setupFont = (data, embeddedFont) => {
  if (!embeddedFont)
    return;
  const styleElement = document.createElement("style");
  const headElement = document.head || document.getElementsByTagName("head")[0];
  styleElement.textContent = `
        @font-face {
            font-family: Melbourne;
            font-weight: normal;
            src: local('Melbourne'), url('${data.FONTPATH}Melbourne.otf') format('opentype');
        }

        @font-face {
            font-family: Melbourne;
            font-weight: bold;
            src: local('Melbourne_bold'), url('${data.FONTPATH}Melbourne_bold.otf') format('opentype');
        }

        @font-face {
            font-family: picon;
            font-weight: normal;
            src: local('picon'), url('${data.FONTPATH}picon.ttf') format('truetype');
        }
        
        ${data.FONTCSS}
    `;
  headElement.appendChild(styleElement);
};
var setupTheme = (data, appletName) => {
  var _a, _b;
  const { DEVICE, CARDMASTER, deviceScheme = Applets.ColorScheme.LIGHT } = data;
  const { WIDTH, HEIGHT, MIN_TOP_SPACE, STATUS_BAR_HEIGHT, NAV_BAR_HEIGHT, MIN_BOTTOM_SPACE, BOTTOM_TABS_HEIGHT, windowHeight, IOS, IS_LONG_SCREEN, APP } = DEVICE || {};
  const { VERSION } = APP || {};
  const CSS_ROOT = {
    "--width-screen": WIDTH + "px",
    "--height-screen": HEIGHT + "px",
    "--height-window": windowHeight + "px",
    "--height-minTop": (MIN_TOP_SPACE || 20) + "px",
    "--height-minBottom": (MIN_BOTTOM_SPACE || 20) + "px",
    "--height-statusBar": (IOS ? STATUS_BAR_HEIGHT : 0) + "px",
    "--height-navigationBar": NAV_BAR_HEIGHT + (IOS ? STATUS_BAR_HEIGHT : 0) + "px",
    "--height-tabBar": BOTTOM_TABS_HEIGHT + "px",
    "--device-ios": IOS,
    "--device-longScreen": IS_LONG_SCREEN,
    "--app-version": VERSION,
    "--font-size-base": Math.round(((WIDTH / 320 - 1) * 0.8 + 1) * 10) + "px",
    "--size-base": `${Math.round(((WIDTH / 320 - 1) * 0.8 + 1) * 10)}`
  };
  Object.keys(CSS_ROOT).forEach((key) => {
    document.documentElement.style.setProperty(key, CSS_ROOT ? CSS_ROOT[key] : "");
  });
  const WIDGET_MASTER = appletName ? ((_a = CARDMASTER == null ? void 0 : CARDMASTER.widgets) == null ? void 0 : _a.find((w) => w.key === appletName)) || {} : {};
  const { colorScheme } = (WIDGET_MASTER == null ? void 0 : WIDGET_MASTER.param) || {};
  const { brand, theme = colorScheme || deviceScheme } = CARDMASTER || {};
  const { light, dark } = ((_b = WIDGET_MASTER == null ? void 0 : WIDGET_MASTER.param) == null ? void 0 : _b.theme) || (brand == null ? void 0 : brand.style) || {};
  const COLOR_SCHEME = {
    light: Object.assign({}, Applets.DefaultTheme.light, light),
    dark: Object.assign({}, Applets.DefaultTheme.dark, dark)
  };
  const colorKeys = Object.keys(COLOR_SCHEME[theme]);
  colorKeys.forEach((key) => {
    const color = COLOR_SCHEME[theme][key];
    if (color) {
      const { r, g, b, o } = hexToRgbo(color);
      const contrast = getContrastColor(color);
      document.documentElement.style.setProperty(`--color-brand-${key}`, `rgb(${r},${g},${b},${o})`);
      document.documentElement.style.setProperty(`--color-brand-${key}-contrast`, contrast);
    }
  });
  document.documentElement.setAttribute("theme", theme);
};
var setupLanguage = async (data, locales, dayjs) => {
  const { ENGLISH } = Applets.Language;
  const { LANGUAGE = ENGLISH } = data;
  document.documentElement.lang = LANGUAGE;
  if (dayjs && locales && dayjs.locale) {
    const localLanguage = locales[LANGUAGE] || locales[ENGLISH];
    dayjs.locale(localLanguage);
  }
};
var setupI18n = (translations) => mergeObject(i18n_default, translations);
export {
  setupEnvironment,
  setupFont,
  setupI18n,
  setupLanguage,
  setupTheme,
  setupWidget
};
//# sourceMappingURL=@perkd_applet-common_setup.js.map
