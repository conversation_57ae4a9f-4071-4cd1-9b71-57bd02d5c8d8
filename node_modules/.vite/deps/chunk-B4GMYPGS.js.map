{"version": 3, "sources": ["../../@perkd/applet-common/dist/actions.js"], "sourcesContent": ["import { Applets } from \"./types/applets.js\";\nimport commonTranslate from './i18n.json';\nexport function closeWindow() {\n    window.$perkd.do('window.close');\n}\nexport async function getConstants() {\n    try {\n        const response = await window.$perkd.do('constants');\n        const { constants } = response || {};\n        return constants;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function openSlotPicker(param) {\n    const { value, theme = Applets.ColorScheme.LIGHT, options } = param || {};\n    try {\n        const response = await window.$perkd.do(\"form.showpicker\", {\n            type: 'slotPicker',\n            theme,\n            options,\n            value\n        });\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function openDurationPicker(param) {\n    const { value, theme = Applets.ColorScheme.LIGHT, options } = param || {};\n    try {\n        const response = await window.$perkd.do(\"form.showpicker\", {\n            type: 'durationPicker',\n            theme,\n            options,\n            value\n        });\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function openDateTimePicker({ value = '', mode = Applets.DateTimePickerType.DATE, theme = Applets.ColorScheme.LIGHT }) {\n    // app typo (should be datetime, typo: dateTime )\n    try {\n        const response = await window.$perkd.do(\"form.showpicker\", {\n            type: 'dateTimePicker',\n            theme,\n            mode: mode === Applets.DateTimePickerType.DATETIME ? 'dateTime' : mode,\n            value\n        });\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function openScanner(payload) {\n    try {\n        const response = await window.$perkd.do('media.scan', payload);\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function navToApplet(appletName, cardInstanceId, params = {}) {\n    try {\n        await window.$perkd.do('app.navto', {\n            route: [\n                { 'card': { 'id': cardInstanceId } },\n                { [appletName]: params }\n            ]\n        });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport function openDialog(params) {\n    const { title, message, buttons, lang = 'en' } = params;\n    return window.$perkd.do('interact.dialog', {\n        title,\n        message,\n        buttons: buttons && buttons.length > 0 ? buttons : [{\n                text: (commonTranslate[lang] || commonTranslate.en)?.button.ok,\n                action: { object: 'window', action: 'close' },\n                style: 'default'\n            }],\n        cancelable: true\n    });\n}\nexport async function emailTo(email, subject) {\n    try {\n        await window.$perkd.do('communicate.email', {\n            address: email,\n            cc: '<EMAIL>',\n            subject\n        });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function callTo(number) {\n    try {\n        await window.$perkd.do('communicate.call', { number });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function copyText(text) {\n    try {\n        await window.$perkd.do('system.toclipboard', { text });\n        await window.$perkd.do('interact.toast', { message: text });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function showToast(text) {\n    try {\n        await window.$perkd.do('interact.toast', { message: text });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function addToBag(params) {\n    try {\n        const items = params.items.map((item) => {\n            const { channel, variantId, productId, sku, title, quantity, status, unitPrice, price, amount, discount, fulfillments, paymentMethods, taxes, images, tags, properties, options, ttl, attributes, kind, unit, custom, expiresAt, capacity, admit, unitPriceMeasure, units, description, inventory } = item;\n            return {\n                channel, variantId, productId, sku, title, quantity, status, unitPrice, price, amount, discount, fulfillments, paymentMethods,\n                taxes, images, tags, properties, options, ttl, attributes, kind, unit, custom, expiresAt,\n                capacity, admit, unitPriceMeasure, units, description, inventory\n            };\n        });\n        const response = await window.$perkd.do('bag.additems', { ...params, items });\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function openBag() {\n    try {\n        await window.$perkd.do('bag.open');\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function orderProducts(params) {\n    try {\n        const response = await window.$perkd.do('order.products', params);\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function reorderFromReceipt(params) {\n    try {\n        const response = await window.$perkd.do('bag.from', params);\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function perkdRemote(data) {\n    const { method, base, endpoint, cardId, ...rest } = data;\n    if (!(base && endpoint)) {\n        return { error: { statusMessage: 'config_missing' } };\n    }\n    const payload = {\n        method,\n        url: `${base}/${endpoint}`,\n        cardId,\n        credentials: 'perkd',\n        ...rest\n    };\n    try {\n        const response = await window.$perkd.do('remote.api', payload);\n        return response;\n    }\n    catch (error) {\n        await trackWatch(`${base}/${endpoint}`, error, payload);\n        return { error };\n    }\n}\n// payload\n// {\n//     value: string,\n//     options: { encode: boolean, description: string }\n// }\nexport async function writeNFC(payload) {\n    try {\n        // nothing will be returned when success\n        const response = await window.$perkd.do('nfc.write', payload);\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\n// payload\n// {\n//     options: { encode: boolean }\n// }\nexport async function readNFC(payload) {\n    try {\n        const response = await window.$perkd.do('nfc.read', payload);\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function lockNFC(password, options) {\n    try {\n        // nothing will be returned when success\n        const response = await window.$perkd.do('nfc.setpassword', { password, options });\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function fileShare(file) {\n    try {\n        const response = await window.$perkd.do('file.share', { file });\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function trackWatch(message, error, data) {\n    try {\n        await window.$perkd.do('track.watch', { message, error, data });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport var SoundEffect;\n(function (SoundEffect) {\n    SoundEffect[\"Achieved\"] = \"achieved\";\n    SoundEffect[\"Beep\"] = \"beep\";\n    SoundEffect[\"BellChord\"] = \"bellchord\";\n    SoundEffect[\"Cash\"] = \"cash\";\n    SoundEffect[\"Cashier\"] = \"cashier\";\n    SoundEffect[\"Chord\"] = \"chord\";\n    SoundEffect[\"Correct\"] = \"correct\";\n    SoundEffect[\"Done\"] = \"done\";\n    SoundEffect[\"Fail\"] = \"fail\";\n    SoundEffect[\"Happy\"] = \"happy\";\n    SoundEffect[\"Magic\"] = \"magic\";\n    SoundEffect[\"Notify\"] = \"notify\";\n    SoundEffect[\"Scan\"] = \"scan\";\n    SoundEffect[\"ServiceBell\"] = \"servicebell\";\n    SoundEffect[\"Success\"] = \"success\";\n    SoundEffect[\"SuccessBell\"] = \"successbell\";\n    SoundEffect[\"Upsell\"] = \"upsell\";\n    SoundEffect[\"WifiOn\"] = \"wifion\";\n})(SoundEffect || (SoundEffect = {}));\nexport async function playSound(name) {\n    try {\n        await window.$perkd.do('interact.playsound', { name });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport var VibrateEffect;\n(function (VibrateEffect) {\n    VibrateEffect[\"Selection\"] = \"selection\";\n    VibrateEffect[\"ImpactLight\"] = \"impactLight\";\n    VibrateEffect[\"ImpactMedium\"] = \"impactMedium\";\n    VibrateEffect[\"ImpactHeavy\"] = \"impactHeavy\";\n    VibrateEffect[\"NotificationSuccess\"] = \"notificationSuccess\";\n    VibrateEffect[\"NotificationWarning\"] = \"notificationWarning\";\n    VibrateEffect[\"NotificationError\"] = \"notificationError\";\n})(VibrateEffect || (VibrateEffect = {}));\nexport async function vibrate(type) {\n    try {\n        await window.$perkd.do('interact.vibrate', { type });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport var WebMethod;\n(function (WebMethod) {\n    WebMethod[\"NATIVE\"] = \"native\";\n    WebMethod[\"BROWSER\"] = \"browser\";\n    WebMethod[\"IN_APP\"] = \"web\";\n})(WebMethod || (WebMethod = {}));\nexport async function openLink(url, method = WebMethod.BROWSER, context) {\n    try {\n        const param = { [method]: url };\n        if (context) {\n            param.context = context;\n        }\n        await window.$perkd.do('web.open', { param });\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function getWidgetData(widgetName, params = {}) {\n    try {\n        const payload = {\n            key: widgetName\n        };\n        if (params?.id !== undefined) {\n            payload.id = params.id;\n        }\n        const response = await window.$perkd.do('widget.data', payload);\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\nexport async function openHoursSettings(hours) {\n    try {\n        const response = await window.$perkd.do('form.hoursSettings', { hours: hours || { periods: [] } });\n        return response;\n    }\n    catch (error) {\n        return { error };\n    }\n}\n"], "mappings": ";;;;;;;;AAEO,SAAS,cAAc;AAC1B,SAAO,OAAO,GAAG,cAAc;AACnC;AACA,eAAsB,eAAe;AACjC,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,WAAW;AACnD,UAAM,EAAE,UAAU,IAAI,YAAY,CAAC;AACnC,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,eAAe,OAAO;AACxC,QAAM,EAAE,OAAO,QAAQ,QAAQ,YAAY,OAAO,QAAQ,IAAI,SAAS,CAAC;AACxE,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,mBAAmB;AAAA,MACvD,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,mBAAmB,OAAO;AAC5C,QAAM,EAAE,OAAO,QAAQ,QAAQ,YAAY,OAAO,QAAQ,IAAI,SAAS,CAAC;AACxE,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,mBAAmB;AAAA,MACvD,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,mBAAmB,EAAE,QAAQ,IAAI,OAAO,QAAQ,mBAAmB,MAAM,QAAQ,QAAQ,YAAY,MAAM,GAAG;AAEhI,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,mBAAmB;AAAA,MACvD,MAAM;AAAA,MACN;AAAA,MACA,MAAM,SAAS,QAAQ,mBAAmB,WAAW,aAAa;AAAA,MAClE;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,YAAY,SAAS;AACvC,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,cAAc,OAAO;AAC7D,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,YAAY,YAAY,gBAAgB,SAAS,CAAC,GAAG;AACvE,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,aAAa;AAAA,MAChC,OAAO;AAAA,QACH,EAAE,QAAQ,EAAE,MAAM,eAAe,EAAE;AAAA,QACnC,EAAE,CAAC,UAAU,GAAG,OAAO;AAAA,MAC3B;AAAA,IACJ,CAAC;AAAA,EACL,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACO,SAAS,WAAW,QAAQ;AAlFnC;AAmFI,QAAM,EAAE,OAAO,SAAS,SAAS,OAAO,KAAK,IAAI;AACjD,SAAO,OAAO,OAAO,GAAG,mBAAmB;AAAA,IACvC;AAAA,IACA;AAAA,IACA,SAAS,WAAW,QAAQ,SAAS,IAAI,UAAU,CAAC;AAAA,MAC5C,OAAO,kBAAgB,IAAI,KAAK,aAAgB,OAAzC,mBAA8C,OAAO;AAAA,MAC5D,QAAQ,EAAE,QAAQ,UAAU,QAAQ,QAAQ;AAAA,MAC5C,OAAO;AAAA,IACX,CAAC;AAAA,IACL,YAAY;AAAA,EAChB,CAAC;AACL;AACA,eAAsB,QAAQ,OAAO,SAAS;AAC1C,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,qBAAqB;AAAA,MACxC,SAAS;AAAA,MACT,IAAI;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,OAAO,QAAQ;AACjC,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,oBAAoB,EAAE,OAAO,CAAC;AAAA,EACzD,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,SAAS,MAAM;AACjC,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,sBAAsB,EAAE,KAAK,CAAC;AACrD,UAAM,OAAO,OAAO,GAAG,kBAAkB,EAAE,SAAS,KAAK,CAAC;AAAA,EAC9D,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,UAAU,MAAM;AAClC,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,kBAAkB,EAAE,SAAS,KAAK,CAAC;AAAA,EAC9D,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,SAAS,QAAQ;AACnC,MAAI;AACA,UAAM,QAAQ,OAAO,MAAM,IAAI,CAAC,SAAS;AACrC,YAAM,EAAE,SAAS,WAAW,WAAW,KAAK,OAAO,UAAU,QAAQ,WAAW,OAAO,QAAQ,UAAU,cAAc,gBAAgB,OAAO,QAAQ,MAAM,YAAY,SAAS,KAAK,YAAY,MAAM,MAAM,QAAQ,WAAW,UAAU,OAAO,kBAAkB,OAAO,aAAa,UAAU,IAAI;AACtS,aAAO;AAAA,QACH;AAAA,QAAS;AAAA,QAAW;AAAA,QAAW;AAAA,QAAK;AAAA,QAAO;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAc;AAAA,QAC/G;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAY;AAAA,QAAS;AAAA,QAAK;AAAA,QAAY;AAAA,QAAM;AAAA,QAAM;AAAA,QAAQ;AAAA,QAC/E;AAAA,QAAU;AAAA,QAAO;AAAA,QAAkB;AAAA,QAAO;AAAA,QAAa;AAAA,MAC3D;AAAA,IACJ,CAAC;AACD,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,gBAAgB,EAAE,GAAG,QAAQ,MAAM,CAAC;AAC5E,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,UAAU;AAC5B,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,UAAU;AAAA,EACrC,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,cAAc,QAAQ;AACxC,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,kBAAkB,MAAM;AAChE,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,mBAAmB,QAAQ;AAC7C,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,YAAY,MAAM;AAC1D,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,YAAY,MAAM;AACpC,QAAM,EAAE,QAAQ,MAAM,UAAU,QAAQ,GAAG,KAAK,IAAI;AACpD,MAAI,EAAE,QAAQ,WAAW;AACrB,WAAO,EAAE,OAAO,EAAE,eAAe,iBAAiB,EAAE;AAAA,EACxD;AACA,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,KAAK,GAAG,IAAI,IAAI,QAAQ;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,IACb,GAAG;AAAA,EACP;AACA,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,cAAc,OAAO;AAC7D,WAAO;AAAA,EACX,SACO,OAAO;AACV,UAAM,WAAW,GAAG,IAAI,IAAI,QAAQ,IAAI,OAAO,OAAO;AACtD,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AAMA,eAAsB,SAAS,SAAS;AACpC,MAAI;AAEA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,aAAa,OAAO;AAC5D,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AAKA,eAAsB,QAAQ,SAAS;AACnC,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,YAAY,OAAO;AAC3D,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,QAAQ,UAAU,SAAS;AAC7C,MAAI;AAEA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,mBAAmB,EAAE,UAAU,QAAQ,CAAC;AAChF,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,UAAU,MAAM;AAClC,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,cAAc,EAAE,KAAK,CAAC;AAC9D,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,WAAW,SAAS,OAAO,MAAM;AACnD,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,eAAe,EAAE,SAAS,OAAO,KAAK,CAAC;AAAA,EAClE,SACOA,QAAO;AACV,WAAO,EAAE,OAAAA,OAAM;AAAA,EACnB;AACJ;AACO,IAAI;AAAA,CACV,SAAUC,cAAa;AACpB,EAAAA,aAAY,UAAU,IAAI;AAC1B,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,WAAW,IAAI;AAC3B,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,SAAS,IAAI;AACzB,EAAAA,aAAY,OAAO,IAAI;AACvB,EAAAA,aAAY,SAAS,IAAI;AACzB,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,OAAO,IAAI;AACvB,EAAAA,aAAY,OAAO,IAAI;AACvB,EAAAA,aAAY,QAAQ,IAAI;AACxB,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,aAAa,IAAI;AAC7B,EAAAA,aAAY,SAAS,IAAI;AACzB,EAAAA,aAAY,aAAa,IAAI;AAC7B,EAAAA,aAAY,QAAQ,IAAI;AACxB,EAAAA,aAAY,QAAQ,IAAI;AAC5B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,eAAsB,UAAU,MAAM;AAClC,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,sBAAsB,EAAE,KAAK,CAAC;AAAA,EACzD,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACO,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,EAAAA,eAAc,WAAW,IAAI;AAC7B,EAAAA,eAAc,aAAa,IAAI;AAC/B,EAAAA,eAAc,cAAc,IAAI;AAChC,EAAAA,eAAc,aAAa,IAAI;AAC/B,EAAAA,eAAc,qBAAqB,IAAI;AACvC,EAAAA,eAAc,qBAAqB,IAAI;AACvC,EAAAA,eAAc,mBAAmB,IAAI;AACzC,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,eAAsB,QAAQ,MAAM;AAChC,MAAI;AACA,UAAM,OAAO,OAAO,GAAG,oBAAoB,EAAE,KAAK,CAAC;AAAA,EACvD,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACO,IAAI;AAAA,CACV,SAAUC,YAAW;AAClB,EAAAA,WAAU,QAAQ,IAAI;AACtB,EAAAA,WAAU,SAAS,IAAI;AACvB,EAAAA,WAAU,QAAQ,IAAI;AAC1B,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,eAAsB,SAAS,KAAK,SAAS,UAAU,SAAS,SAAS;AACrE,MAAI;AACA,UAAM,QAAQ,EAAE,CAAC,MAAM,GAAG,IAAI;AAC9B,QAAI,SAAS;AACT,YAAM,UAAU;AAAA,IACpB;AACA,UAAM,OAAO,OAAO,GAAG,YAAY,EAAE,MAAM,CAAC;AAAA,EAChD,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,cAAc,YAAY,SAAS,CAAC,GAAG;AACzD,MAAI;AACA,UAAM,UAAU;AAAA,MACZ,KAAK;AAAA,IACT;AACA,SAAI,iCAAQ,QAAO,QAAW;AAC1B,cAAQ,KAAK,OAAO;AAAA,IACxB;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,eAAe,OAAO;AAC9D,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;AACA,eAAsB,kBAAkB,OAAO;AAC3C,MAAI;AACA,UAAM,WAAW,MAAM,OAAO,OAAO,GAAG,sBAAsB,EAAE,OAAO,SAAS,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC;AACjG,WAAO;AAAA,EACX,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACJ;", "names": ["error", "SoundEffect", "VibrateEffect", "WebMethod"]}