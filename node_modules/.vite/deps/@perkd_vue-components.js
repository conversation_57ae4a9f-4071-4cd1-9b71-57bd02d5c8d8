import {
  ve
} from "./chunk-S4BG457E.js";
import {
  I
} from "./chunk-HZAHFGFP.js";
import {
  createI18n,
  useI18n
} from "./chunk-ILBRJJGN.js";
import "./chunk-YFT6OQ5R.js";
import {
  B,
  Q,
  h as h2
} from "./chunk-54FI6OKY.js";
import {
  i
} from "./chunk-QMSMXSBA.js";
import {
  Fragment,
  Transition,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineAsyncComponent,
  defineComponent,
  guardReactiveProps,
  h,
  mergeProps,
  nextTick,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  openBlock,
  reactive,
  ref,
  renderList,
  renderSlot,
  resolveDynamicComponent,
  shallowRef,
  toDisplayString,
  toRefs,
  unref,
  vModelCheckbox,
  vShow,
  watch,
  withCtx,
  withDirectives,
  withModifiers
} from "./chunk-U3LI7FBV.js";
import {
  colorToRgbo
} from "./chunk-BA27V7OM.js";
import {
  require_dist
} from "./chunk-3UCQLEQJ.js";
import "./chunk-2KOGM4NU.js";
import {
  Cards,
  debounce,
  isEmail,
  isErrorResponse,
  isUrl
} from "./chunk-QVDR62FV.js";
import "./chunk-6B6YBE7R.js";
import {
  __commonJS,
  __toESM
} from "./chunk-EQCVQC35.js";

// node_modules/jsbarcode/bin/barcodes/Barcode.js
var require_Barcode = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/Barcode.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    var Barcode = function Barcode2(data, options) {
      _classCallCheck(this, Barcode2);
      this.data = data;
      this.text = options.text || data;
      this.options = options;
    };
    exports.default = Barcode;
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE39/index.js
var require_CODE39 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE39/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.CODE39 = void 0;
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var CODE39 = function(_Barcode) {
      _inherits(CODE392, _Barcode);
      function CODE392(data, options) {
        _classCallCheck(this, CODE392);
        data = data.toUpperCase();
        if (options.mod43) {
          data += getCharacter(mod43checksum(data));
        }
        return _possibleConstructorReturn(this, (CODE392.__proto__ || Object.getPrototypeOf(CODE392)).call(this, data, options));
      }
      _createClass(CODE392, [{
        key: "encode",
        value: function encode() {
          var result = getEncoding("*");
          for (var i3 = 0; i3 < this.data.length; i3++) {
            result += getEncoding(this.data[i3]) + "0";
          }
          result += getEncoding("*");
          return {
            data: result,
            text: this.text
          };
        }
      }, {
        key: "valid",
        value: function valid() {
          return this.data.search(/^[0-9A-Z\-\.\ \$\/\+\%]+$/) !== -1;
        }
      }]);
      return CODE392;
    }(_Barcode3.default);
    var characters = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "-", ".", " ", "$", "/", "+", "%", "*"];
    var encodings = [20957, 29783, 23639, 30485, 20951, 29813, 23669, 20855, 29789, 23645, 29975, 23831, 30533, 22295, 30149, 24005, 21623, 29981, 23837, 22301, 30023, 23879, 30545, 22343, 30161, 24017, 21959, 30065, 23921, 22385, 29015, 18263, 29141, 17879, 29045, 18293, 17783, 29021, 18269, 17477, 17489, 17681, 20753, 35770];
    function getEncoding(character) {
      return getBinary(characterValue(character));
    }
    function getBinary(characterValue2) {
      return encodings[characterValue2].toString(2);
    }
    function getCharacter(characterValue2) {
      return characters[characterValue2];
    }
    function characterValue(character) {
      return characters.indexOf(character);
    }
    function mod43checksum(data) {
      var checksum = 0;
      for (var i3 = 0; i3 < data.length; i3++) {
        checksum += characterValue(data[i3]);
      }
      checksum = checksum % 43;
      return checksum;
    }
    exports.CODE39 = CODE39;
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE128/constants.js
var require_constants = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE128/constants.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _SET_BY_CODE;
    function _defineProperty(obj, key, value) {
      if (key in obj) {
        Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
      } else {
        obj[key] = value;
      }
      return obj;
    }
    var SET_A = exports.SET_A = 0;
    var SET_B = exports.SET_B = 1;
    var SET_C = exports.SET_C = 2;
    var SHIFT = exports.SHIFT = 98;
    var START_A = exports.START_A = 103;
    var START_B = exports.START_B = 104;
    var START_C = exports.START_C = 105;
    var MODULO = exports.MODULO = 103;
    var STOP = exports.STOP = 106;
    var FNC1 = exports.FNC1 = 207;
    var SET_BY_CODE = exports.SET_BY_CODE = (_SET_BY_CODE = {}, _defineProperty(_SET_BY_CODE, START_A, SET_A), _defineProperty(_SET_BY_CODE, START_B, SET_B), _defineProperty(_SET_BY_CODE, START_C, SET_C), _SET_BY_CODE);
    var SWAP = exports.SWAP = {
      101: SET_A,
      100: SET_B,
      99: SET_C
    };
    var A_START_CHAR = exports.A_START_CHAR = String.fromCharCode(208);
    var B_START_CHAR = exports.B_START_CHAR = String.fromCharCode(209);
    var C_START_CHAR = exports.C_START_CHAR = String.fromCharCode(210);
    var A_CHARS = exports.A_CHARS = "[\0-_È-Ï]";
    var B_CHARS = exports.B_CHARS = "[ -È-Ï]";
    var C_CHARS = exports.C_CHARS = "(Ï*[0-9]{2}Ï*)";
    var BARS = exports.BARS = [11011001100, 11001101100, 11001100110, 10010011e3, 10010001100, 10001001100, 10011001e3, 10011000100, 10001100100, 11001001e3, 11001000100, 11000100100, 10110011100, 10011011100, 10011001110, 10111001100, 10011101100, 10011100110, 11001110010, 11001011100, 11001001110, 11011100100, 11001110100, 11101101110, 11101001100, 11100101100, 11100100110, 11101100100, 11100110100, 11100110010, 11011011e3, 11011000110, 11000110110, 10100011e3, 10001011e3, 10001000110, 10110001e3, 10001101e3, 10001100010, 11010001e3, 11000101e3, 11000100010, 10110111e3, 10110001110, 10001101110, 10111011e3, 10111000110, 10001110110, 11101110110, 11010001110, 11000101110, 11011101e3, 11011100010, 11011101110, 11101011e3, 11101000110, 11100010110, 11101101e3, 11101100010, 11100011010, 11101111010, 11001000010, 11110001010, 1010011e4, 10100001100, 1001011e4, 10010000110, 10000101100, 10000100110, 1011001e4, 10110000100, 1001101e4, 10011000010, 10000110100, 10000110010, 11000010010, 1100101e4, 11110111010, 11000010100, 10001111010, 10100111100, 10010111100, 10010011110, 10111100100, 10011110100, 10011110010, 11110100100, 11110010100, 11110010010, 11011011110, 11011110110, 11110110110, 10101111e3, 10100011110, 10001011110, 10111101e3, 10111100010, 11110101e3, 11110100010, 10111011110, 10111101110, 11101011110, 11110101110, 11010000100, 1101001e4, 11010011100, 1100011101011];
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE128/CODE128.js
var require_CODE128 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE128/CODE128.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    var _constants = require_constants();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var CODE128 = function(_Barcode) {
      _inherits(CODE1282, _Barcode);
      function CODE1282(data, options) {
        _classCallCheck(this, CODE1282);
        var _this = _possibleConstructorReturn(this, (CODE1282.__proto__ || Object.getPrototypeOf(CODE1282)).call(this, data.substring(1), options));
        _this.bytes = data.split("").map(function(char) {
          return char.charCodeAt(0);
        });
        return _this;
      }
      _createClass(CODE1282, [{
        key: "valid",
        value: function valid() {
          return /^[\x00-\x7F\xC8-\xD3]+$/.test(this.data);
        }
        // The public encoding function
      }, {
        key: "encode",
        value: function encode() {
          var bytes = this.bytes;
          var startIndex = bytes.shift() - 105;
          var startSet = _constants.SET_BY_CODE[startIndex];
          if (startSet === void 0) {
            throw new RangeError("The encoding does not start with a start character.");
          }
          if (this.shouldEncodeAsEan128() === true) {
            bytes.unshift(_constants.FNC1);
          }
          var encodingResult = CODE1282.next(bytes, 1, startSet);
          return {
            text: this.text === this.data ? this.text.replace(/[^\x20-\x7E]/g, "") : this.text,
            data: (
              // Add the start bits
              CODE1282.getBar(startIndex) + // Add the encoded bits
              encodingResult.result + // Add the checksum
              CODE1282.getBar((encodingResult.checksum + startIndex) % _constants.MODULO) + // Add the end bits
              CODE1282.getBar(_constants.STOP)
            )
          };
        }
        // GS1-128/EAN-128
      }, {
        key: "shouldEncodeAsEan128",
        value: function shouldEncodeAsEan128() {
          var isEAN128 = this.options.ean128 || false;
          if (typeof isEAN128 === "string") {
            isEAN128 = isEAN128.toLowerCase() === "true";
          }
          return isEAN128;
        }
        // Get a bar symbol by index
      }], [{
        key: "getBar",
        value: function getBar(index) {
          return _constants.BARS[index] ? _constants.BARS[index].toString() : "";
        }
        // Correct an index by a set and shift it from the bytes array
      }, {
        key: "correctIndex",
        value: function correctIndex(bytes, set) {
          if (set === _constants.SET_A) {
            var charCode = bytes.shift();
            return charCode < 32 ? charCode + 64 : charCode - 32;
          } else if (set === _constants.SET_B) {
            return bytes.shift() - 32;
          } else {
            return (bytes.shift() - 48) * 10 + bytes.shift() - 48;
          }
        }
      }, {
        key: "next",
        value: function next(bytes, pos, set) {
          if (!bytes.length) {
            return { result: "", checksum: 0 };
          }
          var nextCode = void 0, index = void 0;
          if (bytes[0] >= 200) {
            index = bytes.shift() - 105;
            var nextSet = _constants.SWAP[index];
            if (nextSet !== void 0) {
              nextCode = CODE1282.next(bytes, pos + 1, nextSet);
            } else {
              if ((set === _constants.SET_A || set === _constants.SET_B) && index === _constants.SHIFT) {
                bytes[0] = set === _constants.SET_A ? bytes[0] > 95 ? bytes[0] - 96 : bytes[0] : bytes[0] < 32 ? bytes[0] + 96 : bytes[0];
              }
              nextCode = CODE1282.next(bytes, pos + 1, set);
            }
          } else {
            index = CODE1282.correctIndex(bytes, set);
            nextCode = CODE1282.next(bytes, pos + 1, set);
          }
          var enc = CODE1282.getBar(index);
          var weight = index * pos;
          return {
            result: enc + nextCode.result,
            checksum: weight + nextCode.checksum
          };
        }
      }]);
      return CODE1282;
    }(_Barcode3.default);
    exports.default = CODE128;
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE128/auto.js
var require_auto = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE128/auto.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _constants = require_constants();
    var matchSetALength = function matchSetALength2(string) {
      return string.match(new RegExp("^" + _constants.A_CHARS + "*"))[0].length;
    };
    var matchSetBLength = function matchSetBLength2(string) {
      return string.match(new RegExp("^" + _constants.B_CHARS + "*"))[0].length;
    };
    var matchSetC = function matchSetC2(string) {
      return string.match(new RegExp("^" + _constants.C_CHARS + "*"))[0];
    };
    function autoSelectFromAB(string, isA) {
      var ranges = isA ? _constants.A_CHARS : _constants.B_CHARS;
      var untilC = string.match(new RegExp("^(" + ranges + "+?)(([0-9]{2}){2,})([^0-9]|$)"));
      if (untilC) {
        return untilC[1] + String.fromCharCode(204) + autoSelectFromC(string.substring(untilC[1].length));
      }
      var chars = string.match(new RegExp("^" + ranges + "+"))[0];
      if (chars.length === string.length) {
        return string;
      }
      return chars + String.fromCharCode(isA ? 205 : 206) + autoSelectFromAB(string.substring(chars.length), !isA);
    }
    function autoSelectFromC(string) {
      var cMatch = matchSetC(string);
      var length = cMatch.length;
      if (length === string.length) {
        return string;
      }
      string = string.substring(length);
      var isA = matchSetALength(string) >= matchSetBLength(string);
      return cMatch + String.fromCharCode(isA ? 206 : 205) + autoSelectFromAB(string, isA);
    }
    exports.default = function(string) {
      var newString = void 0;
      var cLength = matchSetC(string).length;
      if (cLength >= 2) {
        newString = _constants.C_START_CHAR + autoSelectFromC(string);
      } else {
        var isA = matchSetALength(string) > matchSetBLength(string);
        newString = (isA ? _constants.A_START_CHAR : _constants.B_START_CHAR) + autoSelectFromAB(string, isA);
      }
      return newString.replace(
        /[\xCD\xCE]([^])[\xCD\xCE]/,
        // Any sequence between 205 and 206 characters
        function(match, char) {
          return String.fromCharCode(203) + char;
        }
      );
    };
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE128/CODE128_AUTO.js
var require_CODE128_AUTO = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE128/CODE128_AUTO.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _CODE2 = require_CODE128();
    var _CODE3 = _interopRequireDefault(_CODE2);
    var _auto = require_auto();
    var _auto2 = _interopRequireDefault(_auto);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var CODE128AUTO = function(_CODE) {
      _inherits(CODE128AUTO2, _CODE);
      function CODE128AUTO2(data, options) {
        _classCallCheck(this, CODE128AUTO2);
        if (/^[\x00-\x7F\xC8-\xD3]+$/.test(data)) {
          var _this = _possibleConstructorReturn(this, (CODE128AUTO2.__proto__ || Object.getPrototypeOf(CODE128AUTO2)).call(this, (0, _auto2.default)(data), options));
        } else {
          var _this = _possibleConstructorReturn(this, (CODE128AUTO2.__proto__ || Object.getPrototypeOf(CODE128AUTO2)).call(this, data, options));
        }
        return _possibleConstructorReturn(_this);
      }
      return CODE128AUTO2;
    }(_CODE3.default);
    exports.default = CODE128AUTO;
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE128/CODE128A.js
var require_CODE128A = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE128/CODE128A.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _CODE2 = require_CODE128();
    var _CODE3 = _interopRequireDefault(_CODE2);
    var _constants = require_constants();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var CODE128A = function(_CODE) {
      _inherits(CODE128A2, _CODE);
      function CODE128A2(string, options) {
        _classCallCheck(this, CODE128A2);
        return _possibleConstructorReturn(this, (CODE128A2.__proto__ || Object.getPrototypeOf(CODE128A2)).call(this, _constants.A_START_CHAR + string, options));
      }
      _createClass(CODE128A2, [{
        key: "valid",
        value: function valid() {
          return new RegExp("^" + _constants.A_CHARS + "+$").test(this.data);
        }
      }]);
      return CODE128A2;
    }(_CODE3.default);
    exports.default = CODE128A;
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE128/CODE128B.js
var require_CODE128B = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE128/CODE128B.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _CODE2 = require_CODE128();
    var _CODE3 = _interopRequireDefault(_CODE2);
    var _constants = require_constants();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var CODE128B = function(_CODE) {
      _inherits(CODE128B2, _CODE);
      function CODE128B2(string, options) {
        _classCallCheck(this, CODE128B2);
        return _possibleConstructorReturn(this, (CODE128B2.__proto__ || Object.getPrototypeOf(CODE128B2)).call(this, _constants.B_START_CHAR + string, options));
      }
      _createClass(CODE128B2, [{
        key: "valid",
        value: function valid() {
          return new RegExp("^" + _constants.B_CHARS + "+$").test(this.data);
        }
      }]);
      return CODE128B2;
    }(_CODE3.default);
    exports.default = CODE128B;
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE128/CODE128C.js
var require_CODE128C = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE128/CODE128C.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _CODE2 = require_CODE128();
    var _CODE3 = _interopRequireDefault(_CODE2);
    var _constants = require_constants();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var CODE128C = function(_CODE) {
      _inherits(CODE128C2, _CODE);
      function CODE128C2(string, options) {
        _classCallCheck(this, CODE128C2);
        return _possibleConstructorReturn(this, (CODE128C2.__proto__ || Object.getPrototypeOf(CODE128C2)).call(this, _constants.C_START_CHAR + string, options));
      }
      _createClass(CODE128C2, [{
        key: "valid",
        value: function valid() {
          return new RegExp("^" + _constants.C_CHARS + "+$").test(this.data);
        }
      }]);
      return CODE128C2;
    }(_CODE3.default);
    exports.default = CODE128C;
  }
});

// node_modules/jsbarcode/bin/barcodes/CODE128/index.js
var require_CODE1282 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/CODE128/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.CODE128C = exports.CODE128B = exports.CODE128A = exports.CODE128 = void 0;
    var _CODE128_AUTO = require_CODE128_AUTO();
    var _CODE128_AUTO2 = _interopRequireDefault(_CODE128_AUTO);
    var _CODE128A = require_CODE128A();
    var _CODE128A2 = _interopRequireDefault(_CODE128A);
    var _CODE128B = require_CODE128B();
    var _CODE128B2 = _interopRequireDefault(_CODE128B);
    var _CODE128C = require_CODE128C();
    var _CODE128C2 = _interopRequireDefault(_CODE128C);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    exports.CODE128 = _CODE128_AUTO2.default;
    exports.CODE128A = _CODE128A2.default;
    exports.CODE128B = _CODE128B2.default;
    exports.CODE128C = _CODE128C2.default;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/constants.js
var require_constants2 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/constants.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var SIDE_BIN = exports.SIDE_BIN = "101";
    var MIDDLE_BIN = exports.MIDDLE_BIN = "01010";
    var BINARIES = exports.BINARIES = {
      "L": [
        // The L (left) type of encoding
        "0001101",
        "0011001",
        "0010011",
        "0111101",
        "0100011",
        "0110001",
        "0101111",
        "0111011",
        "0110111",
        "0001011"
      ],
      "G": [
        // The G type of encoding
        "0100111",
        "0110011",
        "0011011",
        "0100001",
        "0011101",
        "0111001",
        "0000101",
        "0010001",
        "0001001",
        "0010111"
      ],
      "R": [
        // The R (right) type of encoding
        "1110010",
        "1100110",
        "1101100",
        "1000010",
        "1011100",
        "1001110",
        "1010000",
        "1000100",
        "1001000",
        "1110100"
      ],
      "O": [
        // The O (odd) encoding for UPC-E
        "0001101",
        "0011001",
        "0010011",
        "0111101",
        "0100011",
        "0110001",
        "0101111",
        "0111011",
        "0110111",
        "0001011"
      ],
      "E": [
        // The E (even) encoding for UPC-E
        "0100111",
        "0110011",
        "0011011",
        "0100001",
        "0011101",
        "0111001",
        "0000101",
        "0010001",
        "0001001",
        "0010111"
      ]
    };
    var EAN2_STRUCTURE = exports.EAN2_STRUCTURE = ["LL", "LG", "GL", "GG"];
    var EAN5_STRUCTURE = exports.EAN5_STRUCTURE = ["GGLLL", "GLGLL", "GLLGL", "GLLLG", "LGGLL", "LLGGL", "LLLGG", "LGLGL", "LGLLG", "LLGLG"];
    var EAN13_STRUCTURE = exports.EAN13_STRUCTURE = ["LLLLLL", "LLGLGG", "LLGGLG", "LLGGGL", "LGLLGG", "LGGLLG", "LGGGLL", "LGLGLG", "LGLGGL", "LGGLGL"];
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/encoder.js
var require_encoder = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/encoder.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _constants = require_constants2();
    var encode = function encode2(data, structure, separator) {
      var encoded = data.split("").map(function(val, idx) {
        return _constants.BINARIES[structure[idx]];
      }).map(function(val, idx) {
        return val ? val[data[idx]] : "";
      });
      if (separator) {
        var last = data.length - 1;
        encoded = encoded.map(function(val, idx) {
          return idx < last ? val + separator : val;
        });
      }
      return encoded.join("");
    };
    exports.default = encode;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN.js
var require_EAN = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _constants = require_constants2();
    var _encoder = require_encoder();
    var _encoder2 = _interopRequireDefault(_encoder);
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var EAN = function(_Barcode) {
      _inherits(EAN2, _Barcode);
      function EAN2(data, options) {
        _classCallCheck(this, EAN2);
        var _this = _possibleConstructorReturn(this, (EAN2.__proto__ || Object.getPrototypeOf(EAN2)).call(this, data, options));
        _this.fontSize = !options.flat && options.fontSize > options.width * 10 ? options.width * 10 : options.fontSize;
        _this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;
        return _this;
      }
      _createClass(EAN2, [{
        key: "encode",
        value: function encode() {
          return this.options.flat ? this.encodeFlat() : this.encodeGuarded();
        }
      }, {
        key: "leftText",
        value: function leftText(from, to) {
          return this.text.substr(from, to);
        }
      }, {
        key: "leftEncode",
        value: function leftEncode(data, structure) {
          return (0, _encoder2.default)(data, structure);
        }
      }, {
        key: "rightText",
        value: function rightText(from, to) {
          return this.text.substr(from, to);
        }
      }, {
        key: "rightEncode",
        value: function rightEncode(data, structure) {
          return (0, _encoder2.default)(data, structure);
        }
      }, {
        key: "encodeGuarded",
        value: function encodeGuarded() {
          var textOptions = { fontSize: this.fontSize };
          var guardOptions = { height: this.guardHeight };
          return [{ data: _constants.SIDE_BIN, options: guardOptions }, { data: this.leftEncode(), text: this.leftText(), options: textOptions }, { data: _constants.MIDDLE_BIN, options: guardOptions }, { data: this.rightEncode(), text: this.rightText(), options: textOptions }, { data: _constants.SIDE_BIN, options: guardOptions }];
        }
      }, {
        key: "encodeFlat",
        value: function encodeFlat() {
          var data = [_constants.SIDE_BIN, this.leftEncode(), _constants.MIDDLE_BIN, this.rightEncode(), _constants.SIDE_BIN];
          return {
            data: data.join(""),
            text: this.text
          };
        }
      }]);
      return EAN2;
    }(_Barcode3.default);
    exports.default = EAN;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN13.js
var require_EAN13 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN13.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _get = function get(object, property, receiver) {
      if (object === null) object = Function.prototype;
      var desc = Object.getOwnPropertyDescriptor(object, property);
      if (desc === void 0) {
        var parent = Object.getPrototypeOf(object);
        if (parent === null) {
          return void 0;
        } else {
          return get(parent, property, receiver);
        }
      } else if ("value" in desc) {
        return desc.value;
      } else {
        var getter = desc.get;
        if (getter === void 0) {
          return void 0;
        }
        return getter.call(receiver);
      }
    };
    var _constants = require_constants2();
    var _EAN2 = require_EAN();
    var _EAN3 = _interopRequireDefault(_EAN2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var checksum = function checksum2(number) {
      var res = number.substr(0, 12).split("").map(function(n) {
        return +n;
      }).reduce(function(sum, a2, idx) {
        return idx % 2 ? sum + a2 * 3 : sum + a2;
      }, 0);
      return (10 - res % 10) % 10;
    };
    var EAN13 = function(_EAN) {
      _inherits(EAN132, _EAN);
      function EAN132(data, options) {
        _classCallCheck(this, EAN132);
        if (data.search(/^[0-9]{12}$/) !== -1) {
          data += checksum(data);
        }
        var _this = _possibleConstructorReturn(this, (EAN132.__proto__ || Object.getPrototypeOf(EAN132)).call(this, data, options));
        _this.lastChar = options.lastChar;
        return _this;
      }
      _createClass(EAN132, [{
        key: "valid",
        value: function valid() {
          return this.data.search(/^[0-9]{13}$/) !== -1 && +this.data[12] === checksum(this.data);
        }
      }, {
        key: "leftText",
        value: function leftText() {
          return _get(EAN132.prototype.__proto__ || Object.getPrototypeOf(EAN132.prototype), "leftText", this).call(this, 1, 6);
        }
      }, {
        key: "leftEncode",
        value: function leftEncode() {
          var data = this.data.substr(1, 6);
          var structure = _constants.EAN13_STRUCTURE[this.data[0]];
          return _get(EAN132.prototype.__proto__ || Object.getPrototypeOf(EAN132.prototype), "leftEncode", this).call(this, data, structure);
        }
      }, {
        key: "rightText",
        value: function rightText() {
          return _get(EAN132.prototype.__proto__ || Object.getPrototypeOf(EAN132.prototype), "rightText", this).call(this, 7, 6);
        }
      }, {
        key: "rightEncode",
        value: function rightEncode() {
          var data = this.data.substr(7, 6);
          return _get(EAN132.prototype.__proto__ || Object.getPrototypeOf(EAN132.prototype), "rightEncode", this).call(this, data, "RRRRRR");
        }
        // The "standard" way of printing EAN13 barcodes with guard bars
      }, {
        key: "encodeGuarded",
        value: function encodeGuarded() {
          var data = _get(EAN132.prototype.__proto__ || Object.getPrototypeOf(EAN132.prototype), "encodeGuarded", this).call(this);
          if (this.options.displayValue) {
            data.unshift({
              data: "000000000000",
              text: this.text.substr(0, 1),
              options: { textAlign: "left", fontSize: this.fontSize }
            });
            if (this.options.lastChar) {
              data.push({
                data: "00"
              });
              data.push({
                data: "00000",
                text: this.options.lastChar,
                options: { fontSize: this.fontSize }
              });
            }
          }
          return data;
        }
      }]);
      return EAN132;
    }(_EAN3.default);
    exports.default = EAN13;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN8.js
var require_EAN8 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN8.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _get = function get(object, property, receiver) {
      if (object === null) object = Function.prototype;
      var desc = Object.getOwnPropertyDescriptor(object, property);
      if (desc === void 0) {
        var parent = Object.getPrototypeOf(object);
        if (parent === null) {
          return void 0;
        } else {
          return get(parent, property, receiver);
        }
      } else if ("value" in desc) {
        return desc.value;
      } else {
        var getter = desc.get;
        if (getter === void 0) {
          return void 0;
        }
        return getter.call(receiver);
      }
    };
    var _EAN2 = require_EAN();
    var _EAN3 = _interopRequireDefault(_EAN2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var checksum = function checksum2(number) {
      var res = number.substr(0, 7).split("").map(function(n) {
        return +n;
      }).reduce(function(sum, a2, idx) {
        return idx % 2 ? sum + a2 : sum + a2 * 3;
      }, 0);
      return (10 - res % 10) % 10;
    };
    var EAN8 = function(_EAN) {
      _inherits(EAN82, _EAN);
      function EAN82(data, options) {
        _classCallCheck(this, EAN82);
        if (data.search(/^[0-9]{7}$/) !== -1) {
          data += checksum(data);
        }
        return _possibleConstructorReturn(this, (EAN82.__proto__ || Object.getPrototypeOf(EAN82)).call(this, data, options));
      }
      _createClass(EAN82, [{
        key: "valid",
        value: function valid() {
          return this.data.search(/^[0-9]{8}$/) !== -1 && +this.data[7] === checksum(this.data);
        }
      }, {
        key: "leftText",
        value: function leftText() {
          return _get(EAN82.prototype.__proto__ || Object.getPrototypeOf(EAN82.prototype), "leftText", this).call(this, 0, 4);
        }
      }, {
        key: "leftEncode",
        value: function leftEncode() {
          var data = this.data.substr(0, 4);
          return _get(EAN82.prototype.__proto__ || Object.getPrototypeOf(EAN82.prototype), "leftEncode", this).call(this, data, "LLLL");
        }
      }, {
        key: "rightText",
        value: function rightText() {
          return _get(EAN82.prototype.__proto__ || Object.getPrototypeOf(EAN82.prototype), "rightText", this).call(this, 4, 4);
        }
      }, {
        key: "rightEncode",
        value: function rightEncode() {
          var data = this.data.substr(4, 4);
          return _get(EAN82.prototype.__proto__ || Object.getPrototypeOf(EAN82.prototype), "rightEncode", this).call(this, data, "RRRR");
        }
      }]);
      return EAN82;
    }(_EAN3.default);
    exports.default = EAN8;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN5.js
var require_EAN5 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN5.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _constants = require_constants2();
    var _encoder = require_encoder();
    var _encoder2 = _interopRequireDefault(_encoder);
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var checksum = function checksum2(data) {
      var result = data.split("").map(function(n) {
        return +n;
      }).reduce(function(sum, a2, idx) {
        return idx % 2 ? sum + a2 * 9 : sum + a2 * 3;
      }, 0);
      return result % 10;
    };
    var EAN5 = function(_Barcode) {
      _inherits(EAN52, _Barcode);
      function EAN52(data, options) {
        _classCallCheck(this, EAN52);
        return _possibleConstructorReturn(this, (EAN52.__proto__ || Object.getPrototypeOf(EAN52)).call(this, data, options));
      }
      _createClass(EAN52, [{
        key: "valid",
        value: function valid() {
          return this.data.search(/^[0-9]{5}$/) !== -1;
        }
      }, {
        key: "encode",
        value: function encode() {
          var structure = _constants.EAN5_STRUCTURE[checksum(this.data)];
          return {
            data: "1011" + (0, _encoder2.default)(this.data, structure, "01"),
            text: this.text
          };
        }
      }]);
      return EAN52;
    }(_Barcode3.default);
    exports.default = EAN5;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN2.js
var require_EAN2 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN2.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _constants = require_constants2();
    var _encoder = require_encoder();
    var _encoder2 = _interopRequireDefault(_encoder);
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var EAN2 = function(_Barcode) {
      _inherits(EAN22, _Barcode);
      function EAN22(data, options) {
        _classCallCheck(this, EAN22);
        return _possibleConstructorReturn(this, (EAN22.__proto__ || Object.getPrototypeOf(EAN22)).call(this, data, options));
      }
      _createClass(EAN22, [{
        key: "valid",
        value: function valid() {
          return this.data.search(/^[0-9]{2}$/) !== -1;
        }
      }, {
        key: "encode",
        value: function encode() {
          var structure = _constants.EAN2_STRUCTURE[parseInt(this.data) % 4];
          return {
            // Start bits + Encode the two digits with 01 in between
            data: "1011" + (0, _encoder2.default)(this.data, structure, "01"),
            text: this.text
          };
        }
      }]);
      return EAN22;
    }(_Barcode3.default);
    exports.default = EAN2;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/UPC.js
var require_UPC = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/UPC.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    exports.checksum = checksum;
    var _encoder = require_encoder();
    var _encoder2 = _interopRequireDefault(_encoder);
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var UPC = function(_Barcode) {
      _inherits(UPC2, _Barcode);
      function UPC2(data, options) {
        _classCallCheck(this, UPC2);
        if (data.search(/^[0-9]{11}$/) !== -1) {
          data += checksum(data);
        }
        var _this = _possibleConstructorReturn(this, (UPC2.__proto__ || Object.getPrototypeOf(UPC2)).call(this, data, options));
        _this.displayValue = options.displayValue;
        if (options.fontSize > options.width * 10) {
          _this.fontSize = options.width * 10;
        } else {
          _this.fontSize = options.fontSize;
        }
        _this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;
        return _this;
      }
      _createClass(UPC2, [{
        key: "valid",
        value: function valid() {
          return this.data.search(/^[0-9]{12}$/) !== -1 && this.data[11] == checksum(this.data);
        }
      }, {
        key: "encode",
        value: function encode() {
          if (this.options.flat) {
            return this.flatEncoding();
          } else {
            return this.guardedEncoding();
          }
        }
      }, {
        key: "flatEncoding",
        value: function flatEncoding() {
          var result = "";
          result += "101";
          result += (0, _encoder2.default)(this.data.substr(0, 6), "LLLLLL");
          result += "01010";
          result += (0, _encoder2.default)(this.data.substr(6, 6), "RRRRRR");
          result += "101";
          return {
            data: result,
            text: this.text
          };
        }
      }, {
        key: "guardedEncoding",
        value: function guardedEncoding() {
          var result = [];
          if (this.displayValue) {
            result.push({
              data: "00000000",
              text: this.text.substr(0, 1),
              options: { textAlign: "left", fontSize: this.fontSize }
            });
          }
          result.push({
            data: "101" + (0, _encoder2.default)(this.data[0], "L"),
            options: { height: this.guardHeight }
          });
          result.push({
            data: (0, _encoder2.default)(this.data.substr(1, 5), "LLLLL"),
            text: this.text.substr(1, 5),
            options: { fontSize: this.fontSize }
          });
          result.push({
            data: "01010",
            options: { height: this.guardHeight }
          });
          result.push({
            data: (0, _encoder2.default)(this.data.substr(6, 5), "RRRRR"),
            text: this.text.substr(6, 5),
            options: { fontSize: this.fontSize }
          });
          result.push({
            data: (0, _encoder2.default)(this.data[11], "R") + "101",
            options: { height: this.guardHeight }
          });
          if (this.displayValue) {
            result.push({
              data: "00000000",
              text: this.text.substr(11, 1),
              options: { textAlign: "right", fontSize: this.fontSize }
            });
          }
          return result;
        }
      }]);
      return UPC2;
    }(_Barcode3.default);
    function checksum(number) {
      var result = 0;
      var i3;
      for (i3 = 1; i3 < 11; i3 += 2) {
        result += parseInt(number[i3]);
      }
      for (i3 = 0; i3 < 11; i3 += 2) {
        result += parseInt(number[i3]) * 3;
      }
      return (10 - result % 10) % 10;
    }
    exports.default = UPC;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/UPCE.js
var require_UPCE = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/UPCE.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _encoder = require_encoder();
    var _encoder2 = _interopRequireDefault(_encoder);
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    var _UPC = require_UPC();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var EXPANSIONS = ["XX00000XXX", "XX10000XXX", "XX20000XXX", "XXX00000XX", "XXXX00000X", "XXXXX00005", "XXXXX00006", "XXXXX00007", "XXXXX00008", "XXXXX00009"];
    var PARITIES = [["EEEOOO", "OOOEEE"], ["EEOEOO", "OOEOEE"], ["EEOOEO", "OOEEOE"], ["EEOOOE", "OOEEEO"], ["EOEEOO", "OEOOEE"], ["EOOEEO", "OEEOOE"], ["EOOOEE", "OEEEOO"], ["EOEOEO", "OEOEOE"], ["EOEOOE", "OEOEEO"], ["EOOEOE", "OEEOEO"]];
    var UPCE = function(_Barcode) {
      _inherits(UPCE2, _Barcode);
      function UPCE2(data, options) {
        _classCallCheck(this, UPCE2);
        var _this = _possibleConstructorReturn(this, (UPCE2.__proto__ || Object.getPrototypeOf(UPCE2)).call(this, data, options));
        _this.isValid = false;
        if (data.search(/^[0-9]{6}$/) !== -1) {
          _this.middleDigits = data;
          _this.upcA = expandToUPCA(data, "0");
          _this.text = options.text || "" + _this.upcA[0] + data + _this.upcA[_this.upcA.length - 1];
          _this.isValid = true;
        } else if (data.search(/^[01][0-9]{7}$/) !== -1) {
          _this.middleDigits = data.substring(1, data.length - 1);
          _this.upcA = expandToUPCA(_this.middleDigits, data[0]);
          if (_this.upcA[_this.upcA.length - 1] === data[data.length - 1]) {
            _this.isValid = true;
          } else {
            return _possibleConstructorReturn(_this);
          }
        } else {
          return _possibleConstructorReturn(_this);
        }
        _this.displayValue = options.displayValue;
        if (options.fontSize > options.width * 10) {
          _this.fontSize = options.width * 10;
        } else {
          _this.fontSize = options.fontSize;
        }
        _this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;
        return _this;
      }
      _createClass(UPCE2, [{
        key: "valid",
        value: function valid() {
          return this.isValid;
        }
      }, {
        key: "encode",
        value: function encode() {
          if (this.options.flat) {
            return this.flatEncoding();
          } else {
            return this.guardedEncoding();
          }
        }
      }, {
        key: "flatEncoding",
        value: function flatEncoding() {
          var result = "";
          result += "101";
          result += this.encodeMiddleDigits();
          result += "010101";
          return {
            data: result,
            text: this.text
          };
        }
      }, {
        key: "guardedEncoding",
        value: function guardedEncoding() {
          var result = [];
          if (this.displayValue) {
            result.push({
              data: "00000000",
              text: this.text[0],
              options: { textAlign: "left", fontSize: this.fontSize }
            });
          }
          result.push({
            data: "101",
            options: { height: this.guardHeight }
          });
          result.push({
            data: this.encodeMiddleDigits(),
            text: this.text.substring(1, 7),
            options: { fontSize: this.fontSize }
          });
          result.push({
            data: "010101",
            options: { height: this.guardHeight }
          });
          if (this.displayValue) {
            result.push({
              data: "00000000",
              text: this.text[7],
              options: { textAlign: "right", fontSize: this.fontSize }
            });
          }
          return result;
        }
      }, {
        key: "encodeMiddleDigits",
        value: function encodeMiddleDigits() {
          var numberSystem = this.upcA[0];
          var checkDigit = this.upcA[this.upcA.length - 1];
          var parity = PARITIES[parseInt(checkDigit)][parseInt(numberSystem)];
          return (0, _encoder2.default)(this.middleDigits, parity);
        }
      }]);
      return UPCE2;
    }(_Barcode3.default);
    function expandToUPCA(middleDigits, numberSystem) {
      var lastUpcE = parseInt(middleDigits[middleDigits.length - 1]);
      var expansion = EXPANSIONS[lastUpcE];
      var result = "";
      var digitIndex = 0;
      for (var i3 = 0; i3 < expansion.length; i3++) {
        var c2 = expansion[i3];
        if (c2 === "X") {
          result += middleDigits[digitIndex++];
        } else {
          result += c2;
        }
      }
      result = "" + numberSystem + result;
      return "" + result + (0, _UPC.checksum)(result);
    }
    exports.default = UPCE;
  }
});

// node_modules/jsbarcode/bin/barcodes/EAN_UPC/index.js
var require_EAN_UPC = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/EAN_UPC/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.UPCE = exports.UPC = exports.EAN2 = exports.EAN5 = exports.EAN8 = exports.EAN13 = void 0;
    var _EAN = require_EAN13();
    var _EAN2 = _interopRequireDefault(_EAN);
    var _EAN3 = require_EAN8();
    var _EAN4 = _interopRequireDefault(_EAN3);
    var _EAN5 = require_EAN5();
    var _EAN6 = _interopRequireDefault(_EAN5);
    var _EAN7 = require_EAN2();
    var _EAN8 = _interopRequireDefault(_EAN7);
    var _UPC = require_UPC();
    var _UPC2 = _interopRequireDefault(_UPC);
    var _UPCE = require_UPCE();
    var _UPCE2 = _interopRequireDefault(_UPCE);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    exports.EAN13 = _EAN2.default;
    exports.EAN8 = _EAN4.default;
    exports.EAN5 = _EAN6.default;
    exports.EAN2 = _EAN8.default;
    exports.UPC = _UPC2.default;
    exports.UPCE = _UPCE2.default;
  }
});

// node_modules/jsbarcode/bin/barcodes/ITF/constants.js
var require_constants3 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/ITF/constants.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var START_BIN = exports.START_BIN = "1010";
    var END_BIN = exports.END_BIN = "11101";
    var BINARIES = exports.BINARIES = ["00110", "10001", "01001", "11000", "00101", "10100", "01100", "00011", "10010", "01010"];
  }
});

// node_modules/jsbarcode/bin/barcodes/ITF/ITF.js
var require_ITF = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/ITF/ITF.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _constants = require_constants3();
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var ITF = function(_Barcode) {
      _inherits(ITF2, _Barcode);
      function ITF2() {
        _classCallCheck(this, ITF2);
        return _possibleConstructorReturn(this, (ITF2.__proto__ || Object.getPrototypeOf(ITF2)).apply(this, arguments));
      }
      _createClass(ITF2, [{
        key: "valid",
        value: function valid() {
          return this.data.search(/^([0-9]{2})+$/) !== -1;
        }
      }, {
        key: "encode",
        value: function encode() {
          var _this2 = this;
          var encoded = this.data.match(/.{2}/g).map(function(pair) {
            return _this2.encodePair(pair);
          }).join("");
          return {
            data: _constants.START_BIN + encoded + _constants.END_BIN,
            text: this.text
          };
        }
        // Calculate the data of a number pair
      }, {
        key: "encodePair",
        value: function encodePair(pair) {
          var second = _constants.BINARIES[pair[1]];
          return _constants.BINARIES[pair[0]].split("").map(function(first, idx) {
            return (first === "1" ? "111" : "1") + (second[idx] === "1" ? "000" : "0");
          }).join("");
        }
      }]);
      return ITF2;
    }(_Barcode3.default);
    exports.default = ITF;
  }
});

// node_modules/jsbarcode/bin/barcodes/ITF/ITF14.js
var require_ITF14 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/ITF/ITF14.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _ITF2 = require_ITF();
    var _ITF3 = _interopRequireDefault(_ITF2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var checksum = function checksum2(data) {
      var res = data.substr(0, 13).split("").map(function(num) {
        return parseInt(num, 10);
      }).reduce(function(sum, n, idx) {
        return sum + n * (3 - idx % 2 * 2);
      }, 0);
      return Math.ceil(res / 10) * 10 - res;
    };
    var ITF14 = function(_ITF) {
      _inherits(ITF142, _ITF);
      function ITF142(data, options) {
        _classCallCheck(this, ITF142);
        if (data.search(/^[0-9]{13}$/) !== -1) {
          data += checksum(data);
        }
        return _possibleConstructorReturn(this, (ITF142.__proto__ || Object.getPrototypeOf(ITF142)).call(this, data, options));
      }
      _createClass(ITF142, [{
        key: "valid",
        value: function valid() {
          return this.data.search(/^[0-9]{14}$/) !== -1 && +this.data[13] === checksum(this.data);
        }
      }]);
      return ITF142;
    }(_ITF3.default);
    exports.default = ITF14;
  }
});

// node_modules/jsbarcode/bin/barcodes/ITF/index.js
var require_ITF2 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/ITF/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.ITF14 = exports.ITF = void 0;
    var _ITF = require_ITF();
    var _ITF2 = _interopRequireDefault(_ITF);
    var _ITF3 = require_ITF14();
    var _ITF4 = _interopRequireDefault(_ITF3);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    exports.ITF = _ITF2.default;
    exports.ITF14 = _ITF4.default;
  }
});

// node_modules/jsbarcode/bin/barcodes/MSI/MSI.js
var require_MSI = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/MSI/MSI.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var MSI = function(_Barcode) {
      _inherits(MSI2, _Barcode);
      function MSI2(data, options) {
        _classCallCheck(this, MSI2);
        return _possibleConstructorReturn(this, (MSI2.__proto__ || Object.getPrototypeOf(MSI2)).call(this, data, options));
      }
      _createClass(MSI2, [{
        key: "encode",
        value: function encode() {
          var ret = "110";
          for (var i3 = 0; i3 < this.data.length; i3++) {
            var digit = parseInt(this.data[i3]);
            var bin = digit.toString(2);
            bin = addZeroes(bin, 4 - bin.length);
            for (var b = 0; b < bin.length; b++) {
              ret += bin[b] == "0" ? "100" : "110";
            }
          }
          ret += "1001";
          return {
            data: ret,
            text: this.text
          };
        }
      }, {
        key: "valid",
        value: function valid() {
          return this.data.search(/^[0-9]+$/) !== -1;
        }
      }]);
      return MSI2;
    }(_Barcode3.default);
    function addZeroes(number, n) {
      for (var i3 = 0; i3 < n; i3++) {
        number = "0" + number;
      }
      return number;
    }
    exports.default = MSI;
  }
});

// node_modules/jsbarcode/bin/barcodes/MSI/checksums.js
var require_checksums = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/MSI/checksums.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.mod10 = mod10;
    exports.mod11 = mod11;
    function mod10(number) {
      var sum = 0;
      for (var i3 = 0; i3 < number.length; i3++) {
        var n = parseInt(number[i3]);
        if ((i3 + number.length) % 2 === 0) {
          sum += n;
        } else {
          sum += n * 2 % 10 + Math.floor(n * 2 / 10);
        }
      }
      return (10 - sum % 10) % 10;
    }
    function mod11(number) {
      var sum = 0;
      var weights = [2, 3, 4, 5, 6, 7];
      for (var i3 = 0; i3 < number.length; i3++) {
        var n = parseInt(number[number.length - 1 - i3]);
        sum += weights[i3 % weights.length] * n;
      }
      return (11 - sum % 11) % 11;
    }
  }
});

// node_modules/jsbarcode/bin/barcodes/MSI/MSI10.js
var require_MSI10 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/MSI/MSI10.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _MSI2 = require_MSI();
    var _MSI3 = _interopRequireDefault(_MSI2);
    var _checksums = require_checksums();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var MSI10 = function(_MSI) {
      _inherits(MSI102, _MSI);
      function MSI102(data, options) {
        _classCallCheck(this, MSI102);
        return _possibleConstructorReturn(this, (MSI102.__proto__ || Object.getPrototypeOf(MSI102)).call(this, data + (0, _checksums.mod10)(data), options));
      }
      return MSI102;
    }(_MSI3.default);
    exports.default = MSI10;
  }
});

// node_modules/jsbarcode/bin/barcodes/MSI/MSI11.js
var require_MSI11 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/MSI/MSI11.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _MSI2 = require_MSI();
    var _MSI3 = _interopRequireDefault(_MSI2);
    var _checksums = require_checksums();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var MSI11 = function(_MSI) {
      _inherits(MSI112, _MSI);
      function MSI112(data, options) {
        _classCallCheck(this, MSI112);
        return _possibleConstructorReturn(this, (MSI112.__proto__ || Object.getPrototypeOf(MSI112)).call(this, data + (0, _checksums.mod11)(data), options));
      }
      return MSI112;
    }(_MSI3.default);
    exports.default = MSI11;
  }
});

// node_modules/jsbarcode/bin/barcodes/MSI/MSI1010.js
var require_MSI1010 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/MSI/MSI1010.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _MSI2 = require_MSI();
    var _MSI3 = _interopRequireDefault(_MSI2);
    var _checksums = require_checksums();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var MSI1010 = function(_MSI) {
      _inherits(MSI10102, _MSI);
      function MSI10102(data, options) {
        _classCallCheck(this, MSI10102);
        data += (0, _checksums.mod10)(data);
        data += (0, _checksums.mod10)(data);
        return _possibleConstructorReturn(this, (MSI10102.__proto__ || Object.getPrototypeOf(MSI10102)).call(this, data, options));
      }
      return MSI10102;
    }(_MSI3.default);
    exports.default = MSI1010;
  }
});

// node_modules/jsbarcode/bin/barcodes/MSI/MSI1110.js
var require_MSI1110 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/MSI/MSI1110.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _MSI2 = require_MSI();
    var _MSI3 = _interopRequireDefault(_MSI2);
    var _checksums = require_checksums();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var MSI1110 = function(_MSI) {
      _inherits(MSI11102, _MSI);
      function MSI11102(data, options) {
        _classCallCheck(this, MSI11102);
        data += (0, _checksums.mod11)(data);
        data += (0, _checksums.mod10)(data);
        return _possibleConstructorReturn(this, (MSI11102.__proto__ || Object.getPrototypeOf(MSI11102)).call(this, data, options));
      }
      return MSI11102;
    }(_MSI3.default);
    exports.default = MSI1110;
  }
});

// node_modules/jsbarcode/bin/barcodes/MSI/index.js
var require_MSI2 = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/MSI/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.MSI1110 = exports.MSI1010 = exports.MSI11 = exports.MSI10 = exports.MSI = void 0;
    var _MSI = require_MSI();
    var _MSI2 = _interopRequireDefault(_MSI);
    var _MSI3 = require_MSI10();
    var _MSI4 = _interopRequireDefault(_MSI3);
    var _MSI5 = require_MSI11();
    var _MSI6 = _interopRequireDefault(_MSI5);
    var _MSI7 = require_MSI1010();
    var _MSI8 = _interopRequireDefault(_MSI7);
    var _MSI9 = require_MSI1110();
    var _MSI10 = _interopRequireDefault(_MSI9);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    exports.MSI = _MSI2.default;
    exports.MSI10 = _MSI4.default;
    exports.MSI11 = _MSI6.default;
    exports.MSI1010 = _MSI8.default;
    exports.MSI1110 = _MSI10.default;
  }
});

// node_modules/jsbarcode/bin/barcodes/pharmacode/index.js
var require_pharmacode = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/pharmacode/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.pharmacode = void 0;
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var pharmacode = function(_Barcode) {
      _inherits(pharmacode2, _Barcode);
      function pharmacode2(data, options) {
        _classCallCheck(this, pharmacode2);
        var _this = _possibleConstructorReturn(this, (pharmacode2.__proto__ || Object.getPrototypeOf(pharmacode2)).call(this, data, options));
        _this.number = parseInt(data, 10);
        return _this;
      }
      _createClass(pharmacode2, [{
        key: "encode",
        value: function encode() {
          var z2 = this.number;
          var result = "";
          while (!isNaN(z2) && z2 != 0) {
            if (z2 % 2 === 0) {
              result = "11100" + result;
              z2 = (z2 - 2) / 2;
            } else {
              result = "100" + result;
              z2 = (z2 - 1) / 2;
            }
          }
          result = result.slice(0, -2);
          return {
            data: result,
            text: this.text
          };
        }
      }, {
        key: "valid",
        value: function valid() {
          return this.number >= 3 && this.number <= 131070;
        }
      }]);
      return pharmacode2;
    }(_Barcode3.default);
    exports.pharmacode = pharmacode;
  }
});

// node_modules/jsbarcode/bin/barcodes/codabar/index.js
var require_codabar = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/codabar/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.codabar = void 0;
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var codabar = function(_Barcode) {
      _inherits(codabar2, _Barcode);
      function codabar2(data, options) {
        _classCallCheck(this, codabar2);
        if (data.search(/^[0-9\-\$\:\.\+\/]+$/) === 0) {
          data = "A" + data + "A";
        }
        var _this = _possibleConstructorReturn(this, (codabar2.__proto__ || Object.getPrototypeOf(codabar2)).call(this, data.toUpperCase(), options));
        _this.text = _this.options.text || _this.text.replace(/[A-D]/g, "");
        return _this;
      }
      _createClass(codabar2, [{
        key: "valid",
        value: function valid() {
          return this.data.search(/^[A-D][0-9\-\$\:\.\+\/]+[A-D]$/) !== -1;
        }
      }, {
        key: "encode",
        value: function encode() {
          var result = [];
          var encodings = this.getEncodings();
          for (var i3 = 0; i3 < this.data.length; i3++) {
            result.push(encodings[this.data.charAt(i3)]);
            if (i3 !== this.data.length - 1) {
              result.push("0");
            }
          }
          return {
            text: this.text,
            data: result.join("")
          };
        }
      }, {
        key: "getEncodings",
        value: function getEncodings() {
          return {
            "0": "101010011",
            "1": "101011001",
            "2": "101001011",
            "3": "110010101",
            "4": "101101001",
            "5": "110101001",
            "6": "100101011",
            "7": "100101101",
            "8": "100110101",
            "9": "110100101",
            "-": "101001101",
            "$": "101100101",
            ":": "1101011011",
            "/": "1101101011",
            ".": "1101101101",
            "+": "1011011011",
            "A": "1011001001",
            "B": "1001001011",
            "C": "1010010011",
            "D": "1010011001"
          };
        }
      }]);
      return codabar2;
    }(_Barcode3.default);
    exports.codabar = codabar;
  }
});

// node_modules/jsbarcode/bin/barcodes/GenericBarcode/index.js
var require_GenericBarcode = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/GenericBarcode/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.GenericBarcode = void 0;
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _Barcode2 = require_Barcode();
    var _Barcode3 = _interopRequireDefault(_Barcode2);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var GenericBarcode = function(_Barcode) {
      _inherits(GenericBarcode2, _Barcode);
      function GenericBarcode2(data, options) {
        _classCallCheck(this, GenericBarcode2);
        return _possibleConstructorReturn(this, (GenericBarcode2.__proto__ || Object.getPrototypeOf(GenericBarcode2)).call(this, data, options));
      }
      _createClass(GenericBarcode2, [{
        key: "encode",
        value: function encode() {
          return {
            data: "10101010101010101010101010101010101010101",
            text: this.text
          };
        }
        // Resturn true/false if the string provided is valid for this encoder
      }, {
        key: "valid",
        value: function valid() {
          return true;
        }
      }]);
      return GenericBarcode2;
    }(_Barcode3.default);
    exports.GenericBarcode = GenericBarcode;
  }
});

// node_modules/jsbarcode/bin/barcodes/index.js
var require_barcodes = __commonJS({
  "node_modules/jsbarcode/bin/barcodes/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _CODE = require_CODE39();
    var _CODE2 = require_CODE1282();
    var _EAN_UPC = require_EAN_UPC();
    var _ITF = require_ITF2();
    var _MSI = require_MSI2();
    var _pharmacode = require_pharmacode();
    var _codabar = require_codabar();
    var _GenericBarcode = require_GenericBarcode();
    exports.default = {
      CODE39: _CODE.CODE39,
      CODE128: _CODE2.CODE128,
      CODE128A: _CODE2.CODE128A,
      CODE128B: _CODE2.CODE128B,
      CODE128C: _CODE2.CODE128C,
      EAN13: _EAN_UPC.EAN13,
      EAN8: _EAN_UPC.EAN8,
      EAN5: _EAN_UPC.EAN5,
      EAN2: _EAN_UPC.EAN2,
      UPC: _EAN_UPC.UPC,
      UPCE: _EAN_UPC.UPCE,
      ITF14: _ITF.ITF14,
      ITF: _ITF.ITF,
      MSI: _MSI.MSI,
      MSI10: _MSI.MSI10,
      MSI11: _MSI.MSI11,
      MSI1010: _MSI.MSI1010,
      MSI1110: _MSI.MSI1110,
      pharmacode: _pharmacode.pharmacode,
      codabar: _codabar.codabar,
      GenericBarcode: _GenericBarcode.GenericBarcode
    };
  }
});

// node_modules/jsbarcode/bin/help/merge.js
var require_merge = __commonJS({
  "node_modules/jsbarcode/bin/help/merge.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _extends = Object.assign || function(target) {
      for (var i3 = 1; i3 < arguments.length; i3++) {
        var source = arguments[i3];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    exports.default = function(old, replaceObj) {
      return _extends({}, old, replaceObj);
    };
  }
});

// node_modules/jsbarcode/bin/help/linearizeEncodings.js
var require_linearizeEncodings = __commonJS({
  "node_modules/jsbarcode/bin/help/linearizeEncodings.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = linearizeEncodings;
    function linearizeEncodings(encodings) {
      var linearEncodings = [];
      function nextLevel(encoded) {
        if (Array.isArray(encoded)) {
          for (var i3 = 0; i3 < encoded.length; i3++) {
            nextLevel(encoded[i3]);
          }
        } else {
          encoded.text = encoded.text || "";
          encoded.data = encoded.data || "";
          linearEncodings.push(encoded);
        }
      }
      nextLevel(encodings);
      return linearEncodings;
    }
  }
});

// node_modules/jsbarcode/bin/help/fixOptions.js
var require_fixOptions = __commonJS({
  "node_modules/jsbarcode/bin/help/fixOptions.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = fixOptions;
    function fixOptions(options) {
      options.marginTop = options.marginTop || options.margin;
      options.marginBottom = options.marginBottom || options.margin;
      options.marginRight = options.marginRight || options.margin;
      options.marginLeft = options.marginLeft || options.margin;
      return options;
    }
  }
});

// node_modules/jsbarcode/bin/help/optionsFromStrings.js
var require_optionsFromStrings = __commonJS({
  "node_modules/jsbarcode/bin/help/optionsFromStrings.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = optionsFromStrings;
    function optionsFromStrings(options) {
      var intOptions = ["width", "height", "textMargin", "fontSize", "margin", "marginTop", "marginBottom", "marginLeft", "marginRight"];
      for (var intOption in intOptions) {
        if (intOptions.hasOwnProperty(intOption)) {
          intOption = intOptions[intOption];
          if (typeof options[intOption] === "string") {
            options[intOption] = parseInt(options[intOption], 10);
          }
        }
      }
      if (typeof options["displayValue"] === "string") {
        options["displayValue"] = options["displayValue"] != "false";
      }
      return options;
    }
  }
});

// node_modules/jsbarcode/bin/options/defaults.js
var require_defaults = __commonJS({
  "node_modules/jsbarcode/bin/options/defaults.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var defaults = {
      width: 2,
      height: 100,
      format: "auto",
      displayValue: true,
      fontOptions: "",
      font: "monospace",
      text: void 0,
      textAlign: "center",
      textPosition: "bottom",
      textMargin: 2,
      fontSize: 20,
      background: "#ffffff",
      lineColor: "#000000",
      margin: 10,
      marginTop: void 0,
      marginBottom: void 0,
      marginLeft: void 0,
      marginRight: void 0,
      valid: function valid() {
      }
    };
    exports.default = defaults;
  }
});

// node_modules/jsbarcode/bin/help/getOptionsFromElement.js
var require_getOptionsFromElement = __commonJS({
  "node_modules/jsbarcode/bin/help/getOptionsFromElement.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _optionsFromStrings = require_optionsFromStrings();
    var _optionsFromStrings2 = _interopRequireDefault(_optionsFromStrings);
    var _defaults = require_defaults();
    var _defaults2 = _interopRequireDefault(_defaults);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function getOptionsFromElement(element) {
      var options = {};
      for (var property in _defaults2.default) {
        if (_defaults2.default.hasOwnProperty(property)) {
          if (element.hasAttribute("jsbarcode-" + property.toLowerCase())) {
            options[property] = element.getAttribute("jsbarcode-" + property.toLowerCase());
          }
          if (element.hasAttribute("data-" + property.toLowerCase())) {
            options[property] = element.getAttribute("data-" + property.toLowerCase());
          }
        }
      }
      options["value"] = element.getAttribute("jsbarcode-value") || element.getAttribute("data-value");
      options = (0, _optionsFromStrings2.default)(options);
      return options;
    }
    exports.default = getOptionsFromElement;
  }
});

// node_modules/jsbarcode/bin/renderers/shared.js
var require_shared = __commonJS({
  "node_modules/jsbarcode/bin/renderers/shared.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.getTotalWidthOfEncodings = exports.calculateEncodingAttributes = exports.getBarcodePadding = exports.getEncodingHeight = exports.getMaximumHeightOfEncodings = void 0;
    var _merge = require_merge();
    var _merge2 = _interopRequireDefault(_merge);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function getEncodingHeight(encoding, options) {
      return options.height + (options.displayValue && encoding.text.length > 0 ? options.fontSize + options.textMargin : 0) + options.marginTop + options.marginBottom;
    }
    function getBarcodePadding(textWidth, barcodeWidth, options) {
      if (options.displayValue && barcodeWidth < textWidth) {
        if (options.textAlign == "center") {
          return Math.floor((textWidth - barcodeWidth) / 2);
        } else if (options.textAlign == "left") {
          return 0;
        } else if (options.textAlign == "right") {
          return Math.floor(textWidth - barcodeWidth);
        }
      }
      return 0;
    }
    function calculateEncodingAttributes(encodings, barcodeOptions, context) {
      for (var i3 = 0; i3 < encodings.length; i3++) {
        var encoding = encodings[i3];
        var options = (0, _merge2.default)(barcodeOptions, encoding.options);
        var textWidth;
        if (options.displayValue) {
          textWidth = messureText(encoding.text, options, context);
        } else {
          textWidth = 0;
        }
        var barcodeWidth = encoding.data.length * options.width;
        encoding.width = Math.ceil(Math.max(textWidth, barcodeWidth));
        encoding.height = getEncodingHeight(encoding, options);
        encoding.barcodePadding = getBarcodePadding(textWidth, barcodeWidth, options);
      }
    }
    function getTotalWidthOfEncodings(encodings) {
      var totalWidth = 0;
      for (var i3 = 0; i3 < encodings.length; i3++) {
        totalWidth += encodings[i3].width;
      }
      return totalWidth;
    }
    function getMaximumHeightOfEncodings(encodings) {
      var maxHeight = 0;
      for (var i3 = 0; i3 < encodings.length; i3++) {
        if (encodings[i3].height > maxHeight) {
          maxHeight = encodings[i3].height;
        }
      }
      return maxHeight;
    }
    function messureText(string, options, context) {
      var ctx;
      if (context) {
        ctx = context;
      } else if (typeof document !== "undefined") {
        ctx = document.createElement("canvas").getContext("2d");
      } else {
        return 0;
      }
      ctx.font = options.fontOptions + " " + options.fontSize + "px " + options.font;
      var measureTextResult = ctx.measureText(string);
      if (!measureTextResult) {
        return 0;
      }
      var size = measureTextResult.width;
      return size;
    }
    exports.getMaximumHeightOfEncodings = getMaximumHeightOfEncodings;
    exports.getEncodingHeight = getEncodingHeight;
    exports.getBarcodePadding = getBarcodePadding;
    exports.calculateEncodingAttributes = calculateEncodingAttributes;
    exports.getTotalWidthOfEncodings = getTotalWidthOfEncodings;
  }
});

// node_modules/jsbarcode/bin/renderers/canvas.js
var require_canvas = __commonJS({
  "node_modules/jsbarcode/bin/renderers/canvas.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _merge = require_merge();
    var _merge2 = _interopRequireDefault(_merge);
    var _shared = require_shared();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    var CanvasRenderer = function() {
      function CanvasRenderer2(canvas, encodings, options) {
        _classCallCheck(this, CanvasRenderer2);
        this.canvas = canvas;
        this.encodings = encodings;
        this.options = options;
      }
      _createClass(CanvasRenderer2, [{
        key: "render",
        value: function render() {
          if (!this.canvas.getContext) {
            throw new Error("The browser does not support canvas.");
          }
          this.prepareCanvas();
          for (var i3 = 0; i3 < this.encodings.length; i3++) {
            var encodingOptions = (0, _merge2.default)(this.options, this.encodings[i3].options);
            this.drawCanvasBarcode(encodingOptions, this.encodings[i3]);
            this.drawCanvasText(encodingOptions, this.encodings[i3]);
            this.moveCanvasDrawing(this.encodings[i3]);
          }
          this.restoreCanvas();
        }
      }, {
        key: "prepareCanvas",
        value: function prepareCanvas() {
          var ctx = this.canvas.getContext("2d");
          ctx.save();
          (0, _shared.calculateEncodingAttributes)(this.encodings, this.options, ctx);
          var totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);
          var maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);
          this.canvas.width = totalWidth + this.options.marginLeft + this.options.marginRight;
          this.canvas.height = maxHeight;
          ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          if (this.options.background) {
            ctx.fillStyle = this.options.background;
            ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
          }
          ctx.translate(this.options.marginLeft, 0);
        }
      }, {
        key: "drawCanvasBarcode",
        value: function drawCanvasBarcode(options, encoding) {
          var ctx = this.canvas.getContext("2d");
          var binary = encoding.data;
          var yFrom;
          if (options.textPosition == "top") {
            yFrom = options.marginTop + options.fontSize + options.textMargin;
          } else {
            yFrom = options.marginTop;
          }
          ctx.fillStyle = options.lineColor;
          for (var b = 0; b < binary.length; b++) {
            var x = b * options.width + encoding.barcodePadding;
            if (binary[b] === "1") {
              ctx.fillRect(x, yFrom, options.width, options.height);
            } else if (binary[b]) {
              ctx.fillRect(x, yFrom, options.width, options.height * binary[b]);
            }
          }
        }
      }, {
        key: "drawCanvasText",
        value: function drawCanvasText(options, encoding) {
          var ctx = this.canvas.getContext("2d");
          var font = options.fontOptions + " " + options.fontSize + "px " + options.font;
          if (options.displayValue) {
            var x, y3;
            if (options.textPosition == "top") {
              y3 = options.marginTop + options.fontSize - options.textMargin;
            } else {
              y3 = options.height + options.textMargin + options.marginTop + options.fontSize;
            }
            ctx.font = font;
            if (options.textAlign == "left" || encoding.barcodePadding > 0) {
              x = 0;
              ctx.textAlign = "left";
            } else if (options.textAlign == "right") {
              x = encoding.width - 1;
              ctx.textAlign = "right";
            } else {
              x = encoding.width / 2;
              ctx.textAlign = "center";
            }
            ctx.fillText(encoding.text, x, y3);
          }
        }
      }, {
        key: "moveCanvasDrawing",
        value: function moveCanvasDrawing(encoding) {
          var ctx = this.canvas.getContext("2d");
          ctx.translate(encoding.width, 0);
        }
      }, {
        key: "restoreCanvas",
        value: function restoreCanvas() {
          var ctx = this.canvas.getContext("2d");
          ctx.restore();
        }
      }]);
      return CanvasRenderer2;
    }();
    exports.default = CanvasRenderer;
  }
});

// node_modules/jsbarcode/bin/renderers/svg.js
var require_svg = __commonJS({
  "node_modules/jsbarcode/bin/renderers/svg.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    var _merge = require_merge();
    var _merge2 = _interopRequireDefault(_merge);
    var _shared = require_shared();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    var svgns = "http://www.w3.org/2000/svg";
    var SVGRenderer = function() {
      function SVGRenderer2(svg, encodings, options) {
        _classCallCheck(this, SVGRenderer2);
        this.svg = svg;
        this.encodings = encodings;
        this.options = options;
        this.document = options.xmlDocument || document;
      }
      _createClass(SVGRenderer2, [{
        key: "render",
        value: function render() {
          var currentX = this.options.marginLeft;
          this.prepareSVG();
          for (var i3 = 0; i3 < this.encodings.length; i3++) {
            var encoding = this.encodings[i3];
            var encodingOptions = (0, _merge2.default)(this.options, encoding.options);
            var group = this.createGroup(currentX, encodingOptions.marginTop, this.svg);
            this.setGroupOptions(group, encodingOptions);
            this.drawSvgBarcode(group, encodingOptions, encoding);
            this.drawSVGText(group, encodingOptions, encoding);
            currentX += encoding.width;
          }
        }
      }, {
        key: "prepareSVG",
        value: function prepareSVG() {
          while (this.svg.firstChild) {
            this.svg.removeChild(this.svg.firstChild);
          }
          (0, _shared.calculateEncodingAttributes)(this.encodings, this.options);
          var totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);
          var maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);
          var width = totalWidth + this.options.marginLeft + this.options.marginRight;
          this.setSvgAttributes(width, maxHeight);
          if (this.options.background) {
            this.drawRect(0, 0, width, maxHeight, this.svg).setAttribute("style", "fill:" + this.options.background + ";");
          }
        }
      }, {
        key: "drawSvgBarcode",
        value: function drawSvgBarcode(parent, options, encoding) {
          var binary = encoding.data;
          var yFrom;
          if (options.textPosition == "top") {
            yFrom = options.fontSize + options.textMargin;
          } else {
            yFrom = 0;
          }
          var barWidth = 0;
          var x = 0;
          for (var b = 0; b < binary.length; b++) {
            x = b * options.width + encoding.barcodePadding;
            if (binary[b] === "1") {
              barWidth++;
            } else if (barWidth > 0) {
              this.drawRect(x - options.width * barWidth, yFrom, options.width * barWidth, options.height, parent);
              barWidth = 0;
            }
          }
          if (barWidth > 0) {
            this.drawRect(x - options.width * (barWidth - 1), yFrom, options.width * barWidth, options.height, parent);
          }
        }
      }, {
        key: "drawSVGText",
        value: function drawSVGText(parent, options, encoding) {
          var textElem = this.document.createElementNS(svgns, "text");
          if (options.displayValue) {
            var x, y3;
            textElem.setAttribute("style", "font:" + options.fontOptions + " " + options.fontSize + "px " + options.font);
            if (options.textPosition == "top") {
              y3 = options.fontSize - options.textMargin;
            } else {
              y3 = options.height + options.textMargin + options.fontSize;
            }
            if (options.textAlign == "left" || encoding.barcodePadding > 0) {
              x = 0;
              textElem.setAttribute("text-anchor", "start");
            } else if (options.textAlign == "right") {
              x = encoding.width - 1;
              textElem.setAttribute("text-anchor", "end");
            } else {
              x = encoding.width / 2;
              textElem.setAttribute("text-anchor", "middle");
            }
            textElem.setAttribute("x", x);
            textElem.setAttribute("y", y3);
            textElem.appendChild(this.document.createTextNode(encoding.text));
            parent.appendChild(textElem);
          }
        }
      }, {
        key: "setSvgAttributes",
        value: function setSvgAttributes(width, height) {
          var svg = this.svg;
          svg.setAttribute("width", width + "px");
          svg.setAttribute("height", height + "px");
          svg.setAttribute("x", "0px");
          svg.setAttribute("y", "0px");
          svg.setAttribute("viewBox", "0 0 " + width + " " + height);
          svg.setAttribute("xmlns", svgns);
          svg.setAttribute("version", "1.1");
          svg.setAttribute("style", "transform: translate(0,0)");
        }
      }, {
        key: "createGroup",
        value: function createGroup(x, y3, parent) {
          var group = this.document.createElementNS(svgns, "g");
          group.setAttribute("transform", "translate(" + x + ", " + y3 + ")");
          parent.appendChild(group);
          return group;
        }
      }, {
        key: "setGroupOptions",
        value: function setGroupOptions(group, options) {
          group.setAttribute("style", "fill:" + options.lineColor + ";");
        }
      }, {
        key: "drawRect",
        value: function drawRect(x, y3, width, height, parent) {
          var rect = this.document.createElementNS(svgns, "rect");
          rect.setAttribute("x", x);
          rect.setAttribute("y", y3);
          rect.setAttribute("width", width);
          rect.setAttribute("height", height);
          parent.appendChild(rect);
          return rect;
        }
      }]);
      return SVGRenderer2;
    }();
    exports.default = SVGRenderer;
  }
});

// node_modules/jsbarcode/bin/renderers/object.js
var require_object = __commonJS({
  "node_modules/jsbarcode/bin/renderers/object.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    var ObjectRenderer = function() {
      function ObjectRenderer2(object, encodings, options) {
        _classCallCheck(this, ObjectRenderer2);
        this.object = object;
        this.encodings = encodings;
        this.options = options;
      }
      _createClass(ObjectRenderer2, [{
        key: "render",
        value: function render() {
          this.object.encodings = this.encodings;
        }
      }]);
      return ObjectRenderer2;
    }();
    exports.default = ObjectRenderer;
  }
});

// node_modules/jsbarcode/bin/renderers/index.js
var require_renderers = __commonJS({
  "node_modules/jsbarcode/bin/renderers/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _canvas = require_canvas();
    var _canvas2 = _interopRequireDefault(_canvas);
    var _svg = require_svg();
    var _svg2 = _interopRequireDefault(_svg);
    var _object = require_object();
    var _object2 = _interopRequireDefault(_object);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    exports.default = { CanvasRenderer: _canvas2.default, SVGRenderer: _svg2.default, ObjectRenderer: _object2.default };
  }
});

// node_modules/jsbarcode/bin/exceptions/exceptions.js
var require_exceptions = __commonJS({
  "node_modules/jsbarcode/bin/exceptions/exceptions.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var InvalidInputException = function(_Error) {
      _inherits(InvalidInputException2, _Error);
      function InvalidInputException2(symbology, input) {
        _classCallCheck(this, InvalidInputException2);
        var _this = _possibleConstructorReturn(this, (InvalidInputException2.__proto__ || Object.getPrototypeOf(InvalidInputException2)).call(this));
        _this.name = "InvalidInputException";
        _this.symbology = symbology;
        _this.input = input;
        _this.message = '"' + _this.input + '" is not a valid input for ' + _this.symbology;
        return _this;
      }
      return InvalidInputException2;
    }(Error);
    var InvalidElementException = function(_Error2) {
      _inherits(InvalidElementException2, _Error2);
      function InvalidElementException2() {
        _classCallCheck(this, InvalidElementException2);
        var _this2 = _possibleConstructorReturn(this, (InvalidElementException2.__proto__ || Object.getPrototypeOf(InvalidElementException2)).call(this));
        _this2.name = "InvalidElementException";
        _this2.message = "Not supported type to render on";
        return _this2;
      }
      return InvalidElementException2;
    }(Error);
    var NoElementException = function(_Error3) {
      _inherits(NoElementException2, _Error3);
      function NoElementException2() {
        _classCallCheck(this, NoElementException2);
        var _this3 = _possibleConstructorReturn(this, (NoElementException2.__proto__ || Object.getPrototypeOf(NoElementException2)).call(this));
        _this3.name = "NoElementException";
        _this3.message = "No element to render on.";
        return _this3;
      }
      return NoElementException2;
    }(Error);
    exports.InvalidInputException = InvalidInputException;
    exports.InvalidElementException = InvalidElementException;
    exports.NoElementException = NoElementException;
  }
});

// node_modules/jsbarcode/bin/help/getRenderProperties.js
var require_getRenderProperties = __commonJS({
  "node_modules/jsbarcode/bin/help/getRenderProperties.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function(obj) {
      return typeof obj;
    } : function(obj) {
      return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
    };
    var _getOptionsFromElement = require_getOptionsFromElement();
    var _getOptionsFromElement2 = _interopRequireDefault(_getOptionsFromElement);
    var _renderers = require_renderers();
    var _renderers2 = _interopRequireDefault(_renderers);
    var _exceptions = require_exceptions();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function getRenderProperties(element) {
      if (typeof element === "string") {
        return querySelectedRenderProperties(element);
      } else if (Array.isArray(element)) {
        var returnArray = [];
        for (var i3 = 0; i3 < element.length; i3++) {
          returnArray.push(getRenderProperties(element[i3]));
        }
        return returnArray;
      } else if (typeof HTMLCanvasElement !== "undefined" && element instanceof HTMLImageElement) {
        return newCanvasRenderProperties(element);
      } else if (element && element.nodeName && element.nodeName.toLowerCase() === "svg" || typeof SVGElement !== "undefined" && element instanceof SVGElement) {
        return {
          element,
          options: (0, _getOptionsFromElement2.default)(element),
          renderer: _renderers2.default.SVGRenderer
        };
      } else if (typeof HTMLCanvasElement !== "undefined" && element instanceof HTMLCanvasElement) {
        return {
          element,
          options: (0, _getOptionsFromElement2.default)(element),
          renderer: _renderers2.default.CanvasRenderer
        };
      } else if (element && element.getContext) {
        return {
          element,
          renderer: _renderers2.default.CanvasRenderer
        };
      } else if (element && (typeof element === "undefined" ? "undefined" : _typeof(element)) === "object" && !element.nodeName) {
        return {
          element,
          renderer: _renderers2.default.ObjectRenderer
        };
      } else {
        throw new _exceptions.InvalidElementException();
      }
    }
    function querySelectedRenderProperties(string) {
      var selector = document.querySelectorAll(string);
      if (selector.length === 0) {
        return void 0;
      } else {
        var returnArray = [];
        for (var i3 = 0; i3 < selector.length; i3++) {
          returnArray.push(getRenderProperties(selector[i3]));
        }
        return returnArray;
      }
    }
    function newCanvasRenderProperties(imgElement) {
      var canvas = document.createElement("canvas");
      return {
        element: canvas,
        options: (0, _getOptionsFromElement2.default)(imgElement),
        renderer: _renderers2.default.CanvasRenderer,
        afterRender: function afterRender() {
          imgElement.setAttribute("src", canvas.toDataURL());
        }
      };
    }
    exports.default = getRenderProperties;
  }
});

// node_modules/jsbarcode/bin/exceptions/ErrorHandler.js
var require_ErrorHandler = __commonJS({
  "node_modules/jsbarcode/bin/exceptions/ErrorHandler.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i3 = 0; i3 < props.length; i3++) {
          var descriptor = props[i3];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    var ErrorHandler = function() {
      function ErrorHandler2(api) {
        _classCallCheck(this, ErrorHandler2);
        this.api = api;
      }
      _createClass(ErrorHandler2, [{
        key: "handleCatch",
        value: function handleCatch(e) {
          if (e.name === "InvalidInputException") {
            if (this.api._options.valid !== this.api._defaults.valid) {
              this.api._options.valid(false);
            } else {
              throw e.message;
            }
          } else {
            throw e;
          }
          this.api.render = function() {
          };
        }
      }, {
        key: "wrapBarcodeCall",
        value: function wrapBarcodeCall(func) {
          try {
            var result = func.apply(void 0, arguments);
            this.api._options.valid(true);
            return result;
          } catch (e) {
            this.handleCatch(e);
            return this.api;
          }
        }
      }]);
      return ErrorHandler2;
    }();
    exports.default = ErrorHandler;
  }
});

// node_modules/jsbarcode/bin/JsBarcode.js
var require_JsBarcode = __commonJS({
  "node_modules/jsbarcode/bin/JsBarcode.js"(exports, module) {
    "use strict";
    var _barcodes = require_barcodes();
    var _barcodes2 = _interopRequireDefault(_barcodes);
    var _merge = require_merge();
    var _merge2 = _interopRequireDefault(_merge);
    var _linearizeEncodings = require_linearizeEncodings();
    var _linearizeEncodings2 = _interopRequireDefault(_linearizeEncodings);
    var _fixOptions = require_fixOptions();
    var _fixOptions2 = _interopRequireDefault(_fixOptions);
    var _getRenderProperties = require_getRenderProperties();
    var _getRenderProperties2 = _interopRequireDefault(_getRenderProperties);
    var _optionsFromStrings = require_optionsFromStrings();
    var _optionsFromStrings2 = _interopRequireDefault(_optionsFromStrings);
    var _ErrorHandler = require_ErrorHandler();
    var _ErrorHandler2 = _interopRequireDefault(_ErrorHandler);
    var _exceptions = require_exceptions();
    var _defaults = require_defaults();
    var _defaults2 = _interopRequireDefault(_defaults);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    var API = function API2() {
    };
    var JsBarcode = function JsBarcode2(element, text, options) {
      var api = new API();
      if (typeof element === "undefined") {
        throw Error("No element to render on was provided.");
      }
      api._renderProperties = (0, _getRenderProperties2.default)(element);
      api._encodings = [];
      api._options = _defaults2.default;
      api._errorHandler = new _ErrorHandler2.default(api);
      if (typeof text !== "undefined") {
        options = options || {};
        if (!options.format) {
          options.format = autoSelectBarcode();
        }
        api.options(options)[options.format](text, options).render();
      }
      return api;
    };
    JsBarcode.getModule = function(name2) {
      return _barcodes2.default[name2];
    };
    for (name in _barcodes2.default) {
      if (_barcodes2.default.hasOwnProperty(name)) {
        registerBarcode(_barcodes2.default, name);
      }
    }
    var name;
    function registerBarcode(barcodes, name2) {
      API.prototype[name2] = API.prototype[name2.toUpperCase()] = API.prototype[name2.toLowerCase()] = function(text, options) {
        var api = this;
        return api._errorHandler.wrapBarcodeCall(function() {
          options.text = typeof options.text === "undefined" ? void 0 : "" + options.text;
          var newOptions = (0, _merge2.default)(api._options, options);
          newOptions = (0, _optionsFromStrings2.default)(newOptions);
          var Encoder = barcodes[name2];
          var encoded = encode(text, Encoder, newOptions);
          api._encodings.push(encoded);
          return api;
        });
      };
    }
    function encode(text, Encoder, options) {
      text = "" + text;
      var encoder = new Encoder(text, options);
      if (!encoder.valid()) {
        throw new _exceptions.InvalidInputException(encoder.constructor.name, text);
      }
      var encoded = encoder.encode();
      encoded = (0, _linearizeEncodings2.default)(encoded);
      for (var i3 = 0; i3 < encoded.length; i3++) {
        encoded[i3].options = (0, _merge2.default)(options, encoded[i3].options);
      }
      return encoded;
    }
    function autoSelectBarcode() {
      if (_barcodes2.default["CODE128"]) {
        return "CODE128";
      }
      return Object.keys(_barcodes2.default)[0];
    }
    API.prototype.options = function(options) {
      this._options = (0, _merge2.default)(this._options, options);
      return this;
    };
    API.prototype.blank = function(size) {
      var zeroes = new Array(size + 1).join("0");
      this._encodings.push({ data: zeroes });
      return this;
    };
    API.prototype.init = function() {
      if (!this._renderProperties) {
        return;
      }
      if (!Array.isArray(this._renderProperties)) {
        this._renderProperties = [this._renderProperties];
      }
      var renderProperty;
      for (var i3 in this._renderProperties) {
        renderProperty = this._renderProperties[i3];
        var options = (0, _merge2.default)(this._options, renderProperty.options);
        if (options.format == "auto") {
          options.format = autoSelectBarcode();
        }
        this._errorHandler.wrapBarcodeCall(function() {
          var text = options.value;
          var Encoder = _barcodes2.default[options.format.toUpperCase()];
          var encoded = encode(text, Encoder, options);
          render(renderProperty, encoded, options);
        });
      }
    };
    API.prototype.render = function() {
      if (!this._renderProperties) {
        throw new _exceptions.NoElementException();
      }
      if (Array.isArray(this._renderProperties)) {
        for (var i3 = 0; i3 < this._renderProperties.length; i3++) {
          render(this._renderProperties[i3], this._encodings, this._options);
        }
      } else {
        render(this._renderProperties, this._encodings, this._options);
      }
      return this;
    };
    API.prototype._defaults = _defaults2.default;
    function render(renderProperties, encodings, options) {
      encodings = (0, _linearizeEncodings2.default)(encodings);
      for (var i3 = 0; i3 < encodings.length; i3++) {
        encodings[i3].options = (0, _merge2.default)(options, encodings[i3].options);
        (0, _fixOptions2.default)(encodings[i3].options);
      }
      (0, _fixOptions2.default)(options);
      var Renderer = renderProperties.renderer;
      var renderer = new Renderer(renderProperties.element, encodings, options);
      renderer.render();
      if (renderProperties.afterRender) {
        renderProperties.afterRender();
      }
    }
    if (typeof window !== "undefined") {
      window.JsBarcode = JsBarcode;
    }
    if (typeof jQuery !== "undefined") {
      jQuery.fn.JsBarcode = function(content, options) {
        var elementArray = [];
        jQuery(this).each(function() {
          elementArray.push(this);
        });
        return JsBarcode(elementArray, content, options);
      };
    }
    module.exports = JsBarcode;
  }
});

// node_modules/qrcode/lib/can-promise.js
var require_can_promise = __commonJS({
  "node_modules/qrcode/lib/can-promise.js"(exports, module) {
    module.exports = function() {
      return typeof Promise === "function" && Promise.prototype && Promise.prototype.then;
    };
  }
});

// node_modules/qrcode/lib/core/utils.js
var require_utils = __commonJS({
  "node_modules/qrcode/lib/core/utils.js"(exports) {
    var toSJISFunction;
    var CODEWORDS_COUNT = [
      0,
      // Not used
      26,
      44,
      70,
      100,
      134,
      172,
      196,
      242,
      292,
      346,
      404,
      466,
      532,
      581,
      655,
      733,
      815,
      901,
      991,
      1085,
      1156,
      1258,
      1364,
      1474,
      1588,
      1706,
      1828,
      1921,
      2051,
      2185,
      2323,
      2465,
      2611,
      2761,
      2876,
      3034,
      3196,
      3362,
      3532,
      3706
    ];
    exports.getSymbolSize = function getSymbolSize(version) {
      if (!version) throw new Error('"version" cannot be null or undefined');
      if (version < 1 || version > 40) throw new Error('"version" should be in range from 1 to 40');
      return version * 4 + 17;
    };
    exports.getSymbolTotalCodewords = function getSymbolTotalCodewords(version) {
      return CODEWORDS_COUNT[version];
    };
    exports.getBCHDigit = function(data) {
      let digit = 0;
      while (data !== 0) {
        digit++;
        data >>>= 1;
      }
      return digit;
    };
    exports.setToSJISFunction = function setToSJISFunction(f) {
      if (typeof f !== "function") {
        throw new Error('"toSJISFunc" is not a valid function.');
      }
      toSJISFunction = f;
    };
    exports.isKanjiModeEnabled = function() {
      return typeof toSJISFunction !== "undefined";
    };
    exports.toSJIS = function toSJIS(kanji) {
      return toSJISFunction(kanji);
    };
  }
});

// node_modules/qrcode/lib/core/error-correction-level.js
var require_error_correction_level = __commonJS({
  "node_modules/qrcode/lib/core/error-correction-level.js"(exports) {
    exports.L = { bit: 1 };
    exports.M = { bit: 0 };
    exports.Q = { bit: 3 };
    exports.H = { bit: 2 };
    function fromString(string) {
      if (typeof string !== "string") {
        throw new Error("Param is not a string");
      }
      const lcStr = string.toLowerCase();
      switch (lcStr) {
        case "l":
        case "low":
          return exports.L;
        case "m":
        case "medium":
          return exports.M;
        case "q":
        case "quartile":
          return exports.Q;
        case "h":
        case "high":
          return exports.H;
        default:
          throw new Error("Unknown EC Level: " + string);
      }
    }
    exports.isValid = function isValid(level) {
      return level && typeof level.bit !== "undefined" && level.bit >= 0 && level.bit < 4;
    };
    exports.from = function from(value, defaultValue) {
      if (exports.isValid(value)) {
        return value;
      }
      try {
        return fromString(value);
      } catch (e) {
        return defaultValue;
      }
    };
  }
});

// node_modules/qrcode/lib/core/bit-buffer.js
var require_bit_buffer = __commonJS({
  "node_modules/qrcode/lib/core/bit-buffer.js"(exports, module) {
    function BitBuffer() {
      this.buffer = [];
      this.length = 0;
    }
    BitBuffer.prototype = {
      get: function(index) {
        const bufIndex = Math.floor(index / 8);
        return (this.buffer[bufIndex] >>> 7 - index % 8 & 1) === 1;
      },
      put: function(num, length) {
        for (let i3 = 0; i3 < length; i3++) {
          this.putBit((num >>> length - i3 - 1 & 1) === 1);
        }
      },
      getLengthInBits: function() {
        return this.length;
      },
      putBit: function(bit) {
        const bufIndex = Math.floor(this.length / 8);
        if (this.buffer.length <= bufIndex) {
          this.buffer.push(0);
        }
        if (bit) {
          this.buffer[bufIndex] |= 128 >>> this.length % 8;
        }
        this.length++;
      }
    };
    module.exports = BitBuffer;
  }
});

// node_modules/qrcode/lib/core/bit-matrix.js
var require_bit_matrix = __commonJS({
  "node_modules/qrcode/lib/core/bit-matrix.js"(exports, module) {
    function BitMatrix(size) {
      if (!size || size < 1) {
        throw new Error("BitMatrix size must be defined and greater than 0");
      }
      this.size = size;
      this.data = new Uint8Array(size * size);
      this.reservedBit = new Uint8Array(size * size);
    }
    BitMatrix.prototype.set = function(row, col, value, reserved) {
      const index = row * this.size + col;
      this.data[index] = value;
      if (reserved) this.reservedBit[index] = true;
    };
    BitMatrix.prototype.get = function(row, col) {
      return this.data[row * this.size + col];
    };
    BitMatrix.prototype.xor = function(row, col, value) {
      this.data[row * this.size + col] ^= value;
    };
    BitMatrix.prototype.isReserved = function(row, col) {
      return this.reservedBit[row * this.size + col];
    };
    module.exports = BitMatrix;
  }
});

// node_modules/qrcode/lib/core/alignment-pattern.js
var require_alignment_pattern = __commonJS({
  "node_modules/qrcode/lib/core/alignment-pattern.js"(exports) {
    var getSymbolSize = require_utils().getSymbolSize;
    exports.getRowColCoords = function getRowColCoords(version) {
      if (version === 1) return [];
      const posCount = Math.floor(version / 7) + 2;
      const size = getSymbolSize(version);
      const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2;
      const positions = [size - 7];
      for (let i3 = 1; i3 < posCount - 1; i3++) {
        positions[i3] = positions[i3 - 1] - intervals;
      }
      positions.push(6);
      return positions.reverse();
    };
    exports.getPositions = function getPositions(version) {
      const coords = [];
      const pos = exports.getRowColCoords(version);
      const posLength = pos.length;
      for (let i3 = 0; i3 < posLength; i3++) {
        for (let j2 = 0; j2 < posLength; j2++) {
          if (i3 === 0 && j2 === 0 || // top-left
          i3 === 0 && j2 === posLength - 1 || // bottom-left
          i3 === posLength - 1 && j2 === 0) {
            continue;
          }
          coords.push([pos[i3], pos[j2]]);
        }
      }
      return coords;
    };
  }
});

// node_modules/qrcode/lib/core/finder-pattern.js
var require_finder_pattern = __commonJS({
  "node_modules/qrcode/lib/core/finder-pattern.js"(exports) {
    var getSymbolSize = require_utils().getSymbolSize;
    var FINDER_PATTERN_SIZE = 7;
    exports.getPositions = function getPositions(version) {
      const size = getSymbolSize(version);
      return [
        // top-left
        [0, 0],
        // top-right
        [size - FINDER_PATTERN_SIZE, 0],
        // bottom-left
        [0, size - FINDER_PATTERN_SIZE]
      ];
    };
  }
});

// node_modules/qrcode/lib/core/mask-pattern.js
var require_mask_pattern = __commonJS({
  "node_modules/qrcode/lib/core/mask-pattern.js"(exports) {
    exports.Patterns = {
      PATTERN000: 0,
      PATTERN001: 1,
      PATTERN010: 2,
      PATTERN011: 3,
      PATTERN100: 4,
      PATTERN101: 5,
      PATTERN110: 6,
      PATTERN111: 7
    };
    var PenaltyScores = {
      N1: 3,
      N2: 3,
      N3: 40,
      N4: 10
    };
    exports.isValid = function isValid(mask) {
      return mask != null && mask !== "" && !isNaN(mask) && mask >= 0 && mask <= 7;
    };
    exports.from = function from(value) {
      return exports.isValid(value) ? parseInt(value, 10) : void 0;
    };
    exports.getPenaltyN1 = function getPenaltyN1(data) {
      const size = data.size;
      let points = 0;
      let sameCountCol = 0;
      let sameCountRow = 0;
      let lastCol = null;
      let lastRow = null;
      for (let row = 0; row < size; row++) {
        sameCountCol = sameCountRow = 0;
        lastCol = lastRow = null;
        for (let col = 0; col < size; col++) {
          let module2 = data.get(row, col);
          if (module2 === lastCol) {
            sameCountCol++;
          } else {
            if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5);
            lastCol = module2;
            sameCountCol = 1;
          }
          module2 = data.get(col, row);
          if (module2 === lastRow) {
            sameCountRow++;
          } else {
            if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5);
            lastRow = module2;
            sameCountRow = 1;
          }
        }
        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5);
        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5);
      }
      return points;
    };
    exports.getPenaltyN2 = function getPenaltyN2(data) {
      const size = data.size;
      let points = 0;
      for (let row = 0; row < size - 1; row++) {
        for (let col = 0; col < size - 1; col++) {
          const last = data.get(row, col) + data.get(row, col + 1) + data.get(row + 1, col) + data.get(row + 1, col + 1);
          if (last === 4 || last === 0) points++;
        }
      }
      return points * PenaltyScores.N2;
    };
    exports.getPenaltyN3 = function getPenaltyN3(data) {
      const size = data.size;
      let points = 0;
      let bitsCol = 0;
      let bitsRow = 0;
      for (let row = 0; row < size; row++) {
        bitsCol = bitsRow = 0;
        for (let col = 0; col < size; col++) {
          bitsCol = bitsCol << 1 & 2047 | data.get(row, col);
          if (col >= 10 && (bitsCol === 1488 || bitsCol === 93)) points++;
          bitsRow = bitsRow << 1 & 2047 | data.get(col, row);
          if (col >= 10 && (bitsRow === 1488 || bitsRow === 93)) points++;
        }
      }
      return points * PenaltyScores.N3;
    };
    exports.getPenaltyN4 = function getPenaltyN4(data) {
      let darkCount = 0;
      const modulesCount = data.data.length;
      for (let i3 = 0; i3 < modulesCount; i3++) darkCount += data.data[i3];
      const k3 = Math.abs(Math.ceil(darkCount * 100 / modulesCount / 5) - 10);
      return k3 * PenaltyScores.N4;
    };
    function getMaskAt(maskPattern, i3, j2) {
      switch (maskPattern) {
        case exports.Patterns.PATTERN000:
          return (i3 + j2) % 2 === 0;
        case exports.Patterns.PATTERN001:
          return i3 % 2 === 0;
        case exports.Patterns.PATTERN010:
          return j2 % 3 === 0;
        case exports.Patterns.PATTERN011:
          return (i3 + j2) % 3 === 0;
        case exports.Patterns.PATTERN100:
          return (Math.floor(i3 / 2) + Math.floor(j2 / 3)) % 2 === 0;
        case exports.Patterns.PATTERN101:
          return i3 * j2 % 2 + i3 * j2 % 3 === 0;
        case exports.Patterns.PATTERN110:
          return (i3 * j2 % 2 + i3 * j2 % 3) % 2 === 0;
        case exports.Patterns.PATTERN111:
          return (i3 * j2 % 3 + (i3 + j2) % 2) % 2 === 0;
        default:
          throw new Error("bad maskPattern:" + maskPattern);
      }
    }
    exports.applyMask = function applyMask(pattern, data) {
      const size = data.size;
      for (let col = 0; col < size; col++) {
        for (let row = 0; row < size; row++) {
          if (data.isReserved(row, col)) continue;
          data.xor(row, col, getMaskAt(pattern, row, col));
        }
      }
    };
    exports.getBestMask = function getBestMask(data, setupFormatFunc) {
      const numPatterns = Object.keys(exports.Patterns).length;
      let bestPattern = 0;
      let lowerPenalty = Infinity;
      for (let p = 0; p < numPatterns; p++) {
        setupFormatFunc(p);
        exports.applyMask(p, data);
        const penalty = exports.getPenaltyN1(data) + exports.getPenaltyN2(data) + exports.getPenaltyN3(data) + exports.getPenaltyN4(data);
        exports.applyMask(p, data);
        if (penalty < lowerPenalty) {
          lowerPenalty = penalty;
          bestPattern = p;
        }
      }
      return bestPattern;
    };
  }
});

// node_modules/qrcode/lib/core/error-correction-code.js
var require_error_correction_code = __commonJS({
  "node_modules/qrcode/lib/core/error-correction-code.js"(exports) {
    var ECLevel = require_error_correction_level();
    var EC_BLOCKS_TABLE = [
      // L  M  Q  H
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      1,
      2,
      2,
      1,
      2,
      2,
      4,
      1,
      2,
      4,
      4,
      2,
      4,
      4,
      4,
      2,
      4,
      6,
      5,
      2,
      4,
      6,
      6,
      2,
      5,
      8,
      8,
      4,
      5,
      8,
      8,
      4,
      5,
      8,
      11,
      4,
      8,
      10,
      11,
      4,
      9,
      12,
      16,
      4,
      9,
      16,
      16,
      6,
      10,
      12,
      18,
      6,
      10,
      17,
      16,
      6,
      11,
      16,
      19,
      6,
      13,
      18,
      21,
      7,
      14,
      21,
      25,
      8,
      16,
      20,
      25,
      8,
      17,
      23,
      25,
      9,
      17,
      23,
      34,
      9,
      18,
      25,
      30,
      10,
      20,
      27,
      32,
      12,
      21,
      29,
      35,
      12,
      23,
      34,
      37,
      12,
      25,
      34,
      40,
      13,
      26,
      35,
      42,
      14,
      28,
      38,
      45,
      15,
      29,
      40,
      48,
      16,
      31,
      43,
      51,
      17,
      33,
      45,
      54,
      18,
      35,
      48,
      57,
      19,
      37,
      51,
      60,
      19,
      38,
      53,
      63,
      20,
      40,
      56,
      66,
      21,
      43,
      59,
      70,
      22,
      45,
      62,
      74,
      24,
      47,
      65,
      77,
      25,
      49,
      68,
      81
    ];
    var EC_CODEWORDS_TABLE = [
      // L  M  Q  H
      7,
      10,
      13,
      17,
      10,
      16,
      22,
      28,
      15,
      26,
      36,
      44,
      20,
      36,
      52,
      64,
      26,
      48,
      72,
      88,
      36,
      64,
      96,
      112,
      40,
      72,
      108,
      130,
      48,
      88,
      132,
      156,
      60,
      110,
      160,
      192,
      72,
      130,
      192,
      224,
      80,
      150,
      224,
      264,
      96,
      176,
      260,
      308,
      104,
      198,
      288,
      352,
      120,
      216,
      320,
      384,
      132,
      240,
      360,
      432,
      144,
      280,
      408,
      480,
      168,
      308,
      448,
      532,
      180,
      338,
      504,
      588,
      196,
      364,
      546,
      650,
      224,
      416,
      600,
      700,
      224,
      442,
      644,
      750,
      252,
      476,
      690,
      816,
      270,
      504,
      750,
      900,
      300,
      560,
      810,
      960,
      312,
      588,
      870,
      1050,
      336,
      644,
      952,
      1110,
      360,
      700,
      1020,
      1200,
      390,
      728,
      1050,
      1260,
      420,
      784,
      1140,
      1350,
      450,
      812,
      1200,
      1440,
      480,
      868,
      1290,
      1530,
      510,
      924,
      1350,
      1620,
      540,
      980,
      1440,
      1710,
      570,
      1036,
      1530,
      1800,
      570,
      1064,
      1590,
      1890,
      600,
      1120,
      1680,
      1980,
      630,
      1204,
      1770,
      2100,
      660,
      1260,
      1860,
      2220,
      720,
      1316,
      1950,
      2310,
      750,
      1372,
      2040,
      2430
    ];
    exports.getBlocksCount = function getBlocksCount(version, errorCorrectionLevel) {
      switch (errorCorrectionLevel) {
        case ECLevel.L:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 0];
        case ECLevel.M:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 1];
        case ECLevel.Q:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 2];
        case ECLevel.H:
          return EC_BLOCKS_TABLE[(version - 1) * 4 + 3];
        default:
          return void 0;
      }
    };
    exports.getTotalCodewordsCount = function getTotalCodewordsCount(version, errorCorrectionLevel) {
      switch (errorCorrectionLevel) {
        case ECLevel.L:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0];
        case ECLevel.M:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1];
        case ECLevel.Q:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2];
        case ECLevel.H:
          return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3];
        default:
          return void 0;
      }
    };
  }
});

// node_modules/qrcode/lib/core/galois-field.js
var require_galois_field = __commonJS({
  "node_modules/qrcode/lib/core/galois-field.js"(exports) {
    var EXP_TABLE = new Uint8Array(512);
    var LOG_TABLE = new Uint8Array(256);
    (function initTables() {
      let x = 1;
      for (let i3 = 0; i3 < 255; i3++) {
        EXP_TABLE[i3] = x;
        LOG_TABLE[x] = i3;
        x <<= 1;
        if (x & 256) {
          x ^= 285;
        }
      }
      for (let i3 = 255; i3 < 512; i3++) {
        EXP_TABLE[i3] = EXP_TABLE[i3 - 255];
      }
    })();
    exports.log = function log(n) {
      if (n < 1) throw new Error("log(" + n + ")");
      return LOG_TABLE[n];
    };
    exports.exp = function exp(n) {
      return EXP_TABLE[n];
    };
    exports.mul = function mul(x, y3) {
      if (x === 0 || y3 === 0) return 0;
      return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y3]];
    };
  }
});

// node_modules/qrcode/lib/core/polynomial.js
var require_polynomial = __commonJS({
  "node_modules/qrcode/lib/core/polynomial.js"(exports) {
    var GF = require_galois_field();
    exports.mul = function mul(p1, p2) {
      const coeff = new Uint8Array(p1.length + p2.length - 1);
      for (let i3 = 0; i3 < p1.length; i3++) {
        for (let j2 = 0; j2 < p2.length; j2++) {
          coeff[i3 + j2] ^= GF.mul(p1[i3], p2[j2]);
        }
      }
      return coeff;
    };
    exports.mod = function mod(divident, divisor) {
      let result = new Uint8Array(divident);
      while (result.length - divisor.length >= 0) {
        const coeff = result[0];
        for (let i3 = 0; i3 < divisor.length; i3++) {
          result[i3] ^= GF.mul(divisor[i3], coeff);
        }
        let offset = 0;
        while (offset < result.length && result[offset] === 0) offset++;
        result = result.slice(offset);
      }
      return result;
    };
    exports.generateECPolynomial = function generateECPolynomial(degree) {
      let poly = new Uint8Array([1]);
      for (let i3 = 0; i3 < degree; i3++) {
        poly = exports.mul(poly, new Uint8Array([1, GF.exp(i3)]));
      }
      return poly;
    };
  }
});

// node_modules/qrcode/lib/core/reed-solomon-encoder.js
var require_reed_solomon_encoder = __commonJS({
  "node_modules/qrcode/lib/core/reed-solomon-encoder.js"(exports, module) {
    var Polynomial = require_polynomial();
    function ReedSolomonEncoder(degree) {
      this.genPoly = void 0;
      this.degree = degree;
      if (this.degree) this.initialize(this.degree);
    }
    ReedSolomonEncoder.prototype.initialize = function initialize(degree) {
      this.degree = degree;
      this.genPoly = Polynomial.generateECPolynomial(this.degree);
    };
    ReedSolomonEncoder.prototype.encode = function encode(data) {
      if (!this.genPoly) {
        throw new Error("Encoder not initialized");
      }
      const paddedData = new Uint8Array(data.length + this.degree);
      paddedData.set(data);
      const remainder = Polynomial.mod(paddedData, this.genPoly);
      const start = this.degree - remainder.length;
      if (start > 0) {
        const buff = new Uint8Array(this.degree);
        buff.set(remainder, start);
        return buff;
      }
      return remainder;
    };
    module.exports = ReedSolomonEncoder;
  }
});

// node_modules/qrcode/lib/core/version-check.js
var require_version_check = __commonJS({
  "node_modules/qrcode/lib/core/version-check.js"(exports) {
    exports.isValid = function isValid(version) {
      return !isNaN(version) && version >= 1 && version <= 40;
    };
  }
});

// node_modules/qrcode/lib/core/regex.js
var require_regex = __commonJS({
  "node_modules/qrcode/lib/core/regex.js"(exports) {
    var numeric = "[0-9]+";
    var alphanumeric = "[A-Z $%*+\\-./:]+";
    var kanji = "(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";
    kanji = kanji.replace(/u/g, "\\u");
    var byte = "(?:(?![A-Z0-9 $%*+\\-./:]|" + kanji + ")(?:.|[\r\n]))+";
    exports.KANJI = new RegExp(kanji, "g");
    exports.BYTE_KANJI = new RegExp("[^A-Z0-9 $%*+\\-./:]+", "g");
    exports.BYTE = new RegExp(byte, "g");
    exports.NUMERIC = new RegExp(numeric, "g");
    exports.ALPHANUMERIC = new RegExp(alphanumeric, "g");
    var TEST_KANJI = new RegExp("^" + kanji + "$");
    var TEST_NUMERIC = new RegExp("^" + numeric + "$");
    var TEST_ALPHANUMERIC = new RegExp("^[A-Z0-9 $%*+\\-./:]+$");
    exports.testKanji = function testKanji(str) {
      return TEST_KANJI.test(str);
    };
    exports.testNumeric = function testNumeric(str) {
      return TEST_NUMERIC.test(str);
    };
    exports.testAlphanumeric = function testAlphanumeric(str) {
      return TEST_ALPHANUMERIC.test(str);
    };
  }
});

// node_modules/qrcode/lib/core/mode.js
var require_mode = __commonJS({
  "node_modules/qrcode/lib/core/mode.js"(exports) {
    var VersionCheck = require_version_check();
    var Regex = require_regex();
    exports.NUMERIC = {
      id: "Numeric",
      bit: 1 << 0,
      ccBits: [10, 12, 14]
    };
    exports.ALPHANUMERIC = {
      id: "Alphanumeric",
      bit: 1 << 1,
      ccBits: [9, 11, 13]
    };
    exports.BYTE = {
      id: "Byte",
      bit: 1 << 2,
      ccBits: [8, 16, 16]
    };
    exports.KANJI = {
      id: "Kanji",
      bit: 1 << 3,
      ccBits: [8, 10, 12]
    };
    exports.MIXED = {
      bit: -1
    };
    exports.getCharCountIndicator = function getCharCountIndicator(mode, version) {
      if (!mode.ccBits) throw new Error("Invalid mode: " + mode);
      if (!VersionCheck.isValid(version)) {
        throw new Error("Invalid version: " + version);
      }
      if (version >= 1 && version < 10) return mode.ccBits[0];
      else if (version < 27) return mode.ccBits[1];
      return mode.ccBits[2];
    };
    exports.getBestModeForData = function getBestModeForData(dataStr) {
      if (Regex.testNumeric(dataStr)) return exports.NUMERIC;
      else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC;
      else if (Regex.testKanji(dataStr)) return exports.KANJI;
      else return exports.BYTE;
    };
    exports.toString = function toString(mode) {
      if (mode && mode.id) return mode.id;
      throw new Error("Invalid mode");
    };
    exports.isValid = function isValid(mode) {
      return mode && mode.bit && mode.ccBits;
    };
    function fromString(string) {
      if (typeof string !== "string") {
        throw new Error("Param is not a string");
      }
      const lcStr = string.toLowerCase();
      switch (lcStr) {
        case "numeric":
          return exports.NUMERIC;
        case "alphanumeric":
          return exports.ALPHANUMERIC;
        case "kanji":
          return exports.KANJI;
        case "byte":
          return exports.BYTE;
        default:
          throw new Error("Unknown mode: " + string);
      }
    }
    exports.from = function from(value, defaultValue) {
      if (exports.isValid(value)) {
        return value;
      }
      try {
        return fromString(value);
      } catch (e) {
        return defaultValue;
      }
    };
  }
});

// node_modules/qrcode/lib/core/version.js
var require_version = __commonJS({
  "node_modules/qrcode/lib/core/version.js"(exports) {
    var Utils = require_utils();
    var ECCode = require_error_correction_code();
    var ECLevel = require_error_correction_level();
    var Mode = require_mode();
    var VersionCheck = require_version_check();
    var G18 = 1 << 12 | 1 << 11 | 1 << 10 | 1 << 9 | 1 << 8 | 1 << 5 | 1 << 2 | 1 << 0;
    var G18_BCH = Utils.getBCHDigit(G18);
    function getBestVersionForDataLength(mode, length, errorCorrectionLevel) {
      for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {
        if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {
          return currentVersion;
        }
      }
      return void 0;
    }
    function getReservedBitsCount(mode, version) {
      return Mode.getCharCountIndicator(mode, version) + 4;
    }
    function getTotalBitsFromDataArray(segments, version) {
      let totalBits = 0;
      segments.forEach(function(data) {
        const reservedBits = getReservedBitsCount(data.mode, version);
        totalBits += reservedBits + data.getBitsLength();
      });
      return totalBits;
    }
    function getBestVersionForMixedData(segments, errorCorrectionLevel) {
      for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {
        const length = getTotalBitsFromDataArray(segments, currentVersion);
        if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {
          return currentVersion;
        }
      }
      return void 0;
    }
    exports.from = function from(value, defaultValue) {
      if (VersionCheck.isValid(value)) {
        return parseInt(value, 10);
      }
      return defaultValue;
    };
    exports.getCapacity = function getCapacity(version, errorCorrectionLevel, mode) {
      if (!VersionCheck.isValid(version)) {
        throw new Error("Invalid QR Code version");
      }
      if (typeof mode === "undefined") mode = Mode.BYTE;
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;
      if (mode === Mode.MIXED) return dataTotalCodewordsBits;
      const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version);
      switch (mode) {
        case Mode.NUMERIC:
          return Math.floor(usableBits / 10 * 3);
        case Mode.ALPHANUMERIC:
          return Math.floor(usableBits / 11 * 2);
        case Mode.KANJI:
          return Math.floor(usableBits / 13);
        case Mode.BYTE:
        default:
          return Math.floor(usableBits / 8);
      }
    };
    exports.getBestVersionForData = function getBestVersionForData(data, errorCorrectionLevel) {
      let seg;
      const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M);
      if (Array.isArray(data)) {
        if (data.length > 1) {
          return getBestVersionForMixedData(data, ecl);
        }
        if (data.length === 0) {
          return 1;
        }
        seg = data[0];
      } else {
        seg = data;
      }
      return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl);
    };
    exports.getEncodedBits = function getEncodedBits(version) {
      if (!VersionCheck.isValid(version) || version < 7) {
        throw new Error("Invalid QR Code version");
      }
      let d = version << 12;
      while (Utils.getBCHDigit(d) - G18_BCH >= 0) {
        d ^= G18 << Utils.getBCHDigit(d) - G18_BCH;
      }
      return version << 12 | d;
    };
  }
});

// node_modules/qrcode/lib/core/format-info.js
var require_format_info = __commonJS({
  "node_modules/qrcode/lib/core/format-info.js"(exports) {
    var Utils = require_utils();
    var G15 = 1 << 10 | 1 << 8 | 1 << 5 | 1 << 4 | 1 << 2 | 1 << 1 | 1 << 0;
    var G15_MASK = 1 << 14 | 1 << 12 | 1 << 10 | 1 << 4 | 1 << 1;
    var G15_BCH = Utils.getBCHDigit(G15);
    exports.getEncodedBits = function getEncodedBits(errorCorrectionLevel, mask) {
      const data = errorCorrectionLevel.bit << 3 | mask;
      let d = data << 10;
      while (Utils.getBCHDigit(d) - G15_BCH >= 0) {
        d ^= G15 << Utils.getBCHDigit(d) - G15_BCH;
      }
      return (data << 10 | d) ^ G15_MASK;
    };
  }
});

// node_modules/qrcode/lib/core/numeric-data.js
var require_numeric_data = __commonJS({
  "node_modules/qrcode/lib/core/numeric-data.js"(exports, module) {
    var Mode = require_mode();
    function NumericData(data) {
      this.mode = Mode.NUMERIC;
      this.data = data.toString();
    }
    NumericData.getBitsLength = function getBitsLength(length) {
      return 10 * Math.floor(length / 3) + (length % 3 ? length % 3 * 3 + 1 : 0);
    };
    NumericData.prototype.getLength = function getLength() {
      return this.data.length;
    };
    NumericData.prototype.getBitsLength = function getBitsLength() {
      return NumericData.getBitsLength(this.data.length);
    };
    NumericData.prototype.write = function write(bitBuffer) {
      let i3, group, value;
      for (i3 = 0; i3 + 3 <= this.data.length; i3 += 3) {
        group = this.data.substr(i3, 3);
        value = parseInt(group, 10);
        bitBuffer.put(value, 10);
      }
      const remainingNum = this.data.length - i3;
      if (remainingNum > 0) {
        group = this.data.substr(i3);
        value = parseInt(group, 10);
        bitBuffer.put(value, remainingNum * 3 + 1);
      }
    };
    module.exports = NumericData;
  }
});

// node_modules/qrcode/lib/core/alphanumeric-data.js
var require_alphanumeric_data = __commonJS({
  "node_modules/qrcode/lib/core/alphanumeric-data.js"(exports, module) {
    var Mode = require_mode();
    var ALPHA_NUM_CHARS = [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z",
      " ",
      "$",
      "%",
      "*",
      "+",
      "-",
      ".",
      "/",
      ":"
    ];
    function AlphanumericData(data) {
      this.mode = Mode.ALPHANUMERIC;
      this.data = data;
    }
    AlphanumericData.getBitsLength = function getBitsLength(length) {
      return 11 * Math.floor(length / 2) + 6 * (length % 2);
    };
    AlphanumericData.prototype.getLength = function getLength() {
      return this.data.length;
    };
    AlphanumericData.prototype.getBitsLength = function getBitsLength() {
      return AlphanumericData.getBitsLength(this.data.length);
    };
    AlphanumericData.prototype.write = function write(bitBuffer) {
      let i3;
      for (i3 = 0; i3 + 2 <= this.data.length; i3 += 2) {
        let value = ALPHA_NUM_CHARS.indexOf(this.data[i3]) * 45;
        value += ALPHA_NUM_CHARS.indexOf(this.data[i3 + 1]);
        bitBuffer.put(value, 11);
      }
      if (this.data.length % 2) {
        bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i3]), 6);
      }
    };
    module.exports = AlphanumericData;
  }
});

// node_modules/qrcode/lib/core/byte-data.js
var require_byte_data = __commonJS({
  "node_modules/qrcode/lib/core/byte-data.js"(exports, module) {
    var Mode = require_mode();
    function ByteData(data) {
      this.mode = Mode.BYTE;
      if (typeof data === "string") {
        this.data = new TextEncoder().encode(data);
      } else {
        this.data = new Uint8Array(data);
      }
    }
    ByteData.getBitsLength = function getBitsLength(length) {
      return length * 8;
    };
    ByteData.prototype.getLength = function getLength() {
      return this.data.length;
    };
    ByteData.prototype.getBitsLength = function getBitsLength() {
      return ByteData.getBitsLength(this.data.length);
    };
    ByteData.prototype.write = function(bitBuffer) {
      for (let i3 = 0, l = this.data.length; i3 < l; i3++) {
        bitBuffer.put(this.data[i3], 8);
      }
    };
    module.exports = ByteData;
  }
});

// node_modules/qrcode/lib/core/kanji-data.js
var require_kanji_data = __commonJS({
  "node_modules/qrcode/lib/core/kanji-data.js"(exports, module) {
    var Mode = require_mode();
    var Utils = require_utils();
    function KanjiData(data) {
      this.mode = Mode.KANJI;
      this.data = data;
    }
    KanjiData.getBitsLength = function getBitsLength(length) {
      return length * 13;
    };
    KanjiData.prototype.getLength = function getLength() {
      return this.data.length;
    };
    KanjiData.prototype.getBitsLength = function getBitsLength() {
      return KanjiData.getBitsLength(this.data.length);
    };
    KanjiData.prototype.write = function(bitBuffer) {
      let i3;
      for (i3 = 0; i3 < this.data.length; i3++) {
        let value = Utils.toSJIS(this.data[i3]);
        if (value >= 33088 && value <= 40956) {
          value -= 33088;
        } else if (value >= 57408 && value <= 60351) {
          value -= 49472;
        } else {
          throw new Error(
            "Invalid SJIS character: " + this.data[i3] + "\nMake sure your charset is UTF-8"
          );
        }
        value = (value >>> 8 & 255) * 192 + (value & 255);
        bitBuffer.put(value, 13);
      }
    };
    module.exports = KanjiData;
  }
});

// node_modules/dijkstrajs/dijkstra.js
var require_dijkstra = __commonJS({
  "node_modules/dijkstrajs/dijkstra.js"(exports, module) {
    "use strict";
    var dijkstra = {
      single_source_shortest_paths: function(graph, s, d) {
        var predecessors = {};
        var costs = {};
        costs[s] = 0;
        var open = dijkstra.PriorityQueue.make();
        open.push(s, 0);
        var closest, u, v3, cost_of_s_to_u, adjacent_nodes, cost_of_e, cost_of_s_to_u_plus_cost_of_e, cost_of_s_to_v, first_visit;
        while (!open.empty()) {
          closest = open.pop();
          u = closest.value;
          cost_of_s_to_u = closest.cost;
          adjacent_nodes = graph[u] || {};
          for (v3 in adjacent_nodes) {
            if (adjacent_nodes.hasOwnProperty(v3)) {
              cost_of_e = adjacent_nodes[v3];
              cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;
              cost_of_s_to_v = costs[v3];
              first_visit = typeof costs[v3] === "undefined";
              if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {
                costs[v3] = cost_of_s_to_u_plus_cost_of_e;
                open.push(v3, cost_of_s_to_u_plus_cost_of_e);
                predecessors[v3] = u;
              }
            }
          }
        }
        if (typeof d !== "undefined" && typeof costs[d] === "undefined") {
          var msg = ["Could not find a path from ", s, " to ", d, "."].join("");
          throw new Error(msg);
        }
        return predecessors;
      },
      extract_shortest_path_from_predecessor_list: function(predecessors, d) {
        var nodes = [];
        var u = d;
        var predecessor;
        while (u) {
          nodes.push(u);
          predecessor = predecessors[u];
          u = predecessors[u];
        }
        nodes.reverse();
        return nodes;
      },
      find_path: function(graph, s, d) {
        var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);
        return dijkstra.extract_shortest_path_from_predecessor_list(
          predecessors,
          d
        );
      },
      /**
       * A very naive priority queue implementation.
       */
      PriorityQueue: {
        make: function(opts) {
          var T = dijkstra.PriorityQueue, t2 = {}, key;
          opts = opts || {};
          for (key in T) {
            if (T.hasOwnProperty(key)) {
              t2[key] = T[key];
            }
          }
          t2.queue = [];
          t2.sorter = opts.sorter || T.default_sorter;
          return t2;
        },
        default_sorter: function(a2, b) {
          return a2.cost - b.cost;
        },
        /**
         * Add a new item to the queue and ensure the highest priority element
         * is at the front of the queue.
         */
        push: function(value, cost) {
          var item = { value, cost };
          this.queue.push(item);
          this.queue.sort(this.sorter);
        },
        /**
         * Return the highest priority element in the queue.
         */
        pop: function() {
          return this.queue.shift();
        },
        empty: function() {
          return this.queue.length === 0;
        }
      }
    };
    if (typeof module !== "undefined") {
      module.exports = dijkstra;
    }
  }
});

// node_modules/qrcode/lib/core/segments.js
var require_segments = __commonJS({
  "node_modules/qrcode/lib/core/segments.js"(exports) {
    var Mode = require_mode();
    var NumericData = require_numeric_data();
    var AlphanumericData = require_alphanumeric_data();
    var ByteData = require_byte_data();
    var KanjiData = require_kanji_data();
    var Regex = require_regex();
    var Utils = require_utils();
    var dijkstra = require_dijkstra();
    function getStringByteLength(str) {
      return unescape(encodeURIComponent(str)).length;
    }
    function getSegments(regex, mode, str) {
      const segments = [];
      let result;
      while ((result = regex.exec(str)) !== null) {
        segments.push({
          data: result[0],
          index: result.index,
          mode,
          length: result[0].length
        });
      }
      return segments;
    }
    function getSegmentsFromString(dataStr) {
      const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr);
      const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr);
      let byteSegs;
      let kanjiSegs;
      if (Utils.isKanjiModeEnabled()) {
        byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr);
        kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr);
      } else {
        byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr);
        kanjiSegs = [];
      }
      const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs);
      return segs.sort(function(s1, s2) {
        return s1.index - s2.index;
      }).map(function(obj) {
        return {
          data: obj.data,
          mode: obj.mode,
          length: obj.length
        };
      });
    }
    function getSegmentBitsLength(length, mode) {
      switch (mode) {
        case Mode.NUMERIC:
          return NumericData.getBitsLength(length);
        case Mode.ALPHANUMERIC:
          return AlphanumericData.getBitsLength(length);
        case Mode.KANJI:
          return KanjiData.getBitsLength(length);
        case Mode.BYTE:
          return ByteData.getBitsLength(length);
      }
    }
    function mergeSegments(segs) {
      return segs.reduce(function(acc, curr) {
        const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null;
        if (prevSeg && prevSeg.mode === curr.mode) {
          acc[acc.length - 1].data += curr.data;
          return acc;
        }
        acc.push(curr);
        return acc;
      }, []);
    }
    function buildNodes(segs) {
      const nodes = [];
      for (let i3 = 0; i3 < segs.length; i3++) {
        const seg = segs[i3];
        switch (seg.mode) {
          case Mode.NUMERIC:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },
              { data: seg.data, mode: Mode.BYTE, length: seg.length }
            ]);
            break;
          case Mode.ALPHANUMERIC:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.BYTE, length: seg.length }
            ]);
            break;
          case Mode.KANJI:
            nodes.push([
              seg,
              { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }
            ]);
            break;
          case Mode.BYTE:
            nodes.push([
              { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }
            ]);
        }
      }
      return nodes;
    }
    function buildGraph(nodes, version) {
      const table = {};
      const graph = { start: {} };
      let prevNodeIds = ["start"];
      for (let i3 = 0; i3 < nodes.length; i3++) {
        const nodeGroup = nodes[i3];
        const currentNodeIds = [];
        for (let j2 = 0; j2 < nodeGroup.length; j2++) {
          const node = nodeGroup[j2];
          const key = "" + i3 + j2;
          currentNodeIds.push(key);
          table[key] = { node, lastCount: 0 };
          graph[key] = {};
          for (let n = 0; n < prevNodeIds.length; n++) {
            const prevNodeId = prevNodeIds[n];
            if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {
              graph[prevNodeId][key] = getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) - getSegmentBitsLength(table[prevNodeId].lastCount, node.mode);
              table[prevNodeId].lastCount += node.length;
            } else {
              if (table[prevNodeId]) table[prevNodeId].lastCount = node.length;
              graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) + 4 + Mode.getCharCountIndicator(node.mode, version);
            }
          }
        }
        prevNodeIds = currentNodeIds;
      }
      for (let n = 0; n < prevNodeIds.length; n++) {
        graph[prevNodeIds[n]].end = 0;
      }
      return { map: graph, table };
    }
    function buildSingleSegment(data, modesHint) {
      let mode;
      const bestMode = Mode.getBestModeForData(data);
      mode = Mode.from(modesHint, bestMode);
      if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {
        throw new Error('"' + data + '" cannot be encoded with mode ' + Mode.toString(mode) + ".\n Suggested mode is: " + Mode.toString(bestMode));
      }
      if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {
        mode = Mode.BYTE;
      }
      switch (mode) {
        case Mode.NUMERIC:
          return new NumericData(data);
        case Mode.ALPHANUMERIC:
          return new AlphanumericData(data);
        case Mode.KANJI:
          return new KanjiData(data);
        case Mode.BYTE:
          return new ByteData(data);
      }
    }
    exports.fromArray = function fromArray(array) {
      return array.reduce(function(acc, seg) {
        if (typeof seg === "string") {
          acc.push(buildSingleSegment(seg, null));
        } else if (seg.data) {
          acc.push(buildSingleSegment(seg.data, seg.mode));
        }
        return acc;
      }, []);
    };
    exports.fromString = function fromString(data, version) {
      const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled());
      const nodes = buildNodes(segs);
      const graph = buildGraph(nodes, version);
      const path = dijkstra.find_path(graph.map, "start", "end");
      const optimizedSegs = [];
      for (let i3 = 1; i3 < path.length - 1; i3++) {
        optimizedSegs.push(graph.table[path[i3]].node);
      }
      return exports.fromArray(mergeSegments(optimizedSegs));
    };
    exports.rawSplit = function rawSplit(data) {
      return exports.fromArray(
        getSegmentsFromString(data, Utils.isKanjiModeEnabled())
      );
    };
  }
});

// node_modules/qrcode/lib/core/qrcode.js
var require_qrcode = __commonJS({
  "node_modules/qrcode/lib/core/qrcode.js"(exports) {
    var Utils = require_utils();
    var ECLevel = require_error_correction_level();
    var BitBuffer = require_bit_buffer();
    var BitMatrix = require_bit_matrix();
    var AlignmentPattern = require_alignment_pattern();
    var FinderPattern = require_finder_pattern();
    var MaskPattern = require_mask_pattern();
    var ECCode = require_error_correction_code();
    var ReedSolomonEncoder = require_reed_solomon_encoder();
    var Version = require_version();
    var FormatInfo = require_format_info();
    var Mode = require_mode();
    var Segments = require_segments();
    function setupFinderPattern(matrix, version) {
      const size = matrix.size;
      const pos = FinderPattern.getPositions(version);
      for (let i3 = 0; i3 < pos.length; i3++) {
        const row = pos[i3][0];
        const col = pos[i3][1];
        for (let r2 = -1; r2 <= 7; r2++) {
          if (row + r2 <= -1 || size <= row + r2) continue;
          for (let c2 = -1; c2 <= 7; c2++) {
            if (col + c2 <= -1 || size <= col + c2) continue;
            if (r2 >= 0 && r2 <= 6 && (c2 === 0 || c2 === 6) || c2 >= 0 && c2 <= 6 && (r2 === 0 || r2 === 6) || r2 >= 2 && r2 <= 4 && c2 >= 2 && c2 <= 4) {
              matrix.set(row + r2, col + c2, true, true);
            } else {
              matrix.set(row + r2, col + c2, false, true);
            }
          }
        }
      }
    }
    function setupTimingPattern(matrix) {
      const size = matrix.size;
      for (let r2 = 8; r2 < size - 8; r2++) {
        const value = r2 % 2 === 0;
        matrix.set(r2, 6, value, true);
        matrix.set(6, r2, value, true);
      }
    }
    function setupAlignmentPattern(matrix, version) {
      const pos = AlignmentPattern.getPositions(version);
      for (let i3 = 0; i3 < pos.length; i3++) {
        const row = pos[i3][0];
        const col = pos[i3][1];
        for (let r2 = -2; r2 <= 2; r2++) {
          for (let c2 = -2; c2 <= 2; c2++) {
            if (r2 === -2 || r2 === 2 || c2 === -2 || c2 === 2 || r2 === 0 && c2 === 0) {
              matrix.set(row + r2, col + c2, true, true);
            } else {
              matrix.set(row + r2, col + c2, false, true);
            }
          }
        }
      }
    }
    function setupVersionInfo(matrix, version) {
      const size = matrix.size;
      const bits = Version.getEncodedBits(version);
      let row, col, mod;
      for (let i3 = 0; i3 < 18; i3++) {
        row = Math.floor(i3 / 3);
        col = i3 % 3 + size - 8 - 3;
        mod = (bits >> i3 & 1) === 1;
        matrix.set(row, col, mod, true);
        matrix.set(col, row, mod, true);
      }
    }
    function setupFormatInfo(matrix, errorCorrectionLevel, maskPattern) {
      const size = matrix.size;
      const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern);
      let i3, mod;
      for (i3 = 0; i3 < 15; i3++) {
        mod = (bits >> i3 & 1) === 1;
        if (i3 < 6) {
          matrix.set(i3, 8, mod, true);
        } else if (i3 < 8) {
          matrix.set(i3 + 1, 8, mod, true);
        } else {
          matrix.set(size - 15 + i3, 8, mod, true);
        }
        if (i3 < 8) {
          matrix.set(8, size - i3 - 1, mod, true);
        } else if (i3 < 9) {
          matrix.set(8, 15 - i3 - 1 + 1, mod, true);
        } else {
          matrix.set(8, 15 - i3 - 1, mod, true);
        }
      }
      matrix.set(size - 8, 8, 1, true);
    }
    function setupData(matrix, data) {
      const size = matrix.size;
      let inc = -1;
      let row = size - 1;
      let bitIndex = 7;
      let byteIndex = 0;
      for (let col = size - 1; col > 0; col -= 2) {
        if (col === 6) col--;
        while (true) {
          for (let c2 = 0; c2 < 2; c2++) {
            if (!matrix.isReserved(row, col - c2)) {
              let dark = false;
              if (byteIndex < data.length) {
                dark = (data[byteIndex] >>> bitIndex & 1) === 1;
              }
              matrix.set(row, col - c2, dark);
              bitIndex--;
              if (bitIndex === -1) {
                byteIndex++;
                bitIndex = 7;
              }
            }
          }
          row += inc;
          if (row < 0 || size <= row) {
            row -= inc;
            inc = -inc;
            break;
          }
        }
      }
    }
    function createData(version, errorCorrectionLevel, segments) {
      const buffer = new BitBuffer();
      segments.forEach(function(data) {
        buffer.put(data.mode.bit, 4);
        buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version));
        data.write(buffer);
      });
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8;
      if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {
        buffer.put(0, 4);
      }
      while (buffer.getLengthInBits() % 8 !== 0) {
        buffer.putBit(0);
      }
      const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8;
      for (let i3 = 0; i3 < remainingByte; i3++) {
        buffer.put(i3 % 2 ? 17 : 236, 8);
      }
      return createCodewords(buffer, version, errorCorrectionLevel);
    }
    function createCodewords(bitBuffer, version, errorCorrectionLevel) {
      const totalCodewords = Utils.getSymbolTotalCodewords(version);
      const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel);
      const dataTotalCodewords = totalCodewords - ecTotalCodewords;
      const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel);
      const blocksInGroup2 = totalCodewords % ecTotalBlocks;
      const blocksInGroup1 = ecTotalBlocks - blocksInGroup2;
      const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks);
      const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks);
      const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1;
      const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1;
      const rs = new ReedSolomonEncoder(ecCount);
      let offset = 0;
      const dcData = new Array(ecTotalBlocks);
      const ecData = new Array(ecTotalBlocks);
      let maxDataSize = 0;
      const buffer = new Uint8Array(bitBuffer.buffer);
      for (let b = 0; b < ecTotalBlocks; b++) {
        const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2;
        dcData[b] = buffer.slice(offset, offset + dataSize);
        ecData[b] = rs.encode(dcData[b]);
        offset += dataSize;
        maxDataSize = Math.max(maxDataSize, dataSize);
      }
      const data = new Uint8Array(totalCodewords);
      let index = 0;
      let i3, r2;
      for (i3 = 0; i3 < maxDataSize; i3++) {
        for (r2 = 0; r2 < ecTotalBlocks; r2++) {
          if (i3 < dcData[r2].length) {
            data[index++] = dcData[r2][i3];
          }
        }
      }
      for (i3 = 0; i3 < ecCount; i3++) {
        for (r2 = 0; r2 < ecTotalBlocks; r2++) {
          data[index++] = ecData[r2][i3];
        }
      }
      return data;
    }
    function createSymbol(data, version, errorCorrectionLevel, maskPattern) {
      let segments;
      if (Array.isArray(data)) {
        segments = Segments.fromArray(data);
      } else if (typeof data === "string") {
        let estimatedVersion = version;
        if (!estimatedVersion) {
          const rawSegments = Segments.rawSplit(data);
          estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel);
        }
        segments = Segments.fromString(data, estimatedVersion || 40);
      } else {
        throw new Error("Invalid data");
      }
      const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel);
      if (!bestVersion) {
        throw new Error("The amount of data is too big to be stored in a QR Code");
      }
      if (!version) {
        version = bestVersion;
      } else if (version < bestVersion) {
        throw new Error(
          "\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: " + bestVersion + ".\n"
        );
      }
      const dataBits = createData(version, errorCorrectionLevel, segments);
      const moduleCount = Utils.getSymbolSize(version);
      const modules = new BitMatrix(moduleCount);
      setupFinderPattern(modules, version);
      setupTimingPattern(modules);
      setupAlignmentPattern(modules, version);
      setupFormatInfo(modules, errorCorrectionLevel, 0);
      if (version >= 7) {
        setupVersionInfo(modules, version);
      }
      setupData(modules, dataBits);
      if (isNaN(maskPattern)) {
        maskPattern = MaskPattern.getBestMask(
          modules,
          setupFormatInfo.bind(null, modules, errorCorrectionLevel)
        );
      }
      MaskPattern.applyMask(maskPattern, modules);
      setupFormatInfo(modules, errorCorrectionLevel, maskPattern);
      return {
        modules,
        version,
        errorCorrectionLevel,
        maskPattern,
        segments
      };
    }
    exports.create = function create(data, options) {
      if (typeof data === "undefined" || data === "") {
        throw new Error("No input text");
      }
      let errorCorrectionLevel = ECLevel.M;
      let version;
      let mask;
      if (typeof options !== "undefined") {
        errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M);
        version = Version.from(options.version);
        mask = MaskPattern.from(options.maskPattern);
        if (options.toSJISFunc) {
          Utils.setToSJISFunction(options.toSJISFunc);
        }
      }
      return createSymbol(data, version, errorCorrectionLevel, mask);
    };
  }
});

// node_modules/qrcode/lib/renderer/utils.js
var require_utils2 = __commonJS({
  "node_modules/qrcode/lib/renderer/utils.js"(exports) {
    function hex2rgba(hex) {
      if (typeof hex === "number") {
        hex = hex.toString();
      }
      if (typeof hex !== "string") {
        throw new Error("Color should be defined as hex string");
      }
      let hexCode = hex.slice().replace("#", "").split("");
      if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {
        throw new Error("Invalid hex color: " + hex);
      }
      if (hexCode.length === 3 || hexCode.length === 4) {
        hexCode = Array.prototype.concat.apply([], hexCode.map(function(c2) {
          return [c2, c2];
        }));
      }
      if (hexCode.length === 6) hexCode.push("F", "F");
      const hexValue = parseInt(hexCode.join(""), 16);
      return {
        r: hexValue >> 24 & 255,
        g: hexValue >> 16 & 255,
        b: hexValue >> 8 & 255,
        a: hexValue & 255,
        hex: "#" + hexCode.slice(0, 6).join("")
      };
    }
    exports.getOptions = function getOptions(options) {
      if (!options) options = {};
      if (!options.color) options.color = {};
      const margin = typeof options.margin === "undefined" || options.margin === null || options.margin < 0 ? 4 : options.margin;
      const width = options.width && options.width >= 21 ? options.width : void 0;
      const scale = options.scale || 4;
      return {
        width,
        scale: width ? 4 : scale,
        margin,
        color: {
          dark: hex2rgba(options.color.dark || "#000000ff"),
          light: hex2rgba(options.color.light || "#ffffffff")
        },
        type: options.type,
        rendererOpts: options.rendererOpts || {}
      };
    };
    exports.getScale = function getScale(qrSize, opts) {
      return opts.width && opts.width >= qrSize + opts.margin * 2 ? opts.width / (qrSize + opts.margin * 2) : opts.scale;
    };
    exports.getImageWidth = function getImageWidth(qrSize, opts) {
      const scale = exports.getScale(qrSize, opts);
      return Math.floor((qrSize + opts.margin * 2) * scale);
    };
    exports.qrToImageData = function qrToImageData(imgData, qr, opts) {
      const size = qr.modules.size;
      const data = qr.modules.data;
      const scale = exports.getScale(size, opts);
      const symbolSize = Math.floor((size + opts.margin * 2) * scale);
      const scaledMargin = opts.margin * scale;
      const palette = [opts.color.light, opts.color.dark];
      for (let i3 = 0; i3 < symbolSize; i3++) {
        for (let j2 = 0; j2 < symbolSize; j2++) {
          let posDst = (i3 * symbolSize + j2) * 4;
          let pxColor = opts.color.light;
          if (i3 >= scaledMargin && j2 >= scaledMargin && i3 < symbolSize - scaledMargin && j2 < symbolSize - scaledMargin) {
            const iSrc = Math.floor((i3 - scaledMargin) / scale);
            const jSrc = Math.floor((j2 - scaledMargin) / scale);
            pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0];
          }
          imgData[posDst++] = pxColor.r;
          imgData[posDst++] = pxColor.g;
          imgData[posDst++] = pxColor.b;
          imgData[posDst] = pxColor.a;
        }
      }
    };
  }
});

// node_modules/qrcode/lib/renderer/canvas.js
var require_canvas2 = __commonJS({
  "node_modules/qrcode/lib/renderer/canvas.js"(exports) {
    var Utils = require_utils2();
    function clearCanvas(ctx, canvas, size) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      if (!canvas.style) canvas.style = {};
      canvas.height = size;
      canvas.width = size;
      canvas.style.height = size + "px";
      canvas.style.width = size + "px";
    }
    function getCanvasElement() {
      try {
        return document.createElement("canvas");
      } catch (e) {
        throw new Error("You need to specify a canvas element");
      }
    }
    exports.render = function render(qrData, canvas, options) {
      let opts = options;
      let canvasEl = canvas;
      if (typeof opts === "undefined" && (!canvas || !canvas.getContext)) {
        opts = canvas;
        canvas = void 0;
      }
      if (!canvas) {
        canvasEl = getCanvasElement();
      }
      opts = Utils.getOptions(opts);
      const size = Utils.getImageWidth(qrData.modules.size, opts);
      const ctx = canvasEl.getContext("2d");
      const image = ctx.createImageData(size, size);
      Utils.qrToImageData(image.data, qrData, opts);
      clearCanvas(ctx, canvasEl, size);
      ctx.putImageData(image, 0, 0);
      return canvasEl;
    };
    exports.renderToDataURL = function renderToDataURL(qrData, canvas, options) {
      let opts = options;
      if (typeof opts === "undefined" && (!canvas || !canvas.getContext)) {
        opts = canvas;
        canvas = void 0;
      }
      if (!opts) opts = {};
      const canvasEl = exports.render(qrData, canvas, opts);
      const type = opts.type || "image/png";
      const rendererOpts = opts.rendererOpts || {};
      return canvasEl.toDataURL(type, rendererOpts.quality);
    };
  }
});

// node_modules/qrcode/lib/renderer/svg-tag.js
var require_svg_tag = __commonJS({
  "node_modules/qrcode/lib/renderer/svg-tag.js"(exports) {
    var Utils = require_utils2();
    function getColorAttrib(color, attrib) {
      const alpha = color.a / 255;
      const str = attrib + '="' + color.hex + '"';
      return alpha < 1 ? str + " " + attrib + '-opacity="' + alpha.toFixed(2).slice(1) + '"' : str;
    }
    function svgCmd(cmd, x, y3) {
      let str = cmd + x;
      if (typeof y3 !== "undefined") str += " " + y3;
      return str;
    }
    function qrToPath(data, size, margin) {
      let path = "";
      let moveBy = 0;
      let newRow = false;
      let lineLength = 0;
      for (let i3 = 0; i3 < data.length; i3++) {
        const col = Math.floor(i3 % size);
        const row = Math.floor(i3 / size);
        if (!col && !newRow) newRow = true;
        if (data[i3]) {
          lineLength++;
          if (!(i3 > 0 && col > 0 && data[i3 - 1])) {
            path += newRow ? svgCmd("M", col + margin, 0.5 + row + margin) : svgCmd("m", moveBy, 0);
            moveBy = 0;
            newRow = false;
          }
          if (!(col + 1 < size && data[i3 + 1])) {
            path += svgCmd("h", lineLength);
            lineLength = 0;
          }
        } else {
          moveBy++;
        }
      }
      return path;
    }
    exports.render = function render(qrData, options, cb) {
      const opts = Utils.getOptions(options);
      const size = qrData.modules.size;
      const data = qrData.modules.data;
      const qrcodesize = size + opts.margin * 2;
      const bg = !opts.color.light.a ? "" : "<path " + getColorAttrib(opts.color.light, "fill") + ' d="M0 0h' + qrcodesize + "v" + qrcodesize + 'H0z"/>';
      const path = "<path " + getColorAttrib(opts.color.dark, "stroke") + ' d="' + qrToPath(data, size, opts.margin) + '"/>';
      const viewBox = 'viewBox="0 0 ' + qrcodesize + " " + qrcodesize + '"';
      const width = !opts.width ? "" : 'width="' + opts.width + '" height="' + opts.width + '" ';
      const svgTag = '<svg xmlns="http://www.w3.org/2000/svg" ' + width + viewBox + ' shape-rendering="crispEdges">' + bg + path + "</svg>\n";
      if (typeof cb === "function") {
        cb(null, svgTag);
      }
      return svgTag;
    };
  }
});

// node_modules/qrcode/lib/browser.js
var require_browser = __commonJS({
  "node_modules/qrcode/lib/browser.js"(exports) {
    var canPromise = require_can_promise();
    var QRCode = require_qrcode();
    var CanvasRenderer = require_canvas2();
    var SvgRenderer = require_svg_tag();
    function renderCanvas(renderFunc, canvas, text, opts, cb) {
      const args = [].slice.call(arguments, 1);
      const argsNum = args.length;
      const isLastArgCb = typeof args[argsNum - 1] === "function";
      if (!isLastArgCb && !canPromise()) {
        throw new Error("Callback required as last argument");
      }
      if (isLastArgCb) {
        if (argsNum < 2) {
          throw new Error("Too few arguments provided");
        }
        if (argsNum === 2) {
          cb = text;
          text = canvas;
          canvas = opts = void 0;
        } else if (argsNum === 3) {
          if (canvas.getContext && typeof cb === "undefined") {
            cb = opts;
            opts = void 0;
          } else {
            cb = opts;
            opts = text;
            text = canvas;
            canvas = void 0;
          }
        }
      } else {
        if (argsNum < 1) {
          throw new Error("Too few arguments provided");
        }
        if (argsNum === 1) {
          text = canvas;
          canvas = opts = void 0;
        } else if (argsNum === 2 && !canvas.getContext) {
          opts = text;
          text = canvas;
          canvas = void 0;
        }
        return new Promise(function(resolve, reject) {
          try {
            const data = QRCode.create(text, opts);
            resolve(renderFunc(data, canvas, opts));
          } catch (e) {
            reject(e);
          }
        });
      }
      try {
        const data = QRCode.create(text, opts);
        cb(null, renderFunc(data, canvas, opts));
      } catch (e) {
        cb(e);
      }
    }
    exports.create = QRCode.create;
    exports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render);
    exports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL);
    exports.toString = renderCanvas.bind(null, function(data, _2, opts) {
      return SvgRenderer.render(data, opts);
    });
  }
});

// node_modules/@perkd/vue-components/dist/components/UISlideButton.js
var le = {
  key: 1,
  class: "button-title-container"
};
var ne = { class: "slide-thumb-content" };
var se = defineComponent({
  __name: "UISlideButton",
  props: {
    color: {
      type: String,
      default: "accent"
    },
    icon: {
      type: Object,
      required: false
    },
    title: String,
    titleClass: {
      type: String,
      default: ""
    },
    subtitle: String,
    subtitleClass: {
      type: String,
      default: ""
    },
    startColor: {
      type: String,
      default: ""
    },
    endColor: {
      type: String,
      default: ""
    },
    threshold: {
      type: Number,
      default: 0.6
    },
    disabled: {
      type: Boolean,
      default: false
    },
    proceeding: {
      type: Boolean,
      default: false
    }
  },
  emits: ["confirmClick", "qualifyClick"],
  setup(e, { emit: I2 }) {
    const N = e, { startColor: W2, endColor: A2, threshold: x, disabled: p, proceeding: F } = toRefs(N), m3 = ref(null), r2 = ref(null), f = ref(0), n = ref(0), M = ref(0), v3 = ref(false), h3 = ref(F.value), d = ref(null), S = ref(false), B2 = computed(() => n.value >= f.value * x.value), j2 = computed(() => {
      const l = document.documentElement, t2 = colorToRgbo(W2.value || getComputedStyle(l).getPropertyValue("--color-text-neutral").trim() || "#000000"), o = colorToRgbo(A2.value || getComputedStyle(l).getPropertyValue("--color-text-contrast").trim() || "#FFFFFF"), i3 = n.value / (f.value * x.value), s = Math.floor(t2.r + (o.r - t2.r) * i3), b = Math.floor(t2.g + (o.g - t2.g) * i3), G3 = Math.floor(t2.b + (o.b - t2.b) * i3), H = Math.floor(t2.o + (o.o - t2.o) * i3);
      return `rgb(${s}, ${b}, ${G3}, ${H})`;
    }), P = I2;
    onMounted(() => {
      m3.value && r2.value && (f.value = m3.value.offsetWidth - r2.value.offsetWidth - r2.value.offsetLeft * 2);
    }), watch(F, (l, t2) => {
      l === false && t2 === true && (X2(), h3.value = false);
    });
    function D2(l) {
      l.touches.length > 1 || p.value || (M.value = l.touches[0].clientX, v3.value = true);
    }
    function L2(l) {
      if (!m3.value || !v3.value || p.value) return;
      d.value !== null && (cancelAnimationFrame(d.value), d.value = null);
      const t2 = l.touches[0], o = m3.value.getBoundingClientRect();
      if (!U2(o, t2.clientX, t2.clientY)) {
        T();
        return;
      }
      const s = t2.clientX - M.value;
      n.value = s > 0 ? Math.min(s, f.value) : 0, B2.value || (S.value = false), B2.value && !S.value && (S.value = true, P("qualifyClick")), $2(n.value);
    }
    function O2() {
      !v3.value || !f.value || (d.value !== null && (cancelAnimationFrame(d.value), d.value = null), B2.value ? Q3() : T());
    }
    function T() {
      X2();
    }
    function Q3() {
      n.value = f.value, $2(n.value), h3.value = true, v3.value = false, P("confirmClick");
    }
    function X2() {
      n.value = 0, $2(n.value), v3.value = false;
    }
    function U2(l, t2, o) {
      const { left: i3, top: s, height: b } = l;
      return t2 >= i3 && o >= s - b / 2 && o <= s + b * 2;
    }
    function $2(l) {
      r2.value && (r2.value.style.transform = `translateX(${l}px)`);
    }
    return (l, t2) => (openBlock(), createElementBlock("div", {
      ref_key: "slideButtonRef",
      ref: m3,
      class: normalizeClass(["slide-button", { active: v3.value, proceeding: h3.value, disabled: unref(p) }])
    }, [
      createBaseVNode("div", {
        class: "progress",
        style: normalizeStyle({ width: n.value ? `calc(${n.value}px + var(--size-button) / 2)` : 0 })
      }, null, 4),
      createBaseVNode("div", {
        class: "slide-button-content",
        style: normalizeStyle({ color: j2.value })
      }, [
        e.icon && e.icon.position !== "right" ? (openBlock(), createBlock(i, {
          key: 0,
          name: e.icon.name,
          class: normalizeClass(`button-icon ${e.icon.class || ""}`)
        }, null, 8, ["name", "class"])) : createCommentVNode("", true),
        e.title || e.subtitle ? (openBlock(), createElementBlock("span", le, [
          e.title ? (openBlock(), createElementBlock("span", {
            key: 0,
            class: normalizeClass("button-title " + e.titleClass)
          }, toDisplayString(e.title), 3)) : createCommentVNode("", true),
          e.subtitle ? (openBlock(), createElementBlock("span", {
            key: 1,
            class: normalizeClass("button-subtitle " + e.subtitleClass)
          }, toDisplayString(e.subtitle), 3)) : createCommentVNode("", true)
        ])) : createCommentVNode("", true),
        e.icon && e.icon.position === "right" ? (openBlock(), createBlock(i, {
          key: 2,
          name: e.icon.name,
          class: normalizeClass("button-icon " + e.icon.class)
        }, null, 8, ["name", "class"])) : createCommentVNode("", true)
      ], 4),
      createBaseVNode("div", {
        ref_key: "slideThumbRef",
        ref: r2,
        class: "slide-thumb",
        onTouchstartPassive: D2,
        onTouchmovePassive: L2,
        onTouchend: withModifiers(O2, ["stop", "prevent"]),
        onTouchcancel: withModifiers(T, ["stop", "prevent"])
      }, [
        createBaseVNode("div", ne, [
          renderSlot(l.$slots, "status", {}, () => [
            h3.value ? createCommentVNode("", true) : (openBlock(), createBlock(i, {
              key: 0,
              name: "arrow-forward"
            })),
            h3.value ? (openBlock(), createBlock(h2, {
              key: 1,
              colorBackground: "",
              size: "1.5em",
              thickness: "xxs"
            })) : createCommentVNode("", true)
          ])
        ])
      ], 544)
    ], 2));
  }
});

// node_modules/@perkd/vue-components/dist/components/UIField.js
var J = { class: "field-wrapper" };
var L = {
  key: 0,
  class: "icon-container"
};
var Q2 = { class: "field-inner" };
var W = {
  key: 0,
  class: "icon-container"
};
var X = {
  key: 0,
  class: "field-error error"
};
var ee = defineComponent({
  __name: "UIField",
  props: {
    icon: {
      type: Object,
      required: false
    },
    label: {
      type: String,
      default: ""
    },
    labelClass: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    errorMessages: {
      type: Object
    },
    showError: {
      type: Number,
      default: 0
    }
  },
  emits: ["focusChange"],
  setup(t2, { expose: K2, emit: M }) {
    const z2 = t2, { t: j2 } = useI18n(), { label: d, errorMessages: o, showError: w2 } = toRefs(z2), f = ref(false), b = ref(""), h3 = ref(""), v3 = ref(true), r2 = ref(w2.value), P = computed(() => (f.value || b.value) && !!d.value), B2 = M;
    watch(b, () => {
      r2.value = 0;
    }), watch(w2, (e) => {
      r2.value = e;
    });
    function E2(e) {
      f.value = true, B2("focusChange", e, true);
    }
    function F(e) {
      f.value = false, B2("focusChange", e, false);
    }
    function I2(e) {
      b.value = e;
    }
    function O2(e, n) {
      v3.value = e, h3.value = n;
    }
    function T() {
      if (!r2.value || v3.value) return "";
      let e = "", n = {};
      const V = (s) => s.reduce((i3, g2) => {
        const [k3, ...m3] = g2.split(":");
        return i3[k3] = m3.join(":"), i3;
      }, {});
      if (h3.value) {
        const [s, ...i3] = h3.value.split("|");
        if (e = `error.${s}`, Object.keys(i3).length && Object.assign(n, V(i3)), o != null && o.value && o.value[s]) {
          const g2 = o.value[s] || "", [k3, ...m3] = g2.split("|");
          e = `error.${k3}`, Object.keys(m3).length && Object.assign(n, V(m3));
        }
      }
      return e ? j2(e, n) : j2("error.invalid");
    }
    return K2({
      onFocus: E2,
      onBlur: F,
      onInput: I2,
      inputCheck: O2
    }), (e, n) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["field-container", { disabled: t2.disabled }, { readonly: t2.readonly }, { active: P.value }, { focus: f.value }, { invalid: r2.value > 0 && !v3.value }])
    }, [
      createBaseVNode("div", J, [
        t2.icon && !unref(d) ? (openBlock(), createElementBlock("div", L, [
          createVNode(i, {
            name: t2.icon.name,
            class: normalizeClass(t2.icon.class)
          }, null, 8, ["name", "class"])
        ])) : createCommentVNode("", true),
        createBaseVNode("div", Q2, [
          renderSlot(e.$slots, "default", {
            required: t2.required,
            disabled: t2.disabled,
            readonly: t2.readonly,
            onInputFocus: E2,
            onInputBlur: F,
            onInputChange: I2,
            onInputCheck: O2
          }),
          unref(d) ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: normalizeClass(["label-container", t2.labelClass])
          }, [
            t2.icon ? (openBlock(), createElementBlock("div", W, [
              createVNode(i, {
                name: t2.icon.name,
                class: normalizeClass(t2.icon.class)
              }, null, 8, ["name", "class"])
            ])) : createCommentVNode("", true),
            createBaseVNode("label", null, [
              createTextVNode(toDisplayString(unref(d)), 1),
              t2.required ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                createTextVNode("*")
              ], 64)) : createCommentVNode("", true)
            ])
          ], 2)) : createCommentVNode("", true)
        ])
      ]),
      r2.value > 0 && !v3.value ? (openBlock(), createElementBlock("div", X, toDisplayString(T()), 1)) : createCommentVNode("", true)
    ], 2));
  }
});

// node_modules/@perkd/vue-components/dist/components/UIInputDate.js
var import_format_datetime = __toESM(require_dist(), 1);
var $ = ["value"];
var A = defineComponent({
  __name: "UIInputDate",
  props: {
    value: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      default: ""
    },
    min: {
      type: String,
      default: ""
    },
    max: {
      type: String,
      default: ""
    },
    defaultDate: {
      type: String,
      default: ""
    },
    timeStamp: {
      type: String,
      default: ""
    },
    smartFormat: {
      type: Boolean,
      default: false
    },
    openDateTimePicker: {
      type: Function,
      required: true
    }
  },
  emits: ["input", "blur", "focus", "showError"],
  setup(w2, { expose: D2, emit: T }) {
    const m3 = w2, { type: u, value: c2, min: r2, max: n, timeStamp: d, defaultDate: x, smartFormat: I2 } = toRefs(m3), t2 = ref(c2.value), v3 = computed(() => {
      if (!t2.value) return "";
      if (u.value === "time") return (0, import_format_datetime.formatDateTime)(t2.value).format("LT");
      if (I2.value) return (0, import_format_datetime.formatDateTime)(t2.value).smartDateTime(void 0, u.value === "datetime");
      const e = u.value === "date" ? "ll" : "LL";
      return (0, import_format_datetime.formatDateTime)(t2.value).format(e);
    }), p = ref(false), i3 = ref(void 0), S = ref(""), o = T;
    watch(c2, (e) => {
      t2.value = e;
    });
    function L2() {
      p.value = true, o("focus", { target: i3.value }), h3();
    }
    function f() {
      p.value = false, o("blur", { target: i3.value });
    }
    function h3() {
      m3.openDateTimePicker && m3.openDateTimePicker({
        value: t2.value || x.value || g2(void 0),
        mode: u.value,
        theme: window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"
      }).then((e) => {
        O2(e);
      }).catch((e) => {
        V(e);
      });
    }
    function O2(e) {
      typeof e == "string" && (t2.value = g2(e), o("input", { target: i3.value }, t2.value)), f();
    }
    function V(e) {
      o("showError", e), f();
    }
    function g2(e) {
      return d.value ? d.value === "start" ? (0, import_format_datetime.formatDateTime)(e).startOf("d").toISOString() : (0, import_format_datetime.formatDateTime)(e).endOf("d").toISOString() : e ? (0, import_format_datetime.formatDateTime)(e).toISOString() : (0, import_format_datetime.formatDateTime)().toISOString();
    }
    function b() {
      const e = (0, import_format_datetime.formatDateTime)(t2.value || ""), s = e.isValid(), y3 = r2.value ? e.isSame(r2.value) || e.isAfter(r2.value) : true, _2 = n.value ? e.isSame(n.value) || e.isBefore(n.value) : true, k3 = u.value === "date" ? "ll" : u.value === "datetime" ? "LL" : "LT";
      return S.value = s ? y3 ? _2 ? "" : `after_maximum_date|date:${(0, import_format_datetime.formatDateTime)(n.value).format(k3)}` : `before_minimum_date|date:${(0, import_format_datetime.formatDateTime)(r2.value).format(k3)}` : "invalid_date", s && y3 && _2;
    }
    return D2({
      value: t2,
      formattedValue: v3,
      focus: L2,
      blur: f,
      checkValidity: b,
      validationMessage: S
    }), (e, s) => (openBlock(), createElementBlock("div", {
      class: "input-wrapper--date",
      ref_key: "inputContainerRef",
      ref: i3,
      onClick: h3
    }, [
      createBaseVNode("input", {
        readonly: "",
        value: v3.value
      }, null, 8, $)
    ], 512));
  }
});

// node_modules/@perkd/vue-components/dist/components/UIInput.js
var he = defineComponent({
  __name: "UIInput",
  props: {
    modelValue: {
      type: String,
      default: ""
    },
    inputType: {
      type: String,
      default: "text"
    },
    inputClass: {
      type: String,
      default: ""
    },
    inputmode: {
      type: String,
      default: ""
    },
    value: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String,
      default: ""
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    autocapitalize: {
      type: String,
      default: "off"
    },
    autocomplete: {
      type: String,
      default: "off"
    },
    autocorrect: {
      type: String,
      default: "off"
    },
    spellcheck: {
      type: Boolean,
      default: false
    },
    pattern: {
      type: String,
      default: ""
    },
    minlength: {
      type: String,
      default: ""
    },
    maxlength: {
      type: String,
      default: ""
    },
    min: {
      type: String,
      default: ""
    },
    max: {
      type: String,
      default: ""
    },
    inputOptions: {
      type: Object,
      default: () => ({})
    },
    validate: {
      type: Function
    }
  },
  emits: ["update:modelValue", "inputFocus", "inputBlur", "inputChange", "inputCheck"],
  setup(v3, { expose: T, emit: q }) {
    const A2 = ["tel", "date", "datetime", "time", "money"], p = v3, { disabled: B2, required: x, readonly: P, clearable: U2, inputType: d, value: N, modelValue: $2, inputOptions: m3 } = toRefs(p), { pattern: S, minlength: f, maxlength: y3, min: g2, max: h3, inputmode: O2 } = toRefs(p), e = ref(($2.value || N.value || "").trim()), o = shallowRef(void 0), C3 = shallowRef(void 0), r2 = ref(false), j2 = defineAsyncComponent({
      loader: () => import("./UIInputMobile-2FCEBOTR.js"),
      errorComponent: h("input"),
      // Create a virtual node for the input element
      timeout: 3e3
      // Add timeout
    }), D2 = defineAsyncComponent({
      loader: () => import("./UIInputMoney-3OUHUWEA.js"),
      errorComponent: h("input"),
      timeout: 3e3
    }), V = computed(() => {
      switch (d.value) {
        case "tel":
          return j2;
        case "date":
        case "datetime":
        case "time":
          return A;
        case "money":
          return D2;
        case "textarea":
          return "textarea";
        default:
          return "input";
      }
    }), c2 = ref(""), i3 = ref(true), L2 = computed(() => {
      var l;
      return A2.indexOf(d.value) !== -1 && ((l = o.value) == null ? void 0 : l.formattedValue) || e.value;
    }), Y2 = computed(() => {
      var a2;
      return o.value && "countryCode" in o.value ? (a2 = o.value) == null ? void 0 : a2.countryCode : "";
    }), G3 = computed(() => !B2.value && U2.value && !!e.value), u = q;
    onMounted(() => {
      var a2, l;
      if (u("inputChange", e.value), u("update:modelValue", e.value), C3.value && ((a2 = m3 == null ? void 0 : m3.value) != null && a2.rows)) {
        const t2 = getComputedStyle(C3.value).fontSize;
        C3.value.style.height = t2 ? `calc(${t2} * 1.21 * ${(l = m3.value) == null ? void 0 : l.rows} + var(--height-input) * 0.33 + 0.7em)` : "var(--height-input)";
      }
    }), watch([$2, N], ([a2, l]) => {
      var t2;
      e.value = (t2 = a2 || l) == null ? void 0 : t2.trim(), i3.value = true, c2.value = "", u("inputChange", e.value), u("update:modelValue", e.value);
    });
    const H = debounce((a2) => {
      u("inputChange", a2), u("update:modelValue", a2);
    }, 100);
    function J2(a2, l) {
      var s;
      const t2 = a2.target;
      e.value = (s = l || t2.value) == null ? void 0 : s.trim(), H(e.value);
    }
    function E2() {
      e.value = "", I2(), u("inputChange", ""), u("update:modelValue", "");
    }
    function K2(a2) {
      r2.value || (r2.value = true, u("inputFocus", a2));
    }
    function Q3(a2) {
      r2.value && (r2.value = false, u("inputBlur", a2));
    }
    function I2() {
      var a2;
      r2.value || (r2.value = true, (a2 = o.value) == null || a2.focus());
    }
    function W2() {
      var a2;
      r2.value && (r2.value = false, (a2 = o.value) == null || a2.blur());
    }
    function X2() {
      const l = [
        { check: () => !x.value || !!e.value, message: "is_required" },
        { check: () => !e.value || !f.value || e.value.length >= Number(f.value), message: `minimum_length|n:${f.value}` },
        { check: () => !e.value || !y3.value || e.value.length <= Number(y3.value), message: `maximum_length|n:${y3.value}` },
        { check: () => e.value && S.value ? new RegExp(S.value).test(e.value) : true, message: "invalid_pattern" },
        { check: () => !(V.value !== "input") && !!e.value && !isNaN(+e.value) && g2.value ? Number(e.value) >= Number(g2.value) : true, message: `minimum_number|n:${g2.value}` },
        { check: () => !(V.value !== "input") && !!e.value && !isNaN(+e.value) && h3.value ? Number(e.value) <= Number(h3.value) : true, message: `maximum_number|n:${h3.value}` }
      ].find((t2) => !t2.check());
      if (l)
        i3.value = false, c2.value = l.message;
      else {
        const t2 = !x.value && !e.value;
        switch (d.value) {
          case "email":
            i3.value = t2 || isEmail(e.value), c2.value = i3.value ? "" : "invalid";
            break;
          case "url":
            i3.value = t2 || isUrl(e.value), c2.value = i3.value ? "" : "invalid";
            break;
          case "date":
          case "datetime":
          case "time":
          case "tel":
          case "money":
            o.value && (i3.value = t2 || o.value.checkValidity(), c2.value = i3.value ? "" : o.value.validationMessage || "");
            break;
        }
        if (p.validate) {
          const s = p.validate(e.value);
          i3.value = s.isValid, c2.value = s.isValid ? "" : s.validationMessage;
        }
      }
      return u("inputCheck", i3.value, c2.value), i3.value;
    }
    return T({
      value: e,
      countryCode: Y2,
      formattedValue: L2,
      focus: I2,
      blur: W2,
      checkValidity: X2,
      validationMessage: c2,
      clear: E2,
      setValue: (a2) => {
        e.value = a2, u("inputChange", a2), u("update:modelValue", a2);
      }
    }), (a2, l) => (openBlock(), createElementBlock("div", {
      ref_key: "inputContainerRef",
      ref: C3,
      class: normalizeClass(`input-container ${unref(d)}-container`)
    }, [
      (openBlock(), createBlock(resolveDynamicComponent(V.value), mergeProps({
        ref_key: "inputRef",
        ref: o,
        value: e.value,
        class: v3.inputClass,
        type: unref(d)
      }, { placeholder: v3.placeholder, disabled: unref(B2), readonly: unref(P), required: unref(x), autocapitalize: v3.autocapitalize, autocomplete: v3.autocomplete, autocorrect: v3.autocorrect, spellcheck: v3.spellcheck, pattern: unref(S), inputmode: unref(O2), minlength: unref(f), maxlength: unref(y3), min: unref(g2), max: unref(h3), ...unref(m3) }, {
        onFocus: K2,
        onBlur: Q3,
        onInput: J2
      }), null, 16, ["value", "class", "type"])),
      G3.value ? (openBlock(), createBlock(Q, {
        key: 0,
        icon: { name: "close" },
        type: "circle",
        onClick: withModifiers(E2, ["stop"]),
        class: "clear-button",
        "aria-label": "Clear input"
      })) : createCommentVNode("", true),
      renderSlot(a2.$slots, "customButton")
    ], 2));
  }
});

// node_modules/@perkd/vue-components/dist/components/UISwitch.js
var j = { class: "switch" };
var z = ["required", "disabled"];
var E = { class: "switch-slider" };
var O = defineComponent({
  __name: "UISwitch",
  props: {
    checkedIcon: {
      type: Object
    },
    unCheckedIcon: {
      type: Object
    },
    checked: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue", "inputChange", "inputCheck"],
  setup(c2, { expose: u, emit: r2 }) {
    const h3 = c2, { checked: m3, required: l } = toRefs(h3), e = ref(m3.value), p = ref(void 0), n = ref(""), o = r2;
    watch(e, (t2) => {
      o("inputChange", t2), o("update:modelValue", t2);
    });
    function f() {
      const t2 = l.value ? !!e.value : true;
      return n.value = t2 ? "" : "is_required", o("inputCheck", t2, n.value), t2;
    }
    return u({
      value: e,
      checkValidity: f,
      validationMessage: n
    }), (t2, d) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["switch-container", { checked: e.value }])
    }, [
      createBaseVNode("div", j, [
        withDirectives(createBaseVNode("input", {
          class: "switch-input",
          ref_key: "inputRef",
          ref: p,
          "onUpdate:modelValue": d[0] || (d[0] = (k3) => e.value = k3),
          type: "checkbox",
          required: unref(l),
          disabled: c2.disabled
        }, null, 8, z), [
          [vModelCheckbox, e.value]
        ]),
        createBaseVNode("div", E, [
          c2.checkedIcon || c2.unCheckedIcon ? (openBlock(), createBlock(i, normalizeProps(mergeProps({ key: 0 }, e.value ? c2.checkedIcon : c2.unCheckedIcon)), null, 16)) : createCommentVNode("", true)
        ])
      ])
    ], 2));
  }
});

// node_modules/@perkd/vue-components/dist/components/UIDialog.js
var g = { class: "dialog-container" };
var k = {
  key: 0,
  class: "dialog-title"
};
var y = {
  key: 1,
  class: "dialog-desc"
};
var v = {
  key: 2,
  class: "dialog-content"
};
var _ = { class: "actions-container" };
var D = defineComponent({
  __name: "UIDialog",
  props: {
    title: {
      type: String,
      default: ""
    },
    description: {
      type: String,
      default: ""
    }
  },
  setup(t2) {
    const { t: r2 } = useI18n();
    return (e, s) => (openBlock(), createElementBlock("div", g, [
      t2.title ? (openBlock(), createElementBlock("div", k, toDisplayString(t2.title), 1)) : createCommentVNode("", true),
      t2.description ? (openBlock(), createElementBlock("div", y, toDisplayString(t2.description), 1)) : createCommentVNode("", true),
      e.$slots.content ? (openBlock(), createElementBlock("div", v, [
        renderSlot(e.$slots, "content")
      ])) : createCommentVNode("", true),
      createBaseVNode("div", _, [
        renderSlot(e.$slots, "buttons", {}, () => [
          createVNode(Q, {
            type: "clear",
            title: unref(r2)("button.ok"),
            onClick: s[0] || (s[0] = ($2) => e.$emit("closeDialog"))
          }, null, 8, ["title"])
        ])
      ])
    ]));
  }
});

// node_modules/@perkd/vue-components/dist/components/UISelection.js
var C = { class: "selection-wrapper" };
var w = {
  key: 0,
  class: "selection-content"
};
var y2 = defineComponent({
  __name: "UISelection",
  emits: ["click"],
  setup(E2, { emit: u }) {
    const f = u, s = ref(void 0), t2 = ref(false), o = ref([]);
    function p(e) {
      var r2;
      if (t2.value) return;
      t2.value = true, (r2 = s.value) == null || r2.createRipple(e);
      const c2 = setTimeout(() => {
        f("click", e), t2.value = false;
      }, 200);
      o.value.push(c2);
    }
    return onUnmounted(() => {
      o.value.forEach((e) => clearTimeout(e)), o.value = [];
    }), (e, c2) => (openBlock(), createElementBlock("div", {
      class: "selection-container",
      onClick: p
    }, [
      createBaseVNode("div", C, [
        renderSlot(e.$slots, "icon"),
        e.$slots.content ? (openBlock(), createElementBlock("span", w, [
          renderSlot(e.$slots, "content")
        ])) : createCommentVNode("", true),
        createVNode(i, { name: "arrow-forward" })
      ]),
      createVNode(B, {
        ref_key: "rippleRef",
        ref: s
      }, null, 512)
    ]));
  }
});

// node_modules/@perkd/vue-components/dist/components/UICard.js
var import_jsbarcode = __toESM(require_JsBarcode(), 1);
var import_qrcode = __toESM(require_browser(), 1);
var import_format_datetime2 = __toESM(require_dist(), 1);
var AA = "data:image/png;base64,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";
var eA = ["src"];
var tA = ["src"];
var cA = ["id"];
var aA = ["innerHTML"];
var gA = {
  key: 0,
  class: "card-date-join"
};
var uA = { class: "label" };
var dA = { class: "date" };
var rA = { class: "label" };
var oA = { class: "date" };
var bA = defineComponent({
  __name: "UICard",
  props: {
    image: {
      type: String,
      require: true
    },
    placeholder: {
      type: String,
      require: true,
      default: AA
    },
    width: {
      type: Number,
      default: parseFloat(getComputedStyle(document.documentElement).getPropertyValue("--width-card")) || 28.8
    },
    cardNumber: {
      type: String,
      default: ""
    },
    barcodeType: {
      type: String,
      default: ""
    },
    barcodePatterns: {
      type: String,
      default: ""
    },
    startTime: {
      type: String,
      default: ""
    },
    endTime: {
      type: String,
      default: ""
    }
  },
  setup(e, { expose: h3 }) {
    const { t: M } = useI18n(), g2 = e, { width: N, barcodeType: O2, cardNumber: a2 } = toRefs(g2), w2 = parseInt(getComputedStyle(document.documentElement).getPropertyValue("--font-size-base")) || 10, E2 = document.documentElement.getAttribute("lang"), C3 = ref(false), n = ref(N.value * w2), A2 = ref(N.value * w2 / 1.6), s = ref(0), p = ref(""), T = computed(() => `${O2.value || "card"}-${a2.value.replace(/[^a-zA-Z0-9]/g, "")}`), D2 = computed(() => Cards.CODE_SQUARE.includes(O2.value)), H = computed(() => a2.value && (isUrl(a2.value) || a2.value.length > 30)), Y2 = computed(() => !(s.value / 180 % 2)), f = computed(() => ({
      width: `${n.value}px`,
      height: `${A2.value}px`,
      borderRadius: `${A2.value / 10}px`,
      transform: `rotateY(${s.value}deg)`
    })), R = computed(() => {
      const r2 = E2 && E2.startsWith("zh"), c2 = E2 && ["ja", "ko"].includes(E2), u = r2 ? 0.08 : c2 ? 0.07 : 0.1;
      return {
        fontSize: `${A2.value * u}px`,
        padding: `${A2.value * 0.045}px ${A2.value * 0.08}px`
      };
    }), S = computed(() => g2.barcodeType ? D2.value ? {
      height: A2.value * (H.value ? 0.75 : 0.6) + "px",
      marginTop: A2.value * 0.02 + "px"
    } : {
      height: A2.value * 0.42 + "px",
      marginTop: A2.value * 0.08 + "px"
    } : {
      height: A2.value * 0.24 + "px",
      marginTop: A2.value * 0.15 + "px",
      marginBottom: A2.value * 0.075 + "px",
      backgroundColor: "var(--color-background-maximal)"
    }), y3 = computed(() => ({
      fontSize: A2.value * (D2.value ? 0.12 : 0.17) + "px",
      lineHeight: D2.value ? 1.3 : 1.6,
      padding: `0 ${A2.value * 0.08}px`,
      marginTop: D2.value && !H.value ? -A2.value * 0.02 + "px" : 0
    }));
    function X2(r2) {
      const c2 = r2.target, u = c2.width / c2.height, Q3 = n.value / A2.value;
      u > Q3 ? (n.value = Math.min(c2.width, n.value), A2.value = n.value / u) : (A2.value = Math.min(c2.height, A2.value), n.value = A2.value * u), C3.value = true;
    }
    function z2(r2 = void 0, c2 = false) {
      const { cardNumber: u, startTime: Q3, endTime: J2 } = g2, x = r2 ? r2.offsetX < n.value / 2 : c2;
      (u || Q3 || J2) && (s.value = x ? s.value - 180 : s.value + 180);
    }
    onMounted(() => {
      j2();
    }), watch([a2, O2], () => {
      nextTick(() => {
        j2();
      });
    });
    async function j2() {
      g2.cardNumber && g2.barcodeType && (g2.barcodeType !== Cards.Barcode.QRCODE ? (0, import_jsbarcode.default)(`#${T.value}`, g2.cardNumber, {
        format: g2.barcodeType,
        width: 4,
        height: 100,
        displayValue: false
      }) : p.value = await import_qrcode.default.toString(g2.cardNumber, { type: "svg" }));
    }
    return h3({
      isFront: Y2,
      flipCard: z2
    }), (r2, c2) => (openBlock(), createElementBlock("div", {
      theme: "light",
      class: "card-container",
      style: normalizeStyle({ width: `${unref(N) * unref(w2)}px` })
    }, [
      createBaseVNode("div", {
        class: "card",
        style: normalizeStyle(f.value),
        onClick: c2[0] || (c2[0] = (u) => r2.$attrs.onClick || z2(u))
      }, [
        createBaseVNode("div", {
          class: "card-front",
          style: normalizeStyle({ borderRadius: `${A2.value / 10}px` })
        }, [
          withDirectives(createBaseVNode("img", {
            src: e.image,
            onLoad: X2
          }, null, 40, eA), [
            [vShow, C3.value]
          ]),
          withDirectives(createBaseVNode("img", { src: e.placeholder }, null, 8, tA), [
            [vShow, !C3.value]
          ])
        ], 4),
        unref(a2) || e.startTime || e.endTime ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: "card-back",
          style: normalizeStyle({ borderRadius: `${A2.value / 10}px` })
        }, [
          createBaseVNode("div", {
            class: "card-code",
            style: normalizeStyle(S.value)
          }, [
            unref(a2) && !D2.value ? (openBlock(), createElementBlock("svg", {
              key: 0,
              id: T.value,
              class: "barcode"
            }, null, 8, cA)) : createCommentVNode("", true),
            unref(a2) && D2.value ? (openBlock(), createElementBlock("div", {
              key: 1,
              class: "qrcode",
              innerHTML: p.value
            }, null, 8, aA)) : createCommentVNode("", true)
          ], 4),
          unref(a2) && !H.value ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: "card-number",
            style: normalizeStyle(y3.value)
          }, toDisplayString(unref(a2)), 5)) : createCommentVNode("", true),
          e.startTime || e.endTime ? (openBlock(), createElementBlock("div", {
            key: 1,
            class: "card-dates",
            style: normalizeStyle(R.value)
          }, [
            e.startTime ? (openBlock(), createElementBlock("div", gA, [
              createBaseVNode("span", uA, toDisplayString(unref(M)("form.join")), 1),
              createBaseVNode("span", dA, toDisplayString(unref(import_format_datetime2.formatDateTime)(e.startTime).format("ll")), 1)
            ])) : createCommentVNode("", true),
            e.endTime ? (openBlock(), createElementBlock("div", {
              key: 1,
              class: normalizeClass(["card-date-expire", { expired: unref(import_format_datetime2.formatDateTime)(e.endTime).isBefore() }])
            }, [
              createBaseVNode("span", rA, toDisplayString(unref(M)("form.expire")), 1),
              createBaseVNode("span", oA, toDisplayString(unref(import_format_datetime2.formatDateTime)(e.endTime).format("ll")), 1)
            ], 2)) : createCommentVNode("", true)
          ], 4)) : createCommentVNode("", true)
        ], 4)) : createCommentVNode("", true)
      ], 4)
    ], 4));
  }
});

// node_modules/@perkd/vue-components/dist/components/UICardOverlay.js
var m = 300;
var G = defineComponent({
  __name: "UICardOverlay",
  props: {
    cardInfo: {
      type: Object
    },
    startRect: {
      type: DOMRect,
      default: () => ({
        top: 0,
        left: 0
      })
    },
    startWidth: {
      type: Number,
      default: 9
    }
  },
  emits: ["closeCardOverlay"],
  setup(r2, { emit: I2 }) {
    const x = r2, { startRect: v3, startWidth: p } = toRefs(x), O2 = parseFloat(getComputedStyle(document.documentElement).getPropertyValue("--width-card")) || 28.8, S = ref(void 0), a2 = ref(void 0), y3 = ref(void 0), c2 = ref(void 0), s = ref(false), d = ref(false), w2 = I2;
    onMounted(() => {
      var e;
      c2.value = (e = y3.value) == null ? void 0 : e.getBoundingClientRect(), setTimeout(() => {
        s.value = true, setTimeout(() => {
          d.value = true, a2.value && a2.value.isFront && a2.value.flipCard();
        }, m);
      }, 0);
    });
    const i3 = computed(() => {
      var e, t2, l, o;
      return {
        top: ((e = v3.value) == null ? void 0 : e.top) - (((t2 = c2.value) == null ? void 0 : t2.top) || 0),
        left: ((l = v3.value) == null ? void 0 : l.left) - (((o = c2.value) == null ? void 0 : o.left) || 0)
      };
    }), N = computed(() => {
      var e, t2;
      return {
        transition: `transform ${m}ms ease-in-out`,
        top: `${(e = i3.value) == null ? void 0 : e.top}px`,
        left: `${(t2 = i3.value) == null ? void 0 : t2.left}px`
      };
    }), P = computed(() => {
      var e, t2, l, o;
      return `translate(calc(${(((e = c2.value) == null ? void 0 : e.width) || 0) / 2}px - 50% - ${(t2 = i3.value) == null ? void 0 : t2.left}px), calc(${(((l = c2.value) == null ? void 0 : l.height) || 0) / 2}px - 50% - ${(o = i3.value) == null ? void 0 : o.top}px)) scale(${O2 / p.value})`;
    });
    function B2() {
      a2.value && !a2.value.isFront ? (a2.value.flipCard(), setTimeout(() => {
        C3();
      }, 800)) : C3();
    }
    function C3() {
      d.value = false, s.value = false, setTimeout(() => {
        w2("closeCardOverlay");
      }, m);
    }
    function E2() {
      a2.value && a2.value.flipCard();
    }
    return (e, t2) => {
      var l, o, T, h3, b, R, g2;
      return openBlock(), createElementBlock("div", {
        ref_key: "screenRef",
        ref: y3,
        class: normalizeClass(["screen", "card-overlay-screen", { overlay: d.value }]),
        onClick: B2
      }, [
        createVNode(bA, {
          ref_key: "duppedCardRef",
          ref: S,
          class: normalizeClass(["duppedCard", { hide: d.value }]),
          width: unref(p),
          image: (l = r2.cardInfo) == null ? void 0 : l.image,
          style: normalizeStyle({ ...N.value, transform: s.value ? P.value : "" })
        }, null, 8, ["class", "width", "image", "style"]),
        createVNode(bA, {
          ref_key: "overlayCardRef",
          ref: a2,
          class: normalizeClass(["overlayCard", { hide: !d.value }]),
          image: (o = r2.cardInfo) == null ? void 0 : o.image,
          cardNumber: (T = r2.cardInfo) == null ? void 0 : T.cardNumber,
          barcodeType: (h3 = r2.cardInfo) == null ? void 0 : h3.barcodeType,
          barcodePatterns: (b = r2.cardInfo) == null ? void 0 : b.barcodePatterns,
          startTime: (R = r2.cardInfo) == null ? void 0 : R.startTime,
          endTime: (g2 = r2.cardInfo) == null ? void 0 : g2.endTime,
          onClick: withModifiers(E2, ["stop"])
        }, null, 8, ["class", "image", "cardNumber", "barcodeType", "barcodePatterns", "startTime", "endTime"])
      ], 2);
    };
  }
});

// node_modules/@perkd/vue-components/dist/components/UICircleProgress.js
var Y = ["dx", "dy"];
var Z = ["stdDeviation"];
var tt = ["flood-color", "flood-opacity"];
var et = ["dx", "dy"];
var ot = ["stdDeviation"];
var rt = ["floodColor", "floodOpacity"];
var nt = {
  key: 0,
  class: "current-counter"
};
var it = {
  key: 1,
  class: "text-container"
};
var lt = defineComponent({
  __name: "UICircleProgress",
  props: {
    size: {
      type: Number,
      default: 180
    },
    borderWidth: {
      type: Number,
      default: 10
    },
    borderBgWidth: {
      type: Number,
      default: 10
    },
    fillColor: {
      type: String,
      default: "accent"
    },
    emptyColor: {
      type: String,
      default: "var(--color-background-strong)"
    },
    background: {
      type: String,
      default: "none"
    },
    percent: {
      type: Number,
      default: 0
    },
    linecap: {
      type: String,
      default: "round"
    },
    transition: {
      type: Number,
      // speed, animation time
      default: 400
    },
    isGradient: {
      type: Boolean,
      default: false
    },
    gradient: {
      type: Object
    },
    isShadow: {
      type: Boolean,
      default: false
    },
    shadow: {
      type: Object
    },
    isBgShadow: {
      type: Boolean,
      default: false
    },
    bgShadow: {
      type: Object
    },
    viewport: {
      type: Boolean,
      default: false
    },
    onViewport: {
      type: Function
    },
    showPercent: {
      type: Boolean,
      default: false
    },
    unit: {
      type: String,
      default: ""
    }
  },
  setup(s, { expose: b }) {
    const t2 = s, v3 = B2("grd_"), w2 = B2("shd1_"), W2 = B2("shd2_"), S = ref(null), c2 = ref(0), P = (o) => ["primary", "accent", "success", "warning", "error"].indexOf(o) !== -1 ? `var(--color-background-${o})` : o, C3 = { angle: 0, startColor: "#ff0000", stopColor: "#ffff00", ...t2.gradient }, u = { inset: false, vertical: 10, horizontal: 0, blur: 10, opacity: 0.5, color: "#000000", ...t2.shadow }, a2 = { inset: true, vertical: 3, horizontal: 0, blur: 3, opacity: 0.4, color: "#000000", ...t2.bgShadow }, I2 = () => {
      let o = (t2.size - t2.borderBgWidth) * 0.5;
      return t2.borderWidth > t2.borderBgWidth && (o -= (t2.borderWidth - t2.borderBgWidth) * 0.5), o;
    }, m3 = () => {
      let o = (t2.size - t2.borderWidth) * 0.5;
      return t2.borderBgWidth > t2.borderWidth && (o -= (t2.borderBgWidth - t2.borderWidth) * 0.5), o;
    }, M = 2 * Math.PI * m3(), k3 = ref(2 * Math.PI * m3()), V = {
      style: {
        transform: "rotate(-90deg)",
        overflow: "visible"
      },
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: `${t2.size / 2} ${t2.size / 2} ${t2.size} ${t2.size}`
    }, _2 = {
      cx: t2.size,
      cy: t2.size,
      r: I2(),
      stroke: P(t2.emptyColor),
      "stroke-width": t2.borderBgWidth,
      fill: t2.background,
      ...t2.isBgShadow && { filter: `url(#${W2})` }
    }, E2 = computed(() => ({
      cx: t2.size,
      cy: t2.size,
      r: m3(),
      fill: "none",
      "stroke-width": t2.borderWidth,
      "stroke-dasharray": M,
      "stroke-dashoffset": k3.value,
      "stroke-linecap": t2.linecap,
      stroke: t2.isGradient ? `url(#${v3})` : P(t2.fillColor),
      ...t2.isShadow && { filter: `url(#${w2})` },
      ...t2.transition && {
        style: { transition: `stroke-dashoffset ${t2.transition}ms` }
      }
    })), N = {
      id: v3,
      x1: "0%",
      y1: "0%",
      x2: "0%",
      y2: "100%",
      gradientTransform: `rotate(${C3.angle}, .5, .5)`
    }, F = {
      offset: 0,
      "stop-color": C3.startColor
    }, R = {
      offset: 100,
      "stop-color": C3.stopColor
    }, O2 = {
      id: w2,
      width: "500%",
      height: "500%",
      x: "-250%",
      y: "-250%"
    }, $2 = {
      id: W2,
      width: "500%",
      height: "500%",
      x: "-250%",
      y: "-250%"
    }, f = {
      dx: u.vertical * -1,
      dy: u.horizontal,
      stdDeviation: u.blur,
      floodColor: u.color,
      floodOpacity: u.opacity
    }, p = {
      dx: a2.vertical * -1,
      dy: a2.horizontal,
      stdDeviation: a2.blur,
      floodColor: a2.color,
      floodOpacity: a2.opacity
    };
    onMounted(() => {
      h3();
    }), onBeforeUnmount(() => {
      window.removeEventListener("scroll", h3);
    }), watch(
      () => t2.percent,
      () => {
        h3();
      }
    );
    function j2(o) {
      if (o === null) return;
      const e = o.getBoundingClientRect();
      return e.top >= 0 && e.left >= 0 && e.bottom <= (window.innerHeight || document.documentElement.clientHeight) && e.right <= (window.innerWidth || document.documentElement.clientWidth);
    }
    function x() {
      const o = 2 * Math.PI * m3();
      k3.value = o - o * t2.percent / 100;
      const e = Math.round(100 - 100 / o * k3.value);
      L2(e);
    }
    function L2(o) {
      const e = o - c2.value;
      if (e) {
        const U2 = t2.transition / Math.abs(e), D2 = setInterval(() => {
          e > 0 ? (c2.value += 1, c2.value >= o && clearInterval(D2)) : (c2.value -= 1, c2.value <= o && clearInterval(D2));
        }, U2);
      }
    }
    function h3() {
      t2.viewport ? (window.addEventListener("scroll", h3), S.value && j2(S.value) && (window.removeEventListener("scroll", h3), t2.viewport && x(), t2.onViewport && typeof t2.onViewport == "function" && t2.onViewport())) : x();
    }
    function B2(o = "", e = "") {
      return o + Math.random().toString(36).substring(2, 8) + Math.random().toString(36).substring(2, 8) + e;
    }
    return b({
      updatePercent: x
    }), (o, e) => (openBlock(), createElementBlock("div", {
      ref_key: "circleProgressRef",
      ref: S,
      style: normalizeStyle({ height: s.size + "px", width: t2.size + "px" }),
      class: "circular-progress"
    }, [
      (openBlock(), createElementBlock("svg", normalizeProps(guardReactiveProps(V)), [
        s.isGradient ? (openBlock(), createElementBlock("linearGradient", normalizeProps(mergeProps({ key: 0 }, N)), [
          createBaseVNode("stop", normalizeProps(guardReactiveProps(F)), null, 16),
          createBaseVNode("stop", normalizeProps(guardReactiveProps(R)), null, 16)
        ], 16)) : createCommentVNode("", true),
        createBaseVNode("circle", mergeProps(_2, { class: "circle-background" }), null, 16),
        createBaseVNode("circle", mergeProps(E2.value, { class: "circle-forground" }), null, 16),
        s.isShadow ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
          u.inset === false ? (openBlock(), createElementBlock("filter", normalizeProps(mergeProps({ key: 0 }, O2)), [
            createBaseVNode("feDropShadow", normalizeProps(guardReactiveProps(f)), null, 16)
          ], 16)) : (openBlock(), createElementBlock("filter", normalizeProps(mergeProps({ key: 1 }, O2)), [
            createBaseVNode("feOffset", {
              dx: f.dx,
              dy: f.dy
            }, null, 8, Y),
            createBaseVNode("feGaussianBlur", {
              stdDeviation: f.stdDeviation
            }, null, 8, Z),
            e[0] || (e[0] = createBaseVNode("feComposite", {
              operator: "out",
              in: "SourceGraphic",
              result: "inverse"
            }, null, -1)),
            createBaseVNode("feFlood", {
              "flood-color": f.floodColor,
              "flood-opacity": f.floodOpacity,
              result: "color"
            }, null, 8, tt),
            e[1] || (e[1] = createBaseVNode("feComposite", {
              operator: "in",
              in: "color",
              in2: "inverse",
              result: "shadow"
            }, null, -1)),
            e[2] || (e[2] = createBaseVNode("feComposite", {
              operator: "over",
              in: "shadow",
              in2: "SourceGraphic"
            }, null, -1))
          ], 16))
        ], 64)) : createCommentVNode("", true),
        a2 ? (openBlock(), createElementBlock(Fragment, { key: 2 }, [
          a2.inset === false ? (openBlock(), createElementBlock("filter", normalizeProps(mergeProps({ key: 0 }, $2)), [
            createBaseVNode("feDropShadow", normalizeProps(guardReactiveProps(p)), null, 16)
          ], 16)) : (openBlock(), createElementBlock("filter", normalizeProps(mergeProps({ key: 1 }, $2)), [
            createBaseVNode("feOffset", {
              dx: p.dx,
              dy: p.dy
            }, null, 8, et),
            createBaseVNode("feGaussianBlur", {
              stdDeviation: p.stdDeviation
            }, null, 8, ot),
            e[3] || (e[3] = createBaseVNode("feComposite", {
              operator: "out",
              in: "SourceGraphic",
              result: "inverse"
            }, null, -1)),
            createBaseVNode("feFlood", {
              floodColor: p.floodColor,
              floodOpacity: p.floodOpacity,
              result: "color"
            }, null, 8, rt),
            e[4] || (e[4] = createBaseVNode("feComposite", {
              operator: "in",
              in: "color",
              in2: "inverse",
              result: "shadow"
            }, null, -1)),
            e[5] || (e[5] = createBaseVNode("feComposite", {
              operator: "over",
              in: "shadow",
              in2: "SourceGraphic"
            }, null, -1))
          ], 16))
        ], 64)) : createCommentVNode("", true)
      ], 16)),
      s.showPercent ? (openBlock(), createElementBlock("span", nt, toDisplayString(c2.value) + " " + toDisplayString(s.unit), 1)) : createCommentVNode("", true),
      o.$slots.textContent ? (openBlock(), createElementBlock("div", it, [
        renderSlot(o.$slots, "textContent", {}, void 0, true)
      ])) : createCommentVNode("", true)
    ], 4));
  }
});
var st = (s, b) => {
  const t2 = s.__vccOpts || s;
  for (const [v3, w2] of b)
    t2[v3] = w2;
  return t2;
};
var dt = st(lt, [["__scopeId", "data-v-7da27c7b"]]);

// node_modules/@perkd/vue-components/dist/components/UIModalBox.js
var G2 = defineComponent({
  __name: "UIModalBox",
  props: {
    showClose: {
      type: Boolean,
      default: true
    },
    overlayAnimation: {
      type: String
    },
    modalAnimation: {
      type: String
    },
    showHandle: {
      type: Boolean,
      default: false
    },
    touchable: {
      type: Boolean,
      default: true
    },
    threshold: {
      type: Number,
      default: 0.35
    }
  },
  emits: ["closeModal"],
  setup(C3, { emit: M }) {
    const w2 = C3, { showClose: T, overlayAnimation: B2, modalAnimation: E2, showHandle: R, threshold: g2, touchable: d } = toRefs(w2), l = ref(null), t2 = ref(null), h3 = ref(null), s = ref(0), u = ref(0), c2 = ref(false), v3 = ref(false), S = M;
    onMounted(() => {
      c2.value = true;
    }), onUnmounted(() => {
      c2.value = false;
    });
    function Y2(e) {
      if (!t2.value || !d.value) return;
      const { scrollTop: n } = t2.value, H = e.target === h3.value ? 0 : n;
      e.touches.length === 1 && (s.value = e.touches[0].clientY + H, u.value = s.value);
    }
    function b(e) {
      if (e.touches.length === 1 && d.value) {
        u.value = e.touches[0].clientY;
        const n = u.value - s.value;
        l.value && n > 0 && (l.value.style.transform = `translateY(${n}px)`);
      }
    }
    function x() {
      if (!t2.value || !d.value) return;
      const e = t2.value.clientHeight * g2.value;
      u.value - s.value > e ? r2() : l.value && (l.value.style.transform = "");
    }
    function r2() {
      v3.value = false;
    }
    function A2() {
      v3.value = true;
    }
    function L2() {
    }
    function N() {
      c2.value = false;
    }
    function $2() {
      S("closeModal");
    }
    return (e, n) => (openBlock(), createBlock(Transition, {
      name: unref(B2) || "fade",
      onEnter: A2,
      onLeave: $2
    }, {
      default: withCtx(() => [
        c2.value ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: "screen overlay modal-screen",
          onClick: withModifiers(r2, ["self"])
        }, [
          createVNode(Transition, {
            name: unref(E2) || "slide-up",
            onEnter: L2,
            onLeave: N
          }, {
            default: withCtx(() => [
              v3.value ? (openBlock(), createElementBlock("div", {
                key: 0,
                ref_key: "modalContainerRef",
                ref: l,
                class: "modal-container",
                onTouchstartPassive: Y2,
                onTouchmovePassive: b,
                onTouchend: withModifiers(x, ["stop"])
              }, [
                unref(R) ? (openBlock(), createElementBlock("div", {
                  key: 0,
                  ref_key: "handleRef",
                  ref: h3,
                  class: "handle"
                }, null, 512)) : createCommentVNode("", true),
                unref(T) ? (openBlock(), createBlock(Q, {
                  key: 1,
                  class: "close-button",
                  type: "circle",
                  icon: { name: "close" },
                  onClick: r2
                })) : createCommentVNode("", true),
                createBaseVNode("div", {
                  class: "modal",
                  ref_key: "modalRef",
                  ref: t2
                }, [
                  renderSlot(e.$slots, "default", { onCloseModal: r2 })
                ], 512)
              ], 544)) : createCommentVNode("", true)
            ]),
            _: 3
          }, 8, ["name"])
        ])) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["name"]));
  }
});

// node_modules/@perkd/vue-components/dist/components/CountDown.js
var import_format_datetime3 = __toESM(require_dist(), 1);
var te = {
  key: 0,
  class: "js-countdown-offline"
};
var ne2 = { class: "js-countdown-before" };
var oe = { class: "prefix" };
var se2 = { class: "end-date" };
var ae = { class: "suffix" };
var ue = {
  key: 2,
  class: "js-countdown-ing"
};
var le2 = { class: "prefix" };
var ie = { class: "js-countdown-timer" };
var ce = {
  key: 0,
  class: "time-block time-block-days"
};
var re = { class: "time-value" };
var de = { class: "time-unit" };
var me = {
  key: 1,
  class: "time-block time-block-hours"
};
var fe = { class: "time-value" };
var ve2 = { class: "time-unit" };
var pe = {
  key: 2,
  class: "time-block time-block-mins"
};
var _e = { class: "time-value" };
var he2 = { class: "time-unit" };
var we = {
  key: 3,
  class: "time-block time-block-secs"
};
var ge = { class: "time-value secs" };
var be = { class: "time-unit" };
var Ce = { class: "suffix" };
var ke = { class: "js-countdown-after" };
var Te = defineComponent({
  __name: "CountDown",
  props: {
    serverTimeRequired: {
      type: Boolean,
      default: true
    },
    serverTime: {
      type: [Number, String],
      validator: function(m3) {
        return new Date(m3).toString() !== "Invalid Date";
      }
    },
    endCountTime: {
      required: true,
      type: [Number, String],
      validator: function(m3) {
        return new Date(m3).toString() !== "Invalid Date";
      }
    },
    countDistance: {
      type: Number,
      default: 2 * 24 * 60 * 60 * 1e3
      // '2 days'
    },
    timerFormat: {
      type: String,
      default: "auto",
      validator: function(m3) {
        return !![
          "auto",
          "dd-hh-mm-ss",
          "hh-mm-ss",
          "mm-ss",
          "auto-mm-ss",
          "auto-hh-mm-ss"
        ].find((x) => x === m3);
      }
    },
    countUpAfterCountDown: {
      type: Boolean,
      default: false
    }
  },
  emits: ["endCountDown"],
  setup(m3, { expose: w2, emit: x }) {
    const { t: u } = useI18n(), L2 = m3, { serverTimeRequired: O2, serverTime: a2, endCountTime: g2, countDistance: V, timerFormat: M, countUpAfterCountDown: b } = toRefs(L2), c2 = ref(), p = ref(false), l = ref(new Date(g2.value).getTime() - (a2 != null && a2.value ? new Date(a2 == null ? void 0 : a2.value).getTime() : (/* @__PURE__ */ new Date()).getTime())), I2 = ref(0), q = computed(() => l.value > V.value), F = computed(() => l.value <= V.value && l.value >= 1e3 || b.value), S = computed(() => l.value < 1e3), A2 = x;
    onMounted(() => {
      S.value ? (A2("endCountDown"), b.value && j2(true)) : c2.value = setInterval(N, 1e3);
    }), onBeforeUnmount(() => {
      c2.value && clearInterval(c2.value);
    }), watch(g2, (o) => {
      o && (l.value = new Date(g2.value).getTime() - (a2 != null && a2.value ? new Date(a2 == null ? void 0 : a2.value).getTime() : (/* @__PURE__ */ new Date()).getTime()), l.value > 0 && p.value && j2(false));
    });
    const r2 = computed(() => {
      const o = p.value ? I2.value : l.value;
      if (o < 0) return {};
      const i3 = {
        dd: Math.floor(o / (1e3 * 60 * 60 * 24)),
        hh: Math.floor(o % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60)),
        mm: Math.floor(o % (1e3 * 60 * 60) / (1e3 * 60)),
        ss: Math.floor(o % (1e3 * 60) / 1e3)
      }, T = (s) => s <= 9 ? `0${s}` : `${s}`, B2 = Object.keys(i3).findIndex((s) => i3[s] !== 0), U2 = B2 === -1 ? Object.keys(i3).length - 1 : B2, H = M.value.startsWith("auto"), C3 = 4 - M.value.split("-").filter((s) => s !== "auto").length, K2 = [24, 60, 60, 60], z2 = Array(C3).fill(0).reduce((s, k3, _2) => {
        const h3 = Object.keys(i3)[_2];
        return s += K2[_2] * i3[h3], s;
      }, 0);
      return Object.keys(i3).reduce((s, k3, _2) => {
        const h3 = i3[k3];
        if (H && U2 < C3) {
          const W2 = h3 === 0 && _2 < U2;
          s[k3] = W2 ? "" : T(h3);
        } else
          s[k3] = _2 < C3 ? "" : T(_2 === C3 ? h3 + z2 : h3);
        return s;
      }, {});
    });
    function j2(o) {
      const i3 = Math.abs(l.value) < 1e3 ? 1e3 : 0;
      p.value = o, I2.value = o ? Math.abs(l.value) + i3 : 0, c2.value && clearInterval(c2.value), c2.value = setInterval(o ? R : N, 1e3);
    }
    function N() {
      l.value < 1e3 ? (clearInterval(c2.value), A2("endCountDown"), b.value && j2(true)) : l.value -= 1e3;
    }
    function R() {
      I2.value += 1e3;
    }
    function E2() {
      c2.value && clearInterval(c2.value);
    }
    return w2({
      stopCount: E2
    }), (o, i3) => (openBlock(), createElementBlock("span", {
      class: normalizeClass({ "js-countdown-container": true, error: p.value })
    }, [
      !unref(a2) && unref(O2) ? (openBlock(), createElementBlock("span", te, toDisplayString(unref(u)("error.failed_to_get_server_time")), 1)) : createCommentVNode("", true),
      q.value ? renderSlot(o.$slots, "beforeCount", { key: 1 }, () => [
        createBaseVNode("span", ne2, [
          createBaseVNode("span", oe, toDisplayString(unref(u)("countdown.before.prefix")), 1),
          createBaseVNode("span", se2, toDisplayString(unref(import_format_datetime3.formatDateTime)(unref(g2)).format("LL")), 1),
          createBaseVNode("span", ae, toDisplayString(unref(u)("countdown.before.suffix")), 1)
        ])
      ]) : createCommentVNode("", true),
      F.value ? (openBlock(), createElementBlock("span", ue, [
        createBaseVNode("span", le2, toDisplayString(p.value ? unref(u)("countdown.counting_up.prefix") : unref(u)("countdown.counting_down.prefix")), 1),
        createBaseVNode("span", ie, [
          r2.value.dd ? (openBlock(), createElementBlock("span", ce, [
            createBaseVNode("span", re, toDisplayString(r2.value.dd), 1),
            createBaseVNode("span", de, toDisplayString(unref(u)("countdown.time_unit.dd")), 1)
          ])) : createCommentVNode("", true),
          r2.value.hh ? (openBlock(), createElementBlock("span", me, [
            createBaseVNode("span", fe, toDisplayString(r2.value.hh), 1),
            createBaseVNode("span", ve2, toDisplayString(unref(u)("countdown.time_unit.hh")), 1)
          ])) : createCommentVNode("", true),
          r2.value.mm ? (openBlock(), createElementBlock("span", pe, [
            createBaseVNode("span", _e, toDisplayString(r2.value.mm), 1),
            createBaseVNode("span", he2, toDisplayString(unref(u)("countdown.time_unit.mm")), 1)
          ])) : createCommentVNode("", true),
          r2.value.ss ? (openBlock(), createElementBlock("span", we, [
            createBaseVNode("span", ge, toDisplayString(r2.value.ss), 1),
            createBaseVNode("span", be, toDisplayString(unref(u)("countdown.time_unit.ss")), 1)
          ])) : createCommentVNode("", true)
        ]),
        createBaseVNode("span", Ce, toDisplayString(p.value ? unref(u)("countdown.counting_up.suffix") : unref(u)("countdown.counting_down.suffix")), 1)
      ])) : createCommentVNode("", true),
      S.value && !unref(b) ? renderSlot(o.$slots, "afterCount", { key: 3 }, () => [
        createBaseVNode("span", ke, toDisplayString(unref(u)("countdown.after")), 1)
      ]) : createCommentVNode("", true)
    ], 2));
  }
});

// node_modules/@perkd/vue-components/dist/components/UISwipeout.js
var le3 = {
  key: 0,
  class: "swipeout"
};
var ae2 = defineComponent({
  __name: "UISwipeout",
  props: {
    leftActions: {
      type: Array
    },
    rightActions: {
      type: Array
    },
    threshold: {
      type: Number,
      default: 0.8
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ["clickAction", "startSwipe", "endSwipe"],
  setup(Y2, { expose: z2, emit: D2 }) {
    const P = Y2, { leftActions: o, rightActions: d, threshold: B2, disabled: b } = toRefs(P), r2 = ref(null), X2 = ref(null), c2 = ref(null), i3 = ref(0), M = ref(0), $2 = ref(0), s = ref(0), p = ref(false), v3 = ref(true), x = ref(false), f = ref(null), w2 = D2, m3 = ref(false), h3 = computed(() => o != null && o.value || d != null && d.value ? d == null ? void 0 : d.value : [{
      key: "delete",
      color: "var(--color-background-error)",
      icon: "trash-o"
    }]), k3 = computed(() => {
      if (c2.value)
        return c2.value === "right" ? o == null ? void 0 : o.value : h3 == null ? void 0 : h3.value;
    });
    function V(e) {
      b.value || (M.value = e.touches[0].clientX, $2.value = e.touches[0].clientY, v3.value = true);
    }
    function q(e) {
      if (!r2.value || !v3.value || b.value) return;
      f.value !== null && (cancelAnimationFrame(f.value), f.value = null), s.value = e.touches[0].clientX - M.value + i3.value;
      const n = Math.abs(s.value) > Math.abs(e.touches[0].clientY - $2.value);
      if (!U2(r2.value.getBoundingClientRect(), e.touches[0].clientX, e.touches[0].clientY) || !n) {
        F();
        return;
      }
      c2.value = s.value > 0 ? "right" : "left", v3.value = O2(c2.value), v3.value && (p.value || (p.value = true, w2("startSwipe")), f.value = requestAnimationFrame(() => {
        y3(s.value);
      }));
    }
    function L2() {
      var n, u, t2;
      if (!r2.value || !p.value || b.value) return;
      if (f.value !== null && (cancelAnimationFrame(f.value), f.value = null), p.value = false, v3.value = false, B2.value && ((n = k3.value) == null ? void 0 : n.length) === 1 && Math.abs(s.value) > r2.value.offsetWidth * B2.value)
        R(((u = k3.value) == null ? void 0 : u[0].key) || "");
      else {
        const a2 = ((t2 = X2.value) == null ? void 0 : t2.getBoundingClientRect().width) || 80, g2 = Math.abs(s.value) > a2;
        i3.value = g2 ? s.value > 0 ? a2 : -a2 : 0, i3.value === 0 ? C3() : (y3(i3.value), m3.value = true);
      }
      w2("endSwipe", m3.value);
    }
    function F() {
      p.value = false, v3.value = false, y3(i3.value), i3.value === 0 && (c2.value = null), w2("endSwipe", m3.value);
    }
    function O2(e) {
      var t2, a2;
      const n = !!((t2 = o == null ? void 0 : o.value) != null && t2.length), u = !!((a2 = h3 == null ? void 0 : h3.value) != null && a2.length);
      return e === "right" && n || e === "left" && u;
    }
    function y3(e) {
      r2.value && (r2.value.style.transform = `translateX(${e}px)`);
    }
    function R(e, n = true) {
      n && C3(), w2("clickAction", e), e === "delete" && (x.value = true);
    }
    function C3() {
      r2.value && (i3.value = 0, c2.value = null, y3(i3.value), m3.value = false);
    }
    function U2(e, n, u) {
      const { left: t2, top: a2, height: g2 } = e;
      return n >= t2 && u >= a2 - g2 / 2 && u <= a2 + g2 * 2;
    }
    return z2({
      isSwipeoutOpen: m3,
      closeSwipeout: C3
    }), (e, n) => (openBlock(), createBlock(Transition, { name: "scale-up" }, {
      default: withCtx(() => {
        var u;
        return [
          x.value ? createCommentVNode("", true) : (openBlock(), createElementBlock("div", le3, [
            createBaseVNode("div", {
              class: "swipeout-background",
              style: normalizeStyle({ "background-color": (u = k3.value) == null ? void 0 : u[0].color })
            }, null, 4),
            createBaseVNode("div", {
              ref_key: "actionContainerRef",
              ref: X2,
              class: normalizeClass(`swipeout-action-container ${c2.value === "left" ? "right" : "left"}`)
            }, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(k3.value, (t2) => (openBlock(), createBlock(Q, {
                class: "swipeout-action",
                key: t2.key,
                style: normalizeStyle({ backgroundColor: t2.color }),
                icon: t2.icon ? { name: t2.icon } : void 0,
                title: t2.text,
                onTouchend: withModifiers((a2) => R(t2.key, t2.closeAfterClick), ["stop", "prevent"])
              }, null, 8, ["style", "icon", "title", "onTouchend"]))), 128))
            ], 2),
            createBaseVNode("div", {
              ref_key: "contentRef",
              ref: r2,
              class: normalizeClass(["swipeout-content", { swiping: v3.value && p.value }]),
              onTouchstartPassive: V,
              onTouchmovePassive: q,
              onTouchend: withModifiers(L2, ["stop", "prevent"]),
              onTouchcancel: withModifiers(F, ["stop", "prevent"])
            }, [
              renderSlot(e.$slots, "default")
            ], 34)
          ]))
        ];
      }),
      _: 3
    }));
  }
});

// node_modules/@perkd/vue-components/dist/components/UIMarquee.js
var k2 = defineComponent({
  __name: "UIMarquee",
  props: {
    text: {
      type: String,
      required: true
    },
    speed: {
      type: Number,
      default: 25
      // 25px per second
    },
    delay: {
      type: Number,
      default: 2
    }
  },
  setup(p) {
    const d = p, { text: y3, speed: i3, delay: c2 } = toRefs(d), o = ref(null), e = ref(null), r2 = ref(false), v3 = ref(0), n = ref(0), a2 = ref(0);
    onMounted(() => {
      if (o.value && e.value) {
        if (a2.value = e.value.clientWidth - o.value.clientWidth, r2.value = a2.value > 0, !r2.value) return;
        const t2 = a2.value / i3.value;
        e.value.style.transition = `all ${t2}s linear ${c2.value}s`, e.value.style.transform = `translateX(-${a2.value}px)`;
      }
    });
    function h3(t2) {
      r2.value && o.value && e.value && (e.value.style.animationPlayState = "paused", v3.value = t2.touches[0].clientX, n.value = m3(), e.value.style.transform = `translateX(${n.value}px)`, e.value.style.transition = "none");
    }
    function x(t2) {
      if (r2.value && t2.touches.length === 1 && e.value) {
        const l = t2.touches[0].clientX - v3.value, s = Math.min(Math.max(-a2.value, n.value + l), 0);
        e.value.style.transform = `translateX(${s}px)`;
      }
    }
    function f() {
      if (r2.value && o.value && e.value && (n.value = m3(), Math.abs(n.value) < a2.value)) {
        const t2 = (a2.value - Math.abs(n.value)) / i3.value;
        e.value.style.transition = `all ${t2}s linear ${c2.value}s`, e.value.style.transform = `translateX(-${a2.value}px)`, e.value.style.animationPlayState = "running";
      }
    }
    function m3() {
      if (!e.value) return 0;
      const l = getComputedStyle(e.value).transform;
      if (l === "none" || !l)
        return 0;
      const s = l.match(/matrix\((.+)\)/);
      return s && s[1].split(",").map(parseFloat)[4] || 0;
    }
    return (t2, l) => (openBlock(), createElementBlock("div", {
      ref_key: "containerRef",
      ref: o,
      class: "marquee-container"
    }, [
      createBaseVNode("div", {
        ref_key: "contentRef",
        ref: e,
        class: "marquee-content",
        onTouchstartPassive: h3,
        onTouchmovePassive: x,
        onTouchcancel: f,
        onTouchend: f
      }, toDisplayString(unref(y3)), 545)
    ], 512));
  }
});

// node_modules/@perkd/vue-components/dist/composables/useLoading.js
function r() {
  const t2 = reactive({
    show: false,
    success: void 0,
    text: ""
  });
  let e = null;
  function i3(s = "") {
    t2.show = true, t2.success = void 0, t2.text = s;
  }
  function n(s, o, u = 1500) {
    t2.success = s, e && clearTimeout(e), e = setTimeout(() => {
      t2.show = false, o && o();
    }, u);
  }
  return {
    loading: t2,
    startLoading: i3,
    endLoadingWithDelay: n
  };
}

// node_modules/@perkd/vue-components/dist/composables/useStorage.js
function c(t2) {
  const e = ref(JSON.parse(localStorage.getItem(t2) || "{}") || {});
  function r2(o) {
    Object.assign(e.value, o), localStorage.setItem(t2, JSON.stringify(e.value));
  }
  return {
    preferenceStorage: e,
    updatePreferenceStorage: r2
  };
}

// node_modules/@perkd/applet-common/dist/types/actions.js
var Actions;
(function(Actions2) {
  let Remote;
  (function(Remote2) {
    Remote2["CONNECT"] = "remote.connect";
    Remote2["SEND"] = "remote.send";
    Remote2["CLOSE"] = "remote.close";
  })(Remote = Actions2.Remote || (Actions2.Remote = {}));
})(Actions || (Actions = {}));

// node_modules/@perkd/applet-common/dist/websocket.js
var { CONNECT, SEND, CLOSE } = Actions.Remote;
var SUBSCRIBE = "subscribe";
var Websocket = {
  create: async (SOCKET_URL, cardId, masterId) => {
    if (!SOCKET_URL) {
      return { error: { statusMessage: "websocket.config_missing" } };
    }
    const url = `${SOCKET_URL}/metrics-push?access_token={{x-access-token}}`;
    const credentials = "perkd";
    try {
      const socketId = await window.$perkd.do(CONNECT, { url, cardId, masterId, credentials });
      return socketId;
    } catch (error) {
      return { error };
    }
  },
  subscribe: async (id, message) => {
    try {
      await window.$perkd.do(SEND, { id, message: JSON.stringify({ type: SUBSCRIBE, ...message }) });
    } catch (error) {
      return { error };
    }
  },
  unsubscribe: async (id) => {
    try {
      await window.$perkd.do(CLOSE, { id });
    } catch (error) {
      return { error };
    }
  }
};
async function onSocketMessage(event, sockets) {
  const result = parseEvent(event);
  if (isErrorResponse(result)) {
    return { error: { ...result.error, statusMessage: "websocket.onmessage_error" } };
  }
  const found = Object.entries(sockets).find(([key, socketId]) => result.socketId === socketId);
  if (!found) {
    const { socketId } = result;
    const response = await Websocket.unsubscribe(socketId);
    if (isErrorResponse(response)) {
      return {
        error: {
          ...response.error,
          statusMessage: "websocket.onmessage_unsubscribe_failed",
          socketId,
          sockets
        }
      };
    }
    return { error: { statusMessage: "websocket.onmessage_unsubscribe", socketId, data: result.data, sockets } };
  }
  return {
    key: found[0],
    data: result.data
  };
}
async function onSocketClose(event, sockets) {
  const result = parseEvent(event);
  if (isErrorResponse(result)) {
    return { error: { ...result.error, statusMessage: "websocket.close_failed", sockets } };
  }
  const found = Object.entries(sockets).find(([key, socketId]) => result.socketId === socketId);
  if (!found) {
    const { socketId } = result;
    return { error: { statusMessage: "websocket.onclose_unknown_socket", socketId, sockets } };
  }
  const closedKey = found[0];
  if (sockets[closedKey])
    sockets[closedKey] = "";
  return { socketId: result.socketId, key: closedKey };
}
async function onSocketError(appletName, sockets, data) {
  return { error: { ...data, statusMessage: "websocket.onerror", sockets } };
}
function parseEvent(event) {
  const { id, message } = event.detail || {};
  const result = { socketId: id, data: {} };
  try {
    if (message)
      result.data = JSON.parse(message);
  } catch (error) {
    return { error };
  }
  return result;
}

// node_modules/@perkd/vue-components/dist/composables/useWebsocket.js
function C2(e, f) {
  const a2 = ref(""), i3 = ref(""), c2 = ref(false), u = ref(0), r2 = ref(e.status), k3 = debounce(async () => {
    await e.keepAlive();
  }, 300);
  watch(r2, (s) => {
    Object.keys(s).filter((t2) => !s[t2]).length && k3();
  });
  async function d(s) {
    const n = await onSocketMessage(s, e.status);
    if (isErrorResponse(n)) {
      console.log("WebSocket Error:", n.error);
      return;
    }
    const { key: t2, data: y3 } = n;
    t2 && e.onMessage({ key: t2, data: y3 }), a2.value = (/* @__PURE__ */ new Date()).toISOString();
  }
  async function w2(s) {
    const n = JSON.parse(JSON.stringify(e.status)), t2 = await onSocketClose(s, n);
    if (isErrorResponse(t2)) {
      console.log("WebSocket Error:", t2.error);
      return;
    }
    e.onClose(t2), r2.value = e.status;
  }
  async function l(s) {
    const n = JSON.parse(JSON.stringify(e.status));
    u.value += 1, u.value < 5 && await onSocketError(f, n, s);
  }
  async function v3() {
    i3.value = (/* @__PURE__ */ new Date()).toISOString(), await e.keepAlive();
  }
  return onMounted(async () => {
    window.addEventListener("socket.message", d), window.addEventListener("socket.close", w2), window.addEventListener("socket.error", l), window.addEventListener("app.resume", v3), await e.start(), r2.value = e.status, c2.value = true;
  }), onBeforeUnmount(async () => {
    window.removeEventListener("socket.message", d), window.removeEventListener("socket.close", w2), window.removeEventListener("socket.error", l), window.removeEventListener("app.resume", v3), await e.end();
  }), {
    topic: e,
    ready: c2,
    syncTime: a2,
    resumeTime: i3
  };
}

// node_modules/@perkd/vue-components/dist/translation.js
var m2 = { button: { ok: "Ok", cancel: "Cancel", submit: "Submit", delete: "Delete" }, form: { search: "search", join: "join", expire: "expire" }, countdown: { before: { prefix: "end on ", suffix: "" }, after: "Event ended", counting_down: { prefix: "end in ", suffix: "" }, counting_up: { prefix: "overdue", suffix: "" }, time_unit: { dd: "day", hh: "hour", mm: "min", ss: "sec" } }, error: { offline_status: "offline", failed_to_get_server_time: "Failed to get server time", is_required: "required", minimum_length: "min. {n} character | min. {n} characters", maximum_length: "max. {n} character | max. {n} characters", minimum_number: "must be {n} or above", maximum_number: "must be {n} or below", invalid_pattern: "wrong format", invalid: "invalid", invalid_date: "wrong date format", before_minimum_date: "must be {date} or later", after_maximum_date: "must be {date} or earlier", above_minimum_amount: "must be {amount} or above", below_maximum_amount: "must be {amount} or below" } };
var i2 = {
  en: m2,
  "zh-Hans": { button: { ok: "好", cancel: "取消", submit: "提交", delete: "删除" }, form: { search: "搜索", join: "加入", expire: "到期" }, countdown: { before: { prefix: "", suffix: "结束" }, after: "活动已结束", counting_down: { prefix: "", suffix: "后结束" }, counting_up: { prefix: "超时", suffix: "" }, time_unit: { dd: "天", hh: "时", mm: "分", ss: "秒" } }, error: { offline_status: "离线", failed_to_get_server_time: "服务器时间获取失败", is_required: "必填项", minimum_length: "最少{n}个字符", maximum_length: "最多{n}个字符", minimum_number: "最少为{n}", maximum_number: "最多为{n}", invalid_pattern: "格式错误", invalid: "错误", invalid_date: "时间格式错误", before_minimum_date: "应该在{date}或之后", after_maximum_date: "应该在{date}或之前", above_minimum_amount: "金额至少为{amount}", below_maximum_amount: "金额最多为{amount}" } },
  "zh-Hant": { button: { ok: "好", cancel: "取消", submit: "提交", delete: "刪除" }, form: { search: "搜寻", join: "加入", expire: "到期" }, countdown: { before: { prefix: "", suffix: "結束" }, after: "活動已結束", counting_down: { prefix: "", suffix: "後結束" }, counting_up: { prefix: "超時", suffix: "" }, time_unit: { dd: "天", hh: "时", mm: "分", ss: "秒" } }, error: { offline_status: "離線", failed_to_get_server_time: "服務器時間獲取失敗", is_required: "必填項", minimum_length: "最少{n}個字符", maximum_length: "最多{n}個字符", minimum_number: "最少为{n}", maximum_number: "最多为{n}", invalid_pattern: "格式錯誤", invalid: "錯誤", invalid_date: "時間格式錯誤", before_minimum_date: "應該在{date}之後", after_maximum_date: "應該在{date}之前", above_minimum_amount: "金額至少為{amount}", below_maximum_amount: "金額最多為{amount}" } }
};
var t = createI18n({
  legacy: false,
  locale: "en",
  messages: i2
});
export {
  Q as Button,
  bA as Card,
  G as CardOverlay,
  dt as CircleProgress,
  Te as CountDown,
  D as Dialog,
  ee as Field,
  i as Icon,
  he as Input,
  h2 as Loading,
  k2 as Marquee,
  G2 as Modal,
  I as NavigationBar,
  B as Ripple,
  ve as Screen,
  y2 as Selection,
  se as SlideButton,
  ae2 as Swipeout,
  O as Switch,
  t as i18n,
  r as useLoading,
  c as useStorage,
  C2 as useWebsocket
};
//# sourceMappingURL=@perkd_vue-components.js.map
