{"version": 3, "sources": ["../../@perkd/applet-common/dist/types/applets.js"], "sourcesContent": ["export var Applets;\n(function (Applets) {\n    let Language;\n    (function (Language) {\n        Language[\"ENGLISH\"] = \"en\";\n        Language[\"CHINESE_SIMPLIFIED\"] = \"zh-Hans\";\n        Language[\"CHINESE_TRADITIONAL\"] = \"zh-Hant\";\n        Language[\"CHINESE_TRADITIONAL_HK\"] = \"zh-Hant-HK\";\n        Language[\"CHINESE_TRADITIONAL_TW\"] = \"zh-Hant-TW\";\n        Language[\"JAPANESE\"] = \"ja\";\n        Language[\"KOREAN\"] = \"ko\";\n        Language[\"BAHASA_MELAYU\"] = \"ms\";\n        Language[\"BAHASA_INDONESIA\"] = \"id\";\n    })(Language = Applets.Language || (Applets.Language = {}));\n    Applets.LANGUAGES = {\n        DEFAULT: Language.ENGLISH,\n        SUPPORTED: [\n            Language.ENGLISH,\n            Language.CHINESE_SIMPLIFIED,\n            Language.CHINESE_TRADITIONAL\n        ],\n        FALLBACKS: {\n            [Language.CHINESE_TRADITIONAL_HK]: [Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.ENGLISH],\n            [Language.CHINESE_TRADITIONAL_TW]: [Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_SIMPLIFIED, Language.ENGLISH],\n            [Language.CHINESE_TRADITIONAL]: [Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_SIMPLIFIED, Language.ENGLISH],\n            [Language.CHINESE_SIMPLIFIED]: [Language.CHINESE_SIMPLIFIED, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL_HK, Language.ENGLISH],\n            [Language.BAHASA_MELAYU]: [Language.BAHASA_MELAYU, Language.BAHASA_INDONESIA, Language.ENGLISH],\n            [Language.BAHASA_INDONESIA]: [Language.BAHASA_INDONESIA, Language.BAHASA_MELAYU, Language.ENGLISH],\n            [Language.JAPANESE]: [Language.JAPANESE, Language.ENGLISH],\n            [Language.KOREAN]: [Language.KOREAN, Language.ENGLISH],\n            [Language.ENGLISH]: [Language.ENGLISH],\n            default: [Language.ENGLISH],\n        },\n    };\n    let DateTimePickerType;\n    (function (DateTimePickerType) {\n        DateTimePickerType[\"TIME\"] = \"time\";\n        DateTimePickerType[\"DATE\"] = \"date\";\n        DateTimePickerType[\"DATETIME\"] = \"datetime\";\n    })(DateTimePickerType = Applets.DateTimePickerType || (Applets.DateTimePickerType = {}));\n    let ColorScheme;\n    (function (ColorScheme) {\n        ColorScheme[\"DARK\"] = \"dark\";\n        ColorScheme[\"LIGHT\"] = \"light\";\n    })(ColorScheme = Applets.ColorScheme || (Applets.ColorScheme = {}));\n    Applets.DefaultTheme = {\n        light: {\n            background: '#FFFFFFFF',\n            text: '#1A1A1AFF',\n            primary: '#F99300FF',\n            secondary: '#7B7B7BFF',\n            tertiary: '#999220FF',\n            accent: '#0A84FFFF',\n        },\n        dark: {\n            background: '#3D3D3DFF',\n            text: '#FFFFFFFF',\n            primary: '#FFB300FF',\n            secondary: '#BABABAFF',\n            tertiary: '#FF3F34FF',\n            accent: '#0AA3FFFF',\n        },\n    };\n})(Applets || (Applets = {}));\n"], "mappings": ";AAAO,IAAI;AAAA,CACV,SAAUA,UAAS;AAChB,MAAI;AACJ,GAAC,SAAUC,WAAU;AACjB,IAAAA,UAAS,SAAS,IAAI;AACtB,IAAAA,UAAS,oBAAoB,IAAI;AACjC,IAAAA,UAAS,qBAAqB,IAAI;AAClC,IAAAA,UAAS,wBAAwB,IAAI;AACrC,IAAAA,UAAS,wBAAwB,IAAI;AACrC,IAAAA,UAAS,UAAU,IAAI;AACvB,IAAAA,UAAS,QAAQ,IAAI;AACrB,IAAAA,UAAS,eAAe,IAAI;AAC5B,IAAAA,UAAS,kBAAkB,IAAI;AAAA,EACnC,GAAG,WAAWD,SAAQ,aAAaA,SAAQ,WAAW,CAAC,EAAE;AACzD,EAAAA,SAAQ,YAAY;AAAA,IAChB,SAAS,SAAS;AAAA,IAClB,WAAW;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACb;AAAA,IACA,WAAW;AAAA,MACP,CAAC,SAAS,sBAAsB,GAAG,CAAC,SAAS,wBAAwB,SAAS,qBAAqB,SAAS,wBAAwB,SAAS,OAAO;AAAA,MACpJ,CAAC,SAAS,sBAAsB,GAAG,CAAC,SAAS,wBAAwB,SAAS,qBAAqB,SAAS,wBAAwB,SAAS,oBAAoB,SAAS,OAAO;AAAA,MACjL,CAAC,SAAS,mBAAmB,GAAG,CAAC,SAAS,qBAAqB,SAAS,wBAAwB,SAAS,wBAAwB,SAAS,oBAAoB,SAAS,OAAO;AAAA,MAC9K,CAAC,SAAS,kBAAkB,GAAG,CAAC,SAAS,oBAAoB,SAAS,qBAAqB,SAAS,wBAAwB,SAAS,wBAAwB,SAAS,OAAO;AAAA,MAC7K,CAAC,SAAS,aAAa,GAAG,CAAC,SAAS,eAAe,SAAS,kBAAkB,SAAS,OAAO;AAAA,MAC9F,CAAC,SAAS,gBAAgB,GAAG,CAAC,SAAS,kBAAkB,SAAS,eAAe,SAAS,OAAO;AAAA,MACjG,CAAC,SAAS,QAAQ,GAAG,CAAC,SAAS,UAAU,SAAS,OAAO;AAAA,MACzD,CAAC,SAAS,MAAM,GAAG,CAAC,SAAS,QAAQ,SAAS,OAAO;AAAA,MACrD,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,OAAO;AAAA,MACrC,SAAS,CAAC,SAAS,OAAO;AAAA,IAC9B;AAAA,EACJ;AACA,MAAI;AACJ,GAAC,SAAUE,qBAAoB;AAC3B,IAAAA,oBAAmB,MAAM,IAAI;AAC7B,IAAAA,oBAAmB,MAAM,IAAI;AAC7B,IAAAA,oBAAmB,UAAU,IAAI;AAAA,EACrC,GAAG,qBAAqBF,SAAQ,uBAAuBA,SAAQ,qBAAqB,CAAC,EAAE;AACvF,MAAI;AACJ,GAAC,SAAUG,cAAa;AACpB,IAAAA,aAAY,MAAM,IAAI;AACtB,IAAAA,aAAY,OAAO,IAAI;AAAA,EAC3B,GAAG,cAAcH,SAAQ,gBAAgBA,SAAQ,cAAc,CAAC,EAAE;AAClE,EAAAA,SAAQ,eAAe;AAAA,IACnB,OAAO;AAAA,MACH,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACF,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,IACZ;AAAA,EACJ;AACJ,GAAG,YAAY,UAAU,CAAC,EAAE;", "names": ["Applets", "Language", "DateTimePickerType", "ColorScheme"]}