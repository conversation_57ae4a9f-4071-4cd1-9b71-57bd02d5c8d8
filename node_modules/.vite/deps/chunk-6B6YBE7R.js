// node_modules/@perkd/applet-common/dist/types/applets.js
var Applets;
(function(Applets2) {
  let Language;
  (function(Language2) {
    Language2["ENGLISH"] = "en";
    Language2["CHINESE_SIMPLIFIED"] = "zh-<PERSON>";
    Language2["CHINESE_TRADITIONAL"] = "zh-Hant";
    Language2["CHINESE_TRADITIONAL_HK"] = "zh-Hant-HK";
    Language2["CHINESE_TRADITIONAL_TW"] = "zh-Hant-TW";
    Language2["JAPANESE"] = "ja";
    Language2["KOREAN"] = "ko";
    Language2["BAHASA_MELAYU"] = "ms";
    Language2["BAHASA_INDONESIA"] = "id";
  })(Language = Applets2.Language || (Applets2.Language = {}));
  Applets2.LANGUAGES = {
    DEFAULT: Language.ENGLISH,
    SUPPORTED: [
      Language.ENGLISH,
      Language.CHINESE_SIMPLIFIED,
      Language.CHINESE_TRADITIONAL
    ],
    FALLBACKS: {
      [Language.CHINESE_TRADITIONAL_HK]: [Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.ENGLISH],
      [Language.CHINESE_TRADITIONAL_TW]: [Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_SIMPLIFIED, Language.ENGLISH],
      [Language.CHINESE_TRADITIONAL]: [Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_SIMPLIFIED, Language.ENGLISH],
      [Language.CHINESE_SIMPLIFIED]: [Language.CHINESE_SIMPLIFIED, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL_HK, Language.ENGLISH],
      [Language.BAHASA_MELAYU]: [Language.BAHASA_MELAYU, Language.BAHASA_INDONESIA, Language.ENGLISH],
      [Language.BAHASA_INDONESIA]: [Language.BAHASA_INDONESIA, Language.BAHASA_MELAYU, Language.ENGLISH],
      [Language.JAPANESE]: [Language.JAPANESE, Language.ENGLISH],
      [Language.KOREAN]: [Language.KOREAN, Language.ENGLISH],
      [Language.ENGLISH]: [Language.ENGLISH],
      default: [Language.ENGLISH]
    }
  };
  let DateTimePickerType;
  (function(DateTimePickerType2) {
    DateTimePickerType2["TIME"] = "time";
    DateTimePickerType2["DATE"] = "date";
    DateTimePickerType2["DATETIME"] = "datetime";
  })(DateTimePickerType = Applets2.DateTimePickerType || (Applets2.DateTimePickerType = {}));
  let ColorScheme;
  (function(ColorScheme2) {
    ColorScheme2["DARK"] = "dark";
    ColorScheme2["LIGHT"] = "light";
  })(ColorScheme = Applets2.ColorScheme || (Applets2.ColorScheme = {}));
  Applets2.DefaultTheme = {
    light: {
      background: "#FFFFFFFF",
      text: "#1A1A1AFF",
      primary: "#F99300FF",
      secondary: "#7B7B7BFF",
      tertiary: "#999220FF",
      accent: "#0A84FFFF"
    },
    dark: {
      background: "#3D3D3DFF",
      text: "#FFFFFFFF",
      primary: "#FFB300FF",
      secondary: "#BABABAFF",
      tertiary: "#FF3F34FF",
      accent: "#0AA3FFFF"
    }
  };
})(Applets || (Applets = {}));

export {
  Applets
};
//# sourceMappingURL=chunk-6B6YBE7R.js.map
