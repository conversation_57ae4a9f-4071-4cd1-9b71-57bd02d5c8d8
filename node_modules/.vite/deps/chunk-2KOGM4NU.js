import {
  __commonJS
} from "./chunk-EQCVQC35.js";

// node_modules/@perkd/format-datetime/dist/locale/en.js
var require_en = __commonJS({
  "node_modules/@perkd/format-datetime/dist/locale/en.js"(exports) {
    Object.defineProperty(exports, "__esModule", { value: true });
    var locale = {
      name: "en",
      weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),
      months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"),
      weekStart: 1,
      weekdaysShort: "Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),
      monthsShort: "Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),
      weekdaysMin: "Su_Mo_Tu_We_Th_Fr_Sa".split("_"),
      ordinal: (n) => {
        const s = ["th", "st", "nd", "rd"];
        const v = n % 100;
        return `[${n}${s[(v - 20) % 10] || s[v] || s[0]}]`;
      },
      formats: {
        LT: "h:mma",
        LTS: "ha",
        L: "D MMM, LTX",
        LL: "D MMM YYYY, LTX",
        LLL: "dddd, D MMM, LTX",
        LLLL: "dddd, D MMM YYYY, LTX",
        l: "D MMM",
        ll: "D MMM YYYY",
        lll: "dddd, D MMM",
        llll: "dddd, D MMM YYYY"
      },
      calendar: {
        lastDay: "[yesterday]",
        sameDay: "[today]",
        nextDay: "[tomorrow]",
        lastWeek: "[last] dddd",
        sameWeek: "dddd",
        nextWeek: "[next] dddd",
        sameYear: "l",
        sameElse: "ll",
        timeFormat: "%c, LTX"
      },
      relativeTime: {
        future: "in %s",
        past: "%s ago",
        s: "a few seconds",
        m: "a minute",
        mm: "%d minutes",
        h: "an hour",
        hh: "%d hours",
        d: "a day",
        dd: "%d days",
        M: "a month",
        MM: "%d months",
        y: "a year",
        yy: "%d years"
      },
      humane: {
        daysToRelative: 0,
        daysToCalendar: 1,
        skipFromUnit: "second",
        startFrom: {
          value: 30,
          unit: "second"
        },
        soon: "in few %u",
        justnow: "just now",
        s: "1 sec",
        ss: "%d secs",
        m: "1 min",
        mm: "%d mins",
        h: "1 hour",
        hh: "%d hours",
        d: "1 day",
        dd: "%d days",
        M: "1 month",
        MM: "%d months",
        y: "1 year",
        yy: "%d years"
      },
      period: {
        daysToCalendar: 1,
        showSameDayToday: false,
        sameYear: { startDate: "l", endDate: "lyx", startTime: "LYX", endTime: "LYX", format: "%ds - %de" },
        sameMonth: { startDate: "D", endDate: "lyx", startTime: "LYX", endTime: "LYX", format: "%ds - %de" },
        sameDay: { startDate: "lyx", endDate: "", startTime: "LYX", endTime: "LTX", format: "%ds - %de" },
        sameMeridiem: { startDate: "lyx", endDate: "", startTime: "LYX", endTime: "LTX", format: "%ds - %de" },
        others: { startDate: "ll", endDate: "ll", startTime: "LL", endTime: "LL", format: "%ds - %de" }
      }
    };
    exports.default = locale;
  }
});

export {
  require_en
};
//# sourceMappingURL=chunk-2KOGM4NU.js.map
