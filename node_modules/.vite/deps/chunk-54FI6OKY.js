import {
  i
} from "./chunk-QMSMXSBA.js";
import {
  Fragment,
  Transition,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createVNode,
  defineComponent,
  normalizeClass,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  openBlock,
  ref,
  renderList,
  renderSlot,
  toDisplayString,
  toRefs,
  unref,
  useCssVars,
  withCtx
} from "./chunk-U3LI7FBV.js";

// node_modules/@perkd/vue-components/dist/components/UILoading.js
var k = { class: "loading-container" };
var B = {
  key: 0,
  class: "loading-text"
};
var h = defineComponent({
  __name: "UILoading",
  props: {
    colorBackground: {
      type: Boolean,
      default: false
    },
    success: {
      type: Boolean,
      default: void 0
    },
    thickness: {
      type: String,
      default: ""
    },
    size: {
      type: String,
      default: "md"
    },
    color: {
      type: String,
      default: ""
    },
    emptyColor: {
      type: String,
      default: ""
    },
    successColor: {
      type: String,
      default: ""
    },
    failedColor: {
      type: String,
      default: ""
    },
    text: {
      type: String,
      default: ""
    }
  },
  setup(o) {
    useCssVars((t) => ({
      "3b55d032": u.value,
      "016ed342": d.value,
      "178fa1ae": i2.value,
      "5b848406": m.value,
      "4a23360b": p.value,
      "51a6d6fd": f.value
    }));
    const e = o, u = computed(() => r(e.color || (e.colorBackground ? "#FFFFFF" : "accent"))), d = computed(() => r(e.emptyColor || (e.colorBackground ? "rgba(255,255,255,0.2)" : "var(--color-background-heavy)"))), i2 = computed(() => r(e.successColor || (e.colorBackground ? "#FFFFFF" : "success"))), m = computed(() => r(e.failedColor || (e.colorBackground ? "#FFFFFF" : "error"))), p = computed(() => {
      const t = e.thickness || e.size;
      switch (t) {
        case "xxs":
          return "2px";
        case "xs":
          return "2px";
        case "sm":
          return "4px";
        case "md":
          return "6px";
        case "lg":
          return "8px";
        case "xl":
          return "10px";
        default:
          return t || "6px";
      }
    }), f = computed(() => {
      switch (e.size) {
        case "xxs":
          return "1em";
        case "xs":
          return "2em";
        case "sm":
          return "3em";
        case "md":
          return "5em";
        case "lg":
          return "7em";
        case "xl":
          return "10em";
        default:
          return e.size || "5em";
      }
    }), g = computed(() => e.success === true ? "success" : e.success === false ? "failed" : "");
    function r(t) {
      return ["primary", "accent", "success", "warning", "error"].indexOf(t) !== -1 ? `var(--color-background-${t})` : t;
    }
    return (t, a) => (openBlock(), createElementBlock("div", k, [
      createBaseVNode("div", {
        class: normalizeClass(`circle-loader ${g.value}`)
      }, a[0] || (a[0] = [
        createBaseVNode("div", { class: "status draw" }, null, -1)
      ]), 2),
      o.text ? (openBlock(), createElementBlock("div", B, toDisplayString(o.text), 1)) : createCommentVNode("", true)
    ]));
  }
});

// node_modules/@perkd/vue-components/dist/components/UIRipple.js
var k2 = { class: "ripple-container" };
var B2 = defineComponent({
  __name: "UIRipple",
  setup($, { expose: a }) {
    const n = ref([]), l = ref([]);
    function u(t) {
      const e = t.currentTarget.getBoundingClientRect(), o = Math.max(e.width, e.height), m = t.clientX - e.left - o / 2, h2 = t.clientY - e.top - o / 2, i2 = Date.now();
      n.value.push({
        key: i2,
        style: {
          width: `${o}px`,
          height: `${o}px`,
          top: `${h2}px`,
          left: `${m}px`
        }
      });
      const d = setTimeout(() => {
        n.value = n.value.filter((f) => f.key !== i2);
      }, 600);
      l.value.push(d);
    }
    return onUnmounted(() => {
      l.value.forEach((t) => clearTimeout(t)), l.value = [];
    }), a({
      createRipple: u
    }), (t, p) => (openBlock(), createElementBlock("div", k2, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(n.value, (e) => (openBlock(), createElementBlock("div", {
        class: "ripple",
        key: e.key,
        style: normalizeStyle(e.style)
      }, null, 4))), 128))
    ]));
  }
});

// node_modules/@perkd/vue-components/dist/components/UIButton.js
var A = {
  key: 1,
  class: "button-title-container"
};
var F = {
  key: 0,
  class: "status-container"
};
var Q = defineComponent({
  __name: "UIButton",
  props: {
    type: {
      type: String,
      default: "solid"
    },
    color: {
      type: String,
      default: "accent"
    },
    icon: {
      type: Object,
      required: false
    },
    title: String,
    titleClass: {
      type: String,
      default: ""
    },
    subtitle: String,
    subtitleClass: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ["click"],
  setup(e, { emit: P }) {
    const z = e, { loading: v } = toRefs(z), g = P, l = ref(false), o = ref(null), m = ref(void 0), y = "ontouchstart" in window;
    let r;
    onMounted(() => {
      var t, n, i2, a, c;
      y ? ((t = o.value) == null || t.addEventListener("touchstart", B3, { passive: true }), (n = o.value) == null || n.addEventListener("touchmove", C, { passive: true }), (i2 = o.value) == null || i2.addEventListener("touchend", E, { passive: true }), (a = o.value) == null || a.addEventListener("touchcancel", R)) : (c = o.value) == null || c.addEventListener("click", k3);
    }), onBeforeUnmount(() => {
      var t, n, i2, a, c;
      r && clearTimeout(r), y ? ((t = o.value) == null || t.removeEventListener("touchstart", B3), (n = o.value) == null || n.removeEventListener("touchmove", C), (i2 = o.value) == null || i2.removeEventListener("touchend", E), (a = o.value) == null || a.removeEventListener("touchcancel", R)) : (c = o.value) == null || c.removeEventListener("click", k3);
    });
    function k3(t) {
      var n;
      t.stopPropagation(), !(l.value || v.value) && (l.value = true, (n = m.value) == null || n.createRipple(t), r && clearTimeout(r), r = setTimeout(() => {
        g("click", t), l.value = false;
      }, 200));
    }
    function B3(t) {
      var n;
      o.value && !l.value && !v.value && (l.value = true, (n = m.value) == null || n.createRipple(t));
    }
    function C(t) {
      if (o.value) {
        const n = t.touches[0], i2 = o.value.getBoundingClientRect();
        L(i2, n.clientX, n.clientY) || (l.value = false);
      }
    }
    function E(t) {
      if (t.stopPropagation(), o.value && l.value) {
        const n = t.changedTouches[0], i2 = o.value.getBoundingClientRect();
        L(i2, n.clientX, n.clientY) && g("click", t), l.value = false;
      }
    }
    function R(t) {
      t.stopPropagation(), o.value && l.value && (l.value = false);
    }
    function L(t, n, i2) {
      const { left: a, top: c, width: N, height: V } = t;
      return n >= a && n <= a + N && i2 >= c && i2 <= c + V;
    }
    return (t, n) => (openBlock(), createElementBlock("div", {
      ref_key: "buttonRef",
      ref: o,
      class: normalizeClass(["button", e.type, e.color, e.disabled ? "disabled" : "", e.icon && !(e.title || e.subtitle) && e.type === "clear" ? "clear-icon" : ""])
    }, [
      createBaseVNode("div", {
        class: "button-wrapper",
        style: normalizeStyle({ opacity: unref(v) ? 0 : 1 })
      }, [
        e.icon && e.icon.position !== "right" ? (openBlock(), createBlock(i, {
          key: 0,
          name: e.icon.name,
          class: normalizeClass(`button-icon ${e.icon.class || ""}`)
        }, null, 8, ["name", "class"])) : createCommentVNode("", true),
        e.title || e.subtitle || t.$slots.content ? (openBlock(), createElementBlock("span", A, [
          e.title ? (openBlock(), createElementBlock("span", {
            key: 0,
            class: normalizeClass("button-title " + e.titleClass)
          }, toDisplayString(e.title), 3)) : createCommentVNode("", true),
          e.subtitle ? (openBlock(), createElementBlock("span", {
            key: 1,
            class: normalizeClass("button-subtitle " + e.subtitleClass)
          }, toDisplayString(e.subtitle), 3)) : createCommentVNode("", true),
          renderSlot(t.$slots, "content")
        ])) : createCommentVNode("", true),
        e.icon && e.icon.position === "right" ? (openBlock(), createBlock(i, {
          key: 2,
          name: e.icon.name,
          class: normalizeClass("button-icon " + e.icon.class)
        }, null, 8, ["name", "class"])) : createCommentVNode("", true)
      ], 4),
      createVNode(Transition, { name: "fade" }, {
        default: withCtx(() => [
          unref(v) ? (openBlock(), createElementBlock("div", F, [
            renderSlot(t.$slots, "status", {}, () => [
              createVNode(h, {
                size: "xxs",
                colorBackground: e.type === "solid"
              }, null, 8, ["colorBackground"])
            ])
          ])) : createCommentVNode("", true)
        ]),
        _: 3
      }),
      createVNode(B2, {
        ref_key: "rippleRef",
        ref: m
      }, null, 512)
    ], 2));
  }
});

export {
  h,
  B2 as B,
  Q
};
//# sourceMappingURL=chunk-54FI6OKY.js.map
