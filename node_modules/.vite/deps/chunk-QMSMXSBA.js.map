{"version": 3, "sources": ["../../@perkd/vue-components/dist/components/UIIcon.js"], "sourcesContent": ["import { defineComponent as n, createElementBlock as r, openBlock as o, normalizeClass as t } from \"vue\";\nconst i = /* @__PURE__ */ n({\n  __name: \"UIIcon\",\n  props: {\n    name: {\n      type: String,\n      require: !0\n    },\n    color: {\n      type: String,\n      require: !1\n    }\n  },\n  setup(e) {\n    return (c, a) => (o(), r(\"span\", {\n      class: t([\"picon\", `picon-${e.name}`, e.color || \"\"])\n    }, null, 2));\n  }\n});\nexport {\n  i as default\n};\n"], "mappings": ";;;;;;;;AACA,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ;AAAA,MAC/B,OAAO,eAAE,CAAC,SAAS,SAAS,EAAE,IAAI,IAAI,EAAE,SAAS,EAAE,CAAC;AAAA,IACtD,GAAG,MAAM,CAAC;AAAA,EACZ;AACF,CAAC;", "names": []}