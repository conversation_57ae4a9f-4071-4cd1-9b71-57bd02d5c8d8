import {
  Applets
} from "./chunk-6B6YBE7R.js";
import {
  __commonJS,
  __toESM
} from "./chunk-EQCVQC35.js";

// node_modules/@perkd/crm-types/dist/contacts.js
var require_contacts = __commonJS({
  "node_modules/@perkd/crm-types/dist/contacts.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Contacts = void 0;
    var Contacts;
    (function(Contacts2) {
      let Type;
      (function(Type2) {
        Type2["MOBILE"] = "mobile";
        Type2["HOME"] = "home";
        Type2["WORK"] = "work";
        Type2["OTHERS"] = "others";
      })(Type = Contacts2.Type || (Contacts2.Type = {}));
      let Dates;
      (function(Dates2) {
        Dates2["BIRTH"] = "birth";
        Dates2["GRADUATE"] = "graduate";
        Dates2["MARRIED"] = "married";
        Dates2["BAPTISED"] = "baptised";
        Dates2["CONTRACT_START"] = "contractstart";
        Dates2["CONTRACT_END"] = "contractend";
      })(Dates = Contacts2.Dates || (Contacts2.Dates = {}));
      let UrlKind;
      (function(UrlKind2) {
        UrlKind2["WEBSITE"] = "website";
        UrlKind2["EMAIL"] = "email";
        UrlKind2["SOCIAL"] = "social";
        UrlKind2["CUSTOM"] = "custom";
      })(UrlKind = Contacts2.UrlKind || (Contacts2.UrlKind = {}));
    })(Contacts || (exports.Contacts = Contacts = {}));
  }
});

// node_modules/@perkd/crm-types/dist/persons.js
var require_persons = __commonJS({
  "node_modules/@perkd/crm-types/dist/persons.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Socials = exports.Persons = void 0;
    var contacts_1 = require_contacts();
    var Persons2;
    (function(Persons3) {
      let Gender;
      (function(Gender2) {
        Gender2["MALE"] = "m";
        Gender2["FEMALE"] = "f";
      })(Gender = Persons3.Gender || (Persons3.Gender = {}));
      let NameOrder;
      (function(NameOrder2) {
        NameOrder2["FAMILY_GIVEN"] = "familygiven";
        NameOrder2["GIVEN_FAMILY"] = "givenfamily";
      })(NameOrder = Persons3.NameOrder || (Persons3.NameOrder = {}));
      let Identities;
      (function(Identities2) {
        Identities2["PERKD"] = "perkd";
        Identities2["USER"] = "user";
        Identities2["CUSTOMER"] = "customer";
        Identities2["SMARTCOLLECTION"] = "smartcollection";
        Identities2["NATIONAL"] = "national";
        Identities2["REGISTRATION"] = "registration";
        Identities2["PASSPORT"] = "passport";
        Identities2["DRIVER"] = "driver";
        Identities2["PET"] = "pet";
      })(Identities = Persons3.Identities || (Persons3.Identities = {}));
      let PermissionChannel;
      (function(PermissionChannel2) {
        PermissionChannel2["MOBILE"] = "mobile";
        PermissionChannel2["EMAIL"] = "email";
        PermissionChannel2["POSTAL"] = "postal";
        PermissionChannel2["SERVICE_TERMS"] = "serviceTerms";
        PermissionChannel2["PRIVACY_POLICY"] = "privacyPolicy";
      })(PermissionChannel = Persons3.PermissionChannel || (Persons3.PermissionChannel = {}));
      let PermissionStatus;
      (function(PermissionStatus2) {
        PermissionStatus2[PermissionStatus2["DO_NOT_DISTURB"] = -2] = "DO_NOT_DISTURB";
        PermissionStatus2[PermissionStatus2["OPTOUT"] = -1] = "OPTOUT";
        PermissionStatus2[PermissionStatus2["UNKNOWN"] = 0] = "UNKNOWN";
        PermissionStatus2[PermissionStatus2["OPTIN"] = 1] = "OPTIN";
      })(PermissionStatus = Persons3.PermissionStatus || (Persons3.PermissionStatus = {}));
      let Residency;
      (function(Residency2) {
        Residency2["CITIZEN"] = "citizen";
        Residency2["RESIDENT"] = "resident";
        Residency2["EMPLOYMENT"] = "employment";
      })(Residency = Persons3.Residency || (Persons3.Residency = {}));
      let BloodType;
      (function(BloodType2) {
        BloodType2["A_POSITIVE"] = "A+";
        BloodType2["A_NEGATIVE"] = "A-";
        BloodType2["B_POSITIVE"] = "B+";
        BloodType2["B_NEGATIVE"] = "B-";
        BloodType2["O_POSITIVE"] = "O+";
        BloodType2["O_NEGATIVE"] = "O-";
        BloodType2["AB_POSITIVE"] = "AB+";
        BloodType2["AB_NEGATIVE"] = "AB-";
      })(BloodType = Persons3.BloodType || (Persons3.BloodType = {}));
      let Species;
      (function(Species2) {
        Species2["DOG"] = "dog";
        Species2["CAT"] = "cat";
        Species2["BIRD"] = "bird";
        Species2["RABBIT"] = "rabbit";
        Species2["RODENT"] = "rodent";
      })(Species = Persons3.Species || (Persons3.Species = {}));
      Persons3.PROFILE = {
        FAMILY_GIVEN: NameOrder.FAMILY_GIVEN,
        GIVEN_FAMILY: NameOrder.GIVEN_FAMILY,
        NAME_ORDER: [NameOrder.FAMILY_GIVEN, NameOrder.GIVEN_FAMILY],
        MOBILE: contacts_1.Contacts.Type.MOBILE,
        BIRTH_DATE: contacts_1.Contacts.Dates.BIRTH
      };
    })(Persons2 || (exports.Persons = Persons2 = {}));
    var Socials;
    (function(Socials2) {
      let Relationship;
      (function(Relationship2) {
        Relationship2["FRIEND"] = "friend";
        Relationship2["SPOUSE"] = "spouse";
        Relationship2["PARENT"] = "parent";
        Relationship2["CHILD"] = "child";
        Relationship2["SIBLING"] = "sibling";
        Relationship2["COLLEAGUE"] = "colleague";
        Relationship2["CLASSMATE"] = "classmate";
        Relationship2["PET"] = "pet";
      })(Relationship = Socials2.Relationship || (Socials2.Relationship = {}));
      let InverseRelationship;
      (function(InverseRelationship2) {
        InverseRelationship2["parent"] = "child";
        InverseRelationship2["child"] = "parent";
        InverseRelationship2["pet"] = "owner";
      })(InverseRelationship = Socials2.InverseRelationship || (Socials2.InverseRelationship = {}));
    })(Socials || (exports.Socials = Socials = {}));
  }
});

// node_modules/@perkd/applet-common/dist/utils.js
var import_persons2 = __toESM(require_persons());

// node_modules/@perkd/applet-common/dist/types/persons.js
var import_persons = __toESM(require_persons(), 1);
var Persons;
(function(Persons2) {
  Persons2.DEFAULT_NAME_ORDER = import_persons.Persons.NameOrder.GIVEN_FAMILY;
})(Persons || (Persons = {}));

// node_modules/@perkd/applet-common/dist/types/cards.js
var Cards;
(function(Cards2) {
  let Barcode;
  (function(Barcode2) {
    Barcode2["QRCODE"] = "QRCODE";
    Barcode2["AZTEC"] = "AZTEC";
    Barcode2["DATAMATRIX"] = "DATAMATRIX";
    Barcode2["CODE128"] = "CODE128";
  })(Barcode = Cards2.Barcode || (Cards2.Barcode = {}));
  Cards2.DEFAULT_BARCODE_TYPE = Barcode.CODE128;
  Cards2.CODE_SQUARE = [Barcode.QRCODE, "QR_CODE", Barcode.AZTEC, Barcode.DATAMATRIX];
})(Cards || (Cards = {}));

// node_modules/@perkd/applet-common/dist/utils.js
var isUrl = (str) => {
  const exp = /[-a-zA-Z0-9@:%._\\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)?/gi;
  return !!str && !!str.match(new RegExp(exp));
};
var isEmail = (str) => {
  const exp = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return !!str && !!str.match(new RegExp(exp));
};
var isZero = (str) => {
  const numbers = str.match(/\d+/g);
  const sum = (numbers == null ? void 0 : numbers.map(Number).reduce((acc, num) => acc + num, 0)) || 0;
  return sum === 0;
};
var isObject = (item) => {
  return item && typeof item === "object" && !Array.isArray(item);
};
var mergeObject = (target, source) => {
  if (!isObject(target) || !isObject(source)) {
    return source === void 0 ? target : source;
  }
  const result = Array.isArray(target) ? [] : {};
  for (const key in target) {
    if (target.hasOwnProperty(key)) {
      result[key] = isObject(target[key]) ? mergeObject({}, target[key]) : target[key];
    }
  }
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (Array.isArray(source[key])) {
        result[key] = source[key] == void 0 ? target[key] : source[key];
      } else if (isObject(source[key])) {
        if (!(key in target)) {
          result[key] = source[key];
        } else {
          result[key] = mergeObject(target[key], source[key]);
        }
      } else {
        result[key] = source[key] === void 0 ? target[key] : source[key];
      }
    }
  }
  return result;
};
function generateCSV(arrData, name) {
  const contents = [
    Object.keys(arrData[0]).join(","),
    ...arrData.map((item) => Object.values(item).join(","))
  ].join("\n").replace(/(^\[)|(\]$)/gm, "");
  return {
    fileName: `${name}.csv`,
    contents
  };
}
function getErrorMessage(error, t) {
  const { statusCode, statusMessage = "", code, message = "" } = error || {};
  const errorKey = statusMessage || message;
  const translation = t(`error.${errorKey}`);
  const isTranslationValid = !!translation && translation !== `error.${errorKey}`;
  const title = isTranslationValid ? translation : t("error.message");
  const errorCode = `${statusCode || code || ""}`;
  const part1 = errorCode && errorCode !== message.trim() ? errorCode : "";
  const part2 = errorKey !== message || !isTranslationValid ? message.trim() : "";
  const description = `${part1} ${part2}`.trim();
  return `${title}${description ? ": " : ""}${description}`;
}
function getDisplayAs(familyName, givenName, nameOrder = Persons.DEFAULT_NAME_ORDER) {
  if (!(givenName && familyName))
    return "";
  if (nameOrder === import_persons2.Persons.NameOrder.GIVEN_FAMILY)
    return `${givenName} ${familyName}`;
  return `${familyName} ${givenName}`;
}
function getFormFields(form) {
  return (Object.keys(form.schema.properties) || []).map((key) => key);
}
function toProfile(person) {
  var _a;
  const { familyName, givenName, fullName, gender, name, phones, emails, birthDate } = person || {};
  const { year, month, day, date } = birthDate || {};
  const { countryCode = "", fullNumber = "", optIn: mobileOpt } = (phones == null ? void 0 : phones[0]) || {};
  const { address = "", optIn: emailOpt } = (emails == null ? void 0 : emails[0]) || {};
  return {
    familyName,
    givenName,
    nameOrder: name == null ? void 0 : name.order,
    fullName: fullName || getDisplayAs(familyName, givenName, name == null ? void 0 : name.order),
    gender,
    birthDate: date ? date : year && month && day ? `${year}-${month}-${day}` : month && day ? `${month}-${day}` : "",
    countryCode: (_a = phones == null ? void 0 : phones[0]) == null ? void 0 : _a.countryCode,
    formattedMobile: formatPhoneNumber(fullNumber.replace(countryCode, ""), countryCode),
    mobile: fullNumber,
    mobileOpt,
    email: address,
    emailOpt
  };
}
function toMembershipCard(data, programs, cardMaster) {
  var _a, _b, _c;
  const { membershipId, programId, tierLevel, cardNumber, startTime, endTime } = data || {};
  const program = programs.find((p) => p.programId === programId);
  const tier = program == null ? void 0 : program.tiers.find((tier2) => (tier2 == null ? void 0 : tier2.level) === tierLevel);
  return {
    name: (tier == null ? void 0 : tier.name) || "",
    image: (_b = (_a = tier == null ? void 0 : tier.card) == null ? void 0 : _a.image) == null ? void 0 : _b.original,
    cardNumber,
    startTime,
    endTime,
    barcodeType: ((_c = cardMaster == null ? void 0 : cardMaster.barcodeTypes) == null ? void 0 : _c[0]) || Cards.DEFAULT_BARCODE_TYPE,
    barcodePatterns: (cardMaster == null ? void 0 : cardMaster.barcodePatterns) || "",
    membershipId,
    programId,
    programName: (program == null ? void 0 : program.name) || "",
    tierLevel,
    type: (program == null ? void 0 : program.type) || ""
  };
}
function toQueryString(searchParams) {
  const parts = [];
  Object.keys(searchParams).forEach((key) => {
    const value = searchParams[key];
    if (value !== void 0) {
      if (typeof value === "object") {
        parts.push(`${key}=${encodeURIComponent(JSON.stringify(value))}`);
      } else {
        parts.push(`${key}=${encodeURIComponent(value)}`);
      }
    }
  });
  return parts.join("&");
}
function getTransLang(lang, provisions = Applets.LANGUAGES.SUPPORTED) {
  if (provisions.includes(lang)) {
    return lang;
  }
  const { FALLBACKS, DEFAULT } = Applets.LANGUAGES;
  if (Object.keys(FALLBACKS).includes(lang)) {
    const langFallbacks = FALLBACKS[lang] || FALLBACKS.default;
    return langFallbacks.find((l) => provisions.includes(l)) || DEFAULT;
  }
  return DEFAULT;
}
function formatAmount(amount, currency, minDigits, maxDigits, showFullrSign = false) {
  if (amount === void 0)
    return "";
  const CURRENCY = {
    CNY: { dollarSign: "¥", fullDollarSign: "CN$", decimal: 2 },
    HKD: { dollarSign: "$", fullDollarSign: "HK$", decimal: 2 },
    SGD: { dollarSign: "$", fullDollarSign: "S$", decimal: 2 },
    TWD: { dollarSign: "$", fullDollarSign: "NT$", decimal: 0 },
    others: { dollarSign: "$", fullDollarSign: "$", decimal: 2 }
  };
  const selected = CURRENCY[currency || "others"] || CURRENCY.others;
  const { fullDollarSign, dollarSign, decimal } = selected;
  return `${showFullrSign ? fullDollarSign : dollarSign}${amount.toLocaleString(void 0, {
    minimumFractionDigits: minDigits ?? decimal ?? 2,
    maximumFractionDigits: maxDigits ?? decimal ?? 2
  })}`;
}
function formatPhoneNumber(number, countryCode) {
  const cleanNumber = number.replace(/\D/g, "");
  let formatted = countryCode ? `+${countryCode} ` : "";
  const patterns = {
    "7": [3, 4],
    // +XX XXX XXXX
    "8": [4, 4],
    // +XX XXXX XXXX
    "9": [3, 3, 3],
    // +XX XXX XXX XXX
    "10": [3, 3, 4],
    // +XX XXX XXX XXXX
    "11": [3, 4, 4]
    // +XX XXX XXXX XXXX
  };
  const pattern = patterns[cleanNumber.length.toString()] || calculateGroups(cleanNumber.length);
  let position = 0;
  for (let i = 0; i < pattern.length; i++) {
    if (i > 0)
      formatted += " ";
    formatted += cleanNumber.substring(position, position + pattern[i]);
    position += pattern[i];
  }
  return formatted;
}
function calculateGroups(length) {
  const numGroups = Math.ceil(length / 4);
  const groups = [];
  const baseSize = Math.floor(length / numGroups);
  let remainder = length % numGroups;
  for (let i = 0; i < numGroups; i++) {
    const groupSize = i >= numGroups - remainder ? baseSize + 1 : baseSize;
    groups.push(groupSize);
  }
  groups.sort();
  return groups;
}
function getCookies() {
  const fields = (document.cookie || "").split(";");
  const params = {};
  for (var i = 0; i < fields.length; i++) {
    var pair = fields[i].split("=");
    if (!pair || pair.length < 2)
      continue;
    Object.assign(params, { [pair[0].trim()]: pair[1] });
  }
  return params;
}
function getUrlParameters(url = "") {
  const hasParams = url && url.split("?").length > 0;
  if (!hasParams)
    return {};
  const params = new URL(url).searchParams;
  return Object.fromEntries(params);
}
function detectLanguage(str) {
  if (typeof str !== "string")
    return "";
  if (str.match(/[\uac00-\ud7ff]|[\u1100-\u11FF]|[\u3130-\u318F]/g))
    return "ko";
  if (str.match(/[\uff66-\uff9d]|[\u3041-\u309f]|[\u30a0-\u30ff]|[\u31F0-\u31FF]/g))
    return "ja";
  if (str.match(/[\u4e00-\u9fa5]/g))
    return "zh-Hant";
  return "en";
}
function isErrorResponse(resp) {
  return !!resp && typeof resp === "object" && "error" in resp;
}
function debounce(fn, delay) {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      fn(...args);
    }, delay);
  };
}

export {
  Cards,
  isUrl,
  isEmail,
  isZero,
  isObject,
  mergeObject,
  generateCSV,
  getErrorMessage,
  getDisplayAs,
  getFormFields,
  toProfile,
  toMembershipCard,
  toQueryString,
  getTransLang,
  formatAmount,
  formatPhoneNumber,
  getCookies,
  getUrlParameters,
  detectLanguage,
  isErrorResponse,
  debounce
};
//# sourceMappingURL=chunk-QVDR62FV.js.map
