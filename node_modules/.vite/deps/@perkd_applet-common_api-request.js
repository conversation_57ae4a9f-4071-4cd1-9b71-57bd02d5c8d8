import {
  perkdRemote
} from "./chunk-B4GMYPGS.js";
import "./chunk-ZD7BQ6ST.js";
import {
  getCookies,
  getUrlParameters,
  toQueryString
} from "./chunk-QVDR62FV.js";
import "./chunk-6B6YBE7R.js";
import "./chunk-EQCVQC35.js";

// node_modules/@perkd/applet-common/dist/api-request.js
var Membership = {
  API: {
    "qualify": {
      "method": "post",
      "path": "membership/qualify"
    },
    "join": {
      "method": "post",
      "path": "membership/join"
    },
    "cancel": {
      "method": "post",
      "path": "membership/{membershipId}/cancel"
    },
    "renew": {
      "method": "post",
      "path": "membership/{membershipId}/renew"
    },
    "update": {
      "method": "post",
      "path": "membership/{membershipId}/update"
    },
    "terminate": {
      "method": "post",
      "path": "membership/{membershipId}/terminate"
    },
    "recent": {
      "method": "post",
      "path": "membership/recent"
    },
    "export": {
      "method": "post",
      "path": "membership/export"
    },
    "recruit": {
      "method": "get",
      "path": "membership/recruitment"
    }
  },
  qualify: (base, cardId, body) => {
    const { method, path } = Membership.API.qualify;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body
    });
  },
  join: (base, cardId, body) => {
    const { method, path } = Membership.API.join;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body
    });
  },
  cancel: (base, cardId, membershipId, reason) => {
    const { method, path } = Membership.API.cancel;
    return perkdRemote({
      method,
      endpoint: path.replace("{membershipId}", membershipId || ""),
      base,
      cardId,
      body: { reason }
    });
  },
  renew: (base, cardId, membershipId, endTime) => {
    const { method, path } = Membership.API.renew;
    return perkdRemote({
      method,
      endpoint: path.replace("{membershipId}", membershipId || ""),
      base,
      cardId,
      body: endTime ? { endTime } : {}
    });
  },
  update: (base, cardId, membershipId, endTime) => {
    const { method, path } = Membership.API.update;
    return perkdRemote({
      method,
      endpoint: path.replace("{membershipId}", membershipId || ""),
      base,
      cardId,
      body: { endTime }
    });
  },
  terminate: (base, cardId, membershipId, reason) => {
    const { method, path } = Membership.API.terminate;
    return perkdRemote({
      method,
      endpoint: path.replace("{membershipId}", membershipId || ""),
      base,
      cardId,
      body: { reason }
    });
  },
  recent: (base, cardId) => {
    const { method, path } = Membership.API.recent;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body: {}
    });
  },
  export: (base, cardId) => {
    const { method, path } = Membership.API.export;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body: {}
    });
  },
  recruitByDate: (base, cardId, filter) => {
    const { method, path } = Membership.API.recruit;
    return perkdRemote({
      method,
      endpoint: `${path}?${toQueryString(filter)}`,
      base,
      cardId
    });
  }
};
var Order = {
  API: {
    "qualify": {
      "method": "post",
      "path": "orders/qualify"
    },
    "create": {
      "method": "post",
      "path": "orders"
    },
    "cancel": {
      "method": "post",
      "path": "orders/{orderId}/cancel"
    },
    "recent": {
      "method": "post",
      "path": "orders/recent"
    },
    "getList": {
      "method": "get",
      "path": "orders/all"
    },
    "getPackedList": {
      "method": "get",
      "path": "orders/packed"
    },
    "issueInvoice": {
      "method": "post",
      "path": "orders/{orderId}/invoice/reissue"
    },
    "printReceipt": {
      "method": "post",
      "path": "orders/{orderId}/receipt"
    },
    "cancelUnfulfilled": {
      "method": "post",
      "path": "orders/{orderId}/cancel/unfulfilled"
    },
    "relocate": {
      "method": "post",
      "path": "orders/relocate"
    },
    "markCollected": {
      "method": "post",
      "path": "orders/{orderId}/collected"
    }
  },
  qualify: (base, cardId, body) => {
    const { method, path } = Order.API.qualify;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body
    });
  },
  create: (base, cardId, body) => {
    const { method, path } = Order.API.create;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body
    });
  },
  cancel: (base, cardId, orderId) => {
    const { method, path } = Order.API.cancel;
    return perkdRemote({
      method,
      endpoint: path.replace("{orderId}", orderId || ""),
      base,
      cardId,
      body: {}
    });
  },
  recent: (base, cardId) => {
    const { method, path } = Order.API.recent;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body: {}
    });
  },
  getList: (base, cardId) => {
    const { method, path } = Order.API.getList;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId
    });
  },
  getPackedList: (base, cardId) => {
    const { method, path } = Order.API.getPackedList;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId
    });
  },
  issueInvoice: (base, cardId, orderId, taxId) => {
    const { method, path } = Order.API.issueInvoice;
    return perkdRemote({
      method,
      endpoint: path.replace("{orderId}", orderId || ""),
      base,
      cardId,
      body: { taxId, print: true }
    });
  },
  printReceipt: (base, cardId, orderId) => {
    const { method, path } = Order.API.printReceipt;
    return perkdRemote({
      method,
      endpoint: path.replace("{orderId}", orderId || ""),
      base,
      cardId
    });
  },
  cancelUnfulfilled: (base, cardId, orderId, reason = "") => {
    const { method, path } = Order.API.cancelUnfulfilled;
    return perkdRemote({
      method,
      endpoint: path.replace("{orderId}", orderId || ""),
      base,
      cardId,
      body: { reason }
    });
  },
  relocate: (base, cardId, body) => {
    const { method, path } = Order.API.relocate;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body
    });
  },
  markCollected: (base, cardId, orderId) => {
    const { method, path } = Order.API.markCollected;
    return perkdRemote({
      method,
      endpoint: path.replace("{orderId}", orderId || ""),
      base,
      cardId,
      body: {}
    });
  }
};
var Sale = {
  API: {
    "getUnpaid": {
      "method": "get",
      "path": "sales/staff/orders/unpaid"
    },
    "getPaid": {
      "method": "get",
      "path": "sales/staff/orders/paid"
    },
    "markPaid": {
      "method": "post",
      "path": "sales/staff/orders/{orderId}/paid"
    }
  },
  getUnpaid: (base, cardId) => {
    const { method, path } = Sale.API.getUnpaid;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId
    });
  },
  getPaid: (base, cardId) => {
    const { method, path } = Sale.API.getPaid;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId
    });
  },
  markPaid: (base, cardId, orderId, body) => {
    const { method, path } = Sale.API.markPaid;
    return perkdRemote({
      method,
      endpoint: path.replace("{orderId}", orderId),
      base,
      cardId,
      body
    });
  }
};
var Person = {
  API: {
    "search": {
      "method": "get",
      "path": "person/search"
    }
  },
  search: (base, cardId, search) => {
    const { method, path } = Person.API.search;
    return perkdRemote({
      method,
      endpoint: `${path}?${toQueryString(search)}`,
      base,
      cardId
    });
  }
};
var Staff = {
  API: {
    "getList": {
      "method": "get",
      "path": "staff/profile"
    },
    "qrCodeLogin": {
      "method": "post",
      "path": "users/login/qr"
    }
  },
  getList: (base, cardId) => {
    const { method, path } = Staff.API.getList;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId
    });
  },
  qrCodeLogin: (base, cardId) => {
    const { method, path } = Staff.API.qrCodeLogin;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId
    });
  }
};
var Business = {
  API: {
    "provisionMerchant": {
      "method": "post",
      "path": "business/place/{placeId}/merchant"
    },
    "verifyMerchant": {
      "method": "get",
      "path": "business/place/{placeId}/merchant"
    },
    "provisionPrinter": {
      "method": "post",
      "path": "place/{placeId}/printer"
    },
    "detachPrinter": {
      "method": "get",
      "path": "{placeId}/printers/{printerId}/detach"
    },
    "deletePrinter": {
      "method": "get",
      "path": "{printerId}"
    }
  },
  provisionMerchant: (base, cardId, placeId, body) => {
    const { method, path } = Business.API.provisionMerchant;
    return perkdRemote({
      method,
      endpoint: path.replace("{placeId}", placeId || ""),
      base,
      cardId,
      body
    });
  },
  verifyMerchant: (base, cardId, placeId) => {
    const { method, path } = Business.API.verifyMerchant;
    return perkdRemote({
      method,
      endpoint: path.replace("{placeId}", placeId || ""),
      base,
      cardId
    });
  },
  provisionPrinter: (base, cardId, placeId, body) => {
    const { method, path } = Business.API.provisionPrinter;
    return perkdRemote({
      method,
      endpoint: path.replace("{placeId}", placeId || ""),
      base,
      cardId,
      body
    });
  },
  detachPrinter: (base, cardId, printerId) => {
    const { method, path } = Business.API.detachPrinter;
    return perkdRemote({
      method,
      endpoint: path.replace("{printerId}", printerId || ""),
      base,
      cardId
    });
  },
  deletePrinter: (base, cardId, printerId) => {
    const { method, path } = Business.API.deletePrinter;
    return perkdRemote({
      method,
      endpoint: path.replace("{printerId}", printerId || ""),
      base,
      cardId
    });
  }
};
var Microsite = {
  API: {
    "signup": {
      "method": "post",
      "path": "https://j6h5u62etg.execute-api.ap-southeast-1.amazonaws.com/v1/form"
    },
    "download": {
      "method": "get",
      "path": "https://kx1irzsdfe.execute-api.ap-southeast-1.amazonaws.com/live/download"
    },
    "provisionPlace": {
      "method": "get",
      "path": "https://a49eb792-1a3d-4936-b959-3d9bc0d6fbd5.mock.pstmn.io/place"
    }
  },
  signup: async (countryCode, phoneNumber, language, eventId) => {
    const href = window.location.href;
    const params = getUrlParameters(href);
    const cookies = getCookies();
    const body = {
      form: {
        countryCode,
        number: phoneNumber,
        locale: {
          languages: [language]
        }
      },
      event: eventId,
      context: {
        referer: document.referrer,
        userAgent: window.navigator.userAgent,
        site: href.substring(href.lastIndexOf("/") + 1),
        client_id: cookies["_ga"] && cookies["_ga"].slice(6),
        fbc: cookies["_fbc"],
        fbp: cookies["_fbp"],
        uuid: params.uuid,
        uid: params.uid,
        store: params.store || "",
        tags: params.tags || params.key || ""
      },
      params
    };
    try {
      const { path, method } = Microsite.API.signup;
      const request = new Request(path, {
        method,
        mode: "cors",
        body: JSON.stringify(body),
        headers: {
          "Content-Type": "application/json"
        }
      });
      const response = await fetch(request).then((response2) => response2.json());
      return response;
    } catch (error) {
      return { error };
    }
  },
  appDownload() {
    window.location.href = Microsite.API.download.path;
  }
};
var Event = {
  API: {
    "getList": {
      "method": "post",
      "path": "product/app/staff/events"
    }
  },
  getList: (base, cardId, body) => {
    const { method, path } = Event.API.getList;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body
    });
  }
};
var Product = {
  API: {
    "getList": {
      "method": "get",
      "path": "variant/app/products"
    },
    "setAvailability": {
      "method": "post",
      "path": "product/staff/products/{variantId}/availability"
    }
  },
  getList: (base, cardId, placeId, channel) => {
    const { method, path } = Product.API.getList;
    const param = {
      where: { "visible": true },
      soldAt: placeId
    };
    if (channel)
      Object.assign(param, { channel });
    return perkdRemote({
      method,
      endpoint: `${path}?${toQueryString(param)}`,
      base,
      cardId
    });
  },
  setAvailability: (base, cardId, variantId, status) => {
    const { method, path } = Product.API.setAvailability;
    return perkdRemote({
      method,
      endpoint: path.replace("{variantId}", variantId),
      base,
      cardId,
      body: {
        availability: status
      }
    });
  }
};
var Fulfillment = {
  API: {
    "getList": {
      "method": "get",
      "path": "fulfillments/pack"
    },
    "getCancelledList": {
      "method": "get",
      "path": "fulfillments/cancelled"
    },
    "fulfillItems": {
      "method": "post",
      "path": "fulfillments/{fulfillmentId}/fulfill/items"
    },
    "getDetail": {
      "method": "get",
      "path": "fulfillments/kitchen"
    },
    "startQueue": {
      "method": "post",
      "path": "fulfillments/{fulfillmentId}/queue"
    },
    "getPrepareList": {
      "method": "get",
      "path": "fulfillments/prepare"
    },
    "startPrepare": {
      "method": "post",
      "path": "fulfillments/{fulfillmentId}/prepare"
    },
    "cancelPrepare": {
      "method": "post",
      "path": "fulfillments/{fulfillmentId}/prepare/cancel"
    },
    "markPacked": {
      "method": "post",
      "path": "fulfillments/{fulfillmentId}/packed"
    }
  },
  getList: (base, cardId, filter) => {
    const { method, path } = Fulfillment.API.getList;
    return perkdRemote({
      method,
      endpoint: filter ? `${path}?${toQueryString(filter)}` : path,
      base,
      cardId
    });
  },
  getCancelledList: (base, cardId, filter) => {
    const { method, path } = Fulfillment.API.getCancelledList;
    return perkdRemote({
      method,
      endpoint: filter ? `${path}?${toQueryString(filter)}` : path,
      base,
      cardId
    });
  },
  fulfillItems: (base, cardId, fulfillmentId, items) => {
    const { method, path } = Fulfillment.API.fulfillItems;
    return perkdRemote({
      method,
      endpoint: path.replace("{fulfillmentId}", fulfillmentId),
      base,
      cardId,
      body: { id: fulfillmentId, items }
    });
  },
  getDetail: (base, cardId, fulfillmentId) => {
    const { method, path } = Fulfillment.API.getDetail;
    return perkdRemote({
      method,
      endpoint: `${path}?mainFulfillmentId=${fulfillmentId}`,
      base,
      cardId
    });
  },
  startQueue: (base, cardId, fulfillmentId) => {
    const { method, path } = Fulfillment.API.startQueue;
    return perkdRemote({
      method,
      endpoint: path.replace("{fulfillmentId}", fulfillmentId),
      base,
      cardId,
      body: {}
    });
  },
  getPrepareList: (base, cardId) => {
    const { method, path } = Fulfillment.API.getPrepareList;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId
    });
  },
  startPrepare: (base, cardId, fulfillmentId) => {
    const { method, path } = Fulfillment.API.startPrepare;
    return perkdRemote({
      method,
      endpoint: path.replace("{fulfillmentId}", fulfillmentId),
      base,
      cardId,
      body: {}
    });
  },
  cancelPrepare: (base, cardId, fulfillmentId) => {
    const { method, path } = Fulfillment.API.cancelPrepare;
    return perkdRemote({
      method,
      endpoint: path.replace("{fulfillmentId}", fulfillmentId),
      base,
      cardId,
      body: {}
    });
  },
  markPacked: (base, cardId, fulfillmentId) => {
    const { method, path } = Fulfillment.API.markPacked;
    return perkdRemote({
      method,
      endpoint: path.replace("{fulfillmentId}", fulfillmentId),
      base,
      cardId,
      body: {}
    });
  }
};
var Booking = {
  API: {
    "getVenues": {
      "method": "get",
      "path": "booking/resources"
    },
    "getAvailability": {
      "method": "post",
      "path": "booking/products/availability"
    },
    "getItems": {
      "method": "post",
      "path": "booking/products/items"
    }
  },
  getVenues: (base, cardId, masterId, tenantCode) => {
    const { method, path } = Booking.API.getVenues;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      headers: { "tenant-code": tenantCode },
      routeParams: { masterId, kind: "venue" }
    });
  },
  getAvailability: (base, cardId, masterId, tenantCode, body) => {
    const { method, path } = Booking.API.getAvailability;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      headers: { "tenant-code": tenantCode },
      routeParams: { masterId },
      body,
      options: {
        showTimeoutAlert: true
      }
    });
  },
  getItems: (base, cardId, masterId, tenantCode, body) => {
    const { method, path } = Booking.API.getItems;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      headers: { "tenant-code": tenantCode },
      routeParams: { masterId },
      body,
      options: {
        showTimeoutAlert: true
      }
    });
  }
};
var Offer = {
  API: {
    "getQualified": {
      "method": "post",
      "path": "offer/qualified"
    },
    "issue": {
      "method": "post",
      "path": "offer/issue"
    }
  },
  getQualified: (base, cardId, body) => {
    const { method, path } = Offer.API.getQualified;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body
    });
  },
  issue: (base, cardId, body) => {
    const { method, path } = Offer.API.issue;
    return perkdRemote({
      method,
      endpoint: path,
      base,
      cardId,
      body
    });
  }
};
var Place = {
  API: {
    "updateHours": {
      "method": "put",
      "path": "places/{placeId}/hours"
    }
  },
  updateHours: (base, cardId, placeId, body) => {
    const { method, path } = Place.API.updateHours;
    return perkdRemote({
      method,
      endpoint: path.replace("{placeId}", placeId || ""),
      base,
      cardId,
      body
    });
  }
};
export {
  Booking,
  Business,
  Event,
  Fulfillment,
  Membership,
  Microsite,
  Offer,
  Order,
  Person,
  Place,
  Product,
  Sale,
  Staff
};
//# sourceMappingURL=@perkd_applet-common_api-request.js.map
