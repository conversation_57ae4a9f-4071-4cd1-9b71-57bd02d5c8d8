{"version": 3, "sources": ["../../@perkd/applet-common/dist/setup.js"], "sourcesContent": ["import { Applets } from \"./types/applets.js\";\nimport { mergeObject } from \"./utils.js\";\nimport { hexToRgbo, getContrastColor } from \"./utilsColor.js\";\nimport commonTranslate from './i18n.json';\nexport const setupEnvironment = (constants) => {\n    const { DEVICE, CARD, COUNTRY, CARDMASTER, CONTENT, PERSON, FONTCSS, FONTPATH, LANGUAGE, CONTEXT } = constants || {};\n    return {\n        DEVICE,\n        CARD,\n        CARDMASTER,\n        COUNTRY,\n        LANGUAGE,\n        CONTENT,\n        FONTCSS,\n        FONTPATH,\n        CONTEXT,\n        PERSON,\n        deviceScheme: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? Applets.ColorScheme.DARK : Applets.ColorScheme.LIGHT,\n    };\n};\nexport const setupWidget = (data, appletName) => {\n    const { CARD, CARDMASTER } = data || {};\n    const WIDGET_INSTANCE = CARD?.widgets?.find((w) => w.key === appletName) || {};\n    const WIDGET_MASTER = CARDMASTER?.widgets?.find((w) => w.key === appletName) || {};\n    return {\n        API_BASE: WIDGET_INSTANCE?.param?.api?.baseUrl || WIDGET_MASTER?.param?.api?.baseUrl,\n        API: WIDGET_INSTANCE?.param?.api || WIDGET_MASTER?.param?.api,\n        COLOR_SCHEME: WIDGET_INSTANCE?.param?.colorScheme || WIDGET_MASTER?.param?.colorScheme,\n        SETTINGS: WIDGET_INSTANCE?.param?.settings || WIDGET_MASTER?.param?.settings,\n        MASTER_SETTINGS: WIDGET_MASTER?.param?.settings,\n        MEMBERSHIP_PROGRAMS: WIDGET_INSTANCE?.param?.settings?.programs || WIDGET_MASTER?.param?.settings?.programs\n    };\n};\nexport const setupFont = (data, embeddedFont) => {\n    if (!embeddedFont)\n        return;\n    const styleElement = document.createElement('style');\n    const headElement = document.head || document.getElementsByTagName('head')[0];\n    styleElement.textContent = `\n        @font-face {\n            font-family: Melbourne;\n            font-weight: normal;\n            src: local('Melbourne'), url('${data.FONTPATH}Melbourne.otf') format('opentype');\n        }\n\n        @font-face {\n            font-family: Melbourne;\n            font-weight: bold;\n            src: local('Melbourne_bold'), url('${data.FONTPATH}Melbourne_bold.otf') format('opentype');\n        }\n\n        @font-face {\n            font-family: picon;\n            font-weight: normal;\n            src: local('picon'), url('${data.FONTPATH}picon.ttf') format('truetype');\n        }\n        \n        ${data.FONTCSS}\n    `;\n    headElement.appendChild(styleElement);\n};\nexport const setupTheme = (data, appletName) => {\n    // setup css variables \n    const { DEVICE, CARDMASTER, deviceScheme = Applets.ColorScheme.LIGHT } = data;\n    const { WIDTH, HEIGHT, MIN_TOP_SPACE, STATUS_BAR_HEIGHT, NAV_BAR_HEIGHT, MIN_BOTTOM_SPACE, BOTTOM_TABS_HEIGHT, windowHeight, IOS, IS_LONG_SCREEN, APP } = DEVICE || {};\n    const { VERSION } = APP || {};\n    const CSS_ROOT = {\n        '--width-screen': WIDTH + 'px',\n        '--height-screen': HEIGHT + 'px',\n        '--height-window': windowHeight + 'px',\n        '--height-minTop': (MIN_TOP_SPACE || 20) + 'px',\n        '--height-minBottom': (MIN_BOTTOM_SPACE || 20) + 'px',\n        '--height-statusBar': (IOS ? STATUS_BAR_HEIGHT : 0) + 'px',\n        '--height-navigationBar': (NAV_BAR_HEIGHT + (IOS ? STATUS_BAR_HEIGHT : 0)) + 'px',\n        '--height-tabBar': BOTTOM_TABS_HEIGHT + 'px',\n        '--device-ios': IOS,\n        '--device-longScreen': IS_LONG_SCREEN,\n        '--app-version': VERSION,\n        '--font-size-base': Math.round(((WIDTH / 320 - 1) * 0.8 + 1) * 10) + 'px',\n        '--size-base': `${Math.round(((WIDTH / 320 - 1) * 0.8 + 1) * 10)}`\n    };\n    Object.keys(CSS_ROOT).forEach((key) => {\n        document.documentElement.style.setProperty(key, CSS_ROOT ? CSS_ROOT[key] : '');\n    });\n    // setup theme\n    const WIDGET_MASTER = appletName ? (CARDMASTER?.widgets?.find((w) => w.key === appletName) || {}) : {};\n    const { colorScheme } = WIDGET_MASTER?.param || {};\n    const { brand, theme = colorScheme || deviceScheme } = CARDMASTER || {};\n    const { light, dark } = WIDGET_MASTER?.param?.theme || brand?.style || {};\n    const COLOR_SCHEME = {\n        light: Object.assign({}, Applets.DefaultTheme.light, light),\n        dark: Object.assign({}, Applets.DefaultTheme.dark, dark),\n    };\n    const colorKeys = Object.keys(COLOR_SCHEME[theme]);\n    colorKeys.forEach((key) => {\n        const color = COLOR_SCHEME[theme][key];\n        if (color) {\n            const { r, g, b, o } = hexToRgbo(color);\n            const contrast = getContrastColor(color);\n            document.documentElement.style.setProperty(`--color-brand-${key}`, `rgb(${r},${g},${b},${o})`);\n            document.documentElement.style.setProperty(`--color-brand-${key}-contrast`, contrast);\n        }\n    });\n    document.documentElement.setAttribute('theme', theme);\n};\nexport const setupLanguage = async (data, locales, dayjs) => {\n    const { ENGLISH } = Applets.Language;\n    const { LANGUAGE = ENGLISH } = data;\n    document.documentElement.lang = LANGUAGE;\n    if (dayjs && locales && dayjs.locale) {\n        const localLanguage = locales[LANGUAGE] || locales[ENGLISH];\n        dayjs.locale(localLanguage);\n    }\n};\nexport const setupI18n = (translations) => mergeObject(commonTranslate, translations);\n"], "mappings": ";;;;;;;;;;;;;;;;AAIO,IAAM,mBAAmB,CAAC,cAAc;AAC3C,QAAM,EAAE,QAAQ,MAAM,SAAS,YAAY,SAAS,QAAQ,SAAS,UAAU,UAAU,QAAQ,IAAI,aAAa,CAAC;AACnH,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,OAAO,cAAc,OAAO,WAAW,8BAA8B,EAAE,UAAU,QAAQ,YAAY,OAAO,QAAQ,YAAY;AAAA,EAClJ;AACJ;AACO,IAAM,cAAc,CAAC,MAAM,eAAe;AApBjD;AAqBI,QAAM,EAAE,MAAM,WAAW,IAAI,QAAQ,CAAC;AACtC,QAAM,oBAAkB,kCAAM,YAAN,mBAAe,KAAK,CAAC,MAAM,EAAE,QAAQ,gBAAe,CAAC;AAC7E,QAAM,kBAAgB,8CAAY,YAAZ,mBAAqB,KAAK,CAAC,MAAM,EAAE,QAAQ,gBAAe,CAAC;AACjF,SAAO;AAAA,IACH,YAAU,8DAAiB,UAAjB,mBAAwB,QAAxB,mBAA6B,cAAW,0DAAe,UAAf,mBAAsB,QAAtB,mBAA2B;AAAA,IAC7E,OAAK,wDAAiB,UAAjB,mBAAwB,UAAO,oDAAe,UAAf,mBAAsB;AAAA,IAC1D,gBAAc,wDAAiB,UAAjB,mBAAwB,kBAAe,oDAAe,UAAf,mBAAsB;AAAA,IAC3E,YAAU,wDAAiB,UAAjB,mBAAwB,eAAY,oDAAe,UAAf,mBAAsB;AAAA,IACpE,kBAAiB,oDAAe,UAAf,mBAAsB;AAAA,IACvC,uBAAqB,8DAAiB,UAAjB,mBAAwB,aAAxB,mBAAkC,eAAY,0DAAe,UAAf,mBAAsB,aAAtB,mBAAgC;AAAA,EACvG;AACJ;AACO,IAAM,YAAY,CAAC,MAAM,iBAAiB;AAC7C,MAAI,CAAC;AACD;AACJ,QAAM,eAAe,SAAS,cAAc,OAAO;AACnD,QAAM,cAAc,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AAC5E,eAAa,cAAc;AAAA;AAAA;AAAA;AAAA,4CAIa,KAAK,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iDAMR,KAAK,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wCAMtB,KAAK,QAAQ;AAAA;AAAA;AAAA,UAG3C,KAAK,OAAO;AAAA;AAElB,cAAY,YAAY,YAAY;AACxC;AACO,IAAM,aAAa,CAAC,MAAM,eAAe;AA7DhD;AA+DI,QAAM,EAAE,QAAQ,YAAY,eAAe,QAAQ,YAAY,MAAM,IAAI;AACzE,QAAM,EAAE,OAAO,QAAQ,eAAe,mBAAmB,gBAAgB,kBAAkB,oBAAoB,cAAc,KAAK,gBAAgB,IAAI,IAAI,UAAU,CAAC;AACrK,QAAM,EAAE,QAAQ,IAAI,OAAO,CAAC;AAC5B,QAAM,WAAW;AAAA,IACb,kBAAkB,QAAQ;AAAA,IAC1B,mBAAmB,SAAS;AAAA,IAC5B,mBAAmB,eAAe;AAAA,IAClC,oBAAoB,iBAAiB,MAAM;AAAA,IAC3C,uBAAuB,oBAAoB,MAAM;AAAA,IACjD,uBAAuB,MAAM,oBAAoB,KAAK;AAAA,IACtD,0BAA2B,kBAAkB,MAAM,oBAAoB,KAAM;AAAA,IAC7E,mBAAmB,qBAAqB;AAAA,IACxC,gBAAgB;AAAA,IAChB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB,KAAK,QAAQ,QAAQ,MAAM,KAAK,MAAM,KAAK,EAAE,IAAI;AAAA,IACrE,eAAe,GAAG,KAAK,QAAQ,QAAQ,MAAM,KAAK,MAAM,KAAK,EAAE,CAAC;AAAA,EACpE;AACA,SAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AACnC,aAAS,gBAAgB,MAAM,YAAY,KAAK,WAAW,SAAS,GAAG,IAAI,EAAE;AAAA,EACjF,CAAC;AAED,QAAM,gBAAgB,eAAc,8CAAY,YAAZ,mBAAqB,KAAK,CAAC,MAAM,EAAE,QAAQ,gBAAe,CAAC,IAAK,CAAC;AACrG,QAAM,EAAE,YAAY,KAAI,+CAAe,UAAS,CAAC;AACjD,QAAM,EAAE,OAAO,QAAQ,eAAe,aAAa,IAAI,cAAc,CAAC;AACtE,QAAM,EAAE,OAAO,KAAK,MAAI,oDAAe,UAAf,mBAAsB,WAAS,+BAAO,UAAS,CAAC;AACxE,QAAM,eAAe;AAAA,IACjB,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,aAAa,OAAO,KAAK;AAAA,IAC1D,MAAM,OAAO,OAAO,CAAC,GAAG,QAAQ,aAAa,MAAM,IAAI;AAAA,EAC3D;AACA,QAAM,YAAY,OAAO,KAAK,aAAa,KAAK,CAAC;AACjD,YAAU,QAAQ,CAAC,QAAQ;AACvB,UAAM,QAAQ,aAAa,KAAK,EAAE,GAAG;AACrC,QAAI,OAAO;AACP,YAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,UAAU,KAAK;AACtC,YAAM,WAAW,iBAAiB,KAAK;AACvC,eAAS,gBAAgB,MAAM,YAAY,iBAAiB,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAC7F,eAAS,gBAAgB,MAAM,YAAY,iBAAiB,GAAG,aAAa,QAAQ;AAAA,IACxF;AAAA,EACJ,CAAC;AACD,WAAS,gBAAgB,aAAa,SAAS,KAAK;AACxD;AACO,IAAM,gBAAgB,OAAO,MAAM,SAAS,UAAU;AACzD,QAAM,EAAE,QAAQ,IAAI,QAAQ;AAC5B,QAAM,EAAE,WAAW,QAAQ,IAAI;AAC/B,WAAS,gBAAgB,OAAO;AAChC,MAAI,SAAS,WAAW,MAAM,QAAQ;AAClC,UAAM,gBAAgB,QAAQ,QAAQ,KAAK,QAAQ,OAAO;AAC1D,UAAM,OAAO,aAAa;AAAA,EAC9B;AACJ;AACO,IAAM,YAAY,CAAC,iBAAiB,YAAY,cAAiB,YAAY;", "names": []}