import {
  I
} from "./chunk-HZAHFGFP.js";
import {
  Q,
  h
} from "./chunk-54FI6OKY.js";
import {
  Fragment,
  Transition,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createVNode,
  defineComponent,
  mergeProps,
  normalizeClass,
  normalizeProps,
  onBeforeUnmount,
  onMounted,
  openBlock,
  ref,
  renderSlot,
  toRefs,
  unref,
  useSlots,
  withCtx
} from "./chunk-U3LI7FBV.js";

// node_modules/@perkd/vue-components/dist/components/UIScreen.js
var le = {
  key: 0,
  theme: "light",
  class: "notify-container"
};
var ae = {
  key: 0,
  class: "content"
};
var ie = {
  key: 0,
  class: "screen overlay"
};
var re = {
  key: 0,
  class: "screen overlay"
};
var ve = defineComponent({
  __name: "UIScreen",
  props: {
    title: {
      type: String,
      default: ""
    },
    titleClass: {
      type: String,
      default: ""
    },
    navigationBarTheme: String,
    disableNavBack: {
      type: Boolean,
      default: false
    },
    isOnline: {
      type: Boolean,
      default: true
    },
    status: {
      type: String,
      default: ""
    },
    showClose: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    onContentScroll: {
      type: Function
    }
  },
  emits: ["goPreviousPage", "closeWindow"],
  setup(N, { expose: R, emit: E }) {
    const s = useSlots(), c = N, { title: h2, titleClass: L, navigationBarTheme: y, isOnline: O, status: F, showClose: g, loading: I2 } = toRefs(c), H = window.innerHeight / 3, V = window.innerHeight / 2, w = ref(0), l = ref(void 0), a = ref(void 0);
    let m;
    const $ = E, k = computed(() => {
      var e;
      return (e = window.history.state) != null && e.back && !c.disableNavBack ? { type: "back", onClick: () => $("goPreviousPage") } : void 0;
    }), W = computed(() => ({
      title: h2.value,
      titleClass: L.value,
      theme: y == null ? void 0 : y.value,
      isOnline: O.value,
      status: F.value,
      navBack: k.value
    })), j = computed(() => {
      const e = [];
      return (s.navigationBar || h2.value || k.value || g.value) && e.push("with-navigation-bar"), s.tabBar && e.push("with-tab-bar"), e;
    });
    onMounted(() => {
      var e;
      c.onContentScroll && ((e = l.value) == null || e.addEventListener("scroll", S));
    }), onBeforeUnmount(() => {
      var e;
      c.onContentScroll && ((e = l.value) == null || e.removeEventListener("scroll", S));
    });
    function S(e) {
      c.onContentScroll && c.onContentScroll(e);
    }
    function z(e, o) {
      m && (clearTimeout(m), m = void 0), o ? U(e) : M();
    }
    function U(e) {
      const o = e.target;
      w.value = o.getBoundingClientRect().top;
      const n = w.value - H;
      a.value && (a.value.classList.remove("close"), a.value.style.height = V + "px"), setTimeout(() => {
        var d;
        (d = l.value) == null || d.scrollBy({ top: n, behavior: "smooth" });
      }, 0);
    }
    function M() {
      m = setTimeout(() => {
        a.value && a.value.classList.add("close");
      }, 500);
    }
    function q(e, o) {
      var d;
      const n = { behavior: "smooth" };
      e !== void 0 && Object.assign(n, { top: e }), o !== void 0 && Object.assign(n, { left: o }), (d = l.value) == null || d.scrollBy(n);
    }
    function A(e) {
      var o;
      (o = l.value) == null || o.scrollTo({ top: e || 0, behavior: "smooth" });
    }
    function D(e) {
      var o;
      (o = l.value) == null || o.scrollTo({ left: e || 0, behavior: "smooth" });
    }
    function G() {
      $("closeWindow");
    }
    return R({
      scrollBy: q,
      scrollToTop: A,
      scrollToLeft: D
    }), (e, o) => {
      var n;
      return openBlock(), createElementBlock(Fragment, null, [
        createBaseVNode("div", mergeProps({
          class: ["screen", ...j.value]
        }, e.$attrs), [
          renderSlot(e.$slots, "navigationBar", {}, () => [
            unref(h2) || k.value || unref(g) ? (openBlock(), createBlock(I, normalizeProps(mergeProps({ key: 0 }, W.value)), createSlots({ _: 2 }, [
              unref(g) ? {
                name: "rightContent",
                fn: withCtx(() => [
                  createVNode(Q, {
                    type: "circle",
                    icon: { name: "close" },
                    onClick: G
                  })
                ]),
                key: "0"
              } : void 0
            ]), 1040)) : createCommentVNode("", true)
          ]),
          createBaseVNode("div", {
            ref_key: "screenContentRef",
            ref: l,
            class: normalizeClass(`screen-content ${unref(s).footer ? "screen-content-with-footer" : ""}`)
          }, [
            createVNode(Transition, { name: "swipe-down" }, {
              default: withCtx(() => [
                unref(s).notify ? (openBlock(), createElementBlock("div", le, [
                  renderSlot(e.$slots, "notify")
                ])) : createCommentVNode("", true)
              ]),
              _: 3
            }),
            unref(s).content ? (openBlock(), createElementBlock("div", ae, [
              renderSlot(e.$slots, "content", { focusChange: z })
            ])) : createCommentVNode("", true),
            unref(s).footer ? (openBlock(), createElementBlock("div", {
              key: 1,
              class: normalizeClass(`footer ${(n = a.value) != null && n.style.height ? "footer-before-keyboard" : ""}`)
            }, [
              renderSlot(e.$slots, "footer")
            ], 2)) : createCommentVNode("", true),
            createBaseVNode("div", {
              ref_key: "keyboardRef",
              ref: a,
              class: "screen-keyboard"
            }, null, 512)
          ], 2),
          renderSlot(e.$slots, "tabBar")
        ], 16),
        createVNode(Transition, { name: "fade" }, {
          default: withCtx(() => [
            unref(I2) ? (openBlock(), createElementBlock("div", ie, [
              renderSlot(e.$slots, "loading", {}, () => [
                createVNode(h)
              ])
            ])) : createCommentVNode("", true)
          ]),
          _: 3
        }),
        createVNode(Transition, { name: "fade" }, {
          default: withCtx(() => [
            unref(s).dialog ? (openBlock(), createElementBlock("div", re, [
              renderSlot(e.$slots, "dialog")
            ])) : createCommentVNode("", true)
          ]),
          _: 3
        })
      ], 64);
    };
  }
});

export {
  ve
};
//# sourceMappingURL=chunk-S4BG457E.js.map
