{"version": 3, "sources": ["../../@perkd/crm-types/src/contacts.ts", "../../@perkd/crm-types/src/persons.ts", "../../@perkd/applet-common/dist/utils.js", "../../@perkd/applet-common/dist/types/persons.js", "../../@perkd/applet-common/dist/types/cards.js"], "sourcesContent": [null, null, "import { Persons as Crm<PERSON>ersons } from '@perkd/crm-types/dist/persons.js';\nimport { Persons } from './types/persons.js';\nimport { Cards } from './types/cards.js';\nimport { Applets } from './types/applets.js';\nexport const isUrl = (str) => {\n    const exp = /[-a-zA-Z0-9@:%._\\\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\\\+.~#?&//=]*)?/gi;\n    return !!str && !!str.match(new RegExp(exp));\n};\nexport const isEmail = (str) => {\n    const exp = /^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\n    return !!str && !!str.match(new RegExp(exp));\n};\nexport const isZero = (str) => {\n    const numbers = str.match(/\\d+/g);\n    const sum = numbers?.map(Number).reduce((acc, num) => acc + num, 0) || 0;\n    return sum === 0;\n};\nexport const isObject = (item) => {\n    return item && typeof item === 'object' && !Array.isArray(item);\n};\nexport const mergeObject = (target, source) => {\n    if (!isObject(target) || !isObject(source)) {\n        return source === undefined ? target : source;\n    }\n    const result = Array.isArray(target) ? [] : {};\n    // Ensure we copy all properties from the target\n    for (const key in target) {\n        if (target.hasOwnProperty(key)) {\n            result[key] = isObject(target[key]) ? mergeObject({}, target[key]) : target[key];\n        }\n    }\n    for (const key in source) {\n        if (source.hasOwnProperty(key)) {\n            if (Array.isArray(source[key])) {\n                // Replace arrays, do not merge\n                result[key] = source[key] == undefined ? target[key] : source[key];\n            }\n            else if (isObject(source[key])) {\n                if (!(key in target)) {\n                    result[key] = source[key];\n                }\n                else {\n                    result[key] = mergeObject(target[key], source[key]);\n                }\n            }\n            else {\n                result[key] = source[key] === undefined ? target[key] : source[key];\n            }\n        }\n    }\n    return result;\n};\nexport function generateCSV(arrData, name) {\n    const contents = [\n        Object.keys(arrData[0]).join(\",\"),\n        ...arrData.map((item) => Object.values(item).join(\",\"))\n    ]\n        .join(\"\\n\")\n        .replace(/(^\\[)|(\\]$)/gm, \"\");\n    return {\n        fileName: `${name}.csv`,\n        contents,\n    };\n}\nexport function getErrorMessage(error, t) {\n    const { statusCode, statusMessage = '', code, message = '' } = error || {};\n    const errorKey = statusMessage || message;\n    const translation = t(`error.${errorKey}`);\n    const isTranslationValid = !!translation && translation !== `error.${errorKey}`;\n    const title = isTranslationValid ? translation : t('error.message');\n    const errorCode = `${statusCode || code || ''}`;\n    const part1 = errorCode && errorCode !== message.trim() ? errorCode : '';\n    const part2 = (errorKey !== message || !isTranslationValid) ? message.trim() : '';\n    const description = `${part1} ${part2}`.trim();\n    return `${title}${description ? ': ' : ''}${description}`;\n}\nexport function getDisplayAs(familyName, givenName, nameOrder = Persons.DEFAULT_NAME_ORDER) {\n    if (!(givenName && familyName))\n        return '';\n    if (nameOrder === CrmPersons.NameOrder.GIVEN_FAMILY)\n        return `${givenName} ${familyName}`;\n    return `${familyName} ${givenName}`;\n}\nexport function getFormFields(form) {\n    return (Object.keys(form.schema.properties) || []).map((key) => key);\n}\nexport function toProfile(person) {\n    const { familyName, givenName, fullName, gender, name, phones, emails, birthDate } = person || {};\n    const { year, month, day, date } = birthDate || {};\n    const { countryCode = '', fullNumber = '', optIn: mobileOpt } = phones?.[0] || {};\n    const { address = '', optIn: emailOpt } = emails?.[0] || {};\n    return {\n        familyName,\n        givenName,\n        nameOrder: name?.order,\n        fullName: fullName || getDisplayAs(familyName, givenName, name?.order),\n        gender,\n        birthDate: date ? date : (year && month && day ? `${year}-${month}-${day}` : (month && day ? `${month}-${day}` : '')),\n        countryCode: phones?.[0]?.countryCode,\n        formattedMobile: formatPhoneNumber(fullNumber.replace(countryCode, ''), countryCode),\n        mobile: fullNumber,\n        mobileOpt,\n        email: address,\n        emailOpt\n    };\n}\nexport function toMembershipCard(data, programs, cardMaster) {\n    const { membershipId, programId, tierLevel, cardNumber, startTime, endTime } = data || {};\n    const program = programs.find((p) => p.programId === programId);\n    const tier = program?.tiers.find((tier) => tier?.level === tierLevel);\n    return {\n        name: tier?.name || '',\n        image: tier?.card?.image?.original,\n        cardNumber,\n        startTime,\n        endTime,\n        barcodeType: cardMaster?.barcodeTypes?.[0] || Cards.DEFAULT_BARCODE_TYPE,\n        barcodePatterns: cardMaster?.barcodePatterns || '',\n        membershipId,\n        programId,\n        programName: program?.name || '',\n        tierLevel,\n        type: program?.type || '',\n    };\n}\nexport function toQueryString(searchParams) {\n    const parts = [];\n    Object.keys(searchParams).forEach(key => {\n        const value = searchParams[key];\n        if (value !== undefined) {\n            if (typeof value === 'object') {\n                parts.push(`${key}=${encodeURIComponent(JSON.stringify(value))}`);\n            }\n            else {\n                parts.push(`${key}=${encodeURIComponent(value)}`);\n            }\n        }\n    });\n    return parts.join('&');\n}\nexport function getTransLang(lang, provisions = Applets.LANGUAGES.SUPPORTED) {\n    if (provisions.includes(lang)) {\n        return lang;\n    }\n    const { FALLBACKS, DEFAULT } = Applets.LANGUAGES;\n    if (Object.keys(FALLBACKS).includes(lang)) {\n        const langFallbacks = FALLBACKS[lang] || FALLBACKS.default;\n        return langFallbacks.find((l) => provisions.includes(l)) || DEFAULT;\n    }\n    return DEFAULT;\n}\nexport function formatAmount(amount, currency, minDigits, maxDigits, showFullrSign = false) {\n    if (amount === undefined)\n        return '';\n    const CURRENCY = {\n        CNY: { dollarSign: '¥', fullDollarSign: 'CN$', decimal: 2 },\n        HKD: { dollarSign: '$', fullDollarSign: 'HK$', decimal: 2 },\n        SGD: { dollarSign: '$', fullDollarSign: 'S$', decimal: 2 },\n        TWD: { dollarSign: '$', fullDollarSign: 'NT$', decimal: 0 },\n        others: { dollarSign: '$', fullDollarSign: '$', decimal: 2 },\n    };\n    const selected = CURRENCY[currency || 'others'] || CURRENCY.others;\n    const { fullDollarSign, dollarSign, decimal } = selected;\n    return `${showFullrSign ? fullDollarSign : dollarSign}${amount.toLocaleString(undefined, {\n        minimumFractionDigits: minDigits ?? decimal ?? 2,\n        maximumFractionDigits: maxDigits ?? decimal ?? 2,\n    })}`;\n}\nexport function formatPhoneNumber(number, countryCode) {\n    const cleanNumber = number.replace(/\\D/g, '');\n    let formatted = countryCode ? `+${countryCode} ` : '';\n    // Calculate optimal grouping\n    // max part 4, mini part 2, even distribution every part\n    // put the shortest part at the beginning\n    // Common formatting patterns by length\n    const patterns = {\n        '7': [3, 4], // +XX XXX XXXX\n        '8': [4, 4], // +XX XXXX XXXX\n        '9': [3, 3, 3], // +XX XXX XXX XXX\n        '10': [3, 3, 4], // +XX XXX XXX XXXX\n        '11': [3, 4, 4] // +XX XXX XXXX XXXX\n    };\n    const pattern = patterns[cleanNumber.length.toString()] || calculateGroups(cleanNumber.length);\n    // Apply the grouping\n    let position = 0;\n    for (let i = 0; i < pattern.length; i++) {\n        if (i > 0)\n            formatted += \" \";\n        formatted += cleanNumber.substring(position, position + pattern[i]);\n        position += pattern[i];\n    }\n    return formatted;\n}\nfunction calculateGroups(length) {\n    // For longer numbers, calculate optimal grouping\n    const numGroups = Math.ceil(length / 4);\n    const groups = [];\n    // Calculate base group size\n    const baseSize = Math.floor(length / numGroups);\n    let remainder = length % numGroups;\n    // Create groups, filling from end to start\n    for (let i = 0; i < numGroups; i++) {\n        // Assign extra digit to later groups first when remainder exists\n        const groupSize = (i >= numGroups - remainder) ? baseSize + 1 : baseSize;\n        groups.push(groupSize);\n    }\n    // Ensure shorter parts are at the beginning\n    groups.sort();\n    return groups;\n}\nexport function getCookies() {\n    const fields = (document.cookie || '').split(';');\n    const params = {};\n    for (var i = 0; i < fields.length; i++) {\n        var pair = fields[i].split('=');\n        if (!pair || pair.length < 2)\n            continue;\n        Object.assign(params, { [pair[0].trim()]: pair[1] });\n    }\n    return params;\n}\nexport function getUrlParameters(url = '') {\n    const hasParams = url && url.split('?').length > 0;\n    if (!hasParams)\n        return {};\n    const params = new URL(url).searchParams;\n    return Object.fromEntries(params);\n}\nexport function detectLanguage(str) {\n    if (typeof str !== 'string')\n        return '';\n    if (str.match(/[\\uac00-\\ud7ff]|[\\u1100-\\u11FF]|[\\u3130-\\u318F]/g))\n        return 'ko';\n    if (str.match(/[\\uff66-\\uff9d]|[\\u3041-\\u309f]|[\\u30a0-\\u30ff]|[\\u31F0-\\u31FF]/g))\n        return 'ja';\n    if (str.match(/[\\u4e00-\\u9fa5]/g))\n        return 'zh-Hant';\n    return 'en';\n}\nexport function isErrorResponse(resp) {\n    return (!!resp && typeof resp === 'object' && 'error' in resp);\n}\nexport function debounce(fn, delay) {\n    let timeoutId;\n    return (...args) => {\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(() => {\n            fn(...args);\n        }, delay);\n    };\n}\n", "import { Persons as CrmPersons } from '@perkd/crm-types/dist/persons.js';\nexport var Persons;\n(function (Persons) {\n    Persons.DEFAULT_NAME_ORDER = CrmPersons.NameOrder.GIVEN_FAMILY;\n})(Persons || (Persons = {}));\n", "export var Cards;\n(function (Cards) {\n    let Barcode;\n    (function (Barcode) {\n        Barcode[\"QRCODE\"] = \"QRCODE\";\n        Barcode[\"AZTEC\"] = \"AZTEC\";\n        Barcode[\"DATAMATRIX\"] = \"DATAMATRIX\";\n        Barcode[\"CODE128\"] = \"CODE128\";\n    })(Barcode = Cards.Barcode || (Cards.Barcode = {}));\n    Cards.DEFAULT_BARCODE_TYPE = Barcode.CODE128;\n    Cards.CODE_SQUARE = [Barcode.QRCODE, 'QR_CODE', Barcode.AZTEC, Barcode.DATAMATRIX];\n})(Cards || (Cards = {}));\n"], "mappings": ";;;;;;;;;;;;;;AAEA,QAAiB;AAAjB,KAAA,SAAiBA,WAAQ;AAExB,UAAY;AAAZ,OAAA,SAAYC,OAAI;AACf,QAAAA,MAAA,QAAA,IAAA;AACA,QAAAA,MAAA,MAAA,IAAA;AACA,QAAAA,MAAA,MAAA,IAAA;AACA,QAAAA,MAAA,QAAA,IAAA;MACD,GALY,OAAAD,UAAA,SAAAA,UAAA,OAAI,CAAA,EAAA;AAOhB,UAAY;AAAZ,OAAA,SAAYE,QAAK;AAChB,QAAAA,OAAA,OAAA,IAAA;AACA,QAAAA,OAAA,UAAA,IAAA;AACA,QAAAA,OAAA,SAAA,IAAA;AACA,QAAAA,OAAA,UAAA,IAAA;AAEA,QAAAA,OAAA,gBAAA,IAAA;AACA,QAAAA,OAAA,cAAA,IAAA;MACD,GARY,QAAAF,UAAA,UAAAA,UAAA,QAAK,CAAA,EAAA;AAUjB,UAAY;AAAZ,OAAA,SAAYG,UAAO;AAClB,QAAAA,SAAA,SAAA,IAAA;AACA,QAAAA,SAAA,OAAA,IAAA;AACA,QAAAA,SAAA,QAAA,IAAA;AACA,QAAAA,SAAA,QAAA,IAAA;MACD,GALY,UAAAH,UAAA,YAAAA,UAAA,UAAO,CAAA,EAAA;IAyDpB,GA5EiB,aAAQ,QAAA,WAAR,WAAQ,CAAA,EAAA;;;;;;;;;;ACFzB,QAAA,aAAA;AAEA,QAAiBI;AAAjB,KAAA,SAAiBA,UAAO;AAEvB,UAAY;AAAZ,OAAA,SAAYC,SAAM;AACjB,QAAAA,QAAA,MAAA,IAAA;AACA,QAAAA,QAAA,QAAA,IAAA;MACD,GAHY,SAAAD,SAAA,WAAAA,SAAA,SAAM,CAAA,EAAA;AAKlB,UAAY;AAAZ,OAAA,SAAYE,YAAS;AACpB,QAAAA,WAAA,cAAA,IAAA;AACA,QAAAA,WAAA,cAAA,IAAA;MACD,GAHY,YAAAF,SAAA,cAAAA,SAAA,YAAS,CAAA,EAAA;AAKrB,UAAY;AAAZ,OAAA,SAAYG,aAAU;AACrB,QAAAA,YAAA,OAAA,IAAA;AACA,QAAAA,YAAA,MAAA,IAAA;AACA,QAAAA,YAAA,UAAA,IAAA;AACA,QAAAA,YAAA,iBAAA,IAAA;AACA,QAAAA,YAAA,UAAA,IAAA;AACA,QAAAA,YAAA,cAAA,IAAA;AACA,QAAAA,YAAA,UAAA,IAAA;AACA,QAAAA,YAAA,QAAA,IAAA;AACA,QAAAA,YAAA,KAAA,IAAA;MACD,GAVY,aAAAH,SAAA,eAAAA,SAAA,aAAU,CAAA,EAAA;AAYtB,UAAY;AAAZ,OAAA,SAAYI,oBAAiB;AAC5B,QAAAA,mBAAA,QAAA,IAAA;AACA,QAAAA,mBAAA,OAAA,IAAA;AACA,QAAAA,mBAAA,QAAA,IAAA;AACA,QAAAA,mBAAA,eAAA,IAAA;AACA,QAAAA,mBAAA,gBAAA,IAAA;MACD,GANY,oBAAAJ,SAAA,sBAAAA,SAAA,oBAAiB,CAAA,EAAA;AAQ7B,UAAY;AAAZ,OAAA,SAAYK,mBAAgB;AAC3B,QAAAA,kBAAAA,kBAAA,gBAAA,IAAA,EAAA,IAAA;AACA,QAAAA,kBAAAA,kBAAA,QAAA,IAAA,EAAA,IAAA;AACA,QAAAA,kBAAAA,kBAAA,SAAA,IAAA,CAAA,IAAA;AACA,QAAAA,kBAAAA,kBAAA,OAAA,IAAA,CAAA,IAAA;MACD,GALY,mBAAAL,SAAA,qBAAAA,SAAA,mBAAgB,CAAA,EAAA;AAO5B,UAAY;AAAZ,OAAA,SAAYM,YAAS;AACpB,QAAAA,WAAA,SAAA,IAAA;AACA,QAAAA,WAAA,UAAA,IAAA;AACA,QAAAA,WAAA,YAAA,IAAA;MACD,GAJY,YAAAN,SAAA,cAAAA,SAAA,YAAS,CAAA,EAAA;AAMrB,UAAY;AAAZ,OAAA,SAAYO,YAAS;AACpB,QAAAA,WAAA,YAAA,IAAA;AACA,QAAAA,WAAA,YAAA,IAAA;AACA,QAAAA,WAAA,YAAA,IAAA;AACA,QAAAA,WAAA,YAAA,IAAA;AACA,QAAAA,WAAA,YAAA,IAAA;AACA,QAAAA,WAAA,YAAA,IAAA;AACA,QAAAA,WAAA,aAAA,IAAA;AACA,QAAAA,WAAA,aAAA,IAAA;MACD,GATY,YAAAP,SAAA,cAAAA,SAAA,YAAS,CAAA,EAAA;AAWrB,UAAY;AAAZ,OAAA,SAAYQ,UAAO;AAClB,QAAAA,SAAA,KAAA,IAAA;AACA,QAAAA,SAAA,KAAA,IAAA;AACA,QAAAA,SAAA,MAAA,IAAA;AACA,QAAAA,SAAA,QAAA,IAAA;AACA,QAAAA,SAAA,QAAA,IAAA;MACD,GANY,UAAAR,SAAA,YAAAA,SAAA,UAAO,CAAA,EAAA;AAyGN,MAAAA,SAAA,UAAU;QACtB,cAAc,UAAU;QACxB,cAAc,UAAU;QACxB,YAAY,CAAC,UAAU,cAAc,UAAU,YAAY;QAC3D,QAAQ,WAAA,SAAS,KAAK;QACtB,YAAY,WAAA,SAAS,MAAM;;IAE7B,GAxKiBA,aAAO,QAAA,UAAPA,WAAO,CAAA,EAAA;AA0KxB,QAAiB;AAAjB,KAAA,SAAiBS,UAAO;AAEvB,UAAY;AAAZ,OAAA,SAAYC,eAAY;AACvB,QAAAA,cAAA,QAAA,IAAA;AACA,QAAAA,cAAA,QAAA,IAAA;AACA,QAAAA,cAAA,QAAA,IAAA;AACA,QAAAA,cAAA,OAAA,IAAA;AACA,QAAAA,cAAA,SAAA,IAAA;AACA,QAAAA,cAAA,WAAA,IAAA;AACA,QAAAA,cAAA,WAAA,IAAA;AACA,QAAAA,cAAA,KAAA,IAAA;MACD,GATY,eAAAD,SAAA,iBAAAA,SAAA,eAAY,CAAA,EAAA;AAWxB,UAAY;AAAZ,OAAA,SAAYE,sBAAmB;AAC9B,QAAAA,qBAAA,QAAA,IAAA;AACA,QAAAA,qBAAA,OAAA,IAAA;AACA,QAAAA,qBAAA,KAAA,IAAA;MACD,GAJY,sBAAAF,SAAA,wBAAAA,SAAA,sBAAmB,CAAA,EAAA;IAKhC,GAlBiB,YAAO,QAAA,UAAP,UAAO,CAAA,EAAA;;;;;AC5KxB,IAAAG,kBAAsC;;;ACAtC,qBAAsC;AAC/B,IAAI;AAAA,CACV,SAAUC,UAAS;AAChB,EAAAA,SAAQ,qBAAqB,eAAAC,QAAW,UAAU;AACtD,GAAG,YAAY,UAAU,CAAC,EAAE;;;ACJrB,IAAI;AAAA,CACV,SAAUC,QAAO;AACd,MAAI;AACJ,GAAC,SAAUC,UAAS;AAChB,IAAAA,SAAQ,QAAQ,IAAI;AACpB,IAAAA,SAAQ,OAAO,IAAI;AACnB,IAAAA,SAAQ,YAAY,IAAI;AACxB,IAAAA,SAAQ,SAAS,IAAI;AAAA,EACzB,GAAG,UAAUD,OAAM,YAAYA,OAAM,UAAU,CAAC,EAAE;AAClD,EAAAA,OAAM,uBAAuB,QAAQ;AACrC,EAAAA,OAAM,cAAc,CAAC,QAAQ,QAAQ,WAAW,QAAQ,OAAO,QAAQ,UAAU;AACrF,GAAG,UAAU,QAAQ,CAAC,EAAE;;;AFPjB,IAAM,QAAQ,CAAC,QAAQ;AAC1B,QAAM,MAAM;AACZ,SAAO,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,MAAM,IAAI,OAAO,GAAG,CAAC;AAC/C;AACO,IAAM,UAAU,CAAC,QAAQ;AAC5B,QAAM,MAAM;AACZ,SAAO,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,MAAM,IAAI,OAAO,GAAG,CAAC;AAC/C;AACO,IAAM,SAAS,CAAC,QAAQ;AAC3B,QAAM,UAAU,IAAI,MAAM,MAAM;AAChC,QAAM,OAAM,mCAAS,IAAI,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,OAAM;AACvE,SAAO,QAAQ;AACnB;AACO,IAAM,WAAW,CAAC,SAAS;AAC9B,SAAO,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM,QAAQ,IAAI;AAClE;AACO,IAAM,cAAc,CAAC,QAAQ,WAAW;AAC3C,MAAI,CAAC,SAAS,MAAM,KAAK,CAAC,SAAS,MAAM,GAAG;AACxC,WAAO,WAAW,SAAY,SAAS;AAAA,EAC3C;AACA,QAAM,SAAS,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC;AAE7C,aAAW,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC5B,aAAO,GAAG,IAAI,SAAS,OAAO,GAAG,CAAC,IAAI,YAAY,CAAC,GAAG,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG;AAAA,IACnF;AAAA,EACJ;AACA,aAAW,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC5B,UAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,GAAG;AAE5B,eAAO,GAAG,IAAI,OAAO,GAAG,KAAK,SAAY,OAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MACrE,WACS,SAAS,OAAO,GAAG,CAAC,GAAG;AAC5B,YAAI,EAAE,OAAO,SAAS;AAClB,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC5B,OACK;AACD,iBAAO,GAAG,IAAI,YAAY,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,QACtD;AAAA,MACJ,OACK;AACD,eAAO,GAAG,IAAI,OAAO,GAAG,MAAM,SAAY,OAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MACtE;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,YAAY,SAAS,MAAM;AACvC,QAAM,WAAW;AAAA,IACb,OAAO,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,IAChC,GAAG,QAAQ,IAAI,CAAC,SAAS,OAAO,OAAO,IAAI,EAAE,KAAK,GAAG,CAAC;AAAA,EAC1D,EACK,KAAK,IAAI,EACT,QAAQ,iBAAiB,EAAE;AAChC,SAAO;AAAA,IACH,UAAU,GAAG,IAAI;AAAA,IACjB;AAAA,EACJ;AACJ;AACO,SAAS,gBAAgB,OAAO,GAAG;AACtC,QAAM,EAAE,YAAY,gBAAgB,IAAI,MAAM,UAAU,GAAG,IAAI,SAAS,CAAC;AACzE,QAAM,WAAW,iBAAiB;AAClC,QAAM,cAAc,EAAE,SAAS,QAAQ,EAAE;AACzC,QAAM,qBAAqB,CAAC,CAAC,eAAe,gBAAgB,SAAS,QAAQ;AAC7E,QAAM,QAAQ,qBAAqB,cAAc,EAAE,eAAe;AAClE,QAAM,YAAY,GAAG,cAAc,QAAQ,EAAE;AAC7C,QAAM,QAAQ,aAAa,cAAc,QAAQ,KAAK,IAAI,YAAY;AACtE,QAAM,QAAS,aAAa,WAAW,CAAC,qBAAsB,QAAQ,KAAK,IAAI;AAC/E,QAAM,cAAc,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK;AAC7C,SAAO,GAAG,KAAK,GAAG,cAAc,OAAO,EAAE,GAAG,WAAW;AAC3D;AACO,SAAS,aAAa,YAAY,WAAW,YAAY,QAAQ,oBAAoB;AACxF,MAAI,EAAE,aAAa;AACf,WAAO;AACX,MAAI,cAAc,gBAAAE,QAAW,UAAU;AACnC,WAAO,GAAG,SAAS,IAAI,UAAU;AACrC,SAAO,GAAG,UAAU,IAAI,SAAS;AACrC;AACO,SAAS,cAAc,MAAM;AAChC,UAAQ,OAAO,KAAK,KAAK,OAAO,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG;AACvE;AACO,SAAS,UAAU,QAAQ;AAtFlC;AAuFI,QAAM,EAAE,YAAY,WAAW,UAAU,QAAQ,MAAM,QAAQ,QAAQ,UAAU,IAAI,UAAU,CAAC;AAChG,QAAM,EAAE,MAAM,OAAO,KAAK,KAAK,IAAI,aAAa,CAAC;AACjD,QAAM,EAAE,cAAc,IAAI,aAAa,IAAI,OAAO,UAAU,KAAI,iCAAS,OAAM,CAAC;AAChF,QAAM,EAAE,UAAU,IAAI,OAAO,SAAS,KAAI,iCAAS,OAAM,CAAC;AAC1D,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,WAAW,6BAAM;AAAA,IACjB,UAAU,YAAY,aAAa,YAAY,WAAW,6BAAM,KAAK;AAAA,IACrE;AAAA,IACA,WAAW,OAAO,OAAQ,QAAQ,SAAS,MAAM,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,KAAM,SAAS,MAAM,GAAG,KAAK,IAAI,GAAG,KAAK;AAAA,IACjH,cAAa,sCAAS,OAAT,mBAAa;AAAA,IAC1B,iBAAiB,kBAAkB,WAAW,QAAQ,aAAa,EAAE,GAAG,WAAW;AAAA,IACnF,QAAQ;AAAA,IACR;AAAA,IACA,OAAO;AAAA,IACP;AAAA,EACJ;AACJ;AACO,SAAS,iBAAiB,MAAM,UAAU,YAAY;AA1G7D;AA2GI,QAAM,EAAE,cAAc,WAAW,WAAW,YAAY,WAAW,QAAQ,IAAI,QAAQ,CAAC;AACxF,QAAM,UAAU,SAAS,KAAK,CAAC,MAAM,EAAE,cAAc,SAAS;AAC9D,QAAM,OAAO,mCAAS,MAAM,KAAK,CAACC,WAASA,SAAA,gBAAAA,MAAM,WAAU;AAC3D,SAAO;AAAA,IACH,OAAM,6BAAM,SAAQ;AAAA,IACpB,QAAO,wCAAM,SAAN,mBAAY,UAAZ,mBAAmB;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAa,8CAAY,iBAAZ,mBAA2B,OAAM,MAAM;AAAA,IACpD,kBAAiB,yCAAY,oBAAmB;AAAA,IAChD;AAAA,IACA;AAAA,IACA,cAAa,mCAAS,SAAQ;AAAA,IAC9B;AAAA,IACA,OAAM,mCAAS,SAAQ;AAAA,EAC3B;AACJ;AACO,SAAS,cAAc,cAAc;AACxC,QAAM,QAAQ,CAAC;AACf,SAAO,KAAK,YAAY,EAAE,QAAQ,SAAO;AACrC,UAAM,QAAQ,aAAa,GAAG;AAC9B,QAAI,UAAU,QAAW;AACrB,UAAI,OAAO,UAAU,UAAU;AAC3B,cAAM,KAAK,GAAG,GAAG,IAAI,mBAAmB,KAAK,UAAU,KAAK,CAAC,CAAC,EAAE;AAAA,MACpE,OACK;AACD,cAAM,KAAK,GAAG,GAAG,IAAI,mBAAmB,KAAK,CAAC,EAAE;AAAA,MACpD;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO,MAAM,KAAK,GAAG;AACzB;AACO,SAAS,aAAa,MAAM,aAAa,QAAQ,UAAU,WAAW;AACzE,MAAI,WAAW,SAAS,IAAI,GAAG;AAC3B,WAAO;AAAA,EACX;AACA,QAAM,EAAE,WAAW,QAAQ,IAAI,QAAQ;AACvC,MAAI,OAAO,KAAK,SAAS,EAAE,SAAS,IAAI,GAAG;AACvC,UAAM,gBAAgB,UAAU,IAAI,KAAK,UAAU;AACnD,WAAO,cAAc,KAAK,CAAC,MAAM,WAAW,SAAS,CAAC,CAAC,KAAK;AAAA,EAChE;AACA,SAAO;AACX;AACO,SAAS,aAAa,QAAQ,UAAU,WAAW,WAAW,gBAAgB,OAAO;AACxF,MAAI,WAAW;AACX,WAAO;AACX,QAAM,WAAW;AAAA,IACb,KAAK,EAAE,YAAY,KAAK,gBAAgB,OAAO,SAAS,EAAE;AAAA,IAC1D,KAAK,EAAE,YAAY,KAAK,gBAAgB,OAAO,SAAS,EAAE;AAAA,IAC1D,KAAK,EAAE,YAAY,KAAK,gBAAgB,MAAM,SAAS,EAAE;AAAA,IACzD,KAAK,EAAE,YAAY,KAAK,gBAAgB,OAAO,SAAS,EAAE;AAAA,IAC1D,QAAQ,EAAE,YAAY,KAAK,gBAAgB,KAAK,SAAS,EAAE;AAAA,EAC/D;AACA,QAAM,WAAW,SAAS,YAAY,QAAQ,KAAK,SAAS;AAC5D,QAAM,EAAE,gBAAgB,YAAY,QAAQ,IAAI;AAChD,SAAO,GAAG,gBAAgB,iBAAiB,UAAU,GAAG,OAAO,eAAe,QAAW;AAAA,IACrF,uBAAuB,aAAa,WAAW;AAAA,IAC/C,uBAAuB,aAAa,WAAW;AAAA,EACnD,CAAC,CAAC;AACN;AACO,SAAS,kBAAkB,QAAQ,aAAa;AACnD,QAAM,cAAc,OAAO,QAAQ,OAAO,EAAE;AAC5C,MAAI,YAAY,cAAc,IAAI,WAAW,MAAM;AAKnD,QAAM,WAAW;AAAA,IACb,KAAK,CAAC,GAAG,CAAC;AAAA;AAAA,IACV,KAAK,CAAC,GAAG,CAAC;AAAA;AAAA,IACV,KAAK,CAAC,GAAG,GAAG,CAAC;AAAA;AAAA,IACb,MAAM,CAAC,GAAG,GAAG,CAAC;AAAA;AAAA,IACd,MAAM,CAAC,GAAG,GAAG,CAAC;AAAA;AAAA,EAClB;AACA,QAAM,UAAU,SAAS,YAAY,OAAO,SAAS,CAAC,KAAK,gBAAgB,YAAY,MAAM;AAE7F,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,QAAI,IAAI;AACJ,mBAAa;AACjB,iBAAa,YAAY,UAAU,UAAU,WAAW,QAAQ,CAAC,CAAC;AAClE,gBAAY,QAAQ,CAAC;AAAA,EACzB;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,QAAQ;AAE7B,QAAM,YAAY,KAAK,KAAK,SAAS,CAAC;AACtC,QAAM,SAAS,CAAC;AAEhB,QAAM,WAAW,KAAK,MAAM,SAAS,SAAS;AAC9C,MAAI,YAAY,SAAS;AAEzB,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAEhC,UAAM,YAAa,KAAK,YAAY,YAAa,WAAW,IAAI;AAChE,WAAO,KAAK,SAAS;AAAA,EACzB;AAEA,SAAO,KAAK;AACZ,SAAO;AACX;AACO,SAAS,aAAa;AACzB,QAAM,UAAU,SAAS,UAAU,IAAI,MAAM,GAAG;AAChD,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,OAAO,OAAO,CAAC,EAAE,MAAM,GAAG;AAC9B,QAAI,CAAC,QAAQ,KAAK,SAAS;AACvB;AACJ,WAAO,OAAO,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AAAA,EACvD;AACA,SAAO;AACX;AACO,SAAS,iBAAiB,MAAM,IAAI;AACvC,QAAM,YAAY,OAAO,IAAI,MAAM,GAAG,EAAE,SAAS;AACjD,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,QAAM,SAAS,IAAI,IAAI,GAAG,EAAE;AAC5B,SAAO,OAAO,YAAY,MAAM;AACpC;AACO,SAAS,eAAe,KAAK;AAChC,MAAI,OAAO,QAAQ;AACf,WAAO;AACX,MAAI,IAAI,MAAM,kDAAkD;AAC5D,WAAO;AACX,MAAI,IAAI,MAAM,kEAAkE;AAC5E,WAAO;AACX,MAAI,IAAI,MAAM,kBAAkB;AAC5B,WAAO;AACX,SAAO;AACX;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAQ,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,WAAW;AAC7D;AACO,SAAS,SAAS,IAAI,OAAO;AAChC,MAAI;AACJ,SAAO,IAAI,SAAS;AAChB,QAAI,WAAW;AACX,mBAAa,SAAS;AAAA,IAC1B;AACA,gBAAY,WAAW,MAAM;AACzB,SAAG,GAAG,IAAI;AAAA,IACd,GAAG,KAAK;AAAA,EACZ;AACJ;", "names": ["Contacts", "Type", "Dates", "<PERSON><PERSON><PERSON><PERSON>", "Persons", "Gender", "NameOrder", "Identities", "PermissionChannel", "PermissionStatus", "Residency", "BloodType", "Species", "Socials", "Relationship", "InverseRelationship", "import_persons", "Persons", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cards", "Barcode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tier"]}