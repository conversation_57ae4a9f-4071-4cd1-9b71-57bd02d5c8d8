{"version": 3, "sources": ["../../@perkd/vue-components/dist/components/UIScreen.js"], "sourcesContent": ["import { defineComponent as J, useSlots as K, toRefs as Q, ref as C, computed as b, onMounted as X, onBeforeUnmount as Y, createElementBlock as u, openBlock as i, Fragment as Z, createElementVNode as B, createVNode as v, mergeProps as T, renderSlot as r, createBlock as x, createCommentVNode as f, unref as t, normalizeProps as ee, createSlots as oe, withCtx as p, normalizeClass as P, Transition as _ } from \"vue\";\nimport te from \"./UINavigationBar.js\";\nimport ne from \"./UIButton.js\";\nimport se from \"./UILoading.js\";\nconst le = {\n  key: 0,\n  theme: \"light\",\n  class: \"notify-container\"\n}, ae = {\n  key: 0,\n  class: \"content\"\n}, ie = {\n  key: 0,\n  class: \"screen overlay\"\n}, re = {\n  key: 0,\n  class: \"screen overlay\"\n}, ve = /* @__PURE__ */ J({\n  __name: \"UIScreen\",\n  props: {\n    title: {\n      type: String,\n      default: \"\"\n    },\n    titleClass: {\n      type: String,\n      default: \"\"\n    },\n    navigationBarTheme: String,\n    disableNavBack: {\n      type: Boolean,\n      default: !1\n    },\n    isOnline: {\n      type: Boolean,\n      default: !0\n    },\n    status: {\n      type: String,\n      default: \"\"\n    },\n    showClose: {\n      type: Boolean,\n      default: !0\n    },\n    loading: {\n      type: Boolean,\n      default: !1\n    },\n    onContentScroll: {\n      type: Function\n    }\n  },\n  emits: [\"goPreviousPage\", \"closeWindow\"],\n  setup(N, { expose: R, emit: E }) {\n    const s = K(), c = N, { title: h, titleClass: L, navigationBarTheme: y, isOnline: O, status: F, showClose: g, loading: I } = Q(c), H = window.innerHeight / 3, V = window.innerHeight / 2, w = C(0), l = C(void 0), a = C(void 0);\n    let m;\n    const $ = E, k = b(() => {\n      var e;\n      return (e = window.history.state) != null && e.back && !c.disableNavBack ? { type: \"back\", onClick: () => $(\"goPreviousPage\") } : void 0;\n    }), W = b(() => ({\n      title: h.value,\n      titleClass: L.value,\n      theme: y == null ? void 0 : y.value,\n      isOnline: O.value,\n      status: F.value,\n      navBack: k.value\n    })), j = b(() => {\n      const e = [];\n      return (s.navigationBar || h.value || k.value || g.value) && e.push(\"with-navigation-bar\"), s.tabBar && e.push(\"with-tab-bar\"), e;\n    });\n    X(() => {\n      var e;\n      c.onContentScroll && ((e = l.value) == null || e.addEventListener(\"scroll\", S));\n    }), Y(() => {\n      var e;\n      c.onContentScroll && ((e = l.value) == null || e.removeEventListener(\"scroll\", S));\n    });\n    function S(e) {\n      c.onContentScroll && c.onContentScroll(e);\n    }\n    function z(e, o) {\n      m && (clearTimeout(m), m = void 0), o ? U(e) : M();\n    }\n    function U(e) {\n      const o = e.target;\n      w.value = o.getBoundingClientRect().top;\n      const n = w.value - H;\n      a.value && (a.value.classList.remove(\"close\"), a.value.style.height = V + \"px\"), setTimeout(() => {\n        var d;\n        (d = l.value) == null || d.scrollBy({ top: n, behavior: \"smooth\" });\n      }, 0);\n    }\n    function M() {\n      m = setTimeout(() => {\n        a.value && a.value.classList.add(\"close\");\n      }, 500);\n    }\n    function q(e, o) {\n      var d;\n      const n = { behavior: \"smooth\" };\n      e !== void 0 && Object.assign(n, { top: e }), o !== void 0 && Object.assign(n, { left: o }), (d = l.value) == null || d.scrollBy(n);\n    }\n    function A(e) {\n      var o;\n      (o = l.value) == null || o.scrollTo({ top: e || 0, behavior: \"smooth\" });\n    }\n    function D(e) {\n      var o;\n      (o = l.value) == null || o.scrollTo({ left: e || 0, behavior: \"smooth\" });\n    }\n    function G() {\n      $(\"closeWindow\");\n    }\n    return R({\n      scrollBy: q,\n      scrollToTop: A,\n      scrollToLeft: D\n    }), (e, o) => {\n      var n;\n      return i(), u(Z, null, [\n        B(\"div\", T({\n          class: [\"screen\", ...j.value]\n        }, e.$attrs), [\n          r(e.$slots, \"navigationBar\", {}, () => [\n            t(h) || k.value || t(g) ? (i(), x(te, ee(T({ key: 0 }, W.value)), oe({ _: 2 }, [\n              t(g) ? {\n                name: \"rightContent\",\n                fn: p(() => [\n                  v(ne, {\n                    type: \"circle\",\n                    icon: { name: \"close\" },\n                    onClick: G\n                  })\n                ]),\n                key: \"0\"\n              } : void 0\n            ]), 1040)) : f(\"\", !0)\n          ]),\n          B(\"div\", {\n            ref_key: \"screenContentRef\",\n            ref: l,\n            class: P(`screen-content ${t(s).footer ? \"screen-content-with-footer\" : \"\"}`)\n          }, [\n            v(_, { name: \"swipe-down\" }, {\n              default: p(() => [\n                t(s).notify ? (i(), u(\"div\", le, [\n                  r(e.$slots, \"notify\")\n                ])) : f(\"\", !0)\n              ]),\n              _: 3\n            }),\n            t(s).content ? (i(), u(\"div\", ae, [\n              r(e.$slots, \"content\", { focusChange: z })\n            ])) : f(\"\", !0),\n            t(s).footer ? (i(), u(\"div\", {\n              key: 1,\n              class: P(`footer ${(n = a.value) != null && n.style.height ? \"footer-before-keyboard\" : \"\"}`)\n            }, [\n              r(e.$slots, \"footer\")\n            ], 2)) : f(\"\", !0),\n            B(\"div\", {\n              ref_key: \"keyboardRef\",\n              ref: a,\n              class: \"screen-keyboard\"\n            }, null, 512)\n          ], 2),\n          r(e.$slots, \"tabBar\")\n        ], 16),\n        v(_, { name: \"fade\" }, {\n          default: p(() => [\n            t(I) ? (i(), u(\"div\", ie, [\n              r(e.$slots, \"loading\", {}, () => [\n                v(se)\n              ])\n            ])) : f(\"\", !0)\n          ]),\n          _: 3\n        }),\n        v(_, { name: \"fade\" }, {\n          default: p(() => [\n            t(s).dialog ? (i(), u(\"div\", re, [\n              r(e.$slots, \"dialog\")\n            ])) : f(\"\", !0)\n          ]),\n          _: 3\n        })\n      ], 64);\n    };\n  }\n});\nexport {\n  ve as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,KAAK;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AACT;AAJA,IAIG,KAAK;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AACT;AAPA,IAOG,KAAK;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AACT;AAVA,IAUG,KAAK;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AACT;AAbA,IAaG,KAAqB,gBAAE;AAAA,EACxB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAO,CAAC,kBAAkB,aAAa;AAAA,EACvC,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,UAAM,IAAI,SAAE,GAAG,IAAI,GAAG,EAAE,OAAOA,IAAG,YAAY,GAAG,oBAAoB,GAAG,UAAU,GAAG,QAAQ,GAAG,WAAW,GAAG,SAASC,GAAE,IAAI,OAAE,CAAC,GAAG,IAAI,OAAO,cAAc,GAAG,IAAI,OAAO,cAAc,GAAG,IAAI,IAAE,CAAC,GAAG,IAAI,IAAE,MAAM,GAAG,IAAI,IAAE,MAAM;AAChO,QAAI;AACJ,UAAM,IAAI,GAAG,IAAI,SAAE,MAAM;AACvB,UAAI;AACJ,cAAQ,IAAI,OAAO,QAAQ,UAAU,QAAQ,EAAE,QAAQ,CAAC,EAAE,iBAAiB,EAAE,MAAM,QAAQ,SAAS,MAAM,EAAE,gBAAgB,EAAE,IAAI;AAAA,IACpI,CAAC,GAAG,IAAI,SAAE,OAAO;AAAA,MACf,OAAOD,GAAE;AAAA,MACT,YAAY,EAAE;AAAA,MACd,OAAO,KAAK,OAAO,SAAS,EAAE;AAAA,MAC9B,UAAU,EAAE;AAAA,MACZ,QAAQ,EAAE;AAAA,MACV,SAAS,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,SAAE,MAAM;AACf,YAAM,IAAI,CAAC;AACX,cAAQ,EAAE,iBAAiBA,GAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,qBAAqB,GAAG,EAAE,UAAU,EAAE,KAAK,cAAc,GAAG;AAAA,IAClI,CAAC;AACD,cAAE,MAAM;AACN,UAAI;AACJ,QAAE,qBAAqB,IAAI,EAAE,UAAU,QAAQ,EAAE,iBAAiB,UAAU,CAAC;AAAA,IAC/E,CAAC,GAAG,gBAAE,MAAM;AACV,UAAI;AACJ,QAAE,qBAAqB,IAAI,EAAE,UAAU,QAAQ,EAAE,oBAAoB,UAAU,CAAC;AAAA,IAClF,CAAC;AACD,aAAS,EAAE,GAAG;AACZ,QAAE,mBAAmB,EAAE,gBAAgB,CAAC;AAAA,IAC1C;AACA,aAAS,EAAE,GAAG,GAAG;AACf,YAAM,aAAa,CAAC,GAAG,IAAI,SAAS,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,IACnD;AACA,aAAS,EAAE,GAAG;AACZ,YAAM,IAAI,EAAE;AACZ,QAAE,QAAQ,EAAE,sBAAsB,EAAE;AACpC,YAAM,IAAI,EAAE,QAAQ;AACpB,QAAE,UAAU,EAAE,MAAM,UAAU,OAAO,OAAO,GAAG,EAAE,MAAM,MAAM,SAAS,IAAI,OAAO,WAAW,MAAM;AAChG,YAAI;AACJ,SAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE,KAAK,GAAG,UAAU,SAAS,CAAC;AAAA,MACpE,GAAG,CAAC;AAAA,IACN;AACA,aAAS,IAAI;AACX,UAAI,WAAW,MAAM;AACnB,UAAE,SAAS,EAAE,MAAM,UAAU,IAAI,OAAO;AAAA,MAC1C,GAAG,GAAG;AAAA,IACR;AACA,aAAS,EAAE,GAAG,GAAG;AACf,UAAI;AACJ,YAAM,IAAI,EAAE,UAAU,SAAS;AAC/B,YAAM,UAAU,OAAO,OAAO,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM,UAAU,OAAO,OAAO,GAAG,EAAE,MAAM,EAAE,CAAC,IAAI,IAAI,EAAE,UAAU,QAAQ,EAAE,SAAS,CAAC;AAAA,IACpI;AACA,aAAS,EAAE,GAAG;AACZ,UAAI;AACJ,OAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE,KAAK,KAAK,GAAG,UAAU,SAAS,CAAC;AAAA,IACzE;AACA,aAAS,EAAE,GAAG;AACZ,UAAI;AACJ,OAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE,MAAM,KAAK,GAAG,UAAU,SAAS,CAAC;AAAA,IAC1E;AACA,aAAS,IAAI;AACX,QAAE,aAAa;AAAA,IACjB;AACA,WAAO,EAAE;AAAA,MACP,UAAU;AAAA,MACV,aAAa;AAAA,MACb,cAAc;AAAA,IAChB,CAAC,GAAG,CAAC,GAAG,MAAM;AACZ,UAAI;AACJ,aAAO,UAAE,GAAG,mBAAE,UAAG,MAAM;AAAA,QACrB,gBAAE,OAAO,WAAE;AAAA,UACT,OAAO,CAAC,UAAU,GAAG,EAAE,KAAK;AAAA,QAC9B,GAAG,EAAE,MAAM,GAAG;AAAA,UACZ,WAAE,EAAE,QAAQ,iBAAiB,CAAC,GAAG,MAAM;AAAA,YACrC,MAAEA,EAAC,KAAK,EAAE,SAAS,MAAE,CAAC,KAAK,UAAE,GAAG,YAAE,GAAI,eAAG,WAAE,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,YAAG,EAAE,GAAG,EAAE,GAAG;AAAA,cAC7E,MAAE,CAAC,IAAI;AAAA,gBACL,MAAM;AAAA,gBACN,IAAI,QAAE,MAAM;AAAA,kBACV,YAAE,GAAI;AAAA,oBACJ,MAAM;AAAA,oBACN,MAAM,EAAE,MAAM,QAAQ;AAAA,oBACtB,SAAS;AAAA,kBACX,CAAC;AAAA,gBACH,CAAC;AAAA,gBACD,KAAK;AAAA,cACP,IAAI;AAAA,YACN,CAAC,GAAG,IAAI,KAAK,mBAAE,IAAI,IAAE;AAAA,UACvB,CAAC;AAAA,UACD,gBAAE,OAAO;AAAA,YACP,SAAS;AAAA,YACT,KAAK;AAAA,YACL,OAAO,eAAE,kBAAkB,MAAE,CAAC,EAAE,SAAS,+BAA+B,EAAE,EAAE;AAAA,UAC9E,GAAG;AAAA,YACD,YAAE,YAAG,EAAE,MAAM,aAAa,GAAG;AAAA,cAC3B,SAAS,QAAE,MAAM;AAAA,gBACf,MAAE,CAAC,EAAE,UAAU,UAAE,GAAG,mBAAE,OAAO,IAAI;AAAA,kBAC/B,WAAE,EAAE,QAAQ,QAAQ;AAAA,gBACtB,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,cAChB,CAAC;AAAA,cACD,GAAG;AAAA,YACL,CAAC;AAAA,YACD,MAAE,CAAC,EAAE,WAAW,UAAE,GAAG,mBAAE,OAAO,IAAI;AAAA,cAChC,WAAE,EAAE,QAAQ,WAAW,EAAE,aAAa,EAAE,CAAC;AAAA,YAC3C,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YACd,MAAE,CAAC,EAAE,UAAU,UAAE,GAAG,mBAAE,OAAO;AAAA,cAC3B,KAAK;AAAA,cACL,OAAO,eAAE,WAAW,IAAI,EAAE,UAAU,QAAQ,EAAE,MAAM,SAAS,2BAA2B,EAAE,EAAE;AAAA,YAC9F,GAAG;AAAA,cACD,WAAE,EAAE,QAAQ,QAAQ;AAAA,YACtB,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YACjB,gBAAE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,KAAK;AAAA,cACL,OAAO;AAAA,YACT,GAAG,MAAM,GAAG;AAAA,UACd,GAAG,CAAC;AAAA,UACJ,WAAE,EAAE,QAAQ,QAAQ;AAAA,QACtB,GAAG,EAAE;AAAA,QACL,YAAE,YAAG,EAAE,MAAM,OAAO,GAAG;AAAA,UACrB,SAAS,QAAE,MAAM;AAAA,YACf,MAAEC,EAAC,KAAK,UAAE,GAAG,mBAAE,OAAO,IAAI;AAAA,cACxB,WAAE,EAAE,QAAQ,WAAW,CAAC,GAAG,MAAM;AAAA,gBAC/B,YAAE,CAAE;AAAA,cACN,CAAC;AAAA,YACH,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UAChB,CAAC;AAAA,UACD,GAAG;AAAA,QACL,CAAC;AAAA,QACD,YAAE,YAAG,EAAE,MAAM,OAAO,GAAG;AAAA,UACrB,SAAS,QAAE,MAAM;AAAA,YACf,MAAE,CAAC,EAAE,UAAU,UAAE,GAAG,mBAAE,OAAO,IAAI;AAAA,cAC/B,WAAE,EAAE,QAAQ,QAAQ;AAAA,YACtB,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UAChB,CAAC;AAAA,UACD,GAAG;AAAA,QACL,CAAC;AAAA,MACH,GAAG,EAAE;AAAA,IACP;AAAA,EACF;AACF,CAAC;", "names": ["h", "I"]}