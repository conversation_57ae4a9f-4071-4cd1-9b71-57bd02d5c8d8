{"version": 3, "sources": ["../../@perkd/applet-common/dist/appbridge.js"], "sourcesContent": ["'use strict';\n(function () {\n    const s6 = () => Math.floor((1 + Math.random()) * 0x1000000).toString(16).substring(1);\n    const evtId = () => s6() + s6();\n    const PENDING = {};\n    const DATA = {};\n    const BAG = {};\n    // This method updates the DATA object and syncs it with the $data global object.\n    const setDATA = (data) => {\n        Object.assign(DATA, data);\n        Object.keys(DATA).forEach(function (prop) {\n            if (typeof window.$data[prop] !== 'undefined')\n                return;\n            Object.defineProperty(window.$data, prop, {\n                get() {\n                    return DATA[prop];\n                },\n                set(value) {\n                    DATA[prop] = value;\n                },\n                enumerable: true,\n            });\n        });\n        window.dispatchEvent(new CustomEvent('data.changed'));\n    };\n    window.$perkd = {\n        onMessage(param) {\n            const pending = PENDING[param.id];\n            if (pending) {\n                if (param.error)\n                    pending.reject(param.error);\n                else\n                    pending.resolve(param.data);\n            }\n            else if (param.name === 'data.changed')\n                setDATA(param.data);\n            else if (param.name === 'bag.changed') {\n                Object.assign(BAG, param.data);\n                window.dispatchEvent(new CustomEvent('bag.changed'));\n            }\n            else\n                window.dispatchEvent(new CustomEvent(param.name, { detail: param.data }));\n        },\n        emit(name, data) {\n            const id = evtId(), event = JSON.stringify({ id, name, data });\n            if (window.ReactNativeWebView) {\n                window.ReactNativeWebView.postMessage(event);\n            }\n            else {\n                window.parent.postMessage(JSON.parse(event), '*');\n            }\n            return id;\n        },\n        do(action, param) {\n            const id = window.$perkd.emit('do', { action, param });\n            return new Promise(function (resolve, reject) { PENDING[id] = { resolve, reject }; });\n        },\n    };\n    // widget data\n    class Data {\n        save() { window.$perkd.do('data.save', DATA); }\n        add(prop) {\n            Object.defineProperty(this, prop, {\n                get() { return DATA[prop]; },\n                set(value) { DATA[prop] = value; },\n                enumerable: true,\n            });\n        }\n    }\n    window.$data = new Data();\n    // shopping bag\n    class Bag {\n        addItems(items) {\n            window.$perkd.do('bag.addItems', { items });\n        }\n        updateItems(items) {\n            window.$perkd.do('bag.updateItems', { items });\n        }\n        removeItems(items) {\n            window.$perkd.do('bag.removeItems', { items });\n        }\n        items;\n        amount;\n    }\n    window.$bag = new Bag();\n    const params = ['items', 'amount'];\n    params.forEach((prop) => Object.defineProperty(window.$bag, prop, {\n        get() {\n            return BAG[prop];\n        },\n        enumerable: true,\n    }));\n    // init\n    window.$perkd.do('init').then(setDATA);\n}());\nexport {};\n"], "mappings": ";;;;;CACC,WAAY;AACT,QAAM,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,OAAO,KAAK,QAAS,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC;AACrF,QAAM,QAAQ,MAAM,GAAG,IAAI,GAAG;AAC9B,QAAM,UAAU,CAAC;AACjB,QAAM,OAAO,CAAC;AACd,QAAM,MAAM,CAAC;AAEb,QAAM,UAAU,CAAC,SAAS;AACtB,WAAO,OAAO,MAAM,IAAI;AACxB,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAU,MAAM;AACtC,UAAI,OAAO,OAAO,MAAM,IAAI,MAAM;AAC9B;AACJ,aAAO,eAAe,OAAO,OAAO,MAAM;AAAA,QACtC,MAAM;AACF,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,QACA,IAAI,OAAO;AACP,eAAK,IAAI,IAAI;AAAA,QACjB;AAAA,QACA,YAAY;AAAA,MAChB,CAAC;AAAA,IACL,CAAC;AACD,WAAO,cAAc,IAAI,YAAY,cAAc,CAAC;AAAA,EACxD;AACA,SAAO,SAAS;AAAA,IACZ,UAAU,OAAO;AACb,YAAM,UAAU,QAAQ,MAAM,EAAE;AAChC,UAAI,SAAS;AACT,YAAI,MAAM;AACN,kBAAQ,OAAO,MAAM,KAAK;AAAA;AAE1B,kBAAQ,QAAQ,MAAM,IAAI;AAAA,MAClC,WACS,MAAM,SAAS;AACpB,gBAAQ,MAAM,IAAI;AAAA,eACb,MAAM,SAAS,eAAe;AACnC,eAAO,OAAO,KAAK,MAAM,IAAI;AAC7B,eAAO,cAAc,IAAI,YAAY,aAAa,CAAC;AAAA,MACvD;AAEI,eAAO,cAAc,IAAI,YAAY,MAAM,MAAM,EAAE,QAAQ,MAAM,KAAK,CAAC,CAAC;AAAA,IAChF;AAAA,IACA,KAAK,MAAM,MAAM;AACb,YAAM,KAAK,MAAM,GAAG,QAAQ,KAAK,UAAU,EAAE,IAAI,MAAM,KAAK,CAAC;AAC7D,UAAI,OAAO,oBAAoB;AAC3B,eAAO,mBAAmB,YAAY,KAAK;AAAA,MAC/C,OACK;AACD,eAAO,OAAO,YAAY,KAAK,MAAM,KAAK,GAAG,GAAG;AAAA,MACpD;AACA,aAAO;AAAA,IACX;AAAA,IACA,GAAG,QAAQ,OAAO;AACd,YAAM,KAAK,OAAO,OAAO,KAAK,MAAM,EAAE,QAAQ,MAAM,CAAC;AACrD,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,gBAAQ,EAAE,IAAI,EAAE,SAAS,OAAO;AAAA,MAAG,CAAC;AAAA,IACxF;AAAA,EACJ;AAAA,EAEA,MAAM,KAAK;AAAA,IACP,OAAO;AAAE,aAAO,OAAO,GAAG,aAAa,IAAI;AAAA,IAAG;AAAA,IAC9C,IAAI,MAAM;AACN,aAAO,eAAe,MAAM,MAAM;AAAA,QAC9B,MAAM;AAAE,iBAAO,KAAK,IAAI;AAAA,QAAG;AAAA,QAC3B,IAAI,OAAO;AAAE,eAAK,IAAI,IAAI;AAAA,QAAO;AAAA,QACjC,YAAY;AAAA,MAChB,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO,QAAQ,IAAI,KAAK;AAAA,EAExB,MAAM,IAAI;AAAA,IAAV;AAUI;AACA;AAAA;AAAA,IAVA,SAAS,OAAO;AACZ,aAAO,OAAO,GAAG,gBAAgB,EAAE,MAAM,CAAC;AAAA,IAC9C;AAAA,IACA,YAAY,OAAO;AACf,aAAO,OAAO,GAAG,mBAAmB,EAAE,MAAM,CAAC;AAAA,IACjD;AAAA,IACA,YAAY,OAAO;AACf,aAAO,OAAO,GAAG,mBAAmB,EAAE,MAAM,CAAC;AAAA,IACjD;AAAA,EAGJ;AACA,SAAO,OAAO,IAAI,IAAI;AACtB,QAAM,SAAS,CAAC,SAAS,QAAQ;AACjC,SAAO,QAAQ,CAAC,SAAS,OAAO,eAAe,OAAO,MAAM,MAAM;AAAA,IAC9D,MAAM;AACF,aAAO,IAAI,IAAI;AAAA,IACnB;AAAA,IACA,YAAY;AAAA,EAChB,CAAC,CAAC;AAEF,SAAO,OAAO,GAAG,MAAM,EAAE,KAAK,OAAO;AACzC,GAAE;", "names": []}