import {
  createElementBlock,
  defineComponent,
  normalizeClass,
  openBlock
} from "./chunk-U3LI7FBV.js";

// node_modules/@perkd/vue-components/dist/components/UIIcon.js
var i = defineComponent({
  __name: "UIIcon",
  props: {
    name: {
      type: String,
      require: true
    },
    color: {
      type: String,
      require: false
    }
  },
  setup(e) {
    return (c, a) => (openBlock(), createElementBlock("span", {
      class: normalizeClass(["picon", `picon-${e.name}`, e.color || ""])
    }, null, 2));
  }
});

export {
  i
};
//# sourceMappingURL=chunk-QMSMXSBA.js.map
