{"version": 3, "sources": ["../../vue-tel-input/dist/vue-tel-input.js", "../../@perkd/vue-components/dist/components/UIInputMobile.js"], "sourcesContent": ["import { defineComponent as gd, mergeModels as ve, shallowRef as J, useModel as md, watch as _, nextTick as be, reactive as vd, computed as A, onMounted as bd, resolveDirective as Cd, openBlock as P, createElementBlock as N, normalizeClass as E, withDirectives as Q, with<PERSON>eys as D, createElementVNode as R, unref as Ce, createCommentVNode as T, toDisplayString as j, renderSlot as ee, withModifiers as Od, vModelText as Pd, Fragment as Nd, renderList as wd, vModelDynamic as Id } from \"vue\";\nconst Ed = [\n  [\n    \"Afghanistan (‫افغانستان‬‎)\",\n    \"af\",\n    \"93\"\n  ],\n  [\n    \"Albania (Shqipëri)\",\n    \"al\",\n    \"355\"\n  ],\n  [\n    \"Algeria (‫الجزائر‬‎)\",\n    \"dz\",\n    \"213\"\n  ],\n  [\n    \"American Samoa\",\n    \"as\",\n    \"1\",\n    5,\n    [\"684\"]\n  ],\n  [\n    \"Andorra\",\n    \"ad\",\n    \"376\"\n  ],\n  [\n    \"Angola\",\n    \"ao\",\n    \"244\"\n  ],\n  [\n    \"<PERSON><PERSON><PERSON>\",\n    \"ai\",\n    \"1\",\n    6,\n    [\"264\"]\n  ],\n  [\n    \"Antigua and Barbuda\",\n    \"ag\",\n    \"1\",\n    7,\n    [\"268\"]\n  ],\n  [\n    \"Argentina\",\n    \"ar\",\n    \"54\"\n  ],\n  [\n    \"Armenia (Հայաստան)\",\n    \"am\",\n    \"374\"\n  ],\n  [\n    \"Aruba\",\n    \"aw\",\n    \"297\"\n  ],\n  [\n    \"Ascension Island\",\n    \"ac\",\n    \"247\"\n  ],\n  [\n    \"Australia\",\n    \"au\",\n    \"61\",\n    0\n  ],\n  [\n    \"Austria (Österreich)\",\n    \"at\",\n    \"43\"\n  ],\n  [\n    \"Azerbaijan (Azərbaycan)\",\n    \"az\",\n    \"994\"\n  ],\n  [\n    \"Bahamas\",\n    \"bs\",\n    \"1\",\n    8,\n    [\"242\"]\n  ],\n  [\n    \"Bahrain (‫البحرين‬‎)\",\n    \"bh\",\n    \"973\"\n  ],\n  [\n    \"Bangladesh (বাংলাদেশ)\",\n    \"bd\",\n    \"880\"\n  ],\n  [\n    \"Barbados\",\n    \"bb\",\n    \"1\",\n    9,\n    [\"246\"]\n  ],\n  [\n    \"Belarus (Беларусь)\",\n    \"by\",\n    \"375\"\n  ],\n  [\n    \"Belgium (België)\",\n    \"be\",\n    \"32\"\n  ],\n  [\n    \"Belize\",\n    \"bz\",\n    \"501\"\n  ],\n  [\n    \"Benin (Bénin)\",\n    \"bj\",\n    \"229\"\n  ],\n  [\n    \"Bermuda\",\n    \"bm\",\n    \"1\",\n    10,\n    [\"441\"]\n  ],\n  [\n    \"Bhutan (འབྲུག)\",\n    \"bt\",\n    \"975\"\n  ],\n  [\n    \"Bolivia\",\n    \"bo\",\n    \"591\"\n  ],\n  [\n    \"Bosnia and Herzegovina (Босна и Херцеговина)\",\n    \"ba\",\n    \"387\"\n  ],\n  [\n    \"Botswana\",\n    \"bw\",\n    \"267\"\n  ],\n  [\n    \"Brazil (Brasil)\",\n    \"br\",\n    \"55\"\n  ],\n  [\n    \"British Indian Ocean Territory\",\n    \"io\",\n    \"246\"\n  ],\n  [\n    \"British Virgin Islands\",\n    \"vg\",\n    \"1\",\n    11,\n    [\"284\"]\n  ],\n  [\n    \"Brunei\",\n    \"bn\",\n    \"673\"\n  ],\n  [\n    \"Bulgaria (България)\",\n    \"bg\",\n    \"359\"\n  ],\n  [\n    \"Burkina Faso\",\n    \"bf\",\n    \"226\"\n  ],\n  [\n    \"Burundi (Uburundi)\",\n    \"bi\",\n    \"257\"\n  ],\n  [\n    \"Cambodia (កម្ពុជា)\",\n    \"kh\",\n    \"855\"\n  ],\n  [\n    \"Cameroon (Cameroun)\",\n    \"cm\",\n    \"237\"\n  ],\n  [\n    \"Canada\",\n    \"ca\",\n    \"1\",\n    1,\n    [\"204\", \"226\", \"236\", \"249\", \"250\", \"263\", \"289\", \"306\", \"343\", \"354\", \"365\", \"367\", \"368\", \"382\", \"387\", \"403\", \"416\", \"418\", \"428\", \"431\", \"437\", \"438\", \"450\", \"584\", \"468\", \"474\", \"506\", \"514\", \"519\", \"548\", \"579\", \"581\", \"584\", \"587\", \"604\", \"613\", \"639\", \"647\", \"672\", \"683\", \"705\", \"709\", \"742\", \"753\", \"778\", \"780\", \"782\", \"807\", \"819\", \"825\", \"867\", \"873\", \"902\", \"905\"]\n  ],\n  [\n    \"Cape Verde (Kabu Verdi)\",\n    \"cv\",\n    \"238\"\n  ],\n  [\n    \"Caribbean Netherlands\",\n    \"bq\",\n    \"599\",\n    1,\n    [\"3\", \"4\", \"7\"]\n  ],\n  [\n    \"Cayman Islands\",\n    \"ky\",\n    \"1\",\n    12,\n    [\"345\"]\n  ],\n  [\n    \"Central African Republic (République centrafricaine)\",\n    \"cf\",\n    \"236\"\n  ],\n  [\n    \"Chad (Tchad)\",\n    \"td\",\n    \"235\"\n  ],\n  [\n    \"Chile\",\n    \"cl\",\n    \"56\"\n  ],\n  [\n    \"China (中国)\",\n    \"cn\",\n    \"86\"\n  ],\n  [\n    \"Christmas Island\",\n    \"cx\",\n    \"61\",\n    2,\n    [\"89164\"]\n  ],\n  [\n    \"Cocos (Keeling) Islands\",\n    \"cc\",\n    \"61\",\n    1,\n    [\"89162\"]\n  ],\n  [\n    \"Colombia\",\n    \"co\",\n    \"57\"\n  ],\n  [\n    \"Comoros (‫جزر القمر‬‎)\",\n    \"km\",\n    \"269\"\n  ],\n  [\n    \"Congo (DRC) (République démocratique du Congo)\",\n    \"cd\",\n    \"243\"\n  ],\n  [\n    \"Congo (Republic) (Congo-Brazzaville)\",\n    \"cg\",\n    \"242\"\n  ],\n  [\n    \"Cook Islands\",\n    \"ck\",\n    \"682\"\n  ],\n  [\n    \"Costa Rica\",\n    \"cr\",\n    \"506\"\n  ],\n  [\n    \"Côte d’Ivoire\",\n    \"ci\",\n    \"225\"\n  ],\n  [\n    \"Croatia (Hrvatska)\",\n    \"hr\",\n    \"385\"\n  ],\n  [\n    \"Cuba\",\n    \"cu\",\n    \"53\"\n  ],\n  [\n    \"Curaçao\",\n    \"cw\",\n    \"599\",\n    0\n  ],\n  [\n    \"Cyprus (Κύπρος)\",\n    \"cy\",\n    \"357\"\n  ],\n  [\n    \"Czech Republic (Česká republika)\",\n    \"cz\",\n    \"420\"\n  ],\n  [\n    \"Denmark (Danmark)\",\n    \"dk\",\n    \"45\"\n  ],\n  [\n    \"Djibouti\",\n    \"dj\",\n    \"253\"\n  ],\n  [\n    \"Dominica\",\n    \"dm\",\n    \"1\",\n    13,\n    [\"767\"]\n  ],\n  [\n    \"Dominican Republic (República Dominicana)\",\n    \"do\",\n    \"1\",\n    2,\n    [\"809\", \"829\", \"849\"]\n  ],\n  [\n    \"Ecuador\",\n    \"ec\",\n    \"593\"\n  ],\n  [\n    \"Egypt (‫مصر‬‎)\",\n    \"eg\",\n    \"20\"\n  ],\n  [\n    \"El Salvador\",\n    \"sv\",\n    \"503\"\n  ],\n  [\n    \"Equatorial Guinea (Guinea Ecuatorial)\",\n    \"gq\",\n    \"240\"\n  ],\n  [\n    \"Eritrea\",\n    \"er\",\n    \"291\"\n  ],\n  [\n    \"Estonia (Eesti)\",\n    \"ee\",\n    \"372\"\n  ],\n  [\n    \"Eswatini\",\n    \"sz\",\n    \"268\"\n  ],\n  [\n    \"Ethiopia\",\n    \"et\",\n    \"251\"\n  ],\n  [\n    \"Falkland Islands (Islas Malvinas)\",\n    \"fk\",\n    \"500\"\n  ],\n  [\n    \"Faroe Islands (Føroyar)\",\n    \"fo\",\n    \"298\"\n  ],\n  [\n    \"Fiji\",\n    \"fj\",\n    \"679\"\n  ],\n  [\n    \"Finland (Suomi)\",\n    \"fi\",\n    \"358\",\n    0\n  ],\n  [\n    \"France\",\n    \"fr\",\n    \"33\"\n  ],\n  [\n    \"French Guiana (Guyane française)\",\n    \"gf\",\n    \"594\"\n  ],\n  [\n    \"French Polynesia (Polynésie française)\",\n    \"pf\",\n    \"689\"\n  ],\n  [\n    \"Gabon\",\n    \"ga\",\n    \"241\"\n  ],\n  [\n    \"Gambia\",\n    \"gm\",\n    \"220\"\n  ],\n  [\n    \"Georgia (საქართველო)\",\n    \"ge\",\n    \"995\"\n  ],\n  [\n    \"Germany (Deutschland)\",\n    \"de\",\n    \"49\"\n  ],\n  [\n    \"Ghana (Gaana)\",\n    \"gh\",\n    \"233\"\n  ],\n  [\n    \"Gibraltar\",\n    \"gi\",\n    \"350\"\n  ],\n  [\n    \"Greece (Ελλάδα)\",\n    \"gr\",\n    \"30\"\n  ],\n  [\n    \"Greenland (Kalaallit Nunaat)\",\n    \"gl\",\n    \"299\"\n  ],\n  [\n    \"Grenada\",\n    \"gd\",\n    \"1\",\n    14,\n    [\"473\"]\n  ],\n  [\n    \"Guadeloupe\",\n    \"gp\",\n    \"590\",\n    0\n  ],\n  [\n    \"Guam\",\n    \"gu\",\n    \"1\",\n    15,\n    [\"671\"]\n  ],\n  [\n    \"Guatemala\",\n    \"gt\",\n    \"502\"\n  ],\n  [\n    \"Guernsey\",\n    \"gg\",\n    \"44\",\n    1,\n    [\"1481\", \"7781\", \"7839\", \"7911\"]\n  ],\n  [\n    \"Guinea (Guinée)\",\n    \"gn\",\n    \"224\"\n  ],\n  [\n    \"Guinea-Bissau (Guiné Bissau)\",\n    \"gw\",\n    \"245\"\n  ],\n  [\n    \"Guyana\",\n    \"gy\",\n    \"592\"\n  ],\n  [\n    \"Haiti\",\n    \"ht\",\n    \"509\"\n  ],\n  [\n    \"Honduras\",\n    \"hn\",\n    \"504\"\n  ],\n  [\n    \"Hong Kong (香港)\",\n    \"hk\",\n    \"852\"\n  ],\n  [\n    \"Hungary (Magyarország)\",\n    \"hu\",\n    \"36\"\n  ],\n  [\n    \"Iceland (Ísland)\",\n    \"is\",\n    \"354\"\n  ],\n  [\n    \"India (भारत)\",\n    \"in\",\n    \"91\"\n  ],\n  [\n    \"Indonesia\",\n    \"id\",\n    \"62\"\n  ],\n  [\n    \"Iran (‫ایران‬‎)\",\n    \"ir\",\n    \"98\"\n  ],\n  [\n    \"Iraq (‫العراق‬‎)\",\n    \"iq\",\n    \"964\"\n  ],\n  [\n    \"Ireland\",\n    \"ie\",\n    \"353\"\n  ],\n  [\n    \"Isle of Man\",\n    \"im\",\n    \"44\",\n    2,\n    [\"1624\", \"74576\", \"7524\", \"7924\", \"7624\"]\n  ],\n  [\n    \"Israel (‫ישראל‬‎)\",\n    \"il\",\n    \"972\"\n  ],\n  [\n    \"Italy (Italia)\",\n    \"it\",\n    \"39\",\n    0\n  ],\n  [\n    \"Jamaica\",\n    \"jm\",\n    \"1\",\n    4,\n    [\"876\", \"658\"]\n  ],\n  [\n    \"Japan (日本)\",\n    \"jp\",\n    \"81\"\n  ],\n  [\n    \"Jersey\",\n    \"je\",\n    \"44\",\n    3,\n    [\"1534\", \"7509\", \"7700\", \"7797\", \"7829\", \"7937\"]\n  ],\n  [\n    \"Jordan (‫الأردن‬‎)\",\n    \"jo\",\n    \"962\"\n  ],\n  [\n    \"Kazakhstan (Казахстан)\",\n    \"kz\",\n    \"7\",\n    1,\n    [\"33\", \"7\"]\n  ],\n  [\n    \"Kenya\",\n    \"ke\",\n    \"254\"\n  ],\n  [\n    \"Kiribati\",\n    \"ki\",\n    \"686\"\n  ],\n  [\n    \"Kosovo\",\n    \"xk\",\n    \"383\"\n  ],\n  [\n    \"Kuwait (‫الكويت‬‎)\",\n    \"kw\",\n    \"965\"\n  ],\n  [\n    \"Kyrgyzstan (Кыргызстан)\",\n    \"kg\",\n    \"996\"\n  ],\n  [\n    \"Laos (ລາວ)\",\n    \"la\",\n    \"856\"\n  ],\n  [\n    \"Latvia (Latvija)\",\n    \"lv\",\n    \"371\"\n  ],\n  [\n    \"Lebanon (‫لبنان‬‎)\",\n    \"lb\",\n    \"961\"\n  ],\n  [\n    \"Lesotho\",\n    \"ls\",\n    \"266\"\n  ],\n  [\n    \"Liberia\",\n    \"lr\",\n    \"231\"\n  ],\n  [\n    \"Libya (‫ليبيا‬‎)\",\n    \"ly\",\n    \"218\"\n  ],\n  [\n    \"Liechtenstein\",\n    \"li\",\n    \"423\"\n  ],\n  [\n    \"Lithuania (Lietuva)\",\n    \"lt\",\n    \"370\"\n  ],\n  [\n    \"Luxembourg\",\n    \"lu\",\n    \"352\"\n  ],\n  [\n    \"Macau (澳門)\",\n    \"mo\",\n    \"853\"\n  ],\n  [\n    \"Madagascar (Madagasikara)\",\n    \"mg\",\n    \"261\"\n  ],\n  [\n    \"Malawi\",\n    \"mw\",\n    \"265\"\n  ],\n  [\n    \"Malaysia\",\n    \"my\",\n    \"60\"\n  ],\n  [\n    \"Maldives\",\n    \"mv\",\n    \"960\"\n  ],\n  [\n    \"Mali\",\n    \"ml\",\n    \"223\"\n  ],\n  [\n    \"Malta\",\n    \"mt\",\n    \"356\"\n  ],\n  [\n    \"Marshall Islands\",\n    \"mh\",\n    \"692\"\n  ],\n  [\n    \"Martinique\",\n    \"mq\",\n    \"596\"\n  ],\n  [\n    \"Mauritania (‫موريتانيا‬‎)\",\n    \"mr\",\n    \"222\"\n  ],\n  [\n    \"Mauritius (Moris)\",\n    \"mu\",\n    \"230\"\n  ],\n  [\n    \"Mayotte\",\n    \"yt\",\n    \"262\",\n    1,\n    [\"269\", \"639\"]\n  ],\n  [\n    \"Mexico (México)\",\n    \"mx\",\n    \"52\"\n  ],\n  [\n    \"Micronesia\",\n    \"fm\",\n    \"691\"\n  ],\n  [\n    \"Moldova (Republica Moldova)\",\n    \"md\",\n    \"373\"\n  ],\n  [\n    \"Monaco\",\n    \"mc\",\n    \"377\"\n  ],\n  [\n    \"Mongolia (Монгол)\",\n    \"mn\",\n    \"976\"\n  ],\n  [\n    \"Montenegro (Crna Gora)\",\n    \"me\",\n    \"382\"\n  ],\n  [\n    \"Montserrat\",\n    \"ms\",\n    \"1\",\n    16,\n    [\"664\"]\n  ],\n  [\n    \"Morocco (‫المغرب‬‎)\",\n    \"ma\",\n    \"212\",\n    0\n  ],\n  [\n    \"Mozambique (Moçambique)\",\n    \"mz\",\n    \"258\"\n  ],\n  [\n    \"Myanmar (Burma) (မြန်မာ)\",\n    \"mm\",\n    \"95\"\n  ],\n  [\n    \"Namibia (Namibië)\",\n    \"na\",\n    \"264\"\n  ],\n  [\n    \"Nauru\",\n    \"nr\",\n    \"674\"\n  ],\n  [\n    \"Nepal (नेपाल)\",\n    \"np\",\n    \"977\"\n  ],\n  [\n    \"Netherlands (Nederland)\",\n    \"nl\",\n    \"31\"\n  ],\n  [\n    \"New Caledonia (Nouvelle-Calédonie)\",\n    \"nc\",\n    \"687\"\n  ],\n  [\n    \"New Zealand\",\n    \"nz\",\n    \"64\"\n  ],\n  [\n    \"Nicaragua\",\n    \"ni\",\n    \"505\"\n  ],\n  [\n    \"Niger (Nijar)\",\n    \"ne\",\n    \"227\"\n  ],\n  [\n    \"Nigeria\",\n    \"ng\",\n    \"234\"\n  ],\n  [\n    \"Niue\",\n    \"nu\",\n    \"683\"\n  ],\n  [\n    \"Norfolk Island\",\n    \"nf\",\n    \"672\"\n  ],\n  [\n    \"North Korea (조선 민주주의 인민 공화국)\",\n    \"kp\",\n    \"850\"\n  ],\n  [\n    \"North Macedonia (Северна Македонија)\",\n    \"mk\",\n    \"389\"\n  ],\n  [\n    \"Northern Mariana Islands\",\n    \"mp\",\n    \"1\",\n    17,\n    [\"670\"]\n  ],\n  [\n    \"Norway (Norge)\",\n    \"no\",\n    \"47\",\n    0\n  ],\n  [\n    \"Oman (‫عُمان‬‎)\",\n    \"om\",\n    \"968\"\n  ],\n  [\n    \"Pakistan (‫پاکستان‬‎)\",\n    \"pk\",\n    \"92\"\n  ],\n  [\n    \"Palau\",\n    \"pw\",\n    \"680\"\n  ],\n  [\n    \"Palestine (‫فلسطين‬‎)\",\n    \"ps\",\n    \"970\"\n  ],\n  [\n    \"Panama (Panamá)\",\n    \"pa\",\n    \"507\"\n  ],\n  [\n    \"Papua New Guinea\",\n    \"pg\",\n    \"675\"\n  ],\n  [\n    \"Paraguay\",\n    \"py\",\n    \"595\"\n  ],\n  [\n    \"Peru (Perú)\",\n    \"pe\",\n    \"51\"\n  ],\n  [\n    \"Philippines\",\n    \"ph\",\n    \"63\"\n  ],\n  [\n    \"Poland (Polska)\",\n    \"pl\",\n    \"48\"\n  ],\n  [\n    \"Portugal\",\n    \"pt\",\n    \"351\"\n  ],\n  [\n    \"Puerto Rico\",\n    \"pr\",\n    \"1\",\n    3,\n    [\"787\", \"939\"]\n  ],\n  [\n    \"Qatar (‫قطر‬‎)\",\n    \"qa\",\n    \"974\"\n  ],\n  [\n    \"Réunion (La Réunion)\",\n    \"re\",\n    \"262\",\n    0\n  ],\n  [\n    \"Romania (România)\",\n    \"ro\",\n    \"40\"\n  ],\n  [\n    \"Russia (Россия)\",\n    \"ru\",\n    \"7\",\n    0\n  ],\n  [\n    \"Rwanda\",\n    \"rw\",\n    \"250\"\n  ],\n  [\n    \"Saint Barthélemy\",\n    \"bl\",\n    \"590\",\n    1\n  ],\n  [\n    \"Saint Helena\",\n    \"sh\",\n    \"290\"\n  ],\n  [\n    \"Saint Kitts and Nevis\",\n    \"kn\",\n    \"1\",\n    18,\n    [\"869\"]\n  ],\n  [\n    \"Saint Lucia\",\n    \"lc\",\n    \"1\",\n    19,\n    [\"758\"]\n  ],\n  [\n    \"Saint Martin (Saint-Martin (partie française))\",\n    \"mf\",\n    \"590\",\n    2\n  ],\n  [\n    \"Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)\",\n    \"pm\",\n    \"508\"\n  ],\n  [\n    \"Saint Vincent and the Grenadines\",\n    \"vc\",\n    \"1\",\n    20,\n    [\"784\"]\n  ],\n  [\n    \"Samoa\",\n    \"ws\",\n    \"685\"\n  ],\n  [\n    \"San Marino\",\n    \"sm\",\n    \"378\"\n  ],\n  [\n    \"São Tomé and Príncipe (São Tomé e Príncipe)\",\n    \"st\",\n    \"239\"\n  ],\n  [\n    \"Saudi Arabia (‫المملكة العربية السعودية‬‎)\",\n    \"sa\",\n    \"966\"\n  ],\n  [\n    \"Senegal (Sénégal)\",\n    \"sn\",\n    \"221\"\n  ],\n  [\n    \"Serbia (Србија)\",\n    \"rs\",\n    \"381\"\n  ],\n  [\n    \"Seychelles\",\n    \"sc\",\n    \"248\"\n  ],\n  [\n    \"Sierra Leone\",\n    \"sl\",\n    \"232\"\n  ],\n  [\n    \"Singapore\",\n    \"sg\",\n    \"65\"\n  ],\n  [\n    \"Sint Maarten\",\n    \"sx\",\n    \"1\",\n    21,\n    [\"721\"]\n  ],\n  [\n    \"Slovakia (Slovensko)\",\n    \"sk\",\n    \"421\"\n  ],\n  [\n    \"Slovenia (Slovenija)\",\n    \"si\",\n    \"386\"\n  ],\n  [\n    \"Solomon Islands\",\n    \"sb\",\n    \"677\"\n  ],\n  [\n    \"Somalia (Soomaaliya)\",\n    \"so\",\n    \"252\"\n  ],\n  [\n    \"South Africa\",\n    \"za\",\n    \"27\"\n  ],\n  [\n    \"South Korea (대한민국)\",\n    \"kr\",\n    \"82\"\n  ],\n  [\n    \"South Sudan (‫جنوب السودان‬‎)\",\n    \"ss\",\n    \"211\"\n  ],\n  [\n    \"Spain (España)\",\n    \"es\",\n    \"34\"\n  ],\n  [\n    \"Sri Lanka (ශ්‍රී ලංකාව)\",\n    \"lk\",\n    \"94\"\n  ],\n  [\n    \"Sudan (‫السودان‬‎)\",\n    \"sd\",\n    \"249\"\n  ],\n  [\n    \"Suriname\",\n    \"sr\",\n    \"597\"\n  ],\n  [\n    \"Svalbard and Jan Mayen\",\n    \"sj\",\n    \"47\",\n    1,\n    [\"79\"]\n  ],\n  [\n    \"Sweden (Sverige)\",\n    \"se\",\n    \"46\"\n  ],\n  [\n    \"Switzerland (Schweiz)\",\n    \"ch\",\n    \"41\"\n  ],\n  [\n    \"Syria (‫سوريا‬‎)\",\n    \"sy\",\n    \"963\"\n  ],\n  [\n    \"Taiwan (台灣)\",\n    \"tw\",\n    \"886\"\n  ],\n  [\n    \"Tajikistan\",\n    \"tj\",\n    \"992\"\n  ],\n  [\n    \"Tanzania\",\n    \"tz\",\n    \"255\"\n  ],\n  [\n    \"Thailand (ไทย)\",\n    \"th\",\n    \"66\"\n  ],\n  [\n    \"Timor-Leste\",\n    \"tl\",\n    \"670\"\n  ],\n  [\n    \"Togo\",\n    \"tg\",\n    \"228\"\n  ],\n  [\n    \"Tokelau\",\n    \"tk\",\n    \"690\"\n  ],\n  [\n    \"Tonga\",\n    \"to\",\n    \"676\"\n  ],\n  [\n    \"Trinidad and Tobago\",\n    \"tt\",\n    \"1\",\n    22,\n    [\"868\"]\n  ],\n  [\n    \"Tunisia (‫تونس‬‎)\",\n    \"tn\",\n    \"216\"\n  ],\n  [\n    \"Turkey (Türkiye)\",\n    \"tr\",\n    \"90\"\n  ],\n  [\n    \"Turkmenistan\",\n    \"tm\",\n    \"993\"\n  ],\n  [\n    \"Turks and Caicos Islands\",\n    \"tc\",\n    \"1\",\n    23,\n    [\"649\"]\n  ],\n  [\n    \"Tuvalu\",\n    \"tv\",\n    \"688\"\n  ],\n  [\n    \"U.S. Virgin Islands\",\n    \"vi\",\n    \"1\",\n    24,\n    [\"340\"]\n  ],\n  [\n    \"Uganda\",\n    \"ug\",\n    \"256\"\n  ],\n  [\n    \"Ukraine (Україна)\",\n    \"ua\",\n    \"380\"\n  ],\n  [\n    \"United Arab Emirates (‫الإمارات العربية المتحدة‬‎)\",\n    \"ae\",\n    \"971\"\n  ],\n  [\n    \"United Kingdom\",\n    \"gb\",\n    \"44\",\n    0\n  ],\n  [\n    \"United States\",\n    \"us\",\n    \"1\",\n    0\n  ],\n  [\n    \"Uruguay\",\n    \"uy\",\n    \"598\"\n  ],\n  [\n    \"Uzbekistan (Oʻzbekiston)\",\n    \"uz\",\n    \"998\"\n  ],\n  [\n    \"Vanuatu\",\n    \"vu\",\n    \"678\"\n  ],\n  [\n    \"Vatican City (Città del Vaticano)\",\n    \"va\",\n    \"39\",\n    1,\n    [\"06698\"]\n  ],\n  [\n    \"Venezuela\",\n    \"ve\",\n    \"58\"\n  ],\n  [\n    \"Vietnam (Việt Nam)\",\n    \"vn\",\n    \"84\"\n  ],\n  [\n    \"Wallis and Futuna (Wallis-et-Futuna)\",\n    \"wf\",\n    \"681\"\n  ],\n  [\n    \"Western Sahara (‫الصحراء الغربية‬‎)\",\n    \"eh\",\n    \"212\",\n    1,\n    [\"5288\", \"5289\"]\n  ],\n  [\n    \"Yemen (‫اليمن‬‎)\",\n    \"ye\",\n    \"967\"\n  ],\n  [\n    \"Zambia\",\n    \"zm\",\n    \"260\"\n  ],\n  [\n    \"Zimbabwe\",\n    \"zw\",\n    \"263\"\n  ],\n  [\n    \"Åland Islands\",\n    \"ax\",\n    \"358\",\n    1,\n    [\"18\"]\n  ]\n], Sd = Ed.map(([e, d, t, n = 0, r = null]) => ({\n  name: e,\n  iso2: d.toUpperCase(),\n  dialCode: t,\n  priority: n,\n  areaCodes: r\n}));\nfunction _d() {\n  return fetch(\"https://ip2c.org/s\").then((e) => e.text()).then((e) => {\n    const d = (e || \"\").toString();\n    if (!d || d[0] !== \"1\")\n      throw new Error(\"unable to fetch the country\");\n    return d.substr(2, 2);\n  });\n}\nfunction Fd(e, d) {\n  if (e.setSelectionRange)\n    e.focus(), e.setSelectionRange(d, d);\n  else if (\"createTextRange\" in e && typeof e.createTextRange == \"function\") {\n    const t = e.createTextRange();\n    t.collapse(!0), t.moveEnd(\"character\", d), t.moveStart(\"character\", d), t.select();\n  }\n}\nconst Td = [\n  {\n    name: \"allCountries\",\n    type: Array,\n    default: Sd,\n    description: \"All countries that are used in <code>libphonenumber-js</code>, can be overridden by this prop\",\n    inDemo: !1\n  },\n  {\n    name: \"autoFormat\",\n    type: Boolean,\n    default: !0,\n    description: \"Auto update the input to the formatted phone number when it's valid\",\n    inDemo: !0\n  },\n  {\n    name: \"customValidate\",\n    type: [Boolean, RegExp],\n    default: !1,\n    description: \"Custom validation RegExp for input\",\n    inDemo: !1\n  },\n  {\n    name: \"defaultCountry\",\n    default: \"\",\n    type: [String, Number],\n    description: \"Default country (by iso2 or dialCode), will override the country fetched from IP address of user\",\n    inDemo: !1\n  },\n  {\n    name: \"disabled\",\n    default: !1,\n    type: Boolean,\n    description: \"Disable <code>vue-tel-input</code>, including the input & flag dropdown\",\n    inDemo: !1\n  },\n  {\n    name: \"autoDefaultCountry\",\n    default: !0,\n    type: Boolean,\n    description: \"To fetch default country based on IP address of user\",\n    inDemo: !1\n  },\n  {\n    name: \"dropdownOptions\",\n    type: Object,\n    description: \"Options for dropdown, see below\",\n    inDemo: !1\n  },\n  {\n    name: \"dropdownOptions.disabled\",\n    default: !1,\n    type: Boolean,\n    description: \"Disable dropdown\",\n    inDemo: !1\n  },\n  {\n    name: \"dropdownOptions.showDialCodeInList\",\n    default: !0,\n    type: Boolean,\n    description: \"Show dial code in the dropdown list\",\n    inDemo: !0\n  },\n  {\n    name: \"dropdownOptions.showDialCodeInSelection\",\n    default: !1,\n    type: Boolean,\n    description: \"Show dial code in the dropdown selection\",\n    inDemo: !0\n  },\n  {\n    name: \"dropdownOptions.showFlags\",\n    default: !0,\n    type: Boolean,\n    description: \"Show flags in the dropdown selection and list\",\n    inDemo: !0\n  },\n  {\n    name: \"dropdownOptions.showSearchBox\",\n    default: !1,\n    type: Boolean,\n    description: \"Show country search box\",\n    inDemo: !0\n  },\n  {\n    name: \"dropdownOptions.searchBoxPlaceholder\",\n    default: \"\",\n    type: String,\n    description: \"Placeholder for the search box\",\n    inDemo: !1\n  },\n  {\n    name: \"dropdownOptions.tabindex\",\n    default: 0,\n    type: Number,\n    description: \"Native dropdown <code>tabindex</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"ignoredCountries\",\n    default: [],\n    type: Array,\n    description: \"List of countries will NOT be shown on the dropdown\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions\",\n    type: Object,\n    description: \"Options for input, see below\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.autocomplete\",\n    type: String,\n    default: \"on\",\n    description: \"Native input <code>autocomplete</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.autofocus\",\n    type: Boolean,\n    default: !1,\n    description: \"Native input <code>autofocus</code> attribute\",\n    inDemo: !1\n  },\n  // {\n  //   name: 'inputOptions.dynamicPlaceholder',\n  //   default: false,\n  //   type: Boolean,\n  //   description: 'Placeholder as a sample phone number in the current country',\n  //   inDemo: false,\n  // },\n  {\n    name: \"inputOptions.aria-describedby\",\n    default: \"\",\n    type: String,\n    description: \"Native input <code>aria-describedby</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.id\",\n    default: \"\",\n    type: String,\n    description: \"Native input <code>id</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.maxlength\",\n    default: 25,\n    type: Number,\n    description: \"Native input <code>maxlength</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.name\",\n    default: \"telephone\",\n    type: String,\n    description: \"Native input <code>name</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.showDialCode\",\n    default: !1,\n    type: Boolean,\n    description: \"Show dial code in input\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.placeholder\",\n    default: \"Enter a phone number\",\n    type: String,\n    description: \"Placeholder for the input\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.readonly\",\n    default: !1,\n    type: Boolean,\n    description: \"Native input <code>readonly</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.required\",\n    default: !1,\n    type: Boolean,\n    description: \"Native input <code>required</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.tabindex\",\n    default: 0,\n    type: Number,\n    description: \"Native input <code>tabindex</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.type\",\n    default: \"tel\",\n    type: String,\n    description: \"Native input <code>type</code> attribute\",\n    inDemo: !1\n  },\n  {\n    name: \"inputOptions.styleClasses\",\n    default: \"\",\n    type: [String, Array, Object],\n    description: \"Custom classes for the <code>input</code>\",\n    inDemo: !1\n  },\n  {\n    name: \"invalidMsg\",\n    default: \"\",\n    type: String,\n    description: \"\",\n    inDemo: !1\n  },\n  {\n    name: \"mode\",\n    default: \"auto\",\n    type: String,\n    description: \"Allowed values: <code>'auto'</code> (Default set by phone),  <code>'international'</code> (Format number with the dial code i.e. + 61), <code>'national'</code> (Format number without dial code i.e. 0321232)\",\n    inDemo: !0,\n    options: [\"auto\", \"national\", \"international\"]\n  },\n  {\n    name: \"onlyCountries\",\n    default: [],\n    type: Array,\n    description: \"List of countries will be shown on the dropdown\",\n    inDemo: !1\n  },\n  {\n    name: \"preferredCountries\",\n    default: [],\n    type: Array,\n    description: \"Preferred countries list, will be on top of the dropdown\",\n    inDemo: !1\n  },\n  {\n    name: \"styleClasses\",\n    default: \"\",\n    type: [String, Array, Object],\n    description: \"Custom classes for the wrapper\",\n    inDemo: !1\n  },\n  {\n    name: \"validCharactersOnly\",\n    default: !1,\n    type: Boolean,\n    description: \"Only allow valid characters in a phone number (will also verify in <code>mounted</code>, so phone number with invalid characters will be shown as an empty string)\",\n    inDemo: !1\n  }\n], Ze = [...Td].reduce((e, d) => {\n  if (d.name.includes(\".\")) {\n    const [t, n] = d.name.split(\".\");\n    e[t] ? Object.assign(e[t], { [n]: d.default }) : Object.assign(e, { [t]: { [n]: d.default } });\n  } else\n    Object.assign(e, { [d.name]: d.default });\n  return e;\n}, {}), re = {\n  options: { ...Ze }\n};\nfunction m(e) {\n  const d = re.options[e];\n  return typeof d > \"u\" ? re.options[e] : d;\n}\nfunction M(e) {\n  return e == null ? void 0 : e.toLowerCase();\n}\nfunction L(e) {\n  return e == null ? void 0 : e.toUpperCase();\n}\nconst xd = { version: 4, country_calling_codes: { 1: [\"US\", \"AG\", \"AI\", \"AS\", \"BB\", \"BM\", \"BS\", \"CA\", \"DM\", \"DO\", \"GD\", \"GU\", \"JM\", \"KN\", \"KY\", \"LC\", \"MP\", \"MS\", \"PR\", \"SX\", \"TC\", \"TT\", \"VC\", \"VG\", \"VI\"], 7: [\"RU\", \"KZ\"], 20: [\"EG\"], 27: [\"ZA\"], 30: [\"GR\"], 31: [\"NL\"], 32: [\"BE\"], 33: [\"FR\"], 34: [\"ES\"], 36: [\"HU\"], 39: [\"IT\", \"VA\"], 40: [\"RO\"], 41: [\"CH\"], 43: [\"AT\"], 44: [\"GB\", \"GG\", \"IM\", \"JE\"], 45: [\"DK\"], 46: [\"SE\"], 47: [\"NO\", \"SJ\"], 48: [\"PL\"], 49: [\"DE\"], 51: [\"PE\"], 52: [\"MX\"], 53: [\"CU\"], 54: [\"AR\"], 55: [\"BR\"], 56: [\"CL\"], 57: [\"CO\"], 58: [\"VE\"], 60: [\"MY\"], 61: [\"AU\", \"CC\", \"CX\"], 62: [\"ID\"], 63: [\"PH\"], 64: [\"NZ\"], 65: [\"SG\"], 66: [\"TH\"], 81: [\"JP\"], 82: [\"KR\"], 84: [\"VN\"], 86: [\"CN\"], 90: [\"TR\"], 91: [\"IN\"], 92: [\"PK\"], 93: [\"AF\"], 94: [\"LK\"], 95: [\"MM\"], 98: [\"IR\"], 211: [\"SS\"], 212: [\"MA\", \"EH\"], 213: [\"DZ\"], 216: [\"TN\"], 218: [\"LY\"], 220: [\"GM\"], 221: [\"SN\"], 222: [\"MR\"], 223: [\"ML\"], 224: [\"GN\"], 225: [\"CI\"], 226: [\"BF\"], 227: [\"NE\"], 228: [\"TG\"], 229: [\"BJ\"], 230: [\"MU\"], 231: [\"LR\"], 232: [\"SL\"], 233: [\"GH\"], 234: [\"NG\"], 235: [\"TD\"], 236: [\"CF\"], 237: [\"CM\"], 238: [\"CV\"], 239: [\"ST\"], 240: [\"GQ\"], 241: [\"GA\"], 242: [\"CG\"], 243: [\"CD\"], 244: [\"AO\"], 245: [\"GW\"], 246: [\"IO\"], 247: [\"AC\"], 248: [\"SC\"], 249: [\"SD\"], 250: [\"RW\"], 251: [\"ET\"], 252: [\"SO\"], 253: [\"DJ\"], 254: [\"KE\"], 255: [\"TZ\"], 256: [\"UG\"], 257: [\"BI\"], 258: [\"MZ\"], 260: [\"ZM\"], 261: [\"MG\"], 262: [\"RE\", \"YT\"], 263: [\"ZW\"], 264: [\"NA\"], 265: [\"MW\"], 266: [\"LS\"], 267: [\"BW\"], 268: [\"SZ\"], 269: [\"KM\"], 290: [\"SH\", \"TA\"], 291: [\"ER\"], 297: [\"AW\"], 298: [\"FO\"], 299: [\"GL\"], 350: [\"GI\"], 351: [\"PT\"], 352: [\"LU\"], 353: [\"IE\"], 354: [\"IS\"], 355: [\"AL\"], 356: [\"MT\"], 357: [\"CY\"], 358: [\"FI\", \"AX\"], 359: [\"BG\"], 370: [\"LT\"], 371: [\"LV\"], 372: [\"EE\"], 373: [\"MD\"], 374: [\"AM\"], 375: [\"BY\"], 376: [\"AD\"], 377: [\"MC\"], 378: [\"SM\"], 380: [\"UA\"], 381: [\"RS\"], 382: [\"ME\"], 383: [\"XK\"], 385: [\"HR\"], 386: [\"SI\"], 387: [\"BA\"], 389: [\"MK\"], 420: [\"CZ\"], 421: [\"SK\"], 423: [\"LI\"], 500: [\"FK\"], 501: [\"BZ\"], 502: [\"GT\"], 503: [\"SV\"], 504: [\"HN\"], 505: [\"NI\"], 506: [\"CR\"], 507: [\"PA\"], 508: [\"PM\"], 509: [\"HT\"], 590: [\"GP\", \"BL\", \"MF\"], 591: [\"BO\"], 592: [\"GY\"], 593: [\"EC\"], 594: [\"GF\"], 595: [\"PY\"], 596: [\"MQ\"], 597: [\"SR\"], 598: [\"UY\"], 599: [\"CW\", \"BQ\"], 670: [\"TL\"], 672: [\"NF\"], 673: [\"BN\"], 674: [\"NR\"], 675: [\"PG\"], 676: [\"TO\"], 677: [\"SB\"], 678: [\"VU\"], 679: [\"FJ\"], 680: [\"PW\"], 681: [\"WF\"], 682: [\"CK\"], 683: [\"NU\"], 685: [\"WS\"], 686: [\"KI\"], 687: [\"NC\"], 688: [\"TV\"], 689: [\"PF\"], 690: [\"TK\"], 691: [\"FM\"], 692: [\"MH\"], 850: [\"KP\"], 852: [\"HK\"], 853: [\"MO\"], 855: [\"KH\"], 856: [\"LA\"], 880: [\"BD\"], 886: [\"TW\"], 960: [\"MV\"], 961: [\"LB\"], 962: [\"JO\"], 963: [\"SY\"], 964: [\"IQ\"], 965: [\"KW\"], 966: [\"SA\"], 967: [\"YE\"], 968: [\"OM\"], 970: [\"PS\"], 971: [\"AE\"], 972: [\"IL\"], 973: [\"BH\"], 974: [\"QA\"], 975: [\"BT\"], 976: [\"MN\"], 977: [\"NP\"], 992: [\"TJ\"], 993: [\"TM\"], 994: [\"AZ\"], 995: [\"GE\"], 996: [\"KG\"], 998: [\"UZ\"] }, countries: { AC: [\"247\", \"00\", \"(?:[01589]\\\\d|[46])\\\\d{4}\", [5, 6]], AD: [\"376\", \"00\", \"(?:1|6\\\\d)\\\\d{7}|[135-9]\\\\d{5}\", [6, 8, 9], [[\"(\\\\d{3})(\\\\d{3})\", \"$1 $2\", [\"[135-9]\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"1\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6\"]]]], AE: [\"971\", \"00\", \"(?:[4-7]\\\\d|9[0-689])\\\\d{7}|800\\\\d{2,9}|[2-4679]\\\\d{7}\", [5, 6, 7, 8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{2,9})\", \"$1 $2\", [\"60|8\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[236]|[479][2-8]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d)(\\\\d{5})\", \"$1 $2 $3\", [\"[479]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"5\"], \"0$1\"]], \"0\"], AF: [\"93\", \"00\", \"[2-7]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-7]\"], \"0$1\"]], \"0\"], AG: [\"1\", \"011\", \"(?:268|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([457]\\\\d{6})$|1\", \"268$1\", 0, \"268\"], AI: [\"1\", \"011\", \"(?:264|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2457]\\\\d{6})$|1\", \"264$1\", 0, \"264\"], AL: [\"355\", \"00\", \"(?:700\\\\d\\\\d|900)\\\\d{3}|8\\\\d{5,7}|(?:[2-5]|6\\\\d)\\\\d{7}\", [6, 7, 8, 9], [[\"(\\\\d{3})(\\\\d{3,4})\", \"$1 $2\", [\"80|9\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"4[2-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2358][2-5]|4\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[23578]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"6\"], \"0$1\"]], \"0\"], AM: [\"374\", \"00\", \"(?:[1-489]\\\\d|55|60|77)\\\\d{6}\", [8], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"[89]0\"], \"0 $1\"], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"2|3[12]\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"1|47\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[3-9]\"], \"0$1\"]], \"0\"], AO: [\"244\", \"00\", \"[29]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[29]\"]]]], AR: [\"54\", \"00\", \"(?:11|[89]\\\\d\\\\d)\\\\d{8}|[2368]\\\\d{9}\", [10, 11], [[\"(\\\\d{4})(\\\\d{2})(\\\\d{4})\", \"$1 $2-$3\", [\"2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9])\", \"2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8]))|2(?:2[24-9]|3[1-59]|47)\", \"2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5[56][46]|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]\", \"2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|58|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|54(?:4|5[13-7]|6[89])|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:454|85[56])[46]|3(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]\"], \"0$1\", 1], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2-$3\", [\"1\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[68]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2-$3\", [\"[23]\"], \"0$1\", 1], [\"(\\\\d)(\\\\d{4})(\\\\d{2})(\\\\d{4})\", \"$2 15-$3-$4\", [\"9(?:2[2-469]|3[3-578])\", \"9(?:2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9]))\", \"9(?:2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8])))|92(?:2[24-9]|3[1-59]|47)\", \"9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5(?:[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]\", \"9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|5(?:4(?:4|5[13-7]|6[89])|[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]\"], \"0$1\", 0, \"$1 $2 $3-$4\"], [\"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$2 15-$3-$4\", [\"91\"], \"0$1\", 0, \"$1 $2 $3-$4\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\", \"$1-$2-$3\", [\"8\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$2 15-$3-$4\", [\"9\"], \"0$1\", 0, \"$1 $2 $3-$4\"]], \"0\", 0, \"0?(?:(11|2(?:2(?:02?|[13]|2[13-79]|4[1-6]|5[2457]|6[124-8]|7[1-4]|8[13-6]|9[1267])|3(?:02?|1[467]|2[03-6]|3[13-8]|[49][2-6]|5[2-8]|[67])|4(?:7[3-578]|9)|6(?:[0136]|2[24-6]|4[6-8]?|5[15-8])|80|9(?:0[1-3]|[19]|2\\\\d|3[1-6]|4[02568]?|5[2-4]|6[2-46]|72?|8[23]?))|3(?:3(?:2[79]|6|8[2578])|4(?:0[0-24-9]|[12]|3[5-8]?|4[24-7]|5[4-68]?|6[02-9]|7[126]|8[2379]?|9[1-36-8])|5(?:1|2[1245]|3[237]?|4[1-46-9]|6[2-4]|7[1-6]|8[2-5]?)|6[24]|7(?:[069]|1[1568]|2[15]|3[145]|4[13]|5[14-8]|7[2-57]|8[126])|8(?:[01]|2[15-7]|3[2578]?|4[13-6]|5[4-8]?|6[1-357-9]|7[36-8]?|8[5-8]?|9[124])))15)?\", \"9$1\"], AS: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|684|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([267]\\\\d{6})$|1\", \"684$1\", 0, \"684\"], AT: [\"43\", \"00\", \"1\\\\d{3,12}|2\\\\d{6,12}|43(?:(?:0\\\\d|5[02-9])\\\\d{3,9}|2\\\\d{4,5}|[3467]\\\\d{4}|8\\\\d{4,6}|9\\\\d{4,7})|5\\\\d{4,12}|8\\\\d{7,12}|9\\\\d{8,12}|(?:[367]\\\\d|4[0-24-9])\\\\d{4,11}\", [4, 5, 6, 7, 8, 9, 10, 11, 12, 13], [[\"(\\\\d)(\\\\d{3,12})\", \"$1 $2\", [\"1(?:11|[2-9])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})\", \"$1 $2\", [\"517\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3,5})\", \"$1 $2\", [\"5[079]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3,10})\", \"$1 $2\", [\"(?:31|4)6|51|6(?:5[0-3579]|[6-9])|7(?:20|32|8)|[89]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3,9})\", \"$1 $2\", [\"[2-467]|5[2-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"5\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4,7})\", \"$1 $2 $3\", [\"5\"], \"0$1\"]], \"0\"], AU: [\"61\", \"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\", \"1(?:[0-79]\\\\d{7}(?:\\\\d(?:\\\\d{2})?)?|8[0-24-9]\\\\d{7})|[2-478]\\\\d{8}|1\\\\d{4,7}\", [5, 6, 7, 8, 9, 10, 12], [[\"(\\\\d{2})(\\\\d{3,4})\", \"$1 $2\", [\"16\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2,4})\", \"$1 $2 $3\", [\"16\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"14|4\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[2378]\"], \"(0$1)\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1(?:30|[89])\"]]], \"0\", 0, \"(183[12])|0\", 0, 0, 0, [[\"(?:(?:2(?:[0-26-9]\\\\d|3[0-8]|4[02-9]|5[0135-9])|3(?:[0-3589]\\\\d|4[0-578]|6[1-9]|7[0-35-9])|7(?:[013-57-9]\\\\d|2[0-8]))\\\\d{3}|8(?:51(?:0(?:0[03-9]|[12479]\\\\d|3[2-9]|5[0-8]|6[1-9]|8[0-7])|1(?:[0235689]\\\\d|1[0-69]|4[0-589]|7[0-47-9])|2(?:0[0-79]|[18][13579]|2[14-9]|3[0-46-9]|[4-6]\\\\d|7[89]|9[0-4]))|(?:6[0-8]|[78]\\\\d)\\\\d{3}|9(?:[02-9]\\\\d{3}|1(?:(?:[0-58]\\\\d|6[0135-9])\\\\d|7(?:0[0-24-9]|[1-9]\\\\d)|9(?:[0-46-9]\\\\d|5[0-79])))))\\\\d{3}\", [9]], [\"4(?:(?:79|94)[01]|83[0-389])\\\\d{5}|4(?:[0-3]\\\\d|4[047-9]|5[0-25-9]|6[0-26-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\", [9]], [\"180(?:0\\\\d{3}|2)\\\\d{3}\", [7, 10]], [\"190[0-26]\\\\d{6}\", [10]], 0, 0, 0, [\"163\\\\d{2,6}\", [5, 6, 7, 8, 9]], [\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\", [9]], [\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\", [6, 8, 10, 12]]], \"0011\"], AW: [\"297\", \"00\", \"(?:[25-79]\\\\d\\\\d|800)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[25-9]\"]]]], AX: [\"358\", \"00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))\", \"2\\\\d{4,9}|35\\\\d{4,5}|(?:60\\\\d\\\\d|800)\\\\d{4,6}|7\\\\d{5,11}|(?:[14]\\\\d|3[0-46-9]|50)\\\\d{4,8}\", [5, 6, 7, 8, 9, 10, 11, 12], 0, \"0\", 0, 0, 0, 0, \"18\", 0, \"00\"], AZ: [\"994\", \"00\", \"365\\\\d{6}|(?:[124579]\\\\d|60|88)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"90\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"1[28]|2|365|46\", \"1[28]|2|365[45]|46\", \"1[28]|2|365(?:4|5[02])|46\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[13-9]\"], \"0$1\"]], \"0\"], BA: [\"387\", \"00\", \"6\\\\d{8}|(?:[35689]\\\\d|49|70)\\\\d{6}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6[1-3]|[7-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2-$3\", [\"[3-5]|6[56]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"6\"], \"0$1\"]], \"0\"], BB: [\"1\", \"011\", \"(?:246|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"246$1\", 0, \"246\"], BD: [\"880\", \"00\", \"[1-469]\\\\d{9}|8[0-79]\\\\d{7,8}|[2-79]\\\\d{8}|[2-9]\\\\d{7}|[3-9]\\\\d{6}|[57-9]\\\\d{5}\", [6, 7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{4,6})\", \"$1-$2\", [\"31[5-8]|[459]1\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3,7})\", \"$1-$2\", [\"3(?:[67]|8[013-9])|4(?:6[168]|7|[89][18])|5(?:6[128]|9)|6(?:[15]|28|4[14])|7[2-589]|8(?:0[014-9]|[12])|9[358]|(?:3[2-5]|4[235]|5[2-578]|6[0389]|76|8[3-7]|9[24])1|(?:44|66)[01346-9]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3,6})\", \"$1-$2\", [\"[13-9]|22\"], \"0$1\"], [\"(\\\\d)(\\\\d{7,8})\", \"$1-$2\", [\"2\"], \"0$1\"]], \"0\"], BE: [\"32\", \"00\", \"4\\\\d{8}|[1-9]\\\\d{7}\", [8, 9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"(?:80|9)0\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[239]|4[23]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[15-8]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"4\"], \"0$1\"]], \"0\"], BF: [\"226\", \"00\", \"[025-7]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[025-7]\"]]]], BG: [\"359\", \"00\", \"00800\\\\d{7}|[2-7]\\\\d{6,7}|[89]\\\\d{6,8}|2\\\\d{5}\", [6, 7, 8, 9, 12], [[\"(\\\\d)(\\\\d)(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"43[1-6]|70[1-9]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\", \"$1 $2 $3\", [\"[356]|4[124-7]|7[1-9]|8[1-6]|9[1-7]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"(?:70|8)0\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})\", \"$1 $2 $3\", [\"43[1-7]|7\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[48]|9[08]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"9\"], \"0$1\"]], \"0\"], BH: [\"973\", \"00\", \"[136-9]\\\\d{7}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[13679]|8[02-4679]\"]]]], BI: [\"257\", \"00\", \"(?:[267]\\\\d|31)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2367]\"]]]], BJ: [\"229\", \"00\", \"[24-689]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[24-689]\"]]]], BL: [\"590\", \"00\", \"590\\\\d{6}|(?:69|80|9\\\\d)\\\\d{7}\", [9], 0, \"0\", 0, 0, 0, 0, 0, [[\"590(?:2[7-9]|3[3-7]|5[12]|87)\\\\d{4}\"], [\"69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5]))\\\\d{4}\"], [\"80[0-5]\\\\d{6}\"], 0, 0, 0, 0, 0, [\"9(?:(?:395|76[018])\\\\d|475[0-5])\\\\d{4}\"]]], BM: [\"1\", \"011\", \"(?:441|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"441$1\", 0, \"441\"], BN: [\"673\", \"00\", \"[2-578]\\\\d{6}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-578]\"]]]], BO: [\"591\", \"00(?:1\\\\d)?\", \"(?:[2-467]\\\\d\\\\d|8001)\\\\d{5}\", [8, 9], [[\"(\\\\d)(\\\\d{7})\", \"$1 $2\", [\"[23]|4[46]\"]], [\"(\\\\d{8})\", \"$1\", [\"[67]\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]]], \"0\", 0, \"0(1\\\\d)?\"], BQ: [\"599\", \"00\", \"(?:[34]1|7\\\\d)\\\\d{5}\", [7], 0, 0, 0, 0, 0, 0, \"[347]\"], BR: [\"55\", \"00(?:1[245]|2[1-35]|31|4[13]|[56]5|99)\", \"(?:[1-46-9]\\\\d\\\\d|5(?:[0-46-9]\\\\d|5[0-46-9]))\\\\d{8}|[1-9]\\\\d{9}|[3589]\\\\d{8}|[34]\\\\d{7}\", [8, 9, 10, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1-$2\", [\"300|4(?:0[02]|37)\", \"4(?:02|37)0|[34]00\"]], [\"(\\\\d{3})(\\\\d{2,3})(\\\\d{4})\", \"$1 $2 $3\", [\"(?:[358]|90)0\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2-$3\", [\"(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])[2-57]\"], \"($1)\"], [\"(\\\\d{2})(\\\\d{5})(\\\\d{4})\", \"$1 $2-$3\", [\"[16][1-9]|[2-57-9]\"], \"($1)\"]], \"0\", 0, \"(?:0|90)(?:(1[245]|2[1-35]|31|4[13]|[56]5|99)(\\\\d{10,11}))?\", \"$2\"], BS: [\"1\", \"011\", \"(?:242|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([3-8]\\\\d{6})$|1\", \"242$1\", 0, \"242\"], BT: [\"975\", \"00\", \"[17]\\\\d{7}|[2-8]\\\\d{6}\", [7, 8], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-68]|7[246]\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"1[67]|7\"]]]], BW: [\"267\", \"00\", \"(?:0800|(?:[37]|800)\\\\d)\\\\d{6}|(?:[2-6]\\\\d|90)\\\\d{5}\", [7, 8, 10], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"90\"]], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[24-6]|3[15-9]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[37]\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"0\"]], [\"(\\\\d{3})(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]]]], BY: [\"375\", \"810\", \"(?:[12]\\\\d|33|44|902)\\\\d{7}|8(?:0[0-79]\\\\d{5,7}|[1-7]\\\\d{9})|8(?:1[0-489]|[5-79]\\\\d)\\\\d{7}|8[1-79]\\\\d{6,7}|8[0-79]\\\\d{5}|8\\\\d{5}\", [6, 7, 8, 9, 10, 11], [[\"(\\\\d{3})(\\\\d{3})\", \"$1 $2\", [\"800\"], \"8 $1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2,4})\", \"$1 $2 $3\", [\"800\"], \"8 $1\"], [\"(\\\\d{4})(\\\\d{2})(\\\\d{3})\", \"$1 $2-$3\", [\"1(?:5[169]|6[3-5]|7[179])|2(?:1[35]|2[34]|3[3-5])\", \"1(?:5[169]|6(?:3[1-3]|4|5[125])|7(?:1[3-9]|7[0-24-6]|9[2-7]))|2(?:1[35]|2[34]|3[3-5])\"], \"8 0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"1(?:[56]|7[467])|2[1-3]\"], \"8 0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"[1-4]\"], \"8 0$1\"], [\"(\\\\d{3})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"], \"8 $1\"]], \"8\", 0, \"0|80?\", 0, 0, 0, 0, \"8~10\"], BZ: [\"501\", \"00\", \"(?:0800\\\\d|[2-8])\\\\d{6}\", [7, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[2-8]\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{4})(\\\\d{3})\", \"$1-$2-$3-$4\", [\"0\"]]]], CA: [\"1\", \"011\", \"(?:[2-8]\\\\d|90)\\\\d{8}|3\\\\d{6}\", [7, 10], 0, \"1\", 0, 0, 0, 0, 0, [[\"(?:2(?:04|[23]6|[48]9|50|63)|3(?:06|43|54|6[578]|82)|4(?:03|1[68]|[26]8|3[178]|50|74)|5(?:06|1[49]|48|79|8[147])|6(?:04|[18]3|39|47|72)|7(?:0[59]|42|53|78|8[02])|8(?:[06]7|19|25|73)|90[25])[2-9]\\\\d{6}\", [10]], [\"\", [10]], [\"8(?:00|33|44|55|66|77|88)[2-9]\\\\d{6}\", [10]], [\"900[2-9]\\\\d{6}\", [10]], [\"52(?:3(?:[2-46-9][02-9]\\\\d|5(?:[02-46-9]\\\\d|5[0-46-9]))|4(?:[2-478][02-9]\\\\d|5(?:[034]\\\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\\\d)|9(?:[05-9]\\\\d|2[0-5]|49)))\\\\d{4}|52[34][2-9]1[02-9]\\\\d{4}|(?:5(?:00|2[125-9]|33|44|66|77|88)|622)[2-9]\\\\d{6}\", [10]], 0, [\"310\\\\d{4}\", [7]], 0, [\"600[2-9]\\\\d{6}\", [10]]]], CC: [\"61\", \"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\", \"1(?:[0-79]\\\\d{8}(?:\\\\d{2})?|8[0-24-9]\\\\d{7})|[148]\\\\d{8}|1\\\\d{5,7}\", [6, 7, 8, 9, 10, 12], 0, \"0\", 0, \"([59]\\\\d{7})$|0\", \"8$1\", 0, 0, [[\"8(?:51(?:0(?:02|31|60|89)|1(?:18|76)|223)|91(?:0(?:1[0-2]|29)|1(?:[28]2|50|79)|2(?:10|64)|3(?:[06]8|22)|4[29]8|62\\\\d|70[23]|959))\\\\d{3}\", [9]], [\"4(?:(?:79|94)[01]|83[0-389])\\\\d{5}|4(?:[0-3]\\\\d|4[047-9]|5[0-25-9]|6[0-26-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\", [9]], [\"180(?:0\\\\d{3}|2)\\\\d{3}\", [7, 10]], [\"190[0-26]\\\\d{6}\", [10]], 0, 0, 0, 0, [\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\", [9]], [\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\", [6, 8, 10, 12]]], \"0011\"], CD: [\"243\", \"00\", \"[189]\\\\d{8}|[1-68]\\\\d{6}\", [7, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"88\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"[1-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"]], \"0\"], CF: [\"236\", \"00\", \"(?:[27]\\\\d{3}|8776)\\\\d{4}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[278]\"]]]], CG: [\"242\", \"00\", \"222\\\\d{6}|(?:0\\\\d|80)\\\\d{7}\", [9], [[\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[02]\"]]]], CH: [\"41\", \"00\", \"8\\\\d{11}|[2-9]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8[047]|90\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-79]|81\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"8\"], \"0$1\"]], \"0\"], CI: [\"225\", \"00\", \"[02]\\\\d{9}\", [10], [[\"(\\\\d{2})(\\\\d{2})(\\\\d)(\\\\d{5})\", \"$1 $2 $3 $4\", [\"2\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3 $4\", [\"0\"]]]], CK: [\"682\", \"00\", \"[2-578]\\\\d{4}\", [5], [[\"(\\\\d{2})(\\\\d{3})\", \"$1 $2\", [\"[2-578]\"]]]], CL: [\"56\", \"(?:0|1(?:1[0-69]|2[02-5]|5[13-58]|69|7[0167]|8[018]))0\", \"12300\\\\d{6}|6\\\\d{9,10}|[2-9]\\\\d{8}\", [9, 10, 11], [[\"(\\\\d{5})(\\\\d{4})\", \"$1 $2\", [\"219\", \"2196\"], \"($1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"44\"]], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"2[1-36]\"], \"($1)\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"9[2-9]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"3[2-5]|[47]|5[1-3578]|6[13-57]|8(?:0[1-9]|[1-9])\"], \"($1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"60|8\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"60\"]]]], CM: [\"237\", \"00\", \"[26]\\\\d{8}|88\\\\d{6,7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"88\"]], [\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"[26]|88\"]]]], CN: [\"86\", \"00|1(?:[12]\\\\d|79)\\\\d\\\\d00\", \"1[127]\\\\d{8,9}|2\\\\d{9}(?:\\\\d{2})?|[12]\\\\d{6,7}|86\\\\d{6}|(?:1[03-689]\\\\d|6)\\\\d{7,9}|(?:[3-579]\\\\d|8[0-57-9])\\\\d{6,9}\", [7, 8, 9, 10, 11, 12], [[\"(\\\\d{2})(\\\\d{5,6})\", \"$1 $2\", [\"(?:10|2[0-57-9])[19]\", \"(?:10|2[0-57-9])(?:10|9[56])\", \"10(?:10|9[56])|2[0-57-9](?:100|9[56])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5,6})\", \"$1 $2\", [\"3(?:[157]|35|49|9[1-68])|4(?:[17]|2[179]|6[47-9]|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]|4[13]|5[1-5])|(?:4[35]|59|85)[1-9]\", \"(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))[19]\", \"85[23](?:10|95)|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))(?:10|9[56])\", \"85[23](?:100|95)|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))(?:100|9[56])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"(?:4|80)0\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"10|2(?:[02-57-9]|1[1-9])\", \"10|2(?:[02-57-9]|1[1-9])\", \"10[0-79]|2(?:[02-57-9]|1[1-79])|(?:10|21)8(?:0[1-9]|[1-9])\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"3(?:[3-59]|7[02-68])|4(?:[26-8]|3[3-9]|5[2-9])|5(?:3[03-9]|[468]|7[028]|9[2-46-9])|6|7(?:[0-247]|3[04-9]|5[0-4689]|6[2368])|8(?:[1-358]|9[1-7])|9(?:[013479]|5[1-5])|(?:[34]1|55|79|87)[02-9]\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{7,8})\", \"$1 $2\", [\"9\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"80\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[3-578]\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"1[3-9]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3 $4\", [\"[12]\"], \"0$1\", 1]], \"0\", 0, \"(1(?:[12]\\\\d|79)\\\\d\\\\d)|0\", 0, 0, 0, 0, \"00\"], CO: [\"57\", \"00(?:4(?:[14]4|56)|[579])\", \"(?:60\\\\d\\\\d|9101)\\\\d{6}|(?:1\\\\d|3)\\\\d{9}\", [10, 11], [[\"(\\\\d{3})(\\\\d{7})\", \"$1 $2\", [\"6\"], \"($1)\"], [\"(\\\\d{3})(\\\\d{7})\", \"$1 $2\", [\"3[0-357]|91\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{7})\", \"$1-$2-$3\", [\"1\"], \"0$1\", 0, \"$1 $2 $3\"]], \"0\", 0, \"0([3579]|4(?:[14]4|56))?\"], CR: [\"506\", \"00\", \"(?:8\\\\d|90)\\\\d{8}|(?:[24-8]\\\\d{3}|3005)\\\\d{4}\", [8, 10], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2-7]|8[3-9]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[89]\"]]], 0, 0, \"(19(?:0[0-2468]|1[09]|20|66|77|99))\"], CU: [\"53\", \"119\", \"[27]\\\\d{6,7}|[34]\\\\d{5,7}|63\\\\d{6}|(?:5|8\\\\d\\\\d)\\\\d{7}\", [6, 7, 8, 10], [[\"(\\\\d{2})(\\\\d{4,6})\", \"$1 $2\", [\"2[1-4]|[34]\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{6,7})\", \"$1 $2\", [\"7\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{7})\", \"$1 $2\", [\"[56]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{7})\", \"$1 $2\", [\"8\"], \"0$1\"]], \"0\"], CV: [\"238\", \"0\", \"(?:[2-59]\\\\d\\\\d|800)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[2-589]\"]]]], CW: [\"599\", \"00\", \"(?:[34]1|60|(?:7|9\\\\d)\\\\d)\\\\d{5}\", [7, 8], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[3467]\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"9[4-8]\"]]], 0, 0, 0, 0, 0, \"[69]\"], CX: [\"61\", \"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\", \"1(?:[0-79]\\\\d{8}(?:\\\\d{2})?|8[0-24-9]\\\\d{7})|[148]\\\\d{8}|1\\\\d{5,7}\", [6, 7, 8, 9, 10, 12], 0, \"0\", 0, \"([59]\\\\d{7})$|0\", \"8$1\", 0, 0, [[\"8(?:51(?:0(?:01|30|59|88)|1(?:17|46|75)|2(?:22|35))|91(?:00[6-9]|1(?:[28]1|49|78)|2(?:09|63)|3(?:12|26|75)|4(?:56|97)|64\\\\d|7(?:0[01]|1[0-2])|958))\\\\d{3}\", [9]], [\"4(?:(?:79|94)[01]|83[0-389])\\\\d{5}|4(?:[0-3]\\\\d|4[047-9]|5[0-25-9]|6[0-26-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\", [9]], [\"180(?:0\\\\d{3}|2)\\\\d{3}\", [7, 10]], [\"190[0-26]\\\\d{6}\", [10]], 0, 0, 0, 0, [\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\", [9]], [\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\", [6, 8, 10, 12]]], \"0011\"], CY: [\"357\", \"00\", \"(?:[279]\\\\d|[58]0)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[257-9]\"]]]], CZ: [\"420\", \"00\", \"(?:[2-578]\\\\d|60)\\\\d{7}|9\\\\d{8,11}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-8]|9[015-7]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"96\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"9\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"9\"]]]], DE: [\"49\", \"00\", \"[2579]\\\\d{5,14}|49(?:[34]0|69|8\\\\d)\\\\d\\\\d?|49(?:37|49|60|7[089]|9\\\\d)\\\\d{1,3}|49(?:2[024-9]|3[2-689]|7[1-7])\\\\d{1,8}|(?:1|[368]\\\\d|4[0-8])\\\\d{3,13}|49(?:[015]\\\\d|2[13]|31|[46][1-8])\\\\d{1,9}\", [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], [[\"(\\\\d{2})(\\\\d{3,13})\", \"$1 $2\", [\"3[02]|40|[68]9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3,12})\", \"$1 $2\", [\"2(?:0[1-389]|1[124]|2[18]|3[14])|3(?:[35-9][15]|4[015])|906|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1\", \"2(?:0[1-389]|12[0-8])|3(?:[35-9][15]|4[015])|906|2(?:[13][14]|2[18])|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{2,11})\", \"$1 $2\", [\"[24-6]|3(?:[3569][02-46-9]|4[2-4679]|7[2-467]|8[2-46-8])|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]\", \"[24-6]|3(?:3(?:0[1-467]|2[127-9]|3[124578]|7[1257-9]|8[1256]|9[145])|4(?:2[135]|4[13578]|9[1346])|5(?:0[14]|2[1-3589]|6[1-4]|7[13468]|8[13568])|6(?:2[1-489]|3[124-6]|6[13]|7[12579]|8[1-356]|9[135])|7(?:2[1-7]|4[145]|6[1-5]|7[1-4])|8(?:21|3[1468]|6|7[1467]|8[136])|9(?:0[12479]|2[1358]|4[134679]|6[1-9]|7[136]|8[147]|9[1468]))|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]|3[68]4[1347]|3(?:47|60)[1356]|3(?:3[46]|46|5[49])[1246]|3[4579]3[1357]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"138\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{2,10})\", \"$1 $2\", [\"3\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5,11})\", \"$1 $2\", [\"181\"], \"0$1\"], [\"(\\\\d{3})(\\\\d)(\\\\d{4,10})\", \"$1 $2 $3\", [\"1(?:3|80)|9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{7,8})\", \"$1 $2\", [\"1[67]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{7,12})\", \"$1 $2\", [\"8\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{6})\", \"$1 $2\", [\"185\", \"1850\", \"18500\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{7})\", \"$1 $2\", [\"18[68]\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{6})\", \"$1 $2\", [\"15[0568]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{7})\", \"$1 $2\", [\"15[1279]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{8})\", \"$1 $2\", [\"18\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{7,8})\", \"$1 $2 $3\", [\"1(?:6[023]|7)\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{2})(\\\\d{7})\", \"$1 $2 $3\", [\"15[279]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{8})\", \"$1 $2 $3\", [\"15\"], \"0$1\"]], \"0\"], DJ: [\"253\", \"00\", \"(?:2\\\\d|77)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[27]\"]]]], DK: [\"45\", \"00\", \"[2-9]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-9]\"]]]], DM: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|767|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-7]\\\\d{6})$|1\", \"767$1\", 0, \"767\"], DO: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, 0, 0, 0, \"8001|8[024]9\"], DZ: [\"213\", \"00\", \"(?:[1-4]|[5-79]\\\\d|80)\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[1-4]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-8]\"], \"0$1\"]], \"0\"], EC: [\"593\", \"00\", \"1\\\\d{9,10}|(?:[2-7]|9\\\\d)\\\\d{7}\", [8, 9, 10, 11], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2-$3\", [\"[2-7]\"], \"(0$1)\", 0, \"$1-$2-$3\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"9\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"1\"]]], \"0\"], EE: [\"372\", \"00\", \"8\\\\d{9}|[4578]\\\\d{7}|(?:[3-8]\\\\d|90)\\\\d{5}\", [7, 8, 10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[369]|4[3-8]|5(?:[0-2]|5[0-478]|6[45])|7[1-9]|88\", \"[369]|4[3-8]|5(?:[02]|1(?:[0-8]|95)|5[0-478]|6(?:4[0-4]|5[1-589]))|7[1-9]|88\"]], [\"(\\\\d{4})(\\\\d{3,4})\", \"$1 $2\", [\"[45]|8(?:00|[1-49])\", \"[45]|8(?:00[1-9]|[1-49])\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]]]], EG: [\"20\", \"00\", \"[189]\\\\d{8,9}|[24-6]\\\\d{8}|[135]\\\\d{7}\", [8, 9, 10], [[\"(\\\\d)(\\\\d{7,8})\", \"$1 $2\", [\"[23]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{6,7})\", \"$1 $2\", [\"1[35]|[4-6]|8[2468]|9[235-7]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{8})\", \"$1 $2\", [\"1\"], \"0$1\"]], \"0\"], EH: [\"212\", \"00\", \"[5-8]\\\\d{8}\", [9], 0, \"0\", 0, 0, 0, 0, \"528[89]\"], ER: [\"291\", \"00\", \"[178]\\\\d{6}\", [7], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[178]\"], \"0$1\"]], \"0\"], ES: [\"34\", \"00\", \"[5-9]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[89]00\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-9]\"]]]], ET: [\"251\", \"00\", \"(?:11|[2-579]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[1-579]\"], \"0$1\"]], \"0\"], FI: [\"358\", \"00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))\", \"[1-35689]\\\\d{4}|7\\\\d{10,11}|(?:[124-7]\\\\d|3[0-46-9])\\\\d{8}|[1-9]\\\\d{5,8}\", [5, 6, 7, 8, 9, 10, 11, 12], [[\"(\\\\d)(\\\\d{4,9})\", \"$1 $2\", [\"[2568][1-8]|3(?:0[1-9]|[1-9])|9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3,7})\", \"$1 $2\", [\"[12]00|[368]|70[07-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4,8})\", \"$1 $2\", [\"[1245]|7[135]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{6,10})\", \"$1 $2\", [\"7\"], \"0$1\"]], \"0\", 0, 0, 0, 0, \"1[03-79]|[2-9]\", 0, \"00\"], FJ: [\"679\", \"0(?:0|52)\", \"45\\\\d{5}|(?:0800\\\\d|[235-9])\\\\d{6}\", [7, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[235-9]|45\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"0\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"], FK: [\"500\", \"00\", \"[2-7]\\\\d{4}\", [5]], FM: [\"691\", \"00\", \"(?:[39]\\\\d\\\\d|820)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[389]\"]]]], FO: [\"298\", \"00\", \"[2-9]\\\\d{5}\", [6], [[\"(\\\\d{6})\", \"$1\", [\"[2-9]\"]]], 0, 0, \"(10(?:01|[12]0|88))\"], FR: [\"33\", \"00\", \"[1-9]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"], \"0 $1\"], [\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"[1-79]\"], \"0$1\"]], \"0\"], GA: [\"241\", \"00\", \"(?:[067]\\\\d|11)\\\\d{6}|[2-7]\\\\d{6}\", [7, 8], [[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-7]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"0\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"11|[67]\"], \"0$1\"]], 0, 0, \"0(11\\\\d{6}|60\\\\d{6}|61\\\\d{6}|6[256]\\\\d{6}|7[467]\\\\d{6})\", \"$1\"], GB: [\"44\", \"00\", \"[1-357-9]\\\\d{9}|[18]\\\\d{8}|8\\\\d{6}\", [7, 9, 10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"800\", \"8001\", \"80011\", \"800111\", \"8001111\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"845\", \"8454\", \"84546\", \"845464\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"800\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{4,5})\", \"$1 $2\", [\"1(?:38|5[23]|69|76|94)\", \"1(?:(?:38|69)7|5(?:24|39)|768|946)\", \"1(?:3873|5(?:242|39[4-6])|(?:697|768)[347]|9467)\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5,6})\", \"$1 $2\", [\"1(?:[2-69][02-9]|[78])\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[25]|7(?:0|6[02-9])\", \"[25]|7(?:0|6(?:[03-9]|2[356]))\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{6})\", \"$1 $2\", [\"7\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[1389]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, [[\"(?:1(?:1(?:3(?:[0-58]\\\\d\\\\d|73[0235])|4(?:[0-5]\\\\d\\\\d|69[7-9]|70[0-79])|(?:(?:5[0-26-9]|[78][0-49])\\\\d|6(?:[0-4]\\\\d|50))\\\\d)|(?:2(?:(?:0[024-9]|2[3-9]|3[3-79]|4[1-689]|[58][02-9]|6[0-47-9]|7[013-9]|9\\\\d)\\\\d|1(?:[0-7]\\\\d|8[0-2]))|(?:3(?:0\\\\d|1[0-8]|[25][02-9]|3[02-579]|[468][0-46-9]|7[1-35-79]|9[2-578])|4(?:0[03-9]|[137]\\\\d|[28][02-57-9]|4[02-69]|5[0-8]|[69][0-79])|5(?:0[1-35-9]|[16]\\\\d|2[024-9]|3[015689]|4[02-9]|5[03-9]|7[0-35-9]|8[0-468]|9[0-57-9])|6(?:0[034689]|1\\\\d|2[0-35689]|[38][013-9]|4[1-467]|5[0-69]|6[13-9]|7[0-8]|9[0-24578])|7(?:0[0246-9]|2\\\\d|3[0236-8]|4[03-9]|5[0-46-9]|6[013-9]|7[0-35-9]|8[024-9]|9[02-9])|8(?:0[35-9]|2[1-57-9]|3[02-578]|4[0-578]|5[124-9]|6[2-69]|7\\\\d|8[02-9]|9[02569])|9(?:0[02-589]|[18]\\\\d|2[02-689]|3[1-57-9]|4[2-9]|5[0-579]|6[2-47-9]|7[0-24578]|9[2-57]))\\\\d)\\\\d)|2(?:0[013478]|3[0189]|4[017]|8[0-46-9]|9[0-2])\\\\d{3})\\\\d{4}|1(?:2(?:0(?:46[1-4]|87[2-9])|545[1-79]|76(?:2\\\\d|3[1-8]|6[1-6])|9(?:7(?:2[0-4]|3[2-5])|8(?:2[2-8]|7[0-47-9]|8[3-5])))|3(?:6(?:38[2-5]|47[23])|8(?:47[04-9]|64[0157-9]))|4(?:044[1-7]|20(?:2[23]|8\\\\d)|6(?:0(?:30|5[2-57]|6[1-8]|7[2-8])|140)|8(?:052|87[1-3]))|5(?:2(?:4(?:3[2-79]|6\\\\d)|76\\\\d)|6(?:26[06-9]|686))|6(?:06(?:4\\\\d|7[4-79])|295[5-7]|35[34]\\\\d|47(?:24|61)|59(?:5[08]|6[67]|74)|9(?:55[0-4]|77[23]))|7(?:26(?:6[13-9]|7[0-7])|(?:442|688)\\\\d|50(?:2[0-3]|[3-68]2|76))|8(?:27[56]\\\\d|37(?:5[2-5]|8[239])|843[2-58])|9(?:0(?:0(?:6[1-8]|85)|52\\\\d)|3583|4(?:66[1-8]|9(?:2[01]|81))|63(?:23|3[1-4])|9561))\\\\d{3}\", [9, 10]], [\"7(?:457[0-57-9]|700[01]|911[028])\\\\d{5}|7(?:[1-3]\\\\d\\\\d|4(?:[0-46-9]\\\\d|5[0-689])|5(?:0[0-8]|[13-9]\\\\d|2[0-35-9])|7(?:0[1-9]|[1-7]\\\\d|8[02-9]|9[0-689])|8(?:[014-9]\\\\d|[23][0-8])|9(?:[024-9]\\\\d|1[02-9]|3[0-689]))\\\\d{6}\", [10]], [\"80[08]\\\\d{7}|800\\\\d{6}|8001111\"], [\"(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\\\d|8[2-49]))\\\\d{7}|845464\\\\d\", [7, 10]], [\"70\\\\d{8}\", [10]], 0, [\"(?:3[0347]|55)\\\\d{8}\", [10]], [\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\", [10]], [\"56\\\\d{8}\", [10]]], 0, \" x\"], GD: [\"1\", \"011\", \"(?:473|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"473$1\", 0, \"473\"], GE: [\"995\", \"00\", \"(?:[3-57]\\\\d\\\\d|800)\\\\d{6}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"70\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"32\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[57]\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[348]\"], \"0$1\"]], \"0\"], GF: [\"594\", \"00\", \"[56]94\\\\d{6}|(?:80|9\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[56]|9[47]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[89]\"], \"0$1\"]], \"0\"], GG: [\"44\", \"00\", \"(?:1481|[357-9]\\\\d{3})\\\\d{6}|8\\\\d{6}(?:\\\\d{2})?\", [7, 9, 10], 0, \"0\", 0, \"([25-9]\\\\d{5})$|0\", \"1481$1\", 0, 0, [[\"1481[25-9]\\\\d{5}\", [10]], [\"7(?:(?:781|839)\\\\d|911[17])\\\\d{5}\", [10]], [\"80[08]\\\\d{7}|800\\\\d{6}|8001111\"], [\"(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\\\d|8[0-3]))\\\\d{7}|845464\\\\d\", [7, 10]], [\"70\\\\d{8}\", [10]], 0, [\"(?:3[0347]|55)\\\\d{8}\", [10]], [\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\", [10]], [\"56\\\\d{8}\", [10]]]], GH: [\"233\", \"00\", \"(?:[235]\\\\d{3}|800)\\\\d{5}\", [8, 9], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[235]\"], \"0$1\"]], \"0\"], GI: [\"350\", \"00\", \"(?:[25]\\\\d|60)\\\\d{6}\", [8], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"2\"]]]], GL: [\"299\", \"00\", \"(?:19|[2-689]\\\\d|70)\\\\d{4}\", [6], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"19|[2-9]\"]]]], GM: [\"220\", \"00\", \"[2-9]\\\\d{6}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-9]\"]]]], GN: [\"224\", \"00\", \"722\\\\d{6}|(?:3|6\\\\d)\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"3\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[67]\"]]]], GP: [\"590\", \"00\", \"590\\\\d{6}|(?:69|80|9\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[569]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, [[\"590(?:0[1-68]|[14][0-24-9]|2[0-68]|3[1-9]|5[3-579]|[68][0-689]|7[08]|9\\\\d)\\\\d{4}\"], [\"69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5]))\\\\d{4}\"], [\"80[0-5]\\\\d{6}\"], 0, 0, 0, 0, 0, [\"9(?:(?:395|76[018])\\\\d|475[0-5])\\\\d{4}\"]]], GQ: [\"240\", \"00\", \"222\\\\d{6}|(?:3\\\\d|55|[89]0)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[235]\"]], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"[89]\"]]]], GR: [\"30\", \"00\", \"5005000\\\\d{3}|8\\\\d{9,11}|(?:[269]\\\\d|70)\\\\d{8}\", [10, 11, 12], [[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"21|7\"]], [\"(\\\\d{4})(\\\\d{6})\", \"$1 $2\", [\"2(?:2|3[2-57-9]|4[2-469]|5[2-59]|6[2-9]|7[2-69]|8[2-49])|5\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2689]\"]], [\"(\\\\d{3})(\\\\d{3,4})(\\\\d{5})\", \"$1 $2 $3\", [\"8\"]]]], GT: [\"502\", \"00\", \"80\\\\d{6}|(?:1\\\\d{3}|[2-7])\\\\d{7}\", [8, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2-8]\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"]]]], GU: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|671|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"671$1\", 0, \"671\"], GW: [\"245\", \"00\", \"[49]\\\\d{8}|4\\\\d{6}\", [7, 9], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"40\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[49]\"]]]], GY: [\"592\", \"001\", \"(?:[2-8]\\\\d{3}|9008)\\\\d{3}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-9]\"]]]], HK: [\"852\", \"00(?:30|5[09]|[126-9]?)\", \"8[0-46-9]\\\\d{6,7}|9\\\\d{4,7}|(?:[2-7]|9\\\\d{3})\\\\d{7}\", [5, 6, 7, 8, 9, 11], [[\"(\\\\d{3})(\\\\d{2,5})\", \"$1 $2\", [\"900\", \"9003\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2-7]|8[1-4]|9(?:0[1-9]|[1-8])\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"9\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"], HN: [\"504\", \"00\", \"8\\\\d{10}|[237-9]\\\\d{7}\", [8, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1-$2\", [\"[237-9]\"]]]], HR: [\"385\", \"00\", \"(?:[24-69]\\\\d|3[0-79])\\\\d{7}|80\\\\d{5,7}|[1-79]\\\\d{7}|6\\\\d{5,6}\", [6, 7, 8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"6[01]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"8\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[67]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"9\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2-5]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"], \"0$1\"]], \"0\"], HT: [\"509\", \"00\", \"(?:[2-489]\\\\d|55)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-589]\"]]]], HU: [\"36\", \"00\", \"[235-7]\\\\d{8}|[1-9]\\\\d{7}\", [8, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"(06 $1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[27][2-9]|3[2-7]|4[24-9]|5[2-79]|6|8[2-57-9]|9[2-69]\"], \"(06 $1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2-9]\"], \"06 $1\"]], \"06\"], ID: [\"62\", \"00[89]\", \"(?:(?:00[1-9]|8\\\\d)\\\\d{4}|[1-36])\\\\d{6}|00\\\\d{10}|[1-9]\\\\d{8,10}|[2-9]\\\\d{7}\", [7, 8, 9, 10, 11, 12, 13], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"15\"]], [\"(\\\\d{2})(\\\\d{5,9})\", \"$1 $2\", [\"2[124]|[36]1\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{5,7})\", \"$1 $2\", [\"800\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5,8})\", \"$1 $2\", [\"[2-79]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3,4})(\\\\d{3})\", \"$1-$2-$3\", [\"8[1-35-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6,8})\", \"$1 $2\", [\"1\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"804\"], \"0$1\"], [\"(\\\\d{3})(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"80\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\", \"$1-$2-$3\", [\"8\"], \"0$1\"]], \"0\"], IE: [\"353\", \"00\", \"(?:1\\\\d|[2569])\\\\d{6,8}|4\\\\d{6,9}|7\\\\d{8}|8\\\\d{8,9}\", [7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"2[24-9]|47|58|6[237-9]|9[35-9]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[45]0\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2569]|4[1-69]|7[14]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"70\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"81\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[78]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"4\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\"], IL: [\"972\", \"0(?:0|1[2-9])\", \"1\\\\d{6}(?:\\\\d{3,5})?|[57]\\\\d{8}|[1-489]\\\\d{7}\", [7, 8, 9, 10, 11, 12], [[\"(\\\\d{4})(\\\\d{3})\", \"$1-$2\", [\"125\"]], [\"(\\\\d{4})(\\\\d{2})(\\\\d{2})\", \"$1-$2-$3\", [\"121\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[2-489]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[57]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1-$2-$3\", [\"12\"]], [\"(\\\\d{4})(\\\\d{6})\", \"$1-$2\", [\"159\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1-$2-$3-$4\", [\"1[7-9]\"]], [\"(\\\\d{3})(\\\\d{1,2})(\\\\d{3})(\\\\d{4})\", \"$1-$2 $3-$4\", [\"15\"]]], \"0\"], IM: [\"44\", \"00\", \"1624\\\\d{6}|(?:[3578]\\\\d|90)\\\\d{8}\", [10], 0, \"0\", 0, \"([25-8]\\\\d{5})$|0\", \"1624$1\", 0, \"74576|(?:16|7[56])24\"], IN: [\"91\", \"00\", \"(?:000800|[2-9]\\\\d\\\\d)\\\\d{7}|1\\\\d{7,12}\", [8, 9, 10, 11, 12, 13], [[\"(\\\\d{8})\", \"$1\", [\"5(?:0|2[23]|3[03]|[67]1|88)\", \"5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|888)\", \"5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|8888)\"], 0, 1], [\"(\\\\d{4})(\\\\d{4,5})\", \"$1 $2\", [\"180\", \"1800\"], 0, 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"140\"], 0, 1], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"11|2[02]|33|4[04]|79[1-7]|80[2-46]\", \"11|2[02]|33|4[04]|79(?:[1-6]|7[19])|80(?:[2-4]|6[0-589])\", \"11|2[02]|33|4[04]|79(?:[124-6]|3(?:[02-9]|1[0-24-9])|7(?:1|9[1-6]))|80(?:[2-4]|6[0-589])\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1(?:2[0-249]|3[0-25]|4[145]|[68]|7[1257])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|5[12]|[78]1)|6(?:12|[2-4]1|5[17]|6[13]|80)|7(?:12|3[134]|4[47]|61|88)|8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91)|(?:43|59|75)[15]|(?:1[59]|29|67|72)[14]\", \"1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|674|7(?:(?:2[14]|3[34]|5[15])[2-6]|61[346]|88[0-8])|8(?:70[2-6]|84[235-7]|91[3-7])|(?:1(?:29|60|8[06])|261|552|6(?:12|[2-47]1|5[17]|6[13]|80)|7(?:12|31|4[47])|8(?:16|2[014]|3[126]|6[136]|7[78]|83))[2-7]\", \"1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|6(?:12(?:[2-6]|7[0-8])|74[2-7])|7(?:(?:2[14]|5[15])[2-6]|3171|61[346]|88(?:[2-7]|82))|8(?:70[2-6]|84(?:[2356]|7[19])|91(?:[3-6]|7[19]))|73[134][2-6]|(?:74[47]|8(?:16|2[014]|3[126]|6[136]|7[78]|83))(?:[2-6]|7[19])|(?:1(?:29|60|8[06])|261|552|6(?:[2-4]1|5[17]|6[13]|7(?:1|4[0189])|80)|7(?:12|88[01]))[2-7]\"], \"0$1\", 1], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2[2457-9]|3[2-5]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1[013-9]|28|3[129]|4[1-35689]|5[29]|6[02-5]|70)|807\", \"1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2(?:[2457]|84|95)|3(?:[2-4]|55)|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1(?:[013-8]|9[6-9])|28[6-8]|3(?:17|2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4|5[0-367])|70[13-7])|807[19]\", \"1(?:[2-479]|5(?:[0236-9]|5[013-9]))|[2-5]|6(?:2(?:84|95)|355|83)|73179|807(?:1|9[1-3])|(?:1552|6(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[124-6])\\\\d|7(?:1(?:[013-8]\\\\d|9[6-9])|28[6-8]|3(?:2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]\\\\d|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4\\\\d|5[0-367])|70[13-7]))[2-7]\"], \"0$1\", 1], [\"(\\\\d{5})(\\\\d{5})\", \"$1 $2\", [\"[6-9]\"], \"0$1\", 1], [\"(\\\\d{4})(\\\\d{2,4})(\\\\d{4})\", \"$1 $2 $3\", [\"1(?:6|8[06])\", \"1(?:6|8[06]0)\"], 0, 1], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"18\"], 0, 1]], \"0\"], IO: [\"246\", \"00\", \"3\\\\d{6}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"3\"]]]], IQ: [\"964\", \"00\", \"(?:1|7\\\\d\\\\d)\\\\d{7}|[2-6]\\\\d{7,8}\", [8, 9, 10], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2-6]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"]], \"0\"], IR: [\"98\", \"00\", \"[1-9]\\\\d{9}|(?:[1-8]\\\\d\\\\d|9)\\\\d{3,4}\", [4, 5, 6, 7, 10], [[\"(\\\\d{4,5})\", \"$1\", [\"96\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4,5})\", \"$1 $2\", [\"(?:1[137]|2[13-68]|3[1458]|4[145]|5[1468]|6[16]|7[1467]|8[13467])[12689]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"9\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[1-8]\"], \"0$1\"]], \"0\"], IS: [\"354\", \"00|1(?:0(?:01|[12]0)|100)\", \"(?:38\\\\d|[4-9])\\\\d{6}\", [7, 9], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[4-9]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"3\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"], IT: [\"39\", \"00\", \"0\\\\d{5,10}|1\\\\d{8,10}|3(?:[0-8]\\\\d{7,10}|9\\\\d{7,8})|(?:55|70)\\\\d{8}|8\\\\d{5}(?:\\\\d{2,4})?\", [6, 7, 8, 9, 10, 11], [[\"(\\\\d{2})(\\\\d{4,6})\", \"$1 $2\", [\"0[26]\"]], [\"(\\\\d{3})(\\\\d{3,6})\", \"$1 $2\", [\"0[13-57-9][0159]|8(?:03|4[17]|9[2-5])\", \"0[13-57-9][0159]|8(?:03|4[17]|9(?:2|3[04]|[45][0-4]))\"]], [\"(\\\\d{4})(\\\\d{2,6})\", \"$1 $2\", [\"0(?:[13-579][2-46-8]|8[236-8])\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"894\"]], [\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"0[26]|5\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"1(?:44|[679])|[378]\"]], [\"(\\\\d{3})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"0[13-57-9][0159]|14\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{5})\", \"$1 $2 $3\", [\"0[26]\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"0\"]], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\", \"$1 $2 $3\", [\"3\"]]], 0, 0, 0, 0, 0, 0, [[\"0669[0-79]\\\\d{1,6}|0(?:1(?:[0159]\\\\d|[27][1-5]|31|4[1-4]|6[1356]|8[2-57])|2\\\\d\\\\d|3(?:[0159]\\\\d|2[1-4]|3[12]|[48][1-6]|6[2-59]|7[1-7])|4(?:[0159]\\\\d|[23][1-9]|4[245]|6[1-5]|7[1-4]|81)|5(?:[0159]\\\\d|2[1-5]|3[2-6]|4[1-79]|6[4-6]|7[1-578]|8[3-8])|6(?:[0-57-9]\\\\d|6[0-8])|7(?:[0159]\\\\d|2[12]|3[1-7]|4[2-46]|6[13569]|7[13-6]|8[1-59])|8(?:[0159]\\\\d|2[3-578]|3[1-356]|[6-8][1-5])|9(?:[0159]\\\\d|[238][1-5]|4[12]|6[1-8]|7[1-6]))\\\\d{2,7}\"], [\"3[1-9]\\\\d{8}|3[2-9]\\\\d{7}\", [9, 10]], [\"80(?:0\\\\d{3}|3)\\\\d{3}\", [6, 9]], [\"(?:0878\\\\d{3}|89(?:2\\\\d|3[04]|4(?:[0-4]|[5-9]\\\\d\\\\d)|5[0-4]))\\\\d\\\\d|(?:1(?:44|6[346])|89(?:38|5[5-9]|9))\\\\d{6}\", [6, 8, 9, 10]], [\"1(?:78\\\\d|99)\\\\d{6}\", [9, 10]], 0, 0, 0, [\"55\\\\d{8}\", [10]], [\"84(?:[08]\\\\d{3}|[17])\\\\d{3}\", [6, 9]]]], JE: [\"44\", \"00\", \"1534\\\\d{6}|(?:[3578]\\\\d|90)\\\\d{8}\", [10], 0, \"0\", 0, \"([0-24-8]\\\\d{5})$|0\", \"1534$1\", 0, 0, [[\"1534[0-24-8]\\\\d{5}\"], [\"7(?:(?:(?:50|82)9|937)\\\\d|7(?:00[378]|97[7-9]))\\\\d{5}\"], [\"80(?:07(?:35|81)|8901)\\\\d{4}\"], [\"(?:8(?:4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|90(?:066[59]|1810|71(?:07|55)))\\\\d{4}\"], [\"701511\\\\d{4}\"], 0, [\"(?:3(?:0(?:07(?:35|81)|8901)|3\\\\d{4}|4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|55\\\\d{4})\\\\d{4}\"], [\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\"], [\"56\\\\d{8}\"]]], JM: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|658|900)\\\\d{7}\", [10], 0, \"1\", 0, 0, 0, 0, \"658|876\"], JO: [\"962\", \"00\", \"(?:(?:[2689]|7\\\\d)\\\\d|32|53)\\\\d{6}\", [8, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2356]|87\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{5,6})\", \"$1 $2\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"70\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"]], \"0\"], JP: [\"81\", \"010\", \"00[1-9]\\\\d{6,14}|[257-9]\\\\d{9}|(?:00|[1-9]\\\\d\\\\d)\\\\d{6}\", [8, 9, 10, 11, 12, 13, 14, 15, 16, 17], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1-$2-$3\", [\"(?:12|57|99)0\"], \"0$1\"], [\"(\\\\d{4})(\\\\d)(\\\\d{4})\", \"$1-$2-$3\", [\"1(?:26|3[79]|4[56]|5[4-68]|6[3-5])|499|5(?:76|97)|746|8(?:3[89]|47|51)|9(?:80|9[16])\", \"1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:76|97)9|7468|8(?:3(?:8[7-9]|96)|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]\", \"1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:769|979[2-69])|7468|8(?:3(?:8[7-9]|96[2457-9])|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"60\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1-$2-$3\", [\"[36]|4(?:2[09]|7[01])\", \"[36]|4(?:2(?:0|9[02-69])|7(?:0[019]|1))\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"1(?:1|5[45]|77|88|9[69])|2(?:2[1-37]|3[0-269]|4[59]|5|6[24]|7[1-358]|8[1369]|9[0-38])|4(?:[28][1-9]|3[0-57]|[45]|6[248]|7[2-579]|9[29])|5(?:2|3[0459]|4[0-369]|5[29]|8[02389]|9[0-389])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9[2-6])|8(?:2[124589]|3[26-9]|49|51|6|7[0-468]|8[68]|9[019])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9[1-489])\", \"1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2(?:[127]|3[014-9])|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9[19])|62|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|8[1-9]|9[29])|5(?:2|3(?:[045]|9[0-8])|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0-2469])|3(?:[29]|60)|49|51|6(?:[0-24]|36|5[0-3589]|7[23]|9[01459])|7[0-468]|8[68])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9(?:[1289]|3[34]|4[0178]))|(?:264|837)[016-9]|2(?:57|93)[015-9]|(?:25[0468]|422|838)[01]|(?:47[59]|59[89]|8(?:6[68]|9))[019]\", \"1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2[127]|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9(?:17|99))|6(?:2|4[016-9])|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|9[29])|5(?:2|3(?:[045]|9(?:[0-58]|6[4-9]|7[0-35689]))|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0169])|3(?:[29]|60|7(?:[017-9]|6[6-8]))|49|51|6(?:[0-24]|36[2-57-9]|5(?:[0-389]|5[23])|6(?:[01]|9[178])|7(?:2[2-468]|3[78])|9[0145])|7[0-468]|8[68])|9(?:4[15]|5[138]|7[156]|8[189]|9(?:[1289]|3(?:31|4[357])|4[0178]))|(?:8294|96)[1-3]|2(?:57|93)[015-9]|(?:223|8699)[014-9]|(?:25[0468]|422|838)[01]|(?:48|8292|9[23])[1-9]|(?:47[59]|59[89]|8(?:68|9))[019]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1-$2-$3\", [\"[14]|[289][2-9]|5[3-9]|7[2-4679]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"800\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1-$2-$3\", [\"[257-9]\"], \"0$1\"]], \"0\", 0, \"(000[259]\\\\d{6})$|(?:(?:003768)0?)|0\", \"$1\"], KE: [\"254\", \"000\", \"(?:[17]\\\\d\\\\d|900)\\\\d{6}|(?:2|80)0\\\\d{6,7}|[4-6]\\\\d{6,8}\", [7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{5,7})\", \"$1 $2\", [\"[24-6]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"[17]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"]], \"0\"], KG: [\"996\", \"00\", \"8\\\\d{9}|[235-9]\\\\d{8}\", [9, 10], [[\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"3(?:1[346]|[24-79])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[235-79]|88\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d)(\\\\d{2,3})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\"], KH: [\"855\", \"00[14-9]\", \"1\\\\d{9}|[1-9]\\\\d{7,8}\", [8, 9, 10], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[1-9]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"]]], \"0\"], KI: [\"686\", \"00\", \"(?:[37]\\\\d|6[0-79])\\\\d{6}|(?:[2-48]\\\\d|50)\\\\d{3}\", [5, 8], 0, \"0\"], KM: [\"269\", \"00\", \"[3478]\\\\d{6}\", [7], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[3478]\"]]]], KN: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-7]\\\\d{6})$|1\", \"869$1\", 0, \"869\"], KP: [\"850\", \"00|99\", \"85\\\\d{6}|(?:19\\\\d|[2-7])\\\\d{7}\", [8, 10], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-7]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"0$1\"]], \"0\"], KR: [\"82\", \"00(?:[125689]|3(?:[46]5|91)|7(?:00|27|3|55|6[126]))\", \"00[1-9]\\\\d{8,11}|(?:[12]|5\\\\d{3})\\\\d{7}|[13-6]\\\\d{9}|(?:[1-6]\\\\d|80)\\\\d{7}|[3-6]\\\\d{4,5}|(?:00|7)0\\\\d{8}\", [5, 6, 8, 9, 10, 11, 12, 13, 14], [[\"(\\\\d{2})(\\\\d{3,4})\", \"$1-$2\", [\"(?:3[1-3]|[46][1-4]|5[1-5])1\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{4})\", \"$1-$2\", [\"1\"]], [\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\", \"$1-$2-$3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"60|8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\", \"$1-$2-$3\", [\"[1346]|5[1-5]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1-$2-$3\", [\"[57]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{5})(\\\\d{4})\", \"$1-$2-$3\", [\"5\"], \"0$1\"]], \"0\", 0, \"0(8(?:[1-46-8]|5\\\\d\\\\d))?\"], KW: [\"965\", \"00\", \"18\\\\d{5}|(?:[2569]\\\\d|41)\\\\d{6}\", [7, 8], [[\"(\\\\d{4})(\\\\d{3,4})\", \"$1 $2\", [\"[169]|2(?:[235]|4[1-35-9])|52\"]], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[245]\"]]]], KY: [\"1\", \"011\", \"(?:345|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"345$1\", 0, \"345\"], KZ: [\"7\", \"810\", \"(?:33622|8\\\\d{8})\\\\d{5}|[78]\\\\d{9}\", [10, 14], 0, \"8\", 0, 0, 0, 0, \"33|7\", 0, \"8~10\"], LA: [\"856\", \"00\", \"[23]\\\\d{9}|3\\\\d{8}|(?:[235-8]\\\\d|41)\\\\d{6}\", [8, 9, 10], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2[13]|3[14]|[4-8]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"30[013-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"[23]\"], \"0$1\"]], \"0\"], LB: [\"961\", \"00\", \"[27-9]\\\\d{7}|[13-9]\\\\d{6}\", [7, 8], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[13-69]|7(?:[2-57]|62|8[0-7]|9[04-9])|8[02-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[27-9]\"]]], \"0\"], LC: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|758|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-8]\\\\d{6})$|1\", \"758$1\", 0, \"758\"], LI: [\"423\", \"00\", \"[68]\\\\d{8}|(?:[2378]\\\\d|90)\\\\d{5}\", [7, 9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[2379]|8(?:0[09]|7)\", \"[2379]|8(?:0(?:02|9)|7)\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"69\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6\"]]], \"0\", 0, \"(1001)|0\"], LK: [\"94\", \"00\", \"[1-9]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[1-689]\"], \"0$1\"]], \"0\"], LR: [\"231\", \"00\", \"(?:[25]\\\\d|33|77|88)\\\\d{7}|(?:2\\\\d|[4-6])\\\\d{6}\", [7, 8, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[4-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[23578]\"], \"0$1\"]], \"0\"], LS: [\"266\", \"00\", \"(?:[256]\\\\d\\\\d|800)\\\\d{5}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2568]\"]]]], LT: [\"370\", \"00\", \"(?:[3469]\\\\d|52|[78]0)\\\\d{6}\", [8], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"52[0-7]\"], \"(8-$1)\", 1], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"[7-9]\"], \"8 $1\", 1], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"37|4(?:[15]|6[1-8])\"], \"(8-$1)\", 1], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[3-6]\"], \"(8-$1)\", 1]], \"8\", 0, \"[08]\"], LU: [\"352\", \"00\", \"35[013-9]\\\\d{4,8}|6\\\\d{8}|35\\\\d{2,4}|(?:[2457-9]\\\\d|3[0-46-9])\\\\d{2,9}\", [4, 5, 6, 7, 8, 9, 10, 11], [[\"(\\\\d{2})(\\\\d{3})\", \"$1 $2\", [\"2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"20[2-689]\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,2})\", \"$1 $2 $3 $4\", [\"2(?:[0367]|4[3-8])\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"80[01]|90[015]\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"20\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,2})\", \"$1 $2 $3 $4 $5\", [\"2(?:[0367]|4[3-8])\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,5})\", \"$1 $2 $3 $4\", [\"[3-57]|8[13-9]|9(?:0[89]|[2-579])|(?:2|80)[2-9]\"]]], 0, 0, \"(15(?:0[06]|1[12]|[35]5|4[04]|6[26]|77|88|99)\\\\d)\"], LV: [\"371\", \"00\", \"(?:[268]\\\\d|90)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[269]|8[01]\"]]]], LY: [\"218\", \"00\", \"[2-9]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{7})\", \"$1-$2\", [\"[2-9]\"], \"0$1\"]], \"0\"], MA: [\"212\", \"00\", \"[5-8]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"5[45]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5})\", \"$1-$2\", [\"5(?:2[2-489]|3[5-9]|9)|8(?:0[89]|92)\", \"5(?:2(?:[2-49]|8[235-9])|3[5-9]|9)|8(?:0[89]|92)\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1-$2\", [\"8\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6})\", \"$1-$2\", [\"[5-7]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, [[\"5(?:2(?:[0-25-79]\\\\d|3[1-578]|4[02-46-8]|8[0235-7])|3(?:[0-47]\\\\d|5[02-9]|6[02-8]|8[014-9]|9[3-9])|(?:4[067]|5[03])\\\\d)\\\\d{5}\"], [\"(?:6(?:[0-79]\\\\d|8[0-247-9])|7(?:[0167]\\\\d|2[0-2]|5[01]|8[0-3]))\\\\d{6}\"], [\"80[0-7]\\\\d{6}\"], [\"89\\\\d{7}\"], 0, 0, 0, 0, [\"(?:592(?:4[0-2]|93)|80[89]\\\\d\\\\d)\\\\d{4}\"]]], MC: [\"377\", \"00\", \"(?:[3489]|6\\\\d)\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"4\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[389]\"]], [\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"6\"], \"0$1\"]], \"0\"], MD: [\"373\", \"00\", \"(?:[235-7]\\\\d|[89]0)\\\\d{6}\", [8], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"22|3\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"[25-7]\"], \"0$1\"]], \"0\"], ME: [\"382\", \"00\", \"(?:20|[3-79]\\\\d)\\\\d{6}|80\\\\d{6,7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2-9]\"], \"0$1\"]], \"0\"], MF: [\"590\", \"00\", \"590\\\\d{6}|(?:69|80|9\\\\d)\\\\d{7}\", [9], 0, \"0\", 0, 0, 0, 0, 0, [[\"590(?:0[079]|[14]3|[27][79]|3[03-7]|5[0-268]|87)\\\\d{4}\"], [\"69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5]))\\\\d{4}\"], [\"80[0-5]\\\\d{6}\"], 0, 0, 0, 0, 0, [\"9(?:(?:395|76[018])\\\\d|475[0-5])\\\\d{4}\"]]], MG: [\"261\", \"00\", \"[23]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[23]\"], \"0$1\"]], \"0\", 0, \"([24-9]\\\\d{6})$|0\", \"20$1\"], MH: [\"692\", \"011\", \"329\\\\d{4}|(?:[256]\\\\d|45)\\\\d{5}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[2-6]\"]]], \"1\"], MK: [\"389\", \"00\", \"[2-578]\\\\d{7}\", [8], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"2|34[47]|4(?:[37]7|5[47]|64)\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[347]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d)(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[58]\"], \"0$1\"]], \"0\"], ML: [\"223\", \"00\", \"[24-9]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[24-9]\"]]]], MM: [\"95\", \"00\", \"1\\\\d{5,7}|95\\\\d{6}|(?:[4-7]|9[0-46-9])\\\\d{6,8}|(?:2|8\\\\d)\\\\d{5,8}\", [6, 7, 8, 9, 10], [[\"(\\\\d)(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"16|2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"[45]|6(?:0[23]|[1-689]|7[235-7])|7(?:[0-4]|5[2-7])|8[1-6]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[12]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[4-7]|8[1-35]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{4,6})\", \"$1 $2 $3\", [\"9(?:2[0-4]|[35-9]|4[137-9])\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"92\"], \"0$1\"], [\"(\\\\d)(\\\\d{5})(\\\\d{4})\", \"$1 $2 $3\", [\"9\"], \"0$1\"]], \"0\"], MN: [\"976\", \"001\", \"[12]\\\\d{7,9}|[5-9]\\\\d{7}\", [8, 9, 10], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"[12]1\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[5-9]\"]], [\"(\\\\d{3})(\\\\d{5,6})\", \"$1 $2\", [\"[12]2[1-3]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5,6})\", \"$1 $2\", [\"[12](?:27|3[2-8]|4[2-68]|5[1-4689])\", \"[12](?:27|3[2-8]|4[2-68]|5[1-4689])[0-3]\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{4,5})\", \"$1 $2\", [\"[12]\"], \"0$1\"]], \"0\"], MO: [\"853\", \"00\", \"0800\\\\d{3}|(?:28|[68]\\\\d)\\\\d{6}\", [7, 8], [[\"(\\\\d{4})(\\\\d{3})\", \"$1 $2\", [\"0\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[268]\"]]]], MP: [\"1\", \"011\", \"[58]\\\\d{9}|(?:67|90)0\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"670$1\", 0, \"670\"], MQ: [\"596\", \"00\", \"596\\\\d{6}|(?:69|80|9\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[569]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\"], MR: [\"222\", \"00\", \"(?:[2-4]\\\\d\\\\d|800)\\\\d{5}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-48]\"]]]], MS: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|664|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([34]\\\\d{6})$|1\", \"664$1\", 0, \"664\"], MT: [\"356\", \"00\", \"3550\\\\d{4}|(?:[2579]\\\\d\\\\d|800)\\\\d{5}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2357-9]\"]]]], MU: [\"230\", \"0(?:0|[24-7]0|3[03])\", \"(?:[57]|8\\\\d\\\\d)\\\\d{7}|[2-468]\\\\d{6}\", [7, 8, 10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-46]|8[013]\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[57]\"]], [\"(\\\\d{5})(\\\\d{5})\", \"$1 $2\", [\"8\"]]], 0, 0, 0, 0, 0, 0, 0, \"020\"], MV: [\"960\", \"0(?:0|19)\", \"(?:800|9[0-57-9]\\\\d)\\\\d{7}|[34679]\\\\d{6}\", [7, 10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[34679]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"], MW: [\"265\", \"00\", \"(?:[1289]\\\\d|31|77)\\\\d{7}|1\\\\d{6}\", [7, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1[2-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[137-9]\"], \"0$1\"]], \"0\"], MX: [\"52\", \"0[09]\", \"1(?:(?:[27]2|44|87|99)[1-9]|65[0-689])\\\\d{7}|(?:1(?:[01]\\\\d|2[13-9]|[35][1-9]|4[0-35-9]|6[0-46-9]|7[013-9]|8[1-69]|9[1-8])|[2-9]\\\\d)\\\\d{8}\", [10, 11], [[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"33|5[56]|81\"], 0, 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-9]\"], 0, 1], [\"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$2 $3 $4\", [\"1(?:33|5[56]|81)\"], 0, 1], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$2 $3 $4\", [\"1\"], 0, 1]], \"01\", 0, \"0(?:[12]|4[45])|1\", 0, 0, 0, 0, \"00\"], MY: [\"60\", \"00\", \"1\\\\d{8,9}|(?:3\\\\d|[4-9])\\\\d{7}\", [8, 9, 10], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1-$2 $3\", [\"[4-79]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1-$2 $3\", [\"1(?:[02469]|[378][1-9]|53)|8\", \"1(?:[02469]|[37][1-9]|53|8(?:[1-46-9]|5[7-9]))|8\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1-$2 $3\", [\"3\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1-$2-$3-$4\", [\"1(?:[367]|80)\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1-$2 $3\", [\"15\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1-$2 $3\", [\"1\"], \"0$1\"]], \"0\"], MZ: [\"258\", \"00\", \"(?:2|8\\\\d)\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2|8[2-79]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]]]], NA: [\"264\", \"00\", \"[68]\\\\d{7,8}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"88\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"6\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"87\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"], \"0$1\"]], \"0\"], NC: [\"687\", \"00\", \"(?:050|[2-57-9]\\\\d\\\\d)\\\\d{3}\", [6], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1.$2.$3\", [\"[02-57-9]\"]]]], NE: [\"227\", \"00\", \"[027-9]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"08\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[089]|2[013]|7[047]\"]]]], NF: [\"672\", \"00\", \"[13]\\\\d{5}\", [6], [[\"(\\\\d{2})(\\\\d{4})\", \"$1 $2\", [\"1[0-3]\"]], [\"(\\\\d)(\\\\d{5})\", \"$1 $2\", [\"[13]\"]]], 0, 0, \"([0-258]\\\\d{4})$\", \"3$1\"], NG: [\"234\", \"009\", \"(?:[124-7]|9\\\\d{3})\\\\d{6}|[1-9]\\\\d{7}|[78]\\\\d{9,13}\", [7, 8, 10, 11, 12, 13, 14], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"78\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[12]|9(?:0[3-9]|[1-9])\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\", \"$1 $2 $3\", [\"[3-7]|8[2-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[7-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\", \"$1 $2 $3\", [\"[78]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5})(\\\\d{5,6})\", \"$1 $2 $3\", [\"[78]\"], \"0$1\"]], \"0\"], NI: [\"505\", \"00\", \"(?:1800|[25-8]\\\\d{3})\\\\d{4}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[125-8]\"]]]], NL: [\"31\", \"00\", \"(?:[124-7]\\\\d\\\\d|3(?:[02-9]\\\\d|1[0-8]))\\\\d{6}|8\\\\d{6,9}|9\\\\d{6,10}|1\\\\d{4,5}\", [5, 6, 7, 8, 9, 10, 11], [[\"(\\\\d{3})(\\\\d{4,7})\", \"$1 $2\", [\"[89]0\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"66\"], \"0$1\"], [\"(\\\\d)(\\\\d{8})\", \"$1 $2\", [\"6\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1[16-8]|2[259]|3[124]|4[17-9]|5[124679]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[1-578]|91\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\", \"$1 $2 $3\", [\"9\"], \"0$1\"]], \"0\"], NO: [\"47\", \"00\", \"(?:0|[2-9]\\\\d{3})\\\\d{4}\", [5, 8], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-79]\"]]], 0, 0, 0, 0, 0, \"[02-689]|7[0-8]\"], NP: [\"977\", \"00\", \"(?:1\\\\d|9)\\\\d{9}|[1-9]\\\\d{7}\", [8, 10, 11], [[\"(\\\\d)(\\\\d{7})\", \"$1-$2\", [\"1[2-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1-$2\", [\"1[01]|[2-8]|9(?:[1-59]|[67][2-6])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{7})\", \"$1-$2\", [\"9\"]]], \"0\"], NR: [\"674\", \"00\", \"(?:444|(?:55|8\\\\d)\\\\d|666)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[4-68]\"]]]], NU: [\"683\", \"00\", \"(?:[4-7]|888\\\\d)\\\\d{3}\", [4, 7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"8\"]]]], NZ: [\"64\", \"0(?:0|161)\", \"[1289]\\\\d{9}|50\\\\d{5}(?:\\\\d{2,3})?|[27-9]\\\\d{7,8}|(?:[34]\\\\d|6[0-35-9])\\\\d{6}|8\\\\d{4,6}\", [5, 6, 7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{3,8})\", \"$1 $2\", [\"8[1-79]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"50[036-8]|8|90\", \"50(?:[0367]|88)|8|90\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"24|[346]|7[2-57-9]|9[2-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2(?:10|74)|[589]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"1|2[028]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,5})\", \"$1 $2 $3\", [\"2(?:[169]|7[0-35-9])|7\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, 0, \"00\"], OM: [\"968\", \"00\", \"(?:1505|[279]\\\\d{3}|500)\\\\d{4}|800\\\\d{5,6}\", [7, 8, 9], [[\"(\\\\d{3})(\\\\d{4,6})\", \"$1 $2\", [\"[58]\"]], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"2\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[179]\"]]]], PA: [\"507\", \"00\", \"(?:00800|8\\\\d{3})\\\\d{6}|[68]\\\\d{7}|[1-57-9]\\\\d{6}\", [7, 8, 10, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[1-57-9]\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1-$2\", [\"[68]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]]]], PE: [\"51\", \"00|19(?:1[124]|77|90)00\", \"(?:[14-8]|9\\\\d)\\\\d{7}\", [8, 9], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"80\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{7})\", \"$1 $2\", [\"1\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[4-8]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"9\"]]], \"0\", 0, 0, 0, 0, 0, 0, \"00\", \" Anexo \"], PF: [\"689\", \"00\", \"4\\\\d{5}(?:\\\\d{2})?|8\\\\d{7,8}\", [6, 8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"44\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"4|8[7-9]\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"]]]], PG: [\"675\", \"00|140[1-3]\", \"(?:180|[78]\\\\d{3})\\\\d{4}|(?:[2-589]\\\\d|64)\\\\d{5}\", [7, 8], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"18|[2-69]|85\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[78]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"], PH: [\"63\", \"00\", \"(?:[2-7]|9\\\\d)\\\\d{8}|2\\\\d{5}|(?:1800|8)\\\\d{7,9}\", [6, 8, 9, 10, 11, 12, 13], [[\"(\\\\d)(\\\\d{5})\", \"$1 $2\", [\"2\"], \"(0$1)\"], [\"(\\\\d{4})(\\\\d{4,6})\", \"$1 $2\", [\"3(?:23|39|46)|4(?:2[3-6]|[35]9|4[26]|76)|544|88[245]|(?:52|64|86)2\", \"3(?:230|397|461)|4(?:2(?:35|[46]4|51)|396|4(?:22|63)|59[347]|76[15])|5(?:221|446)|642[23]|8(?:622|8(?:[24]2|5[13]))\"], \"(0$1)\"], [\"(\\\\d{5})(\\\\d{4})\", \"$1 $2\", [\"346|4(?:27|9[35])|883\", \"3469|4(?:279|9(?:30|56))|8834\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"2\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[3-7]|8[2-8]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"]], [\"(\\\\d{4})(\\\\d{1,2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3 $4\", [\"1\"]]], \"0\"], PK: [\"92\", \"00\", \"122\\\\d{6}|[24-8]\\\\d{10,11}|9(?:[013-9]\\\\d{8,10}|2(?:[01]\\\\d\\\\d|2(?:[06-8]\\\\d|1[01]))\\\\d{7})|(?:[2-8]\\\\d{3}|92(?:[0-7]\\\\d|8[1-9]))\\\\d{6}|[24-9]\\\\d{8}|[89]\\\\d{7}\", [8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,7})\", \"$1 $2 $3\", [\"[89]0\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"1\"]], [\"(\\\\d{3})(\\\\d{6,7})\", \"$1 $2\", [\"2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:2[2-8]|3[27-9]|4[2-6]|6[3569]|9[25-8])\", \"9(?:2[3-8]|98)|(?:2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:22|3[27-9]|4[2-6]|6[3569]|9[25-7]))[2-9]\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{7,8})\", \"$1 $2\", [\"(?:2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91)[2-9]\"], \"(0$1)\"], [\"(\\\\d{5})(\\\\d{5})\", \"$1 $2\", [\"58\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{7})\", \"$1 $2\", [\"3\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"[24-9]\"], \"(0$1)\"]], \"0\"], PL: [\"48\", \"00\", \"(?:6|8\\\\d\\\\d)\\\\d{7}|[1-9]\\\\d{6}(?:\\\\d{2})?|[26]\\\\d{5}\", [6, 7, 8, 9, 10], [[\"(\\\\d{5})\", \"$1\", [\"19\"]], [\"(\\\\d{3})(\\\\d{3})\", \"$1 $2\", [\"11|20|64\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])1\", \"(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])19\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"64\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"21|39|45|5[0137]|6[0469]|7[02389]|8(?:0[14]|8)\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"1[2-8]|[2-7]|8[1-79]|9[145]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"8\"]]]], PM: [\"508\", \"00\", \"[45]\\\\d{5}|(?:708|80\\\\d)\\\\d{6}\", [6, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[45]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"7\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\"], PR: [\"1\", \"011\", \"(?:[589]\\\\d\\\\d|787)\\\\d{7}\", [10], 0, \"1\", 0, 0, 0, 0, \"787|939\"], PS: [\"970\", \"00\", \"[2489]2\\\\d{6}|(?:1\\\\d|5)\\\\d{8}\", [8, 9, 10], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2489]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"5\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"]]], \"0\"], PT: [\"351\", \"00\", \"1693\\\\d{5}|(?:[26-9]\\\\d|30)\\\\d{7}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"2[12]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"16|[236-9]\"]]]], PW: [\"680\", \"01[12]\", \"(?:[24-8]\\\\d\\\\d|345|900)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-9]\"]]]], PY: [\"595\", \"00\", \"59\\\\d{4,6}|9\\\\d{5,10}|(?:[2-46-8]\\\\d|5[0-8])\\\\d{4,7}\", [6, 7, 8, 9, 10, 11], [[\"(\\\\d{3})(\\\\d{3,6})\", \"$1 $2\", [\"[2-9]0\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"[26]1|3[289]|4[1246-8]|7[1-3]|8[1-36]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{4,5})\", \"$1 $2\", [\"2[279]|3[13-5]|4[359]|5|6(?:[34]|7[1-46-8])|7[46-8]|85\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2[14-68]|3[26-9]|4[1246-8]|6(?:1|75)|7[1-35]|8[1-36]\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"87\"]], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"9(?:[5-79]|8[1-6])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-8]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"9\"]]], \"0\"], QA: [\"974\", \"00\", \"800\\\\d{4}|(?:2|800)\\\\d{6}|(?:0080|[3-7])\\\\d{7}\", [7, 8, 9, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"2[16]|8\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[3-7]\"]]]], RE: [\"262\", \"00\", \"(?:26|[689]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2689]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, [[\"26(?:2\\\\d\\\\d|3(?:0\\\\d|1[0-6]))\\\\d{4}\"], [\"69(?:2\\\\d\\\\d|3(?:[06][0-6]|1[013]|2[0-2]|3[0-39]|4\\\\d|5[0-5]|7[0-37]|8[0-8]|9[0-479]))\\\\d{4}\"], [\"80\\\\d{7}\"], [\"89[1-37-9]\\\\d{6}\"], 0, 0, 0, 0, [\"9(?:399[0-3]|479[0-5]|76(?:2[27]|3[0-37]))\\\\d{4}\"], [\"8(?:1[019]|2[0156]|84|90)\\\\d{6}\"]]], RO: [\"40\", \"00\", \"(?:[2378]\\\\d|62|90)\\\\d{7}|[23]\\\\d{5}\", [6, 9], [[\"(\\\\d{3})(\\\\d{3})\", \"$1 $2\", [\"2[3-6]\", \"2[3-6]\\\\d9\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})\", \"$1 $2\", [\"219|31\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[23]1\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[236-9]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, 0, 0, \" int \"], RS: [\"381\", \"00\", \"38[02-9]\\\\d{6,9}|6\\\\d{7,9}|90\\\\d{4,8}|38\\\\d{5,6}|(?:7\\\\d\\\\d|800)\\\\d{3,9}|(?:[12]\\\\d|3[0-79])\\\\d{5,10}\", [6, 7, 8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{3,9})\", \"$1 $2\", [\"(?:2[389]|39)0|[7-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{5,10})\", \"$1 $2\", [\"[1-36]\"], \"0$1\"]], \"0\"], RU: [\"7\", \"810\", \"8\\\\d{13}|[347-9]\\\\d{9}\", [10, 14], [[\"(\\\\d{4})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"7(?:1[0-8]|2[1-9])\", \"7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:1[23]|[2-9]2))\", \"7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:13[03-69]|62[013-9]))|72[1-57-9]2\"], \"8 ($1)\", 1], [\"(\\\\d{5})(\\\\d)(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"7(?:1[0-68]|2[1-9])\", \"7(?:1(?:[06][3-6]|[18]|2[35]|[3-5][3-5])|2(?:[13][3-5]|[24-689]|7[457]))\", \"7(?:1(?:0(?:[356]|4[023])|[18]|2(?:3[013-9]|5)|3[45]|43[013-79]|5(?:3[1-8]|4[1-7]|5)|6(?:3[0-35-9]|[4-6]))|2(?:1(?:3[178]|[45])|[24-689]|3[35]|7[457]))|7(?:14|23)4[0-8]|71(?:33|45)[1-79]\"], \"8 ($1)\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"8 ($1)\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"[349]|8(?:[02-7]|1[1-8])\"], \"8 ($1)\", 1], [\"(\\\\d{4})(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"8\"], \"8 ($1)\"]], \"8\", 0, 0, 0, 0, \"3[04-689]|[489]\", 0, \"8~10\"], RW: [\"250\", \"00\", \"(?:06|[27]\\\\d\\\\d|[89]00)\\\\d{6}\", [8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"0\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[7-9]\"], \"0$1\"]], \"0\"], SA: [\"966\", \"00\", \"92\\\\d{7}|(?:[15]|8\\\\d)\\\\d{8}\", [9, 10], [[\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"9\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"5\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"81\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]]], \"0\"], SB: [\"677\", \"0[01]\", \"(?:[1-6]|[7-9]\\\\d\\\\d)\\\\d{4}\", [5, 7], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"7|8[4-9]|9(?:[1-8]|9[0-8])\"]]]], SC: [\"248\", \"010|0[0-2]\", \"800\\\\d{4}|(?:[249]\\\\d|64)\\\\d{5}\", [7], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[246]|9[57]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"], SD: [\"249\", \"00\", \"[19]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[19]\"], \"0$1\"]], \"0\"], SE: [\"46\", \"00\", \"(?:[26]\\\\d\\\\d|9)\\\\d{9}|[1-9]\\\\d{8}|[1-689]\\\\d{7}|[1-4689]\\\\d{6}|2\\\\d{5}\", [6, 7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{2,3})(\\\\d{2})\", \"$1-$2 $3\", [\"20\"], \"0$1\", 0, \"$1 $2 $3\"], [\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"9(?:00|39|44|9)\"], \"0$1\", 0, \"$1 $2\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})\", \"$1-$2 $3\", [\"[12][136]|3[356]|4[0246]|6[03]|90[1-9]\"], \"0$1\", 0, \"$1 $2 $3\"], [\"(\\\\d)(\\\\d{2,3})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"8\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{3})(\\\\d{2,3})(\\\\d{2})\", \"$1-$2 $3\", [\"1[2457]|2(?:[247-9]|5[0138])|3[0247-9]|4[1357-9]|5[0-35-9]|6(?:[125689]|4[02-57]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])\"], \"0$1\", 0, \"$1 $2 $3\"], [\"(\\\\d{3})(\\\\d{2,3})(\\\\d{3})\", \"$1-$2 $3\", [\"9(?:00|39|44)\"], \"0$1\", 0, \"$1 $2 $3\"], [\"(\\\\d{2})(\\\\d{2,3})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"1[13689]|2[0136]|3[1356]|4[0246]|54|6[03]|90[1-9]\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"10|7\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"8\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"[13-5]|2(?:[247-9]|5[0138])|6(?:[124-689]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1-$2 $3 $4\", [\"9\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4 $5\", [\"[26]\"], \"0$1\", 0, \"$1 $2 $3 $4 $5\"]], \"0\"], SG: [\"65\", \"0[0-3]\\\\d\", \"(?:(?:1\\\\d|8)\\\\d\\\\d|7000)\\\\d{7}|[3689]\\\\d{7}\", [8, 10, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[369]|8(?:0[1-8]|[1-9])\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{4})(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"7\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"]]]], SH: [\"290\", \"00\", \"(?:[256]\\\\d|8)\\\\d{3}\", [4, 5], 0, 0, 0, 0, 0, 0, \"[256]\"], SI: [\"386\", \"00|10(?:22|66|88|99)\", \"[1-7]\\\\d{7}|8\\\\d{4,7}|90\\\\d{4,6}\", [5, 6, 7, 8], [[\"(\\\\d{2})(\\\\d{3,6})\", \"$1 $2\", [\"8[09]|9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"59|8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[37][01]|4[0139]|51|6\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[1-57]\"], \"(0$1)\"]], \"0\", 0, 0, 0, 0, 0, 0, \"00\"], SJ: [\"47\", \"00\", \"0\\\\d{4}|(?:[489]\\\\d|79)\\\\d{6}\", [5, 8], 0, 0, 0, 0, 0, 0, \"79\"], SK: [\"421\", \"00\", \"[2-689]\\\\d{8}|[2-59]\\\\d{6}|[2-5]\\\\d{5}\", [6, 7, 9], [[\"(\\\\d)(\\\\d{2})(\\\\d{3,4})\", \"$1 $2 $3\", [\"21\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"[3-5][1-8]1\", \"[3-5][1-8]1[67]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{2})\", \"$1/$2 $3 $4\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[689]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1/$2 $3 $4\", [\"[3-5]\"], \"0$1\"]], \"0\"], SL: [\"232\", \"00\", \"(?:[237-9]\\\\d|66)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[236-9]\"], \"(0$1)\"]], \"0\"], SM: [\"378\", \"00\", \"(?:0549|[5-7]\\\\d)\\\\d{6}\", [8, 10], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-7]\"]], [\"(\\\\d{4})(\\\\d{6})\", \"$1 $2\", [\"0\"]]], 0, 0, \"([89]\\\\d{5})$\", \"0549$1\"], SN: [\"221\", \"00\", \"(?:[378]\\\\d|93)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[379]\"]]]], SO: [\"252\", \"00\", \"[346-9]\\\\d{8}|[12679]\\\\d{7}|[1-5]\\\\d{6}|[1348]\\\\d{5}\", [6, 7, 8, 9], [[\"(\\\\d{2})(\\\\d{4})\", \"$1 $2\", [\"8[125]\"]], [\"(\\\\d{6})\", \"$1\", [\"[134]\"]], [\"(\\\\d)(\\\\d{6})\", \"$1 $2\", [\"[15]|2[0-79]|3[0-46-8]|4[0-7]\"]], [\"(\\\\d)(\\\\d{7})\", \"$1 $2\", [\"(?:2|90)4|[67]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[348]|64|79|90\"]], [\"(\\\\d{2})(\\\\d{5,7})\", \"$1 $2\", [\"1|28|6[0-35-9]|77|9[2-9]\"]]], \"0\"], SR: [\"597\", \"00\", \"(?:[2-5]|68|[78]\\\\d)\\\\d{5}\", [6, 7], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1-$2-$3\", [\"56\"]], [\"(\\\\d{3})(\\\\d{3})\", \"$1-$2\", [\"[2-5]\"]], [\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[6-8]\"]]]], SS: [\"211\", \"00\", \"[19]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[19]\"], \"0$1\"]], \"0\"], ST: [\"239\", \"00\", \"(?:22|9\\\\d)\\\\d{5}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[29]\"]]]], SV: [\"503\", \"00\", \"[267]\\\\d{7}|[89]00\\\\d{4}(?:\\\\d{4})?\", [7, 8, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[89]\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[267]\"]], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"]]]], SX: [\"1\", \"011\", \"7215\\\\d{6}|(?:[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"(5\\\\d{6})$|1\", \"721$1\", 0, \"721\"], SY: [\"963\", \"00\", \"[1-39]\\\\d{8}|[1-5]\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[1-5]\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"9\"], \"0$1\", 1]], \"0\"], SZ: [\"268\", \"00\", \"0800\\\\d{4}|(?:[237]\\\\d|900)\\\\d{6}\", [8, 9], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[0237]\"]], [\"(\\\\d{5})(\\\\d{4})\", \"$1 $2\", [\"9\"]]]], TA: [\"290\", \"00\", \"8\\\\d{3}\", [4], 0, 0, 0, 0, 0, 0, \"8\"], TC: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|649|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-479]\\\\d{6})$|1\", \"649$1\", 0, \"649\"], TD: [\"235\", \"00|16\", \"(?:22|[69]\\\\d|77)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2679]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"], TG: [\"228\", \"00\", \"[279]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[279]\"]]]], TH: [\"66\", \"00[1-9]\", \"(?:001800|[2-57]|[689]\\\\d)\\\\d{7}|1\\\\d{7,9}\", [8, 9, 10, 13], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[13-9]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"]]], \"0\"], TJ: [\"992\", \"810\", \"[0-57-9]\\\\d{8}\", [9], [[\"(\\\\d{6})(\\\\d)(\\\\d{2})\", \"$1 $2 $3\", [\"331\", \"3317\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"44[02-479]|[34]7\"]], [\"(\\\\d{4})(\\\\d)(\\\\d{4})\", \"$1 $2 $3\", [\"3[1-5]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[0-57-9]\"]]], 0, 0, 0, 0, 0, 0, 0, \"8~10\"], TK: [\"690\", \"00\", \"[2-47]\\\\d{3,6}\", [4, 5, 6, 7]], TL: [\"670\", \"00\", \"7\\\\d{7}|(?:[2-47]\\\\d|[89]0)\\\\d{5}\", [7, 8], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-489]|70\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"7\"]]]], TM: [\"993\", \"810\", \"[1-6]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"12\"], \"(8 $1)\"], [\"(\\\\d{3})(\\\\d)(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"[1-5]\"], \"(8 $1)\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"6\"], \"8 $1\"]], \"8\", 0, 0, 0, 0, 0, 0, \"8~10\"], TN: [\"216\", \"00\", \"[2-57-9]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-57-9]\"]]]], TO: [\"676\", \"00\", \"(?:0800|(?:[5-8]\\\\d\\\\d|999)\\\\d)\\\\d{3}|[2-8]\\\\d{4}\", [5, 7], [[\"(\\\\d{2})(\\\\d{3})\", \"$1-$2\", [\"[2-4]|50|6[09]|7[0-24-69]|8[05]\"]], [\"(\\\\d{4})(\\\\d{3})\", \"$1 $2\", [\"0\"]], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[5-9]\"]]]], TR: [\"90\", \"00\", \"4\\\\d{6}|8\\\\d{11,12}|(?:[2-58]\\\\d\\\\d|900)\\\\d{7}\", [7, 10, 12, 13], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"512|8[01589]|90\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"5(?:[0-59]|61)\", \"5(?:[0-59]|61[06])\", \"5(?:[0-59]|61[06]1)\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[24][1-8]|3[1-9]\"], \"(0$1)\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{6,7})\", \"$1 $2 $3\", [\"80\"], \"0$1\", 1]], \"0\"], TT: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-46-8]\\\\d{6})$|1\", \"868$1\", 0, \"868\"], TV: [\"688\", \"00\", \"(?:2|7\\\\d\\\\d|90)\\\\d{4}\", [5, 6, 7], [[\"(\\\\d{2})(\\\\d{3})\", \"$1 $2\", [\"2\"]], [\"(\\\\d{2})(\\\\d{4})\", \"$1 $2\", [\"90\"]], [\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"7\"]]]], TW: [\"886\", \"0(?:0[25-79]|19)\", \"[2-689]\\\\d{8}|7\\\\d{9,10}|[2-8]\\\\d{7}|2\\\\d{6}\", [7, 8, 9, 10, 11], [[\"(\\\\d{2})(\\\\d)(\\\\d{4})\", \"$1 $2 $3\", [\"202\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[258]0\"], \"0$1\"], [\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"[23568]|4(?:0[02-48]|[1-47-9])|7[1-9]\", \"[23568]|4(?:0[2-48]|[1-47-9])|(?:400|7)[1-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[49]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4,5})\", \"$1 $2 $3\", [\"7\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, 0, 0, \"#\"], TZ: [\"255\", \"00[056]\", \"(?:[25-8]\\\\d|41|90)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[24]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"5\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[67]\"], \"0$1\"]], \"0\"], UA: [\"380\", \"00\", \"[89]\\\\d{9}|[3-9]\\\\d{8}\", [9, 10], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6[12][29]|(?:3[1-8]|4[136-8]|5[12457]|6[49])2|(?:56|65)[24]\", \"6[12][29]|(?:35|4[1378]|5[12457]|6[49])2|(?:56|65)[24]|(?:3[1-46-8]|46)2[013-9]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6[0135689]|7[4-6])|6(?:[12][3-7]|[459])\", \"3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6(?:[015689]|3[02389])|7[4-6])|6(?:[12][3-7]|[459])\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[3-7]|89|9[1-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, 0, \"0~0\"], UG: [\"256\", \"00[057]\", \"800\\\\d{6}|(?:[29]0|[347]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"202\", \"2024\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"[27-9]|4(?:6[45]|[7-9])\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"[34]\"], \"0$1\"]], \"0\"], US: [\"1\", \"011\", \"[2-9]\\\\d{9}|3\\\\d{6}\", [10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"310\"], 0, 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"($1) $2-$3\", [\"[2-9]\"], 0, 1, \"$1-$2-$3\"]], \"1\", 0, 0, 0, 0, 0, [[\"(?:5056(?:[0-35-9]\\\\d|4[468])|73020\\\\d)\\\\d{4}|(?:4722|505[2-57-9]|983[289])\\\\d{6}|(?:2(?:0[1-35-9]|1[02-9]|2[03-57-9]|3[149]|4[08]|5[1-46]|6[0279]|7[0269]|8[13])|3(?:0[1-57-9]|1[02-9]|2[013569]|3[0-24679]|4[167]|5[0-2]|6[0149]|8[056])|4(?:0[124-9]|1[02-579]|2[3-5]|3[0245]|4[023578]|58|6[349]|7[0589]|8[04])|5(?:0[1-47-9]|1[0235-8]|20|3[0149]|4[01]|5[179]|6[1-47]|7[0-5]|8[0256])|6(?:0[1-35-9]|1[024-9]|2[03689]|[34][016]|5[01679]|6[0-279]|78|8[0-29])|7(?:0[1-46-8]|1[2-9]|2[04-7]|3[1247]|4[037]|5[47]|6[02359]|7[0-59]|8[156])|8(?:0[1-68]|1[02-8]|2[068]|3[0-2589]|4[03578]|5[046-9]|6[02-5]|7[028])|9(?:0[1346-9]|1[02-9]|2[0589]|3[0146-8]|4[01357-9]|5[12469]|7[0-389]|8[04-69]))[2-9]\\\\d{6}\"], [\"\"], [\"8(?:00|33|44|55|66|77|88)[2-9]\\\\d{6}\"], [\"900[2-9]\\\\d{6}\"], [\"52(?:3(?:[2-46-9][02-9]\\\\d|5(?:[02-46-9]\\\\d|5[0-46-9]))|4(?:[2-478][02-9]\\\\d|5(?:[034]\\\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\\\d)|9(?:[05-9]\\\\d|2[0-5]|49)))\\\\d{4}|52[34][2-9]1[02-9]\\\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\\\d{6}\"]]], UY: [\"598\", \"0(?:0|1[3-9]\\\\d)\", \"0004\\\\d{2,9}|[1249]\\\\d{7}|(?:[49]\\\\d|80)\\\\d{5}\", [6, 7, 8, 9, 10, 11, 12, 13], [[\"(\\\\d{3})(\\\\d{3,4})\", \"$1 $2\", [\"0\"]], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[49]0|8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"9\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[124]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2,4})\", \"$1 $2 $3\", [\"0\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{2,4})\", \"$1 $2 $3 $4\", [\"0\"]]], \"0\", 0, 0, 0, 0, 0, 0, \"00\", \" int. \"], UZ: [\"998\", \"810\", \"(?:20|33|[5-79]\\\\d|88)\\\\d{7}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[235-9]\"], \"8 $1\"]], \"8\", 0, 0, 0, 0, 0, 0, \"8~10\"], VA: [\"39\", \"00\", \"0\\\\d{5,10}|3[0-8]\\\\d{7,10}|55\\\\d{8}|8\\\\d{5}(?:\\\\d{2,4})?|(?:1\\\\d|39)\\\\d{7,8}\", [6, 7, 8, 9, 10, 11], 0, 0, 0, 0, 0, 0, \"06698\"], VC: [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|784|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-7]\\\\d{6})$|1\", \"784$1\", 0, \"784\"], VE: [\"58\", \"00\", \"[68]00\\\\d{7}|(?:[24]\\\\d|[59]0)\\\\d{8}\", [10], [[\"(\\\\d{3})(\\\\d{7})\", \"$1-$2\", [\"[24-689]\"], \"0$1\"]], \"0\"], VG: [\"1\", \"011\", \"(?:284|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-578]\\\\d{6})$|1\", \"284$1\", 0, \"284\"], VI: [\"1\", \"011\", \"[58]\\\\d{9}|(?:34|90)0\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"340$1\", 0, \"340\"], VN: [\"84\", \"00\", \"[12]\\\\d{9}|[135-9]\\\\d{8}|[16]\\\\d{7}|[16-8]\\\\d{6}\", [7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"80\"], \"0$1\", 1], [\"(\\\\d{4})(\\\\d{4,6})\", \"$1 $2\", [\"1\"], 0, 1], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"6\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[357-9]\"], \"0$1\", 1], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"2[48]\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"2\"], \"0$1\", 1]], \"0\"], VU: [\"678\", \"00\", \"[57-9]\\\\d{6}|(?:[238]\\\\d|48)\\\\d{3}\", [5, 7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[57-9]\"]]]], WF: [\"681\", \"00\", \"(?:40|72)\\\\d{4}|8\\\\d{5}(?:\\\\d{3})?\", [6, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[478]\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"]]]], WS: [\"685\", \"0\", \"(?:[2-6]|8\\\\d{5})\\\\d{4}|[78]\\\\d{6}|[68]\\\\d{5}\", [5, 6, 7, 10], [[\"(\\\\d{5})\", \"$1\", [\"[2-5]|6[1-9]\"]], [\"(\\\\d{3})(\\\\d{3,7})\", \"$1 $2\", [\"[68]\"]], [\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"7\"]]]], XK: [\"383\", \"00\", \"[23]\\\\d{7,8}|(?:4\\\\d\\\\d|[89]00)\\\\d{5}\", [8, 9], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-4]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[23]\"], \"0$1\"]], \"0\"], YE: [\"967\", \"00\", \"(?:1|7\\\\d)\\\\d{7}|[1-7]\\\\d{6}\", [7, 8, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[1-6]|7(?:[24-6]|8[0-7])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"7\"], \"0$1\"]], \"0\"], YT: [\"262\", \"00\", \"(?:80|9\\\\d)\\\\d{7}|(?:26|63)9\\\\d{6}\", [9], 0, \"0\", 0, 0, 0, 0, 0, [[\"269(?:0[0-467]|5[0-4]|6\\\\d|[78]0)\\\\d{4}\"], [\"639(?:0[0-79]|1[019]|[267]\\\\d|3[09]|40|5[05-9]|9[04-79])\\\\d{4}\"], [\"80\\\\d{7}\"], 0, 0, 0, 0, 0, [\"9(?:(?:39|47)8[01]|769\\\\d)\\\\d{4}\"]]], ZA: [\"27\", \"00\", \"[1-79]\\\\d{8}|8\\\\d{4,9}\", [5, 6, 7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{3,4})\", \"$1 $2\", [\"8[1-4]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\", \"$1 $2 $3\", [\"8[1-4]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"860\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[1-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"], \"0$1\"]], \"0\"], ZM: [\"260\", \"00\", \"800\\\\d{6}|(?:21|63|[79]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[28]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"[79]\"], \"0$1\"]], \"0\"], ZW: [\"263\", \"00\", \"2(?:[0-57-9]\\\\d{6,8}|6[0-24-9]\\\\d{6,7})|[38]\\\\d{9}|[35-8]\\\\d{8}|[3-6]\\\\d{7}|[1-689]\\\\d{6}|[1-3569]\\\\d{5}|[1356]\\\\d{4}\", [5, 6, 7, 8, 9, 10], [[\"(\\\\d{3})(\\\\d{3,5})\", \"$1 $2\", [\"2(?:0[45]|2[278]|[49]8)|3(?:[09]8|17)|6(?:[29]8|37|75)|[23][78]|(?:33|5[15]|6[68])[78]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{2,4})\", \"$1 $2 $3\", [\"[49]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"80\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"24|8[13-59]|(?:2[05-79]|39|5[45]|6[15-8])2\", \"2(?:02[014]|4|[56]20|[79]2)|392|5(?:42|525)|6(?:[16-8]21|52[013])|8[13-59]\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2(?:1[39]|2[0157]|[378]|[56][14])|3(?:12|29)\", \"2(?:1[39]|2[0157]|[378]|[56][14])|3(?:123|29)\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{6})\", \"$1 $2\", [\"8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3,5})\", \"$1 $2\", [\"1|2(?:0[0-36-9]|12|29|[56])|3(?:1[0-689]|[24-6])|5(?:[0236-9]|1[2-4])|6(?:[013-59]|7[0-46-9])|(?:33|55|6[68])[0-69]|(?:29|3[09]|62)[0-79]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"29[013-9]|39|54\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3,5})\", \"$1 $2\", [\"(?:25|54)8\", \"258|5483\"], \"0$1\"]], \"0\"] }, nonGeographic: { 800: [\"800\", 0, \"(?:00|[1-9]\\\\d)\\\\d{6}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"\\\\d\"]]], 0, 0, 0, 0, 0, 0, [0, 0, [\"(?:00|[1-9]\\\\d)\\\\d{6}\"]]], 808: [\"808\", 0, \"[1-9]\\\\d{7}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[1-9]\"]]], 0, 0, 0, 0, 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, [\"[1-9]\\\\d{7}\"]]], 870: [\"870\", 0, \"7\\\\d{11}|[35-7]\\\\d{8}\", [9, 12], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[35-7]\"]]], 0, 0, 0, 0, 0, 0, [0, [\"(?:[356]|774[45])\\\\d{8}|7[6-8]\\\\d{7}\"]]], 878: [\"878\", 0, \"10\\\\d{10}\", [12], [[\"(\\\\d{2})(\\\\d{5})(\\\\d{5})\", \"$1 $2 $3\", [\"1\"]]], 0, 0, 0, 0, 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, [\"10\\\\d{10}\"]]], 881: [\"881\", 0, \"6\\\\d{9}|[0-36-9]\\\\d{8}\", [9, 10], [[\"(\\\\d)(\\\\d{3})(\\\\d{5})\", \"$1 $2 $3\", [\"[0-37-9]\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{5,6})\", \"$1 $2 $3\", [\"6\"]]], 0, 0, 0, 0, 0, 0, [0, [\"6\\\\d{9}|[0-36-9]\\\\d{8}\"]]], 882: [\"882\", 0, \"[13]\\\\d{6}(?:\\\\d{2,5})?|[19]\\\\d{7}|(?:[25]\\\\d\\\\d|4)\\\\d{7}(?:\\\\d{2})?\", [7, 8, 9, 10, 11, 12], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"16|342\"]], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"49\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"1[36]|9\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"3[23]\"]], [\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"16\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"10|23|3(?:[15]|4[57])|4|51\"]], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"34\"]], [\"(\\\\d{2})(\\\\d{4,5})(\\\\d{5})\", \"$1 $2 $3\", [\"[1-35]\"]]], 0, 0, 0, 0, 0, 0, [0, [\"342\\\\d{4}|(?:337|49)\\\\d{6}|(?:3(?:2|47|7\\\\d{3})|50\\\\d{3})\\\\d{7}\", [7, 8, 9, 10, 12]], 0, 0, 0, 0, 0, 0, [\"1(?:3(?:0[0347]|[13][0139]|2[035]|4[013568]|6[0459]|7[06]|8[15-8]|9[0689])\\\\d{4}|6\\\\d{5,10})|(?:345\\\\d|9[89])\\\\d{6}|(?:10|2(?:3|85\\\\d)|3(?:[15]|[69]\\\\d\\\\d)|4[15-8]|51)\\\\d{8}\"]]], 883: [\"883\", 0, \"(?:[1-4]\\\\d|51)\\\\d{6,10}\", [8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,8})\", \"$1 $2 $3\", [\"[14]|2[24-689]|3[02-689]|51[24-9]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"510\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"21\"]], [\"(\\\\d{4})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"51[13]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"[235]\"]]], 0, 0, 0, 0, 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, [\"(?:2(?:00\\\\d\\\\d|10)|(?:370[1-9]|51\\\\d0)\\\\d)\\\\d{7}|51(?:00\\\\d{5}|[24-9]0\\\\d{4,7})|(?:1[0-79]|2[24-689]|3[02-689]|4[0-4])0\\\\d{5,9}\"]]], 888: [\"888\", 0, \"\\\\d{11}\", [11], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\", \"$1 $2 $3\"]], 0, 0, 0, 0, 0, 0, [0, 0, 0, 0, 0, 0, [\"\\\\d{11}\"]]], 979: [\"979\", 0, \"[1359]\\\\d{8}\", [9], [[\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[1359]\"]]], 0, 0, 0, 0, 0, 0, [0, 0, 0, [\"[1359]\\\\d{8}\"]]] } };\nfunction Ad(e, d) {\n  var t = Array.prototype.slice.call(d);\n  return t.push(xd), e.apply(this, t);\n}\nfunction ie(e) {\n  \"@babel/helpers - typeof\";\n  return ie = typeof Symbol == \"function\" && typeof Symbol.iterator == \"symbol\" ? function(d) {\n    return typeof d;\n  } : function(d) {\n    return d && typeof Symbol == \"function\" && d.constructor === Symbol && d !== Symbol.prototype ? \"symbol\" : typeof d;\n  }, ie(e);\n}\nfunction Oe(e, d) {\n  for (var t = 0; t < d.length; t++) {\n    var n = d[t];\n    n.enumerable = n.enumerable || !1, n.configurable = !0, \"value\" in n && (n.writable = !0), Object.defineProperty(e, n.key, n);\n  }\n}\nfunction Dd(e, d, t) {\n  return d && Oe(e.prototype, d), t && Oe(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e;\n}\nfunction Rd(e, d) {\n  if (!(e instanceof d))\n    throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction Md(e, d) {\n  if (typeof d != \"function\" && d !== null)\n    throw new TypeError(\"Super expression must either be null or a function\");\n  e.prototype = Object.create(d && d.prototype, { constructor: { value: e, writable: !0, configurable: !0 } }), Object.defineProperty(e, \"prototype\", { writable: !1 }), d && k(e, d);\n}\nfunction Ld(e) {\n  var d = qe();\n  return function() {\n    var n = B(e), r;\n    if (d) {\n      var $ = B(this).constructor;\n      r = Reflect.construct(n, arguments, $);\n    } else\n      r = n.apply(this, arguments);\n    return kd(this, r);\n  };\n}\nfunction kd(e, d) {\n  if (d && (ie(d) === \"object\" || typeof d == \"function\"))\n    return d;\n  if (d !== void 0)\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return Ye(e);\n}\nfunction Ye(e) {\n  if (e === void 0)\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nfunction ae(e) {\n  var d = typeof Map == \"function\" ? /* @__PURE__ */ new Map() : void 0;\n  return ae = function(n) {\n    if (n === null || !Bd(n))\n      return n;\n    if (typeof n != \"function\")\n      throw new TypeError(\"Super expression must either be null or a function\");\n    if (typeof d < \"u\") {\n      if (d.has(n))\n        return d.get(n);\n      d.set(n, r);\n    }\n    function r() {\n      return U(n, arguments, B(this).constructor);\n    }\n    return r.prototype = Object.create(n.prototype, { constructor: { value: r, enumerable: !1, writable: !0, configurable: !0 } }), k(r, n);\n  }, ae(e);\n}\nfunction U(e, d, t) {\n  return qe() ? U = Reflect.construct : U = function(r, $, u) {\n    var o = [null];\n    o.push.apply(o, $);\n    var s = Function.bind.apply(r, o), i = new s();\n    return u && k(i, u.prototype), i;\n  }, U.apply(null, arguments);\n}\nfunction qe() {\n  if (typeof Reflect > \"u\" || !Reflect.construct || Reflect.construct.sham)\n    return !1;\n  if (typeof Proxy == \"function\")\n    return !0;\n  try {\n    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {\n    })), !0;\n  } catch {\n    return !1;\n  }\n}\nfunction Bd(e) {\n  return Function.toString.call(e).indexOf(\"[native code]\") !== -1;\n}\nfunction k(e, d) {\n  return k = Object.setPrototypeOf || function(n, r) {\n    return n.__proto__ = r, n;\n  }, k(e, d);\n}\nfunction B(e) {\n  return B = Object.setPrototypeOf ? Object.getPrototypeOf : function(t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, B(e);\n}\nvar w = /* @__PURE__ */ function(e) {\n  Md(t, e);\n  var d = Ld(t);\n  function t(n) {\n    var r;\n    return Rd(this, t), r = d.call(this, n), Object.setPrototypeOf(Ye(r), t.prototype), r.name = r.constructor.name, r;\n  }\n  return Dd(t);\n}(/* @__PURE__ */ ae(Error)), se = 2, Gd = 17, jd = 3, O = \"0-9０-９٠-٩۰-۹\", Ud = \"-‐-―−ー－\", Hd = \"／/\", Vd = \"．.\", Kd = \"  ­​⁠　\", Wd = \"()（）［］\\\\[\\\\]\", zd = \"~⁓∼～\", V = \"\".concat(Ud).concat(Hd).concat(Vd).concat(Kd).concat(Wd).concat(zd), ce = \"+＋\";\nfunction Pe(e, d) {\n  e = e.split(\"-\"), d = d.split(\"-\");\n  for (var t = e[0].split(\".\"), n = d[0].split(\".\"), r = 0; r < 3; r++) {\n    var $ = Number(t[r]), u = Number(n[r]);\n    if ($ > u)\n      return 1;\n    if (u > $)\n      return -1;\n    if (!isNaN($) && isNaN(u))\n      return 1;\n    if (isNaN($) && !isNaN(u))\n      return -1;\n  }\n  return e[1] && d[1] ? e[1] > d[1] ? 1 : e[1] < d[1] ? -1 : 0 : !e[1] && d[1] ? 1 : e[1] && !d[1] ? -1 : 0;\n}\nvar Xd = {}.constructor;\nfunction H(e) {\n  return e != null && e.constructor === Xd;\n}\nfunction $e(e) {\n  \"@babel/helpers - typeof\";\n  return $e = typeof Symbol == \"function\" && typeof Symbol.iterator == \"symbol\" ? function(d) {\n    return typeof d;\n  } : function(d) {\n    return d && typeof Symbol == \"function\" && d.constructor === Symbol && d !== Symbol.prototype ? \"symbol\" : typeof d;\n  }, $e(e);\n}\nfunction W(e, d) {\n  if (!(e instanceof d))\n    throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction Ne(e, d) {\n  for (var t = 0; t < d.length; t++) {\n    var n = d[t];\n    n.enumerable = n.enumerable || !1, n.configurable = !0, \"value\" in n && (n.writable = !0), Object.defineProperty(e, n.key, n);\n  }\n}\nfunction z(e, d, t) {\n  return d && Ne(e.prototype, d), t && Ne(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e;\n}\nvar Zd = \"1.2.0\", Yd = \"1.7.35\", we = \" ext. \", qd = /^\\d+$/, v = /* @__PURE__ */ function() {\n  function e(d) {\n    W(this, e), d0(d), this.metadata = d, Je.call(this, d);\n  }\n  return z(e, [{\n    key: \"getCountries\",\n    value: function() {\n      return Object.keys(this.metadata.countries).filter(function(t) {\n        return t !== \"001\";\n      });\n    }\n  }, {\n    key: \"getCountryMetadata\",\n    value: function(t) {\n      return this.metadata.countries[t];\n    }\n  }, {\n    key: \"nonGeographic\",\n    value: function() {\n      if (!(this.v1 || this.v2 || this.v3))\n        return this.metadata.nonGeographic || this.metadata.nonGeographical;\n    }\n  }, {\n    key: \"hasCountry\",\n    value: function(t) {\n      return this.getCountryMetadata(t) !== void 0;\n    }\n  }, {\n    key: \"hasCallingCode\",\n    value: function(t) {\n      if (this.getCountryCodesForCallingCode(t))\n        return !0;\n      if (this.nonGeographic()) {\n        if (this.nonGeographic()[t])\n          return !0;\n      } else {\n        var n = this.countryCallingCodes()[t];\n        if (n && n.length === 1 && n[0] === \"001\")\n          return !0;\n      }\n    }\n  }, {\n    key: \"isNonGeographicCallingCode\",\n    value: function(t) {\n      return this.nonGeographic() ? !!this.nonGeographic()[t] : !this.getCountryCodesForCallingCode(t);\n    }\n    // Deprecated.\n  }, {\n    key: \"country\",\n    value: function(t) {\n      return this.selectNumberingPlan(t);\n    }\n  }, {\n    key: \"selectNumberingPlan\",\n    value: function(t, n) {\n      if (t && qd.test(t) && (n = t, t = null), t && t !== \"001\") {\n        if (!this.hasCountry(t))\n          throw new Error(\"Unknown country: \".concat(t));\n        this.numberingPlan = new Ie(this.getCountryMetadata(t), this);\n      } else if (n) {\n        if (!this.hasCallingCode(n))\n          throw new Error(\"Unknown calling code: \".concat(n));\n        this.numberingPlan = new Ie(this.getNumberingPlanMetadata(n), this);\n      } else\n        this.numberingPlan = void 0;\n      return this;\n    }\n  }, {\n    key: \"getCountryCodesForCallingCode\",\n    value: function(t) {\n      var n = this.countryCallingCodes()[t];\n      if (n)\n        return n.length === 1 && n[0].length === 3 ? void 0 : n;\n    }\n  }, {\n    key: \"getCountryCodeForCallingCode\",\n    value: function(t) {\n      var n = this.getCountryCodesForCallingCode(t);\n      if (n)\n        return n[0];\n    }\n  }, {\n    key: \"getNumberingPlanMetadata\",\n    value: function(t) {\n      var n = this.getCountryCodeForCallingCode(t);\n      if (n)\n        return this.getCountryMetadata(n);\n      if (this.nonGeographic()) {\n        var r = this.nonGeographic()[t];\n        if (r)\n          return r;\n      } else {\n        var $ = this.countryCallingCodes()[t];\n        if ($ && $.length === 1 && $[0] === \"001\")\n          return this.metadata.countries[\"001\"];\n      }\n    }\n    // Deprecated.\n  }, {\n    key: \"countryCallingCode\",\n    value: function() {\n      return this.numberingPlan.callingCode();\n    }\n    // Deprecated.\n  }, {\n    key: \"IDDPrefix\",\n    value: function() {\n      return this.numberingPlan.IDDPrefix();\n    }\n    // Deprecated.\n  }, {\n    key: \"defaultIDDPrefix\",\n    value: function() {\n      return this.numberingPlan.defaultIDDPrefix();\n    }\n    // Deprecated.\n  }, {\n    key: \"nationalNumberPattern\",\n    value: function() {\n      return this.numberingPlan.nationalNumberPattern();\n    }\n    // Deprecated.\n  }, {\n    key: \"possibleLengths\",\n    value: function() {\n      return this.numberingPlan.possibleLengths();\n    }\n    // Deprecated.\n  }, {\n    key: \"formats\",\n    value: function() {\n      return this.numberingPlan.formats();\n    }\n    // Deprecated.\n  }, {\n    key: \"nationalPrefixForParsing\",\n    value: function() {\n      return this.numberingPlan.nationalPrefixForParsing();\n    }\n    // Deprecated.\n  }, {\n    key: \"nationalPrefixTransformRule\",\n    value: function() {\n      return this.numberingPlan.nationalPrefixTransformRule();\n    }\n    // Deprecated.\n  }, {\n    key: \"leadingDigits\",\n    value: function() {\n      return this.numberingPlan.leadingDigits();\n    }\n    // Deprecated.\n  }, {\n    key: \"hasTypes\",\n    value: function() {\n      return this.numberingPlan.hasTypes();\n    }\n    // Deprecated.\n  }, {\n    key: \"type\",\n    value: function(t) {\n      return this.numberingPlan.type(t);\n    }\n    // Deprecated.\n  }, {\n    key: \"ext\",\n    value: function() {\n      return this.numberingPlan.ext();\n    }\n  }, {\n    key: \"countryCallingCodes\",\n    value: function() {\n      return this.v1 ? this.metadata.country_phone_code_to_countries : this.metadata.country_calling_codes;\n    }\n    // Deprecated.\n  }, {\n    key: \"chooseCountryByCountryCallingCode\",\n    value: function(t) {\n      return this.selectNumberingPlan(t);\n    }\n  }, {\n    key: \"hasSelectedNumberingPlan\",\n    value: function() {\n      return this.numberingPlan !== void 0;\n    }\n  }]), e;\n}(), Ie = /* @__PURE__ */ function() {\n  function e(d, t) {\n    W(this, e), this.globalMetadataObject = t, this.metadata = d, Je.call(this, t.metadata);\n  }\n  return z(e, [{\n    key: \"callingCode\",\n    value: function() {\n      return this.metadata[0];\n    }\n    // Formatting information for regions which share\n    // a country calling code is contained by only one region\n    // for performance reasons. For example, for NANPA region\n    // (\"North American Numbering Plan Administration\",\n    //  which includes USA, Canada, Cayman Islands, Bahamas, etc)\n    // it will be contained in the metadata for `US`.\n  }, {\n    key: \"getDefaultCountryMetadataForRegion\",\n    value: function() {\n      return this.globalMetadataObject.getNumberingPlanMetadata(this.callingCode());\n    }\n    // Is always present.\n  }, {\n    key: \"IDDPrefix\",\n    value: function() {\n      if (!(this.v1 || this.v2))\n        return this.metadata[1];\n    }\n    // Is only present when a country supports multiple IDD prefixes.\n  }, {\n    key: \"defaultIDDPrefix\",\n    value: function() {\n      if (!(this.v1 || this.v2))\n        return this.metadata[12];\n    }\n  }, {\n    key: \"nationalNumberPattern\",\n    value: function() {\n      return this.v1 || this.v2 ? this.metadata[1] : this.metadata[2];\n    }\n    // \"possible length\" data is always present in Google's metadata.\n  }, {\n    key: \"possibleLengths\",\n    value: function() {\n      if (!this.v1)\n        return this.metadata[this.v2 ? 2 : 3];\n    }\n  }, {\n    key: \"_getFormats\",\n    value: function(t) {\n      return t[this.v1 ? 2 : this.v2 ? 3 : 4];\n    }\n    // For countries of the same region (e.g. NANPA)\n    // formats are all stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n  }, {\n    key: \"formats\",\n    value: function() {\n      var t = this, n = this._getFormats(this.metadata) || this._getFormats(this.getDefaultCountryMetadataForRegion()) || [];\n      return n.map(function(r) {\n        return new Jd(r, t);\n      });\n    }\n  }, {\n    key: \"nationalPrefix\",\n    value: function() {\n      return this.metadata[this.v1 ? 3 : this.v2 ? 4 : 5];\n    }\n  }, {\n    key: \"_getNationalPrefixFormattingRule\",\n    value: function(t) {\n      return t[this.v1 ? 4 : this.v2 ? 5 : 6];\n    }\n    // For countries of the same region (e.g. NANPA)\n    // national prefix formatting rule is stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n  }, {\n    key: \"nationalPrefixFormattingRule\",\n    value: function() {\n      return this._getNationalPrefixFormattingRule(this.metadata) || this._getNationalPrefixFormattingRule(this.getDefaultCountryMetadataForRegion());\n    }\n  }, {\n    key: \"_nationalPrefixForParsing\",\n    value: function() {\n      return this.metadata[this.v1 ? 5 : this.v2 ? 6 : 7];\n    }\n  }, {\n    key: \"nationalPrefixForParsing\",\n    value: function() {\n      return this._nationalPrefixForParsing() || this.nationalPrefix();\n    }\n  }, {\n    key: \"nationalPrefixTransformRule\",\n    value: function() {\n      return this.metadata[this.v1 ? 6 : this.v2 ? 7 : 8];\n    }\n  }, {\n    key: \"_getNationalPrefixIsOptionalWhenFormatting\",\n    value: function() {\n      return !!this.metadata[this.v1 ? 7 : this.v2 ? 8 : 9];\n    }\n    // For countries of the same region (e.g. NANPA)\n    // \"national prefix is optional when formatting\" flag is\n    // stored in the \"main\" country for that region.\n    // E.g. \"RU\" and \"KZ\", \"US\" and \"CA\".\n  }, {\n    key: \"nationalPrefixIsOptionalWhenFormattingInNationalFormat\",\n    value: function() {\n      return this._getNationalPrefixIsOptionalWhenFormatting(this.metadata) || this._getNationalPrefixIsOptionalWhenFormatting(this.getDefaultCountryMetadataForRegion());\n    }\n  }, {\n    key: \"leadingDigits\",\n    value: function() {\n      return this.metadata[this.v1 ? 8 : this.v2 ? 9 : 10];\n    }\n  }, {\n    key: \"types\",\n    value: function() {\n      return this.metadata[this.v1 ? 9 : this.v2 ? 10 : 11];\n    }\n  }, {\n    key: \"hasTypes\",\n    value: function() {\n      return this.types() && this.types().length === 0 ? !1 : !!this.types();\n    }\n  }, {\n    key: \"type\",\n    value: function(t) {\n      if (this.hasTypes() && Ee(this.types(), t))\n        return new e0(Ee(this.types(), t), this);\n    }\n  }, {\n    key: \"ext\",\n    value: function() {\n      return this.v1 || this.v2 ? we : this.metadata[13] || we;\n    }\n  }]), e;\n}(), Jd = /* @__PURE__ */ function() {\n  function e(d, t) {\n    W(this, e), this._format = d, this.metadata = t;\n  }\n  return z(e, [{\n    key: \"pattern\",\n    value: function() {\n      return this._format[0];\n    }\n  }, {\n    key: \"format\",\n    value: function() {\n      return this._format[1];\n    }\n  }, {\n    key: \"leadingDigitsPatterns\",\n    value: function() {\n      return this._format[2] || [];\n    }\n  }, {\n    key: \"nationalPrefixFormattingRule\",\n    value: function() {\n      return this._format[3] || this.metadata.nationalPrefixFormattingRule();\n    }\n  }, {\n    key: \"nationalPrefixIsOptionalWhenFormattingInNationalFormat\",\n    value: function() {\n      return !!this._format[4] || this.metadata.nationalPrefixIsOptionalWhenFormattingInNationalFormat();\n    }\n  }, {\n    key: \"nationalPrefixIsMandatoryWhenFormattingInNationalFormat\",\n    value: function() {\n      return this.usesNationalPrefix() && !this.nationalPrefixIsOptionalWhenFormattingInNationalFormat();\n    }\n    // Checks whether national prefix formatting rule contains national prefix.\n  }, {\n    key: \"usesNationalPrefix\",\n    value: function() {\n      return !!(this.nationalPrefixFormattingRule() && // Check that national prefix formatting rule is not a \"dummy\" one.\n      !Qd.test(this.nationalPrefixFormattingRule()));\n    }\n  }, {\n    key: \"internationalFormat\",\n    value: function() {\n      return this._format[5] || this.format();\n    }\n  }]), e;\n}(), Qd = /^\\(?\\$1\\)?$/, e0 = /* @__PURE__ */ function() {\n  function e(d, t) {\n    W(this, e), this.type = d, this.metadata = t;\n  }\n  return z(e, [{\n    key: \"pattern\",\n    value: function() {\n      return this.metadata.v1 ? this.type : this.type[0];\n    }\n  }, {\n    key: \"possibleLengths\",\n    value: function() {\n      if (!this.metadata.v1)\n        return this.type[1] || this.metadata.possibleLengths();\n    }\n  }]), e;\n}();\nfunction Ee(e, d) {\n  switch (d) {\n    case \"FIXED_LINE\":\n      return e[0];\n    case \"MOBILE\":\n      return e[1];\n    case \"TOLL_FREE\":\n      return e[2];\n    case \"PREMIUM_RATE\":\n      return e[3];\n    case \"PERSONAL_NUMBER\":\n      return e[4];\n    case \"VOICEMAIL\":\n      return e[5];\n    case \"UAN\":\n      return e[6];\n    case \"PAGER\":\n      return e[7];\n    case \"VOIP\":\n      return e[8];\n    case \"SHARED_COST\":\n      return e[9];\n  }\n}\nfunction d0(e) {\n  if (!e)\n    throw new Error(\"[libphonenumber-js] `metadata` argument not passed. Check your arguments.\");\n  if (!H(e) || !H(e.countries))\n    throw new Error(\"[libphonenumber-js] `metadata` argument was passed but it's not a valid metadata. Must be an object having `.countries` child object property. Got \".concat(H(e) ? \"an object of shape: { \" + Object.keys(e).join(\", \") + \" }\" : \"a \" + t0(e) + \": \" + e, \".\"));\n}\nvar t0 = function(d) {\n  return $e(d);\n};\nfunction fe(e, d) {\n  if (d = new v(d), d.hasCountry(e))\n    return d.country(e).countryCallingCode();\n  throw new Error(\"Unknown country: \".concat(e));\n}\nfunction n0(e, d) {\n  return d.countries.hasOwnProperty(e);\n}\nfunction Je(e) {\n  var d = e.version;\n  typeof d == \"number\" ? (this.v1 = d === 1, this.v2 = d === 2, this.v3 = d === 3, this.v4 = d === 4) : d ? Pe(d, Zd) === -1 ? this.v2 = !0 : Pe(d, Yd) === -1 ? this.v3 = !0 : this.v4 = !0 : this.v1 = !0;\n}\nvar r0 = \";ext=\", x = function(d) {\n  return \"([\".concat(O, \"]{1,\").concat(d, \"})\");\n};\nfunction Qe(e) {\n  var d = \"20\", t = \"15\", n = \"9\", r = \"6\", $ = \"[  \\\\t,]*\", u = \"[:\\\\.．]?[  \\\\t,-]*\", o = \"#?\", s = \"(?:e?xt(?:ensi(?:ó?|ó))?n?|ｅ?ｘｔｎ?|доб|anexo)\", i = \"(?:[xｘ#＃~～]|int|ｉｎｔ)\", p = \"[- ]+\", b = \"[  \\\\t]*\", C = \"(?:,{2}|;)\", h = r0 + x(d), y = $ + s + u + x(d) + o, X = $ + i + u + x(n) + o, Z = p + x(r) + \"#\", Y = b + C + u + x(t) + o, F = b + \"(?:,)+\" + u + x(n) + o;\n  return h + \"|\" + y + \"|\" + X + \"|\" + Z + \"|\" + Y + \"|\" + F;\n}\nvar i0 = \"[\" + O + \"]{\" + se + \"}\", a0 = \"[\" + ce + \"]{0,1}(?:[\" + V + \"]*[\" + O + \"]){3,}[\" + V + O + \"]*\", $0 = new RegExp(\"^[\" + ce + \"]{0,1}(?:[\" + V + \"]*[\" + O + \"]){1,2}$\", \"i\"), o0 = a0 + // Phone number extensions\n\"(?:\" + Qe() + \")?\", u0 = new RegExp(\n  // Either a short two-digit-only phone number\n  \"^\" + i0 + \"$|^\" + o0 + \"$\",\n  \"i\"\n);\nfunction l0(e) {\n  return e.length >= se && u0.test(e);\n}\nfunction s0(e) {\n  return $0.test(e);\n}\nvar Se = new RegExp(\"(?:\" + Qe() + \")$\", \"i\");\nfunction c0(e) {\n  var d = e.search(Se);\n  if (d < 0)\n    return {};\n  for (var t = e.slice(0, d), n = e.match(Se), r = 1; r < n.length; ) {\n    if (n[r])\n      return {\n        number: t,\n        ext: n[r]\n      };\n    r++;\n  }\n}\nvar f0 = {\n  0: \"0\",\n  1: \"1\",\n  2: \"2\",\n  3: \"3\",\n  4: \"4\",\n  5: \"5\",\n  6: \"6\",\n  7: \"7\",\n  8: \"8\",\n  9: \"9\",\n  \"０\": \"0\",\n  // Fullwidth digit 0\n  \"１\": \"1\",\n  // Fullwidth digit 1\n  \"２\": \"2\",\n  // Fullwidth digit 2\n  \"３\": \"3\",\n  // Fullwidth digit 3\n  \"４\": \"4\",\n  // Fullwidth digit 4\n  \"５\": \"5\",\n  // Fullwidth digit 5\n  \"６\": \"6\",\n  // Fullwidth digit 6\n  \"７\": \"7\",\n  // Fullwidth digit 7\n  \"８\": \"8\",\n  // Fullwidth digit 8\n  \"９\": \"9\",\n  // Fullwidth digit 9\n  \"٠\": \"0\",\n  // Arabic-indic digit 0\n  \"١\": \"1\",\n  // Arabic-indic digit 1\n  \"٢\": \"2\",\n  // Arabic-indic digit 2\n  \"٣\": \"3\",\n  // Arabic-indic digit 3\n  \"٤\": \"4\",\n  // Arabic-indic digit 4\n  \"٥\": \"5\",\n  // Arabic-indic digit 5\n  \"٦\": \"6\",\n  // Arabic-indic digit 6\n  \"٧\": \"7\",\n  // Arabic-indic digit 7\n  \"٨\": \"8\",\n  // Arabic-indic digit 8\n  \"٩\": \"9\",\n  // Arabic-indic digit 9\n  \"۰\": \"0\",\n  // Eastern-Arabic digit 0\n  \"۱\": \"1\",\n  // Eastern-Arabic digit 1\n  \"۲\": \"2\",\n  // Eastern-Arabic digit 2\n  \"۳\": \"3\",\n  // Eastern-Arabic digit 3\n  \"۴\": \"4\",\n  // Eastern-Arabic digit 4\n  \"۵\": \"5\",\n  // Eastern-Arabic digit 5\n  \"۶\": \"6\",\n  // Eastern-Arabic digit 6\n  \"۷\": \"7\",\n  // Eastern-Arabic digit 7\n  \"۸\": \"8\",\n  // Eastern-Arabic digit 8\n  \"۹\": \"9\"\n  // Eastern-Arabic digit 9\n};\nfunction p0(e) {\n  return f0[e];\n}\nfunction h0(e, d) {\n  var t = typeof Symbol < \"u\" && e[Symbol.iterator] || e[\"@@iterator\"];\n  if (t)\n    return (t = t.call(e)).next.bind(t);\n  if (Array.isArray(e) || (t = y0(e)) || d && e && typeof e.length == \"number\") {\n    t && (e = t);\n    var n = 0;\n    return function() {\n      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };\n    };\n  }\n  throw new TypeError(`Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);\n}\nfunction y0(e, d) {\n  if (e) {\n    if (typeof e == \"string\")\n      return _e(e, d);\n    var t = Object.prototype.toString.call(e).slice(8, -1);\n    if (t === \"Object\" && e.constructor && (t = e.constructor.name), t === \"Map\" || t === \"Set\")\n      return Array.from(e);\n    if (t === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))\n      return _e(e, d);\n  }\n}\nfunction _e(e, d) {\n  (d == null || d > e.length) && (d = e.length);\n  for (var t = 0, n = new Array(d); t < d; t++)\n    n[t] = e[t];\n  return n;\n}\nfunction Fe(e) {\n  for (var d = \"\", t = h0(e.split(\"\")), n; !(n = t()).done; ) {\n    var r = n.value;\n    d += g0(r, d) || \"\";\n  }\n  return d;\n}\nfunction g0(e, d) {\n  return e === \"+\" ? d ? void 0 : \"+\" : p0(e);\n}\nfunction m0(e, d) {\n  var t = typeof Symbol < \"u\" && e[Symbol.iterator] || e[\"@@iterator\"];\n  if (t)\n    return (t = t.call(e)).next.bind(t);\n  if (Array.isArray(e) || (t = v0(e)) || d && e && typeof e.length == \"number\") {\n    t && (e = t);\n    var n = 0;\n    return function() {\n      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };\n    };\n  }\n  throw new TypeError(`Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);\n}\nfunction v0(e, d) {\n  if (e) {\n    if (typeof e == \"string\")\n      return Te(e, d);\n    var t = Object.prototype.toString.call(e).slice(8, -1);\n    if (t === \"Object\" && e.constructor && (t = e.constructor.name), t === \"Map\" || t === \"Set\")\n      return Array.from(e);\n    if (t === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))\n      return Te(e, d);\n  }\n}\nfunction Te(e, d) {\n  (d == null || d > e.length) && (d = e.length);\n  for (var t = 0, n = new Array(d); t < d; t++)\n    n[t] = e[t];\n  return n;\n}\nfunction b0(e, d) {\n  for (var t = e.slice(), n = m0(d), r; !(r = n()).done; ) {\n    var $ = r.value;\n    e.indexOf($) < 0 && t.push($);\n  }\n  return t.sort(function(u, o) {\n    return u - o;\n  });\n}\nfunction pe(e, d) {\n  return ed(e, void 0, d);\n}\nfunction ed(e, d, t) {\n  var n = t.type(d), r = n && n.possibleLengths() || t.possibleLengths();\n  if (!r)\n    return \"IS_POSSIBLE\";\n  if (d === \"FIXED_LINE_OR_MOBILE\") {\n    if (!t.type(\"FIXED_LINE\"))\n      return ed(e, \"MOBILE\", t);\n    var $ = t.type(\"MOBILE\");\n    $ && (r = b0(r, $.possibleLengths()));\n  } else if (d && !n)\n    return \"INVALID_LENGTH\";\n  var u = e.length, o = r[0];\n  return o === u ? \"IS_POSSIBLE\" : o > u ? \"TOO_SHORT\" : r[r.length - 1] < u ? \"TOO_LONG\" : r.indexOf(u, 1) >= 0 ? \"IS_POSSIBLE\" : \"INVALID_LENGTH\";\n}\nfunction C0(e, d, t) {\n  if (d === void 0 && (d = {}), t = new v(t), d.v2) {\n    if (!e.countryCallingCode)\n      throw new Error(\"Invalid phone number object passed\");\n    t.selectNumberingPlan(e.countryCallingCode);\n  } else {\n    if (!e.phone)\n      return !1;\n    if (e.country) {\n      if (!t.hasCountry(e.country))\n        throw new Error(\"Unknown country: \".concat(e.country));\n      t.country(e.country);\n    } else {\n      if (!e.countryCallingCode)\n        throw new Error(\"Invalid phone number object passed\");\n      t.selectNumberingPlan(e.countryCallingCode);\n    }\n  }\n  if (t.possibleLengths())\n    return dd(e.phone || e.nationalNumber, t);\n  if (e.countryCallingCode && t.isNonGeographicCallingCode(e.countryCallingCode))\n    return !0;\n  throw new Error('Missing \"possibleLengths\" in metadata. Perhaps the metadata has been generated before v1.0.18.');\n}\nfunction dd(e, d) {\n  switch (pe(e, d)) {\n    case \"IS_POSSIBLE\":\n      return !0;\n    default:\n      return !1;\n  }\n}\nfunction I(e, d) {\n  return e = e || \"\", new RegExp(\"^(?:\" + d + \")$\").test(e);\n}\nfunction O0(e, d) {\n  var t = typeof Symbol < \"u\" && e[Symbol.iterator] || e[\"@@iterator\"];\n  if (t)\n    return (t = t.call(e)).next.bind(t);\n  if (Array.isArray(e) || (t = P0(e)) || d && e && typeof e.length == \"number\") {\n    t && (e = t);\n    var n = 0;\n    return function() {\n      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };\n    };\n  }\n  throw new TypeError(`Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);\n}\nfunction P0(e, d) {\n  if (e) {\n    if (typeof e == \"string\")\n      return xe(e, d);\n    var t = Object.prototype.toString.call(e).slice(8, -1);\n    if (t === \"Object\" && e.constructor && (t = e.constructor.name), t === \"Map\" || t === \"Set\")\n      return Array.from(e);\n    if (t === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))\n      return xe(e, d);\n  }\n}\nfunction xe(e, d) {\n  (d == null || d > e.length) && (d = e.length);\n  for (var t = 0, n = new Array(d); t < d; t++)\n    n[t] = e[t];\n  return n;\n}\nvar N0 = [\"MOBILE\", \"PREMIUM_RATE\", \"TOLL_FREE\", \"SHARED_COST\", \"VOIP\", \"PERSONAL_NUMBER\", \"PAGER\", \"UAN\", \"VOICEMAIL\"];\nfunction he(e, d, t) {\n  if (d = d || {}, !(!e.country && !e.countryCallingCode)) {\n    t = new v(t), t.selectNumberingPlan(e.country, e.countryCallingCode);\n    var n = d.v2 ? e.nationalNumber : e.phone;\n    if (I(n, t.nationalNumberPattern())) {\n      if (de(n, \"FIXED_LINE\", t))\n        return t.type(\"MOBILE\") && t.type(\"MOBILE\").pattern() === \"\" || !t.type(\"MOBILE\") || de(n, \"MOBILE\", t) ? \"FIXED_LINE_OR_MOBILE\" : \"FIXED_LINE\";\n      for (var r = O0(N0), $; !($ = r()).done; ) {\n        var u = $.value;\n        if (de(n, u, t))\n          return u;\n      }\n    }\n  }\n}\nfunction de(e, d, t) {\n  return d = t.type(d), !d || !d.pattern() || d.possibleLengths() && d.possibleLengths().indexOf(e.length) < 0 ? !1 : I(e, d.pattern());\n}\nfunction w0(e, d, t) {\n  if (d = d || {}, t = new v(t), t.selectNumberingPlan(e.country, e.countryCallingCode), t.hasTypes())\n    return he(e, d, t.metadata) !== void 0;\n  var n = d.v2 ? e.nationalNumber : e.phone;\n  return I(n, t.nationalNumberPattern());\n}\nfunction I0(e, d, t) {\n  var n = new v(t), r = n.getCountryCodesForCallingCode(e);\n  return r ? r.filter(function($) {\n    return E0(d, $, t);\n  }) : [];\n}\nfunction E0(e, d, t) {\n  var n = new v(t);\n  return n.selectNumberingPlan(d), n.numberingPlan.possibleLengths().indexOf(e.length) >= 0;\n}\nfunction S0(e) {\n  return e.replace(new RegExp(\"[\".concat(V, \"]+\"), \"g\"), \" \").trim();\n}\nvar _0 = /(\\$\\d)/;\nfunction F0(e, d, t) {\n  var n = t.useInternationalFormat, r = t.withNationalPrefix;\n  t.carrierCode, t.metadata;\n  var $ = e.replace(new RegExp(d.pattern()), n ? d.internationalFormat() : (\n    // This library doesn't use `domestic_carrier_code_formatting_rule`,\n    // because that one is only used when formatting phone numbers\n    // for dialing from a mobile phone, and this is not a dialing library.\n    // carrierCode && format.domesticCarrierCodeFormattingRule()\n    // \t// First, replace the $CC in the formatting rule with the desired carrier code.\n    // \t// Then, replace the $FG in the formatting rule with the first group\n    // \t// and the carrier code combined in the appropriate way.\n    // \t? format.format().replace(FIRST_GROUP_PATTERN, format.domesticCarrierCodeFormattingRule().replace('$CC', carrierCode))\n    // \t: (\n    // \t\twithNationalPrefix && format.nationalPrefixFormattingRule()\n    // \t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\n    // \t\t\t: format.format()\n    // \t)\n    r && d.nationalPrefixFormattingRule() ? d.format().replace(_0, d.nationalPrefixFormattingRule()) : d.format()\n  ));\n  return n ? S0($) : $;\n}\nvar T0 = /^[\\d]+(?:[~\\u2053\\u223C\\uFF5E][\\d]+)?$/;\nfunction x0(e, d, t) {\n  var n = new v(t);\n  if (n.selectNumberingPlan(e, d), n.defaultIDDPrefix())\n    return n.defaultIDDPrefix();\n  if (T0.test(n.IDDPrefix()))\n    return n.IDDPrefix();\n}\nfunction A0(e) {\n  var d = e.number, t = e.ext;\n  if (!d)\n    return \"\";\n  if (d[0] !== \"+\")\n    throw new Error('\"formatRFC3966()\" expects \"number\" to be in E.164 format.');\n  return \"tel:\".concat(d).concat(t ? \";ext=\" + t : \"\");\n}\nfunction D0(e, d) {\n  var t = typeof Symbol < \"u\" && e[Symbol.iterator] || e[\"@@iterator\"];\n  if (t)\n    return (t = t.call(e)).next.bind(t);\n  if (Array.isArray(e) || (t = R0(e)) || d && e && typeof e.length == \"number\") {\n    t && (e = t);\n    var n = 0;\n    return function() {\n      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };\n    };\n  }\n  throw new TypeError(`Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);\n}\nfunction R0(e, d) {\n  if (e) {\n    if (typeof e == \"string\")\n      return Ae(e, d);\n    var t = Object.prototype.toString.call(e).slice(8, -1);\n    if (t === \"Object\" && e.constructor && (t = e.constructor.name), t === \"Map\" || t === \"Set\")\n      return Array.from(e);\n    if (t === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))\n      return Ae(e, d);\n  }\n}\nfunction Ae(e, d) {\n  (d == null || d > e.length) && (d = e.length);\n  for (var t = 0, n = new Array(d); t < d; t++)\n    n[t] = e[t];\n  return n;\n}\nfunction De(e, d) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    d && (n = n.filter(function(r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, n);\n  }\n  return t;\n}\nfunction Re(e) {\n  for (var d = 1; d < arguments.length; d++) {\n    var t = arguments[d] != null ? arguments[d] : {};\n    d % 2 ? De(Object(t), !0).forEach(function(n) {\n      M0(e, n, t[n]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : De(Object(t)).forEach(function(n) {\n      Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(t, n));\n    });\n  }\n  return e;\n}\nfunction M0(e, d, t) {\n  return d in e ? Object.defineProperty(e, d, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[d] = t, e;\n}\nvar Me = {\n  formatExtension: function(d, t, n) {\n    return \"\".concat(d).concat(n.ext()).concat(t);\n  }\n};\nfunction L0(e, d, t, n) {\n  if (t ? t = Re(Re({}, Me), t) : t = Me, n = new v(n), e.country && e.country !== \"001\") {\n    if (!n.hasCountry(e.country))\n      throw new Error(\"Unknown country: \".concat(e.country));\n    n.country(e.country);\n  } else if (e.countryCallingCode)\n    n.selectNumberingPlan(e.countryCallingCode);\n  else\n    return e.phone || \"\";\n  var r = n.countryCallingCode(), $ = t.v2 ? e.nationalNumber : e.phone, u;\n  switch (d) {\n    case \"NATIONAL\":\n      return $ ? (u = K($, e.carrierCode, \"NATIONAL\", n, t), te(u, e.ext, n, t.formatExtension)) : \"\";\n    case \"INTERNATIONAL\":\n      return $ ? (u = K($, null, \"INTERNATIONAL\", n, t), u = \"+\".concat(r, \" \").concat(u), te(u, e.ext, n, t.formatExtension)) : \"+\".concat(r);\n    case \"E.164\":\n      return \"+\".concat(r).concat($);\n    case \"RFC3966\":\n      return A0({\n        number: \"+\".concat(r).concat($),\n        ext: e.ext\n      });\n    case \"IDD\":\n      if (!t.fromCountry)\n        return;\n      var o = B0($, e.carrierCode, r, t.fromCountry, n);\n      return te(o, e.ext, n, t.formatExtension);\n    default:\n      throw new Error('Unknown \"format\" argument passed to \"formatNumber()\": \"'.concat(d, '\"'));\n  }\n}\nfunction K(e, d, t, n, r) {\n  var $ = k0(n.formats(), e);\n  return $ ? F0(e, $, {\n    useInternationalFormat: t === \"INTERNATIONAL\",\n    withNationalPrefix: !($.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && r && r.nationalPrefix === !1),\n    carrierCode: d,\n    metadata: n\n  }) : e;\n}\nfunction k0(e, d) {\n  for (var t = D0(e), n; !(n = t()).done; ) {\n    var r = n.value;\n    if (r.leadingDigitsPatterns().length > 0) {\n      var $ = r.leadingDigitsPatterns()[r.leadingDigitsPatterns().length - 1];\n      if (d.search($) !== 0)\n        continue;\n    }\n    if (I(d, r.pattern()))\n      return r;\n  }\n}\nfunction te(e, d, t, n) {\n  return d ? n(e, d, t) : e;\n}\nfunction B0(e, d, t, n, r) {\n  var $ = fe(n, r.metadata);\n  if ($ === t) {\n    var u = K(e, d, \"NATIONAL\", r);\n    return t === \"1\" ? t + \" \" + u : u;\n  }\n  var o = x0(n, void 0, r.metadata);\n  if (o)\n    return \"\".concat(o, \" \").concat(t, \" \").concat(K(e, null, \"INTERNATIONAL\", r));\n}\nfunction Le(e, d) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    d && (n = n.filter(function(r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, n);\n  }\n  return t;\n}\nfunction ke(e) {\n  for (var d = 1; d < arguments.length; d++) {\n    var t = arguments[d] != null ? arguments[d] : {};\n    d % 2 ? Le(Object(t), !0).forEach(function(n) {\n      G0(e, n, t[n]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : Le(Object(t)).forEach(function(n) {\n      Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(t, n));\n    });\n  }\n  return e;\n}\nfunction G0(e, d, t) {\n  return d in e ? Object.defineProperty(e, d, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[d] = t, e;\n}\nfunction j0(e, d) {\n  if (!(e instanceof d))\n    throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction Be(e, d) {\n  for (var t = 0; t < d.length; t++) {\n    var n = d[t];\n    n.enumerable = n.enumerable || !1, n.configurable = !0, \"value\" in n && (n.writable = !0), Object.defineProperty(e, n.key, n);\n  }\n}\nfunction U0(e, d, t) {\n  return d && Be(e.prototype, d), t && Be(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e;\n}\nvar H0 = /* @__PURE__ */ function() {\n  function e(d, t, n) {\n    if (j0(this, e), !d)\n      throw new TypeError(\"`country` or `countryCallingCode` not passed\");\n    if (!t)\n      throw new TypeError(\"`nationalNumber` not passed\");\n    if (!n)\n      throw new TypeError(\"`metadata` not passed\");\n    var r = K0(d, n), $ = r.country, u = r.countryCallingCode;\n    this.country = $, this.countryCallingCode = u, this.nationalNumber = t, this.number = \"+\" + this.countryCallingCode + this.nationalNumber, this.getMetadata = function() {\n      return n;\n    };\n  }\n  return U0(e, [{\n    key: \"setExt\",\n    value: function(t) {\n      this.ext = t;\n    }\n  }, {\n    key: \"getPossibleCountries\",\n    value: function() {\n      return this.country ? [this.country] : I0(this.countryCallingCode, this.nationalNumber, this.getMetadata());\n    }\n  }, {\n    key: \"isPossible\",\n    value: function() {\n      return C0(this, {\n        v2: !0\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"isValid\",\n    value: function() {\n      return w0(this, {\n        v2: !0\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"isNonGeographic\",\n    value: function() {\n      var t = new v(this.getMetadata());\n      return t.isNonGeographicCallingCode(this.countryCallingCode);\n    }\n  }, {\n    key: \"isEqual\",\n    value: function(t) {\n      return this.number === t.number && this.ext === t.ext;\n    }\n    // This function was originally meant to be an equivalent for `validatePhoneNumberLength()`,\n    // but later it was found out that it doesn't include the possible `TOO_SHORT` result\n    // returned from `parsePhoneNumberWithError()` in the original `validatePhoneNumberLength()`,\n    // so eventually I simply commented out this method from the `PhoneNumber` class\n    // and just left the `validatePhoneNumberLength()` function, even though that one would require\n    // and additional step to also validate the actual country / calling code of the phone number.\n    // validateLength() {\n    // \tconst metadata = new Metadata(this.getMetadata())\n    // \tmetadata.selectNumberingPlan(this.countryCallingCode)\n    // \tconst result = checkNumberLength(this.nationalNumber, metadata)\n    // \tif (result !== 'IS_POSSIBLE') {\n    // \t\treturn result\n    // \t}\n    // }\n  }, {\n    key: \"getType\",\n    value: function() {\n      return he(this, {\n        v2: !0\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"format\",\n    value: function(t, n) {\n      return L0(this, t, n ? ke(ke({}, n), {}, {\n        v2: !0\n      }) : {\n        v2: !0\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"formatNational\",\n    value: function(t) {\n      return this.format(\"NATIONAL\", t);\n    }\n  }, {\n    key: \"formatInternational\",\n    value: function(t) {\n      return this.format(\"INTERNATIONAL\", t);\n    }\n  }, {\n    key: \"getURI\",\n    value: function(t) {\n      return this.format(\"RFC3966\", t);\n    }\n  }]), e;\n}(), V0 = function(d) {\n  return /^[A-Z]{2}$/.test(d);\n};\nfunction K0(e, d) {\n  var t, n, r = new v(d);\n  return V0(e) ? (t = e, r.selectNumberingPlan(t), n = r.countryCallingCode()) : n = e, {\n    country: t,\n    countryCallingCode: n\n  };\n}\nvar W0 = new RegExp(\"([\" + O + \"])\");\nfunction z0(e, d, t, n) {\n  if (d) {\n    var r = new v(n);\n    r.selectNumberingPlan(d, t);\n    var $ = new RegExp(r.IDDPrefix());\n    if (e.search($) === 0) {\n      e = e.slice(e.match($)[0].length);\n      var u = e.match(W0);\n      if (!(u && u[1] != null && u[1].length > 0 && u[1] === \"0\"))\n        return e;\n    }\n  }\n}\nfunction X0(e, d) {\n  if (e && d.numberingPlan.nationalPrefixForParsing()) {\n    var t = new RegExp(\"^(?:\" + d.numberingPlan.nationalPrefixForParsing() + \")\"), n = t.exec(e);\n    if (n) {\n      var r, $, u = n.length - 1, o = u > 0 && n[u];\n      if (d.nationalPrefixTransformRule() && o)\n        r = e.replace(t, d.nationalPrefixTransformRule()), u > 1 && ($ = n[1]);\n      else {\n        var s = n[0];\n        r = e.slice(s.length), o && ($ = n[1]);\n      }\n      var i;\n      if (o) {\n        var p = e.indexOf(n[1]), b = e.slice(0, p);\n        b === d.numberingPlan.nationalPrefix() && (i = d.numberingPlan.nationalPrefix());\n      } else\n        i = n[0];\n      return {\n        nationalNumber: r,\n        nationalPrefix: i,\n        carrierCode: $\n      };\n    }\n  }\n  return {\n    nationalNumber: e\n  };\n}\nfunction oe(e, d) {\n  var t = X0(e, d), n = t.carrierCode, r = t.nationalNumber;\n  if (r !== e) {\n    if (!Z0(e, r, d))\n      return {\n        nationalNumber: e\n      };\n    if (d.possibleLengths() && !Y0(r, d))\n      return {\n        nationalNumber: e\n      };\n  }\n  return {\n    nationalNumber: r,\n    carrierCode: n\n  };\n}\nfunction Z0(e, d, t) {\n  return !(I(e, t.nationalNumberPattern()) && !I(d, t.nationalNumberPattern()));\n}\nfunction Y0(e, d) {\n  switch (pe(e, d)) {\n    case \"TOO_SHORT\":\n    case \"INVALID_LENGTH\":\n      return !1;\n    default:\n      return !0;\n  }\n}\nfunction q0(e, d, t, n) {\n  var r = d ? fe(d, n) : t;\n  if (e.indexOf(r) === 0) {\n    n = new v(n), n.selectNumberingPlan(d, t);\n    var $ = e.slice(r.length), u = oe($, n), o = u.nationalNumber, s = oe(e, n), i = s.nationalNumber;\n    if (!I(i, n.nationalNumberPattern()) && I(o, n.nationalNumberPattern()) || pe(i, n) === \"TOO_LONG\")\n      return {\n        countryCallingCode: r,\n        number: $\n      };\n  }\n  return {\n    number: e\n  };\n}\nfunction J0(e, d, t, n) {\n  if (!e)\n    return {};\n  var r;\n  if (e[0] !== \"+\") {\n    var $ = z0(e, d, t, n);\n    if ($ && $ !== e)\n      r = !0, e = \"+\" + $;\n    else {\n      if (d || t) {\n        var u = q0(e, d, t, n), o = u.countryCallingCode, s = u.number;\n        if (o)\n          return {\n            countryCallingCodeSource: \"FROM_NUMBER_WITHOUT_PLUS_SIGN\",\n            countryCallingCode: o,\n            number: s\n          };\n      }\n      return {\n        // No need to set it to `UNSPECIFIED`. It can be just `undefined`.\n        // countryCallingCodeSource: 'UNSPECIFIED',\n        number: e\n      };\n    }\n  }\n  if (e[1] === \"0\")\n    return {};\n  n = new v(n);\n  for (var i = 2; i - 1 <= jd && i <= e.length; ) {\n    var p = e.slice(1, i);\n    if (n.hasCallingCode(p))\n      return n.selectNumberingPlan(p), {\n        countryCallingCodeSource: r ? \"FROM_NUMBER_WITH_IDD\" : \"FROM_NUMBER_WITH_PLUS_SIGN\",\n        countryCallingCode: p,\n        number: e.slice(i)\n      };\n    i++;\n  }\n  return {};\n}\nfunction Q0(e, d) {\n  var t = typeof Symbol < \"u\" && e[Symbol.iterator] || e[\"@@iterator\"];\n  if (t)\n    return (t = t.call(e)).next.bind(t);\n  if (Array.isArray(e) || (t = et(e)) || d && e && typeof e.length == \"number\") {\n    t && (e = t);\n    var n = 0;\n    return function() {\n      return n >= e.length ? { done: !0 } : { done: !1, value: e[n++] };\n    };\n  }\n  throw new TypeError(`Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);\n}\nfunction et(e, d) {\n  if (e) {\n    if (typeof e == \"string\")\n      return Ge(e, d);\n    var t = Object.prototype.toString.call(e).slice(8, -1);\n    if (t === \"Object\" && e.constructor && (t = e.constructor.name), t === \"Map\" || t === \"Set\")\n      return Array.from(e);\n    if (t === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))\n      return Ge(e, d);\n  }\n}\nfunction Ge(e, d) {\n  (d == null || d > e.length) && (d = e.length);\n  for (var t = 0, n = new Array(d); t < d; t++)\n    n[t] = e[t];\n  return n;\n}\nfunction dt(e, d) {\n  var t = d.countries, n = d.defaultCountry, r = d.metadata;\n  r = new v(r);\n  for (var $ = [], u = Q0(t), o; !(o = u()).done; ) {\n    var s = o.value;\n    if (r.country(s), r.leadingDigits()) {\n      if (e && e.search(r.leadingDigits()) === 0)\n        return s;\n    } else if (he({\n      phone: e,\n      country: s\n    }, void 0, r.metadata))\n      if (n) {\n        if (s === n)\n          return s;\n        $.push(s);\n      } else\n        return s;\n  }\n  if ($.length > 0)\n    return $[0];\n}\nvar tt = !1;\nfunction nt(e, d) {\n  var t = d.nationalNumber, n = d.defaultCountry, r = d.metadata;\n  if (tt && r.isNonGeographicCallingCode(e))\n    return \"001\";\n  var $ = r.getCountryCodesForCallingCode(e);\n  if ($)\n    return $.length === 1 ? $[0] : dt(t, {\n      countries: $,\n      defaultCountry: n,\n      metadata: r.metadata\n    });\n}\nvar td = \"+\", rt = \"[\\\\-\\\\.\\\\(\\\\)]?\", je = \"([\" + O + \"]|\" + rt + \")\", it = \"^\\\\\" + td + je + \"*[\" + O + \"]\" + je + \"*$\", at = new RegExp(it, \"g\"), ue = O, $t = \"[\" + ue + \"]+((\\\\-)*[\" + ue + \"])*\", ot = \"a-zA-Z\", ut = \"[\" + ot + \"]+((\\\\-)*[\" + ue + \"])*\", lt = \"^(\" + $t + \"\\\\.)*\" + ut + \"\\\\.?$\", st = new RegExp(lt, \"g\"), Ue = \"tel:\", le = \";phone-context=\", ct = \";isub=\";\nfunction ft(e) {\n  var d = e.indexOf(le);\n  if (d < 0)\n    return null;\n  var t = d + le.length;\n  if (t >= e.length)\n    return \"\";\n  var n = e.indexOf(\";\", t);\n  return n >= 0 ? e.substring(t, n) : e.substring(t);\n}\nfunction pt(e) {\n  return e === null ? !0 : e.length === 0 ? !1 : at.test(e) || st.test(e);\n}\nfunction ht(e, d) {\n  var t = d.extractFormattedPhoneNumber, n = ft(e);\n  if (!pt(n))\n    throw new w(\"NOT_A_NUMBER\");\n  var r;\n  if (n === null)\n    r = t(e) || \"\";\n  else {\n    r = \"\", n.charAt(0) === td && (r += n);\n    var $ = e.indexOf(Ue), u;\n    $ >= 0 ? u = $ + Ue.length : u = 0;\n    var o = e.indexOf(le);\n    r += e.substring(u, o);\n  }\n  var s = r.indexOf(ct);\n  if (s > 0 && (r = r.substring(0, s)), r !== \"\")\n    return r;\n}\nvar yt = 250, gt = new RegExp(\"[\" + ce + O + \"]\"), mt = new RegExp(\"[^\" + O + \"#]+$\");\nfunction vt(e, d, t) {\n  if (d = d || {}, t = new v(t), d.defaultCountry && !t.hasCountry(d.defaultCountry))\n    throw d.v2 ? new w(\"INVALID_COUNTRY\") : new Error(\"Unknown country: \".concat(d.defaultCountry));\n  var n = Ct(e, d.v2, d.extract), r = n.number, $ = n.ext, u = n.error;\n  if (!r) {\n    if (d.v2)\n      throw u === \"TOO_SHORT\" ? new w(\"TOO_SHORT\") : new w(\"NOT_A_NUMBER\");\n    return {};\n  }\n  var o = Pt(r, d.defaultCountry, d.defaultCallingCode, t), s = o.country, i = o.nationalNumber, p = o.countryCallingCode, b = o.countryCallingCodeSource, C = o.carrierCode;\n  if (!t.hasSelectedNumberingPlan()) {\n    if (d.v2)\n      throw new w(\"INVALID_COUNTRY\");\n    return {};\n  }\n  if (!i || i.length < se) {\n    if (d.v2)\n      throw new w(\"TOO_SHORT\");\n    return {};\n  }\n  if (i.length > Gd) {\n    if (d.v2)\n      throw new w(\"TOO_LONG\");\n    return {};\n  }\n  if (d.v2) {\n    var h = new H0(p, i, t.metadata);\n    return s && (h.country = s), C && (h.carrierCode = C), $ && (h.ext = $), h.__countryCallingCodeSource = b, h;\n  }\n  var y = (d.extended ? t.hasSelectedNumberingPlan() : s) ? I(i, t.nationalNumberPattern()) : !1;\n  return d.extended ? {\n    country: s,\n    countryCallingCode: p,\n    carrierCode: C,\n    valid: y,\n    possible: y ? !0 : !!(d.extended === !0 && t.possibleLengths() && dd(i, t)),\n    phone: i,\n    ext: $\n  } : y ? Ot(s, i, $) : {};\n}\nfunction bt(e, d, t) {\n  if (e) {\n    if (e.length > yt) {\n      if (t)\n        throw new w(\"TOO_LONG\");\n      return;\n    }\n    if (d === !1)\n      return e;\n    var n = e.search(gt);\n    if (!(n < 0))\n      return e.slice(n).replace(mt, \"\");\n  }\n}\nfunction Ct(e, d, t) {\n  var n = ht(e, {\n    extractFormattedPhoneNumber: function(u) {\n      return bt(u, t, d);\n    }\n  });\n  if (!n)\n    return {};\n  if (!l0(n))\n    return s0(n) ? {\n      error: \"TOO_SHORT\"\n    } : {};\n  var r = c0(n);\n  return r.ext ? r : {\n    number: n\n  };\n}\nfunction Ot(e, d, t) {\n  var n = {\n    country: e,\n    phone: d\n  };\n  return t && (n.ext = t), n;\n}\nfunction Pt(e, d, t, n) {\n  var r = J0(Fe(e), d, t, n.metadata), $ = r.countryCallingCodeSource, u = r.countryCallingCode, o = r.number, s;\n  if (u)\n    n.selectNumberingPlan(u);\n  else if (o && (d || t))\n    n.selectNumberingPlan(d, t), d && (s = d), u = t || fe(d, n.metadata);\n  else\n    return {};\n  if (!o)\n    return {\n      countryCallingCodeSource: $,\n      countryCallingCode: u\n    };\n  var i = oe(Fe(o), n), p = i.nationalNumber, b = i.carrierCode, C = nt(u, {\n    nationalNumber: p,\n    defaultCountry: d,\n    metadata: n\n  });\n  return C && (s = C, C === \"001\" || n.country(s)), {\n    country: s,\n    countryCallingCode: u,\n    countryCallingCodeSource: $,\n    nationalNumber: p,\n    carrierCode: b\n  };\n}\nfunction He(e, d) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    d && (n = n.filter(function(r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, n);\n  }\n  return t;\n}\nfunction Ve(e) {\n  for (var d = 1; d < arguments.length; d++) {\n    var t = arguments[d] != null ? arguments[d] : {};\n    d % 2 ? He(Object(t), !0).forEach(function(n) {\n      Nt(e, n, t[n]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : He(Object(t)).forEach(function(n) {\n      Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(t, n));\n    });\n  }\n  return e;\n}\nfunction Nt(e, d, t) {\n  return d in e ? Object.defineProperty(e, d, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[d] = t, e;\n}\nfunction wt(e, d, t) {\n  return vt(e, Ve(Ve({}, d), {}, {\n    v2: !0\n  }), t);\n}\nfunction Ke(e, d) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    d && (n = n.filter(function(r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, n);\n  }\n  return t;\n}\nfunction It(e) {\n  for (var d = 1; d < arguments.length; d++) {\n    var t = arguments[d] != null ? arguments[d] : {};\n    d % 2 ? Ke(Object(t), !0).forEach(function(n) {\n      Et(e, n, t[n]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : Ke(Object(t)).forEach(function(n) {\n      Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(t, n));\n    });\n  }\n  return e;\n}\nfunction Et(e, d, t) {\n  return d in e ? Object.defineProperty(e, d, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[d] = t, e;\n}\nfunction St(e, d) {\n  return xt(e) || Tt(e, d) || Ft(e, d) || _t();\n}\nfunction _t() {\n  throw new TypeError(`Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);\n}\nfunction Ft(e, d) {\n  if (e) {\n    if (typeof e == \"string\")\n      return We(e, d);\n    var t = Object.prototype.toString.call(e).slice(8, -1);\n    if (t === \"Object\" && e.constructor && (t = e.constructor.name), t === \"Map\" || t === \"Set\")\n      return Array.from(e);\n    if (t === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))\n      return We(e, d);\n  }\n}\nfunction We(e, d) {\n  (d == null || d > e.length) && (d = e.length);\n  for (var t = 0, n = new Array(d); t < d; t++)\n    n[t] = e[t];\n  return n;\n}\nfunction Tt(e, d) {\n  var t = e == null ? null : typeof Symbol < \"u\" && e[Symbol.iterator] || e[\"@@iterator\"];\n  if (t != null) {\n    var n = [], r = !0, $ = !1, u, o;\n    try {\n      for (t = t.call(e); !(r = (u = t.next()).done) && (n.push(u.value), !(d && n.length === d)); r = !0)\n        ;\n    } catch (s) {\n      $ = !0, o = s;\n    } finally {\n      try {\n        !r && t.return != null && t.return();\n      } finally {\n        if ($)\n          throw o;\n      }\n    }\n    return n;\n  }\n}\nfunction xt(e) {\n  if (Array.isArray(e))\n    return e;\n}\nfunction At(e) {\n  var d = Array.prototype.slice.call(e), t = St(d, 4), n = t[0], r = t[1], $ = t[2], u = t[3], o, s, i;\n  if (typeof n == \"string\")\n    o = n;\n  else\n    throw new TypeError(\"A text for parsing must be a string.\");\n  if (!r || typeof r == \"string\")\n    u ? (s = $, i = u) : (s = void 0, i = $), r && (s = It({\n      defaultCountry: r\n    }, s));\n  else if (H(r))\n    $ ? (s = r, i = $) : i = r;\n  else\n    throw new Error(\"Invalid second argument: \".concat(r));\n  return {\n    text: o,\n    options: s,\n    metadata: i\n  };\n}\nfunction ze(e, d) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    d && (n = n.filter(function(r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, n);\n  }\n  return t;\n}\nfunction Xe(e) {\n  for (var d = 1; d < arguments.length; d++) {\n    var t = arguments[d] != null ? arguments[d] : {};\n    d % 2 ? ze(Object(t), !0).forEach(function(n) {\n      Dt(e, n, t[n]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ze(Object(t)).forEach(function(n) {\n      Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(t, n));\n    });\n  }\n  return e;\n}\nfunction Dt(e, d, t) {\n  return d in e ? Object.defineProperty(e, d, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[d] = t, e;\n}\nfunction Rt(e, d, t) {\n  d && d.defaultCountry && !n0(d.defaultCountry, t) && (d = Xe(Xe({}, d), {}, {\n    defaultCountry: void 0\n  }));\n  try {\n    return wt(e, d, t);\n  } catch (n) {\n    if (!(n instanceof w))\n      throw n;\n  }\n}\nfunction Mt() {\n  var e = At(arguments), d = e.text, t = e.options, n = e.metadata;\n  return Rt(d, t, n);\n}\nfunction ne() {\n  return Ad(Mt, arguments);\n}\nconst Lt = {\n  beforeMount(e, d, t) {\n    if (typeof d.value != \"function\") {\n      const n = t.context.name;\n      let r = `[Vue-click-outside:] provided expression ${d.expression} is not a function, but has to be`;\n      n && (r += `Found in component ${n}`), console.warn(r);\n    }\n    e.clickOutsideEvent = function(n) {\n      const r = n.composedPath ? n.composedPath() : n.path;\n      e === n.target || e.contains(n.target) || r.includes(e) || d.value(n, e);\n    }, document.body.addEventListener(\"click\", e.clickOutsideEvent);\n  },\n  unmounted(e) {\n    document.body.removeEventListener(\"click\", e.clickOutsideEvent);\n  }\n}, kt = [\"aria-expanded\", \"tabindex\"], Bt = { class: \"vti__selection\" }, Gt = {\n  key: 1,\n  class: \"vti__country-code\"\n}, jt = { class: \"vti__dropdown-arrow\" }, Ut = {\n  key: 0,\n  class: \"vti__search_box_container\"\n}, Ht = [\"placeholder\"], Vt = [\"onClick\", \"onMousemove\", \"aria-selected\"], Kt = { key: 1 }, Wt = [\"type\", \"autocomplete\", \"autofocus\", \"disabled\", \"id\", \"maxlength\", \"name\", \"placeholder\", \"readonly\", \"required\", \"tabindex\", \"value\", \"aria-describedby\"], zt = /* @__PURE__ */ gd({\n  name: \"VueTelInput\",\n  directives: {\n    clickOutside: Lt\n  },\n  __name: \"vue-tel-input\",\n  props: /* @__PURE__ */ ve({\n    allCountries: {\n      type: Array,\n      default: () => m(\"allCountries\")\n    },\n    autoFormat: {\n      type: Boolean,\n      default: () => m(\"autoFormat\")\n    },\n    customValidate: {\n      type: [Boolean, RegExp],\n      default: () => m(\"customValidate\")\n    },\n    defaultCountry: {\n      // Default country code, ie: 'AU'\n      // Will override the current country of user\n      type: [String, Number],\n      default: () => m(\"defaultCountry\")\n    },\n    disabled: {\n      type: Boolean,\n      default: () => m(\"disabled\")\n    },\n    autoDefaultCountry: {\n      type: Boolean,\n      default: () => m(\"autoDefaultCountry\")\n    },\n    dropdownOptions: {\n      type: Object,\n      default: () => m(\"dropdownOptions\")\n    },\n    ignoredCountries: {\n      type: Array,\n      default: () => m(\"ignoredCountries\")\n    },\n    inputOptions: {\n      type: Object,\n      default: () => m(\"inputOptions\")\n    },\n    invalidMsg: {\n      type: String,\n      default: () => m(\"invalidMsg\")\n    },\n    mode: {\n      type: String,\n      default: () => m(\"mode\")\n    },\n    onlyCountries: {\n      type: Array,\n      default: () => m(\"onlyCountries\")\n    },\n    preferredCountries: {\n      type: Array,\n      default: () => m(\"preferredCountries\")\n    },\n    validCharactersOnly: {\n      type: Boolean,\n      default: () => m(\"validCharactersOnly\")\n    },\n    styleClasses: {\n      type: [String, Array, Object],\n      default: () => m(\"styleClasses\")\n    }\n  }, {\n    modelValue: { type: String },\n    modelModifiers: {}\n  }),\n  emits: /* @__PURE__ */ ve([\n    \"blur\",\n    \"close\",\n    \"country-changed\",\n    \"enter\",\n    \"focus\",\n    \"on-input\",\n    \"open\",\n    \"space\",\n    \"validate\"\n  ], [\"update:modelValue\"]),\n  setup(e, { expose: d, emit: t }) {\n    const n = J(), r = J(), $ = J(), u = t, o = e, s = md(e, \"modelValue\");\n    _(s, (a, l) => {\n      ad() ? i.phone = a ?? \"\" : be(() => {\n        i.phone = l ?? \"\", ye();\n      });\n    });\n    const i = vd({\n      phone: \"\",\n      activeCountryCode: void 0,\n      open: !1,\n      finishMounted: !1,\n      selectedIndex: null,\n      typeToFindInput: \"\",\n      typeToFindTimer: void 0,\n      dropdownOpenDirection: \"below\",\n      parsedPlaceholder: o.inputOptions.placeholder,\n      searchQuery: \"\"\n    });\n    _(() => i.open, (a) => {\n      a ? (yd(), u(\"open\")) : u(\"close\");\n    });\n    const p = A(() => o.onlyCountries.length ? o.allCountries.filter(({ iso2: a }) => o.onlyCountries.some((l) => L(l) === a)) : o.ignoredCountries.length ? o.allCountries.filter(\n      ({ iso2: a }) => !o.ignoredCountries.includes(L(a)) && !o.ignoredCountries.includes(M(a))\n    ) : o.allCountries), b = A(() => F(i.activeCountryCode));\n    _(b, (a, l) => {\n      if (!a && (l != null && l.iso2)) {\n        i.activeCountryCode = l.iso2;\n        return;\n      }\n      a != null && a.iso2 && u(\"country-changed\", a);\n    });\n    const C = A(() => {\n      var l;\n      const a = M(o.mode);\n      return a === \"auto\" ? (l = i.phone) != null && l.startsWith(\"+\") ? \"international\" : \"national\" : [\"national\", \"international\", \"e.164\", \"rfc3966\", \"idd\"].includes(a) ? a : (console.error('Invalid value of prop \"mode\"'), \"international\");\n    }), h = A(() => {\n      const l = [...Y(o.preferredCountries).map((g) => ({ ...g, preferred: !0 })), ...p.value];\n      if (!o.dropdownOptions.showSearchBox)\n        return l;\n      const c = i.searchQuery.toLowerCase().replace(/[~`!@#$%^&*()+={}\\[\\];:\\'\\\"<>.,\\/\\\\\\?-_]/g, \"\");\n      return l.filter(\n        (g) => new RegExp(c, \"i\").test(g.name) || new RegExp(c, \"i\").test(g.iso2) || new RegExp(c, \"i\").test(g.dialCode)\n      );\n    }), y = A(() => {\n      var f;\n      const a = i.phone.startsWith(\"+\") ? ne(i.phone) : ne(i.phone, i.activeCountryCode), l = {\n        country: a == null ? void 0 : a.country,\n        countryCode: a == null ? void 0 : a.country,\n        formatted: i.phone,\n        valid: a == null ? void 0 : a.isValid(),\n        possible: (f = a == null ? void 0 : a.isPossible) == null ? void 0 : f.call(a),\n        nationalNumber: a == null ? void 0 : a.nationalNumber\n      };\n      return l.valid && (l.formatted = a == null ? void 0 : a.format(L(C.value))), a != null && a.country && (o.ignoredCountries.length || o.onlyCountries.length) && !F(a.country) && (l.valid = !1, l.possible = !1, a.country = null), a ? {\n        ...l,\n        ...a\n      } : l;\n    });\n    _(() => y.value.countryCode, (a) => {\n      a && (i.activeCountryCode = a);\n    }), _(() => y.value.valid, () => {\n      u(\"validate\", y.value);\n    }), _(() => y.value.formatted, (a) => {\n      !o.autoFormat || o.customValidate || (G(a), be(() => {\n        a && !s.value && (i.phone = a);\n      }));\n    }), _(() => o.inputOptions.placeholder, X), bd(() => {\n      s.value && (i.phone = s.value.trim()), id(), Z().then(() => {\n        var a;\n        !i.phone && ((a = o.inputOptions) != null && a.showDialCode) && i.activeCountryCode && (i.phone = `+${i.activeCountryCode}`), u(\"validate\", y.value);\n      }).catch(console.error).then(() => {\n        i.finishMounted = !0;\n      });\n    });\n    function X() {\n      i.parsedPlaceholder = o.inputOptions.placeholder;\n    }\n    function Z() {\n      return new Promise((a) => {\n        var f;\n        if (((f = i.phone) == null ? void 0 : f[0]) === \"+\") {\n          a();\n          return;\n        }\n        if (o.defaultCountry) {\n          if (typeof o.defaultCountry == \"string\") {\n            S(o.defaultCountry), a();\n            return;\n          }\n          if (typeof o.defaultCountry == \"number\") {\n            const c = nd(o.defaultCountry);\n            if (c) {\n              S(c.iso2), a();\n              return;\n            }\n          }\n        }\n        const l = o.preferredCountries[0] || p.value[0];\n        o.autoDefaultCountry ? _d().then((c) => {\n          S(c || i.activeCountryCode);\n        }).catch((c) => {\n          console.warn(c), S(l);\n        }).then(() => {\n          a();\n        }) : (S(l), a());\n      });\n    }\n    function Y(a = []) {\n      return a.map(F).filter(Boolean);\n    }\n    function F(a = \"\") {\n      return p.value.find((l) => l.iso2 === L(a));\n    }\n    function nd(a) {\n      return p.value.find((l) => Number(l.dialCode) === a);\n    }\n    function rd(a, l) {\n      const f = i.selectedIndex === a, c = a === o.preferredCountries.length - 1, g = o.preferredCountries.some((q) => L(q) === l);\n      return {\n        highlighted: f,\n        \"last-preferred\": c,\n        preferred: g\n      };\n    }\n    function S(a) {\n      var f, c, g;\n      let l = a;\n      if (typeof l == \"string\" && (l = F(l)), !!l) {\n        if (((f = i.phone) == null ? void 0 : f[0]) === \"+\" && l.iso2 && y.value.nationalNumber) {\n          i.activeCountryCode = l.iso2, i.phone = ((c = ne(\n            y.value.nationalNumber,\n            l.iso2\n          )) == null ? void 0 : c.formatInternational()) ?? \"\";\n          return;\n        }\n        if ((g = o.inputOptions) != null && g.showDialCode && l) {\n          i.phone = `+${l.dialCode}`, i.activeCountryCode = l.iso2;\n          return;\n        }\n        i.activeCountryCode = l.iso2, G(i.phone);\n      }\n    }\n    function id() {\n      const a = i.phone;\n      if (o.validCharactersOnly) {\n        const l = i.phone.match(/[()\\-+0-9\\s]*/g);\n        i.phone = l.join(\"\");\n      }\n      if (o.customValidate && o.customValidate instanceof RegExp) {\n        const l = i.phone.match(o.customValidate);\n        i.phone = l.join(\"\");\n      }\n      a !== i.phone && G(i.phone);\n    }\n    function ad() {\n      return o.validCharactersOnly && !/^[()\\-+0-9\\s]*$/.test(i.phone) ? !1 : o.customValidate ? $d() : !0;\n    }\n    function $d() {\n      return o.customValidate instanceof RegExp ? o.customValidate.test(i.phone) : !1;\n    }\n    function ye() {\n      var a;\n      (a = $.value) == null || a.setCustomValidity(y.value.valid ? \"\" : o.invalidMsg), G(i.phone);\n    }\n    function G(a) {\n      s.value = a, u(\"on-input\", a, y.value, $.value);\n    }\n    function od(a) {\n      u(\"blur\", a);\n    }\n    function ud(a) {\n      Fd($.value, i.phone.length), u(\"focus\", a);\n    }\n    function ld(a) {\n      u(\"enter\", a);\n    }\n    function sd(a) {\n      u(\"space\", a);\n    }\n    function cd() {\n      var a;\n      (a = $.value) == null || a.focus();\n    }\n    function fd() {\n      var a;\n      (a = $.value) == null || a.blur();\n    }\n    function ge() {\n      o.disabled || o.dropdownOptions.disabled || (i.searchQuery = \"\", i.open = !i.open);\n    }\n    function pd() {\n      i.open = !1;\n    }\n    function hd(a) {\n      if (a.keyCode === 40) {\n        a.preventDefault(), i.open = !0, i.selectedIndex === null ? i.selectedIndex = 0 : i.selectedIndex = Math.min(h.value.length - 1, i.selectedIndex + 1);\n        const l = r.value.children[i.selectedIndex];\n        l.focus(), l.offsetTop + l.clientHeight > r.value.scrollTop + r.value.clientHeight && (r.value.scrollTop = l.offsetTop - r.value.clientHeight + l.clientHeight);\n      } else if (a.keyCode === 38) {\n        a.preventDefault(), i.open = !0, i.selectedIndex === null ? i.selectedIndex = h.value.length - 1 : i.selectedIndex = Math.max(0, i.selectedIndex - 1);\n        const l = r.value.children[i.selectedIndex];\n        l.focus(), l.offsetTop < r.value.scrollTop && (r.value.scrollTop = l.offsetTop);\n      } else if (a.keyCode === 13)\n        i.selectedIndex !== null && S(h.value[i.selectedIndex]), i.open = !i.open;\n      else if (i.open) {\n        i.typeToFindInput += a.key, clearTimeout(i.typeToFindTimer), i.typeToFindTimer = setTimeout(() => {\n          i.typeToFindInput = \"\";\n        }, 700);\n        const l = h.value.slice(o.preferredCountries.length).findIndex((f) => M(f.name).startsWith(i.typeToFindInput));\n        if (l >= 0) {\n          i.selectedIndex = o.preferredCountries.length + l;\n          const f = r.value.children[i.selectedIndex], c = f.offsetTop < r.value.scrollTop, g = f.offsetTop + f.clientHeight > r.value.scrollTop + r.value.clientHeight;\n          (c || g) && (r.value.scrollTop = f.offsetTop - r.value.clientHeight / 2);\n        }\n      }\n    }\n    function me() {\n      i.selectedIndex = h.value.map((a) => a.iso2).indexOf(i.activeCountryCode), i.open = !1;\n    }\n    function yd() {\n      window.innerHeight - n.value.getBoundingClientRect().bottom > 200 ? i.dropdownOpenDirection = \"below\" : i.dropdownOpenDirection = \"above\";\n    }\n    return d({\n      focus: cd,\n      blur: fd\n    }), (a, l) => {\n      const f = Cd(\"click-outside\");\n      return P(), N(\"div\", {\n        ref_key: \"refRoot\",\n        ref: n,\n        class: E([\"vue-tel-input\", e.styleClasses, { disabled: e.disabled }])\n      }, [\n        Q((P(), N(\"div\", {\n          \"aria-label\": \"Country Code Selector\",\n          \"aria-haspopup\": \"listbox\",\n          \"aria-expanded\": i.open,\n          role: \"button\",\n          class: E([\"vti__dropdown\", { open: i.open, disabled: e.dropdownOptions.disabled }]),\n          tabindex: e.dropdownOptions.tabindex,\n          onKeydown: [\n            hd,\n            D(ge, [\"space\"]),\n            D(me, [\"esc\"]),\n            D(me, [\"tab\"])\n          ],\n          onClick: ge\n        }, [\n          R(\"span\", Bt, [\n            e.dropdownOptions.showFlags ? (P(), N(\"span\", {\n              key: 0,\n              class: E([\"vti__flag\", Ce(M)(i.activeCountryCode)])\n            }, null, 2)) : T(\"\", !0),\n            e.dropdownOptions.showDialCodeInSelection ? (P(), N(\"span\", Gt, \" +\" + j(b.value && b.value.dialCode), 1)) : T(\"\", !0),\n            ee(a.$slots, \"arrow-icon\", {\n              open: i.open\n            }, () => [\n              R(\"span\", jt, j(i.open ? \"▲\" : \"▼\"), 1)\n            ])\n          ]),\n          i.open ? (P(), N(\"ul\", {\n            key: 0,\n            ref_key: \"refList\",\n            ref: r,\n            class: E([\"vti__dropdown-list\", i.dropdownOpenDirection]),\n            role: \"listbox\"\n          }, [\n            e.dropdownOptions.showSearchBox ? (P(), N(\"div\", Ut, [\n              ee(a.$slots, \"search-icon\"),\n              Q(R(\"input\", {\n                class: E([\"vti__input\", \"vti__search_box\"]),\n                \"aria-label\": \"Search by country name or country code\",\n                placeholder: e.dropdownOptions.searchBoxPlaceholder || (h.value.length ? h.value[0].name : \"\"),\n                type: \"text\",\n                \"onUpdate:modelValue\": l[0] || (l[0] = (c) => i.searchQuery = c),\n                onClick: l[1] || (l[1] = Od(() => {\n                }, [\"stop\"]))\n              }, null, 8, Ht), [\n                [Pd, i.searchQuery]\n              ])\n            ])) : T(\"\", !0),\n            (P(!0), N(Nd, null, wd(h.value, (c, g) => (P(), N(\"li\", {\n              role: \"option\",\n              class: E([\"vti__dropdown-item\", rd(g, c.iso2)]),\n              key: c.iso2 + (c.preferred ? \"-preferred\" : \"\"),\n              tabindex: \"-1\",\n              onClick: (q) => S(c),\n              onMousemove: (q) => i.selectedIndex = g,\n              \"aria-selected\": i.activeCountryCode === c.iso2 && !c.preferred\n            }, [\n              e.dropdownOptions.showFlags ? (P(), N(\"span\", {\n                key: 0,\n                class: E([\"vti__flag\", Ce(M)(c.iso2)])\n              }, null, 2)) : T(\"\", !0),\n              R(\"strong\", null, j(c.name), 1),\n              e.dropdownOptions.showDialCodeInList ? (P(), N(\"span\", Kt, \" +\" + j(c.dialCode), 1)) : T(\"\", !0)\n            ], 42, Vt))), 128))\n          ], 2)) : T(\"\", !0)\n        ], 42, kt)), [\n          [f, pd]\n        ]),\n        Q(R(\"input\", {\n          \"onUpdate:modelValue\": l[2] || (l[2] = (c) => i.phone = c),\n          ref_key: \"refInput\",\n          ref: $,\n          type: e.inputOptions.type,\n          autocomplete: e.inputOptions.autocomplete,\n          autofocus: e.inputOptions.autofocus,\n          class: E([\"vti__input\", \"vti__phone\", e.inputOptions.styleClasses]),\n          disabled: e.disabled,\n          id: e.inputOptions.id,\n          maxlength: e.inputOptions.maxlength,\n          name: e.inputOptions.name,\n          placeholder: i.parsedPlaceholder,\n          readonly: e.inputOptions.readonly,\n          required: e.inputOptions.required,\n          tabindex: e.inputOptions.tabindex,\n          value: s.value,\n          \"aria-describedby\": e.inputOptions[\"aria-describedby\"],\n          onBlur: od,\n          onFocus: ud,\n          onInput: ye,\n          onKeyup: [\n            D(ld, [\"enter\"]),\n            D(sd, [\"space\"])\n          ]\n        }, null, 42, Wt), [\n          [Id, i.phone]\n        ]),\n        ee(a.$slots, \"icon-right\")\n      ], 2);\n    };\n  }\n}), Zt = {\n  install(e, d = {}) {\n    const {\n      dropdownOptions: t,\n      inputOptions: n,\n      ...r\n    } = d, {\n      dropdownOptions: $,\n      inputOptions: u,\n      ...o\n    } = Ze;\n    re.options = {\n      inputOptions: {\n        ...u,\n        ...n\n      },\n      dropdownOptions: {\n        ...$,\n        ...t\n      },\n      ...o,\n      ...r\n    }, e.component(\"vue-tel-input\", zt);\n  }\n};\nexport {\n  zt as VueTelInput,\n  Zt as default\n};\n", "import { defineComponent as J, toRefs as K, useAttrs as Q, ref as e, watch as X, createElement<PERSON>lock as Y, openBlock as Z, createVNode as y, unref as x, withCtx as V } from \"vue\";\nimport $ from \"./UIIcon.js\";\nimport { VueTelInput as ee } from \"vue-tel-input\";\nimport { useI18n as te } from \"vue-i18n\";\nconst ue = /* @__PURE__ */ J({\n  __name: \"UIInputMobile\",\n  props: {\n    value: {\n      type: String,\n      default: \"\"\n    },\n    type: {\n      type: String,\n      default: \"tel\"\n    },\n    disabled: {\n      type: Boolean,\n      default: !1\n    },\n    readonly: {\n      type: Boolean,\n      default: !1\n    },\n    required: {\n      type: Boolean,\n      default: !1\n    },\n    autocomplete: {\n      type: String,\n      default: \"off\"\n    },\n    pattern: {\n      type: String,\n      default: \"\"\n    },\n    placeholder: {\n      type: String,\n      default: \"\"\n    },\n    showDialCode: {\n      type: Boolean,\n      default: !1\n    },\n    dropdownOptions: {\n      type: Object\n    },\n    defaultCountry: {\n      type: String,\n      default: \"\"\n    },\n    preferredCountries: {\n      type: Array,\n      default: [\"sg\", \"my\", \"id\", \"ph\", \"cn\", \"tw\", \"hk\", \"mo\", \"jp\", \"kr\"]\n    }\n  },\n  emits: [\"input\", \"blur\", \"clearInput\", \"focus\", \"focusChange\"],\n  setup(l, { expose: k, emit: D }) {\n    var O, B;\n    const { t: R } = te(), _ = {\n      showDialCodeInList: !0,\n      showFlags: !0,\n      showSearchBox: !0,\n      searchBoxPlaceholder: R(\"form.search\"),\n      showDialCodeInSelection: !0\n    }, A = l, { value: a, type: C, readonly: F, required: N, autocomplete: P, pattern: T, placeholder: U, showDialCode: q, dropdownOptions: f } = K(A), E = Q(), L = e({ type: C, readonly: F, required: N, autocomplete: P, pattern: T, placeholder: U, showDialCode: q, tabindex: E.tabindex }), M = e({ ..._, ...f == null ? void 0 : f.value }), u = e(((O = a.value.match(/[0-9]*/g)) == null ? void 0 : O.join(\"\")) || \"\"), r = e(((B = a.value) == null ? void 0 : B[0]) === \"+\" ? a.value : a.value ? `+${a.value}` : \"\"), h = e(\"\"), w = e(\"\"), c = e(!1), g = e(!1), b = e(!1), s = e(!1), I = e(\"\"), i = e(void 0), p = e(void 0), v = D;\n    X(a, (t) => {\n      t || (u.value = t, r.value = t, d());\n    });\n    function d() {\n      var t;\n      b.value = !0, v(\"focus\", { target: p.value }), s.value || (t = i.value) == null || t.focus();\n    }\n    function m() {\n      var t;\n      b.value = !1, v(\"blur\", { target: p.value }), (t = i.value) == null || t.blur();\n    }\n    function j(t, o) {\n      const { countryCallingCode: n, nationalNumber: H, formatted: S } = o;\n      u.value = `${n || \"\"}${H || \"\"}` || r.value, c.value = o.valid, g.value = o.possible, h.value = n && S ? `+${n || \"\"} ${S || \"\"}` : \"\", w.value = n, v(\"input\", { target: i.value }, u.value);\n    }\n    function W() {\n      return c.value || (I.value = g.value ? \"\" : \"invalid\"), c.value;\n    }\n    function z() {\n      s.value = !0, d();\n    }\n    function G() {\n      s.value = !1, m();\n    }\n    return k({\n      value: u,\n      formattedValue: h,\n      countryCode: w,\n      focus: d,\n      blur: m,\n      checkValidity: W,\n      validationMessage: I\n    }), (t, o) => (Z(), Y(\"div\", {\n      class: \"input-wrapper--tel\",\n      ref_key: \"inputContainerRef\",\n      ref: p\n    }, [\n      y(x(ee), {\n        ref_key: \"inputRef\",\n        ref: i,\n        disabled: l.disabled,\n        type: x(C),\n        modelValue: r.value,\n        \"onUpdate:modelValue\": o[0] || (o[0] = (n) => r.value = n),\n        mode: \"national\",\n        defaultCountry: l.defaultCountry,\n        inputOptions: L.value,\n        dropdownOptions: M.value,\n        preferredCountries: l.preferredCountries,\n        onFocus: d,\n        onBlur: m,\n        onOnInput: j,\n        onOpen: z,\n        onClose: G\n      }, {\n        \"arrow-icon\": V(() => [\n          y($, {\n            name: s.value ? \"arrow-down\" : \"arrow-up\",\n            class: \"country-code-arrow\"\n          }, null, 8, [\"name\"])\n        ]),\n        \"search-icon\": V(() => [\n          y($, {\n            name: \"search\",\n            class: \"search-icon\"\n          })\n        ]),\n        _: 1\n      }, 8, [\"disabled\", \"type\", \"modelValue\", \"defaultCountry\", \"inputOptions\", \"dropdownOptions\", \"preferredCountries\"])\n    ], 512));\n  }\n});\nexport {\n  ue as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,KAAK;AAAA,EACT;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3X;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK,KAAK,GAAG;AAAA,EAChB;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,OAAO,OAAO,KAAK;AAAA,EACtB;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACjC;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,EAC1C;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,OAAO,KAAK;AAAA,EACf;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACjD;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,MAAM,GAAG;AAAA,EACZ;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,OAAO,KAAK;AAAA,EACf;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,KAAK;AAAA,EACR;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,OAAO,KAAK;AAAA,EACf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jB;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AA1xCA,IA0xCG,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,OAAO;AAAA,EAC9C,MAAM;AAAA,EACN,MAAM,EAAE,YAAY;AAAA,EACpB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AACb,EAAE;AACF,SAAS,KAAK;AACZ,SAAO,MAAM,oBAAoB,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM;AACnE,UAAM,KAAK,KAAK,IAAI,SAAS;AAC7B,QAAI,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,YAAM,IAAI,MAAM,6BAA6B;AAC/C,WAAO,EAAE,OAAO,GAAG,CAAC;AAAA,EACtB,CAAC;AACH;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,EAAE;AACJ,MAAE,MAAM,GAAG,EAAE,kBAAkB,GAAG,CAAC;AAAA,WAC5B,qBAAqB,KAAK,OAAO,EAAE,mBAAmB,YAAY;AACzE,UAAM,IAAI,EAAE,gBAAgB;AAC5B,MAAE,SAAS,IAAE,GAAG,EAAE,QAAQ,aAAa,CAAC,GAAG,EAAE,UAAU,aAAa,CAAC,GAAG,EAAE,OAAO;AAAA,EACnF;AACF;AACA,IAAM,KAAK;AAAA,EACT;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,IACT,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM,CAAC,QAAQ,OAAO,MAAM;AAAA,IAC5B,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS,CAAC,QAAQ,YAAY,eAAe;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM,CAAC,QAAQ,OAAO,MAAM;AAAA,IAC5B,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACV;AACF;AA5PA,IA4PG,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,GAAG,MAAM;AAC/B,MAAI,EAAE,KAAK,SAAS,GAAG,GAAG;AACxB,UAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG;AAC/B,MAAE,CAAC,IAAI,OAAO,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,OAAO,OAAO,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC;AAAA,EAC/F;AACE,WAAO,OAAO,GAAG,EAAE,CAAC,EAAE,IAAI,GAAG,EAAE,QAAQ,CAAC;AAC1C,SAAO;AACT,GAAG,CAAC,CAAC;AAnQL,IAmQQ,KAAK;AAAA,EACX,SAAS,EAAE,GAAG,GAAG;AACnB;AACA,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI,GAAG,QAAQ,CAAC;AACtB,SAAO,OAAO,IAAI,MAAM,GAAG,QAAQ,CAAC,IAAI;AAC1C;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,KAAK,OAAO,SAAS,EAAE,YAAY;AAC5C;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,KAAK,OAAO,SAAS,EAAE,YAAY;AAC5C;AACA,IAAM,KAAK,EAAE,SAAS,GAAG,uBAAuB,EAAE,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,WAAW,EAAE,IAAI,CAAC,OAAO,MAAM,6BAA6B,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,0DAA0D,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,kBAAkB,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,qBAAqB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,0DAA0D,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,iCAAiC,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,MAAM,GAAG,CAAC,oBAAoB,SAAS,CAAC,SAAS,GAAG,OAAO,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,GAAG,OAAO,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,wCAAwC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,wFAAwF,mNAAmN,mSAAmS,6WAA6W,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,iCAAiC,eAAe,CAAC,0BAA0B,6FAA6F,yNAAyN,6SAA6S,sXAAsX,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,iCAAiC,eAAe,CAAC,IAAI,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,iCAAiC,eAAe,CAAC,GAAG,GAAG,OAAO,GAAG,aAAa,CAAC,GAAG,KAAK,GAAG,2jBAA2jB,KAAK,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,MAAM,oKAAoK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,uBAAuB,SAAS,CAAC,qDAAqD,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,uDAAuD,gFAAgF,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,QAAQ,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,eAAe,GAAG,GAAG,GAAG,CAAC,CAAC,+aAA+a,CAAC,CAAC,CAAC,GAAG,CAAC,mHAAmH,CAAC,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC,CAAC,GAAG,CAAC,oDAAoD,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,+BAA+B,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,uDAAuD,6FAA6F,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,yCAAyC,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,kBAAkB,sBAAsB,2BAA2B,GAAG,OAAO,GAAG,CAAC,oCAAoC,eAAe,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,sCAAsC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,aAAa,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,mFAAmF,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,sLAAsL,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,mBAAmB,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,iCAAiC,eAAe,CAAC,aAAa,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,kDAAkD,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,8BAA8B,eAAe,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,iBAAiB,GAAG,KAAK,GAAG,CAAC,2BAA2B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,qCAAqC,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,yBAAyB,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,qCAAqC,GAAG,CAAC,wCAAwC,GAAG,CAAC,eAAe,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,wCAAwC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,eAAe,gCAAgC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iBAAiB,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,OAAO,MAAM,wBAAwB,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,MAAM,0CAA0C,2FAA2F,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,qBAAqB,oBAAoB,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,4DAA4D,GAAG,MAAM,GAAG,CAAC,4BAA4B,YAAY,CAAC,oBAAoB,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,+DAA+D,IAAI,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,eAAe,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,wDAAwD,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,OAAO,oIAAoI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,8BAA8B,YAAY,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,4BAA4B,YAAY,CAAC,qDAAqD,uFAAuF,GAAG,OAAO,GAAG,CAAC,oCAAoC,eAAe,CAAC,yBAAyB,GAAG,OAAO,GAAG,CAAC,oCAAoC,eAAe,CAAC,OAAO,GAAG,OAAO,GAAG,CAAC,8BAA8B,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,2BAA2B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,iCAAiC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,iCAAiC,CAAC,GAAG,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,4MAA4M,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,wCAAwC,CAAC,EAAE,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC,GAAG,CAAC,mPAAmP,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,uDAAuD,sEAAsE,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,KAAK,GAAG,mBAAmB,OAAO,GAAG,GAAG,CAAC,CAAC,2IAA2I,CAAC,CAAC,CAAC,GAAG,CAAC,mHAAmH,CAAC,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,4CAA4C,CAAC,CAAC,CAAC,GAAG,CAAC,oDAAoD,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,4BAA4B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,6BAA6B,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,+BAA+B,CAAC,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,4CAA4C,kBAAkB,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC,iCAAiC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,0DAA0D,sCAAsC,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,MAAM,GAAG,MAAM,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,yBAAyB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,kDAAkD,GAAG,MAAM,GAAG,CAAC,8BAA8B,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,yBAAyB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,yCAAyC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,8BAA8B,uHAAuH,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,wBAAwB,gCAAgC,uCAAuC,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,gRAAgR,6SAA6S,sUAAsU,sUAAsU,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,4BAA4B,4BAA4B,4DAA4D,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,+LAA+L,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,6BAA6B,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,6BAA6B,4CAA4C,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,oBAAoB,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,OAAO,GAAG,UAAU,CAAC,GAAG,KAAK,GAAG,0BAA0B,GAAG,IAAI,CAAC,OAAO,MAAM,iDAAiD,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,qCAAqC,GAAG,IAAI,CAAC,MAAM,OAAO,0DAA0D,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,aAAa,GAAG,OAAO,GAAG,CAAC,mBAAmB,SAAS,CAAC,GAAG,GAAG,OAAO,GAAG,CAAC,iBAAiB,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,KAAK,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,oCAAoC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,uDAAuD,sEAAsE,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,KAAK,GAAG,mBAAmB,OAAO,GAAG,GAAG,CAAC,CAAC,6JAA6J,CAAC,CAAC,CAAC,GAAG,CAAC,mHAAmH,CAAC,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,4CAA4C,CAAC,CAAC,CAAC,GAAG,CAAC,oDAAoD,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,4BAA4B,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,sCAAsC,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,iMAAiM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,uBAAuB,SAAS,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC,uBAAuB,SAAS,CAAC,uGAAuG,8GAA8G,GAAG,KAAK,GAAG,CAAC,uBAAuB,SAAS,CAAC,wGAAwG,0bAA0b,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,uBAAuB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,uBAAuB,SAAS,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,aAAa,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,uBAAuB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,QAAQ,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,OAAO,4BAA4B,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,cAAc,GAAG,IAAI,CAAC,OAAO,MAAM,gCAAgC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,mCAAmC,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,OAAO,GAAG,SAAS,GAAG,UAAU,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,8CAA8C,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,oDAAoD,8EAA8E,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,uBAAuB,0BAA0B,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,0CAA0C,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,mBAAmB,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,8BAA8B,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,eAAe,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,2BAA2B,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,uDAAuD,4EAA4E,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,mBAAmB,SAAS,CAAC,iCAAiC,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,uBAAuB,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,uBAAuB,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,kBAAkB,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,aAAa,sCAAsC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,4BAA4B,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,qBAAqB,GAAG,IAAI,CAAC,MAAM,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,yCAAyC,kBAAkB,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,eAAe,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,2DAA2D,IAAI,GAAG,IAAI,CAAC,MAAM,MAAM,sCAAsC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,QAAQ,SAAS,UAAU,SAAS,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,QAAQ,SAAS,QAAQ,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,0BAA0B,sCAAsC,kDAAkD,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,wBAAwB,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,uBAAuB,gCAAgC,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,67CAA67C,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,6NAA6N,CAAC,EAAE,CAAC,GAAG,CAAC,gCAAgC,GAAG,CAAC,+DAA+D,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,wBAAwB,CAAC,EAAE,CAAC,GAAG,CAAC,6FAA6F,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,mDAAmD,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,KAAK,GAAG,qBAAqB,UAAU,GAAG,GAAG,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC,GAAG,CAAC,qCAAqC,CAAC,EAAE,CAAC,GAAG,CAAC,gCAAgC,GAAG,CAAC,8DAA8D,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,wBAAwB,CAAC,EAAE,CAAC,GAAG,CAAC,6FAA6F,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,6BAA6B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,8BAA8B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,kFAAkF,GAAG,CAAC,wCAAwC,GAAG,CAAC,eAAe,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,wCAAwC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,kDAAkD,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,4DAA4D,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,oCAAoC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,sBAAsB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,OAAO,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,2BAA2B,uDAAuD,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,gCAAgC,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,0BAA0B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,kEAAkE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,2BAA2B,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,6BAA6B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,SAAS,GAAG,CAAC,4BAA4B,YAAY,CAAC,sDAAsD,GAAG,SAAS,GAAG,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,UAAU,gFAAgF,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,cAAc,GAAG,OAAO,GAAG,CAAC,sBAAsB,SAAS,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,QAAQ,GAAG,OAAO,GAAG,CAAC,8BAA8B,YAAY,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,iCAAiC,eAAe,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,uDAAuD,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,gCAAgC,GAAG,OAAO,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,GAAG,OAAO,GAAG,CAAC,2BAA2B,YAAY,CAAC,GAAG,GAAG,OAAO,GAAG,CAAC,8BAA8B,YAAY,CAAC,sBAAsB,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,OAAO,GAAG,CAAC,iCAAiC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,iBAAiB,iDAAiD,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,iCAAiC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,sCAAsC,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,qCAAqC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,qBAAqB,UAAU,GAAG,sBAAsB,GAAG,IAAI,CAAC,MAAM,MAAM,2CAA2C,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,YAAY,MAAM,CAAC,+BAA+B,6CAA6C,4CAA4C,GAAG,GAAG,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,OAAO,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,sCAAsC,4DAA4D,0FAA0F,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,uYAAuY,oeAAoe,ukBAAukB,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,kKAAkK,ySAAyS,iWAAiW,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,gBAAgB,eAAe,GAAG,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,yCAAyC,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,cAAc,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,0EAA0E,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,6BAA6B,yBAAyB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,MAAM,4FAA4F,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,yCAAyC,uDAAuD,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,6aAA6a,GAAG,CAAC,6BAA6B,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,kHAAkH,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,+BAA+B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,qCAAqC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,uBAAuB,UAAU,GAAG,GAAG,CAAC,CAAC,oBAAoB,GAAG,CAAC,uDAAuD,GAAG,CAAC,8BAA8B,GAAG,CAAC,uGAAuG,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,+GAA+G,GAAG,CAAC,2FAA2F,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,MAAM,sCAAsC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,WAAW,GAAG,OAAO,GAAG,CAAC,sBAAsB,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,OAAO,2DAA2D,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,wFAAwF,yKAAyK,sLAAsL,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,yBAAyB,yCAAyC,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,uVAAuV,uoBAAuoB,yvBAAyvB,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,kCAAkC,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,wCAAwC,IAAI,GAAG,IAAI,CAAC,OAAO,OAAO,4DAA4D,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,yBAAyB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,qBAAqB,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,aAAa,GAAG,KAAK,GAAG,CAAC,mCAAmC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,YAAY,yBAAyB,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,oDAAoD,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,4BAA4B,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,SAAS,kCAAkC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,uDAAuD,4GAA4G,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,8BAA8B,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,2BAA2B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,2BAA2B,GAAG,IAAI,CAAC,OAAO,MAAM,mCAAmC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,+BAA+B,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,OAAO,sCAAsC,CAAC,IAAI,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,8CAA8C,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,mBAAmB,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,6BAA6B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,+CAA+C,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,uBAAuB,yBAAyB,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,MAAM,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,mDAAmD,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,6BAA6B,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,gCAAgC,CAAC,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,qBAAqB,GAAG,UAAU,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,0EAA0E,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,kEAAkE,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,kEAAkE,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,sCAAsC,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,8CAA8C,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,CAAC,sCAAsC,eAAe,CAAC,iDAAiD,CAAC,CAAC,GAAG,GAAG,GAAG,mDAAmD,GAAG,IAAI,CAAC,OAAO,MAAM,yBAAyB,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,wCAAwC,kDAAkD,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,+HAA+H,GAAG,CAAC,wEAAwE,GAAG,CAAC,eAAe,GAAG,CAAC,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,yCAAyC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,yBAAyB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,yCAAyC,kBAAkB,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,8BAA8B,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,wDAAwD,GAAG,CAAC,wCAAwC,GAAG,CAAC,eAAe,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,wCAAwC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,qBAAqB,MAAM,GAAG,IAAI,CAAC,OAAO,OAAO,mCAAmC,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,8BAA8B,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,iCAAiC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,qEAAqE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,2DAA2D,GAAG,KAAK,GAAG,CAAC,2BAA2B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,2BAA2B,YAAY,CAAC,6BAA6B,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,iCAAiC,eAAe,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,OAAO,4BAA4B,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,uCAAuC,0CAA0C,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,mCAAmC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,+BAA+B,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,6BAA6B,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,mBAAmB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,yCAAyC,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,wBAAwB,wCAAwC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,aAAa,4CAA4C,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,SAAS,8IAA8I,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,iCAAiC,YAAY,CAAC,kBAAkB,GAAG,GAAG,CAAC,GAAG,CAAC,iCAAiC,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,qBAAqB,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,MAAM,kCAAkC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,gCAAgC,kDAAkD,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,iCAAiC,eAAe,CAAC,eAAe,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,gCAAgC,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,oBAAoB,KAAK,GAAG,IAAI,CAAC,OAAO,OAAO,uDAAuD,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,2BAA2B,YAAY,CAAC,wBAAwB,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,+BAA+B,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,gFAAgF,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,iBAAiB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,yCAAyC,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,2BAA2B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,iBAAiB,GAAG,IAAI,CAAC,OAAO,MAAM,gCAAgC,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,iBAAiB,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,mCAAmC,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,oCAAoC,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,cAAc,2FAA2F,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,kBAAkB,sBAAsB,GAAG,KAAK,GAAG,CAAC,yBAAyB,YAAY,CAAC,2BAA2B,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,kBAAkB,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,wBAAwB,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,8CAA8C,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,qDAAqD,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,2BAA2B,yBAAyB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,IAAI,GAAG,OAAO,GAAG,CAAC,iBAAiB,SAAS,CAAC,GAAG,GAAG,OAAO,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,MAAM,gCAAgC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,eAAe,oDAAoD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,MAAM,mDAAmD,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,iBAAiB,SAAS,CAAC,GAAG,GAAG,OAAO,GAAG,CAAC,sBAAsB,SAAS,CAAC,sEAAsE,qHAAqH,GAAG,OAAO,GAAG,CAAC,oBAAoB,SAAS,CAAC,yBAAyB,+BAA+B,GAAG,OAAO,GAAG,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,cAAc,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,sCAAsC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,mKAAmK,CAAC,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,mJAAmJ,qKAAqK,GAAG,OAAO,GAAG,CAAC,sBAAsB,SAAS,CAAC,4DAA4D,GAAG,OAAO,GAAG,CAAC,oBAAoB,SAAS,CAAC,IAAI,GAAG,OAAO,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,mDAAmD,GAAG,OAAO,GAAG,CAAC,oCAAoC,eAAe,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,yDAAyD,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,kFAAkF,iFAAiF,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,gDAAgD,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,6BAA6B,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,6BAA6B,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,UAAU,kCAAkC,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,wDAAwD,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,uCAAuC,GAAG,OAAO,GAAG,CAAC,sBAAsB,SAAS,CAAC,wDAAwD,GAAG,OAAO,GAAG,CAAC,8BAA8B,YAAY,CAAC,sDAAsD,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,oBAAoB,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,kDAAkD,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,yBAAyB,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,sCAAsC,GAAG,CAAC,8FAA8F,GAAG,CAAC,UAAU,GAAG,CAAC,kBAAkB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,kDAAkD,GAAG,CAAC,iCAAiC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,wCAAwC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,UAAU,YAAY,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,MAAM,yGAAyG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,sBAAsB,GAAG,KAAK,GAAG,CAAC,uBAAuB,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,0BAA0B,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,sBAAsB,sDAAsD,uEAAuE,GAAG,UAAU,CAAC,GAAG,CAAC,iCAAiC,eAAe,CAAC,uBAAuB,4EAA4E,4LAA4L,GAAG,UAAU,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,0BAA0B,GAAG,UAAU,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,mBAAmB,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,kCAAkC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,gCAAgC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,SAAS,+BAA+B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,4BAA4B,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,cAAc,mCAAmC,CAAC,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,2EAA2E,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,8BAA8B,YAAY,CAAC,IAAI,GAAG,OAAO,GAAG,UAAU,GAAG,CAAC,oBAAoB,SAAS,CAAC,iBAAiB,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,wCAAwC,GAAG,OAAO,GAAG,UAAU,GAAG,CAAC,mCAAmC,eAAe,CAAC,GAAG,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,8BAA8B,YAAY,CAAC,sHAAsH,GAAG,OAAO,GAAG,UAAU,GAAG,CAAC,8BAA8B,YAAY,CAAC,eAAe,GAAG,OAAO,GAAG,UAAU,GAAG,CAAC,sCAAsC,eAAe,CAAC,mDAAmD,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,oCAAoC,eAAe,CAAC,MAAM,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,iCAAiC,eAAe,CAAC,GAAG,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,oCAAoC,eAAe,CAAC,+EAA+E,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,OAAO,GAAG,aAAa,GAAG,CAAC,4CAA4C,kBAAkB,CAAC,MAAM,GAAG,OAAO,GAAG,gBAAgB,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,aAAa,gDAAgD,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,yBAAyB,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,wBAAwB,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,wBAAwB,oCAAoC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,uBAAuB,GAAG,KAAK,GAAG,CAAC,iCAAiC,eAAe,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,MAAM,iCAAiC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,0CAA0C,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,2BAA2B,YAAY,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,eAAe,iBAAiB,GAAG,KAAK,GAAG,CAAC,iCAAiC,eAAe,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,oCAAoC,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,2BAA2B,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,2BAA2B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,iBAAiB,QAAQ,GAAG,IAAI,CAAC,OAAO,MAAM,yBAAyB,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,wDAAwD,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,SAAS,CAAC,+BAA+B,CAAC,GAAG,CAAC,iBAAiB,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,0BAA0B,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,8BAA8B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,uCAAuC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,OAAO,uCAAuC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,gBAAgB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,4BAA4B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,8BAA8B,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,sBAAsB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,SAAS,2BAA2B,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,WAAW,8CAA8C,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,OAAO,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,yBAAyB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,kBAAkB,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,OAAO,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,IAAI,GAAG,QAAQ,GAAG,CAAC,iCAAiC,eAAe,CAAC,OAAO,GAAG,QAAQ,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,MAAM,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,qDAAqD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,iCAAiC,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,kDAAkD,CAAC,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,kBAAkB,sBAAsB,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,4BAA4B,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,uBAAuB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,MAAM,0BAA0B,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,oBAAoB,gDAAgD,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,2BAA2B,YAAY,CAAC,yCAAyC,8CAA8C,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,WAAW,6BAA6B,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,0BAA0B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,+DAA+D,iFAAiF,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,yFAAyF,mGAAmG,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,iBAAiB,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,WAAW,sCAAsC,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,MAAM,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,yBAAyB,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,uBAAuB,CAAC,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,4BAA4B,cAAc,CAAC,OAAO,GAAG,GAAG,GAAG,UAAU,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,krBAAkrB,GAAG,CAAC,EAAE,GAAG,CAAC,sCAAsC,GAAG,CAAC,gBAAgB,GAAG,CAAC,yOAAyO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,oBAAoB,kDAAkD,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,sCAAsC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,OAAO,gCAAgC,CAAC,CAAC,GAAG,CAAC,CAAC,oCAAoC,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,MAAM,gFAAgF,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,MAAM,wCAAwC,CAAC,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,OAAO,gCAAgC,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,sBAAsB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,OAAO,+BAA+B,CAAC,EAAE,GAAG,GAAG,KAAK,GAAG,oBAAoB,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,MAAM,oDAAoD,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,sCAAsC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,sCAAsC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,KAAK,iDAAiD,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,YAAY,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,sBAAsB,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,MAAM,yCAAyC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,gCAAgC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,2BAA2B,YAAY,CAAC,0BAA0B,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,sCAAsC,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,yCAAyC,GAAG,CAAC,gEAAgE,GAAG,CAAC,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,kCAAkC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,0BAA0B,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,qCAAqC,CAAC,CAAC,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,MAAM,yHAAyH,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,sBAAsB,SAAS,CAAC,wFAAwF,GAAG,KAAK,GAAG,CAAC,2BAA2B,YAAY,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,8CAA8C,4EAA4E,GAAG,OAAO,GAAG,CAAC,4BAA4B,YAAY,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,gDAAgD,+CAA+C,GAAG,KAAK,GAAG,CAAC,oBAAoB,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,2IAA2I,GAAG,KAAK,GAAG,CAAC,8BAA8B,YAAY,CAAC,iBAAiB,GAAG,KAAK,GAAG,CAAC,sBAAsB,SAAS,CAAC,cAAc,UAAU,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,GAAG,eAAe,EAAE,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,uBAAuB,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,yBAAyB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC,4BAA4B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,0BAA0B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,2BAA2B,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,wEAAwE,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,oBAAoB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,4BAA4B,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,8BAA8B,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,mEAAmE,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,+KAA+K,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,4BAA4B,CAAC,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,8BAA8B,YAAY,CAAC,mCAAmC,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,oCAAoC,eAAe,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,kIAAkI,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC,4BAA4B,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,yBAAyB,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE;AACn/pF,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,MAAM,UAAU,MAAM,KAAK,CAAC;AACpC,SAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,MAAM,CAAC;AACpC;AACA,SAAS,GAAG,GAAG;AACb;AACA,SAAO,KAAK,OAAO,UAAU,cAAc,OAAO,OAAO,YAAY,WAAW,SAAS,GAAG;AAC1F,WAAO,OAAO;AAAA,EAChB,IAAI,SAAS,GAAG;AACd,WAAO,KAAK,OAAO,UAAU,cAAc,EAAE,gBAAgB,UAAU,MAAM,OAAO,YAAY,WAAW,OAAO;AAAA,EACpH,GAAG,GAAG,CAAC;AACT;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC;AACX,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,EAAE,KAAK,CAAC;AAAA,EAC9H;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,KAAK,GAAG,EAAE,WAAW,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa,EAAE,UAAU,MAAG,CAAC,GAAG;AAC1G;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,EAAE,aAAa;AACjB,UAAM,IAAI,UAAU,mCAAmC;AAC3D;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,OAAO,KAAK,cAAc,MAAM;AAClC,UAAM,IAAI,UAAU,oDAAoD;AAC1E,IAAE,YAAY,OAAO,OAAO,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,GAAG,UAAU,MAAI,cAAc,KAAG,EAAE,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa,EAAE,UAAU,MAAG,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC;AACpL;AACA,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,GAAG;AACX,SAAO,WAAW;AAChB,QAAI,IAAI,EAAE,CAAC,GAAG;AACd,QAAI,GAAG;AACL,UAAI,IAAI,EAAE,IAAI,EAAE;AAChB,UAAI,QAAQ,UAAU,GAAG,WAAW,CAAC;AAAA,IACvC;AACE,UAAI,EAAE,MAAM,MAAM,SAAS;AAC7B,WAAO,GAAG,MAAM,CAAC;AAAA,EACnB;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,MAAM,GAAG,CAAC,MAAM,YAAY,OAAO,KAAK;AAC1C,WAAO;AACT,MAAI,MAAM;AACR,UAAM,IAAI,UAAU,0DAA0D;AAChF,SAAO,GAAG,CAAC;AACb;AACA,SAAS,GAAG,GAAG;AACb,MAAI,MAAM;AACR,UAAM,IAAI,eAAe,2DAA2D;AACtF,SAAO;AACT;AACA,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,OAAO,OAAO,aAA6B,oBAAI,IAAI,IAAI;AAC/D,SAAO,KAAK,SAAS,GAAG;AACtB,QAAI,MAAM,QAAQ,CAAC,GAAG,CAAC;AACrB,aAAO;AACT,QAAI,OAAO,KAAK;AACd,YAAM,IAAI,UAAU,oDAAoD;AAC1E,QAAI,OAAO,IAAI,KAAK;AAClB,UAAI,EAAE,IAAI,CAAC;AACT,eAAO,EAAE,IAAI,CAAC;AAChB,QAAE,IAAI,GAAG,CAAC;AAAA,IACZ;AACA,aAAS,IAAI;AACX,aAAO,EAAE,GAAG,WAAW,EAAE,IAAI,EAAE,WAAW;AAAA,IAC5C;AACA,WAAO,EAAE,YAAY,OAAO,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,GAAG,YAAY,OAAI,UAAU,MAAI,cAAc,KAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;AAAA,EACxI,GAAG,GAAG,CAAC;AACT;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,SAAO,GAAG,IAAI,IAAI,QAAQ,YAAY,IAAI,SAAS,GAAG,GAAG,GAAG;AAC1D,QAAI,IAAI,CAAC,IAAI;AACb,MAAE,KAAK,MAAM,GAAG,CAAC;AACjB,QAAI,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAGA,KAAI,IAAI,EAAE;AAC7C,WAAO,KAAK,EAAEA,IAAG,EAAE,SAAS,GAAGA;AAAA,EACjC,GAAG,EAAE,MAAM,MAAM,SAAS;AAC5B;AACA,SAAS,KAAK;AACZ,MAAI,OAAO,UAAU,OAAO,CAAC,QAAQ,aAAa,QAAQ,UAAU;AAClE,WAAO;AACT,MAAI,OAAO,SAAS;AAClB,WAAO;AACT,MAAI;AACF,WAAO,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAW;AAAA,IAChF,CAAC,CAAC,GAAG;AAAA,EACP,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,SAAS,SAAS,KAAK,CAAC,EAAE,QAAQ,eAAe,MAAM;AAChE;AACA,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,IAAI,OAAO,kBAAkB,SAAS,GAAG,GAAG;AACjD,WAAO,EAAE,YAAY,GAAG;AAAA,EAC1B,GAAG,EAAE,GAAG,CAAC;AACX;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,IAAI,OAAO,iBAAiB,OAAO,iBAAiB,SAAS,GAAG;AACrE,WAAO,EAAE,aAAa,OAAO,eAAe,CAAC;AAAA,EAC/C,GAAG,EAAE,CAAC;AACR;AACA,IAAI,IAAoB,SAAS,GAAG;AAClC,KAAG,GAAG,CAAC;AACP,MAAI,IAAI,GAAG,CAAC;AACZ,WAAS,EAAE,GAAG;AACZ,QAAI;AACJ,WAAO,GAAG,MAAM,CAAC,GAAG,IAAI,EAAE,KAAK,MAAM,CAAC,GAAG,OAAO,eAAe,GAAG,CAAC,GAAG,EAAE,SAAS,GAAG,EAAE,OAAO,EAAE,YAAY,MAAM;AAAA,EACnH;AACA,SAAO,GAAG,CAAC;AACb,EAAkB,GAAG,KAAK,CAAC;AAR3B,IAQ8B,KAAK;AARnC,IAQsC,KAAK;AAR3C,IAQ+C,KAAK;AARpD,IAQuD,IAAI;AAR3D,IAQ2E,KAAK;AARhF,IAQ2F,KAAK;AARhG,IAQsG,KAAK;AAR3G,IAQiH,KAAK;AARtH,IAQgI,KAAK;AARrI,IAQqJ,KAAK;AAR1J,IAQkK,IAAI,GAAG,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE;AARzO,IAQ4O,KAAK;AACjP,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,EAAE,MAAM,GAAG,GAAG,IAAI,EAAE,MAAM,GAAG;AACjC,WAAS,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK;AACpE,QAAI,IAAI,OAAO,EAAE,CAAC,CAAC,GAAG,IAAI,OAAO,EAAE,CAAC,CAAC;AACrC,QAAI,IAAI;AACN,aAAO;AACT,QAAI,IAAI;AACN,aAAO;AACT,QAAI,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC;AACtB,aAAO;AACT,QAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AACtB,aAAO;AAAA,EACX;AACA,SAAO,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK;AAC1G;AACA,IAAI,KAAK,CAAC,EAAE;AACZ,SAAS,EAAE,GAAG;AACZ,SAAO,KAAK,QAAQ,EAAE,gBAAgB;AACxC;AACA,SAAS,GAAG,GAAG;AACb;AACA,SAAO,KAAK,OAAO,UAAU,cAAc,OAAO,OAAO,YAAY,WAAW,SAAS,GAAG;AAC1F,WAAO,OAAO;AAAA,EAChB,IAAI,SAAS,GAAG;AACd,WAAO,KAAK,OAAO,UAAU,cAAc,EAAE,gBAAgB,UAAU,MAAM,OAAO,YAAY,WAAW,OAAO;AAAA,EACpH,GAAG,GAAG,CAAC;AACT;AACA,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,EAAE,aAAa;AACjB,UAAM,IAAI,UAAU,mCAAmC;AAC3D;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC;AACX,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,EAAE,KAAK,CAAC;AAAA,EAC9H;AACF;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,SAAO,KAAK,GAAG,EAAE,WAAW,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa,EAAE,UAAU,MAAG,CAAC,GAAG;AAC1G;AACA,IAAI,KAAK;AAAT,IAAkB,KAAK;AAAvB,IAAiC,KAAK;AAAtC,IAAgD,KAAK;AAArD,IAA8D,IAAoB,WAAW;AAC3F,WAAS,EAAE,GAAG;AACZ,MAAE,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,WAAW,GAAG,GAAG,KAAK,MAAM,CAAC;AAAA,EACvD;AACA,SAAO,EAAE,GAAG,CAAC;AAAA,IACX,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,OAAO,KAAK,KAAK,SAAS,SAAS,EAAE,OAAO,SAAS,GAAG;AAC7D,eAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,SAAS,UAAU,CAAC;AAAA,IAClC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,EAAE,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/B,eAAO,KAAK,SAAS,iBAAiB,KAAK,SAAS;AAAA,IACxD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,mBAAmB,CAAC,MAAM;AAAA,IACxC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,UAAI,KAAK,8BAA8B,CAAC;AACtC,eAAO;AACT,UAAI,KAAK,cAAc,GAAG;AACxB,YAAI,KAAK,cAAc,EAAE,CAAC;AACxB,iBAAO;AAAA,MACX,OAAO;AACL,YAAI,IAAI,KAAK,oBAAoB,EAAE,CAAC;AACpC,YAAI,KAAK,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM;AAClC,iBAAO;AAAA,MACX;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,cAAc,IAAI,CAAC,CAAC,KAAK,cAAc,EAAE,CAAC,IAAI,CAAC,KAAK,8BAA8B,CAAC;AAAA,IACjG;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,oBAAoB,CAAC;AAAA,IACnC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,KAAK,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,IAAI,OAAO,KAAK,MAAM,OAAO;AAC1D,YAAI,CAAC,KAAK,WAAW,CAAC;AACpB,gBAAM,IAAI,MAAM,oBAAoB,OAAO,CAAC,CAAC;AAC/C,aAAK,gBAAgB,IAAI,GAAG,KAAK,mBAAmB,CAAC,GAAG,IAAI;AAAA,MAC9D,WAAW,GAAG;AACZ,YAAI,CAAC,KAAK,eAAe,CAAC;AACxB,gBAAM,IAAI,MAAM,yBAAyB,OAAO,CAAC,CAAC;AACpD,aAAK,gBAAgB,IAAI,GAAG,KAAK,yBAAyB,CAAC,GAAG,IAAI;AAAA,MACpE;AACE,aAAK,gBAAgB;AACvB,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,UAAI,IAAI,KAAK,oBAAoB,EAAE,CAAC;AACpC,UAAI;AACF,eAAO,EAAE,WAAW,KAAK,EAAE,CAAC,EAAE,WAAW,IAAI,SAAS;AAAA,IAC1D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,UAAI,IAAI,KAAK,8BAA8B,CAAC;AAC5C,UAAI;AACF,eAAO,EAAE,CAAC;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,UAAI,IAAI,KAAK,6BAA6B,CAAC;AAC3C,UAAI;AACF,eAAO,KAAK,mBAAmB,CAAC;AAClC,UAAI,KAAK,cAAc,GAAG;AACxB,YAAI,IAAI,KAAK,cAAc,EAAE,CAAC;AAC9B,YAAI;AACF,iBAAO;AAAA,MACX,OAAO;AACL,YAAI,IAAI,KAAK,oBAAoB,EAAE,CAAC;AACpC,YAAI,KAAK,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM;AAClC,iBAAO,KAAK,SAAS,UAAU,KAAK;AAAA,MACxC;AAAA,IACF;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,YAAY;AAAA,IACxC;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,UAAU;AAAA,IACtC;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,iBAAiB;AAAA,IAC7C;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,sBAAsB;AAAA,IAClD;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,gBAAgB;AAAA,IAC5C;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,QAAQ;AAAA,IACpC;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,yBAAyB;AAAA,IACrD;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,4BAA4B;AAAA,IACxD;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,cAAc;AAAA,IAC1C;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,SAAS;AAAA,IACrC;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,cAAc,KAAK,CAAC;AAAA,IAClC;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,cAAc,IAAI;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,KAAK,KAAK,SAAS,kCAAkC,KAAK,SAAS;AAAA,IACjF;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,oBAAoB,CAAC;AAAA,IACnC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,kBAAkB;AAAA,IAChC;AAAA,EACF,CAAC,CAAC,GAAG;AACP,EAAE;AA1LF,IA0LK,KAAqB,WAAW;AACnC,WAAS,EAAE,GAAG,GAAG;AACf,MAAE,MAAM,CAAC,GAAG,KAAK,uBAAuB,GAAG,KAAK,WAAW,GAAG,GAAG,KAAK,MAAM,EAAE,QAAQ;AAAA,EACxF;AACA,SAAO,EAAE,GAAG,CAAC;AAAA,IACX,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,SAAS,CAAC;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,qBAAqB,yBAAyB,KAAK,YAAY,CAAC;AAAA,IAC9E;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,EAAE,KAAK,MAAM,KAAK;AACpB,eAAO,KAAK,SAAS,CAAC;AAAA,IAC1B;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,EAAE,KAAK,MAAM,KAAK;AACpB,eAAO,KAAK,SAAS,EAAE;AAAA,IAC3B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,MAAM,KAAK,KAAK,KAAK,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC;AAAA,IAChE;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,CAAC,KAAK;AACR,eAAO,KAAK,SAAS,KAAK,KAAK,IAAI,CAAC;AAAA,IACxC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,EAAE,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA,EAIF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,IAAI,MAAM,IAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,KAAK,YAAY,KAAK,mCAAmC,CAAC,KAAK,CAAC;AACrH,aAAO,EAAE,IAAI,SAAS,GAAG;AACvB,eAAO,IAAI,GAAG,GAAG,CAAC;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAAA,IACpD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,EAAE,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA,EAIF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,iCAAiC,KAAK,QAAQ,KAAK,KAAK,iCAAiC,KAAK,mCAAmC,CAAC;AAAA,IAChJ;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAAA,IACpD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,0BAA0B,KAAK,KAAK,eAAe;AAAA,IACjE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAAA,IACpD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAAA,IACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,2CAA2C,KAAK,QAAQ,KAAK,KAAK,2CAA2C,KAAK,mCAAmC,CAAC;AAAA,IACpK;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE;AAAA,IACrD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,EAAE;AAAA,IACtD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE,WAAW,IAAI,QAAK,CAAC,CAAC,KAAK,MAAM;AAAA,IACvE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,UAAI,KAAK,SAAS,KAAK,GAAG,KAAK,MAAM,GAAG,CAAC;AACvC,eAAO,IAAI,GAAG,GAAG,KAAK,MAAM,GAAG,CAAC,GAAG,IAAI;AAAA,IAC3C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,SAAS,EAAE,KAAK;AAAA,IACxD;AAAA,EACF,CAAC,CAAC,GAAG;AACP,EAAE;AAlUF,IAkUK,KAAqB,WAAW;AACnC,WAAS,EAAE,GAAG,GAAG;AACf,MAAE,MAAM,CAAC,GAAG,KAAK,UAAU,GAAG,KAAK,WAAW;AAAA,EAChD;AACA,SAAO,EAAE,GAAG,CAAC;AAAA,IACX,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,QAAQ,CAAC;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,QAAQ,CAAC;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,QAAQ,CAAC,KAAK,CAAC;AAAA,IAC7B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,QAAQ,CAAC,KAAK,KAAK,SAAS,6BAA6B;AAAA,IACvE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,CAAC,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK,SAAS,uDAAuD;AAAA,IACnG;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,mBAAmB,KAAK,CAAC,KAAK,uDAAuD;AAAA,IACnG;AAAA;AAAA,EAEF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,CAAC,EAAE,KAAK,6BAA6B;AAAA,MAC5C,CAAC,GAAG,KAAK,KAAK,6BAA6B,CAAC;AAAA,IAC9C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,QAAQ,CAAC,KAAK,KAAK,OAAO;AAAA,IACxC;AAAA,EACF,CAAC,CAAC,GAAG;AACP,EAAE;AAjXF,IAiXK,KAAK;AAjXV,IAiXyB,KAAqB,WAAW;AACvD,WAAS,EAAE,GAAG,GAAG;AACf,MAAE,MAAM,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,WAAW;AAAA,EAC7C;AACA,SAAO,EAAE,GAAG,CAAC;AAAA,IACX,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,SAAS,KAAK,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,IACnD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,CAAC,KAAK,SAAS;AACjB,eAAO,KAAK,KAAK,CAAC,KAAK,KAAK,SAAS,gBAAgB;AAAA,IACzD;AAAA,EACF,CAAC,CAAC,GAAG;AACP,EAAE;AACF,SAAS,GAAG,GAAG,GAAG;AAChB,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,IACZ,KAAK;AACH,aAAO,EAAE,CAAC;AAAA,EACd;AACF;AACA,SAAS,GAAG,GAAG;AACb,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,2EAA2E;AAC7F,MAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,SAAS;AACzB,UAAM,IAAI,MAAM,sJAAsJ,OAAO,EAAE,CAAC,IAAI,2BAA2B,OAAO,KAAK,CAAC,EAAE,KAAK,IAAI,IAAI,OAAO,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AACnR;AACA,IAAI,KAAK,SAAS,GAAG;AACnB,SAAO,GAAG,CAAC;AACb;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,WAAW,CAAC;AAC9B,WAAO,EAAE,QAAQ,CAAC,EAAE,mBAAmB;AACzC,QAAM,IAAI,MAAM,oBAAoB,OAAO,CAAC,CAAC;AAC/C;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,SAAO,EAAE,UAAU,eAAe,CAAC;AACrC;AACA,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,EAAE;AACV,SAAO,KAAK,YAAY,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,KAAK,IAAI,GAAG,GAAG,EAAE,MAAM,KAAK,KAAK,KAAK,OAAK,GAAG,GAAG,EAAE,MAAM,KAAK,KAAK,KAAK,OAAK,KAAK,KAAK,OAAK,KAAK,KAAK;AACzM;AACA,IAAI,KAAK;AAAT,IAAkB,IAAI,SAAS,GAAG;AAChC,SAAO,KAAK,OAAO,GAAG,MAAM,EAAE,OAAO,GAAG,IAAI;AAC9C;AACA,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,aAAa,IAAI,sBAAsB,IAAI,MAAM,IAAI,iDAAiDA,KAAI,wBAAwB,IAAI,SAAS,IAAI,YAAY,IAAI,cAAc,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,IAAIA,KAAI,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,IAAI,WAAW,IAAI,EAAE,CAAC,IAAI;AAC9W,SAAO,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC3D;AACA,IAAI,KAAK,MAAM,IAAI,OAAO,KAAK;AAA/B,IAAoC,KAAK,MAAM,KAAK,eAAe,IAAI,QAAQ,IAAI,YAAY,IAAI,IAAI;AAAvG,IAA6G,KAAK,IAAI,OAAO,OAAO,KAAK,eAAe,IAAI,QAAQ,IAAI,YAAY,GAAG;AAAvL,IAA0L,KAAK;AAC/L,QAAQ,GAAG,IAAI;AADf,IACqB,KAAK,IAAI;AAAA;AAAA,EAE5B,MAAM,KAAK,QAAQ,KAAK;AAAA,EACxB;AACF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,UAAU,MAAM,GAAG,KAAK,CAAC;AACpC;AACA,SAAS,GAAG,GAAG;AACb,SAAO,GAAG,KAAK,CAAC;AAClB;AACA,IAAI,KAAK,IAAI,OAAO,QAAQ,GAAG,IAAI,MAAM,GAAG;AAC5C,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,EAAE,OAAO,EAAE;AACnB,MAAI,IAAI;AACN,WAAO,CAAC;AACV,WAAS,IAAI,EAAE,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,UAAU;AAClE,QAAI,EAAE,CAAC;AACL,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,KAAK,EAAE,CAAC;AAAA,MACV;AACF;AAAA,EACF;AACF;AACA,IAAI,KAAK;AAAA,EACP,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAAA,EAEL,KAAK;AAAA;AAEP;AACA,SAAS,GAAG,GAAG;AACb,SAAO,GAAG,CAAC;AACb;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,SAAS,OAAO,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AACnE,MAAI;AACF,YAAQ,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;AACpC,MAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,OAAO,EAAE,UAAU,UAAU;AAC5E,UAAM,IAAI;AACV,QAAI,IAAI;AACR,WAAO,WAAW;AAChB,aAAO,KAAK,EAAE,SAAS,EAAE,MAAM,KAAG,IAAI,EAAE,MAAM,OAAI,OAAO,EAAE,GAAG,EAAE;AAAA,IAClE;AAAA,EACF;AACA,QAAM,IAAI,UAAU;AAAA,mFAC6D;AACnF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG;AACL,QAAI,OAAO,KAAK;AACd,aAAO,GAAG,GAAG,CAAC;AAChB,QAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,QAAI,MAAM,YAAY,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,MAAM,SAAS,MAAM;AACpF,aAAO,MAAM,KAAK,CAAC;AACrB,QAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AACxE,aAAO,GAAG,GAAG,CAAC;AAAA,EAClB;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,GAAC,KAAK,QAAQ,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;AACvC,MAAE,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACA,SAAS,GAAG,GAAG;AACb,WAAS,IAAI,IAAI,IAAI,GAAG,EAAE,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,QAAQ;AAC1D,QAAI,IAAI,EAAE;AACV,SAAK,GAAG,GAAG,CAAC,KAAK;AAAA,EACnB;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,SAAO,MAAM,MAAM,IAAI,SAAS,MAAM,GAAG,CAAC;AAC5C;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,SAAS,OAAO,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AACnE,MAAI;AACF,YAAQ,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;AACpC,MAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,OAAO,EAAE,UAAU,UAAU;AAC5E,UAAM,IAAI;AACV,QAAI,IAAI;AACR,WAAO,WAAW;AAChB,aAAO,KAAK,EAAE,SAAS,EAAE,MAAM,KAAG,IAAI,EAAE,MAAM,OAAI,OAAO,EAAE,GAAG,EAAE;AAAA,IAClE;AAAA,EACF;AACA,QAAM,IAAI,UAAU;AAAA,mFAC6D;AACnF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG;AACL,QAAI,OAAO,KAAK;AACd,aAAO,GAAG,GAAG,CAAC;AAChB,QAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,QAAI,MAAM,YAAY,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,MAAM,SAAS,MAAM;AACpF,aAAO,MAAM,KAAK,CAAC;AACrB,QAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AACxE,aAAO,GAAG,GAAG,CAAC;AAAA,EAClB;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,GAAC,KAAK,QAAQ,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;AACvC,MAAE,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,WAAS,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,QAAQ;AACvD,QAAI,IAAI,EAAE;AACV,MAAE,QAAQ,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC;AAAA,EAC9B;AACA,SAAO,EAAE,KAAK,SAAS,GAAG,GAAG;AAC3B,WAAO,IAAI;AAAA,EACb,CAAC;AACH;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,SAAO,GAAG,GAAG,QAAQ,CAAC;AACxB;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,EAAE,KAAK,CAAC,GAAG,IAAI,KAAK,EAAE,gBAAgB,KAAK,EAAE,gBAAgB;AACrE,MAAI,CAAC;AACH,WAAO;AACT,MAAI,MAAM,wBAAwB;AAChC,QAAI,CAAC,EAAE,KAAK,YAAY;AACtB,aAAO,GAAG,GAAG,UAAU,CAAC;AAC1B,QAAI,IAAI,EAAE,KAAK,QAAQ;AACvB,UAAM,IAAI,GAAG,GAAG,EAAE,gBAAgB,CAAC;AAAA,EACrC,WAAW,KAAK,CAAC;AACf,WAAO;AACT,MAAI,IAAI,EAAE,QAAQ,IAAI,EAAE,CAAC;AACzB,SAAO,MAAM,IAAI,gBAAgB,IAAI,IAAI,cAAc,EAAE,EAAE,SAAS,CAAC,IAAI,IAAI,aAAa,EAAE,QAAQ,GAAG,CAAC,KAAK,IAAI,gBAAgB;AACnI;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,MAAM,WAAW,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI;AAChD,QAAI,CAAC,EAAE;AACL,YAAM,IAAI,MAAM,oCAAoC;AACtD,MAAE,oBAAoB,EAAE,kBAAkB;AAAA,EAC5C,OAAO;AACL,QAAI,CAAC,EAAE;AACL,aAAO;AACT,QAAI,EAAE,SAAS;AACb,UAAI,CAAC,EAAE,WAAW,EAAE,OAAO;AACzB,cAAM,IAAI,MAAM,oBAAoB,OAAO,EAAE,OAAO,CAAC;AACvD,QAAE,QAAQ,EAAE,OAAO;AAAA,IACrB,OAAO;AACL,UAAI,CAAC,EAAE;AACL,cAAM,IAAI,MAAM,oCAAoC;AACtD,QAAE,oBAAoB,EAAE,kBAAkB;AAAA,IAC5C;AAAA,EACF;AACA,MAAI,EAAE,gBAAgB;AACpB,WAAO,GAAG,EAAE,SAAS,EAAE,gBAAgB,CAAC;AAC1C,MAAI,EAAE,sBAAsB,EAAE,2BAA2B,EAAE,kBAAkB;AAC3E,WAAO;AACT,QAAM,IAAI,MAAM,gGAAgG;AAClH;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,UAAQ,GAAG,GAAG,CAAC,GAAG;AAAA,IAChB,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,IAAI,KAAK,IAAI,IAAI,OAAO,SAAS,IAAI,IAAI,EAAE,KAAK,CAAC;AAC1D;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,SAAS,OAAO,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AACnE,MAAI;AACF,YAAQ,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;AACpC,MAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,OAAO,EAAE,UAAU,UAAU;AAC5E,UAAM,IAAI;AACV,QAAI,IAAI;AACR,WAAO,WAAW;AAChB,aAAO,KAAK,EAAE,SAAS,EAAE,MAAM,KAAG,IAAI,EAAE,MAAM,OAAI,OAAO,EAAE,GAAG,EAAE;AAAA,IAClE;AAAA,EACF;AACA,QAAM,IAAI,UAAU;AAAA,mFAC6D;AACnF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG;AACL,QAAI,OAAO,KAAK;AACd,aAAO,GAAG,GAAG,CAAC;AAChB,QAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,QAAI,MAAM,YAAY,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,MAAM,SAAS,MAAM;AACpF,aAAO,MAAM,KAAK,CAAC;AACrB,QAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AACxE,aAAO,GAAG,GAAG,CAAC;AAAA,EAClB;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,GAAC,KAAK,QAAQ,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;AACvC,MAAE,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACA,IAAI,KAAK,CAAC,UAAU,gBAAgB,aAAa,eAAe,QAAQ,mBAAmB,SAAS,OAAO,WAAW;AACtH,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC,EAAE,qBAAqB;AACvD,QAAI,IAAI,EAAE,CAAC,GAAG,EAAE,oBAAoB,EAAE,SAAS,EAAE,kBAAkB;AACnE,QAAI,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE;AACpC,QAAI,EAAE,GAAG,EAAE,sBAAsB,CAAC,GAAG;AACnC,UAAI,GAAG,GAAG,cAAc,CAAC;AACvB,eAAO,EAAE,KAAK,QAAQ,KAAK,EAAE,KAAK,QAAQ,EAAE,QAAQ,MAAM,MAAM,CAAC,EAAE,KAAK,QAAQ,KAAK,GAAG,GAAG,UAAU,CAAC,IAAI,yBAAyB;AACrI,eAAS,IAAI,GAAG,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,QAAQ;AACzC,YAAI,IAAI,EAAE;AACV,YAAI,GAAG,GAAG,GAAG,CAAC;AACZ,iBAAO;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,QAAQ,KAAK,EAAE,gBAAgB,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,IAAI,IAAI,QAAK,EAAE,GAAG,EAAE,QAAQ,CAAC;AACtI;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,oBAAoB,EAAE,SAAS,EAAE,kBAAkB,GAAG,EAAE,SAAS;AAChG,WAAO,GAAG,GAAG,GAAG,EAAE,QAAQ,MAAM;AAClC,MAAI,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE;AACpC,SAAO,EAAE,GAAG,EAAE,sBAAsB,CAAC;AACvC;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,8BAA8B,CAAC;AACvD,SAAO,IAAI,EAAE,OAAO,SAAS,GAAG;AAC9B,WAAO,GAAG,GAAG,GAAG,CAAC;AAAA,EACnB,CAAC,IAAI,CAAC;AACR;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,IAAI,EAAE,CAAC;AACf,SAAO,EAAE,oBAAoB,CAAC,GAAG,EAAE,cAAc,gBAAgB,EAAE,QAAQ,EAAE,MAAM,KAAK;AAC1F;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,QAAQ,IAAI,OAAO,IAAI,OAAO,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,EAAE,KAAK;AACnE;AACA,IAAI,KAAK;AACT,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,EAAE,wBAAwB,IAAI,EAAE;AACxC,IAAE,aAAa,EAAE;AACjB,MAAI,IAAI,EAAE,QAAQ,IAAI,OAAO,EAAE,QAAQ,CAAC,GAAG,IAAI,EAAE,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcnE,KAAK,EAAE,6BAA6B,IAAI,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,6BAA6B,CAAC,IAAI,EAAE,OAAO;AAAA,GAC7G;AACD,SAAO,IAAI,GAAG,CAAC,IAAI;AACrB;AACA,IAAI,KAAK;AACT,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,IAAI,EAAE,CAAC;AACf,MAAI,EAAE,oBAAoB,GAAG,CAAC,GAAG,EAAE,iBAAiB;AAClD,WAAO,EAAE,iBAAiB;AAC5B,MAAI,GAAG,KAAK,EAAE,UAAU,CAAC;AACvB,WAAO,EAAE,UAAU;AACvB;AACA,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,EAAE,QAAQ,IAAI,EAAE;AACxB,MAAI,CAAC;AACH,WAAO;AACT,MAAI,EAAE,CAAC,MAAM;AACX,UAAM,IAAI,MAAM,2DAA2D;AAC7E,SAAO,OAAO,OAAO,CAAC,EAAE,OAAO,IAAI,UAAU,IAAI,EAAE;AACrD;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,SAAS,OAAO,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AACnE,MAAI;AACF,YAAQ,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;AACpC,MAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,OAAO,EAAE,UAAU,UAAU;AAC5E,UAAM,IAAI;AACV,QAAI,IAAI;AACR,WAAO,WAAW;AAChB,aAAO,KAAK,EAAE,SAAS,EAAE,MAAM,KAAG,IAAI,EAAE,MAAM,OAAI,OAAO,EAAE,GAAG,EAAE;AAAA,IAClE;AAAA,EACF;AACA,QAAM,IAAI,UAAU;AAAA,mFAC6D;AACnF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG;AACL,QAAI,OAAO,KAAK;AACd,aAAO,GAAG,GAAG,CAAC;AAChB,QAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,QAAI,MAAM,YAAY,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,MAAM,SAAS,MAAM;AACpF,aAAO,MAAM,KAAK,CAAC;AACrB,QAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AACxE,aAAO,GAAG,GAAG,CAAC;AAAA,EAClB;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,GAAC,KAAK,QAAQ,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;AACvC,MAAE,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAS,GAAG;AAC7B,aAAO,OAAO,yBAAyB,GAAG,CAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG;AACb,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,GAAG,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAS,GAAG;AAC5C,SAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAAA,IACf,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,GAAG;AAC1I,aAAO,eAAe,GAAG,GAAG,OAAO,yBAAyB,GAAG,CAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,KAAK,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACxH;AACA,IAAI,KAAK;AAAA,EACP,iBAAiB,SAAS,GAAG,GAAG,GAAG;AACjC,WAAO,GAAG,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAC9C;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,YAAY,OAAO;AACtF,QAAI,CAAC,EAAE,WAAW,EAAE,OAAO;AACzB,YAAM,IAAI,MAAM,oBAAoB,OAAO,EAAE,OAAO,CAAC;AACvD,MAAE,QAAQ,EAAE,OAAO;AAAA,EACrB,WAAW,EAAE;AACX,MAAE,oBAAoB,EAAE,kBAAkB;AAAA;AAE1C,WAAO,EAAE,SAAS;AACpB,MAAI,IAAI,EAAE,mBAAmB,GAAG,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE,OAAO;AACvE,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,KAAK,IAAI,EAAE,GAAG,EAAE,aAAa,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,GAAG,EAAE,eAAe,KAAK;AAAA,IAC/F,KAAK;AACH,aAAO,KAAK,IAAI,EAAE,GAAG,MAAM,iBAAiB,GAAG,CAAC,GAAG,IAAI,IAAI,OAAO,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,GAAG,EAAE,eAAe,KAAK,IAAI,OAAO,CAAC;AAAA,IACzI,KAAK;AACH,aAAO,IAAI,OAAO,CAAC,EAAE,OAAO,CAAC;AAAA,IAC/B,KAAK;AACH,aAAO,GAAG;AAAA,QACR,QAAQ,IAAI,OAAO,CAAC,EAAE,OAAO,CAAC;AAAA,QAC9B,KAAK,EAAE;AAAA,MACT,CAAC;AAAA,IACH,KAAK;AACH,UAAI,CAAC,EAAE;AACL;AACF,UAAI,IAAI,GAAG,GAAG,EAAE,aAAa,GAAG,EAAE,aAAa,CAAC;AAChD,aAAO,GAAG,GAAG,EAAE,KAAK,GAAG,EAAE,eAAe;AAAA,IAC1C;AACE,YAAM,IAAI,MAAM,0DAA0D,OAAO,GAAG,GAAG,CAAC;AAAA,EAC5F;AACF;AACA,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG;AACxB,MAAI,IAAI,GAAG,EAAE,QAAQ,GAAG,CAAC;AACzB,SAAO,IAAI,GAAG,GAAG,GAAG;AAAA,IAClB,wBAAwB,MAAM;AAAA,IAC9B,oBAAoB,EAAE,EAAE,uDAAuD,KAAK,KAAK,EAAE,mBAAmB;AAAA,IAC9G,aAAa;AAAA,IACb,UAAU;AAAA,EACZ,CAAC,IAAI;AACP;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,WAAS,IAAI,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,QAAQ;AACxC,QAAI,IAAI,EAAE;AACV,QAAI,EAAE,sBAAsB,EAAE,SAAS,GAAG;AACxC,UAAI,IAAI,EAAE,sBAAsB,EAAE,EAAE,sBAAsB,EAAE,SAAS,CAAC;AACtE,UAAI,EAAE,OAAO,CAAC,MAAM;AAClB;AAAA,IACJ;AACA,QAAI,EAAE,GAAG,EAAE,QAAQ,CAAC;AAClB,aAAO;AAAA,EACX;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,SAAO,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI;AAC1B;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACzB,MAAI,IAAI,GAAG,GAAG,EAAE,QAAQ;AACxB,MAAI,MAAM,GAAG;AACX,QAAI,IAAI,EAAE,GAAG,GAAG,YAAY,CAAC;AAC7B,WAAO,MAAM,MAAM,IAAI,MAAM,IAAI;AAAA,EACnC;AACA,MAAI,IAAI,GAAG,GAAG,QAAQ,EAAE,QAAQ;AAChC,MAAI;AACF,WAAO,GAAG,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,MAAM,iBAAiB,CAAC,CAAC;AACjF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAS,GAAG;AAC7B,aAAO,OAAO,yBAAyB,GAAG,CAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG;AACb,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,GAAG,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAS,GAAG;AAC5C,SAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAAA,IACf,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,GAAG;AAC1I,aAAO,eAAe,GAAG,GAAG,OAAO,yBAAyB,GAAG,CAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,KAAK,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACxH;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,EAAE,aAAa;AACjB,UAAM,IAAI,UAAU,mCAAmC;AAC3D;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC;AACX,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,EAAE,KAAK,CAAC;AAAA,EAC9H;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,KAAK,GAAG,EAAE,WAAW,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa,EAAE,UAAU,MAAG,CAAC,GAAG;AAC1G;AACA,IAAI,KAAqB,WAAW;AAClC,WAAS,EAAE,GAAG,GAAG,GAAG;AAClB,QAAI,GAAG,MAAM,CAAC,GAAG,CAAC;AAChB,YAAM,IAAI,UAAU,8CAA8C;AACpE,QAAI,CAAC;AACH,YAAM,IAAI,UAAU,6BAA6B;AACnD,QAAI,CAAC;AACH,YAAM,IAAI,UAAU,uBAAuB;AAC7C,QAAI,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,SAAS,IAAI,EAAE;AACvC,SAAK,UAAU,GAAG,KAAK,qBAAqB,GAAG,KAAK,iBAAiB,GAAG,KAAK,SAAS,MAAM,KAAK,qBAAqB,KAAK,gBAAgB,KAAK,cAAc,WAAW;AACvK,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,GAAG,GAAG,CAAC;AAAA,IACZ,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,WAAK,MAAM;AAAA,IACb;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,KAAK,UAAU,CAAC,KAAK,OAAO,IAAI,GAAG,KAAK,oBAAoB,KAAK,gBAAgB,KAAK,YAAY,CAAC;AAAA,IAC5G;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,GAAG,MAAM;AAAA,QACd,IAAI;AAAA,MACN,GAAG,KAAK,YAAY,CAAC;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,GAAG,MAAM;AAAA,QACd,IAAI;AAAA,MACN,GAAG,KAAK,YAAY,CAAC;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,UAAI,IAAI,IAAI,EAAE,KAAK,YAAY,CAAC;AAChC,aAAO,EAAE,2BAA2B,KAAK,kBAAkB;AAAA,IAC7D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,WAAW,EAAE,UAAU,KAAK,QAAQ,EAAE;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,WAAW;AAChB,aAAO,GAAG,MAAM;AAAA,QACd,IAAI;AAAA,MACN,GAAG,KAAK,YAAY,CAAC;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG,GAAG;AACpB,aAAO,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AAAA,QACvC,IAAI;AAAA,MACN,CAAC,IAAI;AAAA,QACH,IAAI;AAAA,MACN,GAAG,KAAK,YAAY,CAAC;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,OAAO,YAAY,CAAC;AAAA,IAClC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,OAAO,iBAAiB,CAAC;AAAA,IACvC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,GAAG;AACjB,aAAO,KAAK,OAAO,WAAW,CAAC;AAAA,IACjC;AAAA,EACF,CAAC,CAAC,GAAG;AACP,EAAE;AA9FF,IA8FK,KAAK,SAAS,GAAG;AACpB,SAAO,aAAa,KAAK,CAAC;AAC5B;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;AACrB,SAAO,GAAG,CAAC,KAAK,IAAI,GAAG,EAAE,oBAAoB,CAAC,GAAG,IAAI,EAAE,mBAAmB,KAAK,IAAI,GAAG;AAAA,IACpF,SAAS;AAAA,IACT,oBAAoB;AAAA,EACtB;AACF;AACA,IAAI,KAAK,IAAI,OAAO,OAAO,IAAI,IAAI;AACnC,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,MAAI,GAAG;AACL,QAAI,IAAI,IAAI,EAAE,CAAC;AACf,MAAE,oBAAoB,GAAG,CAAC;AAC1B,QAAI,IAAI,IAAI,OAAO,EAAE,UAAU,CAAC;AAChC,QAAI,EAAE,OAAO,CAAC,MAAM,GAAG;AACrB,UAAI,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM;AAChC,UAAI,IAAI,EAAE,MAAM,EAAE;AAClB,UAAI,EAAE,KAAK,EAAE,CAAC,KAAK,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,EAAE,CAAC,MAAM;AACrD,eAAO;AAAA,IACX;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,KAAK,EAAE,cAAc,yBAAyB,GAAG;AACnD,QAAI,IAAI,IAAI,OAAO,SAAS,EAAE,cAAc,yBAAyB,IAAI,GAAG,GAAG,IAAI,EAAE,KAAK,CAAC;AAC3F,QAAI,GAAG;AACL,UAAI,GAAG,GAAG,IAAI,EAAE,SAAS,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC;AAC5C,UAAI,EAAE,4BAA4B,KAAK;AACrC,YAAI,EAAE,QAAQ,GAAG,EAAE,4BAA4B,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE,CAAC;AAAA,WACjE;AACH,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;AAAA,MACtC;AACA,UAAIA;AACJ,UAAI,GAAG;AACL,YAAI,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACzC,cAAM,EAAE,cAAc,eAAe,MAAMA,KAAI,EAAE,cAAc,eAAe;AAAA,MAChF;AACE,QAAAA,KAAI,EAAE,CAAC;AACT,aAAO;AAAA,QACL,gBAAgB;AAAA,QAChB,gBAAgBA;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,gBAAgB;AAAA,EAClB;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,aAAa,IAAI,EAAE;AAC3C,MAAI,MAAM,GAAG;AACX,QAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACb,aAAO;AAAA,QACL,gBAAgB;AAAA,MAClB;AACF,QAAI,EAAE,gBAAgB,KAAK,CAAC,GAAG,GAAG,CAAC;AACjC,aAAO;AAAA,QACL,gBAAgB;AAAA,MAClB;AAAA,EACJ;AACA,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,aAAa;AAAA,EACf;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,EAAE,EAAE,GAAG,EAAE,sBAAsB,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,sBAAsB,CAAC;AAC7E;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,UAAQ,GAAG,GAAG,CAAC,GAAG;AAAA,IAChB,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI;AACvB,MAAI,EAAE,QAAQ,CAAC,MAAM,GAAG;AACtB,QAAI,IAAI,EAAE,CAAC,GAAG,EAAE,oBAAoB,GAAG,CAAC;AACxC,QAAI,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,gBAAgB,IAAI,GAAG,GAAG,CAAC,GAAGA,KAAI,EAAE;AACnF,QAAI,CAAC,EAAEA,IAAG,EAAE,sBAAsB,CAAC,KAAK,EAAE,GAAG,EAAE,sBAAsB,CAAC,KAAK,GAAGA,IAAG,CAAC,MAAM;AACtF,aAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,QAAQ;AAAA,MACV;AAAA,EACJ;AACA,SAAO;AAAA,IACL,QAAQ;AAAA,EACV;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,MAAI,CAAC;AACH,WAAO,CAAC;AACV,MAAI;AACJ,MAAI,EAAE,CAAC,MAAM,KAAK;AAChB,QAAI,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACrB,QAAI,KAAK,MAAM;AACb,UAAI,MAAI,IAAI,MAAM;AAAA,SACf;AACH,UAAI,KAAK,GAAG;AACV,YAAI,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,oBAAoB,IAAI,EAAE;AACxD,YAAI;AACF,iBAAO;AAAA,YACL,0BAA0B;AAAA,YAC1B,oBAAoB;AAAA,YACpB,QAAQ;AAAA,UACV;AAAA,MACJ;AACA,aAAO;AAAA;AAAA;AAAA,QAGL,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACA,MAAI,EAAE,CAAC,MAAM;AACX,WAAO,CAAC;AACV,MAAI,IAAI,EAAE,CAAC;AACX,WAASA,KAAI,GAAGA,KAAI,KAAK,MAAMA,MAAK,EAAE,UAAU;AAC9C,QAAI,IAAI,EAAE,MAAM,GAAGA,EAAC;AACpB,QAAI,EAAE,eAAe,CAAC;AACpB,aAAO,EAAE,oBAAoB,CAAC,GAAG;AAAA,QAC/B,0BAA0B,IAAI,yBAAyB;AAAA,QACvD,oBAAoB;AAAA,QACpB,QAAQ,EAAE,MAAMA,EAAC;AAAA,MACnB;AACF,IAAAA;AAAA,EACF;AACA,SAAO,CAAC;AACV;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,SAAS,OAAO,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AACnE,MAAI;AACF,YAAQ,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;AACpC,MAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,OAAO,EAAE,UAAU,UAAU;AAC5E,UAAM,IAAI;AACV,QAAI,IAAI;AACR,WAAO,WAAW;AAChB,aAAO,KAAK,EAAE,SAAS,EAAE,MAAM,KAAG,IAAI,EAAE,MAAM,OAAI,OAAO,EAAE,GAAG,EAAE;AAAA,IAClE;AAAA,EACF;AACA,QAAM,IAAI,UAAU;AAAA,mFAC6D;AACnF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG;AACL,QAAI,OAAO,KAAK;AACd,aAAO,GAAG,GAAG,CAAC;AAChB,QAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,QAAI,MAAM,YAAY,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,MAAM,SAAS,MAAM;AACpF,aAAO,MAAM,KAAK,CAAC;AACrB,QAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AACxE,aAAO,GAAG,GAAG,CAAC;AAAA,EAClB;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,GAAC,KAAK,QAAQ,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;AACvC,MAAE,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,EAAE,WAAW,IAAI,EAAE,gBAAgB,IAAI,EAAE;AACjD,MAAI,IAAI,EAAE,CAAC;AACX,WAAS,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,QAAQ;AAChD,QAAI,IAAI,EAAE;AACV,QAAI,EAAE,QAAQ,CAAC,GAAG,EAAE,cAAc,GAAG;AACnC,UAAI,KAAK,EAAE,OAAO,EAAE,cAAc,CAAC,MAAM;AACvC,eAAO;AAAA,IACX,WAAW,GAAG;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG,QAAQ,EAAE,QAAQ;AACnB,UAAI,GAAG;AACL,YAAI,MAAM;AACR,iBAAO;AACT,UAAE,KAAK,CAAC;AAAA,MACV;AACE,eAAO;AAAA,EACb;AACA,MAAI,EAAE,SAAS;AACb,WAAO,EAAE,CAAC;AACd;AACA,IAAI,KAAK;AACT,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,EAAE,gBAAgB,IAAI,EAAE,gBAAgB,IAAI,EAAE;AACtD,MAAI,MAAM,EAAE,2BAA2B,CAAC;AACtC,WAAO;AACT,MAAI,IAAI,EAAE,8BAA8B,CAAC;AACzC,MAAI;AACF,WAAO,EAAE,WAAW,IAAI,EAAE,CAAC,IAAI,GAAG,GAAG;AAAA,MACnC,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,UAAU,EAAE;AAAA,IACd,CAAC;AACL;AACA,IAAI,KAAK;AAAT,IAAc,KAAK;AAAnB,IAAsC,KAAK,OAAO,IAAI,OAAO,KAAK;AAAlE,IAAuE,KAAK,QAAQ,KAAK,KAAK,OAAO,IAAI,MAAM,KAAK;AAApH,IAA0H,KAAK,IAAI,OAAO,IAAI,GAAG;AAAjJ,IAAoJ,KAAK;AAAzJ,IAA4J,KAAK,MAAM,KAAK,eAAe,KAAK;AAAhM,IAAuM,KAAK;AAA5M,IAAsN,KAAK,MAAM,KAAK,eAAe,KAAK;AAA1P,IAAiQ,KAAK,OAAO,KAAK,UAAU,KAAK;AAAjS,IAA0S,KAAK,IAAI,OAAO,IAAI,GAAG;AAAjU,IAAoU,KAAK;AAAzU,IAAiV,KAAK;AAAtV,IAAyW,KAAK;AAC9W,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,EAAE,QAAQ,EAAE;AACpB,MAAI,IAAI;AACN,WAAO;AACT,MAAI,IAAI,IAAI,GAAG;AACf,MAAI,KAAK,EAAE;AACT,WAAO;AACT,MAAI,IAAI,EAAE,QAAQ,KAAK,CAAC;AACxB,SAAO,KAAK,IAAI,EAAE,UAAU,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;AACnD;AACA,SAAS,GAAG,GAAG;AACb,SAAO,MAAM,OAAO,OAAK,EAAE,WAAW,IAAI,QAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACxE;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,EAAE,6BAA6B,IAAI,GAAG,CAAC;AAC/C,MAAI,CAAC,GAAG,CAAC;AACP,UAAM,IAAI,EAAE,cAAc;AAC5B,MAAI;AACJ,MAAI,MAAM;AACR,QAAI,EAAE,CAAC,KAAK;AAAA,OACT;AACH,QAAI,IAAI,EAAE,OAAO,CAAC,MAAM,OAAO,KAAK;AACpC,QAAI,IAAI,EAAE,QAAQ,EAAE,GAAG;AACvB,SAAK,IAAI,IAAI,IAAI,GAAG,SAAS,IAAI;AACjC,QAAI,IAAI,EAAE,QAAQ,EAAE;AACpB,SAAK,EAAE,UAAU,GAAG,CAAC;AAAA,EACvB;AACA,MAAI,IAAI,EAAE,QAAQ,EAAE;AACpB,MAAI,IAAI,MAAM,IAAI,EAAE,UAAU,GAAG,CAAC,IAAI,MAAM;AAC1C,WAAO;AACX;AACA,IAAI,KAAK;AAAT,IAAc,KAAK,IAAI,OAAO,MAAM,KAAK,IAAI,GAAG;AAAhD,IAAmD,KAAK,IAAI,OAAO,OAAO,IAAI,MAAM;AACpF,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,kBAAkB,CAAC,EAAE,WAAW,EAAE,cAAc;AAC/E,UAAM,EAAE,KAAK,IAAI,EAAE,iBAAiB,IAAI,IAAI,MAAM,oBAAoB,OAAO,EAAE,cAAc,CAAC;AAChG,MAAI,IAAI,GAAG,GAAG,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE;AAC/D,MAAI,CAAC,GAAG;AACN,QAAI,EAAE;AACJ,YAAM,MAAM,cAAc,IAAI,EAAE,WAAW,IAAI,IAAI,EAAE,cAAc;AACrE,WAAO,CAAC;AAAA,EACV;AACA,MAAI,IAAI,GAAG,GAAG,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,GAAG,IAAI,EAAE,SAASA,KAAI,EAAE,gBAAgB,IAAI,EAAE,oBAAoB,IAAI,EAAE,0BAA0B,IAAI,EAAE;AAC/J,MAAI,CAAC,EAAE,yBAAyB,GAAG;AACjC,QAAI,EAAE;AACJ,YAAM,IAAI,EAAE,iBAAiB;AAC/B,WAAO,CAAC;AAAA,EACV;AACA,MAAI,CAACA,MAAKA,GAAE,SAAS,IAAI;AACvB,QAAI,EAAE;AACJ,YAAM,IAAI,EAAE,WAAW;AACzB,WAAO,CAAC;AAAA,EACV;AACA,MAAIA,GAAE,SAAS,IAAI;AACjB,QAAI,EAAE;AACJ,YAAM,IAAI,EAAE,UAAU;AACxB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,EAAE,IAAI;AACR,QAAI,IAAI,IAAI,GAAG,GAAGA,IAAG,EAAE,QAAQ;AAC/B,WAAO,MAAM,EAAE,UAAU,IAAI,MAAM,EAAE,cAAc,IAAI,MAAM,EAAE,MAAM,IAAI,EAAE,6BAA6B,GAAG;AAAA,EAC7G;AACA,MAAI,KAAK,EAAE,WAAW,EAAE,yBAAyB,IAAI,KAAK,EAAEA,IAAG,EAAE,sBAAsB,CAAC,IAAI;AAC5F,SAAO,EAAE,WAAW;AAAA,IAClB,SAAS;AAAA,IACT,oBAAoB;AAAA,IACpB,aAAa;AAAA,IACb,OAAO;AAAA,IACP,UAAU,IAAI,OAAK,CAAC,EAAE,EAAE,aAAa,QAAM,EAAE,gBAAgB,KAAK,GAAGA,IAAG,CAAC;AAAA,IACzE,OAAOA;AAAA,IACP,KAAK;AAAA,EACP,IAAI,IAAI,GAAG,GAAGA,IAAG,CAAC,IAAI,CAAC;AACzB;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,GAAG;AACL,QAAI,EAAE,SAAS,IAAI;AACjB,UAAI;AACF,cAAM,IAAI,EAAE,UAAU;AACxB;AAAA,IACF;AACA,QAAI,MAAM;AACR,aAAO;AACT,QAAI,IAAI,EAAE,OAAO,EAAE;AACnB,QAAI,EAAE,IAAI;AACR,aAAO,EAAE,MAAM,CAAC,EAAE,QAAQ,IAAI,EAAE;AAAA,EACpC;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI,GAAG,GAAG;AAAA,IACZ,6BAA6B,SAAS,GAAG;AACvC,aAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IACnB;AAAA,EACF,CAAC;AACD,MAAI,CAAC;AACH,WAAO,CAAC;AACV,MAAI,CAAC,GAAG,CAAC;AACP,WAAO,GAAG,CAAC,IAAI;AAAA,MACb,OAAO;AAAA,IACT,IAAI,CAAC;AACP,MAAI,IAAI,GAAG,CAAC;AACZ,SAAO,EAAE,MAAM,IAAI;AAAA,IACjB,QAAQ;AAAA,EACV;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,IAAI;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACA,SAAO,MAAM,EAAE,MAAM,IAAI;AAC3B;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,QAAQ,GAAG,IAAI,EAAE,0BAA0B,IAAI,EAAE,oBAAoB,IAAI,EAAE,QAAQ;AAC7G,MAAI;AACF,MAAE,oBAAoB,CAAC;AAAA,WAChB,MAAM,KAAK;AAClB,MAAE,oBAAoB,GAAG,CAAC,GAAG,MAAM,IAAI,IAAI,IAAI,KAAK,GAAG,GAAG,EAAE,QAAQ;AAAA;AAEpE,WAAO,CAAC;AACV,MAAI,CAAC;AACH,WAAO;AAAA,MACL,0BAA0B;AAAA,MAC1B,oBAAoB;AAAA,IACtB;AACF,MAAIA,KAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAIA,GAAE,gBAAgB,IAAIA,GAAE,aAAa,IAAI,GAAG,GAAG;AAAA,IACvE,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,MAAM,IAAI,GAAG,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI;AAAA,IAChD,SAAS;AAAA,IACT,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,gBAAgB;AAAA,IAChB,aAAa;AAAA,EACf;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAS,GAAG;AAC7B,aAAO,OAAO,yBAAyB,GAAG,CAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG;AACb,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,GAAG,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAS,GAAG;AAC5C,SAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAAA,IACf,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,GAAG;AAC1I,aAAO,eAAe,GAAG,GAAG,OAAO,yBAAyB,GAAG,CAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,KAAK,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACxH;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AAAA,IAC7B,IAAI;AAAA,EACN,CAAC,GAAG,CAAC;AACP;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAS,GAAG;AAC7B,aAAO,OAAO,yBAAyB,GAAG,CAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG;AACb,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,GAAG,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAS,GAAG;AAC5C,SAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAAA,IACf,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,GAAG;AAC1I,aAAO,eAAe,GAAG,GAAG,OAAO,yBAAyB,GAAG,CAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,KAAK,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACxH;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,SAAO,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG;AAC7C;AACA,SAAS,KAAK;AACZ,QAAM,IAAI,UAAU;AAAA,mFAC6D;AACnF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,GAAG;AACL,QAAI,OAAO,KAAK;AACd,aAAO,GAAG,GAAG,CAAC;AAChB,QAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,QAAI,MAAM,YAAY,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,MAAM,SAAS,MAAM;AACpF,aAAO,MAAM,KAAK,CAAC;AACrB,QAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AACxE,aAAO,GAAG,GAAG,CAAC;AAAA,EAClB;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,GAAC,KAAK,QAAQ,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG;AACvC,MAAE,CAAC,IAAI,EAAE,CAAC;AACZ,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,KAAK,OAAO,OAAO,OAAO,SAAS,OAAO,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AACtF,MAAI,KAAK,MAAM;AACb,QAAI,IAAI,CAAC,GAAG,IAAI,MAAI,IAAI,OAAI,GAAG;AAC/B,QAAI;AACF,WAAK,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,WAAW,KAAK,IAAI;AAC/F;AAAA,IACJ,SAAS,GAAG;AACV,UAAI,MAAI,IAAI;AAAA,IACd,UAAE;AACA,UAAI;AACF,SAAC,KAAK,EAAE,UAAU,QAAQ,EAAE,OAAO;AAAA,MACrC,UAAE;AACA,YAAI;AACF,gBAAM;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,GAAG,GAAG;AACb,MAAI,MAAM,QAAQ,CAAC;AACjB,WAAO;AACX;AACA,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,MAAM,UAAU,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,GAAG,GAAGA;AACnG,MAAI,OAAO,KAAK;AACd,QAAI;AAAA;AAEJ,UAAM,IAAI,UAAU,sCAAsC;AAC5D,MAAI,CAAC,KAAK,OAAO,KAAK;AACpB,SAAK,IAAI,GAAGA,KAAI,MAAM,IAAI,QAAQA,KAAI,IAAI,MAAM,IAAI,GAAG;AAAA,MACrD,gBAAgB;AAAA,IAClB,GAAG,CAAC;AAAA,WACG,EAAE,CAAC;AACV,SAAK,IAAI,GAAGA,KAAI,KAAKA,KAAI;AAAA;AAEzB,UAAM,IAAI,MAAM,4BAA4B,OAAO,CAAC,CAAC;AACvD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAUA;AAAA,EACZ;AACF;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAS,GAAG;AAC7B,aAAO,OAAO,yBAAyB,GAAG,CAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG;AACb,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,GAAG,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAS,GAAG;AAC5C,SAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAAA,IACf,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,GAAG;AAC1I,aAAO,eAAe,GAAG,GAAG,OAAO,yBAAyB,GAAG,CAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,KAAK,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACxH;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,OAAK,EAAE,kBAAkB,CAAC,GAAG,EAAE,gBAAgB,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AAAA,IAC1E,gBAAgB;AAAA,EAClB,CAAC;AACD,MAAI;AACF,WAAO,GAAG,GAAG,GAAG,CAAC;AAAA,EACnB,SAAS,GAAG;AACV,QAAI,EAAE,aAAa;AACjB,YAAM;AAAA,EACV;AACF;AACA,SAAS,KAAK;AACZ,MAAI,IAAI,GAAG,SAAS,GAAG,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,IAAI,EAAE;AACxD,SAAO,GAAG,GAAG,GAAG,CAAC;AACnB;AACA,SAAS,KAAK;AACZ,SAAO,GAAG,IAAI,SAAS;AACzB;AACA,IAAM,KAAK;AAAA,EACT,YAAY,GAAG,GAAG,GAAG;AACnB,QAAI,OAAO,EAAE,SAAS,YAAY;AAChC,YAAM,IAAI,EAAE,QAAQ;AACpB,UAAI,IAAI,4CAA4C,EAAE,UAAU;AAChE,YAAM,KAAK,sBAAsB,CAAC,KAAK,QAAQ,KAAK,CAAC;AAAA,IACvD;AACA,MAAE,oBAAoB,SAAS,GAAG;AAChC,YAAM,IAAI,EAAE,eAAe,EAAE,aAAa,IAAI,EAAE;AAChD,YAAM,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,IACzE,GAAG,SAAS,KAAK,iBAAiB,SAAS,EAAE,iBAAiB;AAAA,EAChE;AAAA,EACA,UAAU,GAAG;AACX,aAAS,KAAK,oBAAoB,SAAS,EAAE,iBAAiB;AAAA,EAChE;AACF;AAfA,IAeG,KAAK,CAAC,iBAAiB,UAAU;AAfpC,IAeuC,KAAK,EAAE,OAAO,iBAAiB;AAftE,IAeyE,KAAK;AAAA,EAC5E,KAAK;AAAA,EACL,OAAO;AACT;AAlBA,IAkBG,KAAK,EAAE,OAAO,sBAAsB;AAlBvC,IAkB0C,KAAK;AAAA,EAC7C,KAAK;AAAA,EACL,OAAO;AACT;AArBA,IAqBG,KAAK,CAAC,aAAa;AArBtB,IAqByB,KAAK,CAAC,WAAW,eAAe,eAAe;AArBxE,IAqB2E,KAAK,EAAE,KAAK,EAAE;AArBzF,IAqB4F,KAAK,CAAC,QAAQ,gBAAgB,aAAa,YAAY,MAAM,aAAa,QAAQ,eAAe,YAAY,YAAY,YAAY,SAAS,kBAAkB;AArB5P,IAqB+P,KAAqB,gBAAG;AAAA,EACrR,MAAM;AAAA,EACN,YAAY;AAAA,IACV,cAAc;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,EACR,OAAuB,YAAG;AAAA,IACxB,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,cAAc;AAAA,IACjC;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,YAAY;AAAA,IAC/B;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS,MAAM,EAAE,gBAAgB;AAAA,IACnC;AAAA,IACA,gBAAgB;AAAA;AAAA;AAAA,MAGd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAM,EAAE,gBAAgB;AAAA,IACnC;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,UAAU;AAAA,IAC7B;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,oBAAoB;AAAA,IACvC;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,iBAAiB;AAAA,IACpC;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,kBAAkB;AAAA,IACrC;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,cAAc;AAAA,IACjC;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,YAAY;AAAA,IAC/B;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,MAAM;AAAA,IACzB;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,eAAe;AAAA,IAClC;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,oBAAoB;AAAA,IACvC;AAAA,IACA,qBAAqB;AAAA,MACnB,MAAM;AAAA,MACN,SAAS,MAAM,EAAE,qBAAqB;AAAA,IACxC;AAAA,IACA,cAAc;AAAA,MACZ,MAAM,CAAC,QAAQ,OAAO,MAAM;AAAA,MAC5B,SAAS,MAAM,EAAE,cAAc;AAAA,IACjC;AAAA,EACF,GAAG;AAAA,IACD,YAAY,EAAE,MAAM,OAAO;AAAA,IAC3B,gBAAgB,CAAC;AAAA,EACnB,CAAC;AAAA,EACD,OAAuB,YAAG;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,CAAC,mBAAmB,CAAC;AAAA,EACxB,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,UAAM,IAAI,WAAE,GAAG,IAAI,WAAE,GAAG,IAAI,WAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,SAAG,GAAG,YAAY;AACrE,UAAE,GAAG,CAAC,GAAG,MAAM;AACb,SAAG,IAAIA,GAAE,QAAQ,KAAK,KAAK,SAAG,MAAM;AAClC,QAAAA,GAAE,QAAQ,KAAK,IAAI,GAAG;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AACD,UAAMA,KAAI,SAAG;AAAA,MACX,OAAO;AAAA,MACP,mBAAmB;AAAA,MACnB,MAAM;AAAA,MACN,eAAe;AAAA,MACf,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,MACvB,mBAAmB,EAAE,aAAa;AAAA,MAClC,aAAa;AAAA,IACf,CAAC;AACD,UAAE,MAAMA,GAAE,MAAM,CAAC,MAAM;AACrB,WAAK,GAAG,GAAG,EAAE,MAAM,KAAK,EAAE,OAAO;AAAA,IACnC,CAAC;AACD,UAAM,IAAI,SAAE,MAAM,EAAE,cAAc,SAAS,EAAE,aAAa,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,iBAAiB,SAAS,EAAE,aAAa;AAAA,MACtK,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,iBAAiB,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,iBAAiB,SAAS,EAAE,CAAC,CAAC;AAAA,IAC1F,IAAI,EAAE,YAAY,GAAG,IAAI,SAAE,MAAM,EAAEA,GAAE,iBAAiB,CAAC;AACvD,UAAE,GAAG,CAAC,GAAG,MAAM;AACb,UAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,OAAO;AAC/B,QAAAA,GAAE,oBAAoB,EAAE;AACxB;AAAA,MACF;AACA,WAAK,QAAQ,EAAE,QAAQ,EAAE,mBAAmB,CAAC;AAAA,IAC/C,CAAC;AACD,UAAM,IAAI,SAAE,MAAM;AAChB,UAAI;AACJ,YAAM,IAAI,EAAE,EAAE,IAAI;AAClB,aAAO,MAAM,UAAU,IAAIA,GAAE,UAAU,QAAQ,EAAE,WAAW,GAAG,IAAI,kBAAkB,aAAa,CAAC,YAAY,iBAAiB,SAAS,WAAW,KAAK,EAAE,SAAS,CAAC,IAAI,KAAK,QAAQ,MAAM,8BAA8B,GAAG;AAAA,IAC/N,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,YAAM,IAAI,CAAC,GAAG,EAAE,EAAE,kBAAkB,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,GAAG,WAAW,KAAG,EAAE,GAAG,GAAG,EAAE,KAAK;AACvF,UAAI,CAAC,EAAE,gBAAgB;AACrB,eAAO;AACT,YAAM,IAAIA,GAAE,YAAY,YAAY,EAAE,QAAQ,6CAA6C,EAAE;AAC7F,aAAO,EAAE;AAAA,QACP,CAAC,MAAM,IAAI,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,IAAI,KAAK,IAAI,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,IAAI,KAAK,IAAI,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,QAAQ;AAAA,MACjH;AAAA,IACF,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,UAAI;AACJ,YAAM,IAAIA,GAAE,MAAM,WAAW,GAAG,IAAI,GAAGA,GAAE,KAAK,IAAI,GAAGA,GAAE,OAAOA,GAAE,iBAAiB,GAAG,IAAI;AAAA,QACtF,SAAS,KAAK,OAAO,SAAS,EAAE;AAAA,QAChC,aAAa,KAAK,OAAO,SAAS,EAAE;AAAA,QACpC,WAAWA,GAAE;AAAA,QACb,OAAO,KAAK,OAAO,SAAS,EAAE,QAAQ;AAAA,QACtC,WAAW,IAAI,KAAK,OAAO,SAAS,EAAE,eAAe,OAAO,SAAS,EAAE,KAAK,CAAC;AAAA,QAC7E,gBAAgB,KAAK,OAAO,SAAS,EAAE;AAAA,MACzC;AACA,aAAO,EAAE,UAAU,EAAE,YAAY,KAAK,OAAO,SAAS,EAAE,OAAO,EAAE,EAAE,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,YAAY,EAAE,iBAAiB,UAAU,EAAE,cAAc,WAAW,CAAC,EAAE,EAAE,OAAO,MAAM,EAAE,QAAQ,OAAI,EAAE,WAAW,OAAI,EAAE,UAAU,OAAO,IAAI;AAAA,QACtO,GAAG;AAAA,QACH,GAAG;AAAA,MACL,IAAI;AAAA,IACN,CAAC;AACD,UAAE,MAAM,EAAE,MAAM,aAAa,CAAC,MAAM;AAClC,YAAMA,GAAE,oBAAoB;AAAA,IAC9B,CAAC,GAAG,MAAE,MAAM,EAAE,MAAM,OAAO,MAAM;AAC/B,QAAE,YAAY,EAAE,KAAK;AAAA,IACvB,CAAC,GAAG,MAAE,MAAM,EAAE,MAAM,WAAW,CAAC,MAAM;AACpC,OAAC,EAAE,cAAc,EAAE,mBAAmB,EAAE,CAAC,GAAG,SAAG,MAAM;AACnD,aAAK,CAAC,EAAE,UAAUA,GAAE,QAAQ;AAAA,MAC9B,CAAC;AAAA,IACH,CAAC,GAAG,MAAE,MAAM,EAAE,aAAa,aAAa,CAAC,GAAG,UAAG,MAAM;AACnD,QAAE,UAAUA,GAAE,QAAQ,EAAE,MAAM,KAAK,IAAI,GAAG,GAAG,EAAE,EAAE,KAAK,MAAM;AAC1D,YAAI;AACJ,SAACA,GAAE,WAAW,IAAI,EAAE,iBAAiB,QAAQ,EAAE,iBAAiBA,GAAE,sBAAsBA,GAAE,QAAQ,IAAIA,GAAE,iBAAiB,KAAK,EAAE,YAAY,EAAE,KAAK;AAAA,MACrJ,CAAC,EAAE,MAAM,QAAQ,KAAK,EAAE,KAAK,MAAM;AACjC,QAAAA,GAAE,gBAAgB;AAAA,MACpB,CAAC;AAAA,IACH,CAAC;AACD,aAAS,IAAI;AACX,MAAAA,GAAE,oBAAoB,EAAE,aAAa;AAAA,IACvC;AACA,aAAS,IAAI;AACX,aAAO,IAAI,QAAQ,CAAC,MAAM;AACxB,YAAI;AACJ,cAAM,IAAIA,GAAE,UAAU,OAAO,SAAS,EAAE,CAAC,OAAO,KAAK;AACnD,YAAE;AACF;AAAA,QACF;AACA,YAAI,EAAE,gBAAgB;AACpB,cAAI,OAAO,EAAE,kBAAkB,UAAU;AACvC,cAAE,EAAE,cAAc,GAAG,EAAE;AACvB;AAAA,UACF;AACA,cAAI,OAAO,EAAE,kBAAkB,UAAU;AACvC,kBAAM,IAAI,GAAG,EAAE,cAAc;AAC7B,gBAAI,GAAG;AACL,gBAAE,EAAE,IAAI,GAAG,EAAE;AACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,cAAM,IAAI,EAAE,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC;AAC9C,UAAE,qBAAqB,GAAG,EAAE,KAAK,CAAC,MAAM;AACtC,YAAE,KAAKA,GAAE,iBAAiB;AAAA,QAC5B,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,kBAAQ,KAAK,CAAC,GAAG,EAAE,CAAC;AAAA,QACtB,CAAC,EAAE,KAAK,MAAM;AACZ,YAAE;AAAA,QACJ,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE;AAAA,MAChB,CAAC;AAAA,IACH;AACA,aAAS,EAAE,IAAI,CAAC,GAAG;AACjB,aAAO,EAAE,IAAI,CAAC,EAAE,OAAO,OAAO;AAAA,IAChC;AACA,aAAS,EAAE,IAAI,IAAI;AACjB,aAAO,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAAA,IAC5C;AACA,aAAS,GAAG,GAAG;AACb,aAAO,EAAE,MAAM,KAAK,CAAC,MAAM,OAAO,EAAE,QAAQ,MAAM,CAAC;AAAA,IACrD;AACA,aAAS,GAAG,GAAG,GAAG;AAChB,YAAM,IAAIA,GAAE,kBAAkB,GAAG,IAAI,MAAM,EAAE,mBAAmB,SAAS,GAAG,IAAI,EAAE,mBAAmB,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC;AAC3H,aAAO;AAAA,QACL,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,WAAW;AAAA,MACb;AAAA,IACF;AACA,aAAS,EAAE,GAAG;AACZ,UAAI,GAAG,GAAG;AACV,UAAI,IAAI;AACR,UAAI,OAAO,KAAK,aAAa,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG;AAC3C,cAAM,IAAIA,GAAE,UAAU,OAAO,SAAS,EAAE,CAAC,OAAO,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB;AACvF,UAAAA,GAAE,oBAAoB,EAAE,MAAMA,GAAE,UAAU,IAAI;AAAA,YAC5C,EAAE,MAAM;AAAA,YACR,EAAE;AAAA,UACJ,MAAM,OAAO,SAAS,EAAE,oBAAoB,MAAM;AAClD;AAAA,QACF;AACA,aAAK,IAAI,EAAE,iBAAiB,QAAQ,EAAE,gBAAgB,GAAG;AACvD,UAAAA,GAAE,QAAQ,IAAI,EAAE,QAAQ,IAAIA,GAAE,oBAAoB,EAAE;AACpD;AAAA,QACF;AACA,QAAAA,GAAE,oBAAoB,EAAE,MAAM,EAAEA,GAAE,KAAK;AAAA,MACzC;AAAA,IACF;AACA,aAAS,KAAK;AACZ,YAAM,IAAIA,GAAE;AACZ,UAAI,EAAE,qBAAqB;AACzB,cAAM,IAAIA,GAAE,MAAM,MAAM,gBAAgB;AACxC,QAAAA,GAAE,QAAQ,EAAE,KAAK,EAAE;AAAA,MACrB;AACA,UAAI,EAAE,kBAAkB,EAAE,0BAA0B,QAAQ;AAC1D,cAAM,IAAIA,GAAE,MAAM,MAAM,EAAE,cAAc;AACxC,QAAAA,GAAE,QAAQ,EAAE,KAAK,EAAE;AAAA,MACrB;AACA,YAAMA,GAAE,SAAS,EAAEA,GAAE,KAAK;AAAA,IAC5B;AACA,aAAS,KAAK;AACZ,aAAO,EAAE,uBAAuB,CAAC,kBAAkB,KAAKA,GAAE,KAAK,IAAI,QAAK,EAAE,iBAAiB,GAAG,IAAI;AAAA,IACpG;AACA,aAAS,KAAK;AACZ,aAAO,EAAE,0BAA0B,SAAS,EAAE,eAAe,KAAKA,GAAE,KAAK,IAAI;AAAA,IAC/E;AACA,aAAS,KAAK;AACZ,UAAI;AACJ,OAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,kBAAkB,EAAE,MAAM,QAAQ,KAAK,EAAE,UAAU,GAAG,EAAEA,GAAE,KAAK;AAAA,IAC5F;AACA,aAAS,EAAE,GAAG;AACZ,QAAE,QAAQ,GAAG,EAAE,YAAY,GAAG,EAAE,OAAO,EAAE,KAAK;AAAA,IAChD;AACA,aAAS,GAAG,GAAG;AACb,QAAE,QAAQ,CAAC;AAAA,IACb;AACA,aAAS,GAAG,GAAG;AACb,SAAG,EAAE,OAAOA,GAAE,MAAM,MAAM,GAAG,EAAE,SAAS,CAAC;AAAA,IAC3C;AACA,aAAS,GAAG,GAAG;AACb,QAAE,SAAS,CAAC;AAAA,IACd;AACA,aAAS,GAAG,GAAG;AACb,QAAE,SAAS,CAAC;AAAA,IACd;AACA,aAAS,KAAK;AACZ,UAAI;AACJ,OAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,MAAM;AAAA,IACnC;AACA,aAAS,KAAK;AACZ,UAAI;AACJ,OAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,KAAK;AAAA,IAClC;AACA,aAAS,KAAK;AACZ,QAAE,YAAY,EAAE,gBAAgB,aAAaA,GAAE,cAAc,IAAIA,GAAE,OAAO,CAACA,GAAE;AAAA,IAC/E;AACA,aAAS,KAAK;AACZ,MAAAA,GAAE,OAAO;AAAA,IACX;AACA,aAAS,GAAG,GAAG;AACb,UAAI,EAAE,YAAY,IAAI;AACpB,UAAE,eAAe,GAAGA,GAAE,OAAO,MAAIA,GAAE,kBAAkB,OAAOA,GAAE,gBAAgB,IAAIA,GAAE,gBAAgB,KAAK,IAAI,EAAE,MAAM,SAAS,GAAGA,GAAE,gBAAgB,CAAC;AACpJ,cAAM,IAAI,EAAE,MAAM,SAASA,GAAE,aAAa;AAC1C,UAAE,MAAM,GAAG,EAAE,YAAY,EAAE,eAAe,EAAE,MAAM,YAAY,EAAE,MAAM,iBAAiB,EAAE,MAAM,YAAY,EAAE,YAAY,EAAE,MAAM,eAAe,EAAE;AAAA,MACpJ,WAAW,EAAE,YAAY,IAAI;AAC3B,UAAE,eAAe,GAAGA,GAAE,OAAO,MAAIA,GAAE,kBAAkB,OAAOA,GAAE,gBAAgB,EAAE,MAAM,SAAS,IAAIA,GAAE,gBAAgB,KAAK,IAAI,GAAGA,GAAE,gBAAgB,CAAC;AACpJ,cAAM,IAAI,EAAE,MAAM,SAASA,GAAE,aAAa;AAC1C,UAAE,MAAM,GAAG,EAAE,YAAY,EAAE,MAAM,cAAc,EAAE,MAAM,YAAY,EAAE;AAAA,MACvE,WAAW,EAAE,YAAY;AACvB,QAAAA,GAAE,kBAAkB,QAAQ,EAAE,EAAE,MAAMA,GAAE,aAAa,CAAC,GAAGA,GAAE,OAAO,CAACA,GAAE;AAAA,eAC9DA,GAAE,MAAM;AACf,QAAAA,GAAE,mBAAmB,EAAE,KAAK,aAAaA,GAAE,eAAe,GAAGA,GAAE,kBAAkB,WAAW,MAAM;AAChG,UAAAA,GAAE,kBAAkB;AAAA,QACtB,GAAG,GAAG;AACN,cAAM,IAAI,EAAE,MAAM,MAAM,EAAE,mBAAmB,MAAM,EAAE,UAAU,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,WAAWA,GAAE,eAAe,CAAC;AAC7G,YAAI,KAAK,GAAG;AACV,UAAAA,GAAE,gBAAgB,EAAE,mBAAmB,SAAS;AAChD,gBAAM,IAAI,EAAE,MAAM,SAASA,GAAE,aAAa,GAAG,IAAI,EAAE,YAAY,EAAE,MAAM,WAAW,IAAI,EAAE,YAAY,EAAE,eAAe,EAAE,MAAM,YAAY,EAAE,MAAM;AACjJ,WAAC,KAAK,OAAO,EAAE,MAAM,YAAY,EAAE,YAAY,EAAE,MAAM,eAAe;AAAA,QACxE;AAAA,MACF;AAAA,IACF;AACA,aAAS,KAAK;AACZ,MAAAA,GAAE,gBAAgB,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQA,GAAE,iBAAiB,GAAGA,GAAE,OAAO;AAAA,IACtF;AACA,aAAS,KAAK;AACZ,aAAO,cAAc,EAAE,MAAM,sBAAsB,EAAE,SAAS,MAAMA,GAAE,wBAAwB,UAAUA,GAAE,wBAAwB;AAAA,IACpI;AACA,WAAO,EAAE;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,GAAG,MAAM;AACZ,YAAM,IAAI,iBAAG,eAAe;AAC5B,aAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,QACnB,SAAS;AAAA,QACT,KAAK;AAAA,QACL,OAAO,eAAE,CAAC,iBAAiB,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAAA,MACtE,GAAG;AAAA,QACD,gBAAG,UAAE,GAAG,mBAAE,OAAO;AAAA,UACf,cAAc;AAAA,UACd,iBAAiB;AAAA,UACjB,iBAAiBA,GAAE;AAAA,UACnB,MAAM;AAAA,UACN,OAAO,eAAE,CAAC,iBAAiB,EAAE,MAAMA,GAAE,MAAM,UAAU,EAAE,gBAAgB,SAAS,CAAC,CAAC;AAAA,UAClF,UAAU,EAAE,gBAAgB;AAAA,UAC5B,WAAW;AAAA,YACT;AAAA,YACA,SAAE,IAAI,CAAC,OAAO,CAAC;AAAA,YACf,SAAE,IAAI,CAAC,KAAK,CAAC;AAAA,YACb,SAAE,IAAI,CAAC,KAAK,CAAC;AAAA,UACf;AAAA,UACA,SAAS;AAAA,QACX,GAAG;AAAA,UACD,gBAAE,QAAQ,IAAI;AAAA,YACZ,EAAE,gBAAgB,aAAa,UAAE,GAAG,mBAAE,QAAQ;AAAA,cAC5C,KAAK;AAAA,cACL,OAAO,eAAE,CAAC,aAAa,MAAG,CAAC,EAAEA,GAAE,iBAAiB,CAAC,CAAC;AAAA,YACpD,GAAG,MAAM,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YACvB,EAAE,gBAAgB,2BAA2B,UAAE,GAAG,mBAAE,QAAQ,IAAI,OAAO,gBAAE,EAAE,SAAS,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YACrH,WAAG,EAAE,QAAQ,cAAc;AAAA,cACzB,MAAMA,GAAE;AAAA,YACV,GAAG,MAAM;AAAA,cACP,gBAAE,QAAQ,IAAI,gBAAEA,GAAE,OAAO,MAAM,GAAG,GAAG,CAAC;AAAA,YACxC,CAAC;AAAA,UACH,CAAC;AAAA,UACDA,GAAE,QAAQ,UAAE,GAAG,mBAAE,MAAM;AAAA,YACrB,KAAK;AAAA,YACL,SAAS;AAAA,YACT,KAAK;AAAA,YACL,OAAO,eAAE,CAAC,sBAAsBA,GAAE,qBAAqB,CAAC;AAAA,YACxD,MAAM;AAAA,UACR,GAAG;AAAA,YACD,EAAE,gBAAgB,iBAAiB,UAAE,GAAG,mBAAE,OAAO,IAAI;AAAA,cACnD,WAAG,EAAE,QAAQ,aAAa;AAAA,cAC1B,eAAE,gBAAE,SAAS;AAAA,gBACX,OAAO,eAAE,CAAC,cAAc,iBAAiB,CAAC;AAAA,gBAC1C,cAAc;AAAA,gBACd,aAAa,EAAE,gBAAgB,yBAAyB,EAAE,MAAM,SAAS,EAAE,MAAM,CAAC,EAAE,OAAO;AAAA,gBAC3F,MAAM;AAAA,gBACN,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAMA,GAAE,cAAc;AAAA,gBAC9D,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,cAAG,MAAM;AAAA,gBAClC,GAAG,CAAC,MAAM,CAAC;AAAA,cACb,GAAG,MAAM,GAAG,EAAE,GAAG;AAAA,gBACf,CAAC,YAAIA,GAAE,WAAW;AAAA,cACpB,CAAC;AAAA,YACH,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,aACb,UAAE,IAAE,GAAG,mBAAE,UAAI,MAAM,WAAG,EAAE,OAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,MAAM;AAAA,cACtD,MAAM;AAAA,cACN,OAAO,eAAE,CAAC,sBAAsB,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;AAAA,cAC9C,KAAK,EAAE,QAAQ,EAAE,YAAY,eAAe;AAAA,cAC5C,UAAU;AAAA,cACV,SAAS,CAAC,MAAM,EAAE,CAAC;AAAA,cACnB,aAAa,CAAC,MAAMA,GAAE,gBAAgB;AAAA,cACtC,iBAAiBA,GAAE,sBAAsB,EAAE,QAAQ,CAAC,EAAE;AAAA,YACxD,GAAG;AAAA,cACD,EAAE,gBAAgB,aAAa,UAAE,GAAG,mBAAE,QAAQ;AAAA,gBAC5C,KAAK;AAAA,gBACL,OAAO,eAAE,CAAC,aAAa,MAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAAA,cACvC,GAAG,MAAM,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,cACvB,gBAAE,UAAU,MAAM,gBAAE,EAAE,IAAI,GAAG,CAAC;AAAA,cAC9B,EAAE,gBAAgB,sBAAsB,UAAE,GAAG,mBAAE,QAAQ,IAAI,OAAO,gBAAE,EAAE,QAAQ,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YACjG,GAAG,IAAI,EAAE,EAAE,GAAG,GAAG;AAAA,UACnB,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACnB,GAAG,IAAI,EAAE,IAAI;AAAA,UACX,CAAC,GAAG,EAAE;AAAA,QACR,CAAC;AAAA,QACD,eAAE,gBAAE,SAAS;AAAA,UACX,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAMA,GAAE,QAAQ;AAAA,UACxD,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,EAAE,aAAa;AAAA,UACrB,cAAc,EAAE,aAAa;AAAA,UAC7B,WAAW,EAAE,aAAa;AAAA,UAC1B,OAAO,eAAE,CAAC,cAAc,cAAc,EAAE,aAAa,YAAY,CAAC;AAAA,UAClE,UAAU,EAAE;AAAA,UACZ,IAAI,EAAE,aAAa;AAAA,UACnB,WAAW,EAAE,aAAa;AAAA,UAC1B,MAAM,EAAE,aAAa;AAAA,UACrB,aAAaA,GAAE;AAAA,UACf,UAAU,EAAE,aAAa;AAAA,UACzB,UAAU,EAAE,aAAa;AAAA,UACzB,UAAU,EAAE,aAAa;AAAA,UACzB,OAAO,EAAE;AAAA,UACT,oBAAoB,EAAE,aAAa,kBAAkB;AAAA,UACrD,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,YACP,SAAE,IAAI,CAAC,OAAO,CAAC;AAAA,YACf,SAAE,IAAI,CAAC,OAAO,CAAC;AAAA,UACjB;AAAA,QACF,GAAG,MAAM,IAAI,EAAE,GAAG;AAAA,UAChB,CAAC,eAAIA,GAAE,KAAK;AAAA,QACd,CAAC;AAAA,QACD,WAAG,EAAE,QAAQ,YAAY;AAAA,MAC3B,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AACF,CAAC;;;AChpHD,IAAMC,MAAqB,gBAAE;AAAA,EAC3B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,IACtE;AAAA,EACF;AAAA,EACA,OAAO,CAAC,SAAS,QAAQ,cAAc,SAAS,aAAa;AAAA,EAC7D,MAAM,GAAG,EAAE,QAAQC,IAAG,MAAM,EAAE,GAAG;AAC/B,QAAIC,IAAGC;AACP,UAAM,EAAE,GAAG,EAAE,IAAI,QAAG,GAAG,IAAI;AAAA,MACzB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,eAAe;AAAA,MACf,sBAAsB,EAAE,aAAa;AAAA,MACrC,yBAAyB;AAAA,IAC3B,GAAG,IAAI,GAAG,EAAE,OAAO,GAAG,MAAM,GAAG,UAAU,GAAG,UAAU,GAAG,cAAc,GAAG,SAAS,GAAG,aAAaC,IAAG,cAAc,GAAG,iBAAiB,EAAE,IAAI,OAAE,CAAC,GAAG,IAAI,SAAE,GAAGC,KAAI,IAAE,EAAE,MAAM,GAAG,UAAU,GAAG,UAAU,GAAG,cAAc,GAAG,SAAS,GAAG,aAAaD,IAAG,cAAc,GAAG,UAAU,EAAE,SAAS,CAAC,GAAGE,KAAI,IAAE,EAAE,GAAG,GAAG,GAAG,KAAK,OAAO,SAAS,EAAE,MAAM,CAAC,GAAG,IAAI,MAAIJ,KAAI,EAAE,MAAM,MAAM,SAAS,MAAM,OAAO,SAASA,GAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,MAAIC,KAAI,EAAE,UAAU,OAAO,SAASA,GAAE,CAAC,OAAO,MAAM,EAAE,QAAQ,EAAE,QAAQ,IAAI,EAAE,KAAK,KAAK,EAAE,GAAG,IAAI,IAAE,EAAE,GAAGI,KAAI,IAAE,EAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,KAAE,GAAGC,KAAI,IAAE,EAAE,GAAGC,KAAI,IAAE,MAAM,GAAG,IAAI,IAAE,MAAM,GAAGC,KAAI;AAC9mB,UAAE,GAAG,CAAC,MAAM;AACV,YAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,GAAG,EAAE;AAAA,IACpC,CAAC;AACD,aAAS,IAAI;AACX,UAAI;AACJ,QAAE,QAAQ,MAAIA,GAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,UAAU,IAAID,GAAE,UAAU,QAAQ,EAAE,MAAM;AAAA,IAC7F;AACA,aAASE,KAAI;AACX,UAAI;AACJ,QAAE,QAAQ,OAAID,GAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,IAAID,GAAE,UAAU,QAAQ,EAAE,KAAK;AAAA,IAChF;AACA,aAAS,EAAE,GAAG,GAAG;AACf,YAAM,EAAE,oBAAoB,GAAG,gBAAgBG,IAAG,WAAW,EAAE,IAAI;AACnE,QAAE,QAAQ,GAAG,KAAK,EAAE,GAAGA,MAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,KAAK,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,KAAK,IAAIL,GAAE,QAAQ,GAAGG,GAAE,SAAS,EAAE,QAAQD,GAAE,MAAM,GAAG,EAAE,KAAK;AAAA,IAC9L;AACA,aAASI,KAAI;AACX,aAAO,EAAE,UAAUL,GAAE,QAAQ,EAAE,QAAQ,KAAK,YAAY,EAAE;AAAA,IAC5D;AACA,aAASM,KAAI;AACX,QAAE,QAAQ,MAAI,EAAE;AAAA,IAClB;AACA,aAAS,IAAI;AACX,QAAE,QAAQ,OAAIH,GAAE;AAAA,IAClB;AACA,WAAOV,GAAE;AAAA,MACP,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAaM;AAAA,MACb,OAAO;AAAA,MACP,MAAMI;AAAA,MACN,eAAeE;AAAA,MACf,mBAAmBL;AAAA,IACrB,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,OAAO;AAAA,MACP,SAAS;AAAA,MACT,KAAK;AAAA,IACP,GAAG;AAAA,MACD,YAAE,MAAE,EAAE,GAAG;AAAA,QACP,SAAS;AAAA,QACT,KAAKC;AAAA,QACL,UAAU,EAAE;AAAA,QACZ,MAAM,MAAE,CAAC;AAAA,QACT,YAAY,EAAE;AAAA,QACd,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,QACxD,MAAM;AAAA,QACN,gBAAgB,EAAE;AAAA,QAClB,cAAcJ,GAAE;AAAA,QAChB,iBAAiBC,GAAE;AAAA,QACnB,oBAAoB,EAAE;AAAA,QACtB,SAAS;AAAA,QACT,QAAQK;AAAA,QACR,WAAW;AAAA,QACX,QAAQG;AAAA,QACR,SAAS;AAAA,MACX,GAAG;AAAA,QACD,cAAc,QAAE,MAAM;AAAA,UACpB,YAAE,GAAG;AAAA,YACH,MAAM,EAAE,QAAQ,eAAe;AAAA,YAC/B,OAAO;AAAA,UACT,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;AAAA,QACtB,CAAC;AAAA,QACD,eAAe,QAAE,MAAM;AAAA,UACrB,YAAE,GAAG;AAAA,YACH,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,QACH,CAAC;AAAA,QACD,GAAG;AAAA,MACL,GAAG,GAAG,CAAC,YAAY,QAAQ,cAAc,kBAAkB,gBAAgB,mBAAmB,oBAAoB,CAAC;AAAA,IACrH,GAAG,GAAG;AAAA,EACR;AACF,CAAC;", "names": ["i", "ue", "k", "O", "B", "U", "L", "M", "w", "I", "i", "v", "m", "H", "W", "z"]}