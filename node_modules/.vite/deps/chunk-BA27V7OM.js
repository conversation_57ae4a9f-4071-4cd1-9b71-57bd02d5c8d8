// node_modules/@perkd/applet-common/dist/utilsColor.js
var rgbToRgbo = (color) => {
  const regex = /rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(0|1|0?\.\d+)\s*)?\)/;
  const match = color == null ? void 0 : color.match(regex);
  if (!match)
    return { r: 0, g: 0, b: 0, o: 1 };
  return {
    r: parseInt(match[1]),
    g: parseInt(match[2]),
    b: parseInt(match[3]),
    o: match[4] ? parseFloat(match[4]) : 1
  };
};
var hexToRgbo = (color) => {
  const validHexColor = color && color.match(/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/);
  if (!validHexColor)
    return { r: 0, g: 0, b: 0, o: 1 };
  const c = color.length > 3 ? color : "#" + color.slice(1, color.length).replace(/(.{1})/g, "$1$1");
  const r = parseInt(c.slice(1, 3), 16), g = parseInt(c.slice(3, 5), 16), b = parseInt(c.slice(5, 7), 16), o = c.slice(7, 9) ? Math.round(parseInt(c.slice(7, 9), 16) * 100 / 255) / 100 : 1;
  return { r, g, b, o };
};
var colorToRgbo = (color) => {
  return color.startsWith("#") ? hexToRgbo(color) : rgbToRgbo(color);
};
function rgbToHsl(r, g, b) {
  r /= 255;
  g /= 255;
  b /= 255;
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0;
  const l = (max + min) / 2;
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }
  return { h: h * 360, s, l };
}
var getContrastColor = (color) => {
  const { r, g, b } = typeof color === "string" ? colorToRgbo(color) : color;
  const sRGBtoLin = (colorChannel) => colorChannel <= 0.03928 ? colorChannel / 12.92 : Math.pow((colorChannel + 0.055) / 1.055, 2.4);
  const rgbToY = (r2, g2, b2) => 0.2126 * sRGBtoLin(r2) + 0.7152 * sRGBtoLin(g2) + 0.0722 * sRGBtoLin(b2);
  const luminance = rgbToY(r / 255, g / 255, b / 255);
  const hsl = rgbToHsl(r, g, b);
  if (hsl.h >= 90 && hsl.h <= 150 && hsl.l < 0.3) {
    return "#FFFFFF";
  }
  if (hsl.h > 40 && hsl.h < 70 && hsl.l >= 0.5) {
    return luminance < 0.6 ? "#FFFFFF" : "#000000";
  }
  if (hsl.h >= 0 && hsl.h <= 15 && hsl.s > 0.6 || hsl.h > 15 && hsl.h <= 40 && hsl.s > 0.7) {
    return luminance < 0.55 ? "#FFFFFF" : "#000000";
  }
  if (hsl.h >= 90 && hsl.h <= 150 && hsl.s > 0.6) {
    return luminance < 0.55 ? "#FFFFFF" : "#000000";
  }
  if (hsl.h > 150 && hsl.h < 190) {
    return luminance < 0.48 ? "#FFFFFF" : "#000000";
  }
  if (hsl.h >= 270 && hsl.h <= 330 && hsl.s > 0.4) {
    return luminance < 0.42 ? "#FFFFFF" : "#000000";
  }
  if (hsl.h > 40 && hsl.h < 70 && hsl.l < 0.5 || hsl.h >= 70 && hsl.h < 90 && hsl.s > 0.5 || hsl.h >= 90 && hsl.h <= 150 && hsl.s <= 0.6 || hsl.h >= 190 && hsl.h <= 250 && hsl.s > 0.6 || hsl.s < 0.1 || hsl.h >= 20 && hsl.h <= 40 && hsl.s >= 0.4 && hsl.s <= 0.6 && hsl.l <= 0.6) {
    return luminance < 0.45 ? "#FFFFFF" : "#000000";
  }
  const whiteContrast = (1 + 0.05) / (luminance + 0.05);
  const blackContrast = (luminance + 0.05) / (0 + 0.05);
  return whiteContrast > blackContrast ? "#FFFFFF" : "#000000";
};

export {
  hexToRgbo,
  colorToRgbo,
  getContrastColor
};
//# sourceMappingURL=chunk-BA27V7OM.js.map
