{"version": 3, "sources": ["../../jsbarcode/bin/barcodes/Barcode.js", "../../jsbarcode/bin/barcodes/CODE39/index.js", "../../jsbarcode/bin/barcodes/CODE128/constants.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128.js", "../../jsbarcode/bin/barcodes/CODE128/auto.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128_AUTO.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128A.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128B.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128C.js", "../../jsbarcode/bin/barcodes/CODE128/index.js", "../../jsbarcode/bin/barcodes/EAN_UPC/constants.js", "../../jsbarcode/bin/barcodes/EAN_UPC/encoder.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN13.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN8.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN5.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN2.js", "../../jsbarcode/bin/barcodes/EAN_UPC/UPC.js", "../../jsbarcode/bin/barcodes/EAN_UPC/UPCE.js", "../../jsbarcode/bin/barcodes/EAN_UPC/index.js", "../../jsbarcode/bin/barcodes/ITF/constants.js", "../../jsbarcode/bin/barcodes/ITF/ITF.js", "../../jsbarcode/bin/barcodes/ITF/ITF14.js", "../../jsbarcode/bin/barcodes/ITF/index.js", "../../jsbarcode/bin/barcodes/MSI/MSI.js", "../../jsbarcode/bin/barcodes/MSI/checksums.js", "../../jsbarcode/bin/barcodes/MSI/MSI10.js", "../../jsbarcode/bin/barcodes/MSI/MSI11.js", "../../jsbarcode/bin/barcodes/MSI/MSI1010.js", "../../jsbarcode/bin/barcodes/MSI/MSI1110.js", "../../jsbarcode/bin/barcodes/MSI/index.js", "../../jsbarcode/bin/barcodes/pharmacode/index.js", "../../jsbarcode/bin/barcodes/codabar/index.js", "../../jsbarcode/bin/barcodes/GenericBarcode/index.js", "../../jsbarcode/bin/barcodes/index.js", "../../jsbarcode/bin/help/merge.js", "../../jsbarcode/bin/help/linearizeEncodings.js", "../../jsbarcode/bin/help/fixOptions.js", "../../jsbarcode/bin/help/optionsFromStrings.js", "../../jsbarcode/bin/options/defaults.js", "../../jsbarcode/bin/help/getOptionsFromElement.js", "../../jsbarcode/bin/renderers/shared.js", "../../jsbarcode/bin/renderers/canvas.js", "../../jsbarcode/bin/renderers/svg.js", "../../jsbarcode/bin/renderers/object.js", "../../jsbarcode/bin/renderers/index.js", "../../jsbarcode/bin/exceptions/exceptions.js", "../../jsbarcode/bin/help/getRenderProperties.js", "../../jsbarcode/bin/exceptions/ErrorHandler.js", "../../jsbarcode/bin/JsBarcode.js", "../../qrcode/lib/can-promise.js", "../../qrcode/lib/core/utils.js", "../../qrcode/lib/core/error-correction-level.js", "../../qrcode/lib/core/bit-buffer.js", "../../qrcode/lib/core/bit-matrix.js", "../../qrcode/lib/core/alignment-pattern.js", "../../qrcode/lib/core/finder-pattern.js", "../../qrcode/lib/core/mask-pattern.js", "../../qrcode/lib/core/error-correction-code.js", "../../qrcode/lib/core/galois-field.js", "../../qrcode/lib/core/polynomial.js", "../../qrcode/lib/core/reed-solomon-encoder.js", "../../qrcode/lib/core/version-check.js", "../../qrcode/lib/core/regex.js", "../../qrcode/lib/core/mode.js", "../../qrcode/lib/core/version.js", "../../qrcode/lib/core/format-info.js", "../../qrcode/lib/core/numeric-data.js", "../../qrcode/lib/core/alphanumeric-data.js", "../../qrcode/lib/core/byte-data.js", "../../qrcode/lib/core/kanji-data.js", "../../dijkstrajs/dijkstra.js", "../../qrcode/lib/core/segments.js", "../../qrcode/lib/core/qrcode.js", "../../qrcode/lib/renderer/utils.js", "../../qrcode/lib/renderer/canvas.js", "../../qrcode/lib/renderer/svg-tag.js", "../../qrcode/lib/browser.js", "../../@perkd/vue-components/dist/components/UISlideButton.js", "../../@perkd/vue-components/dist/components/UIField.js", "../../@perkd/vue-components/dist/components/UIInputDate.js", "../../@perkd/vue-components/dist/components/UIInput.js", "../../@perkd/vue-components/dist/components/UISwitch.js", "../../@perkd/vue-components/dist/components/UIDialog.js", "../../@perkd/vue-components/dist/components/UISelection.js", "../../@perkd/vue-components/dist/components/UICard.js", "../../@perkd/vue-components/dist/components/UICardOverlay.js", "../../@perkd/vue-components/dist/components/UICircleProgress.js", "../../@perkd/vue-components/dist/components/UIModalBox.js", "../../@perkd/vue-components/dist/components/CountDown.js", "../../@perkd/vue-components/dist/components/UISwipeout.js", "../../@perkd/vue-components/dist/components/UIMarquee.js", "../../@perkd/vue-components/dist/composables/useLoading.js", "../../@perkd/vue-components/dist/composables/useStorage.js", "../../@perkd/applet-common/dist/types/actions.js", "../../@perkd/applet-common/dist/websocket.js", "../../@perkd/vue-components/dist/composables/useWebsocket.js", "../../@perkd/vue-components/dist/translation.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Barcode = function Barcode(data, options) {\n\t_classCallCheck(this, Barcode);\n\n\tthis.data = data;\n\tthis.text = options.text || data;\n\tthis.options = options;\n};\n\nexports.default = Barcode;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.CODE39 = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Barcode2 = require(\"../Barcode.js\");\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// https://en.wikipedia.org/wiki/Code_39#Encoding\n\nvar CODE39 = function (_Barcode) {\n\t_inherits(CODE39, _Barcode);\n\n\tfunction CODE39(data, options) {\n\t\t_classCallCheck(this, CODE39);\n\n\t\tdata = data.toUpperCase();\n\n\t\t// Calculate mod43 checksum if enabled\n\t\tif (options.mod43) {\n\t\t\tdata += getCharacter(mod43checksum(data));\n\t\t}\n\n\t\treturn _possibleConstructorReturn(this, (CODE39.__proto__ || Object.getPrototypeOf(CODE39)).call(this, data, options));\n\t}\n\n\t_createClass(CODE39, [{\n\t\tkey: \"encode\",\n\t\tvalue: function encode() {\n\t\t\t// First character is always a *\n\t\t\tvar result = getEncoding(\"*\");\n\n\t\t\t// Take every character and add the binary representation to the result\n\t\t\tfor (var i = 0; i < this.data.length; i++) {\n\t\t\t\tresult += getEncoding(this.data[i]) + \"0\";\n\t\t\t}\n\n\t\t\t// Last character is always a *\n\t\t\tresult += getEncoding(\"*\");\n\n\t\t\treturn {\n\t\t\t\tdata: result,\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}, {\n\t\tkey: \"valid\",\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9A-Z\\-\\.\\ \\$\\/\\+\\%]+$/) !== -1;\n\t\t}\n\t}]);\n\n\treturn CODE39;\n}(_Barcode3.default);\n\n// All characters. The position in the array is the (checksum) value\n\n\nvar characters = [\"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"-\", \".\", \" \", \"$\", \"/\", \"+\", \"%\", \"*\"];\n\n// The decimal representation of the characters, is converted to the\n// corresponding binary with the getEncoding function\nvar encodings = [20957, 29783, 23639, 30485, 20951, 29813, 23669, 20855, 29789, 23645, 29975, 23831, 30533, 22295, 30149, 24005, 21623, 29981, 23837, 22301, 30023, 23879, 30545, 22343, 30161, 24017, 21959, 30065, 23921, 22385, 29015, 18263, 29141, 17879, 29045, 18293, 17783, 29021, 18269, 17477, 17489, 17681, 20753, 35770];\n\n// Get the binary representation of a character by converting the encodings\n// from decimal to binary\nfunction getEncoding(character) {\n\treturn getBinary(characterValue(character));\n}\n\nfunction getBinary(characterValue) {\n\treturn encodings[characterValue].toString(2);\n}\n\nfunction getCharacter(characterValue) {\n\treturn characters[characterValue];\n}\n\nfunction characterValue(character) {\n\treturn characters.indexOf(character);\n}\n\nfunction mod43checksum(data) {\n\tvar checksum = 0;\n\tfor (var i = 0; i < data.length; i++) {\n\t\tchecksum += characterValue(data[i]);\n\t}\n\n\tchecksum = checksum % 43;\n\treturn checksum;\n}\n\nexports.CODE39 = CODE39;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _SET_BY_CODE;\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n// constants for internal usage\nvar SET_A = exports.SET_A = 0;\nvar SET_B = exports.SET_B = 1;\nvar SET_C = exports.SET_C = 2;\n\n// Special characters\nvar SHIFT = exports.SHIFT = 98;\nvar START_A = exports.START_A = 103;\nvar START_B = exports.START_B = 104;\nvar START_C = exports.START_C = 105;\nvar MODULO = exports.MODULO = 103;\nvar STOP = exports.STOP = 106;\nvar FNC1 = exports.FNC1 = 207;\n\n// Get set by start code\nvar SET_BY_CODE = exports.SET_BY_CODE = (_SET_BY_CODE = {}, _defineProperty(_SET_BY_CODE, START_A, SET_A), _defineProperty(_SET_BY_CODE, START_B, SET_B), _defineProperty(_SET_BY_CODE, START_C, SET_C), _SET_BY_CODE);\n\n// Get next set by code\nvar SWAP = exports.SWAP = {\n\t101: SET_A,\n\t100: SET_B,\n\t99: SET_C\n};\n\nvar A_START_CHAR = exports.A_START_CHAR = String.fromCharCode(208); // START_A + 105\nvar B_START_CHAR = exports.B_START_CHAR = String.fromCharCode(209); // START_B + 105\nvar C_START_CHAR = exports.C_START_CHAR = String.fromCharCode(210); // START_C + 105\n\n// 128A (Code Set A)\n// ASCII characters 00 to 95 (0–9, A–Z and control codes), special characters, and FNC 1–4\nvar A_CHARS = exports.A_CHARS = \"[\\x00-\\x5F\\xC8-\\xCF]\";\n\n// 128B (Code Set B)\n// ASCII characters 32 to 127 (0–9, A–Z, a–z), special characters, and FNC 1–4\nvar B_CHARS = exports.B_CHARS = \"[\\x20-\\x7F\\xC8-\\xCF]\";\n\n// 128C (Code Set C)\n// 00–99 (encodes two digits with a single code point) and FNC1\nvar C_CHARS = exports.C_CHARS = \"(\\xCF*[0-9]{2}\\xCF*)\";\n\n// CODE128 includes 107 symbols:\n// 103 data symbols, 3 start symbols (A, B and C), and 1 stop symbol (the last one)\n// Each symbol consist of three black bars (1) and three white spaces (0).\nvar BARS = exports.BARS = [11011001100, 11001101100, 11001100110, 10010011000, 10010001100, 10001001100, 10011001000, 10011000100, 10001100100, 11001001000, 11001000100, 11000100100, 10110011100, 10011011100, 10011001110, 10111001100, 10011101100, 10011100110, 11001110010, 11001011100, 11001001110, 11011100100, 11001110100, 11101101110, 11101001100, 11100101100, 11100100110, 11101100100, 11100110100, 11100110010, 11011011000, 11011000110, 11000110110, 10100011000, 10001011000, 10001000110, 10110001000, 10001101000, 10001100010, 11010001000, 11000101000, 11000100010, 10110111000, 10110001110, 10001101110, 10111011000, 10111000110, 10001110110, 11101110110, 11010001110, 11000101110, 11011101000, 11011100010, 11011101110, 11101011000, 11101000110, 11100010110, 11101101000, 11101100010, 11100011010, 11101111010, 11001000010, 11110001010, 10100110000, 10100001100, 10010110000, 10010000110, 10000101100, 10000100110, 10110010000, 10110000100, 10011010000, 10011000010, 10000110100, 10000110010, 11000010010, 11001010000, 11110111010, 11000010100, 10001111010, 10100111100, 10010111100, 10010011110, 10111100100, 10011110100, 10011110010, 11110100100, 11110010100, 11110010010, 11011011110, 11011110110, 11110110110, 10101111000, 10100011110, 10001011110, 10111101000, 10111100010, 11110101000, 11110100010, 10111011110, 10111101110, 11101011110, 11110101110, 11010000100, 11010010000, 11010011100, 1100011101011];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Barcode2 = require('../Barcode.js');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nvar _constants = require('./constants');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n// This is the master class,\n// it does require the start code to be included in the string\nvar CODE128 = function (_Barcode) {\n\t_inherits(CODE128, _Barcode);\n\n\tfunction CODE128(data, options) {\n\t\t_classCallCheck(this, CODE128);\n\n\t\t// Get array of ascii codes from data\n\t\tvar _this = _possibleConstructorReturn(this, (CODE128.__proto__ || Object.getPrototypeOf(CODE128)).call(this, data.substring(1), options));\n\n\t\t_this.bytes = data.split('').map(function (char) {\n\t\t\treturn char.charCodeAt(0);\n\t\t});\n\t\treturn _this;\n\t}\n\n\t_createClass(CODE128, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\t// ASCII value ranges 0-127, 200-211\n\t\t\treturn (/^[\\x00-\\x7F\\xC8-\\xD3]+$/.test(this.data)\n\t\t\t);\n\t\t}\n\n\t\t// The public encoding function\n\n\t}, {\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\tvar bytes = this.bytes;\n\t\t\t// Remove the start code from the bytes and set its index\n\t\t\tvar startIndex = bytes.shift() - 105;\n\t\t\t// Get start set by index\n\t\t\tvar startSet = _constants.SET_BY_CODE[startIndex];\n\n\t\t\tif (startSet === undefined) {\n\t\t\t\tthrow new RangeError('The encoding does not start with a start character.');\n\t\t\t}\n\n\t\t\tif (this.shouldEncodeAsEan128() === true) {\n\t\t\t\tbytes.unshift(_constants.FNC1);\n\t\t\t}\n\n\t\t\t// Start encode with the right type\n\t\t\tvar encodingResult = CODE128.next(bytes, 1, startSet);\n\n\t\t\treturn {\n\t\t\t\ttext: this.text === this.data ? this.text.replace(/[^\\x20-\\x7E]/g, '') : this.text,\n\t\t\t\tdata:\n\t\t\t\t// Add the start bits\n\t\t\t\tCODE128.getBar(startIndex) +\n\t\t\t\t// Add the encoded bits\n\t\t\t\tencodingResult.result +\n\t\t\t\t// Add the checksum\n\t\t\t\tCODE128.getBar((encodingResult.checksum + startIndex) % _constants.MODULO) +\n\t\t\t\t// Add the end bits\n\t\t\t\tCODE128.getBar(_constants.STOP)\n\t\t\t};\n\t\t}\n\n\t\t// GS1-128/EAN-128\n\n\t}, {\n\t\tkey: 'shouldEncodeAsEan128',\n\t\tvalue: function shouldEncodeAsEan128() {\n\t\t\tvar isEAN128 = this.options.ean128 || false;\n\t\t\tif (typeof isEAN128 === 'string') {\n\t\t\t\tisEAN128 = isEAN128.toLowerCase() === 'true';\n\t\t\t}\n\t\t\treturn isEAN128;\n\t\t}\n\n\t\t// Get a bar symbol by index\n\n\t}], [{\n\t\tkey: 'getBar',\n\t\tvalue: function getBar(index) {\n\t\t\treturn _constants.BARS[index] ? _constants.BARS[index].toString() : '';\n\t\t}\n\n\t\t// Correct an index by a set and shift it from the bytes array\n\n\t}, {\n\t\tkey: 'correctIndex',\n\t\tvalue: function correctIndex(bytes, set) {\n\t\t\tif (set === _constants.SET_A) {\n\t\t\t\tvar charCode = bytes.shift();\n\t\t\t\treturn charCode < 32 ? charCode + 64 : charCode - 32;\n\t\t\t} else if (set === _constants.SET_B) {\n\t\t\t\treturn bytes.shift() - 32;\n\t\t\t} else {\n\t\t\t\treturn (bytes.shift() - 48) * 10 + bytes.shift() - 48;\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: 'next',\n\t\tvalue: function next(bytes, pos, set) {\n\t\t\tif (!bytes.length) {\n\t\t\t\treturn { result: '', checksum: 0 };\n\t\t\t}\n\n\t\t\tvar nextCode = void 0,\n\t\t\t    index = void 0;\n\n\t\t\t// Special characters\n\t\t\tif (bytes[0] >= 200) {\n\t\t\t\tindex = bytes.shift() - 105;\n\t\t\t\tvar nextSet = _constants.SWAP[index];\n\n\t\t\t\t// Swap to other set\n\t\t\t\tif (nextSet !== undefined) {\n\t\t\t\t\tnextCode = CODE128.next(bytes, pos + 1, nextSet);\n\t\t\t\t}\n\t\t\t\t// Continue on current set but encode a special character\n\t\t\t\telse {\n\t\t\t\t\t\t// Shift\n\t\t\t\t\t\tif ((set === _constants.SET_A || set === _constants.SET_B) && index === _constants.SHIFT) {\n\t\t\t\t\t\t\t// Convert the next character so that is encoded correctly\n\t\t\t\t\t\t\tbytes[0] = set === _constants.SET_A ? bytes[0] > 95 ? bytes[0] - 96 : bytes[0] : bytes[0] < 32 ? bytes[0] + 96 : bytes[0];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnextCode = CODE128.next(bytes, pos + 1, set);\n\t\t\t\t\t}\n\t\t\t}\n\t\t\t// Continue encoding\n\t\t\telse {\n\t\t\t\t\tindex = CODE128.correctIndex(bytes, set);\n\t\t\t\t\tnextCode = CODE128.next(bytes, pos + 1, set);\n\t\t\t\t}\n\n\t\t\t// Get the correct binary encoding and calculate the weight\n\t\t\tvar enc = CODE128.getBar(index);\n\t\t\tvar weight = index * pos;\n\n\t\t\treturn {\n\t\t\t\tresult: enc + nextCode.result,\n\t\t\t\tchecksum: weight + nextCode.checksum\n\t\t\t};\n\t\t}\n\t}]);\n\n\treturn CODE128;\n}(_Barcode3.default);\n\nexports.default = CODE128;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _constants = require('./constants');\n\n// Match Set functions\nvar matchSetALength = function matchSetALength(string) {\n\treturn string.match(new RegExp('^' + _constants.A_CHARS + '*'))[0].length;\n};\nvar matchSetBLength = function matchSetBLength(string) {\n\treturn string.match(new RegExp('^' + _constants.B_CHARS + '*'))[0].length;\n};\nvar matchSetC = function matchSetC(string) {\n\treturn string.match(new RegExp('^' + _constants.C_CHARS + '*'))[0];\n};\n\n// CODE128A or CODE128B\nfunction autoSelectFromAB(string, isA) {\n\tvar ranges = isA ? _constants.A_CHARS : _constants.B_CHARS;\n\tvar untilC = string.match(new RegExp('^(' + ranges + '+?)(([0-9]{2}){2,})([^0-9]|$)'));\n\n\tif (untilC) {\n\t\treturn untilC[1] + String.fromCharCode(204) + autoSelectFromC(string.substring(untilC[1].length));\n\t}\n\n\tvar chars = string.match(new RegExp('^' + ranges + '+'))[0];\n\n\tif (chars.length === string.length) {\n\t\treturn string;\n\t}\n\n\treturn chars + String.fromCharCode(isA ? 205 : 206) + autoSelectFromAB(string.substring(chars.length), !isA);\n}\n\n// CODE128C\nfunction autoSelectFromC(string) {\n\tvar cMatch = matchSetC(string);\n\tvar length = cMatch.length;\n\n\tif (length === string.length) {\n\t\treturn string;\n\t}\n\n\tstring = string.substring(length);\n\n\t// Select A/B depending on the longest match\n\tvar isA = matchSetALength(string) >= matchSetBLength(string);\n\treturn cMatch + String.fromCharCode(isA ? 206 : 205) + autoSelectFromAB(string, isA);\n}\n\n// Detect Code Set (A, B or C) and format the string\n\nexports.default = function (string) {\n\tvar newString = void 0;\n\tvar cLength = matchSetC(string).length;\n\n\t// Select 128C if the string start with enough digits\n\tif (cLength >= 2) {\n\t\tnewString = _constants.C_START_CHAR + autoSelectFromC(string);\n\t} else {\n\t\t// Select A/B depending on the longest match\n\t\tvar isA = matchSetALength(string) > matchSetBLength(string);\n\t\tnewString = (isA ? _constants.A_START_CHAR : _constants.B_START_CHAR) + autoSelectFromAB(string, isA);\n\t}\n\n\treturn newString.replace(/[\\xCD\\xCE]([^])[\\xCD\\xCE]/, // Any sequence between 205 and 206 characters\n\tfunction (match, char) {\n\t\treturn String.fromCharCode(203) + char;\n\t});\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _CODE2 = require('./CODE128');\n\nvar _CODE3 = _interopRequireDefault(_CODE2);\n\nvar _auto = require('./auto');\n\nvar _auto2 = _interopRequireDefault(_auto);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar CODE128AUTO = function (_CODE) {\n\t_inherits(CODE128AUTO, _CODE);\n\n\tfunction CODE128AUTO(data, options) {\n\t\t_classCallCheck(this, CODE128AUTO);\n\n\t\t// ASCII value ranges 0-127, 200-211\n\t\tif (/^[\\x00-\\x7F\\xC8-\\xD3]+$/.test(data)) {\n\t\t\tvar _this = _possibleConstructorReturn(this, (CODE128AUTO.__proto__ || Object.getPrototypeOf(CODE128AUTO)).call(this, (0, _auto2.default)(data), options));\n\t\t} else {\n\t\t\tvar _this = _possibleConstructorReturn(this, (CODE128AUTO.__proto__ || Object.getPrototypeOf(CODE128AUTO)).call(this, data, options));\n\t\t}\n\t\treturn _possibleConstructorReturn(_this);\n\t}\n\n\treturn CODE128AUTO;\n}(_CODE3.default);\n\nexports.default = CODE128AUTO;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _CODE2 = require('./CODE128.js');\n\nvar _CODE3 = _interopRequireDefault(_CODE2);\n\nvar _constants = require('./constants');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar CODE128A = function (_CODE) {\n\t_inherits(CODE128A, _CODE);\n\n\tfunction CODE128A(string, options) {\n\t\t_classCallCheck(this, CODE128A);\n\n\t\treturn _possibleConstructorReturn(this, (CODE128A.__proto__ || Object.getPrototypeOf(CODE128A)).call(this, _constants.A_START_CHAR + string, options));\n\t}\n\n\t_createClass(CODE128A, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn new RegExp('^' + _constants.A_CHARS + '+$').test(this.data);\n\t\t}\n\t}]);\n\n\treturn CODE128A;\n}(_CODE3.default);\n\nexports.default = CODE128A;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _CODE2 = require('./CODE128.js');\n\nvar _CODE3 = _interopRequireDefault(_CODE2);\n\nvar _constants = require('./constants');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar CODE128B = function (_CODE) {\n\t_inherits(CODE128B, _CODE);\n\n\tfunction CODE128B(string, options) {\n\t\t_classCallCheck(this, CODE128B);\n\n\t\treturn _possibleConstructorReturn(this, (CODE128B.__proto__ || Object.getPrototypeOf(CODE128B)).call(this, _constants.B_START_CHAR + string, options));\n\t}\n\n\t_createClass(CODE128B, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn new RegExp('^' + _constants.B_CHARS + '+$').test(this.data);\n\t\t}\n\t}]);\n\n\treturn CODE128B;\n}(_CODE3.default);\n\nexports.default = CODE128B;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _CODE2 = require('./CODE128.js');\n\nvar _CODE3 = _interopRequireDefault(_CODE2);\n\nvar _constants = require('./constants');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar CODE128C = function (_CODE) {\n\t_inherits(CODE128C, _CODE);\n\n\tfunction CODE128C(string, options) {\n\t\t_classCallCheck(this, CODE128C);\n\n\t\treturn _possibleConstructorReturn(this, (CODE128C.__proto__ || Object.getPrototypeOf(CODE128C)).call(this, _constants.C_START_CHAR + string, options));\n\t}\n\n\t_createClass(CODE128C, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn new RegExp('^' + _constants.C_CHARS + '+$').test(this.data);\n\t\t}\n\t}]);\n\n\treturn CODE128C;\n}(_CODE3.default);\n\nexports.default = CODE128C;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CODE128C = exports.CODE128B = exports.CODE128A = exports.CODE128 = undefined;\n\nvar _CODE128_AUTO = require('./CODE128_AUTO.js');\n\nvar _CODE128_AUTO2 = _interopRequireDefault(_CODE128_AUTO);\n\nvar _CODE128A = require('./CODE128A.js');\n\nvar _CODE128A2 = _interopRequireDefault(_CODE128A);\n\nvar _CODE128B = require('./CODE128B.js');\n\nvar _CODE128B2 = _interopRequireDefault(_CODE128B);\n\nvar _CODE128C = require('./CODE128C.js');\n\nvar _CODE128C2 = _interopRequireDefault(_CODE128C);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.CODE128 = _CODE128_AUTO2.default;\nexports.CODE128A = _CODE128A2.default;\nexports.CODE128B = _CODE128B2.default;\nexports.CODE128C = _CODE128C2.default;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n// Standard start end and middle bits\nvar SIDE_BIN = exports.SIDE_BIN = '101';\nvar MIDDLE_BIN = exports.MIDDLE_BIN = '01010';\n\nvar BINARIES = exports.BINARIES = {\n\t'L': [// The L (left) type of encoding\n\t'0001101', '0011001', '0010011', '0111101', '0100011', '0110001', '0101111', '0111011', '0110111', '0001011'],\n\t'G': [// The G type of encoding\n\t'0100111', '0110011', '0011011', '0100001', '0011101', '0111001', '0000101', '0010001', '0001001', '0010111'],\n\t'R': [// The R (right) type of encoding\n\t'1110010', '1100110', '1101100', '1000010', '1011100', '1001110', '1010000', '1000100', '1001000', '1110100'],\n\t'O': [// The O (odd) encoding for UPC-E\n\t'0001101', '0011001', '0010011', '0111101', '0100011', '0110001', '0101111', '0111011', '0110111', '0001011'],\n\t'E': [// The E (even) encoding for UPC-E\n\t'0100111', '0110011', '0011011', '0100001', '0011101', '0111001', '0000101', '0010001', '0001001', '0010111']\n};\n\n// Define the EAN-2 structure\nvar EAN2_STRUCTURE = exports.EAN2_STRUCTURE = ['LL', 'LG', 'GL', 'GG'];\n\n// Define the EAN-5 structure\nvar EAN5_STRUCTURE = exports.EAN5_STRUCTURE = ['GGLLL', 'GLGLL', 'GLLGL', 'GLLLG', 'LGGLL', 'LLGGL', 'LLLGG', 'LGLGL', 'LGLLG', 'LLGLG'];\n\n// Define the EAN-13 structure\nvar EAN13_STRUCTURE = exports.EAN13_STRUCTURE = ['LLLLLL', 'LLGLGG', 'LLGGLG', 'LLGGGL', 'LGLLGG', 'LGGLLG', 'LGGGLL', 'LGLGLG', 'LGLGGL', 'LGGLGL'];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _constants = require('./constants');\n\n// Encode data string\nvar encode = function encode(data, structure, separator) {\n\tvar encoded = data.split('').map(function (val, idx) {\n\t\treturn _constants.BINARIES[structure[idx]];\n\t}).map(function (val, idx) {\n\t\treturn val ? val[data[idx]] : '';\n\t});\n\n\tif (separator) {\n\t\tvar last = data.length - 1;\n\t\tencoded = encoded.map(function (val, idx) {\n\t\t\treturn idx < last ? val + separator : val;\n\t\t});\n\t}\n\n\treturn encoded.join('');\n};\n\nexports.default = encode;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _constants = require('./constants');\n\nvar _encoder = require('./encoder');\n\nvar _encoder2 = _interopRequireDefault(_encoder);\n\nvar _Barcode2 = require('../Barcode');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n// Base class for EAN8 & EAN13\nvar EAN = function (_Barcode) {\n\t_inherits(EAN, _Barcode);\n\n\tfunction EAN(data, options) {\n\t\t_classCallCheck(this, EAN);\n\n\t\t// Make sure the font is not bigger than the space between the guard bars\n\t\tvar _this = _possibleConstructorReturn(this, (EAN.__proto__ || Object.getPrototypeOf(EAN)).call(this, data, options));\n\n\t\t_this.fontSize = !options.flat && options.fontSize > options.width * 10 ? options.width * 10 : options.fontSize;\n\n\t\t// Make the guard bars go down half the way of the text\n\t\t_this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;\n\t\treturn _this;\n\t}\n\n\t_createClass(EAN, [{\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\treturn this.options.flat ? this.encodeFlat() : this.encodeGuarded();\n\t\t}\n\t}, {\n\t\tkey: 'leftText',\n\t\tvalue: function leftText(from, to) {\n\t\t\treturn this.text.substr(from, to);\n\t\t}\n\t}, {\n\t\tkey: 'leftEncode',\n\t\tvalue: function leftEncode(data, structure) {\n\t\t\treturn (0, _encoder2.default)(data, structure);\n\t\t}\n\t}, {\n\t\tkey: 'rightText',\n\t\tvalue: function rightText(from, to) {\n\t\t\treturn this.text.substr(from, to);\n\t\t}\n\t}, {\n\t\tkey: 'rightEncode',\n\t\tvalue: function rightEncode(data, structure) {\n\t\t\treturn (0, _encoder2.default)(data, structure);\n\t\t}\n\t}, {\n\t\tkey: 'encodeGuarded',\n\t\tvalue: function encodeGuarded() {\n\t\t\tvar textOptions = { fontSize: this.fontSize };\n\t\t\tvar guardOptions = { height: this.guardHeight };\n\n\t\t\treturn [{ data: _constants.SIDE_BIN, options: guardOptions }, { data: this.leftEncode(), text: this.leftText(), options: textOptions }, { data: _constants.MIDDLE_BIN, options: guardOptions }, { data: this.rightEncode(), text: this.rightText(), options: textOptions }, { data: _constants.SIDE_BIN, options: guardOptions }];\n\t\t}\n\t}, {\n\t\tkey: 'encodeFlat',\n\t\tvalue: function encodeFlat() {\n\t\t\tvar data = [_constants.SIDE_BIN, this.leftEncode(), _constants.MIDDLE_BIN, this.rightEncode(), _constants.SIDE_BIN];\n\n\t\t\treturn {\n\t\t\t\tdata: data.join(''),\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}]);\n\n\treturn EAN;\n}(_Barcode3.default);\n\nexports.default = EAN;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if (\"value\" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };\n\nvar _constants = require('./constants');\n\nvar _EAN2 = require('./EAN');\n\nvar _EAN3 = _interopRequireDefault(_EAN2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// https://en.wikipedia.org/wiki/International_Article_Number_(EAN)#Binary_encoding_of_data_digits_into_EAN-13_barcode\n\n// Calculate the checksum digit\n// https://en.wikipedia.org/wiki/International_Article_Number_(EAN)#Calculation_of_checksum_digit\nvar checksum = function checksum(number) {\n\tvar res = number.substr(0, 12).split('').map(function (n) {\n\t\treturn +n;\n\t}).reduce(function (sum, a, idx) {\n\t\treturn idx % 2 ? sum + a * 3 : sum + a;\n\t}, 0);\n\n\treturn (10 - res % 10) % 10;\n};\n\nvar EAN13 = function (_EAN) {\n\t_inherits(EAN13, _EAN);\n\n\tfunction EAN13(data, options) {\n\t\t_classCallCheck(this, EAN13);\n\n\t\t// Add checksum if it does not exist\n\t\tif (data.search(/^[0-9]{12}$/) !== -1) {\n\t\t\tdata += checksum(data);\n\t\t}\n\n\t\t// Adds a last character to the end of the barcode\n\t\tvar _this = _possibleConstructorReturn(this, (EAN13.__proto__ || Object.getPrototypeOf(EAN13)).call(this, data, options));\n\n\t\t_this.lastChar = options.lastChar;\n\t\treturn _this;\n\t}\n\n\t_createClass(EAN13, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{13}$/) !== -1 && +this.data[12] === checksum(this.data);\n\t\t}\n\t}, {\n\t\tkey: 'leftText',\n\t\tvalue: function leftText() {\n\t\t\treturn _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'leftText', this).call(this, 1, 6);\n\t\t}\n\t}, {\n\t\tkey: 'leftEncode',\n\t\tvalue: function leftEncode() {\n\t\t\tvar data = this.data.substr(1, 6);\n\t\t\tvar structure = _constants.EAN13_STRUCTURE[this.data[0]];\n\t\t\treturn _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'leftEncode', this).call(this, data, structure);\n\t\t}\n\t}, {\n\t\tkey: 'rightText',\n\t\tvalue: function rightText() {\n\t\t\treturn _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'rightText', this).call(this, 7, 6);\n\t\t}\n\t}, {\n\t\tkey: 'rightEncode',\n\t\tvalue: function rightEncode() {\n\t\t\tvar data = this.data.substr(7, 6);\n\t\t\treturn _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'rightEncode', this).call(this, data, 'RRRRRR');\n\t\t}\n\n\t\t// The \"standard\" way of printing EAN13 barcodes with guard bars\n\n\t}, {\n\t\tkey: 'encodeGuarded',\n\t\tvalue: function encodeGuarded() {\n\t\t\tvar data = _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'encodeGuarded', this).call(this);\n\n\t\t\t// Extend data with left digit & last character\n\t\t\tif (this.options.displayValue) {\n\t\t\t\tdata.unshift({\n\t\t\t\t\tdata: '000000000000',\n\t\t\t\t\ttext: this.text.substr(0, 1),\n\t\t\t\t\toptions: { textAlign: 'left', fontSize: this.fontSize }\n\t\t\t\t});\n\n\t\t\t\tif (this.options.lastChar) {\n\t\t\t\t\tdata.push({\n\t\t\t\t\t\tdata: '00'\n\t\t\t\t\t});\n\t\t\t\t\tdata.push({\n\t\t\t\t\t\tdata: '00000',\n\t\t\t\t\t\ttext: this.options.lastChar,\n\t\t\t\t\t\toptions: { fontSize: this.fontSize }\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn data;\n\t\t}\n\t}]);\n\n\treturn EAN13;\n}(_EAN3.default);\n\nexports.default = EAN13;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if (\"value\" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };\n\nvar _EAN2 = require('./EAN');\n\nvar _EAN3 = _interopRequireDefault(_EAN2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// http://www.barcodeisland.com/ean8.phtml\n\n// Calculate the checksum digit\nvar checksum = function checksum(number) {\n\tvar res = number.substr(0, 7).split('').map(function (n) {\n\t\treturn +n;\n\t}).reduce(function (sum, a, idx) {\n\t\treturn idx % 2 ? sum + a : sum + a * 3;\n\t}, 0);\n\n\treturn (10 - res % 10) % 10;\n};\n\nvar EAN8 = function (_EAN) {\n\t_inherits(EAN8, _EAN);\n\n\tfunction EAN8(data, options) {\n\t\t_classCallCheck(this, EAN8);\n\n\t\t// Add checksum if it does not exist\n\t\tif (data.search(/^[0-9]{7}$/) !== -1) {\n\t\t\tdata += checksum(data);\n\t\t}\n\n\t\treturn _possibleConstructorReturn(this, (EAN8.__proto__ || Object.getPrototypeOf(EAN8)).call(this, data, options));\n\t}\n\n\t_createClass(EAN8, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{8}$/) !== -1 && +this.data[7] === checksum(this.data);\n\t\t}\n\t}, {\n\t\tkey: 'leftText',\n\t\tvalue: function leftText() {\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'leftText', this).call(this, 0, 4);\n\t\t}\n\t}, {\n\t\tkey: 'leftEncode',\n\t\tvalue: function leftEncode() {\n\t\t\tvar data = this.data.substr(0, 4);\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'leftEncode', this).call(this, data, 'LLLL');\n\t\t}\n\t}, {\n\t\tkey: 'rightText',\n\t\tvalue: function rightText() {\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'rightText', this).call(this, 4, 4);\n\t\t}\n\t}, {\n\t\tkey: 'rightEncode',\n\t\tvalue: function rightEncode() {\n\t\t\tvar data = this.data.substr(4, 4);\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'rightEncode', this).call(this, data, 'RRRR');\n\t\t}\n\t}]);\n\n\treturn EAN8;\n}(_EAN3.default);\n\nexports.default = EAN8;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _constants = require('./constants');\n\nvar _encoder = require('./encoder');\n\nvar _encoder2 = _interopRequireDefault(_encoder);\n\nvar _Barcode2 = require('../Barcode');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// https://en.wikipedia.org/wiki/EAN_5#Encoding\n\nvar checksum = function checksum(data) {\n\tvar result = data.split('').map(function (n) {\n\t\treturn +n;\n\t}).reduce(function (sum, a, idx) {\n\t\treturn idx % 2 ? sum + a * 9 : sum + a * 3;\n\t}, 0);\n\treturn result % 10;\n};\n\nvar EAN5 = function (_Barcode) {\n\t_inherits(EAN5, _Barcode);\n\n\tfunction EAN5(data, options) {\n\t\t_classCallCheck(this, EAN5);\n\n\t\treturn _possibleConstructorReturn(this, (EAN5.__proto__ || Object.getPrototypeOf(EAN5)).call(this, data, options));\n\t}\n\n\t_createClass(EAN5, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{5}$/) !== -1;\n\t\t}\n\t}, {\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\tvar structure = _constants.EAN5_STRUCTURE[checksum(this.data)];\n\t\t\treturn {\n\t\t\t\tdata: '1011' + (0, _encoder2.default)(this.data, structure, '01'),\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}]);\n\n\treturn EAN5;\n}(_Barcode3.default);\n\nexports.default = EAN5;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _constants = require('./constants');\n\nvar _encoder = require('./encoder');\n\nvar _encoder2 = _interopRequireDefault(_encoder);\n\nvar _Barcode2 = require('../Barcode');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// https://en.wikipedia.org/wiki/EAN_2#Encoding\n\nvar EAN2 = function (_Barcode) {\n\t_inherits(EAN2, _Barcode);\n\n\tfunction EAN2(data, options) {\n\t\t_classCallCheck(this, EAN2);\n\n\t\treturn _possibleConstructorReturn(this, (EAN2.__proto__ || Object.getPrototypeOf(EAN2)).call(this, data, options));\n\t}\n\n\t_createClass(EAN2, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{2}$/) !== -1;\n\t\t}\n\t}, {\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\t// Choose the structure based on the number mod 4\n\t\t\tvar structure = _constants.EAN2_STRUCTURE[parseInt(this.data) % 4];\n\t\t\treturn {\n\t\t\t\t// Start bits + Encode the two digits with 01 in between\n\t\t\t\tdata: '1011' + (0, _encoder2.default)(this.data, structure, '01'),\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}]);\n\n\treturn EAN2;\n}(_Barcode3.default);\n\nexports.default = EAN2;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nexports.checksum = checksum;\n\nvar _encoder = require(\"./encoder\");\n\nvar _encoder2 = _interopRequireDefault(_encoder);\n\nvar _Barcode2 = require(\"../Barcode.js\");\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// https://en.wikipedia.org/wiki/Universal_Product_Code#Encoding\n\nvar UPC = function (_Barcode) {\n\t_inherits(UPC, _Barcode);\n\n\tfunction UPC(data, options) {\n\t\t_classCallCheck(this, UPC);\n\n\t\t// Add checksum if it does not exist\n\t\tif (data.search(/^[0-9]{11}$/) !== -1) {\n\t\t\tdata += checksum(data);\n\t\t}\n\n\t\tvar _this = _possibleConstructorReturn(this, (UPC.__proto__ || Object.getPrototypeOf(UPC)).call(this, data, options));\n\n\t\t_this.displayValue = options.displayValue;\n\n\t\t// Make sure the font is not bigger than the space between the guard bars\n\t\tif (options.fontSize > options.width * 10) {\n\t\t\t_this.fontSize = options.width * 10;\n\t\t} else {\n\t\t\t_this.fontSize = options.fontSize;\n\t\t}\n\n\t\t// Make the guard bars go down half the way of the text\n\t\t_this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;\n\t\treturn _this;\n\t}\n\n\t_createClass(UPC, [{\n\t\tkey: \"valid\",\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{12}$/) !== -1 && this.data[11] == checksum(this.data);\n\t\t}\n\t}, {\n\t\tkey: \"encode\",\n\t\tvalue: function encode() {\n\t\t\tif (this.options.flat) {\n\t\t\t\treturn this.flatEncoding();\n\t\t\t} else {\n\t\t\t\treturn this.guardedEncoding();\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"flatEncoding\",\n\t\tvalue: function flatEncoding() {\n\t\t\tvar result = \"\";\n\n\t\t\tresult += \"101\";\n\t\t\tresult += (0, _encoder2.default)(this.data.substr(0, 6), \"LLLLLL\");\n\t\t\tresult += \"01010\";\n\t\t\tresult += (0, _encoder2.default)(this.data.substr(6, 6), \"RRRRRR\");\n\t\t\tresult += \"101\";\n\n\t\t\treturn {\n\t\t\t\tdata: result,\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}, {\n\t\tkey: \"guardedEncoding\",\n\t\tvalue: function guardedEncoding() {\n\t\t\tvar result = [];\n\n\t\t\t// Add the first digit\n\t\t\tif (this.displayValue) {\n\t\t\t\tresult.push({\n\t\t\t\t\tdata: \"00000000\",\n\t\t\t\t\ttext: this.text.substr(0, 1),\n\t\t\t\t\toptions: { textAlign: \"left\", fontSize: this.fontSize }\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// Add the guard bars\n\t\t\tresult.push({\n\t\t\t\tdata: \"101\" + (0, _encoder2.default)(this.data[0], \"L\"),\n\t\t\t\toptions: { height: this.guardHeight }\n\t\t\t});\n\n\t\t\t// Add the left side\n\t\t\tresult.push({\n\t\t\t\tdata: (0, _encoder2.default)(this.data.substr(1, 5), \"LLLLL\"),\n\t\t\t\ttext: this.text.substr(1, 5),\n\t\t\t\toptions: { fontSize: this.fontSize }\n\t\t\t});\n\n\t\t\t// Add the middle bits\n\t\t\tresult.push({\n\t\t\t\tdata: \"01010\",\n\t\t\t\toptions: { height: this.guardHeight }\n\t\t\t});\n\n\t\t\t// Add the right side\n\t\t\tresult.push({\n\t\t\t\tdata: (0, _encoder2.default)(this.data.substr(6, 5), \"RRRRR\"),\n\t\t\t\ttext: this.text.substr(6, 5),\n\t\t\t\toptions: { fontSize: this.fontSize }\n\t\t\t});\n\n\t\t\t// Add the end bits\n\t\t\tresult.push({\n\t\t\t\tdata: (0, _encoder2.default)(this.data[11], \"R\") + \"101\",\n\t\t\t\toptions: { height: this.guardHeight }\n\t\t\t});\n\n\t\t\t// Add the last digit\n\t\t\tif (this.displayValue) {\n\t\t\t\tresult.push({\n\t\t\t\t\tdata: \"00000000\",\n\t\t\t\t\ttext: this.text.substr(11, 1),\n\t\t\t\t\toptions: { textAlign: \"right\", fontSize: this.fontSize }\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn result;\n\t\t}\n\t}]);\n\n\treturn UPC;\n}(_Barcode3.default);\n\n// Calulate the checksum digit\n// https://en.wikipedia.org/wiki/International_Article_Number_(EAN)#Calculation_of_checksum_digit\n\n\nfunction checksum(number) {\n\tvar result = 0;\n\n\tvar i;\n\tfor (i = 1; i < 11; i += 2) {\n\t\tresult += parseInt(number[i]);\n\t}\n\tfor (i = 0; i < 11; i += 2) {\n\t\tresult += parseInt(number[i]) * 3;\n\t}\n\n\treturn (10 - result % 10) % 10;\n}\n\nexports.default = UPC;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _encoder = require('./encoder');\n\nvar _encoder2 = _interopRequireDefault(_encoder);\n\nvar _Barcode2 = require('../Barcode.js');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nvar _UPC = require('./UPC.js');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// https://en.wikipedia.org/wiki/Universal_Product_Code#Encoding\n//\n// UPC-E documentation:\n// https://en.wikipedia.org/wiki/Universal_Product_Code#UPC-E\n\nvar EXPANSIONS = [\"XX00000XXX\", \"XX10000XXX\", \"XX20000XXX\", \"XXX00000XX\", \"XXXX00000X\", \"XXXXX00005\", \"XXXXX00006\", \"XXXXX00007\", \"XXXXX00008\", \"XXXXX00009\"];\n\nvar PARITIES = [[\"EEEOOO\", \"OOOEEE\"], [\"EEOEOO\", \"OOEOEE\"], [\"EEOOEO\", \"OOEEOE\"], [\"EEOOOE\", \"OOEEEO\"], [\"EOEEOO\", \"OEOOEE\"], [\"EOOEEO\", \"OEEOOE\"], [\"EOOOEE\", \"OEEEOO\"], [\"EOEOEO\", \"OEOEOE\"], [\"EOEOOE\", \"OEOEEO\"], [\"EOOEOE\", \"OEEOEO\"]];\n\nvar UPCE = function (_Barcode) {\n\t_inherits(UPCE, _Barcode);\n\n\tfunction UPCE(data, options) {\n\t\t_classCallCheck(this, UPCE);\n\n\t\tvar _this = _possibleConstructorReturn(this, (UPCE.__proto__ || Object.getPrototypeOf(UPCE)).call(this, data, options));\n\t\t// Code may be 6 or 8 digits;\n\t\t// A 7 digit code is ambiguous as to whether the extra digit\n\t\t// is a UPC-A check or number system digit.\n\n\n\t\t_this.isValid = false;\n\t\tif (data.search(/^[0-9]{6}$/) !== -1) {\n\t\t\t_this.middleDigits = data;\n\t\t\t_this.upcA = expandToUPCA(data, \"0\");\n\t\t\t_this.text = options.text || '' + _this.upcA[0] + data + _this.upcA[_this.upcA.length - 1];\n\t\t\t_this.isValid = true;\n\t\t} else if (data.search(/^[01][0-9]{7}$/) !== -1) {\n\t\t\t_this.middleDigits = data.substring(1, data.length - 1);\n\t\t\t_this.upcA = expandToUPCA(_this.middleDigits, data[0]);\n\n\t\t\tif (_this.upcA[_this.upcA.length - 1] === data[data.length - 1]) {\n\t\t\t\t_this.isValid = true;\n\t\t\t} else {\n\t\t\t\t// checksum mismatch\n\t\t\t\treturn _possibleConstructorReturn(_this);\n\t\t\t}\n\t\t} else {\n\t\t\treturn _possibleConstructorReturn(_this);\n\t\t}\n\n\t\t_this.displayValue = options.displayValue;\n\n\t\t// Make sure the font is not bigger than the space between the guard bars\n\t\tif (options.fontSize > options.width * 10) {\n\t\t\t_this.fontSize = options.width * 10;\n\t\t} else {\n\t\t\t_this.fontSize = options.fontSize;\n\t\t}\n\n\t\t// Make the guard bars go down half the way of the text\n\t\t_this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;\n\t\treturn _this;\n\t}\n\n\t_createClass(UPCE, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.isValid;\n\t\t}\n\t}, {\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\tif (this.options.flat) {\n\t\t\t\treturn this.flatEncoding();\n\t\t\t} else {\n\t\t\t\treturn this.guardedEncoding();\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: 'flatEncoding',\n\t\tvalue: function flatEncoding() {\n\t\t\tvar result = \"\";\n\n\t\t\tresult += \"101\";\n\t\t\tresult += this.encodeMiddleDigits();\n\t\t\tresult += \"010101\";\n\n\t\t\treturn {\n\t\t\t\tdata: result,\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}, {\n\t\tkey: 'guardedEncoding',\n\t\tvalue: function guardedEncoding() {\n\t\t\tvar result = [];\n\n\t\t\t// Add the UPC-A number system digit beneath the quiet zone\n\t\t\tif (this.displayValue) {\n\t\t\t\tresult.push({\n\t\t\t\t\tdata: \"00000000\",\n\t\t\t\t\ttext: this.text[0],\n\t\t\t\t\toptions: { textAlign: \"left\", fontSize: this.fontSize }\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// Add the guard bars\n\t\t\tresult.push({\n\t\t\t\tdata: \"101\",\n\t\t\t\toptions: { height: this.guardHeight }\n\t\t\t});\n\n\t\t\t// Add the 6 UPC-E digits\n\t\t\tresult.push({\n\t\t\t\tdata: this.encodeMiddleDigits(),\n\t\t\t\ttext: this.text.substring(1, 7),\n\t\t\t\toptions: { fontSize: this.fontSize }\n\t\t\t});\n\n\t\t\t// Add the end bits\n\t\t\tresult.push({\n\t\t\t\tdata: \"010101\",\n\t\t\t\toptions: { height: this.guardHeight }\n\t\t\t});\n\n\t\t\t// Add the UPC-A check digit beneath the quiet zone\n\t\t\tif (this.displayValue) {\n\t\t\t\tresult.push({\n\t\t\t\t\tdata: \"00000000\",\n\t\t\t\t\ttext: this.text[7],\n\t\t\t\t\toptions: { textAlign: \"right\", fontSize: this.fontSize }\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn result;\n\t\t}\n\t}, {\n\t\tkey: 'encodeMiddleDigits',\n\t\tvalue: function encodeMiddleDigits() {\n\t\t\tvar numberSystem = this.upcA[0];\n\t\t\tvar checkDigit = this.upcA[this.upcA.length - 1];\n\t\t\tvar parity = PARITIES[parseInt(checkDigit)][parseInt(numberSystem)];\n\t\t\treturn (0, _encoder2.default)(this.middleDigits, parity);\n\t\t}\n\t}]);\n\n\treturn UPCE;\n}(_Barcode3.default);\n\nfunction expandToUPCA(middleDigits, numberSystem) {\n\tvar lastUpcE = parseInt(middleDigits[middleDigits.length - 1]);\n\tvar expansion = EXPANSIONS[lastUpcE];\n\n\tvar result = \"\";\n\tvar digitIndex = 0;\n\tfor (var i = 0; i < expansion.length; i++) {\n\t\tvar c = expansion[i];\n\t\tif (c === 'X') {\n\t\t\tresult += middleDigits[digitIndex++];\n\t\t} else {\n\t\t\tresult += c;\n\t\t}\n\t}\n\n\tresult = '' + numberSystem + result;\n\treturn '' + result + (0, _UPC.checksum)(result);\n}\n\nexports.default = UPCE;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.UPCE = exports.UPC = exports.EAN2 = exports.EAN5 = exports.EAN8 = exports.EAN13 = undefined;\n\nvar _EAN = require('./EAN13.js');\n\nvar _EAN2 = _interopRequireDefault(_EAN);\n\nvar _EAN3 = require('./EAN8.js');\n\nvar _EAN4 = _interopRequireDefault(_EAN3);\n\nvar _EAN5 = require('./EAN5.js');\n\nvar _EAN6 = _interopRequireDefault(_EAN5);\n\nvar _EAN7 = require('./EAN2.js');\n\nvar _EAN8 = _interopRequireDefault(_EAN7);\n\nvar _UPC = require('./UPC.js');\n\nvar _UPC2 = _interopRequireDefault(_UPC);\n\nvar _UPCE = require('./UPCE.js');\n\nvar _UPCE2 = _interopRequireDefault(_UPCE);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.EAN13 = _EAN2.default;\nexports.EAN8 = _EAN4.default;\nexports.EAN5 = _EAN6.default;\nexports.EAN2 = _EAN8.default;\nexports.UPC = _UPC2.default;\nexports.UPCE = _UPCE2.default;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nvar START_BIN = exports.START_BIN = '1010';\nvar END_BIN = exports.END_BIN = '11101';\n\nvar BINARIES = exports.BINARIES = ['00110', '10001', '01001', '11000', '00101', '10100', '01100', '00011', '10010', '01010'];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _constants = require('./constants');\n\nvar _Barcode2 = require('../Barcode');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar ITF = function (_Barcode) {\n\t_inherits(ITF, _Barcode);\n\n\tfunction ITF() {\n\t\t_classCallCheck(this, ITF);\n\n\t\treturn _possibleConstructorReturn(this, (ITF.__proto__ || Object.getPrototypeOf(ITF)).apply(this, arguments));\n\t}\n\n\t_createClass(ITF, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^([0-9]{2})+$/) !== -1;\n\t\t}\n\t}, {\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\tvar _this2 = this;\n\n\t\t\t// Calculate all the digit pairs\n\t\t\tvar encoded = this.data.match(/.{2}/g).map(function (pair) {\n\t\t\t\treturn _this2.encodePair(pair);\n\t\t\t}).join('');\n\n\t\t\treturn {\n\t\t\t\tdata: _constants.START_BIN + encoded + _constants.END_BIN,\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\n\t\t// Calculate the data of a number pair\n\n\t}, {\n\t\tkey: 'encodePair',\n\t\tvalue: function encodePair(pair) {\n\t\t\tvar second = _constants.BINARIES[pair[1]];\n\n\t\t\treturn _constants.BINARIES[pair[0]].split('').map(function (first, idx) {\n\t\t\t\treturn (first === '1' ? '111' : '1') + (second[idx] === '1' ? '000' : '0');\n\t\t\t}).join('');\n\t\t}\n\t}]);\n\n\treturn ITF;\n}(_Barcode3.default);\n\nexports.default = ITF;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _ITF2 = require('./ITF');\n\nvar _ITF3 = _interopRequireDefault(_ITF2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n// Calculate the checksum digit\nvar checksum = function checksum(data) {\n\tvar res = data.substr(0, 13).split('').map(function (num) {\n\t\treturn parseInt(num, 10);\n\t}).reduce(function (sum, n, idx) {\n\t\treturn sum + n * (3 - idx % 2 * 2);\n\t}, 0);\n\n\treturn Math.ceil(res / 10) * 10 - res;\n};\n\nvar ITF14 = function (_ITF) {\n\t_inherits(ITF14, _ITF);\n\n\tfunction ITF14(data, options) {\n\t\t_classCallCheck(this, ITF14);\n\n\t\t// Add checksum if it does not exist\n\t\tif (data.search(/^[0-9]{13}$/) !== -1) {\n\t\t\tdata += checksum(data);\n\t\t}\n\t\treturn _possibleConstructorReturn(this, (ITF14.__proto__ || Object.getPrototypeOf(ITF14)).call(this, data, options));\n\t}\n\n\t_createClass(ITF14, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{14}$/) !== -1 && +this.data[13] === checksum(this.data);\n\t\t}\n\t}]);\n\n\treturn ITF14;\n}(_ITF3.default);\n\nexports.default = ITF14;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ITF14 = exports.ITF = undefined;\n\nvar _ITF = require('./ITF');\n\nvar _ITF2 = _interopRequireDefault(_ITF);\n\nvar _ITF3 = require('./ITF14');\n\nvar _ITF4 = _interopRequireDefault(_ITF3);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.ITF = _ITF2.default;\nexports.ITF14 = _ITF4.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Barcode2 = require(\"../Barcode.js\");\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation\n// https://en.wikipedia.org/wiki/MSI_Barcode#Character_set_and_binary_lookup\n\nvar MSI = function (_Barcode) {\n\t_inherits(MSI, _Barcode);\n\n\tfunction MSI(data, options) {\n\t\t_classCallCheck(this, MSI);\n\n\t\treturn _possibleConstructorReturn(this, (MSI.__proto__ || Object.getPrototypeOf(MSI)).call(this, data, options));\n\t}\n\n\t_createClass(MSI, [{\n\t\tkey: \"encode\",\n\t\tvalue: function encode() {\n\t\t\t// Start bits\n\t\t\tvar ret = \"110\";\n\n\t\t\tfor (var i = 0; i < this.data.length; i++) {\n\t\t\t\t// Convert the character to binary (always 4 binary digits)\n\t\t\t\tvar digit = parseInt(this.data[i]);\n\t\t\t\tvar bin = digit.toString(2);\n\t\t\t\tbin = addZeroes(bin, 4 - bin.length);\n\n\t\t\t\t// Add 100 for every zero and 110 for every 1\n\t\t\t\tfor (var b = 0; b < bin.length; b++) {\n\t\t\t\t\tret += bin[b] == \"0\" ? \"100\" : \"110\";\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// End bits\n\t\t\tret += \"1001\";\n\n\t\t\treturn {\n\t\t\t\tdata: ret,\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}, {\n\t\tkey: \"valid\",\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]+$/) !== -1;\n\t\t}\n\t}]);\n\n\treturn MSI;\n}(_Barcode3.default);\n\nfunction addZeroes(number, n) {\n\tfor (var i = 0; i < n; i++) {\n\t\tnumber = \"0\" + number;\n\t}\n\treturn number;\n}\n\nexports.default = MSI;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.mod10 = mod10;\nexports.mod11 = mod11;\nfunction mod10(number) {\n\tvar sum = 0;\n\tfor (var i = 0; i < number.length; i++) {\n\t\tvar n = parseInt(number[i]);\n\t\tif ((i + number.length) % 2 === 0) {\n\t\t\tsum += n;\n\t\t} else {\n\t\t\tsum += n * 2 % 10 + Math.floor(n * 2 / 10);\n\t\t}\n\t}\n\treturn (10 - sum % 10) % 10;\n}\n\nfunction mod11(number) {\n\tvar sum = 0;\n\tvar weights = [2, 3, 4, 5, 6, 7];\n\tfor (var i = 0; i < number.length; i++) {\n\t\tvar n = parseInt(number[number.length - 1 - i]);\n\t\tsum += weights[i % weights.length] * n;\n\t}\n\treturn (11 - sum % 11) % 11;\n}", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _MSI2 = require('./MSI.js');\n\nvar _MSI3 = _interopRequireDefault(_MSI2);\n\nvar _checksums = require('./checksums.js');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar MSI10 = function (_MSI) {\n\t_inherits(MSI10, _MSI);\n\n\tfunction MSI10(data, options) {\n\t\t_classCallCheck(this, MSI10);\n\n\t\treturn _possibleConstructorReturn(this, (MSI10.__proto__ || Object.getPrototypeOf(MSI10)).call(this, data + (0, _checksums.mod10)(data), options));\n\t}\n\n\treturn MSI10;\n}(_MSI3.default);\n\nexports.default = MSI10;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _MSI2 = require('./MSI.js');\n\nvar _MSI3 = _interopRequireDefault(_MSI2);\n\nvar _checksums = require('./checksums.js');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar MSI11 = function (_MSI) {\n\t_inherits(MSI11, _MSI);\n\n\tfunction MSI11(data, options) {\n\t\t_classCallCheck(this, MSI11);\n\n\t\treturn _possibleConstructorReturn(this, (MSI11.__proto__ || Object.getPrototypeOf(MSI11)).call(this, data + (0, _checksums.mod11)(data), options));\n\t}\n\n\treturn MSI11;\n}(_MSI3.default);\n\nexports.default = MSI11;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _MSI2 = require('./MSI.js');\n\nvar _MSI3 = _interopRequireDefault(_MSI2);\n\nvar _checksums = require('./checksums.js');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar MSI1010 = function (_MSI) {\n\t_inherits(MSI1010, _MSI);\n\n\tfunction MSI1010(data, options) {\n\t\t_classCallCheck(this, MSI1010);\n\n\t\tdata += (0, _checksums.mod10)(data);\n\t\tdata += (0, _checksums.mod10)(data);\n\t\treturn _possibleConstructorReturn(this, (MSI1010.__proto__ || Object.getPrototypeOf(MSI1010)).call(this, data, options));\n\t}\n\n\treturn MSI1010;\n}(_MSI3.default);\n\nexports.default = MSI1010;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _MSI2 = require('./MSI.js');\n\nvar _MSI3 = _interopRequireDefault(_MSI2);\n\nvar _checksums = require('./checksums.js');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar MSI1110 = function (_MSI) {\n\t_inherits(MSI1110, _MSI);\n\n\tfunction MSI1110(data, options) {\n\t\t_classCallCheck(this, MSI1110);\n\n\t\tdata += (0, _checksums.mod11)(data);\n\t\tdata += (0, _checksums.mod10)(data);\n\t\treturn _possibleConstructorReturn(this, (MSI1110.__proto__ || Object.getPrototypeOf(MSI1110)).call(this, data, options));\n\t}\n\n\treturn MSI1110;\n}(_MSI3.default);\n\nexports.default = MSI1110;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MSI1110 = exports.MSI1010 = exports.MSI11 = exports.MSI10 = exports.MSI = undefined;\n\nvar _MSI = require('./MSI.js');\n\nvar _MSI2 = _interopRequireDefault(_MSI);\n\nvar _MSI3 = require('./MSI10.js');\n\nvar _MSI4 = _interopRequireDefault(_MSI3);\n\nvar _MSI5 = require('./MSI11.js');\n\nvar _MSI6 = _interopRequireDefault(_MSI5);\n\nvar _MSI7 = require('./MSI1010.js');\n\nvar _MSI8 = _interopRequireDefault(_MSI7);\n\nvar _MSI9 = require('./MSI1110.js');\n\nvar _MSI10 = _interopRequireDefault(_MSI9);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.MSI = _MSI2.default;\nexports.MSI10 = _MSI4.default;\nexports.MSI11 = _MSI6.default;\nexports.MSI1010 = _MSI8.default;\nexports.MSI1110 = _MSI10.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.pharmacode = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Barcode2 = require(\"../Barcode.js\");\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation\n// http://www.gomaro.ch/ftproot/Laetus_PHARMA-CODE.pdf\n\nvar pharmacode = function (_Barcode) {\n\t_inherits(pharmacode, _Barcode);\n\n\tfunction pharmacode(data, options) {\n\t\t_classCallCheck(this, pharmacode);\n\n\t\tvar _this = _possibleConstructorReturn(this, (pharmacode.__proto__ || Object.getPrototypeOf(pharmacode)).call(this, data, options));\n\n\t\t_this.number = parseInt(data, 10);\n\t\treturn _this;\n\t}\n\n\t_createClass(pharmacode, [{\n\t\tkey: \"encode\",\n\t\tvalue: function encode() {\n\t\t\tvar z = this.number;\n\t\t\tvar result = \"\";\n\n\t\t\t// http://i.imgur.com/RMm4UDJ.png\n\t\t\t// (source: http://www.gomaro.ch/ftproot/Laetus_PHARMA-CODE.pdf, page: 34)\n\t\t\twhile (!isNaN(z) && z != 0) {\n\t\t\t\tif (z % 2 === 0) {\n\t\t\t\t\t// Even\n\t\t\t\t\tresult = \"11100\" + result;\n\t\t\t\t\tz = (z - 2) / 2;\n\t\t\t\t} else {\n\t\t\t\t\t// Odd\n\t\t\t\t\tresult = \"100\" + result;\n\t\t\t\t\tz = (z - 1) / 2;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Remove the two last zeroes\n\t\t\tresult = result.slice(0, -2);\n\n\t\t\treturn {\n\t\t\t\tdata: result,\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}, {\n\t\tkey: \"valid\",\n\t\tvalue: function valid() {\n\t\t\treturn this.number >= 3 && this.number <= 131070;\n\t\t}\n\t}]);\n\n\treturn pharmacode;\n}(_Barcode3.default);\n\nexports.pharmacode = pharmacode;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.codabar = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Barcode2 = require(\"../Barcode.js\");\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding specification:\n// http://www.barcodeisland.com/codabar.phtml\n\nvar codabar = function (_Barcode) {\n\t_inherits(codabar, _Barcode);\n\n\tfunction codabar(data, options) {\n\t\t_classCallCheck(this, codabar);\n\n\t\tif (data.search(/^[0-9\\-\\$\\:\\.\\+\\/]+$/) === 0) {\n\t\t\tdata = \"A\" + data + \"A\";\n\t\t}\n\n\t\tvar _this = _possibleConstructorReturn(this, (codabar.__proto__ || Object.getPrototypeOf(codabar)).call(this, data.toUpperCase(), options));\n\n\t\t_this.text = _this.options.text || _this.text.replace(/[A-D]/g, '');\n\t\treturn _this;\n\t}\n\n\t_createClass(codabar, [{\n\t\tkey: \"valid\",\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[A-D][0-9\\-\\$\\:\\.\\+\\/]+[A-D]$/) !== -1;\n\t\t}\n\t}, {\n\t\tkey: \"encode\",\n\t\tvalue: function encode() {\n\t\t\tvar result = [];\n\t\t\tvar encodings = this.getEncodings();\n\t\t\tfor (var i = 0; i < this.data.length; i++) {\n\t\t\t\tresult.push(encodings[this.data.charAt(i)]);\n\t\t\t\t// for all characters except the last, append a narrow-space (\"0\")\n\t\t\t\tif (i !== this.data.length - 1) {\n\t\t\t\t\tresult.push(\"0\");\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn {\n\t\t\t\ttext: this.text,\n\t\t\t\tdata: result.join('')\n\t\t\t};\n\t\t}\n\t}, {\n\t\tkey: \"getEncodings\",\n\t\tvalue: function getEncodings() {\n\t\t\treturn {\n\t\t\t\t\"0\": \"101010011\",\n\t\t\t\t\"1\": \"101011001\",\n\t\t\t\t\"2\": \"101001011\",\n\t\t\t\t\"3\": \"110010101\",\n\t\t\t\t\"4\": \"101101001\",\n\t\t\t\t\"5\": \"110101001\",\n\t\t\t\t\"6\": \"100101011\",\n\t\t\t\t\"7\": \"100101101\",\n\t\t\t\t\"8\": \"100110101\",\n\t\t\t\t\"9\": \"110100101\",\n\t\t\t\t\"-\": \"101001101\",\n\t\t\t\t\"$\": \"101100101\",\n\t\t\t\t\":\": \"1101011011\",\n\t\t\t\t\"/\": \"1101101011\",\n\t\t\t\t\".\": \"1101101101\",\n\t\t\t\t\"+\": \"1011011011\",\n\t\t\t\t\"A\": \"1011001001\",\n\t\t\t\t\"B\": \"1001001011\",\n\t\t\t\t\"C\": \"1010010011\",\n\t\t\t\t\"D\": \"1010011001\"\n\t\t\t};\n\t\t}\n\t}]);\n\n\treturn codabar;\n}(_Barcode3.default);\n\nexports.codabar = codabar;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.GenericBarcode = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Barcode2 = require(\"../Barcode.js\");\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar GenericBarcode = function (_Barcode) {\n\t_inherits(GenericBarcode, _Barcode);\n\n\tfunction GenericBarcode(data, options) {\n\t\t_classCallCheck(this, GenericBarcode);\n\n\t\treturn _possibleConstructorReturn(this, (GenericBarcode.__proto__ || Object.getPrototypeOf(GenericBarcode)).call(this, data, options)); // Sets this.data and this.text\n\t}\n\n\t// Return the corresponding binary numbers for the data provided\n\n\n\t_createClass(GenericBarcode, [{\n\t\tkey: \"encode\",\n\t\tvalue: function encode() {\n\t\t\treturn {\n\t\t\t\tdata: \"10101010101010101010101010101010101010101\",\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\n\t\t// Resturn true/false if the string provided is valid for this encoder\n\n\t}, {\n\t\tkey: \"valid\",\n\t\tvalue: function valid() {\n\t\t\treturn true;\n\t\t}\n\t}]);\n\n\treturn GenericBarcode;\n}(_Barcode3.default);\n\nexports.GenericBarcode = GenericBarcode;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _CODE = require('./CODE39/');\n\nvar _CODE2 = require('./CODE128/');\n\nvar _EAN_UPC = require('./EAN_UPC/');\n\nvar _ITF = require('./ITF/');\n\nvar _MSI = require('./MSI/');\n\nvar _pharmacode = require('./pharmacode/');\n\nvar _codabar = require('./codabar');\n\nvar _GenericBarcode = require('./GenericBarcode/');\n\nexports.default = {\n\tCODE39: _CODE.CODE39,\n\tCODE128: _CODE2.CODE128, CODE128A: _CODE2.CODE128A, CODE128B: _CODE2.CODE128B, CODE128C: _CODE2.CODE128C,\n\tEAN13: _EAN_UPC.EAN13, EAN8: _EAN_UPC.EAN8, EAN5: _EAN_UPC.EAN5, EAN2: _EAN_UPC.EAN2, UPC: _EAN_UPC.UPC, UPCE: _EAN_UPC.UPCE,\n\tITF14: _ITF.ITF14,\n\tITF: _ITF.ITF,\n\tMSI: _MSI.MSI, MSI10: _MSI.MSI10, MSI11: _MSI.MSI11, MSI1010: _MSI.MSI1010, MSI1110: _MSI.MSI1110,\n\tpharmacode: _pharmacode.pharmacode,\n\tcodabar: _codabar.codabar,\n\tGenericBarcode: _GenericBarcode.GenericBarcode\n};", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nexports.default = function (old, replaceObj) {\n  return _extends({}, old, replaceObj);\n};", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.default = linearizeEncodings;\n\n// Encodings can be nestled like [[1-1, 1-2], 2, [3-1, 3-2]\n// Convert to [1-1, 1-2, 2, 3-1, 3-2]\n\nfunction linearizeEncodings(encodings) {\n\tvar linearEncodings = [];\n\tfunction nextLevel(encoded) {\n\t\tif (Array.isArray(encoded)) {\n\t\t\tfor (var i = 0; i < encoded.length; i++) {\n\t\t\t\tnextLevel(encoded[i]);\n\t\t\t}\n\t\t} else {\n\t\t\tencoded.text = encoded.text || \"\";\n\t\t\tencoded.data = encoded.data || \"\";\n\t\t\tlinearEncodings.push(encoded);\n\t\t}\n\t}\n\tnextLevel(encodings);\n\n\treturn linearEncodings;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.default = fixOptions;\n\n\nfunction fixOptions(options) {\n\t// Fix the margins\n\toptions.marginTop = options.marginTop || options.margin;\n\toptions.marginBottom = options.marginBottom || options.margin;\n\toptions.marginRight = options.marginRight || options.margin;\n\toptions.marginLeft = options.marginLeft || options.margin;\n\n\treturn options;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.default = optionsFromStrings;\n\n// Convert string to integers/booleans where it should be\n\nfunction optionsFromStrings(options) {\n\tvar intOptions = [\"width\", \"height\", \"textMargin\", \"fontSize\", \"margin\", \"marginTop\", \"marginBottom\", \"marginLeft\", \"marginRight\"];\n\n\tfor (var intOption in intOptions) {\n\t\tif (intOptions.hasOwnProperty(intOption)) {\n\t\t\tintOption = intOptions[intOption];\n\t\t\tif (typeof options[intOption] === \"string\") {\n\t\t\t\toptions[intOption] = parseInt(options[intOption], 10);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (typeof options[\"displayValue\"] === \"string\") {\n\t\toptions[\"displayValue\"] = options[\"displayValue\"] != \"false\";\n\t}\n\n\treturn options;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nvar defaults = {\n\twidth: 2,\n\theight: 100,\n\tformat: \"auto\",\n\tdisplayValue: true,\n\tfontOptions: \"\",\n\tfont: \"monospace\",\n\ttext: undefined,\n\ttextAlign: \"center\",\n\ttextPosition: \"bottom\",\n\ttextMargin: 2,\n\tfontSize: 20,\n\tbackground: \"#ffffff\",\n\tlineColor: \"#000000\",\n\tmargin: 10,\n\tmarginTop: undefined,\n\tmarginBottom: undefined,\n\tmarginLeft: undefined,\n\tmarginRight: undefined,\n\tvalid: function valid() {}\n};\n\nexports.default = defaults;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _optionsFromStrings = require(\"./optionsFromStrings.js\");\n\nvar _optionsFromStrings2 = _interopRequireDefault(_optionsFromStrings);\n\nvar _defaults = require(\"../options/defaults.js\");\n\nvar _defaults2 = _interopRequireDefault(_defaults);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction getOptionsFromElement(element) {\n\tvar options = {};\n\tfor (var property in _defaults2.default) {\n\t\tif (_defaults2.default.hasOwnProperty(property)) {\n\t\t\t// jsbarcode-*\n\t\t\tif (element.hasAttribute(\"jsbarcode-\" + property.toLowerCase())) {\n\t\t\t\toptions[property] = element.getAttribute(\"jsbarcode-\" + property.toLowerCase());\n\t\t\t}\n\n\t\t\t// data-*\n\t\t\tif (element.hasAttribute(\"data-\" + property.toLowerCase())) {\n\t\t\t\toptions[property] = element.getAttribute(\"data-\" + property.toLowerCase());\n\t\t\t}\n\t\t}\n\t}\n\n\toptions[\"value\"] = element.getAttribute(\"jsbarcode-value\") || element.getAttribute(\"data-value\");\n\n\t// Since all atributes are string they need to be converted to integers\n\toptions = (0, _optionsFromStrings2.default)(options);\n\n\treturn options;\n}\n\nexports.default = getOptionsFromElement;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.getTotalWidthOfEncodings = exports.calculateEncodingAttributes = exports.getBarcodePadding = exports.getEncodingHeight = exports.getMaximumHeightOfEncodings = undefined;\n\nvar _merge = require(\"../help/merge.js\");\n\nvar _merge2 = _interopRequireDefault(_merge);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction getEncodingHeight(encoding, options) {\n\treturn options.height + (options.displayValue && encoding.text.length > 0 ? options.fontSize + options.textMargin : 0) + options.marginTop + options.marginBottom;\n}\n\nfunction getBarcodePadding(textWidth, barcodeWidth, options) {\n\tif (options.displayValue && barcodeWidth < textWidth) {\n\t\tif (options.textAlign == \"center\") {\n\t\t\treturn Math.floor((textWidth - barcodeWidth) / 2);\n\t\t} else if (options.textAlign == \"left\") {\n\t\t\treturn 0;\n\t\t} else if (options.textAlign == \"right\") {\n\t\t\treturn Math.floor(textWidth - barcodeWidth);\n\t\t}\n\t}\n\treturn 0;\n}\n\nfunction calculateEncodingAttributes(encodings, barcodeOptions, context) {\n\tfor (var i = 0; i < encodings.length; i++) {\n\t\tvar encoding = encodings[i];\n\t\tvar options = (0, _merge2.default)(barcodeOptions, encoding.options);\n\n\t\t// Calculate the width of the encoding\n\t\tvar textWidth;\n\t\tif (options.displayValue) {\n\t\t\ttextWidth = messureText(encoding.text, options, context);\n\t\t} else {\n\t\t\ttextWidth = 0;\n\t\t}\n\n\t\tvar barcodeWidth = encoding.data.length * options.width;\n\t\tencoding.width = Math.ceil(Math.max(textWidth, barcodeWidth));\n\n\t\tencoding.height = getEncodingHeight(encoding, options);\n\n\t\tencoding.barcodePadding = getBarcodePadding(textWidth, barcodeWidth, options);\n\t}\n}\n\nfunction getTotalWidthOfEncodings(encodings) {\n\tvar totalWidth = 0;\n\tfor (var i = 0; i < encodings.length; i++) {\n\t\ttotalWidth += encodings[i].width;\n\t}\n\treturn totalWidth;\n}\n\nfunction getMaximumHeightOfEncodings(encodings) {\n\tvar maxHeight = 0;\n\tfor (var i = 0; i < encodings.length; i++) {\n\t\tif (encodings[i].height > maxHeight) {\n\t\t\tmaxHeight = encodings[i].height;\n\t\t}\n\t}\n\treturn maxHeight;\n}\n\nfunction messureText(string, options, context) {\n\tvar ctx;\n\n\tif (context) {\n\t\tctx = context;\n\t} else if (typeof document !== \"undefined\") {\n\t\tctx = document.createElement(\"canvas\").getContext(\"2d\");\n\t} else {\n\t\t// If the text cannot be messured we will return 0.\n\t\t// This will make some barcode with big text render incorrectly\n\t\treturn 0;\n\t}\n\tctx.font = options.fontOptions + \" \" + options.fontSize + \"px \" + options.font;\n\n\t// Calculate the width of the encoding\n\tvar measureTextResult = ctx.measureText(string);\n\tif (!measureTextResult) {\n\t\t// Some implementations don't implement measureText and return undefined.\n\t\t// If the text cannot be measured we will return 0.\n\t\t// This will make some barcode with big text render incorrectly\n\t\treturn 0;\n\t}\n\tvar size = measureTextResult.width;\n\treturn size;\n}\n\nexports.getMaximumHeightOfEncodings = getMaximumHeightOfEncodings;\nexports.getEncodingHeight = getEncodingHeight;\nexports.getBarcodePadding = getBarcodePadding;\nexports.calculateEncodingAttributes = calculateEncodingAttributes;\nexports.getTotalWidthOfEncodings = getTotalWidthOfEncodings;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _merge = require(\"../help/merge.js\");\n\nvar _merge2 = _interopRequireDefault(_merge);\n\nvar _shared = require(\"./shared.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar CanvasRenderer = function () {\n\tfunction CanvasRenderer(canvas, encodings, options) {\n\t\t_classCallCheck(this, CanvasRenderer);\n\n\t\tthis.canvas = canvas;\n\t\tthis.encodings = encodings;\n\t\tthis.options = options;\n\t}\n\n\t_createClass(CanvasRenderer, [{\n\t\tkey: \"render\",\n\t\tvalue: function render() {\n\t\t\t// Abort if the browser does not support HTML5 canvas\n\t\t\tif (!this.canvas.getContext) {\n\t\t\t\tthrow new Error('The browser does not support canvas.');\n\t\t\t}\n\n\t\t\tthis.prepareCanvas();\n\t\t\tfor (var i = 0; i < this.encodings.length; i++) {\n\t\t\t\tvar encodingOptions = (0, _merge2.default)(this.options, this.encodings[i].options);\n\n\t\t\t\tthis.drawCanvasBarcode(encodingOptions, this.encodings[i]);\n\t\t\t\tthis.drawCanvasText(encodingOptions, this.encodings[i]);\n\n\t\t\t\tthis.moveCanvasDrawing(this.encodings[i]);\n\t\t\t}\n\n\t\t\tthis.restoreCanvas();\n\t\t}\n\t}, {\n\t\tkey: \"prepareCanvas\",\n\t\tvalue: function prepareCanvas() {\n\t\t\t// Get the canvas context\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tctx.save();\n\n\t\t\t(0, _shared.calculateEncodingAttributes)(this.encodings, this.options, ctx);\n\t\t\tvar totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);\n\t\t\tvar maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);\n\n\t\t\tthis.canvas.width = totalWidth + this.options.marginLeft + this.options.marginRight;\n\n\t\t\tthis.canvas.height = maxHeight;\n\n\t\t\t// Paint the canvas\n\t\t\tctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n\t\t\tif (this.options.background) {\n\t\t\t\tctx.fillStyle = this.options.background;\n\t\t\t\tctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n\t\t\t}\n\n\t\t\tctx.translate(this.options.marginLeft, 0);\n\t\t}\n\t}, {\n\t\tkey: \"drawCanvasBarcode\",\n\t\tvalue: function drawCanvasBarcode(options, encoding) {\n\t\t\t// Get the canvas context\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tvar binary = encoding.data;\n\n\t\t\t// Creates the barcode out of the encoded binary\n\t\t\tvar yFrom;\n\t\t\tif (options.textPosition == \"top\") {\n\t\t\t\tyFrom = options.marginTop + options.fontSize + options.textMargin;\n\t\t\t} else {\n\t\t\t\tyFrom = options.marginTop;\n\t\t\t}\n\n\t\t\tctx.fillStyle = options.lineColor;\n\n\t\t\tfor (var b = 0; b < binary.length; b++) {\n\t\t\t\tvar x = b * options.width + encoding.barcodePadding;\n\n\t\t\t\tif (binary[b] === \"1\") {\n\t\t\t\t\tctx.fillRect(x, yFrom, options.width, options.height);\n\t\t\t\t} else if (binary[b]) {\n\t\t\t\t\tctx.fillRect(x, yFrom, options.width, options.height * binary[b]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"drawCanvasText\",\n\t\tvalue: function drawCanvasText(options, encoding) {\n\t\t\t// Get the canvas context\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tvar font = options.fontOptions + \" \" + options.fontSize + \"px \" + options.font;\n\n\t\t\t// Draw the text if displayValue is set\n\t\t\tif (options.displayValue) {\n\t\t\t\tvar x, y;\n\n\t\t\t\tif (options.textPosition == \"top\") {\n\t\t\t\t\ty = options.marginTop + options.fontSize - options.textMargin;\n\t\t\t\t} else {\n\t\t\t\t\ty = options.height + options.textMargin + options.marginTop + options.fontSize;\n\t\t\t\t}\n\n\t\t\t\tctx.font = font;\n\n\t\t\t\t// Draw the text in the correct X depending on the textAlign option\n\t\t\t\tif (options.textAlign == \"left\" || encoding.barcodePadding > 0) {\n\t\t\t\t\tx = 0;\n\t\t\t\t\tctx.textAlign = 'left';\n\t\t\t\t} else if (options.textAlign == \"right\") {\n\t\t\t\t\tx = encoding.width - 1;\n\t\t\t\t\tctx.textAlign = 'right';\n\t\t\t\t}\n\t\t\t\t// In all other cases, center the text\n\t\t\t\telse {\n\t\t\t\t\t\tx = encoding.width / 2;\n\t\t\t\t\t\tctx.textAlign = 'center';\n\t\t\t\t\t}\n\n\t\t\t\tctx.fillText(encoding.text, x, y);\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"moveCanvasDrawing\",\n\t\tvalue: function moveCanvasDrawing(encoding) {\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tctx.translate(encoding.width, 0);\n\t\t}\n\t}, {\n\t\tkey: \"restoreCanvas\",\n\t\tvalue: function restoreCanvas() {\n\t\t\t// Get the canvas context\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tctx.restore();\n\t\t}\n\t}]);\n\n\treturn CanvasRenderer;\n}();\n\nexports.default = CanvasRenderer;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _merge = require(\"../help/merge.js\");\n\nvar _merge2 = _interopRequireDefault(_merge);\n\nvar _shared = require(\"./shared.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar svgns = \"http://www.w3.org/2000/svg\";\n\nvar SVGRenderer = function () {\n\tfunction SVGRenderer(svg, encodings, options) {\n\t\t_classCallCheck(this, SVGRenderer);\n\n\t\tthis.svg = svg;\n\t\tthis.encodings = encodings;\n\t\tthis.options = options;\n\t\tthis.document = options.xmlDocument || document;\n\t}\n\n\t_createClass(SVGRenderer, [{\n\t\tkey: \"render\",\n\t\tvalue: function render() {\n\t\t\tvar currentX = this.options.marginLeft;\n\n\t\t\tthis.prepareSVG();\n\t\t\tfor (var i = 0; i < this.encodings.length; i++) {\n\t\t\t\tvar encoding = this.encodings[i];\n\t\t\t\tvar encodingOptions = (0, _merge2.default)(this.options, encoding.options);\n\n\t\t\t\tvar group = this.createGroup(currentX, encodingOptions.marginTop, this.svg);\n\n\t\t\t\tthis.setGroupOptions(group, encodingOptions);\n\n\t\t\t\tthis.drawSvgBarcode(group, encodingOptions, encoding);\n\t\t\t\tthis.drawSVGText(group, encodingOptions, encoding);\n\n\t\t\t\tcurrentX += encoding.width;\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"prepareSVG\",\n\t\tvalue: function prepareSVG() {\n\t\t\t// Clear the SVG\n\t\t\twhile (this.svg.firstChild) {\n\t\t\t\tthis.svg.removeChild(this.svg.firstChild);\n\t\t\t}\n\n\t\t\t(0, _shared.calculateEncodingAttributes)(this.encodings, this.options);\n\t\t\tvar totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);\n\t\t\tvar maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);\n\n\t\t\tvar width = totalWidth + this.options.marginLeft + this.options.marginRight;\n\t\t\tthis.setSvgAttributes(width, maxHeight);\n\n\t\t\tif (this.options.background) {\n\t\t\t\tthis.drawRect(0, 0, width, maxHeight, this.svg).setAttribute(\"style\", \"fill:\" + this.options.background + \";\");\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"drawSvgBarcode\",\n\t\tvalue: function drawSvgBarcode(parent, options, encoding) {\n\t\t\tvar binary = encoding.data;\n\n\t\t\t// Creates the barcode out of the encoded binary\n\t\t\tvar yFrom;\n\t\t\tif (options.textPosition == \"top\") {\n\t\t\t\tyFrom = options.fontSize + options.textMargin;\n\t\t\t} else {\n\t\t\t\tyFrom = 0;\n\t\t\t}\n\n\t\t\tvar barWidth = 0;\n\t\t\tvar x = 0;\n\t\t\tfor (var b = 0; b < binary.length; b++) {\n\t\t\t\tx = b * options.width + encoding.barcodePadding;\n\n\t\t\t\tif (binary[b] === \"1\") {\n\t\t\t\t\tbarWidth++;\n\t\t\t\t} else if (barWidth > 0) {\n\t\t\t\t\tthis.drawRect(x - options.width * barWidth, yFrom, options.width * barWidth, options.height, parent);\n\t\t\t\t\tbarWidth = 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Last draw is needed since the barcode ends with 1\n\t\t\tif (barWidth > 0) {\n\t\t\t\tthis.drawRect(x - options.width * (barWidth - 1), yFrom, options.width * barWidth, options.height, parent);\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"drawSVGText\",\n\t\tvalue: function drawSVGText(parent, options, encoding) {\n\t\t\tvar textElem = this.document.createElementNS(svgns, 'text');\n\n\t\t\t// Draw the text if displayValue is set\n\t\t\tif (options.displayValue) {\n\t\t\t\tvar x, y;\n\n\t\t\t\ttextElem.setAttribute(\"style\", \"font:\" + options.fontOptions + \" \" + options.fontSize + \"px \" + options.font);\n\n\t\t\t\tif (options.textPosition == \"top\") {\n\t\t\t\t\ty = options.fontSize - options.textMargin;\n\t\t\t\t} else {\n\t\t\t\t\ty = options.height + options.textMargin + options.fontSize;\n\t\t\t\t}\n\n\t\t\t\t// Draw the text in the correct X depending on the textAlign option\n\t\t\t\tif (options.textAlign == \"left\" || encoding.barcodePadding > 0) {\n\t\t\t\t\tx = 0;\n\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"start\");\n\t\t\t\t} else if (options.textAlign == \"right\") {\n\t\t\t\t\tx = encoding.width - 1;\n\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"end\");\n\t\t\t\t}\n\t\t\t\t// In all other cases, center the text\n\t\t\t\telse {\n\t\t\t\t\t\tx = encoding.width / 2;\n\t\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"middle\");\n\t\t\t\t\t}\n\n\t\t\t\ttextElem.setAttribute(\"x\", x);\n\t\t\t\ttextElem.setAttribute(\"y\", y);\n\n\t\t\t\ttextElem.appendChild(this.document.createTextNode(encoding.text));\n\n\t\t\t\tparent.appendChild(textElem);\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"setSvgAttributes\",\n\t\tvalue: function setSvgAttributes(width, height) {\n\t\t\tvar svg = this.svg;\n\t\t\tsvg.setAttribute(\"width\", width + \"px\");\n\t\t\tsvg.setAttribute(\"height\", height + \"px\");\n\t\t\tsvg.setAttribute(\"x\", \"0px\");\n\t\t\tsvg.setAttribute(\"y\", \"0px\");\n\t\t\tsvg.setAttribute(\"viewBox\", \"0 0 \" + width + \" \" + height);\n\n\t\t\tsvg.setAttribute(\"xmlns\", svgns);\n\t\t\tsvg.setAttribute(\"version\", \"1.1\");\n\n\t\t\tsvg.setAttribute(\"style\", \"transform: translate(0,0)\");\n\t\t}\n\t}, {\n\t\tkey: \"createGroup\",\n\t\tvalue: function createGroup(x, y, parent) {\n\t\t\tvar group = this.document.createElementNS(svgns, 'g');\n\t\t\tgroup.setAttribute(\"transform\", \"translate(\" + x + \", \" + y + \")\");\n\n\t\t\tparent.appendChild(group);\n\n\t\t\treturn group;\n\t\t}\n\t}, {\n\t\tkey: \"setGroupOptions\",\n\t\tvalue: function setGroupOptions(group, options) {\n\t\t\tgroup.setAttribute(\"style\", \"fill:\" + options.lineColor + \";\");\n\t\t}\n\t}, {\n\t\tkey: \"drawRect\",\n\t\tvalue: function drawRect(x, y, width, height, parent) {\n\t\t\tvar rect = this.document.createElementNS(svgns, 'rect');\n\n\t\t\trect.setAttribute(\"x\", x);\n\t\t\trect.setAttribute(\"y\", y);\n\t\t\trect.setAttribute(\"width\", width);\n\t\t\trect.setAttribute(\"height\", height);\n\n\t\t\tparent.appendChild(rect);\n\n\t\t\treturn rect;\n\t\t}\n\t}]);\n\n\treturn SVGRenderer;\n}();\n\nexports.default = SVGRenderer;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ObjectRenderer = function () {\n\tfunction ObjectRenderer(object, encodings, options) {\n\t\t_classCallCheck(this, ObjectRenderer);\n\n\t\tthis.object = object;\n\t\tthis.encodings = encodings;\n\t\tthis.options = options;\n\t}\n\n\t_createClass(ObjectRenderer, [{\n\t\tkey: \"render\",\n\t\tvalue: function render() {\n\t\t\tthis.object.encodings = this.encodings;\n\t\t}\n\t}]);\n\n\treturn ObjectRenderer;\n}();\n\nexports.default = ObjectRenderer;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _canvas = require('./canvas.js');\n\nvar _canvas2 = _interopRequireDefault(_canvas);\n\nvar _svg = require('./svg.js');\n\nvar _svg2 = _interopRequireDefault(_svg);\n\nvar _object = require('./object.js');\n\nvar _object2 = _interopRequireDefault(_object);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = { CanvasRenderer: _canvas2.default, SVGRenderer: _svg2.default, ObjectRenderer: _object2.default };", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar InvalidInputException = function (_Error) {\n\t_inherits(InvalidInputException, _Error);\n\n\tfunction InvalidInputException(symbology, input) {\n\t\t_classCallCheck(this, InvalidInputException);\n\n\t\tvar _this = _possibleConstructorReturn(this, (InvalidInputException.__proto__ || Object.getPrototypeOf(InvalidInputException)).call(this));\n\n\t\t_this.name = \"InvalidInputException\";\n\n\t\t_this.symbology = symbology;\n\t\t_this.input = input;\n\n\t\t_this.message = '\"' + _this.input + '\" is not a valid input for ' + _this.symbology;\n\t\treturn _this;\n\t}\n\n\treturn InvalidInputException;\n}(Error);\n\nvar InvalidElementException = function (_Error2) {\n\t_inherits(InvalidElementException, _Error2);\n\n\tfunction InvalidElementException() {\n\t\t_classCallCheck(this, InvalidElementException);\n\n\t\tvar _this2 = _possibleConstructorReturn(this, (InvalidElementException.__proto__ || Object.getPrototypeOf(InvalidElementException)).call(this));\n\n\t\t_this2.name = \"InvalidElementException\";\n\t\t_this2.message = \"Not supported type to render on\";\n\t\treturn _this2;\n\t}\n\n\treturn InvalidElementException;\n}(Error);\n\nvar NoElementException = function (_Error3) {\n\t_inherits(NoElementException, _Error3);\n\n\tfunction NoElementException() {\n\t\t_classCallCheck(this, NoElementException);\n\n\t\tvar _this3 = _possibleConstructorReturn(this, (NoElementException.__proto__ || Object.getPrototypeOf(NoElementException)).call(this));\n\n\t\t_this3.name = \"NoElementException\";\n\t\t_this3.message = \"No element to render on.\";\n\t\treturn _this3;\n\t}\n\n\treturn NoElementException;\n}(Error);\n\nexports.InvalidInputException = InvalidInputException;\nexports.InvalidElementException = InvalidElementException;\nexports.NoElementException = NoElementException;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; /* global HTMLImageElement */\n/* global HTMLCanvasElement */\n/* global SVGElement */\n\nvar _getOptionsFromElement = require(\"./getOptionsFromElement.js\");\n\nvar _getOptionsFromElement2 = _interopRequireDefault(_getOptionsFromElement);\n\nvar _renderers = require(\"../renderers\");\n\nvar _renderers2 = _interopRequireDefault(_renderers);\n\nvar _exceptions = require(\"../exceptions/exceptions.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// Takes an element and returns an object with information about how\n// it should be rendered\n// This could also return an array with these objects\n// {\n//   element: The element that the renderer should draw on\n//   renderer: The name of the renderer\n//   afterRender (optional): If something has to done after the renderer\n//     completed, calls afterRender (function)\n//   options (optional): Options that can be defined in the element\n// }\n\nfunction getRenderProperties(element) {\n\t// If the element is a string, query select call again\n\tif (typeof element === \"string\") {\n\t\treturn querySelectedRenderProperties(element);\n\t}\n\t// If element is array. Recursivly call with every object in the array\n\telse if (Array.isArray(element)) {\n\t\t\tvar returnArray = [];\n\t\t\tfor (var i = 0; i < element.length; i++) {\n\t\t\t\treturnArray.push(getRenderProperties(element[i]));\n\t\t\t}\n\t\t\treturn returnArray;\n\t\t}\n\t\t// If element, render on canvas and set the uri as src\n\t\telse if (typeof HTMLCanvasElement !== 'undefined' && element instanceof HTMLImageElement) {\n\t\t\t\treturn newCanvasRenderProperties(element);\n\t\t\t}\n\t\t\t// If SVG\n\t\t\telse if (element && element.nodeName && element.nodeName.toLowerCase() === 'svg' || typeof SVGElement !== 'undefined' && element instanceof SVGElement) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\telement: element,\n\t\t\t\t\t\toptions: (0, _getOptionsFromElement2.default)(element),\n\t\t\t\t\t\trenderer: _renderers2.default.SVGRenderer\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t// If canvas (in browser)\n\t\t\t\telse if (typeof HTMLCanvasElement !== 'undefined' && element instanceof HTMLCanvasElement) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\telement: element,\n\t\t\t\t\t\t\toptions: (0, _getOptionsFromElement2.default)(element),\n\t\t\t\t\t\t\trenderer: _renderers2.default.CanvasRenderer\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\t// If canvas (in node)\n\t\t\t\t\telse if (element && element.getContext) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\telement: element,\n\t\t\t\t\t\t\t\trenderer: _renderers2.default.CanvasRenderer\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t} else if (element && (typeof element === \"undefined\" ? \"undefined\" : _typeof(element)) === 'object' && !element.nodeName) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\telement: element,\n\t\t\t\t\t\t\t\trenderer: _renderers2.default.ObjectRenderer\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrow new _exceptions.InvalidElementException();\n\t\t\t\t\t\t}\n}\n\nfunction querySelectedRenderProperties(string) {\n\tvar selector = document.querySelectorAll(string);\n\tif (selector.length === 0) {\n\t\treturn undefined;\n\t} else {\n\t\tvar returnArray = [];\n\t\tfor (var i = 0; i < selector.length; i++) {\n\t\t\treturnArray.push(getRenderProperties(selector[i]));\n\t\t}\n\t\treturn returnArray;\n\t}\n}\n\nfunction newCanvasRenderProperties(imgElement) {\n\tvar canvas = document.createElement('canvas');\n\treturn {\n\t\telement: canvas,\n\t\toptions: (0, _getOptionsFromElement2.default)(imgElement),\n\t\trenderer: _renderers2.default.CanvasRenderer,\n\t\tafterRender: function afterRender() {\n\t\t\timgElement.setAttribute(\"src\", canvas.toDataURL());\n\t\t}\n\t};\n}\n\nexports.default = getRenderProperties;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/*eslint no-console: 0 */\n\nvar ErrorHandler = function () {\n\tfunction ErrorHandler(api) {\n\t\t_classCallCheck(this, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>);\n\n\t\tthis.api = api;\n\t}\n\n\t_createClass(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [{\n\t\tkey: \"handleCatch\",\n\t\tvalue: function handleCatch(e) {\n\t\t\t// If babel supported extending of Error in a correct way instanceof would be used here\n\t\t\tif (e.name === \"InvalidInputException\") {\n\t\t\t\tif (this.api._options.valid !== this.api._defaults.valid) {\n\t\t\t\t\tthis.api._options.valid(false);\n\t\t\t\t} else {\n\t\t\t\t\tthrow e.message;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthrow e;\n\t\t\t}\n\n\t\t\tthis.api.render = function () {};\n\t\t}\n\t}, {\n\t\tkey: \"wrapBarcodeCall\",\n\t\tvalue: function wrapBarcodeCall(func) {\n\t\t\ttry {\n\t\t\t\tvar result = func.apply(undefined, arguments);\n\t\t\t\tthis.api._options.valid(true);\n\t\t\t\treturn result;\n\t\t\t} catch (e) {\n\t\t\t\tthis.handleCatch(e);\n\n\t\t\t\treturn this.api;\n\t\t\t}\n\t\t}\n\t}]);\n\n\treturn ErrorHandler;\n}();\n\nexports.default = ErrorHandler;", "'use strict';\n\nvar _barcodes = require('./barcodes/');\n\nvar _barcodes2 = _interopRequireDefault(_barcodes);\n\nvar _merge = require('./help/merge.js');\n\nvar _merge2 = _interopRequireDefault(_merge);\n\nvar _linearizeEncodings = require('./help/linearizeEncodings.js');\n\nvar _linearizeEncodings2 = _interopRequireDefault(_linearizeEncodings);\n\nvar _fixOptions = require('./help/fixOptions.js');\n\nvar _fixOptions2 = _interopRequireDefault(_fixOptions);\n\nvar _getRenderProperties = require('./help/getRenderProperties.js');\n\nvar _getRenderProperties2 = _interopRequireDefault(_getRenderProperties);\n\nvar _optionsFromStrings = require('./help/optionsFromStrings.js');\n\nvar _optionsFromStrings2 = _interopRequireDefault(_optionsFromStrings);\n\nvar _ErrorHandler = require('./exceptions/ErrorHandler.js');\n\nvar _ErrorHandler2 = _interopRequireDefault(_ErrorHandler);\n\nvar _exceptions = require('./exceptions/exceptions.js');\n\nvar _defaults = require('./options/defaults.js');\n\nvar _defaults2 = _interopRequireDefault(_defaults);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// The protype of the object returned from the JsBarcode() call\n\n\n// Help functions\nvar API = function API() {};\n\n// The first call of the library API\n// Will return an object with all barcodes calls and the data that is used\n// by the renderers\n\n\n// Default values\n\n\n// Exceptions\n// Import all the barcodes\nvar JsBarcode = function JsBarcode(element, text, options) {\n\tvar api = new API();\n\n\tif (typeof element === \"undefined\") {\n\t\tthrow Error(\"No element to render on was provided.\");\n\t}\n\n\t// Variables that will be pased through the API calls\n\tapi._renderProperties = (0, _getRenderProperties2.default)(element);\n\tapi._encodings = [];\n\tapi._options = _defaults2.default;\n\tapi._errorHandler = new _ErrorHandler2.default(api);\n\n\t// If text is set, use the simple syntax (render the barcode directly)\n\tif (typeof text !== \"undefined\") {\n\t\toptions = options || {};\n\n\t\tif (!options.format) {\n\t\t\toptions.format = autoSelectBarcode();\n\t\t}\n\n\t\tapi.options(options)[options.format](text, options).render();\n\t}\n\n\treturn api;\n};\n\n// To make tests work TODO: remove\nJsBarcode.getModule = function (name) {\n\treturn _barcodes2.default[name];\n};\n\n// Register all barcodes\nfor (var name in _barcodes2.default) {\n\tif (_barcodes2.default.hasOwnProperty(name)) {\n\t\t// Security check if the propery is a prototype property\n\t\tregisterBarcode(_barcodes2.default, name);\n\t}\n}\nfunction registerBarcode(barcodes, name) {\n\tAPI.prototype[name] = API.prototype[name.toUpperCase()] = API.prototype[name.toLowerCase()] = function (text, options) {\n\t\tvar api = this;\n\t\treturn api._errorHandler.wrapBarcodeCall(function () {\n\t\t\t// Ensure text is options.text\n\t\t\toptions.text = typeof options.text === 'undefined' ? undefined : '' + options.text;\n\n\t\t\tvar newOptions = (0, _merge2.default)(api._options, options);\n\t\t\tnewOptions = (0, _optionsFromStrings2.default)(newOptions);\n\t\t\tvar Encoder = barcodes[name];\n\t\t\tvar encoded = encode(text, Encoder, newOptions);\n\t\t\tapi._encodings.push(encoded);\n\n\t\t\treturn api;\n\t\t});\n\t};\n}\n\n// encode() handles the Encoder call and builds the binary string to be rendered\nfunction encode(text, Encoder, options) {\n\t// Ensure that text is a string\n\ttext = \"\" + text;\n\n\tvar encoder = new Encoder(text, options);\n\n\t// If the input is not valid for the encoder, throw error.\n\t// If the valid callback option is set, call it instead of throwing error\n\tif (!encoder.valid()) {\n\t\tthrow new _exceptions.InvalidInputException(encoder.constructor.name, text);\n\t}\n\n\t// Make a request for the binary data (and other infromation) that should be rendered\n\tvar encoded = encoder.encode();\n\n\t// Encodings can be nestled like [[1-1, 1-2], 2, [3-1, 3-2]\n\t// Convert to [1-1, 1-2, 2, 3-1, 3-2]\n\tencoded = (0, _linearizeEncodings2.default)(encoded);\n\n\t// Merge\n\tfor (var i = 0; i < encoded.length; i++) {\n\t\tencoded[i].options = (0, _merge2.default)(options, encoded[i].options);\n\t}\n\n\treturn encoded;\n}\n\nfunction autoSelectBarcode() {\n\t// If CODE128 exists. Use it\n\tif (_barcodes2.default[\"CODE128\"]) {\n\t\treturn \"CODE128\";\n\t}\n\n\t// Else, take the first (probably only) barcode\n\treturn Object.keys(_barcodes2.default)[0];\n}\n\n// Sets global encoder options\n// Added to the api by the JsBarcode function\nAPI.prototype.options = function (options) {\n\tthis._options = (0, _merge2.default)(this._options, options);\n\treturn this;\n};\n\n// Will create a blank space (usually in between barcodes)\nAPI.prototype.blank = function (size) {\n\tvar zeroes = new Array(size + 1).join(\"0\");\n\tthis._encodings.push({ data: zeroes });\n\treturn this;\n};\n\n// Initialize JsBarcode on all HTML elements defined.\nAPI.prototype.init = function () {\n\t// Should do nothing if no elements where found\n\tif (!this._renderProperties) {\n\t\treturn;\n\t}\n\n\t// Make sure renderProperies is an array\n\tif (!Array.isArray(this._renderProperties)) {\n\t\tthis._renderProperties = [this._renderProperties];\n\t}\n\n\tvar renderProperty;\n\tfor (var i in this._renderProperties) {\n\t\trenderProperty = this._renderProperties[i];\n\t\tvar options = (0, _merge2.default)(this._options, renderProperty.options);\n\n\t\tif (options.format == \"auto\") {\n\t\t\toptions.format = autoSelectBarcode();\n\t\t}\n\n\t\tthis._errorHandler.wrapBarcodeCall(function () {\n\t\t\tvar text = options.value;\n\t\t\tvar Encoder = _barcodes2.default[options.format.toUpperCase()];\n\t\t\tvar encoded = encode(text, Encoder, options);\n\n\t\t\trender(renderProperty, encoded, options);\n\t\t});\n\t}\n};\n\n// The render API call. Calls the real render function.\nAPI.prototype.render = function () {\n\tif (!this._renderProperties) {\n\t\tthrow new _exceptions.NoElementException();\n\t}\n\n\tif (Array.isArray(this._renderProperties)) {\n\t\tfor (var i = 0; i < this._renderProperties.length; i++) {\n\t\t\trender(this._renderProperties[i], this._encodings, this._options);\n\t\t}\n\t} else {\n\t\trender(this._renderProperties, this._encodings, this._options);\n\t}\n\n\treturn this;\n};\n\nAPI.prototype._defaults = _defaults2.default;\n\n// Prepares the encodings and calls the renderer\nfunction render(renderProperties, encodings, options) {\n\tencodings = (0, _linearizeEncodings2.default)(encodings);\n\n\tfor (var i = 0; i < encodings.length; i++) {\n\t\tencodings[i].options = (0, _merge2.default)(options, encodings[i].options);\n\t\t(0, _fixOptions2.default)(encodings[i].options);\n\t}\n\n\t(0, _fixOptions2.default)(options);\n\n\tvar Renderer = renderProperties.renderer;\n\tvar renderer = new Renderer(renderProperties.element, encodings, options);\n\trenderer.render();\n\n\tif (renderProperties.afterRender) {\n\t\trenderProperties.afterRender();\n\t}\n}\n\n// Export to browser\nif (typeof window !== \"undefined\") {\n\twindow.JsBarcode = JsBarcode;\n}\n\n// Export to jQuery\n/*global jQuery */\nif (typeof jQuery !== 'undefined') {\n\tjQuery.fn.JsBarcode = function (content, options) {\n\t\tvar elementArray = [];\n\t\tjQuery(this).each(function () {\n\t\t\telementArray.push(this);\n\t\t});\n\t\treturn JsBarcode(elementArray, content, options);\n\t};\n}\n\n// Export to commonJS\nmodule.exports = JsBarcode;", "// can-promise has a crash in some versions of react native that dont have\n// standard global objects\n// https://github.com/soldair/node-qrcode/issues/157\n\nmodule.exports = function () {\n  return typeof Promise === 'function' && Promise.prototype && Promise.prototype.then\n}\n", "let toSJISFunction\nconst CODEWORDS_COUNT = [\n  0, // Not used\n  26, 44, 70, 100, 134, 172, 196, 242, 292, 346,\n  404, 466, 532, 581, 655, 733, 815, 901, 991, 1085,\n  1156, 1258, 1364, 1474, 1588, 1706, 1828, 1921, 2051, 2185,\n  2323, 2465, 2611, 2761, 2876, 3034, 3196, 3362, 3532, 3706\n]\n\n/**\n * Returns the QR Code size for the specified version\n *\n * @param  {Number} version QR Code version\n * @return {Number}         size of QR code\n */\nexports.getSymbolSize = function getSymbolSize (version) {\n  if (!version) throw new Error('\"version\" cannot be null or undefined')\n  if (version < 1 || version > 40) throw new Error('\"version\" should be in range from 1 to 40')\n  return version * 4 + 17\n}\n\n/**\n * Returns the total number of codewords used to store data and EC information.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Data length in bits\n */\nexports.getSymbolTotalCodewords = function getSymbolTotalCodewords (version) {\n  return CODEWORDS_COUNT[version]\n}\n\n/**\n * Encode data with Bose-Chaudhuri-Hocquenghem\n *\n * @param  {Number} data Value to encode\n * @return {Number}      Encoded value\n */\nexports.getBCHDigit = function (data) {\n  let digit = 0\n\n  while (data !== 0) {\n    digit++\n    data >>>= 1\n  }\n\n  return digit\n}\n\nexports.setToSJISFunction = function setToSJISFunction (f) {\n  if (typeof f !== 'function') {\n    throw new Error('\"toSJISFunc\" is not a valid function.')\n  }\n\n  toSJISFunction = f\n}\n\nexports.isKanjiModeEnabled = function () {\n  return typeof toSJISFunction !== 'undefined'\n}\n\nexports.toSJIS = function toSJIS (kanji) {\n  return toSJISFunction(kanji)\n}\n", "exports.L = { bit: 1 }\nexports.M = { bit: 0 }\nexports.Q = { bit: 3 }\nexports.H = { bit: 2 }\n\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'l':\n    case 'low':\n      return exports.L\n\n    case 'm':\n    case 'medium':\n      return exports.M\n\n    case 'q':\n    case 'quartile':\n      return exports.Q\n\n    case 'h':\n    case 'high':\n      return exports.H\n\n    default:\n      throw new Error('Unknown EC Level: ' + string)\n  }\n}\n\nexports.isValid = function isValid (level) {\n  return level && typeof level.bit !== 'undefined' &&\n    level.bit >= 0 && level.bit < 4\n}\n\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n", "function BitBuffer () {\n  this.buffer = []\n  this.length = 0\n}\n\nBitBuffer.prototype = {\n\n  get: function (index) {\n    const bufIndex = Math.floor(index / 8)\n    return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) === 1\n  },\n\n  put: function (num, length) {\n    for (let i = 0; i < length; i++) {\n      this.putBit(((num >>> (length - i - 1)) & 1) === 1)\n    }\n  },\n\n  getLengthInBits: function () {\n    return this.length\n  },\n\n  putBit: function (bit) {\n    const bufIndex = Math.floor(this.length / 8)\n    if (this.buffer.length <= bufIndex) {\n      this.buffer.push(0)\n    }\n\n    if (bit) {\n      this.buffer[bufIndex] |= (0x80 >>> (this.length % 8))\n    }\n\n    this.length++\n  }\n}\n\nmodule.exports = BitBuffer\n", "/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix (size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0')\n  }\n\n  this.size = size\n  this.data = new Uint8Array(size * size)\n  this.reservedBit = new Uint8Array(size * size)\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  const index = row * this.size + col\n  this.data[index] = value\n  if (reserved) this.reservedBit[index] = true\n}\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col]\n}\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value\n}\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col]\n}\n\nmodule.exports = BitMatrix\n", "/**\n * Alignment pattern are fixed reference pattern in defined positions\n * in a matrix symbology, which enables the decode software to re-synchronise\n * the coordinate mapping of the image modules in the event of moderate amounts\n * of distortion of the image.\n *\n * Alignment patterns are present only in QR Code symbols of version 2 or larger\n * and their number depends on the symbol version.\n */\n\nconst getSymbolSize = require('./utils').getSymbolSize\n\n/**\n * Calculate the row/column coordinates of the center module of each alignment pattern\n * for the specified QR Code version.\n *\n * The alignment patterns are positioned symmetrically on either side of the diagonal\n * running from the top left corner of the symbol to the bottom right corner.\n *\n * Since positions are simmetrical only half of the coordinates are returned.\n * Each item of the array will represent in turn the x and y coordinate.\n * @see {@link getPositions}\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinate\n */\nexports.getRowColCoords = function getRowColCoords (version) {\n  if (version === 1) return []\n\n  const posCount = Math.floor(version / 7) + 2\n  const size = getSymbolSize(version)\n  const intervals = size === 145 ? 26 : Math.ceil((size - 13) / (2 * posCount - 2)) * 2\n  const positions = [size - 7] // Last coord is always (size - 7)\n\n  for (let i = 1; i < posCount - 1; i++) {\n    positions[i] = positions[i - 1] - intervals\n  }\n\n  positions.push(6) // First coord is always 6\n\n  return positions.reverse()\n}\n\n/**\n * Returns an array containing the positions of each alignment pattern.\n * Each array's element represent the center point of the pattern as (x, y) coordinates\n *\n * Coordinates are calculated expanding the row/column coordinates returned by {@link getRowColCoords}\n * and filtering out the items that overlaps with finder pattern\n *\n * @example\n * For a Version 7 symbol {@link getRowColCoords} returns values 6, 22 and 38.\n * The alignment patterns, therefore, are to be centered on (row, column)\n * positions (6,22), (22,6), (22,22), (22,38), (38,22), (38,38).\n * Note that the coordinates (6,6), (6,38), (38,6) are occupied by finder patterns\n * and are not therefore used for alignment patterns.\n *\n * let pos = getPositions(7)\n * // [[6,22], [22,6], [22,22], [22,38], [38,22], [38,38]]\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const coords = []\n  const pos = exports.getRowColCoords(version)\n  const posLength = pos.length\n\n  for (let i = 0; i < posLength; i++) {\n    for (let j = 0; j < posLength; j++) {\n      // Skip if position is occupied by finder patterns\n      if ((i === 0 && j === 0) || // top-left\n          (i === 0 && j === posLength - 1) || // bottom-left\n          (i === posLength - 1 && j === 0)) { // top-right\n        continue\n      }\n\n      coords.push([pos[i], pos[j]])\n    }\n  }\n\n  return coords\n}\n", "const getSymbolSize = require('./utils').getSymbolSize\nconst FINDER_PATTERN_SIZE = 7\n\n/**\n * Returns an array containing the positions of each finder pattern.\n * Each array's element represent the top-left point of the pattern as (x, y) coordinates\n *\n * @param  {Number} version QR Code version\n * @return {Array}          Array of coordinates\n */\nexports.getPositions = function getPositions (version) {\n  const size = getSymbolSize(version)\n\n  return [\n    // top-left\n    [0, 0],\n    // top-right\n    [size - FINDER_PATTERN_SIZE, 0],\n    // bottom-left\n    [0, size - FINDER_PATTERN_SIZE]\n  ]\n}\n", "/**\n * Data mask pattern reference\n * @type {Object}\n */\nexports.Patterns = {\n  PATTERN000: 0,\n  PATTERN001: 1,\n  PATTERN010: 2,\n  PATTERN011: 3,\n  PATTERN100: 4,\n  PATTERN101: 5,\n  PATTERN110: 6,\n  PATTERN111: 7\n}\n\n/**\n * Weighted penalty scores for the undesirable features\n * @type {Object}\n */\nconst PenaltyScores = {\n  N1: 3,\n  N2: 3,\n  N3: 40,\n  N4: 10\n}\n\n/**\n * Check if mask pattern value is valid\n *\n * @param  {Number}  mask    Mask pattern\n * @return {Boolean}         true if valid, false otherwise\n */\nexports.isValid = function isValid (mask) {\n  return mask != null && mask !== '' && !isNaN(mask) && mask >= 0 && mask <= 7\n}\n\n/**\n * Returns mask pattern from a value.\n * If value is not valid, returns undefined\n *\n * @param  {Number|String} value        Mask pattern value\n * @return {Number}                     Valid mask pattern or undefined\n */\nexports.from = function from (value) {\n  return exports.isValid(value) ? parseInt(value, 10) : undefined\n}\n\n/**\n* Find adjacent modules in row/column with the same color\n* and assign a penalty value.\n*\n* Points: N1 + i\n* i is the amount by which the number of adjacent modules of the same color exceeds 5\n*/\nexports.getPenaltyN1 = function getPenaltyN1 (data) {\n  const size = data.size\n  let points = 0\n  let sameCountCol = 0\n  let sameCountRow = 0\n  let lastCol = null\n  let lastRow = null\n\n  for (let row = 0; row < size; row++) {\n    sameCountCol = sameCountRow = 0\n    lastCol = lastRow = null\n\n    for (let col = 0; col < size; col++) {\n      let module = data.get(row, col)\n      if (module === lastCol) {\n        sameCountCol++\n      } else {\n        if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n        lastCol = module\n        sameCountCol = 1\n      }\n\n      module = data.get(col, row)\n      if (module === lastRow) {\n        sameCountRow++\n      } else {\n        if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n        lastRow = module\n        sameCountRow = 1\n      }\n    }\n\n    if (sameCountCol >= 5) points += PenaltyScores.N1 + (sameCountCol - 5)\n    if (sameCountRow >= 5) points += PenaltyScores.N1 + (sameCountRow - 5)\n  }\n\n  return points\n}\n\n/**\n * Find 2x2 blocks with the same color and assign a penalty value\n *\n * Points: N2 * (m - 1) * (n - 1)\n */\nexports.getPenaltyN2 = function getPenaltyN2 (data) {\n  const size = data.size\n  let points = 0\n\n  for (let row = 0; row < size - 1; row++) {\n    for (let col = 0; col < size - 1; col++) {\n      const last = data.get(row, col) +\n        data.get(row, col + 1) +\n        data.get(row + 1, col) +\n        data.get(row + 1, col + 1)\n\n      if (last === 4 || last === 0) points++\n    }\n  }\n\n  return points * PenaltyScores.N2\n}\n\n/**\n * Find 1:1:3:1:1 ratio (dark:light:dark:light:dark) pattern in row/column,\n * preceded or followed by light area 4 modules wide\n *\n * Points: N3 * number of pattern found\n */\nexports.getPenaltyN3 = function getPenaltyN3 (data) {\n  const size = data.size\n  let points = 0\n  let bitsCol = 0\n  let bitsRow = 0\n\n  for (let row = 0; row < size; row++) {\n    bitsCol = bitsRow = 0\n    for (let col = 0; col < size; col++) {\n      bitsCol = ((bitsCol << 1) & 0x7FF) | data.get(row, col)\n      if (col >= 10 && (bitsCol === 0x5D0 || bitsCol === 0x05D)) points++\n\n      bitsRow = ((bitsRow << 1) & 0x7FF) | data.get(col, row)\n      if (col >= 10 && (bitsRow === 0x5D0 || bitsRow === 0x05D)) points++\n    }\n  }\n\n  return points * PenaltyScores.N3\n}\n\n/**\n * Calculate proportion of dark modules in entire symbol\n *\n * Points: N4 * k\n *\n * k is the rating of the deviation of the proportion of dark modules\n * in the symbol from 50% in steps of 5%\n */\nexports.getPenaltyN4 = function getPenaltyN4 (data) {\n  let darkCount = 0\n  const modulesCount = data.data.length\n\n  for (let i = 0; i < modulesCount; i++) darkCount += data.data[i]\n\n  const k = Math.abs(Math.ceil((darkCount * 100 / modulesCount) / 5) - 10)\n\n  return k * PenaltyScores.N4\n}\n\n/**\n * Return mask value at given position\n *\n * @param  {Number} maskPattern Pattern reference value\n * @param  {Number} i           Row\n * @param  {Number} j           Column\n * @return {Boolean}            Mask value\n */\nfunction getMaskAt (maskPattern, i, j) {\n  switch (maskPattern) {\n    case exports.Patterns.PATTERN000: return (i + j) % 2 === 0\n    case exports.Patterns.PATTERN001: return i % 2 === 0\n    case exports.Patterns.PATTERN010: return j % 3 === 0\n    case exports.Patterns.PATTERN011: return (i + j) % 3 === 0\n    case exports.Patterns.PATTERN100: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 === 0\n    case exports.Patterns.PATTERN101: return (i * j) % 2 + (i * j) % 3 === 0\n    case exports.Patterns.PATTERN110: return ((i * j) % 2 + (i * j) % 3) % 2 === 0\n    case exports.Patterns.PATTERN111: return ((i * j) % 3 + (i + j) % 2) % 2 === 0\n\n    default: throw new Error('bad maskPattern:' + maskPattern)\n  }\n}\n\n/**\n * Apply a mask pattern to a BitMatrix\n *\n * @param  {Number}    pattern Pattern reference number\n * @param  {BitMatrix} data    BitMatrix data\n */\nexports.applyMask = function applyMask (pattern, data) {\n  const size = data.size\n\n  for (let col = 0; col < size; col++) {\n    for (let row = 0; row < size; row++) {\n      if (data.isReserved(row, col)) continue\n      data.xor(row, col, getMaskAt(pattern, row, col))\n    }\n  }\n}\n\n/**\n * Returns the best mask pattern for data\n *\n * @param  {BitMatrix} data\n * @return {Number} Mask pattern reference number\n */\nexports.getBestMask = function getBestMask (data, setupFormatFunc) {\n  const numPatterns = Object.keys(exports.Patterns).length\n  let bestPattern = 0\n  let lowerPenalty = Infinity\n\n  for (let p = 0; p < numPatterns; p++) {\n    setupFormatFunc(p)\n    exports.applyMask(p, data)\n\n    // Calculate penalty\n    const penalty =\n      exports.getPenaltyN1(data) +\n      exports.getPenaltyN2(data) +\n      exports.getPenaltyN3(data) +\n      exports.getPenaltyN4(data)\n\n    // Undo previously applied mask\n    exports.applyMask(p, data)\n\n    if (penalty < lowerPenalty) {\n      lowerPenalty = penalty\n      bestPattern = p\n    }\n  }\n\n  return bestPattern\n}\n", "const ECLevel = require('./error-correction-level')\r\n\r\nconst EC_BLOCKS_TABLE = [\r\n// L  M  Q  H\r\n  1, 1, 1, 1,\r\n  1, 1, 1, 1,\r\n  1, 1, 2, 2,\r\n  1, 2, 2, 4,\r\n  1, 2, 4, 4,\r\n  2, 4, 4, 4,\r\n  2, 4, 6, 5,\r\n  2, 4, 6, 6,\r\n  2, 5, 8, 8,\r\n  4, 5, 8, 8,\r\n  4, 5, 8, 11,\r\n  4, 8, 10, 11,\r\n  4, 9, 12, 16,\r\n  4, 9, 16, 16,\r\n  6, 10, 12, 18,\r\n  6, 10, 17, 16,\r\n  6, 11, 16, 19,\r\n  6, 13, 18, 21,\r\n  7, 14, 21, 25,\r\n  8, 16, 20, 25,\r\n  8, 17, 23, 25,\r\n  9, 17, 23, 34,\r\n  9, 18, 25, 30,\r\n  10, 20, 27, 32,\r\n  12, 21, 29, 35,\r\n  12, 23, 34, 37,\r\n  12, 25, 34, 40,\r\n  13, 26, 35, 42,\r\n  14, 28, 38, 45,\r\n  15, 29, 40, 48,\r\n  16, 31, 43, 51,\r\n  17, 33, 45, 54,\r\n  18, 35, 48, 57,\r\n  19, 37, 51, 60,\r\n  19, 38, 53, 63,\r\n  20, 40, 56, 66,\r\n  21, 43, 59, 70,\r\n  22, 45, 62, 74,\r\n  24, 47, 65, 77,\r\n  25, 49, 68, 81\r\n]\r\n\r\nconst EC_CODEWORDS_TABLE = [\r\n// L  M  Q  H\r\n  7, 10, 13, 17,\r\n  10, 16, 22, 28,\r\n  15, 26, 36, 44,\r\n  20, 36, 52, 64,\r\n  26, 48, 72, 88,\r\n  36, 64, 96, 112,\r\n  40, 72, 108, 130,\r\n  48, 88, 132, 156,\r\n  60, 110, 160, 192,\r\n  72, 130, 192, 224,\r\n  80, 150, 224, 264,\r\n  96, 176, 260, 308,\r\n  104, 198, 288, 352,\r\n  120, 216, 320, 384,\r\n  132, 240, 360, 432,\r\n  144, 280, 408, 480,\r\n  168, 308, 448, 532,\r\n  180, 338, 504, 588,\r\n  196, 364, 546, 650,\r\n  224, 416, 600, 700,\r\n  224, 442, 644, 750,\r\n  252, 476, 690, 816,\r\n  270, 504, 750, 900,\r\n  300, 560, 810, 960,\r\n  312, 588, 870, 1050,\r\n  336, 644, 952, 1110,\r\n  360, 700, 1020, 1200,\r\n  390, 728, 1050, 1260,\r\n  420, 784, 1140, 1350,\r\n  450, 812, 1200, 1440,\r\n  480, 868, 1290, 1530,\r\n  510, 924, 1350, 1620,\r\n  540, 980, 1440, 1710,\r\n  570, 1036, 1530, 1800,\r\n  570, 1064, 1590, 1890,\r\n  600, 1120, 1680, 1980,\r\n  630, 1204, 1770, 2100,\r\n  660, 1260, 1860, 2220,\r\n  720, 1316, 1950, 2310,\r\n  750, 1372, 2040, 2430\r\n]\r\n\r\n/**\r\n * Returns the number of error correction block that the QR Code should contain\r\n * for the specified version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction blocks\r\n */\r\nexports.getBlocksCount = function getBlocksCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_BLOCKS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the number of error correction codewords to use for the specified\r\n * version and error correction level.\r\n *\r\n * @param  {Number} version              QR Code version\r\n * @param  {Number} errorCorrectionLevel Error correction level\r\n * @return {Number}                      Number of error correction codewords\r\n */\r\nexports.getTotalCodewordsCount = function getTotalCodewordsCount (version, errorCorrectionLevel) {\r\n  switch (errorCorrectionLevel) {\r\n    case ECLevel.L:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 0]\r\n    case ECLevel.M:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 1]\r\n    case ECLevel.Q:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 2]\r\n    case ECLevel.H:\r\n      return EC_CODEWORDS_TABLE[(version - 1) * 4 + 3]\r\n    default:\r\n      return undefined\r\n  }\r\n}\r\n", "const EXP_TABLE = new Uint8Array(512)\nconst LOG_TABLE = new Uint8Array(256)\n/**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */\n;(function initTables () {\n  let x = 1\n  for (let i = 0; i < 255; i++) {\n    EXP_TABLE[i] = x\n    LOG_TABLE[x] = i\n\n    x <<= 1 // multiply by 2\n\n    // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n    // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n    if (x & 0x100) { // similar to x >= 256, but a lot faster (because 0x100 == 256)\n      x ^= 0x11D\n    }\n  }\n\n  // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n  // stay inside the bounds (because we will mainly use this table for the multiplication of\n  // two GF numbers, no more).\n  // @see {@link mul}\n  for (let i = 255; i < 512; i++) {\n    EXP_TABLE[i] = EXP_TABLE[i - 255]\n  }\n}())\n\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.log = function log (n) {\n  if (n < 1) throw new Error('log(' + n + ')')\n  return LOG_TABLE[n]\n}\n\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.exp = function exp (n) {\n  return EXP_TABLE[n]\n}\n\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */\nexports.mul = function mul (x, y) {\n  if (x === 0 || y === 0) return 0\n\n  // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n  // @see {@link initTables}\n  return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]]\n}\n", "const GF = require('./galois-field')\n\n/**\n * Multiplies two polynomials inside Galois Field\n *\n * @param  {Uint8Array} p1 Polynomial\n * @param  {Uint8Array} p2 Polynomial\n * @return {Uint8Array}    Product of p1 and p2\n */\nexports.mul = function mul (p1, p2) {\n  const coeff = new Uint8Array(p1.length + p2.length - 1)\n\n  for (let i = 0; i < p1.length; i++) {\n    for (let j = 0; j < p2.length; j++) {\n      coeff[i + j] ^= GF.mul(p1[i], p2[j])\n    }\n  }\n\n  return coeff\n}\n\n/**\n * Calculate the remainder of polynomials division\n *\n * @param  {Uint8Array} divident Polynomial\n * @param  {Uint8Array} divisor  Polynomial\n * @return {Uint8Array}          Remainder\n */\nexports.mod = function mod (divident, divisor) {\n  let result = new Uint8Array(divident)\n\n  while ((result.length - divisor.length) >= 0) {\n    const coeff = result[0]\n\n    for (let i = 0; i < divisor.length; i++) {\n      result[i] ^= GF.mul(divisor[i], coeff)\n    }\n\n    // remove all zeros from buffer head\n    let offset = 0\n    while (offset < result.length && result[offset] === 0) offset++\n    result = result.slice(offset)\n  }\n\n  return result\n}\n\n/**\n * Generate an irreducible generator polynomial of specified degree\n * (used by Reed-Solomon encoder)\n *\n * @param  {Number} degree Degree of the generator polynomial\n * @return {Uint8Array}    Buffer containing polynomial coefficients\n */\nexports.generateECPolynomial = function generateECPolynomial (degree) {\n  let poly = new Uint8Array([1])\n  for (let i = 0; i < degree; i++) {\n    poly = exports.mul(poly, new Uint8Array([1, GF.exp(i)]))\n  }\n\n  return poly\n}\n", "const Polynomial = require('./polynomial')\n\nfunction ReedSolomonEncoder (degree) {\n  this.genPoly = undefined\n  this.degree = degree\n\n  if (this.degree) this.initialize(this.degree)\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize (degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree\n  this.genPoly = Polynomial.generateECPolynomial(this.degree)\n}\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Uint8Array} data Buffer containing input data\n * @return {Uint8Array}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode (data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized')\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  const paddedData = new Uint8Array(data.length + this.degree)\n  paddedData.set(data)\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  const remainder = Polynomial.mod(paddedData, this.genPoly)\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  const start = this.degree - remainder.length\n  if (start > 0) {\n    const buff = new Uint8Array(this.degree)\n    buff.set(remainder, start)\n\n    return buff\n  }\n\n  return remainder\n}\n\nmodule.exports = ReedSolomonEncoder\n", "/**\n * Check if QR Code version is valid\n *\n * @param  {Number}  version QR Code version\n * @return {Boolean}         true if valid version, false otherwise\n */\nexports.isValid = function isValid (version) {\n  return !isNaN(version) && version >= 1 && version <= 40\n}\n", "const numeric = '[0-9]+'\nconst alphanumeric = '[A-Z $%*+\\\\-./:]+'\nlet kanji = '(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|' +\n  '[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|' +\n  '[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|' +\n  '[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+'\nkanji = kanji.replace(/u/g, '\\\\u')\n\nconst byte = '(?:(?![A-Z0-9 $%*+\\\\-./:]|' + kanji + ')(?:.|[\\r\\n]))+'\n\nexports.KANJI = new RegExp(kanji, 'g')\nexports.BYTE_KANJI = new RegExp('[^A-Z0-9 $%*+\\\\-./:]+', 'g')\nexports.BYTE = new RegExp(byte, 'g')\nexports.NUMERIC = new RegExp(numeric, 'g')\nexports.ALPHANUMERIC = new RegExp(alphanumeric, 'g')\n\nconst TEST_KANJI = new RegExp('^' + kanji + '$')\nconst TEST_NUMERIC = new RegExp('^' + numeric + '$')\nconst TEST_ALPHANUMERIC = new RegExp('^[A-Z0-9 $%*+\\\\-./:]+$')\n\nexports.testKanji = function testKanji (str) {\n  return TEST_KANJI.test(str)\n}\n\nexports.testNumeric = function testNumeric (str) {\n  return TEST_NUMERIC.test(str)\n}\n\nexports.testAlphanumeric = function testAlphanumeric (str) {\n  return TEST_ALPHANUMERIC.test(str)\n}\n", "const VersionCheck = require('./version-check')\nconst Regex = require('./regex')\n\n/**\n * Numeric mode encodes data from the decimal digit set (0 - 9)\n * (byte values 30HEX to 39HEX).\n * Normally, 3 data characters are represented by 10 bits.\n *\n * @type {Object}\n */\nexports.NUMERIC = {\n  id: 'Numeric',\n  bit: 1 << 0,\n  ccBits: [10, 12, 14]\n}\n\n/**\n * Alphanumeric mode encodes data from a set of 45 characters,\n * i.e. 10 numeric digits (0 - 9),\n *      26 alphabetic characters (A - Z),\n *   and 9 symbols (SP, $, %, *, +, -, ., /, :).\n * Normally, two input characters are represented by 11 bits.\n *\n * @type {Object}\n */\nexports.ALPHANUMERIC = {\n  id: 'Alphanumeric',\n  bit: 1 << 1,\n  ccBits: [9, 11, 13]\n}\n\n/**\n * In byte mode, data is encoded at 8 bits per character.\n *\n * @type {Object}\n */\nexports.BYTE = {\n  id: 'Byte',\n  bit: 1 << 2,\n  ccBits: [8, 16, 16]\n}\n\n/**\n * The Kanji mode efficiently encodes Kanji characters in accordance with\n * the Shift JIS system based on JIS X 0208.\n * The Shift JIS values are shifted from the JIS X 0208 values.\n * JIS X 0208 gives details of the shift coded representation.\n * Each two-byte character value is compacted to a 13-bit binary codeword.\n *\n * @type {Object}\n */\nexports.KANJI = {\n  id: 'Kanji',\n  bit: 1 << 3,\n  ccBits: [8, 10, 12]\n}\n\n/**\n * Mixed mode will contain a sequences of data in a combination of any of\n * the modes described above\n *\n * @type {Object}\n */\nexports.MIXED = {\n  bit: -1\n}\n\n/**\n * Returns the number of bits needed to store the data length\n * according to QR Code specifications.\n *\n * @param  {Mode}   mode    Data mode\n * @param  {Number} version QR Code version\n * @return {Number}         Number of bits\n */\nexports.getCharCountIndicator = function getCharCountIndicator (mode, version) {\n  if (!mode.ccBits) throw new Error('Invalid mode: ' + mode)\n\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid version: ' + version)\n  }\n\n  if (version >= 1 && version < 10) return mode.ccBits[0]\n  else if (version < 27) return mode.ccBits[1]\n  return mode.ccBits[2]\n}\n\n/**\n * Returns the most efficient mode to store the specified data\n *\n * @param  {String} dataStr Input data string\n * @return {Mode}           Best mode\n */\nexports.getBestModeForData = function getBestModeForData (dataStr) {\n  if (Regex.testNumeric(dataStr)) return exports.NUMERIC\n  else if (Regex.testAlphanumeric(dataStr)) return exports.ALPHANUMERIC\n  else if (Regex.testKanji(dataStr)) return exports.KANJI\n  else return exports.BYTE\n}\n\n/**\n * Return mode name as string\n *\n * @param {Mode} mode Mode object\n * @returns {String}  Mode name\n */\nexports.toString = function toString (mode) {\n  if (mode && mode.id) return mode.id\n  throw new Error('Invalid mode')\n}\n\n/**\n * Check if input param is a valid mode object\n *\n * @param   {Mode}    mode Mode object\n * @returns {Boolean} True if valid mode, false otherwise\n */\nexports.isValid = function isValid (mode) {\n  return mode && mode.bit && mode.ccBits\n}\n\n/**\n * Get mode object from its name\n *\n * @param   {String} string Mode name\n * @returns {Mode}          Mode object\n */\nfunction fromString (string) {\n  if (typeof string !== 'string') {\n    throw new Error('Param is not a string')\n  }\n\n  const lcStr = string.toLowerCase()\n\n  switch (lcStr) {\n    case 'numeric':\n      return exports.NUMERIC\n    case 'alphanumeric':\n      return exports.ALPHANUMERIC\n    case 'kanji':\n      return exports.KANJI\n    case 'byte':\n      return exports.BYTE\n    default:\n      throw new Error('Unknown mode: ' + string)\n  }\n}\n\n/**\n * Returns mode from a value.\n * If value is not a valid mode, returns defaultValue\n *\n * @param  {Mode|String} value        Encoding mode\n * @param  {Mode}        defaultValue Fallback value\n * @return {Mode}                     Encoding mode\n */\nexports.from = function from (value, defaultValue) {\n  if (exports.isValid(value)) {\n    return value\n  }\n\n  try {\n    return fromString(value)\n  } catch (e) {\n    return defaultValue\n  }\n}\n", "const Utils = require('./utils')\nconst ECCode = require('./error-correction-code')\nconst ECLevel = require('./error-correction-level')\nconst Mode = require('./mode')\nconst VersionCheck = require('./version-check')\n\n// Generator polynomial used to encode version information\nconst G18 = (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0)\nconst G18_BCH = Utils.getBCHDigit(G18)\n\nfunction getBestVersionForDataLength (mode, length, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, mode)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\nfunction getReservedBitsCount (mode, version) {\n  // Character count indicator + mode indicator bits\n  return Mode.getCharCountIndicator(mode, version) + 4\n}\n\nfunction getTotalBitsFromDataArray (segments, version) {\n  let totalBits = 0\n\n  segments.forEach(function (data) {\n    const reservedBits = getReservedBitsCount(data.mode, version)\n    totalBits += reservedBits + data.getBitsLength()\n  })\n\n  return totalBits\n}\n\nfunction getBestVersionForMixedData (segments, errorCorrectionLevel) {\n  for (let currentVersion = 1; currentVersion <= 40; currentVersion++) {\n    const length = getTotalBitsFromDataArray(segments, currentVersion)\n    if (length <= exports.getCapacity(currentVersion, errorCorrectionLevel, Mode.MIXED)) {\n      return currentVersion\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns version number from a value.\n * If value is not a valid version, returns defaultValue\n *\n * @param  {Number|String} value        QR Code version\n * @param  {Number}        defaultValue Fallback value\n * @return {Number}                     QR Code version number\n */\nexports.from = function from (value, defaultValue) {\n  if (VersionCheck.isValid(value)) {\n    return parseInt(value, 10)\n  }\n\n  return defaultValue\n}\n\n/**\n * Returns how much data can be stored with the specified QR code version\n * and error correction level\n *\n * @param  {Number} version              QR Code version (1-40)\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Mode}   mode                 Data mode\n * @return {Number}                      Quantity of storable data\n */\nexports.getCapacity = function getCapacity (version, errorCorrectionLevel, mode) {\n  if (!VersionCheck.isValid(version)) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  // Use Byte mode as default\n  if (typeof mode === 'undefined') mode = Mode.BYTE\n\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  if (mode === Mode.MIXED) return dataTotalCodewordsBits\n\n  const usableBits = dataTotalCodewordsBits - getReservedBitsCount(mode, version)\n\n  // Return max number of storable codewords\n  switch (mode) {\n    case Mode.NUMERIC:\n      return Math.floor((usableBits / 10) * 3)\n\n    case Mode.ALPHANUMERIC:\n      return Math.floor((usableBits / 11) * 2)\n\n    case Mode.KANJI:\n      return Math.floor(usableBits / 13)\n\n    case Mode.BYTE:\n    default:\n      return Math.floor(usableBits / 8)\n  }\n}\n\n/**\n * Returns the minimum version needed to contain the amount of data\n *\n * @param  {Segment} data                    Segment of data\n * @param  {Number} [errorCorrectionLevel=H] Error correction level\n * @param  {Mode} mode                       Data mode\n * @return {Number}                          QR Code version\n */\nexports.getBestVersionForData = function getBestVersionForData (data, errorCorrectionLevel) {\n  let seg\n\n  const ecl = ECLevel.from(errorCorrectionLevel, ECLevel.M)\n\n  if (Array.isArray(data)) {\n    if (data.length > 1) {\n      return getBestVersionForMixedData(data, ecl)\n    }\n\n    if (data.length === 0) {\n      return 1\n    }\n\n    seg = data[0]\n  } else {\n    seg = data\n  }\n\n  return getBestVersionForDataLength(seg.mode, seg.getLength(), ecl)\n}\n\n/**\n * Returns version information with relative error correction bits\n *\n * The version information is included in QR Code symbols of version 7 or larger.\n * It consists of an 18-bit sequence containing 6 data bits,\n * with 12 error correction bits calculated using the (18, 6) Golay code.\n *\n * @param  {Number} version QR Code version\n * @return {Number}         Encoded version info bits\n */\nexports.getEncodedBits = function getEncodedBits (version) {\n  if (!VersionCheck.isValid(version) || version < 7) {\n    throw new Error('Invalid QR Code version')\n  }\n\n  let d = version << 12\n\n  while (Utils.getBCHDigit(d) - G18_BCH >= 0) {\n    d ^= (G18 << (Utils.getBCHDigit(d) - G18_BCH))\n  }\n\n  return (version << 12) | d\n}\n", "const Utils = require('./utils')\n\nconst G15 = (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0)\nconst G15_MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1)\nconst G15_BCH = Utils.getBCHDigit(G15)\n\n/**\n * Returns format information with relative error correction bits\n *\n * The format information is a 15-bit sequence containing 5 data bits,\n * with 10 error correction bits calculated using the (15, 5) BCH code.\n *\n * @param  {Number} errorCorrectionLevel Error correction level\n * @param  {Number} mask                 Mask pattern\n * @return {Number}                      Encoded format information bits\n */\nexports.getEncodedBits = function getEncodedBits (errorCorrectionLevel, mask) {\n  const data = ((errorCorrectionLevel.bit << 3) | mask)\n  let d = data << 10\n\n  while (Utils.getBCHDigit(d) - G15_BCH >= 0) {\n    d ^= (G15 << (Utils.getBCHDigit(d) - G15_BCH))\n  }\n\n  // xor final data with mask pattern in order to ensure that\n  // no combination of Error Correction Level and data mask pattern\n  // will result in an all-zero data string\n  return ((data << 10) | d) ^ G15_MASK\n}\n", "const Mode = require('./mode')\n\nfunction NumericData (data) {\n  this.mode = Mode.NUMERIC\n  this.data = data.toString()\n}\n\nNumericData.getBitsLength = function getBitsLength (length) {\n  return 10 * Math.floor(length / 3) + ((length % 3) ? ((length % 3) * 3 + 1) : 0)\n}\n\nNumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nNumericData.prototype.getBitsLength = function getBitsLength () {\n  return NumericData.getBitsLength(this.data.length)\n}\n\nNumericData.prototype.write = function write (bitBuffer) {\n  let i, group, value\n\n  // The input data string is divided into groups of three digits,\n  // and each group is converted to its 10-bit binary equivalent.\n  for (i = 0; i + 3 <= this.data.length; i += 3) {\n    group = this.data.substr(i, 3)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, 10)\n  }\n\n  // If the number of input digits is not an exact multiple of three,\n  // the final one or two digits are converted to 4 or 7 bits respectively.\n  const remainingNum = this.data.length - i\n  if (remainingNum > 0) {\n    group = this.data.substr(i)\n    value = parseInt(group, 10)\n\n    bitBuffer.put(value, remainingNum * 3 + 1)\n  }\n}\n\nmodule.exports = NumericData\n", "const Mode = require('./mode')\n\n/**\n * Array of characters available in alphanumeric mode\n *\n * As per QR Code specification, to each character\n * is assigned a value from 0 to 44 which in this case coincides\n * with the array index\n *\n * @type {Array}\n */\nconst ALPHA_NUM_CHARS = [\n  '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',\n  'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',\n  ' ', '$', '%', '*', '+', '-', '.', '/', ':'\n]\n\nfunction AlphanumericData (data) {\n  this.mode = Mode.ALPHANUMERIC\n  this.data = data\n}\n\nAlphanumericData.getBitsLength = function getBitsLength (length) {\n  return 11 * Math.floor(length / 2) + 6 * (length % 2)\n}\n\nAlphanumericData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nAlphanumericData.prototype.getBitsLength = function getBitsLength () {\n  return AlphanumericData.getBitsLength(this.data.length)\n}\n\nAlphanumericData.prototype.write = function write (bitBuffer) {\n  let i\n\n  // Input data characters are divided into groups of two characters\n  // and encoded as 11-bit binary codes.\n  for (i = 0; i + 2 <= this.data.length; i += 2) {\n    // The character value of the first character is multiplied by 45\n    let value = ALPHA_NUM_CHARS.indexOf(this.data[i]) * 45\n\n    // The character value of the second digit is added to the product\n    value += ALPHA_NUM_CHARS.indexOf(this.data[i + 1])\n\n    // The sum is then stored as 11-bit binary number\n    bitBuffer.put(value, 11)\n  }\n\n  // If the number of input data characters is not a multiple of two,\n  // the character value of the final character is encoded as a 6-bit binary number.\n  if (this.data.length % 2) {\n    bitBuffer.put(ALPHA_NUM_CHARS.indexOf(this.data[i]), 6)\n  }\n}\n\nmodule.exports = AlphanumericData\n", "const Mode = require('./mode')\n\nfunction ByteData (data) {\n  this.mode = Mode.BYTE\n  if (typeof (data) === 'string') {\n    this.data = new TextEncoder().encode(data)\n  } else {\n    this.data = new Uint8Array(data)\n  }\n}\n\nByteData.getBitsLength = function getBitsLength (length) {\n  return length * 8\n}\n\nByteData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nByteData.prototype.getBitsLength = function getBitsLength () {\n  return ByteData.getBitsLength(this.data.length)\n}\n\nByteData.prototype.write = function (bitBuffer) {\n  for (let i = 0, l = this.data.length; i < l; i++) {\n    bitBuffer.put(this.data[i], 8)\n  }\n}\n\nmodule.exports = ByteData\n", "const Mode = require('./mode')\nconst Utils = require('./utils')\n\nfunction KanjiData (data) {\n  this.mode = Mode.KANJI\n  this.data = data\n}\n\nKanjiData.getBitsLength = function getBitsLength (length) {\n  return length * 13\n}\n\nKanjiData.prototype.getLength = function getLength () {\n  return this.data.length\n}\n\nKanjiData.prototype.getBitsLength = function getBitsLength () {\n  return KanjiData.getBitsLength(this.data.length)\n}\n\nKanjiData.prototype.write = function (bitBuffer) {\n  let i\n\n  // In the Shift JIS system, Kanji characters are represented by a two byte combination.\n  // These byte values are shifted from the JIS X 0208 values.\n  // JIS X 0208 gives details of the shift coded representation.\n  for (i = 0; i < this.data.length; i++) {\n    let value = Utils.toSJIS(this.data[i])\n\n    // For characters with Shift JIS values from 0x8140 to 0x9FFC:\n    if (value >= 0x8140 && value <= 0x9FFC) {\n      // Subtract 0x8140 from Shift JIS value\n      value -= 0x8140\n\n    // For characters with Shift JIS values from 0xE040 to 0xEBBF\n    } else if (value >= 0xE040 && value <= 0xEBBF) {\n      // Subtract 0xC140 from Shift JIS value\n      value -= 0xC140\n    } else {\n      throw new Error(\n        'Invalid SJIS character: ' + this.data[i] + '\\n' +\n        'Make sure your charset is UTF-8')\n    }\n\n    // Multiply most significant byte of result by 0xC0\n    // and add least significant byte to product\n    value = (((value >>> 8) & 0xff) * 0xC0) + (value & 0xff)\n\n    // Convert result to a 13-bit binary string\n    bitBuffer.put(value, 13)\n  }\n}\n\nmodule.exports = KanjiData\n", "'use strict';\n\n/******************************************************************************\n * Created 2008-08-19.\n *\n * Dijkstra path-finding functions. Adapted from the Dijkstar Python project.\n *\n * Copyright (C) 2008\n *   <PERSON> <<EMAIL>>\n *   All rights reserved\n *\n * Licensed under the MIT license.\n *\n *   http://www.opensource.org/licenses/mit-license.php\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n *****************************************************************************/\nvar dijkstra = {\n  single_source_shortest_paths: function(graph, s, d) {\n    // Predecessor map for each node that has been encountered.\n    // node ID => predecessor node ID\n    var predecessors = {};\n\n    // Costs of shortest paths from s to all nodes encountered.\n    // node ID => cost\n    var costs = {};\n    costs[s] = 0;\n\n    // Costs of shortest paths from s to all nodes encountered; differs from\n    // `costs` in that it provides easy access to the node that currently has\n    // the known shortest path from s.\n    // XXX: Do we actually need both `costs` and `open`?\n    var open = dijkstra.PriorityQueue.make();\n    open.push(s, 0);\n\n    var closest,\n        u, v,\n        cost_of_s_to_u,\n        adjacent_nodes,\n        cost_of_e,\n        cost_of_s_to_u_plus_cost_of_e,\n        cost_of_s_to_v,\n        first_visit;\n    while (!open.empty()) {\n      // In the nodes remaining in graph that have a known cost from s,\n      // find the node, u, that currently has the shortest path from s.\n      closest = open.pop();\n      u = closest.value;\n      cost_of_s_to_u = closest.cost;\n\n      // Get nodes adjacent to u...\n      adjacent_nodes = graph[u] || {};\n\n      // ...and explore the edges that connect u to those nodes, updating\n      // the cost of the shortest paths to any or all of those nodes as\n      // necessary. v is the node across the current edge from u.\n      for (v in adjacent_nodes) {\n        if (adjacent_nodes.hasOwnProperty(v)) {\n          // Get the cost of the edge running from u to v.\n          cost_of_e = adjacent_nodes[v];\n\n          // Cost of s to u plus the cost of u to v across e--this is *a*\n          // cost from s to v that may or may not be less than the current\n          // known cost to v.\n          cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;\n\n          // If we haven't visited v yet OR if the current known cost from s to\n          // v is greater than the new cost we just found (cost of s to u plus\n          // cost of u to v across e), update v's cost in the cost list and\n          // update v's predecessor in the predecessor list (it's now u).\n          cost_of_s_to_v = costs[v];\n          first_visit = (typeof costs[v] === 'undefined');\n          if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {\n            costs[v] = cost_of_s_to_u_plus_cost_of_e;\n            open.push(v, cost_of_s_to_u_plus_cost_of_e);\n            predecessors[v] = u;\n          }\n        }\n      }\n    }\n\n    if (typeof d !== 'undefined' && typeof costs[d] === 'undefined') {\n      var msg = ['Could not find a path from ', s, ' to ', d, '.'].join('');\n      throw new Error(msg);\n    }\n\n    return predecessors;\n  },\n\n  extract_shortest_path_from_predecessor_list: function(predecessors, d) {\n    var nodes = [];\n    var u = d;\n    var predecessor;\n    while (u) {\n      nodes.push(u);\n      predecessor = predecessors[u];\n      u = predecessors[u];\n    }\n    nodes.reverse();\n    return nodes;\n  },\n\n  find_path: function(graph, s, d) {\n    var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);\n    return dijkstra.extract_shortest_path_from_predecessor_list(\n      predecessors, d);\n  },\n\n  /**\n   * A very naive priority queue implementation.\n   */\n  PriorityQueue: {\n    make: function (opts) {\n      var T = dijkstra.PriorityQueue,\n          t = {},\n          key;\n      opts = opts || {};\n      for (key in T) {\n        if (T.hasOwnProperty(key)) {\n          t[key] = T[key];\n        }\n      }\n      t.queue = [];\n      t.sorter = opts.sorter || T.default_sorter;\n      return t;\n    },\n\n    default_sorter: function (a, b) {\n      return a.cost - b.cost;\n    },\n\n    /**\n     * Add a new item to the queue and ensure the highest priority element\n     * is at the front of the queue.\n     */\n    push: function (value, cost) {\n      var item = {value: value, cost: cost};\n      this.queue.push(item);\n      this.queue.sort(this.sorter);\n    },\n\n    /**\n     * Return the highest priority element in the queue.\n     */\n    pop: function () {\n      return this.queue.shift();\n    },\n\n    empty: function () {\n      return this.queue.length === 0;\n    }\n  }\n};\n\n\n// node.js module exports\nif (typeof module !== 'undefined') {\n  module.exports = dijkstra;\n}\n", "const Mode = require('./mode')\nconst NumericData = require('./numeric-data')\nconst AlphanumericData = require('./alphanumeric-data')\nconst ByteData = require('./byte-data')\nconst KanjiData = require('./kanji-data')\nconst Regex = require('./regex')\nconst Utils = require('./utils')\nconst dijkstra = require('dijkstrajs')\n\n/**\n * Returns UTF8 byte length\n *\n * @param  {String} str Input string\n * @return {Number}     Number of byte\n */\nfunction getStringByteLength (str) {\n  return unescape(encodeURIComponent(str)).length\n}\n\n/**\n * Get a list of segments of the specified mode\n * from a string\n *\n * @param  {Mode}   mode Segment mode\n * @param  {String} str  String to process\n * @return {Array}       Array of object with segments data\n */\nfunction getSegments (regex, mode, str) {\n  const segments = []\n  let result\n\n  while ((result = regex.exec(str)) !== null) {\n    segments.push({\n      data: result[0],\n      index: result.index,\n      mode: mode,\n      length: result[0].length\n    })\n  }\n\n  return segments\n}\n\n/**\n * Extracts a series of segments with the appropriate\n * modes from a string\n *\n * @param  {String} dataStr Input string\n * @return {Array}          Array of object with segments data\n */\nfunction getSegmentsFromString (dataStr) {\n  const numSegs = getSegments(Regex.NUMERIC, Mode.NUMERIC, dataStr)\n  const alphaNumSegs = getSegments(Regex.ALPHANUMERIC, Mode.ALPHANUMERIC, dataStr)\n  let byteSegs\n  let kanjiSegs\n\n  if (Utils.isKanjiModeEnabled()) {\n    byteSegs = getSegments(Regex.BYTE, Mode.BYTE, dataStr)\n    kanjiSegs = getSegments(Regex.KANJI, Mode.KANJI, dataStr)\n  } else {\n    byteSegs = getSegments(Regex.BYTE_KANJI, Mode.BYTE, dataStr)\n    kanjiSegs = []\n  }\n\n  const segs = numSegs.concat(alphaNumSegs, byteSegs, kanjiSegs)\n\n  return segs\n    .sort(function (s1, s2) {\n      return s1.index - s2.index\n    })\n    .map(function (obj) {\n      return {\n        data: obj.data,\n        mode: obj.mode,\n        length: obj.length\n      }\n    })\n}\n\n/**\n * Returns how many bits are needed to encode a string of\n * specified length with the specified mode\n *\n * @param  {Number} length String length\n * @param  {Mode} mode     Segment mode\n * @return {Number}        Bit length\n */\nfunction getSegmentBitsLength (length, mode) {\n  switch (mode) {\n    case Mode.NUMERIC:\n      return NumericData.getBitsLength(length)\n    case Mode.ALPHANUMERIC:\n      return AlphanumericData.getBitsLength(length)\n    case Mode.KANJI:\n      return KanjiData.getBitsLength(length)\n    case Mode.BYTE:\n      return ByteData.getBitsLength(length)\n  }\n}\n\n/**\n * Merges adjacent segments which have the same mode\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction mergeSegments (segs) {\n  return segs.reduce(function (acc, curr) {\n    const prevSeg = acc.length - 1 >= 0 ? acc[acc.length - 1] : null\n    if (prevSeg && prevSeg.mode === curr.mode) {\n      acc[acc.length - 1].data += curr.data\n      return acc\n    }\n\n    acc.push(curr)\n    return acc\n  }, [])\n}\n\n/**\n * Generates a list of all possible nodes combination which\n * will be used to build a segments graph.\n *\n * Nodes are divided by groups. Each group will contain a list of all the modes\n * in which is possible to encode the given text.\n *\n * For example the text '12345' can be encoded as Numeric, Alphanumeric or Byte.\n * The group for '12345' will contain then 3 objects, one for each\n * possible encoding mode.\n *\n * Each node represents a possible segment.\n *\n * @param  {Array} segs Array of object with segments data\n * @return {Array}      Array of object with segments data\n */\nfunction buildNodes (segs) {\n  const nodes = []\n  for (let i = 0; i < segs.length; i++) {\n    const seg = segs[i]\n\n    switch (seg.mode) {\n      case Mode.NUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.ALPHANUMERIC, length: seg.length },\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.ALPHANUMERIC:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: seg.length }\n        ])\n        break\n      case Mode.KANJI:\n        nodes.push([seg,\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n        break\n      case Mode.BYTE:\n        nodes.push([\n          { data: seg.data, mode: Mode.BYTE, length: getStringByteLength(seg.data) }\n        ])\n    }\n  }\n\n  return nodes\n}\n\n/**\n * Builds a graph from a list of nodes.\n * All segments in each node group will be connected with all the segments of\n * the next group and so on.\n *\n * At each connection will be assigned a weight depending on the\n * segment's byte length.\n *\n * @param  {Array} nodes    Array of object with segments data\n * @param  {Number} version QR Code version\n * @return {Object}         Graph of all possible segments\n */\nfunction buildGraph (nodes, version) {\n  const table = {}\n  const graph = { start: {} }\n  let prevNodeIds = ['start']\n\n  for (let i = 0; i < nodes.length; i++) {\n    const nodeGroup = nodes[i]\n    const currentNodeIds = []\n\n    for (let j = 0; j < nodeGroup.length; j++) {\n      const node = nodeGroup[j]\n      const key = '' + i + j\n\n      currentNodeIds.push(key)\n      table[key] = { node: node, lastCount: 0 }\n      graph[key] = {}\n\n      for (let n = 0; n < prevNodeIds.length; n++) {\n        const prevNodeId = prevNodeIds[n]\n\n        if (table[prevNodeId] && table[prevNodeId].node.mode === node.mode) {\n          graph[prevNodeId][key] =\n            getSegmentBitsLength(table[prevNodeId].lastCount + node.length, node.mode) -\n            getSegmentBitsLength(table[prevNodeId].lastCount, node.mode)\n\n          table[prevNodeId].lastCount += node.length\n        } else {\n          if (table[prevNodeId]) table[prevNodeId].lastCount = node.length\n\n          graph[prevNodeId][key] = getSegmentBitsLength(node.length, node.mode) +\n            4 + Mode.getCharCountIndicator(node.mode, version) // switch cost\n        }\n      }\n    }\n\n    prevNodeIds = currentNodeIds\n  }\n\n  for (let n = 0; n < prevNodeIds.length; n++) {\n    graph[prevNodeIds[n]].end = 0\n  }\n\n  return { map: graph, table: table }\n}\n\n/**\n * Builds a segment from a specified data and mode.\n * If a mode is not specified, the more suitable will be used.\n *\n * @param  {String} data             Input data\n * @param  {Mode | String} modesHint Data mode\n * @return {Segment}                 Segment\n */\nfunction buildSingleSegment (data, modesHint) {\n  let mode\n  const bestMode = Mode.getBestModeForData(data)\n\n  mode = Mode.from(modesHint, bestMode)\n\n  // Make sure data can be encoded\n  if (mode !== Mode.BYTE && mode.bit < bestMode.bit) {\n    throw new Error('\"' + data + '\"' +\n      ' cannot be encoded with mode ' + Mode.toString(mode) +\n      '.\\n Suggested mode is: ' + Mode.toString(bestMode))\n  }\n\n  // Use Mode.BYTE if Kanji support is disabled\n  if (mode === Mode.KANJI && !Utils.isKanjiModeEnabled()) {\n    mode = Mode.BYTE\n  }\n\n  switch (mode) {\n    case Mode.NUMERIC:\n      return new NumericData(data)\n\n    case Mode.ALPHANUMERIC:\n      return new AlphanumericData(data)\n\n    case Mode.KANJI:\n      return new KanjiData(data)\n\n    case Mode.BYTE:\n      return new ByteData(data)\n  }\n}\n\n/**\n * Builds a list of segments from an array.\n * Array can contain Strings or Objects with segment's info.\n *\n * For each item which is a string, will be generated a segment with the given\n * string and the more appropriate encoding mode.\n *\n * For each item which is an object, will be generated a segment with the given\n * data and mode.\n * Objects must contain at least the property \"data\".\n * If property \"mode\" is not present, the more suitable mode will be used.\n *\n * @param  {Array} array Array of objects with segments data\n * @return {Array}       Array of Segments\n */\nexports.fromArray = function fromArray (array) {\n  return array.reduce(function (acc, seg) {\n    if (typeof seg === 'string') {\n      acc.push(buildSingleSegment(seg, null))\n    } else if (seg.data) {\n      acc.push(buildSingleSegment(seg.data, seg.mode))\n    }\n\n    return acc\n  }, [])\n}\n\n/**\n * Builds an optimized sequence of segments from a string,\n * which will produce the shortest possible bitstream.\n *\n * @param  {String} data    Input string\n * @param  {Number} version QR Code version\n * @return {Array}          Array of segments\n */\nexports.fromString = function fromString (data, version) {\n  const segs = getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n\n  const nodes = buildNodes(segs)\n  const graph = buildGraph(nodes, version)\n  const path = dijkstra.find_path(graph.map, 'start', 'end')\n\n  const optimizedSegs = []\n  for (let i = 1; i < path.length - 1; i++) {\n    optimizedSegs.push(graph.table[path[i]].node)\n  }\n\n  return exports.fromArray(mergeSegments(optimizedSegs))\n}\n\n/**\n * Splits a string in various segments with the modes which\n * best represent their content.\n * The produced segments are far from being optimized.\n * The output of this function is only used to estimate a QR Code version\n * which may contain the data.\n *\n * @param  {string} data Input string\n * @return {Array}       Array of segments\n */\nexports.rawSplit = function rawSplit (data) {\n  return exports.fromArray(\n    getSegmentsFromString(data, Utils.isKanjiModeEnabled())\n  )\n}\n", "const Utils = require('./utils')\nconst ECLevel = require('./error-correction-level')\nconst BitBuffer = require('./bit-buffer')\nconst BitMatrix = require('./bit-matrix')\nconst AlignmentPattern = require('./alignment-pattern')\nconst FinderPattern = require('./finder-pattern')\nconst MaskPattern = require('./mask-pattern')\nconst ECCode = require('./error-correction-code')\nconst ReedSolomonEncoder = require('./reed-solomon-encoder')\nconst Version = require('./version')\nconst FormatInfo = require('./format-info')\nconst Mode = require('./mode')\nconst Segments = require('./segments')\n\n/**\n * QRCode for JavaScript\n *\n * modified by <PERSON> for nodejs support\n * Copyright (c) 2011 Ryan Day\n *\n * Licensed under the MIT license:\n *   http://www.opensource.org/licenses/mit-license.php\n *\n//---------------------------------------------------------------------\n// QRCode for JavaScript\n//\n// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//   http://www.opensource.org/licenses/mit-license.php\n//\n// The word \"QR Code\" is registered trademark of\n// DENSO WAVE INCORPORATED\n//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n*/\n\n/**\n * Add finder patterns bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupFinderPattern (matrix, version) {\n  const size = matrix.size\n  const pos = FinderPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -1; r <= 7; r++) {\n      if (row + r <= -1 || size <= row + r) continue\n\n      for (let c = -1; c <= 7; c++) {\n        if (col + c <= -1 || size <= col + c) continue\n\n        if ((r >= 0 && r <= 6 && (c === 0 || c === 6)) ||\n          (c >= 0 && c <= 6 && (r === 0 || r === 6)) ||\n          (r >= 2 && r <= 4 && c >= 2 && c <= 4)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add timing pattern bits to matrix\n *\n * Note: this function must be called before {@link setupAlignmentPattern}\n *\n * @param  {BitMatrix} matrix Modules matrix\n */\nfunction setupTimingPattern (matrix) {\n  const size = matrix.size\n\n  for (let r = 8; r < size - 8; r++) {\n    const value = r % 2 === 0\n    matrix.set(r, 6, value, true)\n    matrix.set(6, r, value, true)\n  }\n}\n\n/**\n * Add alignment patterns bits to matrix\n *\n * Note: this function must be called after {@link setupTimingPattern}\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupAlignmentPattern (matrix, version) {\n  const pos = AlignmentPattern.getPositions(version)\n\n  for (let i = 0; i < pos.length; i++) {\n    const row = pos[i][0]\n    const col = pos[i][1]\n\n    for (let r = -2; r <= 2; r++) {\n      for (let c = -2; c <= 2; c++) {\n        if (r === -2 || r === 2 || c === -2 || c === 2 ||\n          (r === 0 && c === 0)) {\n          matrix.set(row + r, col + c, true, true)\n        } else {\n          matrix.set(row + r, col + c, false, true)\n        }\n      }\n    }\n  }\n}\n\n/**\n * Add version info bits to matrix\n *\n * @param  {BitMatrix} matrix  Modules matrix\n * @param  {Number}    version QR Code version\n */\nfunction setupVersionInfo (matrix, version) {\n  const size = matrix.size\n  const bits = Version.getEncodedBits(version)\n  let row, col, mod\n\n  for (let i = 0; i < 18; i++) {\n    row = Math.floor(i / 3)\n    col = i % 3 + size - 8 - 3\n    mod = ((bits >> i) & 1) === 1\n\n    matrix.set(row, col, mod, true)\n    matrix.set(col, row, mod, true)\n  }\n}\n\n/**\n * Add format info bits to matrix\n *\n * @param  {BitMatrix} matrix               Modules matrix\n * @param  {ErrorCorrectionLevel}    errorCorrectionLevel Error correction level\n * @param  {Number}    maskPattern          Mask pattern reference value\n */\nfunction setupFormatInfo (matrix, errorCorrectionLevel, maskPattern) {\n  const size = matrix.size\n  const bits = FormatInfo.getEncodedBits(errorCorrectionLevel, maskPattern)\n  let i, mod\n\n  for (i = 0; i < 15; i++) {\n    mod = ((bits >> i) & 1) === 1\n\n    // vertical\n    if (i < 6) {\n      matrix.set(i, 8, mod, true)\n    } else if (i < 8) {\n      matrix.set(i + 1, 8, mod, true)\n    } else {\n      matrix.set(size - 15 + i, 8, mod, true)\n    }\n\n    // horizontal\n    if (i < 8) {\n      matrix.set(8, size - i - 1, mod, true)\n    } else if (i < 9) {\n      matrix.set(8, 15 - i - 1 + 1, mod, true)\n    } else {\n      matrix.set(8, 15 - i - 1, mod, true)\n    }\n  }\n\n  // fixed module\n  matrix.set(size - 8, 8, 1, true)\n}\n\n/**\n * Add encoded data bits to matrix\n *\n * @param  {BitMatrix}  matrix Modules matrix\n * @param  {Uint8Array} data   Data codewords\n */\nfunction setupData (matrix, data) {\n  const size = matrix.size\n  let inc = -1\n  let row = size - 1\n  let bitIndex = 7\n  let byteIndex = 0\n\n  for (let col = size - 1; col > 0; col -= 2) {\n    if (col === 6) col--\n\n    while (true) {\n      for (let c = 0; c < 2; c++) {\n        if (!matrix.isReserved(row, col - c)) {\n          let dark = false\n\n          if (byteIndex < data.length) {\n            dark = (((data[byteIndex] >>> bitIndex) & 1) === 1)\n          }\n\n          matrix.set(row, col - c, dark)\n          bitIndex--\n\n          if (bitIndex === -1) {\n            byteIndex++\n            bitIndex = 7\n          }\n        }\n      }\n\n      row += inc\n\n      if (row < 0 || size <= row) {\n        row -= inc\n        inc = -inc\n        break\n      }\n    }\n  }\n}\n\n/**\n * Create encoded codewords from data input\n *\n * @param  {Number}   version              QR Code version\n * @param  {ErrorCorrectionLevel}   errorCorrectionLevel Error correction level\n * @param  {ByteData} data                 Data input\n * @return {Uint8Array}                    Buffer containing encoded codewords\n */\nfunction createData (version, errorCorrectionLevel, segments) {\n  // Prepare data buffer\n  const buffer = new BitBuffer()\n\n  segments.forEach(function (data) {\n    // prefix data with mode indicator (4 bits)\n    buffer.put(data.mode.bit, 4)\n\n    // Prefix data with character count indicator.\n    // The character count indicator is a string of bits that represents the\n    // number of characters that are being encoded.\n    // The character count indicator must be placed after the mode indicator\n    // and must be a certain number of bits long, depending on the QR version\n    // and data mode\n    // @see {@link Mode.getCharCountIndicator}.\n    buffer.put(data.getLength(), Mode.getCharCountIndicator(data.mode, version))\n\n    // add binary data sequence to buffer\n    data.write(buffer)\n  })\n\n  // Calculate required number of bits\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n  const dataTotalCodewordsBits = (totalCodewords - ecTotalCodewords) * 8\n\n  // Add a terminator.\n  // If the bit string is shorter than the total number of required bits,\n  // a terminator of up to four 0s must be added to the right side of the string.\n  // If the bit string is more than four bits shorter than the required number of bits,\n  // add four 0s to the end.\n  if (buffer.getLengthInBits() + 4 <= dataTotalCodewordsBits) {\n    buffer.put(0, 4)\n  }\n\n  // If the bit string is fewer than four bits shorter, add only the number of 0s that\n  // are needed to reach the required number of bits.\n\n  // After adding the terminator, if the number of bits in the string is not a multiple of 8,\n  // pad the string on the right with 0s to make the string's length a multiple of 8.\n  while (buffer.getLengthInBits() % 8 !== 0) {\n    buffer.putBit(0)\n  }\n\n  // Add pad bytes if the string is still shorter than the total number of required bits.\n  // Extend the buffer to fill the data capacity of the symbol corresponding to\n  // the Version and Error Correction Level by adding the Pad Codewords 11101100 (0xEC)\n  // and 00010001 (0x11) alternately.\n  const remainingByte = (dataTotalCodewordsBits - buffer.getLengthInBits()) / 8\n  for (let i = 0; i < remainingByte; i++) {\n    buffer.put(i % 2 ? 0x11 : 0xEC, 8)\n  }\n\n  return createCodewords(buffer, version, errorCorrectionLevel)\n}\n\n/**\n * Encode input data with Reed-Solomon and return codewords with\n * relative error correction bits\n *\n * @param  {BitBuffer} bitBuffer            Data to encode\n * @param  {Number}    version              QR Code version\n * @param  {ErrorCorrectionLevel} errorCorrectionLevel Error correction level\n * @return {Uint8Array}                     Buffer containing encoded codewords\n */\nfunction createCodewords (bitBuffer, version, errorCorrectionLevel) {\n  // Total codewords for this QR code version (Data + Error correction)\n  const totalCodewords = Utils.getSymbolTotalCodewords(version)\n\n  // Total number of error correction codewords\n  const ecTotalCodewords = ECCode.getTotalCodewordsCount(version, errorCorrectionLevel)\n\n  // Total number of data codewords\n  const dataTotalCodewords = totalCodewords - ecTotalCodewords\n\n  // Total number of blocks\n  const ecTotalBlocks = ECCode.getBlocksCount(version, errorCorrectionLevel)\n\n  // Calculate how many blocks each group should contain\n  const blocksInGroup2 = totalCodewords % ecTotalBlocks\n  const blocksInGroup1 = ecTotalBlocks - blocksInGroup2\n\n  const totalCodewordsInGroup1 = Math.floor(totalCodewords / ecTotalBlocks)\n\n  const dataCodewordsInGroup1 = Math.floor(dataTotalCodewords / ecTotalBlocks)\n  const dataCodewordsInGroup2 = dataCodewordsInGroup1 + 1\n\n  // Number of EC codewords is the same for both groups\n  const ecCount = totalCodewordsInGroup1 - dataCodewordsInGroup1\n\n  // Initialize a Reed-Solomon encoder with a generator polynomial of degree ecCount\n  const rs = new ReedSolomonEncoder(ecCount)\n\n  let offset = 0\n  const dcData = new Array(ecTotalBlocks)\n  const ecData = new Array(ecTotalBlocks)\n  let maxDataSize = 0\n  const buffer = new Uint8Array(bitBuffer.buffer)\n\n  // Divide the buffer into the required number of blocks\n  for (let b = 0; b < ecTotalBlocks; b++) {\n    const dataSize = b < blocksInGroup1 ? dataCodewordsInGroup1 : dataCodewordsInGroup2\n\n    // extract a block of data from buffer\n    dcData[b] = buffer.slice(offset, offset + dataSize)\n\n    // Calculate EC codewords for this data block\n    ecData[b] = rs.encode(dcData[b])\n\n    offset += dataSize\n    maxDataSize = Math.max(maxDataSize, dataSize)\n  }\n\n  // Create final data\n  // Interleave the data and error correction codewords from each block\n  const data = new Uint8Array(totalCodewords)\n  let index = 0\n  let i, r\n\n  // Add data codewords\n  for (i = 0; i < maxDataSize; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      if (i < dcData[r].length) {\n        data[index++] = dcData[r][i]\n      }\n    }\n  }\n\n  // Apped EC codewords\n  for (i = 0; i < ecCount; i++) {\n    for (r = 0; r < ecTotalBlocks; r++) {\n      data[index++] = ecData[r][i]\n    }\n  }\n\n  return data\n}\n\n/**\n * Build QR Code symbol\n *\n * @param  {String} data                 Input string\n * @param  {Number} version              QR Code version\n * @param  {ErrorCorretionLevel} errorCorrectionLevel Error level\n * @param  {MaskPattern} maskPattern     Mask pattern\n * @return {Object}                      Object containing symbol data\n */\nfunction createSymbol (data, version, errorCorrectionLevel, maskPattern) {\n  let segments\n\n  if (Array.isArray(data)) {\n    segments = Segments.fromArray(data)\n  } else if (typeof data === 'string') {\n    let estimatedVersion = version\n\n    if (!estimatedVersion) {\n      const rawSegments = Segments.rawSplit(data)\n\n      // Estimate best version that can contain raw splitted segments\n      estimatedVersion = Version.getBestVersionForData(rawSegments, errorCorrectionLevel)\n    }\n\n    // Build optimized segments\n    // If estimated version is undefined, try with the highest version\n    segments = Segments.fromString(data, estimatedVersion || 40)\n  } else {\n    throw new Error('Invalid data')\n  }\n\n  // Get the min version that can contain data\n  const bestVersion = Version.getBestVersionForData(segments, errorCorrectionLevel)\n\n  // If no version is found, data cannot be stored\n  if (!bestVersion) {\n    throw new Error('The amount of data is too big to be stored in a QR Code')\n  }\n\n  // If not specified, use min version as default\n  if (!version) {\n    version = bestVersion\n\n  // Check if the specified version can contain the data\n  } else if (version < bestVersion) {\n    throw new Error('\\n' +\n      'The chosen QR Code version cannot contain this amount of data.\\n' +\n      'Minimum version required to store current data is: ' + bestVersion + '.\\n'\n    )\n  }\n\n  const dataBits = createData(version, errorCorrectionLevel, segments)\n\n  // Allocate matrix buffer\n  const moduleCount = Utils.getSymbolSize(version)\n  const modules = new BitMatrix(moduleCount)\n\n  // Add function modules\n  setupFinderPattern(modules, version)\n  setupTimingPattern(modules)\n  setupAlignmentPattern(modules, version)\n\n  // Add temporary dummy bits for format info just to set them as reserved.\n  // This is needed to prevent these bits from being masked by {@link MaskPattern.applyMask}\n  // since the masking operation must be performed only on the encoding region.\n  // These blocks will be replaced with correct values later in code.\n  setupFormatInfo(modules, errorCorrectionLevel, 0)\n\n  if (version >= 7) {\n    setupVersionInfo(modules, version)\n  }\n\n  // Add data codewords\n  setupData(modules, dataBits)\n\n  if (isNaN(maskPattern)) {\n    // Find best mask pattern\n    maskPattern = MaskPattern.getBestMask(modules,\n      setupFormatInfo.bind(null, modules, errorCorrectionLevel))\n  }\n\n  // Apply mask pattern\n  MaskPattern.applyMask(maskPattern, modules)\n\n  // Replace format info bits with correct values\n  setupFormatInfo(modules, errorCorrectionLevel, maskPattern)\n\n  return {\n    modules: modules,\n    version: version,\n    errorCorrectionLevel: errorCorrectionLevel,\n    maskPattern: maskPattern,\n    segments: segments\n  }\n}\n\n/**\n * QR Code\n *\n * @param {String | Array} data                 Input data\n * @param {Object} options                      Optional configurations\n * @param {Number} options.version              QR Code version\n * @param {String} options.errorCorrectionLevel Error correction level\n * @param {Function} options.toSJISFunc         Helper func to convert utf8 to sjis\n */\nexports.create = function create (data, options) {\n  if (typeof data === 'undefined' || data === '') {\n    throw new Error('No input text')\n  }\n\n  let errorCorrectionLevel = ECLevel.M\n  let version\n  let mask\n\n  if (typeof options !== 'undefined') {\n    // Use higher error correction level as default\n    errorCorrectionLevel = ECLevel.from(options.errorCorrectionLevel, ECLevel.M)\n    version = Version.from(options.version)\n    mask = MaskPattern.from(options.maskPattern)\n\n    if (options.toSJISFunc) {\n      Utils.setToSJISFunction(options.toSJISFunc)\n    }\n  }\n\n  return createSymbol(data, version, errorCorrectionLevel, mask)\n}\n", "function hex2rgba (hex) {\n  if (typeof hex === 'number') {\n    hex = hex.toString()\n  }\n\n  if (typeof hex !== 'string') {\n    throw new Error('Color should be defined as hex string')\n  }\n\n  let hexCode = hex.slice().replace('#', '').split('')\n  if (hexCode.length < 3 || hexCode.length === 5 || hexCode.length > 8) {\n    throw new Error('Invalid hex color: ' + hex)\n  }\n\n  // Convert from short to long form (fff -> ffffff)\n  if (hexCode.length === 3 || hexCode.length === 4) {\n    hexCode = Array.prototype.concat.apply([], hexCode.map(function (c) {\n      return [c, c]\n    }))\n  }\n\n  // Add default alpha value\n  if (hexCode.length === 6) hexCode.push('F', 'F')\n\n  const hexValue = parseInt(hexCode.join(''), 16)\n\n  return {\n    r: (hexValue >> 24) & 255,\n    g: (hexValue >> 16) & 255,\n    b: (hexValue >> 8) & 255,\n    a: hexValue & 255,\n    hex: '#' + hexCode.slice(0, 6).join('')\n  }\n}\n\nexports.getOptions = function getOptions (options) {\n  if (!options) options = {}\n  if (!options.color) options.color = {}\n\n  const margin = typeof options.margin === 'undefined' ||\n    options.margin === null ||\n    options.margin < 0\n    ? 4\n    : options.margin\n\n  const width = options.width && options.width >= 21 ? options.width : undefined\n  const scale = options.scale || 4\n\n  return {\n    width: width,\n    scale: width ? 4 : scale,\n    margin: margin,\n    color: {\n      dark: hex2rgba(options.color.dark || '#000000ff'),\n      light: hex2rgba(options.color.light || '#ffffffff')\n    },\n    type: options.type,\n    rendererOpts: options.rendererOpts || {}\n  }\n}\n\nexports.getScale = function getScale (qrSize, opts) {\n  return opts.width && opts.width >= qrSize + opts.margin * 2\n    ? opts.width / (qrSize + opts.margin * 2)\n    : opts.scale\n}\n\nexports.getImageWidth = function getImageWidth (qrSize, opts) {\n  const scale = exports.getScale(qrSize, opts)\n  return Math.floor((qrSize + opts.margin * 2) * scale)\n}\n\nexports.qrToImageData = function qrToImageData (imgData, qr, opts) {\n  const size = qr.modules.size\n  const data = qr.modules.data\n  const scale = exports.getScale(size, opts)\n  const symbolSize = Math.floor((size + opts.margin * 2) * scale)\n  const scaledMargin = opts.margin * scale\n  const palette = [opts.color.light, opts.color.dark]\n\n  for (let i = 0; i < symbolSize; i++) {\n    for (let j = 0; j < symbolSize; j++) {\n      let posDst = (i * symbolSize + j) * 4\n      let pxColor = opts.color.light\n\n      if (i >= scaledMargin && j >= scaledMargin &&\n        i < symbolSize - scaledMargin && j < symbolSize - scaledMargin) {\n        const iSrc = Math.floor((i - scaledMargin) / scale)\n        const jSrc = Math.floor((j - scaledMargin) / scale)\n        pxColor = palette[data[iSrc * size + jSrc] ? 1 : 0]\n      }\n\n      imgData[posDst++] = pxColor.r\n      imgData[posDst++] = pxColor.g\n      imgData[posDst++] = pxColor.b\n      imgData[posDst] = pxColor.a\n    }\n  }\n}\n", "const Utils = require('./utils')\n\nfunction clearCanvas (ctx, canvas, size) {\n  ctx.clearRect(0, 0, canvas.width, canvas.height)\n\n  if (!canvas.style) canvas.style = {}\n  canvas.height = size\n  canvas.width = size\n  canvas.style.height = size + 'px'\n  canvas.style.width = size + 'px'\n}\n\nfunction getCanvasElement () {\n  try {\n    return document.createElement('canvas')\n  } catch (e) {\n    throw new Error('You need to specify a canvas element')\n  }\n}\n\nexports.render = function render (qrData, canvas, options) {\n  let opts = options\n  let canvasEl = canvas\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!canvas) {\n    canvasEl = getCanvasElement()\n  }\n\n  opts = Utils.getOptions(opts)\n  const size = Utils.getImageWidth(qrData.modules.size, opts)\n\n  const ctx = canvasEl.getContext('2d')\n  const image = ctx.createImageData(size, size)\n  Utils.qrToImageData(image.data, qrData, opts)\n\n  clearCanvas(ctx, canvasEl, size)\n  ctx.putImageData(image, 0, 0)\n\n  return canvasEl\n}\n\nexports.renderToDataURL = function renderToDataURL (qrData, canvas, options) {\n  let opts = options\n\n  if (typeof opts === 'undefined' && (!canvas || !canvas.getContext)) {\n    opts = canvas\n    canvas = undefined\n  }\n\n  if (!opts) opts = {}\n\n  const canvasEl = exports.render(qrData, canvas, opts)\n\n  const type = opts.type || 'image/png'\n  const rendererOpts = opts.rendererOpts || {}\n\n  return canvasEl.toDataURL(type, rendererOpts.quality)\n}\n", "const Utils = require('./utils')\n\nfunction getColorAttrib (color, attrib) {\n  const alpha = color.a / 255\n  const str = attrib + '=\"' + color.hex + '\"'\n\n  return alpha < 1\n    ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"'\n    : str\n}\n\nfunction svgCmd (cmd, x, y) {\n  let str = cmd + x\n  if (typeof y !== 'undefined') str += ' ' + y\n\n  return str\n}\n\nfunction qrToPath (data, size, margin) {\n  let path = ''\n  let moveBy = 0\n  let newRow = false\n  let lineLength = 0\n\n  for (let i = 0; i < data.length; i++) {\n    const col = Math.floor(i % size)\n    const row = Math.floor(i / size)\n\n    if (!col && !newRow) newRow = true\n\n    if (data[i]) {\n      lineLength++\n\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow\n          ? svgCmd('M', col + margin, 0.5 + row + margin)\n          : svgCmd('m', moveBy, 0)\n\n        moveBy = 0\n        newRow = false\n      }\n\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength)\n        lineLength = 0\n      }\n    } else {\n      moveBy++\n    }\n  }\n\n  return path\n}\n\nexports.render = function render (qrData, options, cb) {\n  const opts = Utils.getOptions(options)\n  const size = qrData.modules.size\n  const data = qrData.modules.data\n  const qrcodesize = size + opts.margin * 2\n\n  const bg = !opts.color.light.a\n    ? ''\n    : '<path ' + getColorAttrib(opts.color.light, 'fill') +\n      ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>'\n\n  const path =\n    '<path ' + getColorAttrib(opts.color.dark, 'stroke') +\n    ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>'\n\n  const viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"'\n\n  const width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" '\n\n  const svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n'\n\n  if (typeof cb === 'function') {\n    cb(null, svgTag)\n  }\n\n  return svgTag\n}\n", "\nconst canPromise = require('./can-promise')\n\nconst QRCode = require('./core/qrcode')\nconst CanvasRenderer = require('./renderer/canvas')\nconst SvgRenderer = require('./renderer/svg-tag.js')\n\nfunction renderCanvas (renderFunc, canvas, text, opts, cb) {\n  const args = [].slice.call(arguments, 1)\n  const argsNum = args.length\n  const isLastArgCb = typeof args[argsNum - 1] === 'function'\n\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument')\n  }\n\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 2) {\n      cb = text\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts\n        opts = undefined\n      } else {\n        cb = opts\n        opts = text\n        text = canvas\n        canvas = undefined\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided')\n    }\n\n    if (argsNum === 1) {\n      text = canvas\n      canvas = opts = undefined\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text\n      text = canvas\n      canvas = undefined\n    }\n\n    return new Promise(function (resolve, reject) {\n      try {\n        const data = QRCode.create(text, opts)\n        resolve(renderFunc(data, canvas, opts))\n      } catch (e) {\n        reject(e)\n      }\n    })\n  }\n\n  try {\n    const data = QRCode.create(text, opts)\n    cb(null, renderFunc(data, canvas, opts))\n  } catch (e) {\n    cb(e)\n  }\n}\n\nexports.create = QRCode.create\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render)\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL)\n\n// only svg for now.\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts)\n})\n", "import { defineComponent as J, toRefs as K, ref as a, computed as w, onMounted as Y, watch as Z, createElementBlock as y, openBlock as u, normalizeClass as g, unref as _, createElementVNode as C, normalizeStyle as E, createBlock as k, createCommentVNode as c, toDisplayString as q, withModifiers as z, renderSlot as ee } from \"vue\";\nimport R from \"./UIIcon.js\";\nimport te from \"./UILoading.js\";\nimport { colorToRgbo as V } from \"@perkd/applet-common/utilsColor\";\nconst le = {\n  key: 1,\n  class: \"button-title-container\"\n}, ne = { class: \"slide-thumb-content\" }, se = /* @__PURE__ */ J({\n  __name: \"UISlideButton\",\n  props: {\n    color: {\n      type: String,\n      default: \"accent\"\n    },\n    icon: {\n      type: Object,\n      required: !1\n    },\n    title: String,\n    titleClass: {\n      type: String,\n      default: \"\"\n    },\n    subtitle: String,\n    subtitleClass: {\n      type: String,\n      default: \"\"\n    },\n    startColor: {\n      type: String,\n      default: \"\"\n    },\n    endColor: {\n      type: String,\n      default: \"\"\n    },\n    threshold: {\n      type: Number,\n      default: 0.6\n    },\n    disabled: {\n      type: Boolean,\n      default: !1\n    },\n    proceeding: {\n      type: <PERSON>olean,\n      default: !1\n    }\n  },\n  emits: [\"confirmClick\", \"qualifyClick\"],\n  setup(e, { emit: I }) {\n    const N = e, { startColor: W, endColor: A, threshold: x, disabled: p, proceeding: F } = K(N), m = a(null), r = a(null), f = a(0), n = a(0), M = a(0), v = a(!1), h = a(F.value), d = a(null), S = a(!1), B = w(() => n.value >= f.value * x.value), j = w(() => {\n      const l = document.documentElement, t = V(W.value || getComputedStyle(l).getPropertyValue(\"--color-text-neutral\").trim() || \"#000000\"), o = V(A.value || getComputedStyle(l).getPropertyValue(\"--color-text-contrast\").trim() || \"#FFFFFF\"), i = n.value / (f.value * x.value), s = Math.floor(t.r + (o.r - t.r) * i), b = Math.floor(t.g + (o.g - t.g) * i), G = Math.floor(t.b + (o.b - t.b) * i), H = Math.floor(t.o + (o.o - t.o) * i);\n      return `rgb(${s}, ${b}, ${G}, ${H})`;\n    }), P = I;\n    Y(() => {\n      m.value && r.value && (f.value = m.value.offsetWidth - r.value.offsetWidth - r.value.offsetLeft * 2);\n    }), Z(F, (l, t) => {\n      l === !1 && t === !0 && (X(), h.value = !1);\n    });\n    function D(l) {\n      l.touches.length > 1 || p.value || (M.value = l.touches[0].clientX, v.value = !0);\n    }\n    function L(l) {\n      if (!m.value || !v.value || p.value) return;\n      d.value !== null && (cancelAnimationFrame(d.value), d.value = null);\n      const t = l.touches[0], o = m.value.getBoundingClientRect();\n      if (!U(o, t.clientX, t.clientY)) {\n        T();\n        return;\n      }\n      const s = t.clientX - M.value;\n      n.value = s > 0 ? Math.min(s, f.value) : 0, B.value || (S.value = !1), B.value && !S.value && (S.value = !0, P(\"qualifyClick\")), $(n.value);\n    }\n    function O() {\n      !v.value || !f.value || (d.value !== null && (cancelAnimationFrame(d.value), d.value = null), B.value ? Q() : T());\n    }\n    function T() {\n      X();\n    }\n    function Q() {\n      n.value = f.value, $(n.value), h.value = !0, v.value = !1, P(\"confirmClick\");\n    }\n    function X() {\n      n.value = 0, $(n.value), v.value = !1;\n    }\n    function U(l, t, o) {\n      const { left: i, top: s, height: b } = l;\n      return t >= i && o >= s - b / 2 && o <= s + b * 2;\n    }\n    function $(l) {\n      r.value && (r.value.style.transform = `translateX(${l}px)`);\n    }\n    return (l, t) => (u(), y(\"div\", {\n      ref_key: \"slideButtonRef\",\n      ref: m,\n      class: g([\"slide-button\", { active: v.value, proceeding: h.value, disabled: _(p) }])\n    }, [\n      C(\"div\", {\n        class: \"progress\",\n        style: E({ width: n.value ? `calc(${n.value}px + var(--size-button) / 2)` : 0 })\n      }, null, 4),\n      C(\"div\", {\n        class: \"slide-button-content\",\n        style: E({ color: j.value })\n      }, [\n        e.icon && e.icon.position !== \"right\" ? (u(), k(R, {\n          key: 0,\n          name: e.icon.name,\n          class: g(`button-icon ${e.icon.class || \"\"}`)\n        }, null, 8, [\"name\", \"class\"])) : c(\"\", !0),\n        e.title || e.subtitle ? (u(), y(\"span\", le, [\n          e.title ? (u(), y(\"span\", {\n            key: 0,\n            class: g(\"button-title \" + e.titleClass)\n          }, q(e.title), 3)) : c(\"\", !0),\n          e.subtitle ? (u(), y(\"span\", {\n            key: 1,\n            class: g(\"button-subtitle \" + e.subtitleClass)\n          }, q(e.subtitle), 3)) : c(\"\", !0)\n        ])) : c(\"\", !0),\n        e.icon && e.icon.position === \"right\" ? (u(), k(R, {\n          key: 2,\n          name: e.icon.name,\n          class: g(\"button-icon \" + e.icon.class)\n        }, null, 8, [\"name\", \"class\"])) : c(\"\", !0)\n      ], 4),\n      C(\"div\", {\n        ref_key: \"slideThumbRef\",\n        ref: r,\n        class: \"slide-thumb\",\n        onTouchstartPassive: D,\n        onTouchmovePassive: L,\n        onTouchend: z(O, [\"stop\", \"prevent\"]),\n        onTouchcancel: z(T, [\"stop\", \"prevent\"])\n      }, [\n        C(\"div\", ne, [\n          ee(l.$slots, \"status\", {}, () => [\n            h.value ? c(\"\", !0) : (u(), k(R, {\n              key: 0,\n              name: \"arrow-forward\"\n            })),\n            h.value ? (u(), k(te, {\n              key: 1,\n              colorBackground: \"\",\n              size: \"1.5em\",\n              thickness: \"xxs\"\n            })) : c(\"\", !0)\n          ])\n        ])\n      ], 544)\n    ], 2));\n  }\n});\nexport {\n  se as default\n};\n", "import { defineComponent as A, toRefs as D, ref as c, computed as R, watch as q, createElementBlock as a, openBlock as l, normalizeClass as y, createElementVNode as C, createCommentVNode as u, unref as p, createVNode as N, renderSlot as U, createTextVNode as S, toDisplayString as $, Fragment as G } from \"vue\";\nimport { useI18n as H } from \"vue-i18n\";\nimport x from \"./UIIcon.js\";\nconst J = { class: \"field-wrapper\" }, L = {\n  key: 0,\n  class: \"icon-container\"\n}, Q = { class: \"field-inner\" }, W = {\n  key: 0,\n  class: \"icon-container\"\n}, X = {\n  key: 0,\n  class: \"field-error error\"\n}, ee = /* @__PURE__ */ A({\n  __name: \"UIField\",\n  props: {\n    icon: {\n      type: Object,\n      required: !1\n    },\n    label: {\n      type: String,\n      default: \"\"\n    },\n    labelClass: {\n      type: String,\n      default: \"\"\n    },\n    disabled: {\n      type: Boolean,\n      default: !1\n    },\n    readonly: {\n      type: <PERSON><PERSON><PERSON>,\n      default: !1\n    },\n    required: {\n      type: Boolean,\n      default: !1\n    },\n    errorMessages: {\n      type: Object\n    },\n    showError: {\n      type: Number,\n      default: 0\n    }\n  },\n  emits: [\"focusChange\"],\n  setup(t, { expose: K, emit: M }) {\n    const z = t, { t: j } = H(), { label: d, errorMessages: o, showError: w } = D(z), f = c(!1), b = c(\"\"), h = c(\"\"), v = c(!0), r = c(w.value), P = R(() => (f.value || b.value) && !!d.value), B = M;\n    q(b, () => {\n      r.value = 0;\n    }), q(w, (e) => {\n      r.value = e;\n    });\n    function E(e) {\n      f.value = !0, B(\"focusChange\", e, !0);\n    }\n    function F(e) {\n      f.value = !1, B(\"focusChange\", e, !1);\n    }\n    function I(e) {\n      b.value = e;\n    }\n    function O(e, n) {\n      v.value = e, h.value = n;\n    }\n    function T() {\n      if (!r.value || v.value) return \"\";\n      let e = \"\", n = {};\n      const V = (s) => s.reduce((i, g) => {\n        const [k, ...m] = g.split(\":\");\n        return i[k] = m.join(\":\"), i;\n      }, {});\n      if (h.value) {\n        const [s, ...i] = h.value.split(\"|\");\n        if (e = `error.${s}`, Object.keys(i).length && Object.assign(n, V(i)), o != null && o.value && o.value[s]) {\n          const g = o.value[s] || \"\", [k, ...m] = g.split(\"|\");\n          e = `error.${k}`, Object.keys(m).length && Object.assign(n, V(m));\n        }\n      }\n      return e ? j(e, n) : j(\"error.invalid\");\n    }\n    return K({\n      onFocus: E,\n      onBlur: F,\n      onInput: I,\n      inputCheck: O\n    }), (e, n) => (l(), a(\"div\", {\n      class: y([\"field-container\", { disabled: t.disabled }, { readonly: t.readonly }, { active: P.value }, { focus: f.value }, { invalid: r.value > 0 && !v.value }])\n    }, [\n      C(\"div\", J, [\n        t.icon && !p(d) ? (l(), a(\"div\", L, [\n          N(x, {\n            name: t.icon.name,\n            class: y(t.icon.class)\n          }, null, 8, [\"name\", \"class\"])\n        ])) : u(\"\", !0),\n        C(\"div\", Q, [\n          U(e.$slots, \"default\", {\n            required: t.required,\n            disabled: t.disabled,\n            readonly: t.readonly,\n            onInputFocus: E,\n            onInputBlur: F,\n            onInputChange: I,\n            onInputCheck: O\n          }),\n          p(d) ? (l(), a(\"div\", {\n            key: 0,\n            class: y([\"label-container\", t.labelClass])\n          }, [\n            t.icon ? (l(), a(\"div\", W, [\n              N(x, {\n                name: t.icon.name,\n                class: y(t.icon.class)\n              }, null, 8, [\"name\", \"class\"])\n            ])) : u(\"\", !0),\n            C(\"label\", null, [\n              S($(p(d)), 1),\n              t.required ? (l(), a(G, { key: 0 }, [\n                S(\"*\")\n              ], 64)) : u(\"\", !0)\n            ])\n          ], 2)) : u(\"\", !0)\n        ])\n      ]),\n      r.value > 0 && !v.value ? (l(), a(\"div\", X, $(T()), 1)) : u(\"\", !0)\n    ], 2));\n  }\n});\nexport {\n  ee as default\n};\n", "import { defineComponent as B, toRefs as E, ref as l, computed as F, watch as M, createElementBlock as C, openBlock as P, createElementVNode as R } from \"vue\";\nimport { formatDateTime as a } from \"@perkd/format-datetime\";\nconst $ = [\"value\"], A = /* @__PURE__ */ B({\n  __name: \"UIInputDate\",\n  props: {\n    value: {\n      type: String,\n      default: \"\"\n    },\n    type: {\n      type: String,\n      default: \"\"\n    },\n    min: {\n      type: String,\n      default: \"\"\n    },\n    max: {\n      type: String,\n      default: \"\"\n    },\n    defaultDate: {\n      type: String,\n      default: \"\"\n    },\n    timeStamp: {\n      type: String,\n      default: \"\"\n    },\n    smartFormat: {\n      type: Boolean,\n      default: !1\n    },\n    openDateTimePicker: {\n      type: Function,\n      required: !0\n    }\n  },\n  emits: [\"input\", \"blur\", \"focus\", \"showError\"],\n  setup(w, { expose: D, emit: T }) {\n    const m = w, { type: u, value: c, min: r, max: n, timeStamp: d, defaultDate: x, smartFormat: I } = E(m), t = l(c.value), v = F(() => {\n      if (!t.value) return \"\";\n      if (u.value === \"time\") return a(t.value).format(\"LT\");\n      if (I.value) return a(t.value).smartDateTime(void 0, u.value === \"datetime\");\n      const e = u.value === \"date\" ? \"ll\" : \"LL\";\n      return a(t.value).format(e);\n    }), p = l(!1), i = l(void 0), S = l(\"\"), o = T;\n    M(c, (e) => {\n      t.value = e;\n    });\n    function L() {\n      p.value = !0, o(\"focus\", { target: i.value }), h();\n    }\n    function f() {\n      p.value = !1, o(\"blur\", { target: i.value });\n    }\n    function h() {\n      m.openDateTimePicker && m.openDateTimePicker({\n        value: t.value || x.value || g(void 0),\n        mode: u.value,\n        theme: window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\"\n      }).then((e) => {\n        O(e);\n      }).catch((e) => {\n        V(e);\n      });\n    }\n    function O(e) {\n      typeof e == \"string\" && (t.value = g(e), o(\"input\", { target: i.value }, t.value)), f();\n    }\n    function V(e) {\n      o(\"showError\", e), f();\n    }\n    function g(e) {\n      return d.value ? d.value === \"start\" ? a(e).startOf(\"d\").toISOString() : a(e).endOf(\"d\").toISOString() : e ? a(e).toISOString() : a().toISOString();\n    }\n    function b() {\n      const e = a(t.value || \"\"), s = e.isValid(), y = r.value ? e.isSame(r.value) || e.isAfter(r.value) : !0, _ = n.value ? e.isSame(n.value) || e.isBefore(n.value) : !0, k = u.value === \"date\" ? \"ll\" : u.value === \"datetime\" ? \"LL\" : \"LT\";\n      return S.value = s ? y ? _ ? \"\" : `after_maximum_date|date:${a(n.value).format(k)}` : `before_minimum_date|date:${a(r.value).format(k)}` : \"invalid_date\", s && y && _;\n    }\n    return D({\n      value: t,\n      formattedValue: v,\n      focus: L,\n      blur: f,\n      checkValidity: b,\n      validationMessage: S\n    }), (e, s) => (P(), C(\"div\", {\n      class: \"input-wrapper--date\",\n      ref_key: \"inputContainerRef\",\n      ref: i,\n      onClick: h\n    }, [\n      R(\"input\", {\n        readonly: \"\",\n        value: v.value\n      }, null, 8, $)\n    ], 512));\n  }\n});\nexport {\n  A as default\n};\n", "import { defineComponent as Z, toRefs as w, ref as b, shallowRef as F, defineAsyncComponent as R, h as _, computed as k, onMounted as ee, watch as ae, createElementBlock as te, openBlock as M, normalizeClass as ue, unref as n, createBlock as z, createCommentVNode as le, renderSlot as ne, resolveDynamicComponent as ie, mergeProps as oe, withModifiers as re } from \"vue\";\nimport { debounce as ce, isUrl as se, isEmail as ve } from \"@perkd/applet-common/utils\";\nimport de from \"./UIButton.js\";\nimport me from \"./UIInputDate.js\";\nconst he = /* @__PURE__ */ Z({\n  __name: \"UIInput\",\n  props: {\n    modelValue: {\n      type: String,\n      default: \"\"\n    },\n    inputType: {\n      type: String,\n      default: \"text\"\n    },\n    inputClass: {\n      type: String,\n      default: \"\"\n    },\n    inputmode: {\n      type: String,\n      default: \"\"\n    },\n    value: {\n      type: String,\n      default: \"\"\n    },\n    placeholder: {\n      type: String,\n      default: \"\"\n    },\n    clearable: {\n      type: Boolean,\n      default: !0\n    },\n    disabled: {\n      type: Boolean,\n      default: !1\n    },\n    readonly: {\n      type: Boolean,\n      default: !1\n    },\n    required: {\n      type: Boolean,\n      default: !1\n    },\n    autocapitalize: {\n      type: String,\n      default: \"off\"\n    },\n    autocomplete: {\n      type: String,\n      default: \"off\"\n    },\n    autocorrect: {\n      type: String,\n      default: \"off\"\n    },\n    spellcheck: {\n      type: Boolean,\n      default: !1\n    },\n    pattern: {\n      type: String,\n      default: \"\"\n    },\n    minlength: {\n      type: String,\n      default: \"\"\n    },\n    maxlength: {\n      type: String,\n      default: \"\"\n    },\n    min: {\n      type: String,\n      default: \"\"\n    },\n    max: {\n      type: String,\n      default: \"\"\n    },\n    inputOptions: {\n      type: Object,\n      default: () => ({})\n    },\n    validate: {\n      type: Function\n    }\n  },\n  emits: [\"update:modelValue\", \"inputFocus\", \"inputBlur\", \"inputChange\", \"inputCheck\"],\n  setup(v, { expose: T, emit: q }) {\n    const A = [\"tel\", \"date\", \"datetime\", \"time\", \"money\"], p = v, { disabled: B, required: x, readonly: P, clearable: U, inputType: d, value: N, modelValue: $, inputOptions: m } = w(p), { pattern: S, minlength: f, maxlength: y, min: g, max: h, inputmode: O } = w(p), e = b(($.value || N.value || \"\").trim()), o = F(void 0), C = F(void 0), r = b(!1), j = R({\n      loader: () => import(\"./UIInputMobile.js\"),\n      errorComponent: _(\"input\"),\n      // Create a virtual node for the input element\n      timeout: 3e3\n      // Add timeout\n    }), D = R({\n      loader: () => import(\"./UIInputMoney.js\"),\n      errorComponent: _(\"input\"),\n      timeout: 3e3\n    }), V = k(() => {\n      switch (d.value) {\n        case \"tel\":\n          return j;\n        case \"date\":\n        case \"datetime\":\n        case \"time\":\n          return me;\n        case \"money\":\n          return D;\n        case \"textarea\":\n          return \"textarea\";\n        default:\n          return \"input\";\n      }\n    }), c = b(\"\"), i = b(!0), L = k(() => {\n      var l;\n      return A.indexOf(d.value) !== -1 && ((l = o.value) == null ? void 0 : l.formattedValue) || e.value;\n    }), Y = k(() => {\n      var a;\n      return o.value && \"countryCode\" in o.value ? (a = o.value) == null ? void 0 : a.countryCode : \"\";\n    }), G = k(() => !B.value && U.value && !!e.value), u = q;\n    ee(() => {\n      var a, l;\n      if (u(\"inputChange\", e.value), u(\"update:modelValue\", e.value), C.value && ((a = m == null ? void 0 : m.value) != null && a.rows)) {\n        const t = getComputedStyle(C.value).fontSize;\n        C.value.style.height = t ? `calc(${t} * 1.21 * ${(l = m.value) == null ? void 0 : l.rows} + var(--height-input) * 0.33 + 0.7em)` : \"var(--height-input)\";\n      }\n    }), ae([$, N], ([a, l]) => {\n      var t;\n      e.value = (t = a || l) == null ? void 0 : t.trim(), i.value = !0, c.value = \"\", u(\"inputChange\", e.value), u(\"update:modelValue\", e.value);\n    });\n    const H = ce((a) => {\n      u(\"inputChange\", a), u(\"update:modelValue\", a);\n    }, 100);\n    function J(a, l) {\n      var s;\n      const t = a.target;\n      e.value = (s = l || t.value) == null ? void 0 : s.trim(), H(e.value);\n    }\n    function E() {\n      e.value = \"\", I(), u(\"inputChange\", \"\"), u(\"update:modelValue\", \"\");\n    }\n    function K(a) {\n      r.value || (r.value = !0, u(\"inputFocus\", a));\n    }\n    function Q(a) {\n      r.value && (r.value = !1, u(\"inputBlur\", a));\n    }\n    function I() {\n      var a;\n      r.value || (r.value = !0, (a = o.value) == null || a.focus());\n    }\n    function W() {\n      var a;\n      r.value && (r.value = !1, (a = o.value) == null || a.blur());\n    }\n    function X() {\n      const l = [\n        { check: () => !x.value || !!e.value, message: \"is_required\" },\n        { check: () => !e.value || !f.value || e.value.length >= Number(f.value), message: `minimum_length|n:${f.value}` },\n        { check: () => !e.value || !y.value || e.value.length <= Number(y.value), message: `maximum_length|n:${y.value}` },\n        { check: () => e.value && S.value ? new RegExp(S.value).test(e.value) : !0, message: \"invalid_pattern\" },\n        { check: () => !(V.value !== \"input\") && !!e.value && !isNaN(+e.value) && g.value ? Number(e.value) >= Number(g.value) : !0, message: `minimum_number|n:${g.value}` },\n        { check: () => !(V.value !== \"input\") && !!e.value && !isNaN(+e.value) && h.value ? Number(e.value) <= Number(h.value) : !0, message: `maximum_number|n:${h.value}` }\n      ].find((t) => !t.check());\n      if (l)\n        i.value = !1, c.value = l.message;\n      else {\n        const t = !x.value && !e.value;\n        switch (d.value) {\n          case \"email\":\n            i.value = t || ve(e.value), c.value = i.value ? \"\" : \"invalid\";\n            break;\n          case \"url\":\n            i.value = t || se(e.value), c.value = i.value ? \"\" : \"invalid\";\n            break;\n          case \"date\":\n          case \"datetime\":\n          case \"time\":\n          case \"tel\":\n          case \"money\":\n            o.value && (i.value = t || o.value.checkValidity(), c.value = i.value ? \"\" : o.value.validationMessage || \"\");\n            break;\n        }\n        if (p.validate) {\n          const s = p.validate(e.value);\n          i.value = s.isValid, c.value = s.isValid ? \"\" : s.validationMessage;\n        }\n      }\n      return u(\"inputCheck\", i.value, c.value), i.value;\n    }\n    return T({\n      value: e,\n      countryCode: Y,\n      formattedValue: L,\n      focus: I,\n      blur: W,\n      checkValidity: X,\n      validationMessage: c,\n      clear: E,\n      setValue: (a) => {\n        e.value = a, u(\"inputChange\", a), u(\"update:modelValue\", a);\n      }\n    }), (a, l) => (M(), te(\"div\", {\n      ref_key: \"inputContainerRef\",\n      ref: C,\n      class: ue(`input-container ${n(d)}-container`)\n    }, [\n      (M(), z(ie(V.value), oe({\n        ref_key: \"inputRef\",\n        ref: o,\n        value: e.value,\n        class: v.inputClass,\n        type: n(d)\n      }, { placeholder: v.placeholder, disabled: n(B), readonly: n(P), required: n(x), autocapitalize: v.autocapitalize, autocomplete: v.autocomplete, autocorrect: v.autocorrect, spellcheck: v.spellcheck, pattern: n(S), inputmode: n(O), minlength: n(f), maxlength: n(y), min: n(g), max: n(h), ...n(m) }, {\n        onFocus: K,\n        onBlur: Q,\n        onInput: J\n      }), null, 16, [\"value\", \"class\", \"type\"])),\n      G.value ? (M(), z(de, {\n        key: 0,\n        icon: { name: \"close\" },\n        type: \"circle\",\n        onClick: re(E, [\"stop\"]),\n        class: \"clear-button\",\n        \"aria-label\": \"Clear input\"\n      })) : le(\"\", !0),\n      ne(a.$slots, \"customButton\")\n    ], 2));\n  }\n});\nexport {\n  he as default\n};\n", "import { defineComponent as v, toRefs as C, ref as i, watch as y, createElementBlock as b, openBlock as s, normalizeClass as I, createElementVNode as a, withDirectives as q, unref as w, vModelCheckbox as B, createBlock as V, createCommentVNode as x, normalizeProps as g, mergeProps as R } from \"vue\";\nimport _ from \"./UIIcon.js\";\nconst j = { class: \"switch\" }, z = [\"required\", \"disabled\"], E = { class: \"switch-slider\" }, O = /* @__PURE__ */ v({\n  __name: \"UISwitch\",\n  props: {\n    checkedIcon: {\n      type: Object\n    },\n    unCheckedIcon: {\n      type: Object\n    },\n    checked: {\n      type: Boolean,\n      default: !1\n    },\n    required: {\n      type: Boolean,\n      default: !1\n    },\n    disabled: {\n      type: Boolean,\n      default: !1\n    }\n  },\n  emits: [\"update:modelValue\", \"inputChange\", \"inputCheck\"],\n  setup(c, { expose: u, emit: r }) {\n    const h = c, { checked: m, required: l } = C(h), e = i(m.value), p = i(void 0), n = i(\"\"), o = r;\n    y(e, (t) => {\n      o(\"inputChange\", t), o(\"update:modelValue\", t);\n    });\n    function f() {\n      const t = l.value ? !!e.value : !0;\n      return n.value = t ? \"\" : \"is_required\", o(\"inputCheck\", t, n.value), t;\n    }\n    return u({\n      value: e,\n      checkValidity: f,\n      validationMessage: n\n    }), (t, d) => (s(), b(\"div\", {\n      class: I([\"switch-container\", { checked: e.value }])\n    }, [\n      a(\"div\", j, [\n        q(a(\"input\", {\n          class: \"switch-input\",\n          ref_key: \"inputRef\",\n          ref: p,\n          \"onUpdate:modelValue\": d[0] || (d[0] = (k) => e.value = k),\n          type: \"checkbox\",\n          required: w(l),\n          disabled: c.disabled\n        }, null, 8, z), [\n          [B, e.value]\n        ]),\n        a(\"div\", E, [\n          c.checkedIcon || c.unCheckedIcon ? (s(), V(_, g(R({ key: 0 }, e.value ? c.checkedIcon : c.unCheckedIcon)), null, 16)) : x(\"\", !0)\n        ])\n      ])\n    ], 2));\n  }\n});\nexport {\n  O as default\n};\n", "import { defineComponent as a, createElementBlock as o, openBlock as n, createCommentVNode as i, createElementVNode as d, toDisplayString as l, renderSlot as c, createVNode as m, unref as u } from \"vue\";\nimport { useI18n as f } from \"vue-i18n\";\nimport p from \"./UIButton.js\";\nconst g = { class: \"dialog-container\" }, k = {\n  key: 0,\n  class: \"dialog-title\"\n}, y = {\n  key: 1,\n  class: \"dialog-desc\"\n}, v = {\n  key: 2,\n  class: \"dialog-content\"\n}, _ = { class: \"actions-container\" }, D = /* @__PURE__ */ a({\n  __name: \"UIDialog\",\n  props: {\n    title: {\n      type: String,\n      default: \"\"\n    },\n    description: {\n      type: String,\n      default: \"\"\n    }\n  },\n  setup(t) {\n    const { t: r } = f();\n    return (e, s) => (n(), o(\"div\", g, [\n      t.title ? (n(), o(\"div\", k, l(t.title), 1)) : i(\"\", !0),\n      t.description ? (n(), o(\"div\", y, l(t.description), 1)) : i(\"\", !0),\n      e.$slots.content ? (n(), o(\"div\", v, [\n        c(e.$slots, \"content\")\n      ])) : i(\"\", !0),\n      d(\"div\", _, [\n        c(e.$slots, \"buttons\", {}, () => [\n          m(p, {\n            type: \"clear\",\n            title: u(r)(\"button.ok\"),\n            onClick: s[0] || (s[0] = ($) => e.$emit(\"closeDialog\"))\n          }, null, 8, [\"title\"])\n        ])\n      ])\n    ]));\n  }\n});\nexport {\n  D as default\n};\n", "import { defineComponent as _, ref as n, onUnmounted as d, createElementBlock as i, openBlock as l, createElementVNode as v, createVNode as a, renderSlot as m, createCommentVNode as k } from \"vue\";\nimport $ from \"./UIIcon.js\";\nimport h from \"./UIRipple.js\";\nconst C = { class: \"selection-wrapper\" }, w = {\n  key: 0,\n  class: \"selection-content\"\n}, y = /* @__PURE__ */ _({\n  __name: \"UISelection\",\n  emits: [\"click\"],\n  setup(E, { emit: u }) {\n    const f = u, s = n(void 0), t = n(!1), o = n([]);\n    function p(e) {\n      var r;\n      if (t.value) return;\n      t.value = !0, (r = s.value) == null || r.createRipple(e);\n      const c = setTimeout(() => {\n        f(\"click\", e), t.value = !1;\n      }, 200);\n      o.value.push(c);\n    }\n    return d(() => {\n      o.value.forEach((e) => clearTimeout(e)), o.value = [];\n    }), (e, c) => (l(), i(\"div\", {\n      class: \"selection-container\",\n      onClick: p\n    }, [\n      v(\"div\", C, [\n        m(e.$slots, \"icon\"),\n        e.$slots.content ? (l(), i(\"span\", w, [\n          m(e.$slots, \"content\")\n        ])) : k(\"\", !0),\n        a($, { name: \"arrow-forward\" })\n      ]),\n      a(h, {\n        ref_key: \"rippleRef\",\n        ref: s\n      }, null, 512)\n    ]));\n  }\n});\nexport {\n  y as default\n};\n", "import { defineComponent as q, toRefs as V, ref as b, computed as o, onMounted as F, watch as W, nextTick as L, createElementBlock as l, openBlock as i, normalizeStyle as I, unref as t, createElementVNode as d, createCommentVNode as B, withDirectives as k, vShow as G, toDisplayString as m, normalizeClass as P } from \"vue\";\nimport U from \"jsbarcode\";\nimport K from \"qrcode\";\nimport { formatDateTime as v } from \"@perkd/format-datetime\";\nimport { useI18n as $ } from \"vue-i18n\";\nimport { isUrl as _ } from \"@perkd/applet-common/utils\";\nimport { Cards as Z } from \"@perkd/applet-common/types/cards\";\nconst AA = \"data:image/png;base64,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\", eA = [\"src\"], tA = [\"src\"], cA = [\"id\"], aA = [\"innerHTML\"], gA = {\n  key: 0,\n  class: \"card-date-join\"\n}, uA = { class: \"label\" }, dA = { class: \"date\" }, rA = { class: \"label\" }, oA = { class: \"date\" }, bA = /* @__PURE__ */ q({\n  __name: \"UICard\",\n  props: {\n    image: {\n      type: String,\n      require: !0\n    },\n    placeholder: {\n      type: String,\n      require: !0,\n      default: AA\n    },\n    width: {\n      type: Number,\n      default: parseFloat(getComputedStyle(document.documentElement).getPropertyValue(\"--width-card\")) || 28.8\n    },\n    cardNumber: {\n      type: String,\n      default: \"\"\n    },\n    barcodeType: {\n      type: String,\n      default: \"\"\n    },\n    barcodePatterns: {\n      type: String,\n      default: \"\"\n    },\n    startTime: {\n      type: String,\n      default: \"\"\n    },\n    endTime: {\n      type: String,\n      default: \"\"\n    }\n  },\n  setup(e, { expose: h }) {\n    const { t: M } = $(), g = e, { width: N, barcodeType: O, cardNumber: a } = V(g), w = parseInt(getComputedStyle(document.documentElement).getPropertyValue(\"--font-size-base\")) || 10, E = document.documentElement.getAttribute(\"lang\"), C = b(!1), n = b(N.value * w), A = b(N.value * w / 1.6), s = b(0), p = b(\"\"), T = o(() => `${O.value || \"card\"}-${a.value.replace(/[^a-zA-Z0-9]/g, \"\")}`), D = o(() => Z.CODE_SQUARE.includes(O.value)), H = o(() => a.value && (_(a.value) || a.value.length > 30)), Y = o(() => !(s.value / 180 % 2)), f = o(() => ({\n      width: `${n.value}px`,\n      height: `${A.value}px`,\n      borderRadius: `${A.value / 10}px`,\n      transform: `rotateY(${s.value}deg)`\n    })), R = o(() => {\n      const r = E && E.startsWith(\"zh\"), c = E && [\"ja\", \"ko\"].includes(E), u = r ? 0.08 : c ? 0.07 : 0.1;\n      return {\n        fontSize: `${A.value * u}px`,\n        padding: `${A.value * 0.045}px ${A.value * 0.08}px`\n      };\n    }), S = o(() => g.barcodeType ? D.value ? {\n      height: A.value * (H.value ? 0.75 : 0.6) + \"px\",\n      marginTop: A.value * 0.02 + \"px\"\n    } : {\n      height: A.value * 0.42 + \"px\",\n      marginTop: A.value * 0.08 + \"px\"\n    } : {\n      height: A.value * 0.24 + \"px\",\n      marginTop: A.value * 0.15 + \"px\",\n      marginBottom: A.value * 0.075 + \"px\",\n      backgroundColor: \"var(--color-background-maximal)\"\n    }), y = o(() => ({\n      fontSize: A.value * (D.value ? 0.12 : 0.17) + \"px\",\n      lineHeight: D.value ? 1.3 : 1.6,\n      padding: `0 ${A.value * 0.08}px`,\n      marginTop: D.value && !H.value ? -A.value * 0.02 + \"px\" : 0\n    }));\n    function X(r) {\n      const c = r.target, u = c.width / c.height, Q = n.value / A.value;\n      u > Q ? (n.value = Math.min(c.width, n.value), A.value = n.value / u) : (A.value = Math.min(c.height, A.value), n.value = A.value * u), C.value = !0;\n    }\n    function z(r = void 0, c = !1) {\n      const { cardNumber: u, startTime: Q, endTime: J } = g, x = r ? r.offsetX < n.value / 2 : c;\n      (u || Q || J) && (s.value = x ? s.value - 180 : s.value + 180);\n    }\n    F(() => {\n      j();\n    }), W([a, O], () => {\n      L(() => {\n        j();\n      });\n    });\n    async function j() {\n      g.cardNumber && g.barcodeType && (g.barcodeType !== Z.Barcode.QRCODE ? U(`#${T.value}`, g.cardNumber, {\n        format: g.barcodeType,\n        width: 4,\n        height: 100,\n        displayValue: !1\n      }) : p.value = await K.toString(g.cardNumber, { type: \"svg\" }));\n    }\n    return h({\n      isFront: Y,\n      flipCard: z\n    }), (r, c) => (i(), l(\"div\", {\n      theme: \"light\",\n      class: \"card-container\",\n      style: I({ width: `${t(N) * t(w)}px` })\n    }, [\n      d(\"div\", {\n        class: \"card\",\n        style: I(f.value),\n        onClick: c[0] || (c[0] = (u) => r.$attrs.onClick || z(u))\n      }, [\n        d(\"div\", {\n          class: \"card-front\",\n          style: I({ borderRadius: `${A.value / 10}px` })\n        }, [\n          k(d(\"img\", {\n            src: e.image,\n            onLoad: X\n          }, null, 40, eA), [\n            [G, C.value]\n          ]),\n          k(d(\"img\", { src: e.placeholder }, null, 8, tA), [\n            [G, !C.value]\n          ])\n        ], 4),\n        t(a) || e.startTime || e.endTime ? (i(), l(\"div\", {\n          key: 0,\n          class: \"card-back\",\n          style: I({ borderRadius: `${A.value / 10}px` })\n        }, [\n          d(\"div\", {\n            class: \"card-code\",\n            style: I(S.value)\n          }, [\n            t(a) && !D.value ? (i(), l(\"svg\", {\n              key: 0,\n              id: T.value,\n              class: \"barcode\"\n            }, null, 8, cA)) : B(\"\", !0),\n            t(a) && D.value ? (i(), l(\"div\", {\n              key: 1,\n              class: \"qrcode\",\n              innerHTML: p.value\n            }, null, 8, aA)) : B(\"\", !0)\n          ], 4),\n          t(a) && !H.value ? (i(), l(\"div\", {\n            key: 0,\n            class: \"card-number\",\n            style: I(y.value)\n          }, m(t(a)), 5)) : B(\"\", !0),\n          e.startTime || e.endTime ? (i(), l(\"div\", {\n            key: 1,\n            class: \"card-dates\",\n            style: I(R.value)\n          }, [\n            e.startTime ? (i(), l(\"div\", gA, [\n              d(\"span\", uA, m(t(M)(\"form.join\")), 1),\n              d(\"span\", dA, m(t(v)(e.startTime).format(\"ll\")), 1)\n            ])) : B(\"\", !0),\n            e.endTime ? (i(), l(\"div\", {\n              key: 1,\n              class: P([\"card-date-expire\", { expired: t(v)(e.endTime).isBefore() }])\n            }, [\n              d(\"span\", rA, m(t(M)(\"form.expire\")), 1),\n              d(\"span\", oA, m(t(v)(e.endTime).format(\"ll\")), 1)\n            ], 2)) : B(\"\", !0)\n          ], 4)) : B(\"\", !0)\n        ], 4)) : B(\"\", !0)\n      ], 4)\n    ], 4));\n  }\n});\nexport {\n  bA as default\n};\n", "import { defineComponent as F, toRefs as M, ref as n, onMounted as W, computed as u, createElementBlock as z, openBlock as V, normalizeClass as f, createVNode as $, normalizeStyle as j, unref as A, withModifiers as D } from \"vue\";\nimport k from \"./UICard.js\";\nconst m = 300, G = /* @__PURE__ */ F({\n  __name: \"UICardOverlay\",\n  props: {\n    cardInfo: {\n      type: Object\n    },\n    startRect: {\n      type: DOMRect,\n      default: () => ({\n        top: 0,\n        left: 0\n      })\n    },\n    startWidth: {\n      type: Number,\n      default: 9\n    }\n  },\n  emits: [\"closeCardOverlay\"],\n  setup(r, { emit: I }) {\n    const x = r, { startRect: v, startWidth: p } = M(x), O = parseFloat(getComputedStyle(document.documentElement).getPropertyValue(\"--width-card\")) || 28.8, S = n(void 0), a = n(void 0), y = n(void 0), c = n(void 0), s = n(!1), d = n(!1), w = I;\n    W(() => {\n      var e;\n      c.value = (e = y.value) == null ? void 0 : e.getBoundingClientRect(), setTimeout(() => {\n        s.value = !0, setTimeout(() => {\n          d.value = !0, a.value && a.value.isFront && a.value.flipCard();\n        }, m);\n      }, 0);\n    });\n    const i = u(() => {\n      var e, t, l, o;\n      return {\n        top: ((e = v.value) == null ? void 0 : e.top) - (((t = c.value) == null ? void 0 : t.top) || 0),\n        left: ((l = v.value) == null ? void 0 : l.left) - (((o = c.value) == null ? void 0 : o.left) || 0)\n      };\n    }), N = u(() => {\n      var e, t;\n      return {\n        transition: `transform ${m}ms ease-in-out`,\n        top: `${(e = i.value) == null ? void 0 : e.top}px`,\n        left: `${(t = i.value) == null ? void 0 : t.left}px`\n      };\n    }), P = u(() => {\n      var e, t, l, o;\n      return `translate(calc(${(((e = c.value) == null ? void 0 : e.width) || 0) / 2}px - 50% - ${(t = i.value) == null ? void 0 : t.left}px), calc(${(((l = c.value) == null ? void 0 : l.height) || 0) / 2}px - 50% - ${(o = i.value) == null ? void 0 : o.top}px)) scale(${O / p.value})`;\n    });\n    function B() {\n      a.value && !a.value.isFront ? (a.value.flipCard(), setTimeout(() => {\n        C();\n      }, 800)) : C();\n    }\n    function C() {\n      d.value = !1, s.value = !1, setTimeout(() => {\n        w(\"closeCardOverlay\");\n      }, m);\n    }\n    function E() {\n      a.value && a.value.flipCard();\n    }\n    return (e, t) => {\n      var l, o, T, h, b, R, g;\n      return V(), z(\"div\", {\n        ref_key: \"screenRef\",\n        ref: y,\n        class: f([\"screen\", \"card-overlay-screen\", { overlay: d.value }]),\n        onClick: B\n      }, [\n        $(k, {\n          ref_key: \"duppedCardRef\",\n          ref: S,\n          class: f([\"duppedCard\", { hide: d.value }]),\n          width: A(p),\n          image: (l = r.cardInfo) == null ? void 0 : l.image,\n          style: j({ ...N.value, transform: s.value ? P.value : \"\" })\n        }, null, 8, [\"class\", \"width\", \"image\", \"style\"]),\n        $(k, {\n          ref_key: \"overlayCardRef\",\n          ref: a,\n          class: f([\"overlayCard\", { hide: !d.value }]),\n          image: (o = r.cardInfo) == null ? void 0 : o.image,\n          cardNumber: (T = r.cardInfo) == null ? void 0 : T.cardNumber,\n          barcodeType: (h = r.cardInfo) == null ? void 0 : h.barcodeType,\n          barcodePatterns: (b = r.cardInfo) == null ? void 0 : b.barcodePatterns,\n          startTime: (R = r.cardInfo) == null ? void 0 : R.startTime,\n          endTime: (g = r.cardInfo) == null ? void 0 : g.endTime,\n          onClick: D(E, [\"stop\"])\n        }, null, 8, [\"class\", \"image\", \"cardNumber\", \"barcodeType\", \"barcodePatterns\", \"startTime\", \"endTime\"])\n      ], 2);\n    };\n  }\n});\nexport {\n  G as default\n};\n", "import { defineComponent as H, ref as z, computed as T, onMounted as q, onBeforeUnmount as J, watch as K, createElementBlock as n, openBlock as i, normalizeStyle as Q, createCommentVNode as g, normalizeProps as l, guardReactiveProps as y, createElementVNode as r, mergeProps as d, Fragment as A, toDisplayString as G, renderSlot as X } from \"vue\";\nconst Y = [\"dx\", \"dy\"], Z = [\"stdDeviation\"], tt = [\"flood-color\", \"flood-opacity\"], et = [\"dx\", \"dy\"], ot = [\"stdDeviation\"], rt = [\"floodColor\", \"floodOpacity\"], nt = {\n  key: 0,\n  class: \"current-counter\"\n}, it = {\n  key: 1,\n  class: \"text-container\"\n}, lt = /* @__PURE__ */ H({\n  __name: \"UICircleProgress\",\n  props: {\n    size: {\n      type: Number,\n      default: 180\n    },\n    borderWidth: {\n      type: Number,\n      default: 10\n    },\n    borderBgWidth: {\n      type: Number,\n      default: 10\n    },\n    fillColor: {\n      type: String,\n      default: \"accent\"\n    },\n    emptyColor: {\n      type: String,\n      default: \"var(--color-background-strong)\"\n    },\n    background: {\n      type: String,\n      default: \"none\"\n    },\n    percent: {\n      type: Number,\n      default: 0\n    },\n    linecap: {\n      type: String,\n      default: \"round\"\n    },\n    transition: {\n      type: Number,\n      // speed, animation time\n      default: 400\n    },\n    isGradient: {\n      type: Boolean,\n      default: !1\n    },\n    gradient: {\n      type: Object\n    },\n    isShadow: {\n      type: Boolean,\n      default: !1\n    },\n    shadow: {\n      type: Object\n    },\n    isBgShadow: {\n      type: Boolean,\n      default: !1\n    },\n    bgShadow: {\n      type: Object\n    },\n    viewport: {\n      type: Boolean,\n      default: !1\n    },\n    onViewport: {\n      type: Function\n    },\n    showPercent: {\n      type: Boolean,\n      default: !1\n    },\n    unit: {\n      type: String,\n      default: \"\"\n    }\n  },\n  setup(s, { expose: b }) {\n    const t = s, v = B(\"grd_\"), w = B(\"shd1_\"), W = B(\"shd2_\"), S = z(null), c = z(0), P = (o) => [\"primary\", \"accent\", \"success\", \"warning\", \"error\"].indexOf(o) !== -1 ? `var(--color-background-${o})` : o, C = { angle: 0, startColor: \"#ff0000\", stopColor: \"#ffff00\", ...t.gradient }, u = { inset: !1, vertical: 10, horizontal: 0, blur: 10, opacity: 0.5, color: \"#000000\", ...t.shadow }, a = { inset: !0, vertical: 3, horizontal: 0, blur: 3, opacity: 0.4, color: \"#000000\", ...t.bgShadow }, I = () => {\n      let o = (t.size - t.borderBgWidth) * 0.5;\n      return t.borderWidth > t.borderBgWidth && (o -= (t.borderWidth - t.borderBgWidth) * 0.5), o;\n    }, m = () => {\n      let o = (t.size - t.borderWidth) * 0.5;\n      return t.borderBgWidth > t.borderWidth && (o -= (t.borderBgWidth - t.borderWidth) * 0.5), o;\n    }, M = 2 * Math.PI * m(), k = z(2 * Math.PI * m()), V = {\n      style: {\n        transform: \"rotate(-90deg)\",\n        overflow: \"visible\"\n      },\n      xmlns: \"http://www.w3.org/2000/svg\",\n      viewBox: `${t.size / 2} ${t.size / 2} ${t.size} ${t.size}`\n    }, _ = {\n      cx: t.size,\n      cy: t.size,\n      r: I(),\n      stroke: P(t.emptyColor),\n      \"stroke-width\": t.borderBgWidth,\n      fill: t.background,\n      ...t.isBgShadow && { filter: `url(#${W})` }\n    }, E = T(() => ({\n      cx: t.size,\n      cy: t.size,\n      r: m(),\n      fill: \"none\",\n      \"stroke-width\": t.borderWidth,\n      \"stroke-dasharray\": M,\n      \"stroke-dashoffset\": k.value,\n      \"stroke-linecap\": t.linecap,\n      stroke: t.isGradient ? `url(#${v})` : P(t.fillColor),\n      ...t.isShadow && { filter: `url(#${w})` },\n      ...t.transition && {\n        style: { transition: `stroke-dashoffset ${t.transition}ms` }\n      }\n    })), N = {\n      id: v,\n      x1: \"0%\",\n      y1: \"0%\",\n      x2: \"0%\",\n      y2: \"100%\",\n      gradientTransform: `rotate(${C.angle}, .5, .5)`\n    }, F = {\n      offset: 0,\n      \"stop-color\": C.startColor\n    }, R = {\n      offset: 100,\n      \"stop-color\": C.stopColor\n    }, O = {\n      id: w,\n      width: \"500%\",\n      height: \"500%\",\n      x: \"-250%\",\n      y: \"-250%\"\n    }, $ = {\n      id: W,\n      width: \"500%\",\n      height: \"500%\",\n      x: \"-250%\",\n      y: \"-250%\"\n    }, f = {\n      dx: u.vertical * -1,\n      dy: u.horizontal,\n      stdDeviation: u.blur,\n      floodColor: u.color,\n      floodOpacity: u.opacity\n    }, p = {\n      dx: a.vertical * -1,\n      dy: a.horizontal,\n      stdDeviation: a.blur,\n      floodColor: a.color,\n      floodOpacity: a.opacity\n    };\n    q(() => {\n      h();\n    }), J(() => {\n      window.removeEventListener(\"scroll\", h);\n    }), K(\n      () => t.percent,\n      () => {\n        h();\n      }\n    );\n    function j(o) {\n      if (o === null) return;\n      const e = o.getBoundingClientRect();\n      return e.top >= 0 && e.left >= 0 && e.bottom <= (window.innerHeight || document.documentElement.clientHeight) && e.right <= (window.innerWidth || document.documentElement.clientWidth);\n    }\n    function x() {\n      const o = 2 * Math.PI * m();\n      k.value = o - o * t.percent / 100;\n      const e = Math.round(100 - 100 / o * k.value);\n      L(e);\n    }\n    function L(o) {\n      const e = o - c.value;\n      if (e) {\n        const U = t.transition / Math.abs(e), D = setInterval(() => {\n          e > 0 ? (c.value += 1, c.value >= o && clearInterval(D)) : (c.value -= 1, c.value <= o && clearInterval(D));\n        }, U);\n      }\n    }\n    function h() {\n      t.viewport ? (window.addEventListener(\"scroll\", h), S.value && j(S.value) && (window.removeEventListener(\"scroll\", h), t.viewport && x(), t.onViewport && typeof t.onViewport == \"function\" && t.onViewport())) : x();\n    }\n    function B(o = \"\", e = \"\") {\n      return o + Math.random().toString(36).substring(2, 8) + Math.random().toString(36).substring(2, 8) + e;\n    }\n    return b({\n      updatePercent: x\n    }), (o, e) => (i(), n(\"div\", {\n      ref_key: \"circleProgressRef\",\n      ref: S,\n      style: Q({ height: s.size + \"px\", width: t.size + \"px\" }),\n      class: \"circular-progress\"\n    }, [\n      (i(), n(\"svg\", l(y(V)), [\n        s.isGradient ? (i(), n(\"linearGradient\", l(d({ key: 0 }, N)), [\n          r(\"stop\", l(y(F)), null, 16),\n          r(\"stop\", l(y(R)), null, 16)\n        ], 16)) : g(\"\", !0),\n        r(\"circle\", d(_, { class: \"circle-background\" }), null, 16),\n        r(\"circle\", d(E.value, { class: \"circle-forground\" }), null, 16),\n        s.isShadow ? (i(), n(A, { key: 1 }, [\n          u.inset === !1 ? (i(), n(\"filter\", l(d({ key: 0 }, O)), [\n            r(\"feDropShadow\", l(y(f)), null, 16)\n          ], 16)) : (i(), n(\"filter\", l(d({ key: 1 }, O)), [\n            r(\"feOffset\", {\n              dx: f.dx,\n              dy: f.dy\n            }, null, 8, Y),\n            r(\"feGaussianBlur\", {\n              stdDeviation: f.stdDeviation\n            }, null, 8, Z),\n            e[0] || (e[0] = r(\"feComposite\", {\n              operator: \"out\",\n              in: \"SourceGraphic\",\n              result: \"inverse\"\n            }, null, -1)),\n            r(\"feFlood\", {\n              \"flood-color\": f.floodColor,\n              \"flood-opacity\": f.floodOpacity,\n              result: \"color\"\n            }, null, 8, tt),\n            e[1] || (e[1] = r(\"feComposite\", {\n              operator: \"in\",\n              in: \"color\",\n              in2: \"inverse\",\n              result: \"shadow\"\n            }, null, -1)),\n            e[2] || (e[2] = r(\"feComposite\", {\n              operator: \"over\",\n              in: \"shadow\",\n              in2: \"SourceGraphic\"\n            }, null, -1))\n          ], 16))\n        ], 64)) : g(\"\", !0),\n        a ? (i(), n(A, { key: 2 }, [\n          a.inset === !1 ? (i(), n(\"filter\", l(d({ key: 0 }, $)), [\n            r(\"feDropShadow\", l(y(p)), null, 16)\n          ], 16)) : (i(), n(\"filter\", l(d({ key: 1 }, $)), [\n            r(\"feOffset\", {\n              dx: p.dx,\n              dy: p.dy\n            }, null, 8, et),\n            r(\"feGaussianBlur\", {\n              stdDeviation: p.stdDeviation\n            }, null, 8, ot),\n            e[3] || (e[3] = r(\"feComposite\", {\n              operator: \"out\",\n              in: \"SourceGraphic\",\n              result: \"inverse\"\n            }, null, -1)),\n            r(\"feFlood\", {\n              floodColor: p.floodColor,\n              floodOpacity: p.floodOpacity,\n              result: \"color\"\n            }, null, 8, rt),\n            e[4] || (e[4] = r(\"feComposite\", {\n              operator: \"in\",\n              in: \"color\",\n              in2: \"inverse\",\n              result: \"shadow\"\n            }, null, -1)),\n            e[5] || (e[5] = r(\"feComposite\", {\n              operator: \"over\",\n              in: \"shadow\",\n              in2: \"SourceGraphic\"\n            }, null, -1))\n          ], 16))\n        ], 64)) : g(\"\", !0)\n      ], 16)),\n      s.showPercent ? (i(), n(\"span\", nt, G(c.value) + \" \" + G(s.unit), 1)) : g(\"\", !0),\n      o.$slots.textContent ? (i(), n(\"div\", it, [\n        X(o.$slots, \"textContent\", {}, void 0, !0)\n      ])) : g(\"\", !0)\n    ], 4));\n  }\n}), st = (s, b) => {\n  const t = s.__vccOpts || s;\n  for (const [v, w] of b)\n    t[v] = w;\n  return t;\n}, dt = /* @__PURE__ */ st(lt, [[\"__scopeId\", \"data-v-7da27c7b\"]]);\nexport {\n  dt as default\n};\n", "import { defineComponent as O, toRefs as V, ref as o, onMounted as P, onUnmounted as U, createBlock as y, openBlock as a, Transition as p, unref as i, withCtx as k, createElementBlock as m, createCommentVNode as f, withModifiers as _, createVNode as D, createElementVNode as I, renderSlot as j } from \"vue\";\nimport q from \"./UIButton.js\";\nconst G = /* @__PURE__ */ O({\n  __name: \"UIModalBox\",\n  props: {\n    showClose: {\n      type: Boolean,\n      default: !0\n    },\n    overlayAnimation: {\n      type: String\n    },\n    modalAnimation: {\n      type: String\n    },\n    showHandle: {\n      type: Boolean,\n      default: !1\n    },\n    touchable: {\n      type: Boolean,\n      default: !0\n    },\n    threshold: {\n      type: Number,\n      default: 0.35\n    }\n  },\n  emits: [\"closeModal\"],\n  setup(C, { emit: M }) {\n    const w = C, { showClose: T, overlayAnimation: B, modalAnimation: E, showHandle: R, threshold: g, touchable: d } = V(w), l = o(null), t = o(null), h = o(null), s = o(0), u = o(0), c = o(!1), v = o(!1), S = M;\n    P(() => {\n      c.value = !0;\n    }), U(() => {\n      c.value = !1;\n    });\n    function Y(e) {\n      if (!t.value || !d.value) return;\n      const { scrollTop: n } = t.value, H = e.target === h.value ? 0 : n;\n      e.touches.length === 1 && (s.value = e.touches[0].clientY + H, u.value = s.value);\n    }\n    function b(e) {\n      if (e.touches.length === 1 && d.value) {\n        u.value = e.touches[0].clientY;\n        const n = u.value - s.value;\n        l.value && n > 0 && (l.value.style.transform = `translateY(${n}px)`);\n      }\n    }\n    function x() {\n      if (!t.value || !d.value) return;\n      const e = t.value.clientHeight * g.value;\n      u.value - s.value > e ? r() : l.value && (l.value.style.transform = \"\");\n    }\n    function r() {\n      v.value = !1;\n    }\n    function A() {\n      v.value = !0;\n    }\n    function L() {\n    }\n    function N() {\n      c.value = !1;\n    }\n    function $() {\n      S(\"closeModal\");\n    }\n    return (e, n) => (a(), y(p, {\n      name: i(B) || \"fade\",\n      onEnter: A,\n      onLeave: $\n    }, {\n      default: k(() => [\n        c.value ? (a(), m(\"div\", {\n          key: 0,\n          class: \"screen overlay modal-screen\",\n          onClick: _(r, [\"self\"])\n        }, [\n          D(p, {\n            name: i(E) || \"slide-up\",\n            onEnter: L,\n            onLeave: N\n          }, {\n            default: k(() => [\n              v.value ? (a(), m(\"div\", {\n                key: 0,\n                ref_key: \"modalContainerRef\",\n                ref: l,\n                class: \"modal-container\",\n                onTouchstartPassive: Y,\n                onTouchmovePassive: b,\n                onTouchend: _(x, [\"stop\"])\n              }, [\n                i(R) ? (a(), m(\"div\", {\n                  key: 0,\n                  ref_key: \"handleRef\",\n                  ref: h,\n                  class: \"handle\"\n                }, null, 512)) : f(\"\", !0),\n                i(T) ? (a(), y(q, {\n                  key: 1,\n                  class: \"close-button\",\n                  type: \"circle\",\n                  icon: { name: \"close\" },\n                  onClick: r\n                })) : f(\"\", !0),\n                I(\"div\", {\n                  class: \"modal\",\n                  ref_key: \"modalRef\",\n                  ref: t\n                }, [\n                  j(e.$slots, \"default\", { onCloseModal: r })\n                ], 512)\n              ], 544)) : f(\"\", !0)\n            ]),\n            _: 3\n          }, 8, [\"name\"])\n        ])) : f(\"\", !0)\n      ]),\n      _: 3\n    }, 8, [\"name\"]));\n  }\n});\nexport {\n  G as default\n};\n", "import { defineComponent as Z, toRefs as G, ref as D, computed as y, onMounted as J, onBeforeUnmount as P, watch as Q, createElementBlock as f, openBlock as v, normalizeClass as X, createCommentVNode as d, renderSlot as $, unref as e, toDisplayString as n, createElementVNode as t } from \"vue\";\nimport { useI18n as Y } from \"vue-i18n\";\nimport { formatDateTime as ee } from \"@perkd/format-datetime\";\nconst te = {\n  key: 0,\n  class: \"js-countdown-offline\"\n}, ne = { class: \"js-countdown-before\" }, oe = { class: \"prefix\" }, se = { class: \"end-date\" }, ae = { class: \"suffix\" }, ue = {\n  key: 2,\n  class: \"js-countdown-ing\"\n}, le = { class: \"prefix\" }, ie = { class: \"js-countdown-timer\" }, ce = {\n  key: 0,\n  class: \"time-block time-block-days\"\n}, re = { class: \"time-value\" }, de = { class: \"time-unit\" }, me = {\n  key: 1,\n  class: \"time-block time-block-hours\"\n}, fe = { class: \"time-value\" }, ve = { class: \"time-unit\" }, pe = {\n  key: 2,\n  class: \"time-block time-block-mins\"\n}, _e = { class: \"time-value\" }, he = { class: \"time-unit\" }, we = {\n  key: 3,\n  class: \"time-block time-block-secs\"\n}, ge = { class: \"time-value secs\" }, be = { class: \"time-unit\" }, Ce = { class: \"suffix\" }, ke = { class: \"js-countdown-after\" }, Te = /* @__PURE__ */ Z({\n  __name: \"CountDown\",\n  props: {\n    serverTimeRequired: {\n      type: Boolean,\n      default: !0\n    },\n    serverTime: {\n      type: [Number, String],\n      validator: function(m) {\n        return new Date(m).toString() !== \"Invalid Date\";\n      }\n    },\n    endCountTime: {\n      required: !0,\n      type: [Number, String],\n      validator: function(m) {\n        return new Date(m).toString() !== \"Invalid Date\";\n      }\n    },\n    countDistance: {\n      type: Number,\n      default: 2 * 24 * 60 * 60 * 1e3\n      // '2 days'\n    },\n    timerFormat: {\n      type: String,\n      default: \"auto\",\n      validator: function(m) {\n        return !![\n          \"auto\",\n          \"dd-hh-mm-ss\",\n          \"hh-mm-ss\",\n          \"mm-ss\",\n          \"auto-mm-ss\",\n          \"auto-hh-mm-ss\"\n        ].find((x) => x === m);\n      }\n    },\n    countUpAfterCountDown: {\n      type: Boolean,\n      default: !1\n    }\n  },\n  emits: [\"endCountDown\"],\n  setup(m, { expose: w, emit: x }) {\n    const { t: u } = Y(), L = m, { serverTimeRequired: O, serverTime: a, endCountTime: g, countDistance: V, timerFormat: M, countUpAfterCountDown: b } = G(L), c = D(), p = D(!1), l = D(new Date(g.value).getTime() - (a != null && a.value ? new Date(a == null ? void 0 : a.value).getTime() : (/* @__PURE__ */ new Date()).getTime())), I = D(0), q = y(() => l.value > V.value), F = y(() => l.value <= V.value && l.value >= 1e3 || b.value), S = y(() => l.value < 1e3), A = x;\n    J(() => {\n      S.value ? (A(\"endCountDown\"), b.value && j(!0)) : c.value = setInterval(N, 1e3);\n    }), P(() => {\n      c.value && clearInterval(c.value);\n    }), Q(g, (o) => {\n      o && (l.value = new Date(g.value).getTime() - (a != null && a.value ? new Date(a == null ? void 0 : a.value).getTime() : (/* @__PURE__ */ new Date()).getTime()), l.value > 0 && p.value && j(!1));\n    });\n    const r = y(() => {\n      const o = p.value ? I.value : l.value;\n      if (o < 0) return {};\n      const i = {\n        dd: Math.floor(o / (1e3 * 60 * 60 * 24)),\n        hh: Math.floor(o % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60)),\n        mm: Math.floor(o % (1e3 * 60 * 60) / (1e3 * 60)),\n        ss: Math.floor(o % (1e3 * 60) / 1e3)\n      }, T = (s) => s <= 9 ? `0${s}` : `${s}`, B = Object.keys(i).findIndex((s) => i[s] !== 0), U = B === -1 ? Object.keys(i).length - 1 : B, H = M.value.startsWith(\"auto\"), C = 4 - M.value.split(\"-\").filter((s) => s !== \"auto\").length, K = [24, 60, 60, 60], z = Array(C).fill(0).reduce((s, k, _) => {\n        const h = Object.keys(i)[_];\n        return s += K[_] * i[h], s;\n      }, 0);\n      return Object.keys(i).reduce((s, k, _) => {\n        const h = i[k];\n        if (H && U < C) {\n          const W = h === 0 && _ < U;\n          s[k] = W ? \"\" : T(h);\n        } else\n          s[k] = _ < C ? \"\" : T(_ === C ? h + z : h);\n        return s;\n      }, {});\n    });\n    function j(o) {\n      const i = Math.abs(l.value) < 1e3 ? 1e3 : 0;\n      p.value = o, I.value = o ? Math.abs(l.value) + i : 0, c.value && clearInterval(c.value), c.value = setInterval(o ? R : N, 1e3);\n    }\n    function N() {\n      l.value < 1e3 ? (clearInterval(c.value), A(\"endCountDown\"), b.value && j(!0)) : l.value -= 1e3;\n    }\n    function R() {\n      I.value += 1e3;\n    }\n    function E() {\n      c.value && clearInterval(c.value);\n    }\n    return w({\n      stopCount: E\n    }), (o, i) => (v(), f(\"span\", {\n      class: X({ \"js-countdown-container\": !0, error: p.value })\n    }, [\n      !e(a) && e(O) ? (v(), f(\"span\", te, n(e(u)(\"error.failed_to_get_server_time\")), 1)) : d(\"\", !0),\n      q.value ? $(o.$slots, \"beforeCount\", { key: 1 }, () => [\n        t(\"span\", ne, [\n          t(\"span\", oe, n(e(u)(\"countdown.before.prefix\")), 1),\n          t(\"span\", se, n(e(ee)(e(g)).format(\"LL\")), 1),\n          t(\"span\", ae, n(e(u)(\"countdown.before.suffix\")), 1)\n        ])\n      ]) : d(\"\", !0),\n      F.value ? (v(), f(\"span\", ue, [\n        t(\"span\", le, n(p.value ? e(u)(\"countdown.counting_up.prefix\") : e(u)(\"countdown.counting_down.prefix\")), 1),\n        t(\"span\", ie, [\n          r.value.dd ? (v(), f(\"span\", ce, [\n            t(\"span\", re, n(r.value.dd), 1),\n            t(\"span\", de, n(e(u)(\"countdown.time_unit.dd\")), 1)\n          ])) : d(\"\", !0),\n          r.value.hh ? (v(), f(\"span\", me, [\n            t(\"span\", fe, n(r.value.hh), 1),\n            t(\"span\", ve, n(e(u)(\"countdown.time_unit.hh\")), 1)\n          ])) : d(\"\", !0),\n          r.value.mm ? (v(), f(\"span\", pe, [\n            t(\"span\", _e, n(r.value.mm), 1),\n            t(\"span\", he, n(e(u)(\"countdown.time_unit.mm\")), 1)\n          ])) : d(\"\", !0),\n          r.value.ss ? (v(), f(\"span\", we, [\n            t(\"span\", ge, n(r.value.ss), 1),\n            t(\"span\", be, n(e(u)(\"countdown.time_unit.ss\")), 1)\n          ])) : d(\"\", !0)\n        ]),\n        t(\"span\", Ce, n(p.value ? e(u)(\"countdown.counting_up.suffix\") : e(u)(\"countdown.counting_down.suffix\")), 1)\n      ])) : d(\"\", !0),\n      S.value && !e(b) ? $(o.$slots, \"afterCount\", { key: 3 }, () => [\n        t(\"span\", ke, n(e(u)(\"countdown.after\")), 1)\n      ]) : d(\"\", !0)\n    ], 2));\n  }\n});\nexport {\n  Te as default\n};\n", "import { defineComponent as j, toRefs as G, ref as l, computed as A, createBlock as E, openBlock as S, Transition as H, withCtx as J, createElementBlock as I, createCommentVNode as Q, createElementVNode as T, normalizeStyle as N, normalizeClass as W, Fragment as Z, renderList as K, withModifiers as _, renderSlot as ee } from \"vue\";\nimport te from \"./UIButton.js\";\nconst le = {\n  key: 0,\n  class: \"swipeout\"\n}, ae = /* @__PURE__ */ j({\n  __name: \"UISwipeout\",\n  props: {\n    leftActions: {\n      type: Array\n    },\n    rightActions: {\n      type: Array\n    },\n    threshold: {\n      type: Number,\n      default: 0.8\n    },\n    disabled: {\n      type: Boolean,\n      default: !1\n    }\n  },\n  emits: [\"clickAction\", \"startSwipe\", \"endSwipe\"],\n  setup(Y, { expose: z, emit: D }) {\n    const P = Y, { leftActions: o, rightActions: d, threshold: B, disabled: b } = G(P), r = l(null), X = l(null), c = l(null), i = l(0), M = l(0), $ = l(0), s = l(0), p = l(!1), v = l(!0), x = l(!1), f = l(null), w = D, m = l(!1), h = A(() => o != null && o.value || d != null && d.value ? d == null ? void 0 : d.value : [{\n      key: \"delete\",\n      color: \"var(--color-background-error)\",\n      icon: \"trash-o\"\n    }]), k = A(() => {\n      if (c.value)\n        return c.value === \"right\" ? o == null ? void 0 : o.value : h == null ? void 0 : h.value;\n    });\n    function V(e) {\n      b.value || (M.value = e.touches[0].clientX, $.value = e.touches[0].clientY, v.value = !0);\n    }\n    function q(e) {\n      if (!r.value || !v.value || b.value) return;\n      f.value !== null && (cancelAnimationFrame(f.value), f.value = null), s.value = e.touches[0].clientX - M.value + i.value;\n      const n = Math.abs(s.value) > Math.abs(e.touches[0].clientY - $.value);\n      if (!U(r.value.getBoundingClientRect(), e.touches[0].clientX, e.touches[0].clientY) || !n) {\n        F();\n        return;\n      }\n      c.value = s.value > 0 ? \"right\" : \"left\", v.value = O(c.value), v.value && (p.value || (p.value = !0, w(\"startSwipe\")), f.value = requestAnimationFrame(() => {\n        y(s.value);\n      }));\n    }\n    function L() {\n      var n, u, t;\n      if (!r.value || !p.value || b.value) return;\n      if (f.value !== null && (cancelAnimationFrame(f.value), f.value = null), p.value = !1, v.value = !1, B.value && ((n = k.value) == null ? void 0 : n.length) === 1 && Math.abs(s.value) > r.value.offsetWidth * B.value)\n        R(((u = k.value) == null ? void 0 : u[0].key) || \"\");\n      else {\n        const a = ((t = X.value) == null ? void 0 : t.getBoundingClientRect().width) || 80, g = Math.abs(s.value) > a;\n        i.value = g ? s.value > 0 ? a : -a : 0, i.value === 0 ? C() : (y(i.value), m.value = !0);\n      }\n      w(\"endSwipe\", m.value);\n    }\n    function F() {\n      p.value = !1, v.value = !1, y(i.value), i.value === 0 && (c.value = null), w(\"endSwipe\", m.value);\n    }\n    function O(e) {\n      var t, a;\n      const n = !!((t = o == null ? void 0 : o.value) != null && t.length), u = !!((a = h == null ? void 0 : h.value) != null && a.length);\n      return e === \"right\" && n || e === \"left\" && u;\n    }\n    function y(e) {\n      r.value && (r.value.style.transform = `translateX(${e}px)`);\n    }\n    function R(e, n = !0) {\n      n && C(), w(\"clickAction\", e), e === \"delete\" && (x.value = !0);\n    }\n    function C() {\n      r.value && (i.value = 0, c.value = null, y(i.value), m.value = !1);\n    }\n    function U(e, n, u) {\n      const { left: t, top: a, height: g } = e;\n      return n >= t && u >= a - g / 2 && u <= a + g * 2;\n    }\n    return z({\n      isSwipeoutOpen: m,\n      closeSwipeout: C\n    }), (e, n) => (S(), E(H, { name: \"scale-up\" }, {\n      default: J(() => {\n        var u;\n        return [\n          x.value ? Q(\"\", !0) : (S(), I(\"div\", le, [\n            T(\"div\", {\n              class: \"swipeout-background\",\n              style: N({ \"background-color\": (u = k.value) == null ? void 0 : u[0].color })\n            }, null, 4),\n            T(\"div\", {\n              ref_key: \"actionContainerRef\",\n              ref: X,\n              class: W(`swipeout-action-container ${c.value === \"left\" ? \"right\" : \"left\"}`)\n            }, [\n              (S(!0), I(Z, null, K(k.value, (t) => (S(), E(te, {\n                class: \"swipeout-action\",\n                key: t.key,\n                style: N({ backgroundColor: t.color }),\n                icon: t.icon ? { name: t.icon } : void 0,\n                title: t.text,\n                onTouchend: _((a) => R(t.key, t.closeAfterClick), [\"stop\", \"prevent\"])\n              }, null, 8, [\"style\", \"icon\", \"title\", \"onTouchend\"]))), 128))\n            ], 2),\n            T(\"div\", {\n              ref_key: \"contentRef\",\n              ref: r,\n              class: W([\"swipeout-content\", { swiping: v.value && p.value }]),\n              onTouchstartPassive: V,\n              onTouchmovePassive: q,\n              onTouchend: _(L, [\"stop\", \"prevent\"]),\n              onTouchcancel: _(F, [\"stop\", \"prevent\"])\n            }, [\n              ee(e.$slots, \"default\")\n            ], 34)\n          ]))\n        ];\n      }),\n      _: 3\n    }));\n  }\n});\nexport {\n  ae as default\n};\n", "import { defineComponent as S, toRefs as X, ref as u, onMounted as T, createElementBlock as _, openBlock as $, createElementVNode as M, toDisplayString as g, unref as b } from \"vue\";\nconst k = /* @__PURE__ */ S({\n  __name: \"UIMarquee\",\n  props: {\n    text: {\n      type: String,\n      required: !0\n    },\n    speed: {\n      type: Number,\n      default: 25\n      // 25px per second\n    },\n    delay: {\n      type: Number,\n      default: 2\n    }\n  },\n  setup(p) {\n    const d = p, { text: y, speed: i, delay: c } = X(d), o = u(null), e = u(null), r = u(!1), v = u(0), n = u(0), a = u(0);\n    T(() => {\n      if (o.value && e.value) {\n        if (a.value = e.value.clientWidth - o.value.clientWidth, r.value = a.value > 0, !r.value) return;\n        const t = a.value / i.value;\n        e.value.style.transition = `all ${t}s linear ${c.value}s`, e.value.style.transform = `translateX(-${a.value}px)`;\n      }\n    });\n    function h(t) {\n      r.value && o.value && e.value && (e.value.style.animationPlayState = \"paused\", v.value = t.touches[0].clientX, n.value = m(), e.value.style.transform = `translateX(${n.value}px)`, e.value.style.transition = \"none\");\n    }\n    function x(t) {\n      if (r.value && t.touches.length === 1 && e.value) {\n        const l = t.touches[0].clientX - v.value, s = Math.min(Math.max(-a.value, n.value + l), 0);\n        e.value.style.transform = `translateX(${s}px)`;\n      }\n    }\n    function f() {\n      if (r.value && o.value && e.value && (n.value = m(), Math.abs(n.value) < a.value)) {\n        const t = (a.value - Math.abs(n.value)) / i.value;\n        e.value.style.transition = `all ${t}s linear ${c.value}s`, e.value.style.transform = `translateX(-${a.value}px)`, e.value.style.animationPlayState = \"running\";\n      }\n    }\n    function m() {\n      if (!e.value) return 0;\n      const l = getComputedStyle(e.value).transform;\n      if (l === \"none\" || !l)\n        return 0;\n      const s = l.match(/matrix\\((.+)\\)/);\n      return s && s[1].split(\",\").map(parseFloat)[4] || 0;\n    }\n    return (t, l) => ($(), _(\"div\", {\n      ref_key: \"containerRef\",\n      ref: o,\n      class: \"marquee-container\"\n    }, [\n      M(\"div\", {\n        ref_key: \"contentRef\",\n        ref: e,\n        class: \"marquee-content\",\n        onTouchstartPassive: h,\n        onTouchmovePassive: x,\n        onTouchcancel: f,\n        onTouchend: f\n      }, g(b(y)), 545)\n    ], 512));\n  }\n});\nexport {\n  k as default\n};\n", "import { reactive as c } from \"vue\";\nfunction r() {\n  const t = c({\n    show: !1,\n    success: void 0,\n    text: \"\"\n  });\n  let e = null;\n  function i(s = \"\") {\n    t.show = !0, t.success = void 0, t.text = s;\n  }\n  function n(s, o, u = 1500) {\n    t.success = s, e && clearTimeout(e), e = setTimeout(() => {\n      t.show = !1, o && o();\n    }, u);\n  }\n  return {\n    loading: t,\n    startLoading: i,\n    endLoadingWithDelay: n\n  };\n}\nexport {\n  r as useLoading\n};\n", "import { ref as a } from \"vue\";\nfunction c(t) {\n  const e = a(JSON.parse(localStorage.getItem(t) || \"{}\") || {});\n  function r(o) {\n    Object.assign(e.value, o), localStorage.setItem(t, JSON.stringify(e.value));\n  }\n  return {\n    preferenceStorage: e,\n    updatePreferenceStorage: r\n  };\n}\nexport {\n  c as useStorage\n};\n", "export var Actions;\n(function (Actions) {\n    let Remote;\n    (function (Remote) {\n        Remote[\"CONNECT\"] = \"remote.connect\";\n        Remote[\"SEND\"] = \"remote.send\";\n        Remote[\"CLOSE\"] = \"remote.close\";\n    })(Remote = Actions.Remote || (Actions.Remote = {}));\n})(Actions || (Actions = {}));\n", "import { isErrorResponse } from \"./utils.js\";\nimport { Actions } from './types/actions.js';\nconst { CONNECT, SEND, CLOSE } = Actions.Remote, SUBSCRIBE = 'subscribe';\n// ---  Actions\nconst Websocket = {\n    create: async (SOCKET_URL, cardId, masterId) => {\n        if (!SOCKET_URL) {\n            return { error: { statusMessage: 'websocket.config_missing' } };\n        }\n        const url = `${SOCKET_URL}/metrics-push?access_token={{x-access-token}}`;\n        const credentials = 'perkd';\n        try {\n            const socketId = await window.$perkd.do(CONNECT, { url, cardId, masterId, credentials });\n            return socketId;\n        }\n        catch (error) {\n            return { error };\n        }\n    },\n    subscribe: async (id, message) => {\n        try {\n            await window.$perkd.do(SEND, { id, message: JSON.stringify({ type: SUBSCRIBE, ...message }) });\n        }\n        catch (error) {\n            return { error };\n        }\n    },\n    unsubscribe: async (id) => {\n        try {\n            await window.$perkd.do(CLOSE, { id });\n        }\n        catch (error) {\n            return { error };\n        }\n    },\n};\n// Websocket Message\nexport const Order = {\n    created: (storeId, query) => ({ source: 'eventbus', key: 'sales.order.created', query: { storeId, ...query } }),\n    paid: (storeId, query) => ({ source: 'eventbus', key: 'sales.order.paid', query: { storeId, ...query } }),\n    fulfilled: (storeId, query) => ({ source: 'eventbus', key: 'sales.order.fulfilled', query: { storeId, ...query } }),\n    cancelled: (storeId, query) => ({ source: 'eventbus', key: 'sales.order.cancelled', query: { storeId, ...query } })\n};\nexport const Attendance = {\n    Metrics: {\n        booking: (resourceId) => ({ query: `sum(c_sales_booking_confirm{resourceId=\"${resourceId}\"})` }),\n        checkin: (resourceId) => ({ query: `sum(c_person_person_attend_checkin{resourceId=\"${resourceId}\"})` })\n    },\n    List: {\n        booking: (resourceId, start, end) => ({ query: `c_sales_booking_confirm{resourceId=\"${resourceId}\"}`, start, end }),\n        checkin: (resourceId, start, end) => ({ query: `c_person_person_attend_checkin{resourceId=\"${resourceId}\"}`, start, end }),\n    }\n};\nexport const Membership = {\n    joined: (query) => ({ source: 'eventbus', key: 'membership.membership.joined', query }),\n    registered: (query) => ({ source: 'eventbus', key: 'membership.card.registered', query }),\n    cancelled: (query) => ({ source: 'eventbus', key: 'membership.membership.cancelled', query }),\n};\nexport const Fulfillment = {\n    created: (placeId, stations) => {\n        const query = { placeId, 'itemList.fulfilledAt': null };\n        if (stations)\n            Object.assign(query, { 'itemList.product.tags.kitchen': { $in: stations } });\n        return { source: 'eventbus', key: 'sales.fulfillment.requested.kitchen', query };\n    },\n    queued: (placeId, stations) => {\n        const query = { placeId, 'itemList.fulfilledAt': null };\n        if (stations)\n            Object.assign(query, { 'itemList.product.tags.kitchen': { $in: stations } });\n        return { source: 'eventbus', key: 'sales.fulfillment.queued.kitchen', query };\n    },\n    packed: (placeId) => ({ source: 'eventbus', key: 'sales.fulfillment.packed.kitchen', query: { placeId } }),\n    relocated: (placeId) => ({ source: 'eventbus', key: 'sales.fulfillment.relocated.kitchen', query: { placeId } }),\n    cancelled: (placeId) => ({ source: 'eventbus', key: 'sales.fulfillment.cancelled', query: { placeId } }),\n    fulfilled: (placeId) => ({ source: 'eventbus', key: 'sales.fulfillment.success.item', query: { placeId } }),\n};\nexport async function startSocket(key, param) {\n    const { socketUrl, cardId, masterId, subject } = param;\n    const socketId = await Websocket.create(socketUrl, cardId, masterId);\n    if (isErrorResponse(socketId))\n        return socketId;\n    const subscribe = await Websocket.subscribe(socketId, subject);\n    if (isErrorResponse(subscribe))\n        return subscribe;\n    return { [key]: socketId };\n}\nexport async function closeSocket(socketId) {\n    const result = await Websocket.unsubscribe(socketId);\n    if (isErrorResponse(result)) {\n        return { error: { ...result.error, statusMessage: 'websocket.onclose_error' } };\n    }\n}\n// window event socket.message\nexport async function onSocketMessage(event, sockets) {\n    const result = parseEvent(event);\n    if (isErrorResponse(result)) {\n        return { error: { ...result.error, statusMessage: 'websocket.onmessage_error' } };\n    }\n    const found = Object.entries(sockets).find(([key, socketId]) => result.socketId === socketId);\n    // receive unknown socket message\n    if (!found) {\n        const { socketId } = result;\n        const response = await Websocket.unsubscribe(socketId);\n        if (isErrorResponse(response)) {\n            return {\n                error: {\n                    ...response.error, statusMessage: 'websocket.onmessage_unsubscribe_failed', socketId, sockets\n                }\n            };\n        }\n        return { error: { statusMessage: 'websocket.onmessage_unsubscribe', socketId, data: result.data, sockets } };\n    }\n    return {\n        key: found[0],\n        data: result.data\n    };\n}\n// window event socket.close\nexport async function onSocketClose(event, sockets) {\n    const result = parseEvent(event);\n    if (isErrorResponse(result)) {\n        return { error: { ...result.error, statusMessage: 'websocket.close_failed', sockets } };\n    }\n    const found = Object.entries(sockets).find(([key, socketId]) => result.socketId === socketId);\n    if (!found) {\n        const { socketId } = result;\n        return { error: { statusMessage: 'websocket.onclose_unknown_socket', socketId, sockets } };\n    }\n    // clear socket id if close\n    const closedKey = found[0];\n    if (sockets[closedKey])\n        sockets[closedKey] = '';\n    return { socketId: result.socketId, key: closedKey };\n}\n// window event socket.error\nexport async function onSocketError(appletName, sockets, data) {\n    return { error: { ...data, statusMessage: 'websocket.onerror', sockets } };\n}\nfunction parseEvent(event) {\n    const { id, message } = event.detail || {};\n    const result = { socketId: id, data: {} };\n    try {\n        if (message)\n            result.data = JSON.parse(message);\n    }\n    catch (error) {\n        return { error };\n    }\n    return result;\n}\n", "import { ref as o, watch as E, onMounted as S, onBeforeUnmount as g } from \"vue\";\nimport { onSocketMessage as b, onSocketClose as L, onSocketError as O } from \"@perkd/applet-common/websocket\";\nimport { debounce as J, isErrorResponse as m } from \"@perkd/applet-common/utils\";\nfunction C(e, f) {\n  const a = o(\"\"), i = o(\"\"), c = o(!1), u = o(0), r = o(e.status), k = J(async () => {\n    await e.keepAlive();\n  }, 300);\n  E(r, (s) => {\n    Object.keys(s).filter((t) => !s[t]).length && k();\n  });\n  async function d(s) {\n    const n = await b(s, e.status);\n    if (m(n)) {\n      console.log(\"WebSocket Error:\", n.error);\n      return;\n    }\n    const { key: t, data: y } = n;\n    t && e.onMessage({ key: t, data: y }), a.value = (/* @__PURE__ */ new Date()).toISOString();\n  }\n  async function w(s) {\n    const n = JSON.parse(JSON.stringify(e.status)), t = await L(s, n);\n    if (m(t)) {\n      console.log(\"WebSocket Error:\", t.error);\n      return;\n    }\n    e.onClose(t), r.value = e.status;\n  }\n  async function l(s) {\n    const n = JSON.parse(JSON.stringify(e.status));\n    u.value += 1, u.value < 5 && await O(f, n, s);\n  }\n  async function v() {\n    i.value = (/* @__PURE__ */ new Date()).toISOString(), await e.keepAlive();\n  }\n  return S(async () => {\n    window.addEventListener(\"socket.message\", d), window.addEventListener(\"socket.close\", w), window.addEventListener(\"socket.error\", l), window.addEventListener(\"app.resume\", v), await e.start(), r.value = e.status, c.value = !0;\n  }), g(async () => {\n    window.removeEventListener(\"socket.message\", d), window.removeEventListener(\"socket.close\", w), window.removeEventListener(\"socket.error\", l), window.removeEventListener(\"app.resume\", v), await e.end();\n  }), {\n    topic: e,\n    ready: c,\n    syncTime: a,\n    resumeTime: i\n  };\n}\nexport {\n  C as useWebsocket\n};\n", "import { createI18n as e } from \"vue-i18n\";\nconst m = { button: { ok: \"Ok\", cancel: \"Cancel\", submit: \"Submit\", delete: \"Delete\" }, form: { search: \"search\", join: \"join\", expire: \"expire\" }, countdown: { before: { prefix: \"end on \", suffix: \"\" }, after: \"Event ended\", counting_down: { prefix: \"end in \", suffix: \"\" }, counting_up: { prefix: \"overdue\", suffix: \"\" }, time_unit: { dd: \"day\", hh: \"hour\", mm: \"min\", ss: \"sec\" } }, error: { offline_status: \"offline\", failed_to_get_server_time: \"Failed to get server time\", is_required: \"required\", minimum_length: \"min. {n} character | min. {n} characters\", maximum_length: \"max. {n} character | max. {n} characters\", minimum_number: \"must be {n} or above\", maximum_number: \"must be {n} or below\", invalid_pattern: \"wrong format\", invalid: \"invalid\", invalid_date: \"wrong date format\", before_minimum_date: \"must be {date} or later\", after_maximum_date: \"must be {date} or earlier\", above_minimum_amount: \"must be {amount} or above\", below_maximum_amount: \"must be {amount} or below\" } }, i = {\n  en: m,\n  \"zh-Hans\": { button: { ok: \"好\", cancel: \"取消\", submit: \"提交\", delete: \"删除\" }, form: { search: \"搜索\", join: \"加入\", expire: \"到期\" }, countdown: { before: { prefix: \"\", suffix: \"结束\" }, after: \"活动已结束\", counting_down: { prefix: \"\", suffix: \"后结束\" }, counting_up: { prefix: \"超时\", suffix: \"\" }, time_unit: { dd: \"天\", hh: \"时\", mm: \"分\", ss: \"秒\" } }, error: { offline_status: \"离线\", failed_to_get_server_time: \"服务器时间获取失败\", is_required: \"必填项\", minimum_length: \"最少{n}个字符\", maximum_length: \"最多{n}个字符\", minimum_number: \"最少为{n}\", maximum_number: \"最多为{n}\", invalid_pattern: \"格式错误\", invalid: \"错误\", invalid_date: \"时间格式错误\", before_minimum_date: \"应该在{date}或之后\", after_maximum_date: \"应该在{date}或之前\", above_minimum_amount: \"金额至少为{amount}\", below_maximum_amount: \"金额最多为{amount}\" } },\n  \"zh-Hant\": { button: { ok: \"好\", cancel: \"取消\", submit: \"提交\", delete: \"刪除\" }, form: { search: \"搜寻\", join: \"加入\", expire: \"到期\" }, countdown: { before: { prefix: \"\", suffix: \"結束\" }, after: \"活動已結束\", counting_down: { prefix: \"\", suffix: \"後結束\" }, counting_up: { prefix: \"超時\", suffix: \"\" }, time_unit: { dd: \"天\", hh: \"时\", mm: \"分\", ss: \"秒\" } }, error: { offline_status: \"離線\", failed_to_get_server_time: \"服務器時間獲取失敗\", is_required: \"必填項\", minimum_length: \"最少{n}個字符\", maximum_length: \"最多{n}個字符\", minimum_number: \"最少为{n}\", maximum_number: \"最多为{n}\", invalid_pattern: \"格式錯誤\", invalid: \"錯誤\", invalid_date: \"時間格式錯誤\", before_minimum_date: \"應該在{date}之後\", after_maximum_date: \"應該在{date}之前\", above_minimum_amount: \"金額至少為{amount}\", below_maximum_amount: \"金額最多為{amount}\" } }\n}, t = e({\n  legacy: !1,\n  locale: \"en\",\n  messages: i\n});\nexport {\n  t as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,QAAI,UAAU,SAASA,SAAQ,MAAM,SAAS;AAC7C,sBAAgB,MAAMA,QAAO;AAE7B,WAAK,OAAO;AACZ,WAAK,OAAO,QAAQ,QAAQ;AAC5B,WAAK,UAAU;AAAA,IAChB;AAEA,YAAQ,UAAU;AAAA;AAAA;;;AChBlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,SAAS;AAEjB,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,SAAS,SAAU,UAAU;AAChC,gBAAUC,SAAQ,QAAQ;AAE1B,eAASA,QAAO,MAAM,SAAS;AAC9B,wBAAgB,MAAMA,OAAM;AAE5B,eAAO,KAAK,YAAY;AAGxB,YAAI,QAAQ,OAAO;AAClB,kBAAQ,aAAa,cAAc,IAAI,CAAC;AAAA,QACzC;AAEA,eAAO,2BAA2B,OAAOA,QAAO,aAAa,OAAO,eAAeA,OAAM,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACtH;AAEA,mBAAaA,SAAQ,CAAC;AAAA,QACrB,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AAExB,cAAI,SAAS,YAAY,GAAG;AAG5B,mBAASD,KAAI,GAAGA,KAAI,KAAK,KAAK,QAAQA,MAAK;AAC1C,sBAAU,YAAY,KAAK,KAAKA,EAAC,CAAC,IAAI;AAAA,UACvC;AAGA,oBAAU,YAAY,GAAG;AAEzB,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,2BAA2B,MAAM;AAAA,QAC1D;AAAA,MACD,CAAC,CAAC;AAEF,aAAOC;AAAA,IACR,EAAE,UAAU,OAAO;AAKnB,QAAI,aAAa,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAI5O,QAAI,YAAY,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAInU,aAAS,YAAY,WAAW;AAC/B,aAAO,UAAU,eAAe,SAAS,CAAC;AAAA,IAC3C;AAEA,aAAS,UAAUC,iBAAgB;AAClC,aAAO,UAAUA,eAAc,EAAE,SAAS,CAAC;AAAA,IAC5C;AAEA,aAAS,aAAaA,iBAAgB;AACrC,aAAO,WAAWA,eAAc;AAAA,IACjC;AAEA,aAAS,eAAe,WAAW;AAClC,aAAO,WAAW,QAAQ,SAAS;AAAA,IACpC;AAEA,aAAS,cAAc,MAAM;AAC5B,UAAI,WAAW;AACf,eAASF,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACrC,oBAAY,eAAe,KAAKA,EAAC,CAAC;AAAA,MACnC;AAEA,iBAAW,WAAW;AACtB,aAAO;AAAA,IACR;AAEA,YAAQ,SAAS;AAAA;AAAA;;;ACxGjB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI;AAEJ,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAGhN,QAAI,QAAQ,QAAQ,QAAQ;AAC5B,QAAI,QAAQ,QAAQ,QAAQ;AAC5B,QAAI,QAAQ,QAAQ,QAAQ;AAG5B,QAAI,QAAQ,QAAQ,QAAQ;AAC5B,QAAI,UAAU,QAAQ,UAAU;AAChC,QAAI,UAAU,QAAQ,UAAU;AAChC,QAAI,UAAU,QAAQ,UAAU;AAChC,QAAI,SAAS,QAAQ,SAAS;AAC9B,QAAI,OAAO,QAAQ,OAAO;AAC1B,QAAI,OAAO,QAAQ,OAAO;AAG1B,QAAI,cAAc,QAAQ,eAAe,eAAe,CAAC,GAAG,gBAAgB,cAAc,SAAS,KAAK,GAAG,gBAAgB,cAAc,SAAS,KAAK,GAAG,gBAAgB,cAAc,SAAS,KAAK,GAAG;AAGzM,QAAI,OAAO,QAAQ,OAAO;AAAA,MACzB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,IAAI;AAAA,IACL;AAEA,QAAI,eAAe,QAAQ,eAAe,OAAO,aAAa,GAAG;AACjE,QAAI,eAAe,QAAQ,eAAe,OAAO,aAAa,GAAG;AACjE,QAAI,eAAe,QAAQ,eAAe,OAAO,aAAa,GAAG;AAIjE,QAAI,UAAU,QAAQ,UAAU;AAIhC,QAAI,UAAU,QAAQ,UAAU;AAIhC,QAAI,UAAU,QAAQ,UAAU;AAKhC,QAAI,OAAO,QAAQ,OAAO,CAAC,aAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,YAAa,aAAa,YAAa,YAAa,aAAa,YAAa,YAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,aAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,aAAa,aAAa,aAAa,WAAa,aAAa,WAAa,aAAa,aAAa,aAAa,WAAa,aAAa,WAAa,aAAa,aAAa,aAAa,aAAa,WAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,YAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,WAAa,aAAa,aAAa;AAAA;AAAA;;;ACrD14C;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASG,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAI7e,QAAI,UAAU,SAAU,UAAU;AACjC,gBAAUC,UAAS,QAAQ;AAE3B,eAASA,SAAQ,MAAM,SAAS;AAC/B,wBAAgB,MAAMA,QAAO;AAG7B,YAAI,QAAQ,2BAA2B,OAAOA,SAAQ,aAAa,OAAO,eAAeA,QAAO,GAAG,KAAK,MAAM,KAAK,UAAU,CAAC,GAAG,OAAO,CAAC;AAEzI,cAAM,QAAQ,KAAK,MAAM,EAAE,EAAE,IAAI,SAAU,MAAM;AAChD,iBAAO,KAAK,WAAW,CAAC;AAAA,QACzB,CAAC;AACD,eAAO;AAAA,MACR;AAEA,mBAAaA,UAAS,CAAC;AAAA,QACtB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AAEvB,iBAAQ,0BAA0B,KAAK,KAAK,IAAI;AAAA,QAEjD;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,QAAQ,KAAK;AAEjB,cAAI,aAAa,MAAM,MAAM,IAAI;AAEjC,cAAI,WAAW,WAAW,YAAY,UAAU;AAEhD,cAAI,aAAa,QAAW;AAC3B,kBAAM,IAAI,WAAW,qDAAqD;AAAA,UAC3E;AAEA,cAAI,KAAK,qBAAqB,MAAM,MAAM;AACzC,kBAAM,QAAQ,WAAW,IAAI;AAAA,UAC9B;AAGA,cAAI,iBAAiBA,SAAQ,KAAK,OAAO,GAAG,QAAQ;AAEpD,iBAAO;AAAA,YACN,MAAM,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,QAAQ,iBAAiB,EAAE,IAAI,KAAK;AAAA,YAC9E;AAAA;AAAA,cAEAA,SAAQ,OAAO,UAAU;AAAA,cAEzB,eAAe;AAAA,cAEfA,SAAQ,QAAQ,eAAe,WAAW,cAAc,WAAW,MAAM;AAAA,cAEzEA,SAAQ,OAAO,WAAW,IAAI;AAAA;AAAA,UAC/B;AAAA,QACD;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,uBAAuB;AACtC,cAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,cAAI,OAAO,aAAa,UAAU;AACjC,uBAAW,SAAS,YAAY,MAAM;AAAA,UACvC;AACA,iBAAO;AAAA,QACR;AAAA;AAAA,MAID,CAAC,GAAG,CAAC;AAAA,QACJ,KAAK;AAAA,QACL,OAAO,SAAS,OAAO,OAAO;AAC7B,iBAAO,WAAW,KAAK,KAAK,IAAI,WAAW,KAAK,KAAK,EAAE,SAAS,IAAI;AAAA,QACrE;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa,OAAO,KAAK;AACxC,cAAI,QAAQ,WAAW,OAAO;AAC7B,gBAAI,WAAW,MAAM,MAAM;AAC3B,mBAAO,WAAW,KAAK,WAAW,KAAK,WAAW;AAAA,UACnD,WAAW,QAAQ,WAAW,OAAO;AACpC,mBAAO,MAAM,MAAM,IAAI;AAAA,UACxB,OAAO;AACN,oBAAQ,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,IAAI;AAAA,UACpD;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,KAAK,OAAO,KAAK,KAAK;AACrC,cAAI,CAAC,MAAM,QAAQ;AAClB,mBAAO,EAAE,QAAQ,IAAI,UAAU,EAAE;AAAA,UAClC;AAEA,cAAI,WAAW,QACX,QAAQ;AAGZ,cAAI,MAAM,CAAC,KAAK,KAAK;AACpB,oBAAQ,MAAM,MAAM,IAAI;AACxB,gBAAI,UAAU,WAAW,KAAK,KAAK;AAGnC,gBAAI,YAAY,QAAW;AAC1B,yBAAWA,SAAQ,KAAK,OAAO,MAAM,GAAG,OAAO;AAAA,YAChD,OAEK;AAEH,mBAAK,QAAQ,WAAW,SAAS,QAAQ,WAAW,UAAU,UAAU,WAAW,OAAO;AAEzF,sBAAM,CAAC,IAAI,QAAQ,WAAW,QAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,cACzH;AACA,yBAAWA,SAAQ,KAAK,OAAO,MAAM,GAAG,GAAG;AAAA,YAC5C;AAAA,UACF,OAEK;AACH,oBAAQA,SAAQ,aAAa,OAAO,GAAG;AACvC,uBAAWA,SAAQ,KAAK,OAAO,MAAM,GAAG,GAAG;AAAA,UAC5C;AAGD,cAAI,MAAMA,SAAQ,OAAO,KAAK;AAC9B,cAAI,SAAS,QAAQ;AAErB,iBAAO;AAAA,YACN,QAAQ,MAAM,SAAS;AAAA,YACvB,UAAU,SAAS,SAAS;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;ACtKlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,aAAa;AAGjB,QAAI,kBAAkB,SAASC,iBAAgB,QAAQ;AACtD,aAAO,OAAO,MAAM,IAAI,OAAO,MAAM,WAAW,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE;AAAA,IACpE;AACA,QAAI,kBAAkB,SAASC,iBAAgB,QAAQ;AACtD,aAAO,OAAO,MAAM,IAAI,OAAO,MAAM,WAAW,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE;AAAA,IACpE;AACA,QAAI,YAAY,SAASC,WAAU,QAAQ;AAC1C,aAAO,OAAO,MAAM,IAAI,OAAO,MAAM,WAAW,UAAU,GAAG,CAAC,EAAE,CAAC;AAAA,IAClE;AAGA,aAAS,iBAAiB,QAAQ,KAAK;AACtC,UAAI,SAAS,MAAM,WAAW,UAAU,WAAW;AACnD,UAAI,SAAS,OAAO,MAAM,IAAI,OAAO,OAAO,SAAS,+BAA+B,CAAC;AAErF,UAAI,QAAQ;AACX,eAAO,OAAO,CAAC,IAAI,OAAO,aAAa,GAAG,IAAI,gBAAgB,OAAO,UAAU,OAAO,CAAC,EAAE,MAAM,CAAC;AAAA,MACjG;AAEA,UAAI,QAAQ,OAAO,MAAM,IAAI,OAAO,MAAM,SAAS,GAAG,CAAC,EAAE,CAAC;AAE1D,UAAI,MAAM,WAAW,OAAO,QAAQ;AACnC,eAAO;AAAA,MACR;AAEA,aAAO,QAAQ,OAAO,aAAa,MAAM,MAAM,GAAG,IAAI,iBAAiB,OAAO,UAAU,MAAM,MAAM,GAAG,CAAC,GAAG;AAAA,IAC5G;AAGA,aAAS,gBAAgB,QAAQ;AAChC,UAAI,SAAS,UAAU,MAAM;AAC7B,UAAI,SAAS,OAAO;AAEpB,UAAI,WAAW,OAAO,QAAQ;AAC7B,eAAO;AAAA,MACR;AAEA,eAAS,OAAO,UAAU,MAAM;AAGhC,UAAI,MAAM,gBAAgB,MAAM,KAAK,gBAAgB,MAAM;AAC3D,aAAO,SAAS,OAAO,aAAa,MAAM,MAAM,GAAG,IAAI,iBAAiB,QAAQ,GAAG;AAAA,IACpF;AAIA,YAAQ,UAAU,SAAU,QAAQ;AACnC,UAAI,YAAY;AAChB,UAAI,UAAU,UAAU,MAAM,EAAE;AAGhC,UAAI,WAAW,GAAG;AACjB,oBAAY,WAAW,eAAe,gBAAgB,MAAM;AAAA,MAC7D,OAAO;AAEN,YAAI,MAAM,gBAAgB,MAAM,IAAI,gBAAgB,MAAM;AAC1D,qBAAa,MAAM,WAAW,eAAe,WAAW,gBAAgB,iBAAiB,QAAQ,GAAG;AAAA,MACrG;AAEA,aAAO,UAAU;AAAA,QAAQ;AAAA;AAAA,QACzB,SAAU,OAAO,MAAM;AACtB,iBAAO,OAAO,aAAa,GAAG,IAAI;AAAA,QACnC;AAAA,MAAC;AAAA,IACF;AAAA;AAAA;;;ACxEA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,SAAS;AAEb,QAAI,SAAS,uBAAuB,MAAM;AAE1C,QAAI,QAAQ;AAEZ,QAAI,SAAS,uBAAuB,KAAK;AAEzC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,cAAc,SAAU,OAAO;AAClC,gBAAUC,cAAa,KAAK;AAE5B,eAASA,aAAY,MAAM,SAAS;AACnC,wBAAgB,MAAMA,YAAW;AAGjC,YAAI,0BAA0B,KAAK,IAAI,GAAG;AACzC,cAAI,QAAQ,2BAA2B,OAAOA,aAAY,aAAa,OAAO,eAAeA,YAAW,GAAG,KAAK,OAAO,GAAG,OAAO,SAAS,IAAI,GAAG,OAAO,CAAC;AAAA,QAC1J,OAAO;AACN,cAAI,QAAQ,2BAA2B,OAAOA,aAAY,aAAa,OAAO,eAAeA,YAAW,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,QACrI;AACA,eAAO,2BAA2B,KAAK;AAAA,MACxC;AAEA,aAAOA;AAAA,IACR,EAAE,OAAO,OAAO;AAEhB,YAAQ,UAAU;AAAA;AAAA;;;ACxClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,SAAS,uBAAuB,MAAM;AAE1C,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,WAAW,SAAU,OAAO;AAC/B,gBAAUC,WAAU,KAAK;AAEzB,eAASA,UAAS,QAAQ,SAAS;AAClC,wBAAgB,MAAMA,SAAQ;AAE9B,eAAO,2BAA2B,OAAOA,UAAS,aAAa,OAAO,eAAeA,SAAQ,GAAG,KAAK,MAAM,WAAW,eAAe,QAAQ,OAAO,CAAC;AAAA,MACtJ;AAEA,mBAAaA,WAAU,CAAC;AAAA,QACvB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,IAAI,OAAO,MAAM,WAAW,UAAU,IAAI,EAAE,KAAK,KAAK,IAAI;AAAA,QAClE;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,OAAO,OAAO;AAEhB,YAAQ,UAAU;AAAA;AAAA;;;ACzClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,SAAS,uBAAuB,MAAM;AAE1C,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,WAAW,SAAU,OAAO;AAC/B,gBAAUC,WAAU,KAAK;AAEzB,eAASA,UAAS,QAAQ,SAAS;AAClC,wBAAgB,MAAMA,SAAQ;AAE9B,eAAO,2BAA2B,OAAOA,UAAS,aAAa,OAAO,eAAeA,SAAQ,GAAG,KAAK,MAAM,WAAW,eAAe,QAAQ,OAAO,CAAC;AAAA,MACtJ;AAEA,mBAAaA,WAAU,CAAC;AAAA,QACvB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,IAAI,OAAO,MAAM,WAAW,UAAU,IAAI,EAAE,KAAK,KAAK,IAAI;AAAA,QAClE;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,OAAO,OAAO;AAEhB,YAAQ,UAAU;AAAA;AAAA;;;ACzClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,SAAS,uBAAuB,MAAM;AAE1C,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,WAAW,SAAU,OAAO;AAC/B,gBAAUC,WAAU,KAAK;AAEzB,eAASA,UAAS,QAAQ,SAAS;AAClC,wBAAgB,MAAMA,SAAQ;AAE9B,eAAO,2BAA2B,OAAOA,UAAS,aAAa,OAAO,eAAeA,SAAQ,GAAG,KAAK,MAAM,WAAW,eAAe,QAAQ,OAAO,CAAC;AAAA,MACtJ;AAEA,mBAAaA,WAAU,CAAC;AAAA,QACvB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,IAAI,OAAO,MAAM,WAAW,UAAU,IAAI,EAAE,KAAK,KAAK,IAAI;AAAA,QAClE;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,OAAO,OAAO;AAEhB,YAAQ,UAAU;AAAA;AAAA;;;ACzClB,IAAAC,mBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,WAAW,QAAQ,WAAW,QAAQ,WAAW,QAAQ,UAAU;AAE3E,QAAI,gBAAgB;AAEpB,QAAI,iBAAiB,uBAAuB,aAAa;AAEzD,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU,eAAe;AACjC,YAAQ,WAAW,WAAW;AAC9B,YAAQ,WAAW,WAAW;AAC9B,YAAQ,WAAW,WAAW;AAAA;AAAA;;;AC5B9B,IAAAC,qBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,WAAW,QAAQ,WAAW;AAClC,QAAI,aAAa,QAAQ,aAAa;AAEtC,QAAI,WAAW,QAAQ,WAAW;AAAA,MACjC,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,MAC5G,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,MAC5G,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,MAC5G,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,MAC5G,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,IAC7G;AAGA,QAAI,iBAAiB,QAAQ,iBAAiB,CAAC,MAAM,MAAM,MAAM,IAAI;AAGrE,QAAI,iBAAiB,QAAQ,iBAAiB,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,OAAO;AAGvI,QAAI,kBAAkB,QAAQ,kBAAkB,CAAC,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,QAAQ;AAAA;AAAA;;;AC7BnJ;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,aAAa;AAGjB,QAAI,SAAS,SAASC,QAAO,MAAM,WAAW,WAAW;AACxD,UAAI,UAAU,KAAK,MAAM,EAAE,EAAE,IAAI,SAAU,KAAK,KAAK;AACpD,eAAO,WAAW,SAAS,UAAU,GAAG,CAAC;AAAA,MAC1C,CAAC,EAAE,IAAI,SAAU,KAAK,KAAK;AAC1B,eAAO,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI;AAAA,MAC/B,CAAC;AAED,UAAI,WAAW;AACd,YAAI,OAAO,KAAK,SAAS;AACzB,kBAAU,QAAQ,IAAI,SAAU,KAAK,KAAK;AACzC,iBAAO,MAAM,OAAO,MAAM,YAAY;AAAA,QACvC,CAAC;AAAA,MACF;AAEA,aAAO,QAAQ,KAAK,EAAE;AAAA,IACvB;AAEA,YAAQ,UAAU;AAAA;AAAA;;;AC1BlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,aAAa;AAEjB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,MAAM,SAAU,UAAU;AAC7B,gBAAUC,MAAK,QAAQ;AAEvB,eAASA,KAAI,MAAM,SAAS;AAC3B,wBAAgB,MAAMA,IAAG;AAGzB,YAAI,QAAQ,2BAA2B,OAAOA,KAAI,aAAa,OAAO,eAAeA,IAAG,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAEpH,cAAM,WAAW,CAAC,QAAQ,QAAQ,QAAQ,WAAW,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,KAAK,QAAQ;AAGvG,cAAM,cAAc,QAAQ,SAAS,MAAM,WAAW,IAAI,QAAQ;AAClE,eAAO;AAAA,MACR;AAEA,mBAAaA,MAAK,CAAC;AAAA,QAClB,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,iBAAO,KAAK,QAAQ,OAAO,KAAK,WAAW,IAAI,KAAK,cAAc;AAAA,QACnE;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS,MAAM,IAAI;AAClC,iBAAO,KAAK,KAAK,OAAO,MAAM,EAAE;AAAA,QACjC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,WAAW,MAAM,WAAW;AAC3C,kBAAQ,GAAG,UAAU,SAAS,MAAM,SAAS;AAAA,QAC9C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,UAAU,MAAM,IAAI;AACnC,iBAAO,KAAK,KAAK,OAAO,MAAM,EAAE;AAAA,QACjC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY,MAAM,WAAW;AAC5C,kBAAQ,GAAG,UAAU,SAAS,MAAM,SAAS;AAAA,QAC9C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAC/B,cAAI,cAAc,EAAE,UAAU,KAAK,SAAS;AAC5C,cAAI,eAAe,EAAE,QAAQ,KAAK,YAAY;AAE9C,iBAAO,CAAC,EAAE,MAAM,WAAW,UAAU,SAAS,aAAa,GAAG,EAAE,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,SAAS,GAAG,SAAS,YAAY,GAAG,EAAE,MAAM,WAAW,YAAY,SAAS,aAAa,GAAG,EAAE,MAAM,KAAK,YAAY,GAAG,MAAM,KAAK,UAAU,GAAG,SAAS,YAAY,GAAG,EAAE,MAAM,WAAW,UAAU,SAAS,aAAa,CAAC;AAAA,QACjU;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa;AAC5B,cAAI,OAAO,CAAC,WAAW,UAAU,KAAK,WAAW,GAAG,WAAW,YAAY,KAAK,YAAY,GAAG,WAAW,QAAQ;AAElH,iBAAO;AAAA,YACN,MAAM,KAAK,KAAK,EAAE;AAAA,YAClB,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;AC3FlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,OAAO,SAAS,IAAI,QAAQ,UAAU,UAAU;AAAE,UAAI,WAAW,KAAM,UAAS,SAAS;AAAW,UAAI,OAAO,OAAO,yBAAyB,QAAQ,QAAQ;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,SAAS,OAAO,eAAe,MAAM;AAAG,YAAI,WAAW,MAAM;AAAE,iBAAO;AAAA,QAAW,OAAO;AAAE,iBAAO,IAAI,QAAQ,UAAU,QAAQ;AAAA,QAAG;AAAA,MAAE,WAAW,WAAW,MAAM;AAAE,eAAO,KAAK;AAAA,MAAO,OAAO;AAAE,YAAI,SAAS,KAAK;AAAK,YAAI,WAAW,QAAW;AAAE,iBAAO;AAAA,QAAW;AAAE,eAAO,OAAO,KAAK,QAAQ;AAAA,MAAG;AAAA,IAAE;AAEze,QAAI,aAAa;AAEjB,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAK7e,QAAI,WAAW,SAASC,UAAS,QAAQ;AACxC,UAAI,MAAM,OAAO,OAAO,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AACzD,eAAO,CAAC;AAAA,MACT,CAAC,EAAE,OAAO,SAAU,KAAKC,IAAG,KAAK;AAChC,eAAO,MAAM,IAAI,MAAMA,KAAI,IAAI,MAAMA;AAAA,MACtC,GAAG,CAAC;AAEJ,cAAQ,KAAK,MAAM,MAAM;AAAA,IAC1B;AAEA,QAAI,QAAQ,SAAU,MAAM;AAC3B,gBAAUC,QAAO,IAAI;AAErB,eAASA,OAAM,MAAM,SAAS;AAC7B,wBAAgB,MAAMA,MAAK;AAG3B,YAAI,KAAK,OAAO,aAAa,MAAM,IAAI;AACtC,kBAAQ,SAAS,IAAI;AAAA,QACtB;AAGA,YAAI,QAAQ,2BAA2B,OAAOA,OAAM,aAAa,OAAO,eAAeA,MAAK,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAExH,cAAM,WAAW,QAAQ;AACzB,eAAO;AAAA,MACR;AAEA,mBAAaA,QAAO,CAAC;AAAA,QACpB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,aAAa,MAAM,MAAM,CAAC,KAAK,KAAK,EAAE,MAAM,SAAS,KAAK,IAAI;AAAA,QACvF;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,WAAW;AAC1B,iBAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,YAAY,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,QACnH;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa;AAC5B,cAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,cAAI,YAAY,WAAW,gBAAgB,KAAK,KAAK,CAAC,CAAC;AACvD,iBAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,cAAc,IAAI,EAAE,KAAK,MAAM,MAAM,SAAS;AAAA,QAChI;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY;AAC3B,iBAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,aAAa,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,QACpH;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,cAAc;AAC7B,cAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,iBAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,eAAe,IAAI,EAAE,KAAK,MAAM,MAAM,QAAQ;AAAA,QAChI;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAC/B,cAAI,OAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,iBAAiB,IAAI,EAAE,KAAK,IAAI;AAGrH,cAAI,KAAK,QAAQ,cAAc;AAC9B,iBAAK,QAAQ;AAAA,cACZ,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,cAC3B,SAAS,EAAE,WAAW,QAAQ,UAAU,KAAK,SAAS;AAAA,YACvD,CAAC;AAED,gBAAI,KAAK,QAAQ,UAAU;AAC1B,mBAAK,KAAK;AAAA,gBACT,MAAM;AAAA,cACP,CAAC;AACD,mBAAK,KAAK;AAAA,gBACT,MAAM;AAAA,gBACN,MAAM,KAAK,QAAQ;AAAA,gBACnB,SAAS,EAAE,UAAU,KAAK,SAAS;AAAA,cACpC,CAAC;AAAA,YACF;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;ACtHlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,OAAO,SAAS,IAAI,QAAQ,UAAU,UAAU;AAAE,UAAI,WAAW,KAAM,UAAS,SAAS;AAAW,UAAI,OAAO,OAAO,yBAAyB,QAAQ,QAAQ;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,SAAS,OAAO,eAAe,MAAM;AAAG,YAAI,WAAW,MAAM;AAAE,iBAAO;AAAA,QAAW,OAAO;AAAE,iBAAO,IAAI,QAAQ,UAAU,QAAQ;AAAA,QAAG;AAAA,MAAE,WAAW,WAAW,MAAM;AAAE,eAAO,KAAK;AAAA,MAAO,OAAO;AAAE,YAAI,SAAS,KAAK;AAAK,YAAI,WAAW,QAAW;AAAE,iBAAO;AAAA,QAAW;AAAE,eAAO,OAAO,KAAK,QAAQ;AAAA,MAAG;AAAA,IAAE;AAEze,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAI7e,QAAI,WAAW,SAASC,UAAS,QAAQ;AACxC,UAAI,MAAM,OAAO,OAAO,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AACxD,eAAO,CAAC;AAAA,MACT,CAAC,EAAE,OAAO,SAAU,KAAKC,IAAG,KAAK;AAChC,eAAO,MAAM,IAAI,MAAMA,KAAI,MAAMA,KAAI;AAAA,MACtC,GAAG,CAAC;AAEJ,cAAQ,KAAK,MAAM,MAAM;AAAA,IAC1B;AAEA,QAAI,OAAO,SAAU,MAAM;AAC1B,gBAAUC,OAAM,IAAI;AAEpB,eAASA,MAAK,MAAM,SAAS;AAC5B,wBAAgB,MAAMA,KAAI;AAG1B,YAAI,KAAK,OAAO,YAAY,MAAM,IAAI;AACrC,kBAAQ,SAAS,IAAI;AAAA,QACtB;AAEA,eAAO,2BAA2B,OAAOA,MAAK,aAAa,OAAO,eAAeA,KAAI,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MAClH;AAEA,mBAAaA,OAAM,CAAC;AAAA,QACnB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,YAAY,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,SAAS,KAAK,IAAI;AAAA,QACrF;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,WAAW;AAC1B,iBAAO,KAAKA,MAAK,UAAU,aAAa,OAAO,eAAeA,MAAK,SAAS,GAAG,YAAY,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,QACjH;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa;AAC5B,cAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,iBAAO,KAAKA,MAAK,UAAU,aAAa,OAAO,eAAeA,MAAK,SAAS,GAAG,cAAc,IAAI,EAAE,KAAK,MAAM,MAAM,MAAM;AAAA,QAC3H;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY;AAC3B,iBAAO,KAAKA,MAAK,UAAU,aAAa,OAAO,eAAeA,MAAK,SAAS,GAAG,aAAa,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,QAClH;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,cAAc;AAC7B,cAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,iBAAO,KAAKA,MAAK,UAAU,aAAa,OAAO,eAAeA,MAAK,SAAS,GAAG,eAAe,IAAI,EAAE,KAAK,MAAM,MAAM,MAAM;AAAA,QAC5H;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AChFlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,aAAa;AAEjB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,WAAW,SAASC,UAAS,MAAM;AACtC,UAAI,SAAS,KAAK,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AAC5C,eAAO,CAAC;AAAA,MACT,CAAC,EAAE,OAAO,SAAU,KAAKC,IAAG,KAAK;AAChC,eAAO,MAAM,IAAI,MAAMA,KAAI,IAAI,MAAMA,KAAI;AAAA,MAC1C,GAAG,CAAC;AACJ,aAAO,SAAS;AAAA,IACjB;AAEA,QAAI,OAAO,SAAU,UAAU;AAC9B,gBAAUC,OAAM,QAAQ;AAExB,eAASA,MAAK,MAAM,SAAS;AAC5B,wBAAgB,MAAMA,KAAI;AAE1B,eAAO,2BAA2B,OAAOA,MAAK,aAAa,OAAO,eAAeA,KAAI,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MAClH;AAEA,mBAAaA,OAAM,CAAC;AAAA,QACnB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,YAAY,MAAM;AAAA,QAC3C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,YAAY,WAAW,eAAe,SAAS,KAAK,IAAI,CAAC;AAC7D,iBAAO;AAAA,YACN,MAAM,UAAU,GAAG,UAAU,SAAS,KAAK,MAAM,WAAW,IAAI;AAAA,YAChE,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;AChElB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,aAAa;AAEjB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,OAAO,SAAU,UAAU;AAC9B,gBAAUC,OAAM,QAAQ;AAExB,eAASA,MAAK,MAAM,SAAS;AAC5B,wBAAgB,MAAMA,KAAI;AAE1B,eAAO,2BAA2B,OAAOA,MAAK,aAAa,OAAO,eAAeA,KAAI,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MAClH;AAEA,mBAAaA,OAAM,CAAC;AAAA,QACnB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,YAAY,MAAM;AAAA,QAC3C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AAExB,cAAI,YAAY,WAAW,eAAe,SAAS,KAAK,IAAI,IAAI,CAAC;AACjE,iBAAO;AAAA;AAAA,YAEN,MAAM,UAAU,GAAG,UAAU,SAAS,KAAK,MAAM,WAAW,IAAI;AAAA,YAChE,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;ACzDlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,YAAQ,WAAW;AAEnB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,MAAM,SAAU,UAAU;AAC7B,gBAAUC,MAAK,QAAQ;AAEvB,eAASA,KAAI,MAAM,SAAS;AAC3B,wBAAgB,MAAMA,IAAG;AAGzB,YAAI,KAAK,OAAO,aAAa,MAAM,IAAI;AACtC,kBAAQ,SAAS,IAAI;AAAA,QACtB;AAEA,YAAI,QAAQ,2BAA2B,OAAOA,KAAI,aAAa,OAAO,eAAeA,IAAG,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAEpH,cAAM,eAAe,QAAQ;AAG7B,YAAI,QAAQ,WAAW,QAAQ,QAAQ,IAAI;AAC1C,gBAAM,WAAW,QAAQ,QAAQ;AAAA,QAClC,OAAO;AACN,gBAAM,WAAW,QAAQ;AAAA,QAC1B;AAGA,cAAM,cAAc,QAAQ,SAAS,MAAM,WAAW,IAAI,QAAQ;AAClE,eAAO;AAAA,MACR;AAEA,mBAAaA,MAAK,CAAC;AAAA,QAClB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,aAAa,MAAM,MAAM,KAAK,KAAK,EAAE,KAAK,SAAS,KAAK,IAAI;AAAA,QACrF;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,KAAK,QAAQ,MAAM;AACtB,mBAAO,KAAK,aAAa;AAAA,UAC1B,OAAO;AACN,mBAAO,KAAK,gBAAgB;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe;AAC9B,cAAI,SAAS;AAEb,oBAAU;AACV,qBAAW,GAAG,UAAU,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,QAAQ;AACjE,oBAAU;AACV,qBAAW,GAAG,UAAU,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,QAAQ;AACjE,oBAAU;AAEV,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB;AACjC,cAAI,SAAS,CAAC;AAGd,cAAI,KAAK,cAAc;AACtB,mBAAO,KAAK;AAAA,cACX,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,cAC3B,SAAS,EAAE,WAAW,QAAQ,UAAU,KAAK,SAAS;AAAA,YACvD,CAAC;AAAA,UACF;AAGA,iBAAO,KAAK;AAAA,YACX,MAAM,SAAS,GAAG,UAAU,SAAS,KAAK,KAAK,CAAC,GAAG,GAAG;AAAA,YACtD,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,OAAO,GAAG,UAAU,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,OAAO;AAAA,YAC5D,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,YAC3B,SAAS,EAAE,UAAU,KAAK,SAAS;AAAA,UACpC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,MAAM;AAAA,YACN,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,OAAO,GAAG,UAAU,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,OAAO;AAAA,YAC5D,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,YAC3B,SAAS,EAAE,UAAU,KAAK,SAAS;AAAA,UACpC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,OAAO,GAAG,UAAU,SAAS,KAAK,KAAK,EAAE,GAAG,GAAG,IAAI;AAAA,YACnD,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,cAAI,KAAK,cAAc;AACtB,mBAAO,KAAK;AAAA,cACX,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC;AAAA,cAC5B,SAAS,EAAE,WAAW,SAAS,UAAU,KAAK,SAAS;AAAA,YACxD,CAAC;AAAA,UACF;AAEA,iBAAO;AAAA,QACR;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAMnB,aAAS,SAAS,QAAQ;AACzB,UAAI,SAAS;AAEb,UAAID;AACJ,WAAKA,KAAI,GAAGA,KAAI,IAAIA,MAAK,GAAG;AAC3B,kBAAU,SAAS,OAAOA,EAAC,CAAC;AAAA,MAC7B;AACA,WAAKA,KAAI,GAAGA,KAAI,IAAIA,MAAK,GAAG;AAC3B,kBAAU,SAAS,OAAOA,EAAC,CAAC,IAAI;AAAA,MACjC;AAEA,cAAQ,KAAK,SAAS,MAAM;AAAA,IAC7B;AAEA,YAAQ,UAAU;AAAA;AAAA;;;ACpKlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASE,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,QAAI,OAAO;AAEX,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAM7e,QAAI,aAAa,CAAC,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,YAAY;AAE5J,QAAI,WAAW,CAAC,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,CAAC;AAE1O,QAAI,OAAO,SAAU,UAAU;AAC9B,gBAAUC,OAAM,QAAQ;AAExB,eAASA,MAAK,MAAM,SAAS;AAC5B,wBAAgB,MAAMA,KAAI;AAE1B,YAAI,QAAQ,2BAA2B,OAAOA,MAAK,aAAa,OAAO,eAAeA,KAAI,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAMtH,cAAM,UAAU;AAChB,YAAI,KAAK,OAAO,YAAY,MAAM,IAAI;AACrC,gBAAM,eAAe;AACrB,gBAAM,OAAO,aAAa,MAAM,GAAG;AACnC,gBAAM,OAAO,QAAQ,QAAQ,KAAK,MAAM,KAAK,CAAC,IAAI,OAAO,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC;AACzF,gBAAM,UAAU;AAAA,QACjB,WAAW,KAAK,OAAO,gBAAgB,MAAM,IAAI;AAChD,gBAAM,eAAe,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AACtD,gBAAM,OAAO,aAAa,MAAM,cAAc,KAAK,CAAC,CAAC;AAErD,cAAI,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,MAAM,KAAK,KAAK,SAAS,CAAC,GAAG;AAChE,kBAAM,UAAU;AAAA,UACjB,OAAO;AAEN,mBAAO,2BAA2B,KAAK;AAAA,UACxC;AAAA,QACD,OAAO;AACN,iBAAO,2BAA2B,KAAK;AAAA,QACxC;AAEA,cAAM,eAAe,QAAQ;AAG7B,YAAI,QAAQ,WAAW,QAAQ,QAAQ,IAAI;AAC1C,gBAAM,WAAW,QAAQ,QAAQ;AAAA,QAClC,OAAO;AACN,gBAAM,WAAW,QAAQ;AAAA,QAC1B;AAGA,cAAM,cAAc,QAAQ,SAAS,MAAM,WAAW,IAAI,QAAQ;AAClE,eAAO;AAAA,MACR;AAEA,mBAAaA,OAAM,CAAC;AAAA,QACnB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK;AAAA,QACb;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,KAAK,QAAQ,MAAM;AACtB,mBAAO,KAAK,aAAa;AAAA,UAC1B,OAAO;AACN,mBAAO,KAAK,gBAAgB;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe;AAC9B,cAAI,SAAS;AAEb,oBAAU;AACV,oBAAU,KAAK,mBAAmB;AAClC,oBAAU;AAEV,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB;AACjC,cAAI,SAAS,CAAC;AAGd,cAAI,KAAK,cAAc;AACtB,mBAAO,KAAK;AAAA,cACX,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,CAAC;AAAA,cACjB,SAAS,EAAE,WAAW,QAAQ,UAAU,KAAK,SAAS;AAAA,YACvD,CAAC;AAAA,UACF;AAGA,iBAAO,KAAK;AAAA,YACX,MAAM;AAAA,YACN,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,MAAM,KAAK,mBAAmB;AAAA,YAC9B,MAAM,KAAK,KAAK,UAAU,GAAG,CAAC;AAAA,YAC9B,SAAS,EAAE,UAAU,KAAK,SAAS;AAAA,UACpC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,MAAM;AAAA,YACN,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,cAAI,KAAK,cAAc;AACtB,mBAAO,KAAK;AAAA,cACX,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,CAAC;AAAA,cACjB,SAAS,EAAE,WAAW,SAAS,UAAU,KAAK,SAAS;AAAA,YACxD,CAAC;AAAA,UACF;AAEA,iBAAO;AAAA,QACR;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,qBAAqB;AACpC,cAAI,eAAe,KAAK,KAAK,CAAC;AAC9B,cAAI,aAAa,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAC/C,cAAI,SAAS,SAAS,SAAS,UAAU,CAAC,EAAE,SAAS,YAAY,CAAC;AAClE,kBAAQ,GAAG,UAAU,SAAS,KAAK,cAAc,MAAM;AAAA,QACxD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,aAAS,aAAa,cAAc,cAAc;AACjD,UAAI,WAAW,SAAS,aAAa,aAAa,SAAS,CAAC,CAAC;AAC7D,UAAI,YAAY,WAAW,QAAQ;AAEnC,UAAI,SAAS;AACb,UAAI,aAAa;AACjB,eAASD,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAC1C,YAAIE,KAAI,UAAUF,EAAC;AACnB,YAAIE,OAAM,KAAK;AACd,oBAAU,aAAa,YAAY;AAAA,QACpC,OAAO;AACN,oBAAUA;AAAA,QACX;AAAA,MACD;AAEA,eAAS,KAAK,eAAe;AAC7B,aAAO,KAAK,UAAU,GAAG,KAAK,UAAU,MAAM;AAAA,IAC/C;AAEA,YAAQ,UAAU;AAAA;AAAA;;;ACxLlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,OAAO,QAAQ,MAAM,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ,QAAQ;AAE1F,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,QAAQ;AAEZ,QAAI,SAAS,uBAAuB,KAAK;AAEzC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,QAAQ,MAAM;AACtB,YAAQ,OAAO,MAAM;AACrB,YAAQ,OAAO,MAAM;AACrB,YAAQ,OAAO,MAAM;AACrB,YAAQ,MAAM,MAAM;AACpB,YAAQ,OAAO,OAAO;AAAA;AAAA;;;ACtCtB,IAAAC,qBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,QAAI,YAAY,QAAQ,YAAY;AACpC,QAAI,UAAU,QAAQ,UAAU;AAEhC,QAAI,WAAW,QAAQ,WAAW,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,OAAO;AAAA;AAAA;;;ACR3H;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,aAAa;AAEjB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,MAAM,SAAU,UAAU;AAC7B,gBAAUC,MAAK,QAAQ;AAEvB,eAASA,OAAM;AACd,wBAAgB,MAAMA,IAAG;AAEzB,eAAO,2BAA2B,OAAOA,KAAI,aAAa,OAAO,eAAeA,IAAG,GAAG,MAAM,MAAM,SAAS,CAAC;AAAA,MAC7G;AAEA,mBAAaA,MAAK,CAAC;AAAA,QAClB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,eAAe,MAAM;AAAA,QAC9C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,SAAS;AAGb,cAAI,UAAU,KAAK,KAAK,MAAM,OAAO,EAAE,IAAI,SAAU,MAAM;AAC1D,mBAAO,OAAO,WAAW,IAAI;AAAA,UAC9B,CAAC,EAAE,KAAK,EAAE;AAEV,iBAAO;AAAA,YACN,MAAM,WAAW,YAAY,UAAU,WAAW;AAAA,YAClD,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,WAAW,MAAM;AAChC,cAAI,SAAS,WAAW,SAAS,KAAK,CAAC,CAAC;AAExC,iBAAO,WAAW,SAAS,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,OAAO,KAAK;AACvE,oBAAQ,UAAU,MAAM,QAAQ,QAAQ,OAAO,GAAG,MAAM,MAAM,QAAQ;AAAA,UACvE,CAAC,EAAE,KAAK,EAAE;AAAA,QACX;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;ACpElB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,WAAW,SAASC,UAAS,MAAM;AACtC,UAAI,MAAM,KAAK,OAAO,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,KAAK;AACzD,eAAO,SAAS,KAAK,EAAE;AAAA,MACxB,CAAC,EAAE,OAAO,SAAU,KAAK,GAAG,KAAK;AAChC,eAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,MACjC,GAAG,CAAC;AAEJ,aAAO,KAAK,KAAK,MAAM,EAAE,IAAI,KAAK;AAAA,IACnC;AAEA,QAAI,QAAQ,SAAU,MAAM;AAC3B,gBAAUC,QAAO,IAAI;AAErB,eAASA,OAAM,MAAM,SAAS;AAC7B,wBAAgB,MAAMA,MAAK;AAG3B,YAAI,KAAK,OAAO,aAAa,MAAM,IAAI;AACtC,kBAAQ,SAAS,IAAI;AAAA,QACtB;AACA,eAAO,2BAA2B,OAAOA,OAAM,aAAa,OAAO,eAAeA,MAAK,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACpH;AAEA,mBAAaA,QAAO,CAAC;AAAA,QACpB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,aAAa,MAAM,MAAM,CAAC,KAAK,KAAK,EAAE,MAAM,SAAS,KAAK,IAAI;AAAA,QACvF;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;ACtDlB,IAAAC,eAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,QAAQ,QAAQ,MAAM;AAE9B,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,MAAM,MAAM;AACpB,YAAQ,QAAQ,MAAM;AAAA;AAAA;;;AClBtB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,MAAM,SAAU,UAAU;AAC7B,gBAAUC,MAAK,QAAQ;AAEvB,eAASA,KAAI,MAAM,SAAS;AAC3B,wBAAgB,MAAMA,IAAG;AAEzB,eAAO,2BAA2B,OAAOA,KAAI,aAAa,OAAO,eAAeA,IAAG,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MAChH;AAEA,mBAAaA,MAAK,CAAC;AAAA,QAClB,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AAExB,cAAI,MAAM;AAEV,mBAASD,KAAI,GAAGA,KAAI,KAAK,KAAK,QAAQA,MAAK;AAE1C,gBAAI,QAAQ,SAAS,KAAK,KAAKA,EAAC,CAAC;AACjC,gBAAI,MAAM,MAAM,SAAS,CAAC;AAC1B,kBAAM,UAAU,KAAK,IAAI,IAAI,MAAM;AAGnC,qBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,qBAAO,IAAI,CAAC,KAAK,MAAM,QAAQ;AAAA,YAChC;AAAA,UACD;AAGA,iBAAO;AAEP,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,UAAU,MAAM;AAAA,QACzC;AAAA,MACD,CAAC,CAAC;AAEF,aAAOC;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,aAAS,UAAU,QAAQ,GAAG;AAC7B,eAASD,KAAI,GAAGA,KAAI,GAAGA,MAAK;AAC3B,iBAAS,MAAM;AAAA,MAChB;AACA,aAAO;AAAA,IACR;AAEA,YAAQ,UAAU;AAAA;AAAA;;;ACzElB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,QAAQ;AAChB,YAAQ,QAAQ;AAChB,aAAS,MAAM,QAAQ;AACtB,UAAI,MAAM;AACV,eAASE,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACvC,YAAI,IAAI,SAAS,OAAOA,EAAC,CAAC;AAC1B,aAAKA,KAAI,OAAO,UAAU,MAAM,GAAG;AAClC,iBAAO;AAAA,QACR,OAAO;AACN,iBAAO,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,IAAI,EAAE;AAAA,QAC1C;AAAA,MACD;AACA,cAAQ,KAAK,MAAM,MAAM;AAAA,IAC1B;AAEA,aAAS,MAAM,QAAQ;AACtB,UAAI,MAAM;AACV,UAAI,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,eAASA,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACvC,YAAI,IAAI,SAAS,OAAO,OAAO,SAAS,IAAIA,EAAC,CAAC;AAC9C,eAAO,QAAQA,KAAI,QAAQ,MAAM,IAAI;AAAA,MACtC;AACA,cAAQ,KAAK,MAAM,MAAM;AAAA,IAC1B;AAAA;AAAA;;;AC5BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,QAAQ,SAAU,MAAM;AAC3B,gBAAUC,QAAO,IAAI;AAErB,eAASA,OAAM,MAAM,SAAS;AAC7B,wBAAgB,MAAMA,MAAK;AAE3B,eAAO,2BAA2B,OAAOA,OAAM,aAAa,OAAO,eAAeA,MAAK,GAAG,KAAK,MAAM,QAAQ,GAAG,WAAW,OAAO,IAAI,GAAG,OAAO,CAAC;AAAA,MAClJ;AAEA,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AChClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,QAAQ,SAAU,MAAM;AAC3B,gBAAUC,QAAO,IAAI;AAErB,eAASA,OAAM,MAAM,SAAS;AAC7B,wBAAgB,MAAMA,MAAK;AAE3B,eAAO,2BAA2B,OAAOA,OAAM,aAAa,OAAO,eAAeA,MAAK,GAAG,KAAK,MAAM,QAAQ,GAAG,WAAW,OAAO,IAAI,GAAG,OAAO,CAAC;AAAA,MAClJ;AAEA,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AChClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,UAAU,SAAU,MAAM;AAC7B,gBAAUC,UAAS,IAAI;AAEvB,eAASA,SAAQ,MAAM,SAAS;AAC/B,wBAAgB,MAAMA,QAAO;AAE7B,iBAAS,GAAG,WAAW,OAAO,IAAI;AAClC,iBAAS,GAAG,WAAW,OAAO,IAAI;AAClC,eAAO,2BAA2B,OAAOA,SAAQ,aAAa,OAAO,eAAeA,QAAO,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACxH;AAEA,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AClClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,UAAU,SAAU,MAAM;AAC7B,gBAAUC,UAAS,IAAI;AAEvB,eAASA,SAAQ,MAAM,SAAS;AAC/B,wBAAgB,MAAMA,QAAO;AAE7B,iBAAS,GAAG,WAAW,OAAO,IAAI;AAClC,iBAAS,GAAG,WAAW,OAAO,IAAI;AAClC,eAAO,2BAA2B,OAAOA,SAAQ,aAAa,OAAO,eAAeA,QAAO,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACxH;AAEA,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AClClB,IAAAC,eAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAElF,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,SAAS,uBAAuB,KAAK;AAEzC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,MAAM,MAAM;AACpB,YAAQ,QAAQ,MAAM;AACtB,YAAQ,QAAQ,MAAM;AACtB,YAAQ,UAAU,MAAM;AACxB,YAAQ,UAAU,OAAO;AAAA;AAAA;;;ACjCzB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,aAAa;AAErB,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,aAAa,SAAU,UAAU;AACpC,gBAAUC,aAAY,QAAQ;AAE9B,eAASA,YAAW,MAAM,SAAS;AAClC,wBAAgB,MAAMA,WAAU;AAEhC,YAAI,QAAQ,2BAA2B,OAAOA,YAAW,aAAa,OAAO,eAAeA,WAAU,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAElI,cAAM,SAAS,SAAS,MAAM,EAAE;AAChC,eAAO;AAAA,MACR;AAEA,mBAAaA,aAAY,CAAC;AAAA,QACzB,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAIC,KAAI,KAAK;AACb,cAAI,SAAS;AAIb,iBAAO,CAAC,MAAMA,EAAC,KAAKA,MAAK,GAAG;AAC3B,gBAAIA,KAAI,MAAM,GAAG;AAEhB,uBAAS,UAAU;AACnB,cAAAA,MAAKA,KAAI,KAAK;AAAA,YACf,OAAO;AAEN,uBAAS,QAAQ;AACjB,cAAAA,MAAKA,KAAI,KAAK;AAAA,YACf;AAAA,UACD;AAGA,mBAAS,OAAO,MAAM,GAAG,EAAE;AAE3B,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,UAAU,KAAK,KAAK,UAAU;AAAA,QAC3C;AAAA,MACD,CAAC,CAAC;AAEF,aAAOD;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,aAAa;AAAA;AAAA;;;ACxErB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASE,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,UAAU,SAAU,UAAU;AACjC,gBAAUC,UAAS,QAAQ;AAE3B,eAASA,SAAQ,MAAM,SAAS;AAC/B,wBAAgB,MAAMA,QAAO;AAE7B,YAAI,KAAK,OAAO,sBAAsB,MAAM,GAAG;AAC9C,iBAAO,MAAM,OAAO;AAAA,QACrB;AAEA,YAAI,QAAQ,2BAA2B,OAAOA,SAAQ,aAAa,OAAO,eAAeA,QAAO,GAAG,KAAK,MAAM,KAAK,YAAY,GAAG,OAAO,CAAC;AAE1I,cAAM,OAAO,MAAM,QAAQ,QAAQ,MAAM,KAAK,QAAQ,UAAU,EAAE;AAClE,eAAO;AAAA,MACR;AAEA,mBAAaA,UAAS,CAAC;AAAA,QACtB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,gCAAgC,MAAM;AAAA,QAC/D;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,SAAS,CAAC;AACd,cAAI,YAAY,KAAK,aAAa;AAClC,mBAASD,KAAI,GAAGA,KAAI,KAAK,KAAK,QAAQA,MAAK;AAC1C,mBAAO,KAAK,UAAU,KAAK,KAAK,OAAOA,EAAC,CAAC,CAAC;AAE1C,gBAAIA,OAAM,KAAK,KAAK,SAAS,GAAG;AAC/B,qBAAO,KAAK,GAAG;AAAA,YAChB;AAAA,UACD;AACA,iBAAO;AAAA,YACN,MAAM,KAAK;AAAA,YACX,MAAM,OAAO,KAAK,EAAE;AAAA,UACrB;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe;AAC9B,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,UACN;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOC;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;AC3FlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,iBAAiB;AAEzB,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,iBAAiB,SAAU,UAAU;AACxC,gBAAUC,iBAAgB,QAAQ;AAElC,eAASA,gBAAe,MAAM,SAAS;AACtC,wBAAgB,MAAMA,eAAc;AAEpC,eAAO,2BAA2B,OAAOA,gBAAe,aAAa,OAAO,eAAeA,eAAc,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACtI;AAKA,mBAAaA,iBAAgB,CAAC;AAAA,QAC7B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO;AAAA,QACR;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,iBAAiB;AAAA;AAAA;;;ACtDzB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,SAAS;AAEb,QAAI,WAAW;AAEf,QAAI,OAAO;AAEX,QAAI,OAAO;AAEX,QAAI,cAAc;AAElB,QAAI,WAAW;AAEf,QAAI,kBAAkB;AAEtB,YAAQ,UAAU;AAAA,MACjB,QAAQ,MAAM;AAAA,MACd,SAAS,OAAO;AAAA,MAAS,UAAU,OAAO;AAAA,MAAU,UAAU,OAAO;AAAA,MAAU,UAAU,OAAO;AAAA,MAChG,OAAO,SAAS;AAAA,MAAO,MAAM,SAAS;AAAA,MAAM,MAAM,SAAS;AAAA,MAAM,MAAM,SAAS;AAAA,MAAM,KAAK,SAAS;AAAA,MAAK,MAAM,SAAS;AAAA,MACxH,OAAO,KAAK;AAAA,MACZ,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MAAK,OAAO,KAAK;AAAA,MAAO,OAAO,KAAK;AAAA,MAAO,SAAS,KAAK;AAAA,MAAS,SAAS,KAAK;AAAA,MAC1F,YAAY,YAAY;AAAA,MACxB,SAAS,SAAS;AAAA,MAClB,gBAAgB,gBAAgB;AAAA,IACjC;AAAA;AAAA;;;AChCA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,WAAW,OAAO,UAAU,SAAU,QAAQ;AAAE,eAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAAE,YAAI,SAAS,UAAUA,EAAC;AAAG,iBAAS,OAAO,QAAQ;AAAE,cAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAE/P,YAAQ,UAAU,SAAU,KAAK,YAAY;AAC3C,aAAO,SAAS,CAAC,GAAG,KAAK,UAAU;AAAA,IACrC;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,UAAU;AAKlB,aAAS,mBAAmB,WAAW;AACtC,UAAI,kBAAkB,CAAC;AACvB,eAAS,UAAU,SAAS;AAC3B,YAAI,MAAM,QAAQ,OAAO,GAAG;AAC3B,mBAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACxC,sBAAU,QAAQA,EAAC,CAAC;AAAA,UACrB;AAAA,QACD,OAAO;AACN,kBAAQ,OAAO,QAAQ,QAAQ;AAC/B,kBAAQ,OAAO,QAAQ,QAAQ;AAC/B,0BAAgB,KAAK,OAAO;AAAA,QAC7B;AAAA,MACD;AACA,gBAAU,SAAS;AAEnB,aAAO;AAAA,IACR;AAAA;AAAA;;;AC1BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,UAAU;AAGlB,aAAS,WAAW,SAAS;AAE5B,cAAQ,YAAY,QAAQ,aAAa,QAAQ;AACjD,cAAQ,eAAe,QAAQ,gBAAgB,QAAQ;AACvD,cAAQ,cAAc,QAAQ,eAAe,QAAQ;AACrD,cAAQ,aAAa,QAAQ,cAAc,QAAQ;AAEnD,aAAO;AAAA,IACR;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,UAAU;AAIlB,aAAS,mBAAmB,SAAS;AACpC,UAAI,aAAa,CAAC,SAAS,UAAU,cAAc,YAAY,UAAU,aAAa,gBAAgB,cAAc,aAAa;AAEjI,eAAS,aAAa,YAAY;AACjC,YAAI,WAAW,eAAe,SAAS,GAAG;AACzC,sBAAY,WAAW,SAAS;AAChC,cAAI,OAAO,QAAQ,SAAS,MAAM,UAAU;AAC3C,oBAAQ,SAAS,IAAI,SAAS,QAAQ,SAAS,GAAG,EAAE;AAAA,UACrD;AAAA,QACD;AAAA,MACD;AAEA,UAAI,OAAO,QAAQ,cAAc,MAAM,UAAU;AAChD,gBAAQ,cAAc,IAAI,QAAQ,cAAc,KAAK;AAAA,MACtD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC1BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,QAAI,WAAW;AAAA,MACd,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO,SAAS,QAAQ;AAAA,MAAC;AAAA,IAC1B;AAEA,YAAQ,UAAU;AAAA;AAAA;;;AC3BlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,sBAAsB;AAE1B,QAAI,uBAAuB,uBAAuB,mBAAmB;AAErE,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,sBAAsB,SAAS;AACvC,UAAI,UAAU,CAAC;AACf,eAAS,YAAY,WAAW,SAAS;AACxC,YAAI,WAAW,QAAQ,eAAe,QAAQ,GAAG;AAEhD,cAAI,QAAQ,aAAa,eAAe,SAAS,YAAY,CAAC,GAAG;AAChE,oBAAQ,QAAQ,IAAI,QAAQ,aAAa,eAAe,SAAS,YAAY,CAAC;AAAA,UAC/E;AAGA,cAAI,QAAQ,aAAa,UAAU,SAAS,YAAY,CAAC,GAAG;AAC3D,oBAAQ,QAAQ,IAAI,QAAQ,aAAa,UAAU,SAAS,YAAY,CAAC;AAAA,UAC1E;AAAA,QACD;AAAA,MACD;AAEA,cAAQ,OAAO,IAAI,QAAQ,aAAa,iBAAiB,KAAK,QAAQ,aAAa,YAAY;AAG/F,iBAAW,GAAG,qBAAqB,SAAS,OAAO;AAEnD,aAAO;AAAA,IACR;AAEA,YAAQ,UAAU;AAAA;AAAA;;;ACxClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,2BAA2B,QAAQ,8BAA8B,QAAQ,oBAAoB,QAAQ,oBAAoB,QAAQ,8BAA8B;AAEvK,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,kBAAkB,UAAU,SAAS;AAC7C,aAAO,QAAQ,UAAU,QAAQ,gBAAgB,SAAS,KAAK,SAAS,IAAI,QAAQ,WAAW,QAAQ,aAAa,KAAK,QAAQ,YAAY,QAAQ;AAAA,IACtJ;AAEA,aAAS,kBAAkB,WAAW,cAAc,SAAS;AAC5D,UAAI,QAAQ,gBAAgB,eAAe,WAAW;AACrD,YAAI,QAAQ,aAAa,UAAU;AAClC,iBAAO,KAAK,OAAO,YAAY,gBAAgB,CAAC;AAAA,QACjD,WAAW,QAAQ,aAAa,QAAQ;AACvC,iBAAO;AAAA,QACR,WAAW,QAAQ,aAAa,SAAS;AACxC,iBAAO,KAAK,MAAM,YAAY,YAAY;AAAA,QAC3C;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,aAAS,4BAA4B,WAAW,gBAAgB,SAAS;AACxE,eAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAC1C,YAAI,WAAW,UAAUA,EAAC;AAC1B,YAAI,WAAW,GAAG,QAAQ,SAAS,gBAAgB,SAAS,OAAO;AAGnE,YAAI;AACJ,YAAI,QAAQ,cAAc;AACzB,sBAAY,YAAY,SAAS,MAAM,SAAS,OAAO;AAAA,QACxD,OAAO;AACN,sBAAY;AAAA,QACb;AAEA,YAAI,eAAe,SAAS,KAAK,SAAS,QAAQ;AAClD,iBAAS,QAAQ,KAAK,KAAK,KAAK,IAAI,WAAW,YAAY,CAAC;AAE5D,iBAAS,SAAS,kBAAkB,UAAU,OAAO;AAErD,iBAAS,iBAAiB,kBAAkB,WAAW,cAAc,OAAO;AAAA,MAC7E;AAAA,IACD;AAEA,aAAS,yBAAyB,WAAW;AAC5C,UAAI,aAAa;AACjB,eAASA,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAC1C,sBAAc,UAAUA,EAAC,EAAE;AAAA,MAC5B;AACA,aAAO;AAAA,IACR;AAEA,aAAS,4BAA4B,WAAW;AAC/C,UAAI,YAAY;AAChB,eAASA,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAC1C,YAAI,UAAUA,EAAC,EAAE,SAAS,WAAW;AACpC,sBAAY,UAAUA,EAAC,EAAE;AAAA,QAC1B;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,aAAS,YAAY,QAAQ,SAAS,SAAS;AAC9C,UAAI;AAEJ,UAAI,SAAS;AACZ,cAAM;AAAA,MACP,WAAW,OAAO,aAAa,aAAa;AAC3C,cAAM,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AAAA,MACvD,OAAO;AAGN,eAAO;AAAA,MACR;AACA,UAAI,OAAO,QAAQ,cAAc,MAAM,QAAQ,WAAW,QAAQ,QAAQ;AAG1E,UAAI,oBAAoB,IAAI,YAAY,MAAM;AAC9C,UAAI,CAAC,mBAAmB;AAIvB,eAAO;AAAA,MACR;AACA,UAAI,OAAO,kBAAkB;AAC7B,aAAO;AAAA,IACR;AAEA,YAAQ,8BAA8B;AACtC,YAAQ,oBAAoB;AAC5B,YAAQ,oBAAoB;AAC5B,YAAQ,8BAA8B;AACtC,YAAQ,2BAA2B;AAAA;AAAA;;;ACpGnC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,UAAU;AAEd,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,QAAI,iBAAiB,WAAY;AAChC,eAASC,gBAAe,QAAQ,WAAW,SAAS;AACnD,wBAAgB,MAAMA,eAAc;AAEpC,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,UAAU;AAAA,MAChB;AAEA,mBAAaA,iBAAgB,CAAC;AAAA,QAC7B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AAExB,cAAI,CAAC,KAAK,OAAO,YAAY;AAC5B,kBAAM,IAAI,MAAM,sCAAsC;AAAA,UACvD;AAEA,eAAK,cAAc;AACnB,mBAASD,KAAI,GAAGA,KAAI,KAAK,UAAU,QAAQA,MAAK;AAC/C,gBAAI,mBAAmB,GAAG,QAAQ,SAAS,KAAK,SAAS,KAAK,UAAUA,EAAC,EAAE,OAAO;AAElF,iBAAK,kBAAkB,iBAAiB,KAAK,UAAUA,EAAC,CAAC;AACzD,iBAAK,eAAe,iBAAiB,KAAK,UAAUA,EAAC,CAAC;AAEtD,iBAAK,kBAAkB,KAAK,UAAUA,EAAC,CAAC;AAAA,UACzC;AAEA,eAAK,cAAc;AAAA,QACpB;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAE/B,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,KAAK;AAET,WAAC,GAAG,QAAQ,6BAA6B,KAAK,WAAW,KAAK,SAAS,GAAG;AAC1E,cAAI,cAAc,GAAG,QAAQ,0BAA0B,KAAK,SAAS;AACrE,cAAI,aAAa,GAAG,QAAQ,6BAA6B,KAAK,SAAS;AAEvE,eAAK,OAAO,QAAQ,aAAa,KAAK,QAAQ,aAAa,KAAK,QAAQ;AAExE,eAAK,OAAO,SAAS;AAGrB,cAAI,UAAU,GAAG,GAAG,KAAK,OAAO,OAAO,KAAK,OAAO,MAAM;AACzD,cAAI,KAAK,QAAQ,YAAY;AAC5B,gBAAI,YAAY,KAAK,QAAQ;AAC7B,gBAAI,SAAS,GAAG,GAAG,KAAK,OAAO,OAAO,KAAK,OAAO,MAAM;AAAA,UACzD;AAEA,cAAI,UAAU,KAAK,QAAQ,YAAY,CAAC;AAAA,QACzC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB,SAAS,UAAU;AAEpD,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,SAAS,SAAS;AAGtB,cAAI;AACJ,cAAI,QAAQ,gBAAgB,OAAO;AAClC,oBAAQ,QAAQ,YAAY,QAAQ,WAAW,QAAQ;AAAA,UACxD,OAAO;AACN,oBAAQ,QAAQ;AAAA,UACjB;AAEA,cAAI,YAAY,QAAQ;AAExB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,gBAAI,IAAI,IAAI,QAAQ,QAAQ,SAAS;AAErC,gBAAI,OAAO,CAAC,MAAM,KAAK;AACtB,kBAAI,SAAS,GAAG,OAAO,QAAQ,OAAO,QAAQ,MAAM;AAAA,YACrD,WAAW,OAAO,CAAC,GAAG;AACrB,kBAAI,SAAS,GAAG,OAAO,QAAQ,OAAO,QAAQ,SAAS,OAAO,CAAC,CAAC;AAAA,YACjE;AAAA,UACD;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe,SAAS,UAAU;AAEjD,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,OAAO,QAAQ,cAAc,MAAM,QAAQ,WAAW,QAAQ,QAAQ;AAG1E,cAAI,QAAQ,cAAc;AACzB,gBAAI,GAAGE;AAEP,gBAAI,QAAQ,gBAAgB,OAAO;AAClC,cAAAA,KAAI,QAAQ,YAAY,QAAQ,WAAW,QAAQ;AAAA,YACpD,OAAO;AACN,cAAAA,KAAI,QAAQ,SAAS,QAAQ,aAAa,QAAQ,YAAY,QAAQ;AAAA,YACvE;AAEA,gBAAI,OAAO;AAGX,gBAAI,QAAQ,aAAa,UAAU,SAAS,iBAAiB,GAAG;AAC/D,kBAAI;AACJ,kBAAI,YAAY;AAAA,YACjB,WAAW,QAAQ,aAAa,SAAS;AACxC,kBAAI,SAAS,QAAQ;AACrB,kBAAI,YAAY;AAAA,YACjB,OAEK;AACH,kBAAI,SAAS,QAAQ;AACrB,kBAAI,YAAY;AAAA,YACjB;AAED,gBAAI,SAAS,SAAS,MAAM,GAAGA,EAAC;AAAA,UACjC;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB,UAAU;AAC3C,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,UAAU,SAAS,OAAO,CAAC;AAAA,QAChC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAE/B,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,QAAQ;AAAA,QACb;AAAA,MACD,CAAC,CAAC;AAEF,aAAOD;AAAA,IACR,EAAE;AAEF,YAAQ,UAAU;AAAA;AAAA;;;AC7JlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASE,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,UAAU;AAEd,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,QAAI,QAAQ;AAEZ,QAAI,cAAc,WAAY;AAC7B,eAASC,aAAY,KAAK,WAAW,SAAS;AAC7C,wBAAgB,MAAMA,YAAW;AAEjC,aAAK,MAAM;AACX,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,aAAK,WAAW,QAAQ,eAAe;AAAA,MACxC;AAEA,mBAAaA,cAAa,CAAC;AAAA,QAC1B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,WAAW,KAAK,QAAQ;AAE5B,eAAK,WAAW;AAChB,mBAASD,KAAI,GAAGA,KAAI,KAAK,UAAU,QAAQA,MAAK;AAC/C,gBAAI,WAAW,KAAK,UAAUA,EAAC;AAC/B,gBAAI,mBAAmB,GAAG,QAAQ,SAAS,KAAK,SAAS,SAAS,OAAO;AAEzE,gBAAI,QAAQ,KAAK,YAAY,UAAU,gBAAgB,WAAW,KAAK,GAAG;AAE1E,iBAAK,gBAAgB,OAAO,eAAe;AAE3C,iBAAK,eAAe,OAAO,iBAAiB,QAAQ;AACpD,iBAAK,YAAY,OAAO,iBAAiB,QAAQ;AAEjD,wBAAY,SAAS;AAAA,UACtB;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa;AAE5B,iBAAO,KAAK,IAAI,YAAY;AAC3B,iBAAK,IAAI,YAAY,KAAK,IAAI,UAAU;AAAA,UACzC;AAEA,WAAC,GAAG,QAAQ,6BAA6B,KAAK,WAAW,KAAK,OAAO;AACrE,cAAI,cAAc,GAAG,QAAQ,0BAA0B,KAAK,SAAS;AACrE,cAAI,aAAa,GAAG,QAAQ,6BAA6B,KAAK,SAAS;AAEvE,cAAI,QAAQ,aAAa,KAAK,QAAQ,aAAa,KAAK,QAAQ;AAChE,eAAK,iBAAiB,OAAO,SAAS;AAEtC,cAAI,KAAK,QAAQ,YAAY;AAC5B,iBAAK,SAAS,GAAG,GAAG,OAAO,WAAW,KAAK,GAAG,EAAE,aAAa,SAAS,UAAU,KAAK,QAAQ,aAAa,GAAG;AAAA,UAC9G;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe,QAAQ,SAAS,UAAU;AACzD,cAAI,SAAS,SAAS;AAGtB,cAAI;AACJ,cAAI,QAAQ,gBAAgB,OAAO;AAClC,oBAAQ,QAAQ,WAAW,QAAQ;AAAA,UACpC,OAAO;AACN,oBAAQ;AAAA,UACT;AAEA,cAAI,WAAW;AACf,cAAI,IAAI;AACR,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,gBAAI,IAAI,QAAQ,QAAQ,SAAS;AAEjC,gBAAI,OAAO,CAAC,MAAM,KAAK;AACtB;AAAA,YACD,WAAW,WAAW,GAAG;AACxB,mBAAK,SAAS,IAAI,QAAQ,QAAQ,UAAU,OAAO,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,MAAM;AACnG,yBAAW;AAAA,YACZ;AAAA,UACD;AAGA,cAAI,WAAW,GAAG;AACjB,iBAAK,SAAS,IAAI,QAAQ,SAAS,WAAW,IAAI,OAAO,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,MAAM;AAAA,UAC1G;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY,QAAQ,SAAS,UAAU;AACtD,cAAI,WAAW,KAAK,SAAS,gBAAgB,OAAO,MAAM;AAG1D,cAAI,QAAQ,cAAc;AACzB,gBAAI,GAAGE;AAEP,qBAAS,aAAa,SAAS,UAAU,QAAQ,cAAc,MAAM,QAAQ,WAAW,QAAQ,QAAQ,IAAI;AAE5G,gBAAI,QAAQ,gBAAgB,OAAO;AAClC,cAAAA,KAAI,QAAQ,WAAW,QAAQ;AAAA,YAChC,OAAO;AACN,cAAAA,KAAI,QAAQ,SAAS,QAAQ,aAAa,QAAQ;AAAA,YACnD;AAGA,gBAAI,QAAQ,aAAa,UAAU,SAAS,iBAAiB,GAAG;AAC/D,kBAAI;AACJ,uBAAS,aAAa,eAAe,OAAO;AAAA,YAC7C,WAAW,QAAQ,aAAa,SAAS;AACxC,kBAAI,SAAS,QAAQ;AACrB,uBAAS,aAAa,eAAe,KAAK;AAAA,YAC3C,OAEK;AACH,kBAAI,SAAS,QAAQ;AACrB,uBAAS,aAAa,eAAe,QAAQ;AAAA,YAC9C;AAED,qBAAS,aAAa,KAAK,CAAC;AAC5B,qBAAS,aAAa,KAAKA,EAAC;AAE5B,qBAAS,YAAY,KAAK,SAAS,eAAe,SAAS,IAAI,CAAC;AAEhE,mBAAO,YAAY,QAAQ;AAAA,UAC5B;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,iBAAiB,OAAO,QAAQ;AAC/C,cAAI,MAAM,KAAK;AACf,cAAI,aAAa,SAAS,QAAQ,IAAI;AACtC,cAAI,aAAa,UAAU,SAAS,IAAI;AACxC,cAAI,aAAa,KAAK,KAAK;AAC3B,cAAI,aAAa,KAAK,KAAK;AAC3B,cAAI,aAAa,WAAW,SAAS,QAAQ,MAAM,MAAM;AAEzD,cAAI,aAAa,SAAS,KAAK;AAC/B,cAAI,aAAa,WAAW,KAAK;AAEjC,cAAI,aAAa,SAAS,2BAA2B;AAAA,QACtD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY,GAAGA,IAAG,QAAQ;AACzC,cAAI,QAAQ,KAAK,SAAS,gBAAgB,OAAO,GAAG;AACpD,gBAAM,aAAa,aAAa,eAAe,IAAI,OAAOA,KAAI,GAAG;AAEjE,iBAAO,YAAY,KAAK;AAExB,iBAAO;AAAA,QACR;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB,OAAO,SAAS;AAC/C,gBAAM,aAAa,SAAS,UAAU,QAAQ,YAAY,GAAG;AAAA,QAC9D;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS,GAAGA,IAAG,OAAO,QAAQ,QAAQ;AACrD,cAAI,OAAO,KAAK,SAAS,gBAAgB,OAAO,MAAM;AAEtD,eAAK,aAAa,KAAK,CAAC;AACxB,eAAK,aAAa,KAAKA,EAAC;AACxB,eAAK,aAAa,SAAS,KAAK;AAChC,eAAK,aAAa,UAAU,MAAM;AAElC,iBAAO,YAAY,IAAI;AAEvB,iBAAO;AAAA,QACR;AAAA,MACD,CAAC,CAAC;AAEF,aAAOD;AAAA,IACR,EAAE;AAEF,YAAQ,UAAU;AAAA;AAAA;;;AC5LlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASE,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,QAAI,iBAAiB,WAAY;AAChC,eAASC,gBAAe,QAAQ,WAAW,SAAS;AACnD,wBAAgB,MAAMA,eAAc;AAEpC,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,UAAU;AAAA,MAChB;AAEA,mBAAaA,iBAAgB,CAAC;AAAA,QAC7B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,eAAK,OAAO,YAAY,KAAK;AAAA,QAC9B;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE;AAEF,YAAQ,UAAU;AAAA;AAAA;;;AC7BlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU,EAAE,gBAAgB,SAAS,SAAS,aAAa,MAAM,SAAS,gBAAgB,SAAS,QAAQ;AAAA;AAAA;;;ACpBnH;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,wBAAwB,SAAU,QAAQ;AAC7C,gBAAUC,wBAAuB,MAAM;AAEvC,eAASA,uBAAsB,WAAW,OAAO;AAChD,wBAAgB,MAAMA,sBAAqB;AAE3C,YAAI,QAAQ,2BAA2B,OAAOA,uBAAsB,aAAa,OAAO,eAAeA,sBAAqB,GAAG,KAAK,IAAI,CAAC;AAEzI,cAAM,OAAO;AAEb,cAAM,YAAY;AAClB,cAAM,QAAQ;AAEd,cAAM,UAAU,MAAM,MAAM,QAAQ,gCAAgC,MAAM;AAC1E,eAAO;AAAA,MACR;AAEA,aAAOA;AAAA,IACR,EAAE,KAAK;AAEP,QAAI,0BAA0B,SAAU,SAAS;AAChD,gBAAUC,0BAAyB,OAAO;AAE1C,eAASA,2BAA0B;AAClC,wBAAgB,MAAMA,wBAAuB;AAE7C,YAAI,SAAS,2BAA2B,OAAOA,yBAAwB,aAAa,OAAO,eAAeA,wBAAuB,GAAG,KAAK,IAAI,CAAC;AAE9I,eAAO,OAAO;AACd,eAAO,UAAU;AACjB,eAAO;AAAA,MACR;AAEA,aAAOA;AAAA,IACR,EAAE,KAAK;AAEP,QAAI,qBAAqB,SAAU,SAAS;AAC3C,gBAAUC,qBAAoB,OAAO;AAErC,eAASA,sBAAqB;AAC7B,wBAAgB,MAAMA,mBAAkB;AAExC,YAAI,SAAS,2BAA2B,OAAOA,oBAAmB,aAAa,OAAO,eAAeA,mBAAkB,GAAG,KAAK,IAAI,CAAC;AAEpI,eAAO,OAAO;AACd,eAAO,UAAU;AACjB,eAAO;AAAA,MACR;AAEA,aAAOA;AAAA,IACR,EAAE,KAAK;AAEP,YAAQ,wBAAwB;AAChC,YAAQ,0BAA0B;AAClC,YAAQ,qBAAqB;AAAA;AAAA;;;AClE7B;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAK,IAAI,SAAU,KAAK;AAAE,aAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,IAAK;AAI3Q,QAAI,yBAAyB;AAE7B,QAAI,0BAA0B,uBAAuB,sBAAsB;AAE3E,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,QAAI,cAAc;AAElB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAa9F,aAAS,oBAAoB,SAAS;AAErC,UAAI,OAAO,YAAY,UAAU;AAChC,eAAO,8BAA8B,OAAO;AAAA,MAC7C,WAES,MAAM,QAAQ,OAAO,GAAG;AAC/B,YAAI,cAAc,CAAC;AACnB,iBAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACxC,sBAAY,KAAK,oBAAoB,QAAQA,EAAC,CAAC,CAAC;AAAA,QACjD;AACA,eAAO;AAAA,MACR,WAES,OAAO,sBAAsB,eAAe,mBAAmB,kBAAkB;AACxF,eAAO,0BAA0B,OAAO;AAAA,MACzC,WAES,WAAW,QAAQ,YAAY,QAAQ,SAAS,YAAY,MAAM,SAAS,OAAO,eAAe,eAAe,mBAAmB,YAAY;AACtJ,eAAO;AAAA,UACN;AAAA,UACA,UAAU,GAAG,wBAAwB,SAAS,OAAO;AAAA,UACrD,UAAU,YAAY,QAAQ;AAAA,QAC/B;AAAA,MACD,WAES,OAAO,sBAAsB,eAAe,mBAAmB,mBAAmB;AACzF,eAAO;AAAA,UACN;AAAA,UACA,UAAU,GAAG,wBAAwB,SAAS,OAAO;AAAA,UACrD,UAAU,YAAY,QAAQ;AAAA,QAC/B;AAAA,MACD,WAES,WAAW,QAAQ,YAAY;AACtC,eAAO;AAAA,UACN;AAAA,UACA,UAAU,YAAY,QAAQ;AAAA,QAC/B;AAAA,MACD,WAAW,YAAY,OAAO,YAAY,cAAc,cAAc,QAAQ,OAAO,OAAO,YAAY,CAAC,QAAQ,UAAU;AAC1H,eAAO;AAAA,UACN;AAAA,UACA,UAAU,YAAY,QAAQ;AAAA,QAC/B;AAAA,MACD,OAAO;AACN,cAAM,IAAI,YAAY,wBAAwB;AAAA,MAC/C;AAAA,IACN;AAEA,aAAS,8BAA8B,QAAQ;AAC9C,UAAI,WAAW,SAAS,iBAAiB,MAAM;AAC/C,UAAI,SAAS,WAAW,GAAG;AAC1B,eAAO;AAAA,MACR,OAAO;AACN,YAAI,cAAc,CAAC;AACnB,iBAASA,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACzC,sBAAY,KAAK,oBAAoB,SAASA,EAAC,CAAC,CAAC;AAAA,QAClD;AACA,eAAO;AAAA,MACR;AAAA,IACD;AAEA,aAAS,0BAA0B,YAAY;AAC9C,UAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,aAAO;AAAA,QACN,SAAS;AAAA,QACT,UAAU,GAAG,wBAAwB,SAAS,UAAU;AAAA,QACxD,UAAU,YAAY,QAAQ;AAAA,QAC9B,aAAa,SAAS,cAAc;AACnC,qBAAW,aAAa,OAAO,OAAO,UAAU,CAAC;AAAA,QAClD;AAAA,MACD;AAAA,IACD;AAEA,YAAQ,UAAU;AAAA;AAAA;;;AC3GlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AAAE,cAAI,aAAa,MAAMA,EAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAIxJ,QAAI,eAAe,WAAY;AAC9B,eAASC,cAAa,KAAK;AAC1B,wBAAgB,MAAMA,aAAY;AAElC,aAAK,MAAM;AAAA,MACZ;AAEA,mBAAaA,eAAc,CAAC;AAAA,QAC3B,KAAK;AAAA,QACL,OAAO,SAAS,YAAY,GAAG;AAE9B,cAAI,EAAE,SAAS,yBAAyB;AACvC,gBAAI,KAAK,IAAI,SAAS,UAAU,KAAK,IAAI,UAAU,OAAO;AACzD,mBAAK,IAAI,SAAS,MAAM,KAAK;AAAA,YAC9B,OAAO;AACN,oBAAM,EAAE;AAAA,YACT;AAAA,UACD,OAAO;AACN,kBAAM;AAAA,UACP;AAEA,eAAK,IAAI,SAAS,WAAY;AAAA,UAAC;AAAA,QAChC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB,MAAM;AACrC,cAAI;AACH,gBAAI,SAAS,KAAK,MAAM,QAAW,SAAS;AAC5C,iBAAK,IAAI,SAAS,MAAM,IAAI;AAC5B,mBAAO;AAAA,UACR,SAAS,GAAG;AACX,iBAAK,YAAY,CAAC;AAElB,mBAAO,KAAK;AAAA,UACb;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE;AAEF,YAAQ,UAAU;AAAA;AAAA;;;ACrDlB;AAAA;AAAA;AAEA,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,sBAAsB;AAE1B,QAAI,uBAAuB,uBAAuB,mBAAmB;AAErE,QAAI,cAAc;AAElB,QAAI,eAAe,uBAAuB,WAAW;AAErD,QAAI,uBAAuB;AAE3B,QAAI,wBAAwB,uBAAuB,oBAAoB;AAEvE,QAAI,sBAAsB;AAE1B,QAAI,uBAAuB,uBAAuB,mBAAmB;AAErE,QAAI,gBAAgB;AAEpB,QAAI,iBAAiB,uBAAuB,aAAa;AAEzD,QAAI,cAAc;AAElB,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAM9F,QAAI,MAAM,SAASC,OAAM;AAAA,IAAC;AAY1B,QAAI,YAAY,SAASC,WAAU,SAAS,MAAM,SAAS;AAC1D,UAAI,MAAM,IAAI,IAAI;AAElB,UAAI,OAAO,YAAY,aAAa;AACnC,cAAM,MAAM,uCAAuC;AAAA,MACpD;AAGA,UAAI,qBAAqB,GAAG,sBAAsB,SAAS,OAAO;AAClE,UAAI,aAAa,CAAC;AAClB,UAAI,WAAW,WAAW;AAC1B,UAAI,gBAAgB,IAAI,eAAe,QAAQ,GAAG;AAGlD,UAAI,OAAO,SAAS,aAAa;AAChC,kBAAU,WAAW,CAAC;AAEtB,YAAI,CAAC,QAAQ,QAAQ;AACpB,kBAAQ,SAAS,kBAAkB;AAAA,QACpC;AAEA,YAAI,QAAQ,OAAO,EAAE,QAAQ,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO;AAAA,MAC5D;AAEA,aAAO;AAAA,IACR;AAGA,cAAU,YAAY,SAAUC,OAAM;AACrC,aAAO,WAAW,QAAQA,KAAI;AAAA,IAC/B;AAGA,SAAS,QAAQ,WAAW,SAAS;AACpC,UAAI,WAAW,QAAQ,eAAe,IAAI,GAAG;AAE5C,wBAAgB,WAAW,SAAS,IAAI;AAAA,MACzC;AAAA,IACD;AALS;AAMT,aAAS,gBAAgB,UAAUA,OAAM;AACxC,UAAI,UAAUA,KAAI,IAAI,IAAI,UAAUA,MAAK,YAAY,CAAC,IAAI,IAAI,UAAUA,MAAK,YAAY,CAAC,IAAI,SAAU,MAAM,SAAS;AACtH,YAAI,MAAM;AACV,eAAO,IAAI,cAAc,gBAAgB,WAAY;AAEpD,kBAAQ,OAAO,OAAO,QAAQ,SAAS,cAAc,SAAY,KAAK,QAAQ;AAE9E,cAAI,cAAc,GAAG,QAAQ,SAAS,IAAI,UAAU,OAAO;AAC3D,wBAAc,GAAG,qBAAqB,SAAS,UAAU;AACzD,cAAI,UAAU,SAASA,KAAI;AAC3B,cAAI,UAAU,OAAO,MAAM,SAAS,UAAU;AAC9C,cAAI,WAAW,KAAK,OAAO;AAE3B,iBAAO;AAAA,QACR,CAAC;AAAA,MACF;AAAA,IACD;AAGA,aAAS,OAAO,MAAM,SAAS,SAAS;AAEvC,aAAO,KAAK;AAEZ,UAAI,UAAU,IAAI,QAAQ,MAAM,OAAO;AAIvC,UAAI,CAAC,QAAQ,MAAM,GAAG;AACrB,cAAM,IAAI,YAAY,sBAAsB,QAAQ,YAAY,MAAM,IAAI;AAAA,MAC3E;AAGA,UAAI,UAAU,QAAQ,OAAO;AAI7B,iBAAW,GAAG,qBAAqB,SAAS,OAAO;AAGnD,eAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACxC,gBAAQA,EAAC,EAAE,WAAW,GAAG,QAAQ,SAAS,SAAS,QAAQA,EAAC,EAAE,OAAO;AAAA,MACtE;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,oBAAoB;AAE5B,UAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,eAAO;AAAA,MACR;AAGA,aAAO,OAAO,KAAK,WAAW,OAAO,EAAE,CAAC;AAAA,IACzC;AAIA,QAAI,UAAU,UAAU,SAAU,SAAS;AAC1C,WAAK,YAAY,GAAG,QAAQ,SAAS,KAAK,UAAU,OAAO;AAC3D,aAAO;AAAA,IACR;AAGA,QAAI,UAAU,QAAQ,SAAU,MAAM;AACrC,UAAI,SAAS,IAAI,MAAM,OAAO,CAAC,EAAE,KAAK,GAAG;AACzC,WAAK,WAAW,KAAK,EAAE,MAAM,OAAO,CAAC;AACrC,aAAO;AAAA,IACR;AAGA,QAAI,UAAU,OAAO,WAAY;AAEhC,UAAI,CAAC,KAAK,mBAAmB;AAC5B;AAAA,MACD;AAGA,UAAI,CAAC,MAAM,QAAQ,KAAK,iBAAiB,GAAG;AAC3C,aAAK,oBAAoB,CAAC,KAAK,iBAAiB;AAAA,MACjD;AAEA,UAAI;AACJ,eAASA,MAAK,KAAK,mBAAmB;AACrC,yBAAiB,KAAK,kBAAkBA,EAAC;AACzC,YAAI,WAAW,GAAG,QAAQ,SAAS,KAAK,UAAU,eAAe,OAAO;AAExE,YAAI,QAAQ,UAAU,QAAQ;AAC7B,kBAAQ,SAAS,kBAAkB;AAAA,QACpC;AAEA,aAAK,cAAc,gBAAgB,WAAY;AAC9C,cAAI,OAAO,QAAQ;AACnB,cAAI,UAAU,WAAW,QAAQ,QAAQ,OAAO,YAAY,CAAC;AAC7D,cAAI,UAAU,OAAO,MAAM,SAAS,OAAO;AAE3C,iBAAO,gBAAgB,SAAS,OAAO;AAAA,QACxC,CAAC;AAAA,MACF;AAAA,IACD;AAGA,QAAI,UAAU,SAAS,WAAY;AAClC,UAAI,CAAC,KAAK,mBAAmB;AAC5B,cAAM,IAAI,YAAY,mBAAmB;AAAA,MAC1C;AAEA,UAAI,MAAM,QAAQ,KAAK,iBAAiB,GAAG;AAC1C,iBAASA,KAAI,GAAGA,KAAI,KAAK,kBAAkB,QAAQA,MAAK;AACvD,iBAAO,KAAK,kBAAkBA,EAAC,GAAG,KAAK,YAAY,KAAK,QAAQ;AAAA,QACjE;AAAA,MACD,OAAO;AACN,eAAO,KAAK,mBAAmB,KAAK,YAAY,KAAK,QAAQ;AAAA,MAC9D;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,UAAU,YAAY,WAAW;AAGrC,aAAS,OAAO,kBAAkB,WAAW,SAAS;AACrD,mBAAa,GAAG,qBAAqB,SAAS,SAAS;AAEvD,eAASA,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AAC1C,kBAAUA,EAAC,EAAE,WAAW,GAAG,QAAQ,SAAS,SAAS,UAAUA,EAAC,EAAE,OAAO;AACzE,SAAC,GAAG,aAAa,SAAS,UAAUA,EAAC,EAAE,OAAO;AAAA,MAC/C;AAEA,OAAC,GAAG,aAAa,SAAS,OAAO;AAEjC,UAAI,WAAW,iBAAiB;AAChC,UAAI,WAAW,IAAI,SAAS,iBAAiB,SAAS,WAAW,OAAO;AACxE,eAAS,OAAO;AAEhB,UAAI,iBAAiB,aAAa;AACjC,yBAAiB,YAAY;AAAA,MAC9B;AAAA,IACD;AAGA,QAAI,OAAO,WAAW,aAAa;AAClC,aAAO,YAAY;AAAA,IACpB;AAIA,QAAI,OAAO,WAAW,aAAa;AAClC,aAAO,GAAG,YAAY,SAAU,SAAS,SAAS;AACjD,YAAI,eAAe,CAAC;AACpB,eAAO,IAAI,EAAE,KAAK,WAAY;AAC7B,uBAAa,KAAK,IAAI;AAAA,QACvB,CAAC;AACD,eAAO,UAAU,cAAc,SAAS,OAAO;AAAA,MAChD;AAAA,IACD;AAGA,WAAO,UAAU;AAAA;AAAA;;;AC3PjB;AAAA;AAIA,WAAO,UAAU,WAAY;AAC3B,aAAO,OAAO,YAAY,cAAc,QAAQ,aAAa,QAAQ,UAAU;AAAA,IACjF;AAAA;AAAA;;;ACNA;AAAA;AAAA,QAAI;AACJ,QAAM,kBAAkB;AAAA,MACtB;AAAA;AAAA,MACA;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC1C;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC7C;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MACtD;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,MAAM;AAAA,IACxD;AAQA,YAAQ,gBAAgB,SAAS,cAAe,SAAS;AACvD,UAAI,CAAC,QAAS,OAAM,IAAI,MAAM,uCAAuC;AACrE,UAAI,UAAU,KAAK,UAAU,GAAI,OAAM,IAAI,MAAM,2CAA2C;AAC5F,aAAO,UAAU,IAAI;AAAA,IACvB;AAQA,YAAQ,0BAA0B,SAAS,wBAAyB,SAAS;AAC3E,aAAO,gBAAgB,OAAO;AAAA,IAChC;AAQA,YAAQ,cAAc,SAAU,MAAM;AACpC,UAAI,QAAQ;AAEZ,aAAO,SAAS,GAAG;AACjB;AACA,kBAAU;AAAA,MACZ;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,oBAAoB,SAAS,kBAAmB,GAAG;AACzD,UAAI,OAAO,MAAM,YAAY;AAC3B,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,uBAAiB;AAAA,IACnB;AAEA,YAAQ,qBAAqB,WAAY;AACvC,aAAO,OAAO,mBAAmB;AAAA,IACnC;AAEA,YAAQ,SAAS,SAAS,OAAQ,OAAO;AACvC,aAAO,eAAe,KAAK;AAAA,IAC7B;AAAA;AAAA;;;AC9DA;AAAA;AAAA,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AACrB,YAAQ,IAAI,EAAE,KAAK,EAAE;AAErB,aAAS,WAAY,QAAQ;AAC3B,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AAEA,YAAM,QAAQ,OAAO,YAAY;AAEjC,cAAQ,OAAO;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,QAAQ;AAAA,QAEjB;AACE,gBAAM,IAAI,MAAM,uBAAuB,MAAM;AAAA,MACjD;AAAA,IACF;AAEA,YAAQ,UAAU,SAAS,QAAS,OAAO;AACzC,aAAO,SAAS,OAAO,MAAM,QAAQ,eACnC,MAAM,OAAO,KAAK,MAAM,MAAM;AAAA,IAClC;AAEA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI;AACF,eAAO,WAAW,KAAK;AAAA,MACzB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACjDA;AAAA;AAAA,aAAS,YAAa;AACpB,WAAK,SAAS,CAAC;AACf,WAAK,SAAS;AAAA,IAChB;AAEA,cAAU,YAAY;AAAA,MAEpB,KAAK,SAAU,OAAO;AACpB,cAAM,WAAW,KAAK,MAAM,QAAQ,CAAC;AACrC,gBAAS,KAAK,OAAO,QAAQ,MAAO,IAAI,QAAQ,IAAM,OAAO;AAAA,MAC/D;AAAA,MAEA,KAAK,SAAU,KAAK,QAAQ;AAC1B,iBAASC,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC/B,eAAK,QAAS,QAAS,SAASA,KAAI,IAAM,OAAO,CAAC;AAAA,QACpD;AAAA,MACF;AAAA,MAEA,iBAAiB,WAAY;AAC3B,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,QAAQ,SAAU,KAAK;AACrB,cAAM,WAAW,KAAK,MAAM,KAAK,SAAS,CAAC;AAC3C,YAAI,KAAK,OAAO,UAAU,UAAU;AAClC,eAAK,OAAO,KAAK,CAAC;AAAA,QACpB;AAEA,YAAI,KAAK;AACP,eAAK,OAAO,QAAQ,KAAM,QAAU,KAAK,SAAS;AAAA,QACpD;AAEA,aAAK;AAAA,MACP;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpCjB;AAAA;AAKA,aAAS,UAAW,MAAM;AACxB,UAAI,CAAC,QAAQ,OAAO,GAAG;AACrB,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AAEA,WAAK,OAAO;AACZ,WAAK,OAAO,IAAI,WAAW,OAAO,IAAI;AACtC,WAAK,cAAc,IAAI,WAAW,OAAO,IAAI;AAAA,IAC/C;AAWA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK,OAAO,UAAU;AAC7D,YAAM,QAAQ,MAAM,KAAK,OAAO;AAChC,WAAK,KAAK,KAAK,IAAI;AACnB,UAAI,SAAU,MAAK,YAAY,KAAK,IAAI;AAAA,IAC1C;AASA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK;AAC5C,aAAO,KAAK,KAAK,MAAM,KAAK,OAAO,GAAG;AAAA,IACxC;AAUA,cAAU,UAAU,MAAM,SAAU,KAAK,KAAK,OAAO;AACnD,WAAK,KAAK,MAAM,KAAK,OAAO,GAAG,KAAK;AAAA,IACtC;AASA,cAAU,UAAU,aAAa,SAAU,KAAK,KAAK;AACnD,aAAO,KAAK,YAAY,MAAM,KAAK,OAAO,GAAG;AAAA,IAC/C;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChEjB;AAAA;AAUA,QAAM,gBAAgB,gBAAmB;AAgBzC,YAAQ,kBAAkB,SAAS,gBAAiB,SAAS;AAC3D,UAAI,YAAY,EAAG,QAAO,CAAC;AAE3B,YAAM,WAAW,KAAK,MAAM,UAAU,CAAC,IAAI;AAC3C,YAAM,OAAO,cAAc,OAAO;AAClC,YAAM,YAAY,SAAS,MAAM,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,WAAW,EAAE,IAAI;AACpF,YAAM,YAAY,CAAC,OAAO,CAAC;AAE3B,eAASC,KAAI,GAAGA,KAAI,WAAW,GAAGA,MAAK;AACrC,kBAAUA,EAAC,IAAI,UAAUA,KAAI,CAAC,IAAI;AAAA,MACpC;AAEA,gBAAU,KAAK,CAAC;AAEhB,aAAO,UAAU,QAAQ;AAAA,IAC3B;AAsBA,YAAQ,eAAe,SAAS,aAAc,SAAS;AACrD,YAAM,SAAS,CAAC;AAChB,YAAM,MAAM,QAAQ,gBAAgB,OAAO;AAC3C,YAAM,YAAY,IAAI;AAEtB,eAASA,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAClC,iBAASC,KAAI,GAAGA,KAAI,WAAWA,MAAK;AAElC,cAAKD,OAAM,KAAKC,OAAM;AAAA,UACjBD,OAAM,KAAKC,OAAM,YAAY;AAAA,UAC7BD,OAAM,YAAY,KAAKC,OAAM,GAAI;AACpC;AAAA,UACF;AAEA,iBAAO,KAAK,CAAC,IAAID,EAAC,GAAG,IAAIC,EAAC,CAAC,CAAC;AAAA,QAC9B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AClFA;AAAA;AAAA,QAAM,gBAAgB,gBAAmB;AACzC,QAAM,sBAAsB;AAS5B,YAAQ,eAAe,SAAS,aAAc,SAAS;AACrD,YAAM,OAAO,cAAc,OAAO;AAElC,aAAO;AAAA;AAAA,QAEL,CAAC,GAAG,CAAC;AAAA;AAAA,QAEL,CAAC,OAAO,qBAAqB,CAAC;AAAA;AAAA,QAE9B,CAAC,GAAG,OAAO,mBAAmB;AAAA,MAChC;AAAA,IACF;AAAA;AAAA;;;ACrBA;AAAA;AAIA,YAAQ,WAAW;AAAA,MACjB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,IACd;AAMA,QAAM,gBAAgB;AAAA,MACpB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN;AAQA,YAAQ,UAAU,SAAS,QAAS,MAAM;AACxC,aAAO,QAAQ,QAAQ,SAAS,MAAM,CAAC,MAAM,IAAI,KAAK,QAAQ,KAAK,QAAQ;AAAA,IAC7E;AASA,YAAQ,OAAO,SAAS,KAAM,OAAO;AACnC,aAAO,QAAQ,QAAQ,KAAK,IAAI,SAAS,OAAO,EAAE,IAAI;AAAA,IACxD;AASA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,eAAe;AACnB,UAAI,eAAe;AACnB,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,uBAAe,eAAe;AAC9B,kBAAU,UAAU;AAEpB,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,cAAIC,UAAS,KAAK,IAAI,KAAK,GAAG;AAC9B,cAAIA,YAAW,SAAS;AACtB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,sBAAUA;AACV,2BAAe;AAAA,UACjB;AAEA,UAAAA,UAAS,KAAK,IAAI,KAAK,GAAG;AAC1B,cAAIA,YAAW,SAAS;AACtB;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,sBAAUA;AACV,2BAAe;AAAA,UACjB;AAAA,QACF;AAEA,YAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AACpE,YAAI,gBAAgB,EAAG,WAAU,cAAc,MAAM,eAAe;AAAA,MACtE;AAEA,aAAO;AAAA,IACT;AAOA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AAEb,eAAS,MAAM,GAAG,MAAM,OAAO,GAAG,OAAO;AACvC,iBAAS,MAAM,GAAG,MAAM,OAAO,GAAG,OAAO;AACvC,gBAAM,OAAO,KAAK,IAAI,KAAK,GAAG,IAC5B,KAAK,IAAI,KAAK,MAAM,CAAC,IACrB,KAAK,IAAI,MAAM,GAAG,GAAG,IACrB,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC;AAE3B,cAAI,SAAS,KAAK,SAAS,EAAG;AAAA,QAChC;AAAA,MACF;AAEA,aAAO,SAAS,cAAc;AAAA,IAChC;AAQA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS;AACb,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,kBAAU,UAAU;AACpB,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,oBAAY,WAAW,IAAK,OAAS,KAAK,IAAI,KAAK,GAAG;AACtD,cAAI,OAAO,OAAO,YAAY,QAAS,YAAY,IAAQ;AAE3D,oBAAY,WAAW,IAAK,OAAS,KAAK,IAAI,KAAK,GAAG;AACtD,cAAI,OAAO,OAAO,YAAY,QAAS,YAAY,IAAQ;AAAA,QAC7D;AAAA,MACF;AAEA,aAAO,SAAS,cAAc;AAAA,IAChC;AAUA,YAAQ,eAAe,SAAS,aAAc,MAAM;AAClD,UAAI,YAAY;AAChB,YAAM,eAAe,KAAK,KAAK;AAE/B,eAASC,KAAI,GAAGA,KAAI,cAAcA,KAAK,cAAa,KAAK,KAAKA,EAAC;AAE/D,YAAMC,KAAI,KAAK,IAAI,KAAK,KAAM,YAAY,MAAM,eAAgB,CAAC,IAAI,EAAE;AAEvE,aAAOA,KAAI,cAAc;AAAA,IAC3B;AAUA,aAAS,UAAW,aAAaD,IAAGE,IAAG;AACrC,cAAQ,aAAa;AAAA,QACnB,KAAK,QAAQ,SAAS;AAAY,kBAAQF,KAAIE,MAAK,MAAM;AAAA,QACzD,KAAK,QAAQ,SAAS;AAAY,iBAAOF,KAAI,MAAM;AAAA,QACnD,KAAK,QAAQ,SAAS;AAAY,iBAAOE,KAAI,MAAM;AAAA,QACnD,KAAK,QAAQ,SAAS;AAAY,kBAAQF,KAAIE,MAAK,MAAM;AAAA,QACzD,KAAK,QAAQ,SAAS;AAAY,kBAAQ,KAAK,MAAMF,KAAI,CAAC,IAAI,KAAK,MAAME,KAAI,CAAC,KAAK,MAAM;AAAA,QACzF,KAAK,QAAQ,SAAS;AAAY,iBAAQF,KAAIE,KAAK,IAAKF,KAAIE,KAAK,MAAM;AAAA,QACvE,KAAK,QAAQ,SAAS;AAAY,kBAASF,KAAIE,KAAK,IAAKF,KAAIE,KAAK,KAAK,MAAM;AAAA,QAC7E,KAAK,QAAQ,SAAS;AAAY,kBAASF,KAAIE,KAAK,KAAKF,KAAIE,MAAK,KAAK,MAAM;AAAA,QAE7E;AAAS,gBAAM,IAAI,MAAM,qBAAqB,WAAW;AAAA,MAC3D;AAAA,IACF;AAQA,YAAQ,YAAY,SAAS,UAAW,SAAS,MAAM;AACrD,YAAM,OAAO,KAAK;AAElB,eAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,iBAAS,MAAM,GAAG,MAAM,MAAM,OAAO;AACnC,cAAI,KAAK,WAAW,KAAK,GAAG,EAAG;AAC/B,eAAK,IAAI,KAAK,KAAK,UAAU,SAAS,KAAK,GAAG,CAAC;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAQA,YAAQ,cAAc,SAAS,YAAa,MAAM,iBAAiB;AACjE,YAAM,cAAc,OAAO,KAAK,QAAQ,QAAQ,EAAE;AAClD,UAAI,cAAc;AAClB,UAAI,eAAe;AAEnB,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,wBAAgB,CAAC;AACjB,gBAAQ,UAAU,GAAG,IAAI;AAGzB,cAAM,UACJ,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI,IACzB,QAAQ,aAAa,IAAI;AAG3B,gBAAQ,UAAU,GAAG,IAAI;AAEzB,YAAI,UAAU,cAAc;AAC1B,yBAAe;AACf,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACzOA;AAAA;AAAA,QAAM,UAAU;AAEhB,QAAM,kBAAkB;AAAA;AAAA,MAEtB;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MACT;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MACV;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,IACd;AAEA,QAAM,qBAAqB;AAAA;AAAA,MAEzB;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MACX;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MACZ;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MACb;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MACb;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MACd;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MACf;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,MAChB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,MACjB;AAAA,MAAK;AAAA,MAAM;AAAA,MAAM;AAAA,IACnB;AAUA,YAAQ,iBAAiB,SAAS,eAAgB,SAAS,sBAAsB;AAC/E,cAAQ,sBAAsB;AAAA,QAC5B,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C,KAAK,QAAQ;AACX,iBAAO,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,QAC9C;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,YAAQ,yBAAyB,SAAS,uBAAwB,SAAS,sBAAsB;AAC/F,cAAQ,sBAAsB;AAAA,QAC5B,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD,KAAK,QAAQ;AACX,iBAAO,oBAAoB,UAAU,KAAK,IAAI,CAAC;AAAA,QACjD;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAAA;AAAA;;;ACtIA;AAAA;AAAA,QAAM,YAAY,IAAI,WAAW,GAAG;AACpC,QAAM,YAAY,IAAI,WAAW,GAAG;AASnC,KAAC,SAAS,aAAc;AACvB,UAAI,IAAI;AACR,eAASC,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC5B,kBAAUA,EAAC,IAAI;AACf,kBAAU,CAAC,IAAIA;AAEf,cAAM;AAIN,YAAI,IAAI,KAAO;AACb,eAAK;AAAA,QACP;AAAA,MACF;AAMA,eAASA,KAAI,KAAKA,KAAI,KAAKA,MAAK;AAC9B,kBAAUA,EAAC,IAAI,UAAUA,KAAI,GAAG;AAAA,MAClC;AAAA,IACF,GAAE;AAQF,YAAQ,MAAM,SAAS,IAAK,GAAG;AAC7B,UAAI,IAAI,EAAG,OAAM,IAAI,MAAM,SAAS,IAAI,GAAG;AAC3C,aAAO,UAAU,CAAC;AAAA,IACpB;AAQA,YAAQ,MAAM,SAAS,IAAK,GAAG;AAC7B,aAAO,UAAU,CAAC;AAAA,IACpB;AASA,YAAQ,MAAM,SAAS,IAAK,GAAGC,IAAG;AAChC,UAAI,MAAM,KAAKA,OAAM,EAAG,QAAO;AAI/B,aAAO,UAAU,UAAU,CAAC,IAAI,UAAUA,EAAC,CAAC;AAAA,IAC9C;AAAA;AAAA;;;ACpEA;AAAA;AAAA,QAAM,KAAK;AASX,YAAQ,MAAM,SAAS,IAAK,IAAI,IAAI;AAClC,YAAM,QAAQ,IAAI,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC;AAEtD,eAASC,KAAI,GAAGA,KAAI,GAAG,QAAQA,MAAK;AAClC,iBAASC,KAAI,GAAGA,KAAI,GAAG,QAAQA,MAAK;AAClC,gBAAMD,KAAIC,EAAC,KAAK,GAAG,IAAI,GAAGD,EAAC,GAAG,GAAGC,EAAC,CAAC;AAAA,QACrC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,YAAQ,MAAM,SAAS,IAAK,UAAU,SAAS;AAC7C,UAAI,SAAS,IAAI,WAAW,QAAQ;AAEpC,aAAQ,OAAO,SAAS,QAAQ,UAAW,GAAG;AAC5C,cAAM,QAAQ,OAAO,CAAC;AAEtB,iBAASD,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACvC,iBAAOA,EAAC,KAAK,GAAG,IAAI,QAAQA,EAAC,GAAG,KAAK;AAAA,QACvC;AAGA,YAAI,SAAS;AACb,eAAO,SAAS,OAAO,UAAU,OAAO,MAAM,MAAM,EAAG;AACvD,iBAAS,OAAO,MAAM,MAAM;AAAA,MAC9B;AAEA,aAAO;AAAA,IACT;AASA,YAAQ,uBAAuB,SAAS,qBAAsB,QAAQ;AACpE,UAAI,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC;AAC7B,eAASA,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC/B,eAAO,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC,GAAG,GAAG,IAAIA,EAAC,CAAC,CAAC,CAAC;AAAA,MACzD;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC7DA;AAAA;AAAA,QAAM,aAAa;AAEnB,aAAS,mBAAoB,QAAQ;AACnC,WAAK,UAAU;AACf,WAAK,SAAS;AAEd,UAAI,KAAK,OAAQ,MAAK,WAAW,KAAK,MAAM;AAAA,IAC9C;AAQA,uBAAmB,UAAU,aAAa,SAAS,WAAY,QAAQ;AAErE,WAAK,SAAS;AACd,WAAK,UAAU,WAAW,qBAAqB,KAAK,MAAM;AAAA,IAC5D;AAQA,uBAAmB,UAAU,SAAS,SAAS,OAAQ,MAAM;AAC3D,UAAI,CAAC,KAAK,SAAS;AACjB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAIA,YAAM,aAAa,IAAI,WAAW,KAAK,SAAS,KAAK,MAAM;AAC3D,iBAAW,IAAI,IAAI;AAInB,YAAM,YAAY,WAAW,IAAI,YAAY,KAAK,OAAO;AAKzD,YAAM,QAAQ,KAAK,SAAS,UAAU;AACtC,UAAI,QAAQ,GAAG;AACb,cAAM,OAAO,IAAI,WAAW,KAAK,MAAM;AACvC,aAAK,IAAI,WAAW,KAAK;AAEzB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvDjB;AAAA;AAMA,YAAQ,UAAU,SAAS,QAAS,SAAS;AAC3C,aAAO,CAAC,MAAM,OAAO,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD;AAAA;AAAA;;;ACRA;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,eAAe;AACrB,QAAI,QAAQ;AAIZ,YAAQ,MAAM,QAAQ,MAAM,KAAK;AAEjC,QAAM,OAAO,+BAA+B,QAAQ;AAEpD,YAAQ,QAAQ,IAAI,OAAO,OAAO,GAAG;AACrC,YAAQ,aAAa,IAAI,OAAO,yBAAyB,GAAG;AAC5D,YAAQ,OAAO,IAAI,OAAO,MAAM,GAAG;AACnC,YAAQ,UAAU,IAAI,OAAO,SAAS,GAAG;AACzC,YAAQ,eAAe,IAAI,OAAO,cAAc,GAAG;AAEnD,QAAM,aAAa,IAAI,OAAO,MAAM,QAAQ,GAAG;AAC/C,QAAM,eAAe,IAAI,OAAO,MAAM,UAAU,GAAG;AACnD,QAAM,oBAAoB,IAAI,OAAO,wBAAwB;AAE7D,YAAQ,YAAY,SAAS,UAAW,KAAK;AAC3C,aAAO,WAAW,KAAK,GAAG;AAAA,IAC5B;AAEA,YAAQ,cAAc,SAAS,YAAa,KAAK;AAC/C,aAAO,aAAa,KAAK,GAAG;AAAA,IAC9B;AAEA,YAAQ,mBAAmB,SAAS,iBAAkB,KAAK;AACzD,aAAO,kBAAkB,KAAK,GAAG;AAAA,IACnC;AAAA;AAAA;;;AC9BA;AAAA;AAAA,QAAM,eAAe;AACrB,QAAM,QAAQ;AASd,YAAQ,UAAU;AAAA,MAChB,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,IAAI,IAAI,EAAE;AAAA,IACrB;AAWA,YAAQ,eAAe;AAAA,MACrB,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAOA,YAAQ,OAAO;AAAA,MACb,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAWA,YAAQ,QAAQ;AAAA,MACd,IAAI;AAAA,MACJ,KAAK,KAAK;AAAA,MACV,QAAQ,CAAC,GAAG,IAAI,EAAE;AAAA,IACpB;AAQA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,IACP;AAUA,YAAQ,wBAAwB,SAAS,sBAAuB,MAAM,SAAS;AAC7E,UAAI,CAAC,KAAK,OAAQ,OAAM,IAAI,MAAM,mBAAmB,IAAI;AAEzD,UAAI,CAAC,aAAa,QAAQ,OAAO,GAAG;AAClC,cAAM,IAAI,MAAM,sBAAsB,OAAO;AAAA,MAC/C;AAEA,UAAI,WAAW,KAAK,UAAU,GAAI,QAAO,KAAK,OAAO,CAAC;AAAA,eAC7C,UAAU,GAAI,QAAO,KAAK,OAAO,CAAC;AAC3C,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAQA,YAAQ,qBAAqB,SAAS,mBAAoB,SAAS;AACjE,UAAI,MAAM,YAAY,OAAO,EAAG,QAAO,QAAQ;AAAA,eACtC,MAAM,iBAAiB,OAAO,EAAG,QAAO,QAAQ;AAAA,eAChD,MAAM,UAAU,OAAO,EAAG,QAAO,QAAQ;AAAA,UAC7C,QAAO,QAAQ;AAAA,IACtB;AAQA,YAAQ,WAAW,SAAS,SAAU,MAAM;AAC1C,UAAI,QAAQ,KAAK,GAAI,QAAO,KAAK;AACjC,YAAM,IAAI,MAAM,cAAc;AAAA,IAChC;AAQA,YAAQ,UAAU,SAAS,QAAS,MAAM;AACxC,aAAO,QAAQ,KAAK,OAAO,KAAK;AAAA,IAClC;AAQA,aAAS,WAAY,QAAQ;AAC3B,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AAEA,YAAM,QAAQ,OAAO,YAAY;AAEjC,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB,KAAK;AACH,iBAAO,QAAQ;AAAA,QACjB;AACE,gBAAM,IAAI,MAAM,mBAAmB,MAAM;AAAA,MAC7C;AAAA,IACF;AAUA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI;AACF,eAAO,WAAW,KAAK;AAAA,MACzB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACtKA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,QAAM,OAAO;AACb,QAAM,eAAe;AAGrB,QAAM,MAAO,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AAClG,QAAM,UAAU,MAAM,YAAY,GAAG;AAErC,aAAS,4BAA6B,MAAM,QAAQ,sBAAsB;AACxE,eAAS,iBAAiB,GAAG,kBAAkB,IAAI,kBAAkB;AACnE,YAAI,UAAU,QAAQ,YAAY,gBAAgB,sBAAsB,IAAI,GAAG;AAC7E,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,qBAAsB,MAAM,SAAS;AAE5C,aAAO,KAAK,sBAAsB,MAAM,OAAO,IAAI;AAAA,IACrD;AAEA,aAAS,0BAA2B,UAAU,SAAS;AACrD,UAAI,YAAY;AAEhB,eAAS,QAAQ,SAAU,MAAM;AAC/B,cAAM,eAAe,qBAAqB,KAAK,MAAM,OAAO;AAC5D,qBAAa,eAAe,KAAK,cAAc;AAAA,MACjD,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,2BAA4B,UAAU,sBAAsB;AACnE,eAAS,iBAAiB,GAAG,kBAAkB,IAAI,kBAAkB;AACnE,cAAM,SAAS,0BAA0B,UAAU,cAAc;AACjE,YAAI,UAAU,QAAQ,YAAY,gBAAgB,sBAAsB,KAAK,KAAK,GAAG;AACnF,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAUA,YAAQ,OAAO,SAAS,KAAM,OAAO,cAAc;AACjD,UAAI,aAAa,QAAQ,KAAK,GAAG;AAC/B,eAAO,SAAS,OAAO,EAAE;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAWA,YAAQ,cAAc,SAAS,YAAa,SAAS,sBAAsB,MAAM;AAC/E,UAAI,CAAC,aAAa,QAAQ,OAAO,GAAG;AAClC,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAGA,UAAI,OAAO,SAAS,YAAa,QAAO,KAAK;AAG7C,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAG5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AAGpF,YAAM,0BAA0B,iBAAiB,oBAAoB;AAErE,UAAI,SAAS,KAAK,MAAO,QAAO;AAEhC,YAAM,aAAa,yBAAyB,qBAAqB,MAAM,OAAO;AAG9E,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,KAAK,MAAO,aAAa,KAAM,CAAC;AAAA,QAEzC,KAAK,KAAK;AACR,iBAAO,KAAK,MAAO,aAAa,KAAM,CAAC;AAAA,QAEzC,KAAK,KAAK;AACR,iBAAO,KAAK,MAAM,aAAa,EAAE;AAAA,QAEnC,KAAK,KAAK;AAAA,QACV;AACE,iBAAO,KAAK,MAAM,aAAa,CAAC;AAAA,MACpC;AAAA,IACF;AAUA,YAAQ,wBAAwB,SAAS,sBAAuB,MAAM,sBAAsB;AAC1F,UAAI;AAEJ,YAAM,MAAM,QAAQ,KAAK,sBAAsB,QAAQ,CAAC;AAExD,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAI,KAAK,SAAS,GAAG;AACnB,iBAAO,2BAA2B,MAAM,GAAG;AAAA,QAC7C;AAEA,YAAI,KAAK,WAAW,GAAG;AACrB,iBAAO;AAAA,QACT;AAEA,cAAM,KAAK,CAAC;AAAA,MACd,OAAO;AACL,cAAM;AAAA,MACR;AAEA,aAAO,4BAA4B,IAAI,MAAM,IAAI,UAAU,GAAG,GAAG;AAAA,IACnE;AAYA,YAAQ,iBAAiB,SAAS,eAAgB,SAAS;AACzD,UAAI,CAAC,aAAa,QAAQ,OAAO,KAAK,UAAU,GAAG;AACjD,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAEA,UAAI,IAAI,WAAW;AAEnB,aAAO,MAAM,YAAY,CAAC,IAAI,WAAW,GAAG;AAC1C,aAAM,OAAQ,MAAM,YAAY,CAAC,IAAI;AAAA,MACvC;AAEA,aAAQ,WAAW,KAAM;AAAA,IAC3B;AAAA;AAAA;;;AClKA;AAAA;AAAA,QAAM,QAAQ;AAEd,QAAM,MAAO,KAAK,KAAO,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK;AACrF,QAAM,WAAY,KAAK,KAAO,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;AACtE,QAAM,UAAU,MAAM,YAAY,GAAG;AAYrC,YAAQ,iBAAiB,SAAS,eAAgB,sBAAsB,MAAM;AAC5E,YAAM,OAAS,qBAAqB,OAAO,IAAK;AAChD,UAAI,IAAI,QAAQ;AAEhB,aAAO,MAAM,YAAY,CAAC,IAAI,WAAW,GAAG;AAC1C,aAAM,OAAQ,MAAM,YAAY,CAAC,IAAI;AAAA,MACvC;AAKA,cAAS,QAAQ,KAAM,KAAK;AAAA,IAC9B;AAAA;AAAA;;;AC5BA;AAAA;AAAA,QAAM,OAAO;AAEb,aAAS,YAAa,MAAM;AAC1B,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO,KAAK,SAAS;AAAA,IAC5B;AAEA,gBAAY,gBAAgB,SAAS,cAAe,QAAQ;AAC1D,aAAO,KAAK,KAAK,MAAM,SAAS,CAAC,KAAM,SAAS,IAAO,SAAS,IAAK,IAAI,IAAK;AAAA,IAChF;AAEA,gBAAY,UAAU,YAAY,SAAS,YAAa;AACtD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,gBAAY,UAAU,gBAAgB,SAAS,gBAAiB;AAC9D,aAAO,YAAY,cAAc,KAAK,KAAK,MAAM;AAAA,IACnD;AAEA,gBAAY,UAAU,QAAQ,SAAS,MAAO,WAAW;AACvD,UAAIE,IAAG,OAAO;AAId,WAAKA,KAAI,GAAGA,KAAI,KAAK,KAAK,KAAK,QAAQA,MAAK,GAAG;AAC7C,gBAAQ,KAAK,KAAK,OAAOA,IAAG,CAAC;AAC7B,gBAAQ,SAAS,OAAO,EAAE;AAE1B,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAIA,YAAM,eAAe,KAAK,KAAK,SAASA;AACxC,UAAI,eAAe,GAAG;AACpB,gBAAQ,KAAK,KAAK,OAAOA,EAAC;AAC1B,gBAAQ,SAAS,OAAO,EAAE;AAE1B,kBAAU,IAAI,OAAO,eAAe,IAAI,CAAC;AAAA,MAC3C;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1CjB;AAAA;AAAA,QAAM,OAAO;AAWb,QAAM,kBAAkB;AAAA,MACtB;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC7C;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC5D;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC5D;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,IAC1C;AAEA,aAAS,iBAAkB,MAAM;AAC/B,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACd;AAEA,qBAAiB,gBAAgB,SAAS,cAAe,QAAQ;AAC/D,aAAO,KAAK,KAAK,MAAM,SAAS,CAAC,IAAI,KAAK,SAAS;AAAA,IACrD;AAEA,qBAAiB,UAAU,YAAY,SAAS,YAAa;AAC3D,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,qBAAiB,UAAU,gBAAgB,SAAS,gBAAiB;AACnE,aAAO,iBAAiB,cAAc,KAAK,KAAK,MAAM;AAAA,IACxD;AAEA,qBAAiB,UAAU,QAAQ,SAAS,MAAO,WAAW;AAC5D,UAAIC;AAIJ,WAAKA,KAAI,GAAGA,KAAI,KAAK,KAAK,KAAK,QAAQA,MAAK,GAAG;AAE7C,YAAI,QAAQ,gBAAgB,QAAQ,KAAK,KAAKA,EAAC,CAAC,IAAI;AAGpD,iBAAS,gBAAgB,QAAQ,KAAK,KAAKA,KAAI,CAAC,CAAC;AAGjD,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAIA,UAAI,KAAK,KAAK,SAAS,GAAG;AACxB,kBAAU,IAAI,gBAAgB,QAAQ,KAAK,KAAKA,EAAC,CAAC,GAAG,CAAC;AAAA,MACxD;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1DjB;AAAA;AAAA,QAAM,OAAO;AAEb,aAAS,SAAU,MAAM;AACvB,WAAK,OAAO,KAAK;AACjB,UAAI,OAAQ,SAAU,UAAU;AAC9B,aAAK,OAAO,IAAI,YAAY,EAAE,OAAO,IAAI;AAAA,MAC3C,OAAO;AACL,aAAK,OAAO,IAAI,WAAW,IAAI;AAAA,MACjC;AAAA,IACF;AAEA,aAAS,gBAAgB,SAAS,cAAe,QAAQ;AACvD,aAAO,SAAS;AAAA,IAClB;AAEA,aAAS,UAAU,YAAY,SAAS,YAAa;AACnD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,aAAS,UAAU,gBAAgB,SAAS,gBAAiB;AAC3D,aAAO,SAAS,cAAc,KAAK,KAAK,MAAM;AAAA,IAChD;AAEA,aAAS,UAAU,QAAQ,SAAU,WAAW;AAC9C,eAASC,KAAI,GAAG,IAAI,KAAK,KAAK,QAAQA,KAAI,GAAGA,MAAK;AAChD,kBAAU,IAAI,KAAK,KAAKA,EAAC,GAAG,CAAC;AAAA,MAC/B;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,QAAQ;AAEd,aAAS,UAAW,MAAM;AACxB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO;AAAA,IACd;AAEA,cAAU,gBAAgB,SAAS,cAAe,QAAQ;AACxD,aAAO,SAAS;AAAA,IAClB;AAEA,cAAU,UAAU,YAAY,SAAS,YAAa;AACpD,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,cAAU,UAAU,gBAAgB,SAAS,gBAAiB;AAC5D,aAAO,UAAU,cAAc,KAAK,KAAK,MAAM;AAAA,IACjD;AAEA,cAAU,UAAU,QAAQ,SAAU,WAAW;AAC/C,UAAIC;AAKJ,WAAKA,KAAI,GAAGA,KAAI,KAAK,KAAK,QAAQA,MAAK;AACrC,YAAI,QAAQ,MAAM,OAAO,KAAK,KAAKA,EAAC,CAAC;AAGrC,YAAI,SAAS,SAAU,SAAS,OAAQ;AAEtC,mBAAS;AAAA,QAGX,WAAW,SAAS,SAAU,SAAS,OAAQ;AAE7C,mBAAS;AAAA,QACX,OAAO;AACL,gBAAM,IAAI;AAAA,YACR,6BAA6B,KAAK,KAAKA,EAAC,IAAI;AAAA,UACX;AAAA,QACrC;AAIA,iBAAW,UAAU,IAAK,OAAQ,OAAS,QAAQ;AAGnD,kBAAU,IAAI,OAAO,EAAE;AAAA,MACzB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrDjB;AAAA;AAAA;AAuBA,QAAI,WAAW;AAAA,MACb,8BAA8B,SAAS,OAAO,GAAG,GAAG;AAGlD,YAAI,eAAe,CAAC;AAIpB,YAAI,QAAQ,CAAC;AACb,cAAM,CAAC,IAAI;AAMX,YAAI,OAAO,SAAS,cAAc,KAAK;AACvC,aAAK,KAAK,GAAG,CAAC;AAEd,YAAI,SACA,GAAGC,IACH,gBACA,gBACA,WACA,+BACA,gBACA;AACJ,eAAO,CAAC,KAAK,MAAM,GAAG;AAGpB,oBAAU,KAAK,IAAI;AACnB,cAAI,QAAQ;AACZ,2BAAiB,QAAQ;AAGzB,2BAAiB,MAAM,CAAC,KAAK,CAAC;AAK9B,eAAKA,MAAK,gBAAgB;AACxB,gBAAI,eAAe,eAAeA,EAAC,GAAG;AAEpC,0BAAY,eAAeA,EAAC;AAK5B,8CAAgC,iBAAiB;AAMjD,+BAAiB,MAAMA,EAAC;AACxB,4BAAe,OAAO,MAAMA,EAAC,MAAM;AACnC,kBAAI,eAAe,iBAAiB,+BAA+B;AACjE,sBAAMA,EAAC,IAAI;AACX,qBAAK,KAAKA,IAAG,6BAA6B;AAC1C,6BAAaA,EAAC,IAAI;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,OAAO,MAAM,eAAe,OAAO,MAAM,CAAC,MAAM,aAAa;AAC/D,cAAI,MAAM,CAAC,+BAA+B,GAAG,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE;AACpE,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,6CAA6C,SAAS,cAAc,GAAG;AACrE,YAAI,QAAQ,CAAC;AACb,YAAI,IAAI;AACR,YAAI;AACJ,eAAO,GAAG;AACR,gBAAM,KAAK,CAAC;AACZ,wBAAc,aAAa,CAAC;AAC5B,cAAI,aAAa,CAAC;AAAA,QACpB;AACA,cAAM,QAAQ;AACd,eAAO;AAAA,MACT;AAAA,MAEA,WAAW,SAAS,OAAO,GAAG,GAAG;AAC/B,YAAI,eAAe,SAAS,6BAA6B,OAAO,GAAG,CAAC;AACpE,eAAO,SAAS;AAAA,UACd;AAAA,UAAc;AAAA,QAAC;AAAA,MACnB;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe;AAAA,QACb,MAAM,SAAU,MAAM;AACpB,cAAI,IAAI,SAAS,eACbC,KAAI,CAAC,GACL;AACJ,iBAAO,QAAQ,CAAC;AAChB,eAAK,OAAO,GAAG;AACb,gBAAI,EAAE,eAAe,GAAG,GAAG;AACzB,cAAAA,GAAE,GAAG,IAAI,EAAE,GAAG;AAAA,YAChB;AAAA,UACF;AACA,UAAAA,GAAE,QAAQ,CAAC;AACX,UAAAA,GAAE,SAAS,KAAK,UAAU,EAAE;AAC5B,iBAAOA;AAAA,QACT;AAAA,QAEA,gBAAgB,SAAUC,IAAG,GAAG;AAC9B,iBAAOA,GAAE,OAAO,EAAE;AAAA,QACpB;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,MAAM,SAAU,OAAO,MAAM;AAC3B,cAAI,OAAO,EAAC,OAAc,KAAU;AACpC,eAAK,MAAM,KAAK,IAAI;AACpB,eAAK,MAAM,KAAK,KAAK,MAAM;AAAA,QAC7B;AAAA;AAAA;AAAA;AAAA,QAKA,KAAK,WAAY;AACf,iBAAO,KAAK,MAAM,MAAM;AAAA,QAC1B;AAAA,QAEA,OAAO,WAAY;AACjB,iBAAO,KAAK,MAAM,WAAW;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAIA,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACpKA;AAAA;AAAA,QAAM,OAAO;AACb,QAAM,cAAc;AACpB,QAAM,mBAAmB;AACzB,QAAM,WAAW;AACjB,QAAM,YAAY;AAClB,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,WAAW;AAQjB,aAAS,oBAAqB,KAAK;AACjC,aAAO,SAAS,mBAAmB,GAAG,CAAC,EAAE;AAAA,IAC3C;AAUA,aAAS,YAAa,OAAO,MAAM,KAAK;AACtC,YAAM,WAAW,CAAC;AAClB,UAAI;AAEJ,cAAQ,SAAS,MAAM,KAAK,GAAG,OAAO,MAAM;AAC1C,iBAAS,KAAK;AAAA,UACZ,MAAM,OAAO,CAAC;AAAA,UACd,OAAO,OAAO;AAAA,UACd;AAAA,UACA,QAAQ,OAAO,CAAC,EAAE;AAAA,QACpB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AASA,aAAS,sBAAuB,SAAS;AACvC,YAAM,UAAU,YAAY,MAAM,SAAS,KAAK,SAAS,OAAO;AAChE,YAAM,eAAe,YAAY,MAAM,cAAc,KAAK,cAAc,OAAO;AAC/E,UAAI;AACJ,UAAI;AAEJ,UAAI,MAAM,mBAAmB,GAAG;AAC9B,mBAAW,YAAY,MAAM,MAAM,KAAK,MAAM,OAAO;AACrD,oBAAY,YAAY,MAAM,OAAO,KAAK,OAAO,OAAO;AAAA,MAC1D,OAAO;AACL,mBAAW,YAAY,MAAM,YAAY,KAAK,MAAM,OAAO;AAC3D,oBAAY,CAAC;AAAA,MACf;AAEA,YAAM,OAAO,QAAQ,OAAO,cAAc,UAAU,SAAS;AAE7D,aAAO,KACJ,KAAK,SAAU,IAAI,IAAI;AACtB,eAAO,GAAG,QAAQ,GAAG;AAAA,MACvB,CAAC,EACA,IAAI,SAAU,KAAK;AAClB,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,QAAQ,IAAI;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACL;AAUA,aAAS,qBAAsB,QAAQ,MAAM;AAC3C,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,YAAY,cAAc,MAAM;AAAA,QACzC,KAAK,KAAK;AACR,iBAAO,iBAAiB,cAAc,MAAM;AAAA,QAC9C,KAAK,KAAK;AACR,iBAAO,UAAU,cAAc,MAAM;AAAA,QACvC,KAAK,KAAK;AACR,iBAAO,SAAS,cAAc,MAAM;AAAA,MACxC;AAAA,IACF;AAQA,aAAS,cAAe,MAAM;AAC5B,aAAO,KAAK,OAAO,SAAU,KAAK,MAAM;AACtC,cAAM,UAAU,IAAI,SAAS,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI;AAC5D,YAAI,WAAW,QAAQ,SAAS,KAAK,MAAM;AACzC,cAAI,IAAI,SAAS,CAAC,EAAE,QAAQ,KAAK;AACjC,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,IAAI;AACb,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAkBA,aAAS,WAAY,MAAM;AACzB,YAAM,QAAQ,CAAC;AACf,eAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,cAAM,MAAM,KAAKA,EAAC;AAElB,gBAAQ,IAAI,MAAM;AAAA,UAChB,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,cAAc,QAAQ,IAAI,OAAO;AAAA,cAC9D,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,IAAI,OAAO;AAAA,YACxD,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,IAAI,OAAO;AAAA,YACxD,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cAAC;AAAA,cACV,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,oBAAoB,IAAI,IAAI,EAAE;AAAA,YAC3E,CAAC;AACD;AAAA,UACF,KAAK,KAAK;AACR,kBAAM,KAAK;AAAA,cACT,EAAE,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,QAAQ,oBAAoB,IAAI,IAAI,EAAE;AAAA,YAC3E,CAAC;AAAA,QACL;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAcA,aAAS,WAAY,OAAO,SAAS;AACnC,YAAM,QAAQ,CAAC;AACf,YAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;AAC1B,UAAI,cAAc,CAAC,OAAO;AAE1B,eAASA,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,cAAM,YAAY,MAAMA,EAAC;AACzB,cAAM,iBAAiB,CAAC;AAExB,iBAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,gBAAM,OAAO,UAAUA,EAAC;AACxB,gBAAM,MAAM,KAAKD,KAAIC;AAErB,yBAAe,KAAK,GAAG;AACvB,gBAAM,GAAG,IAAI,EAAE,MAAY,WAAW,EAAE;AACxC,gBAAM,GAAG,IAAI,CAAC;AAEd,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,kBAAM,aAAa,YAAY,CAAC;AAEhC,gBAAI,MAAM,UAAU,KAAK,MAAM,UAAU,EAAE,KAAK,SAAS,KAAK,MAAM;AAClE,oBAAM,UAAU,EAAE,GAAG,IACnB,qBAAqB,MAAM,UAAU,EAAE,YAAY,KAAK,QAAQ,KAAK,IAAI,IACzE,qBAAqB,MAAM,UAAU,EAAE,WAAW,KAAK,IAAI;AAE7D,oBAAM,UAAU,EAAE,aAAa,KAAK;AAAA,YACtC,OAAO;AACL,kBAAI,MAAM,UAAU,EAAG,OAAM,UAAU,EAAE,YAAY,KAAK;AAE1D,oBAAM,UAAU,EAAE,GAAG,IAAI,qBAAqB,KAAK,QAAQ,KAAK,IAAI,IAClE,IAAI,KAAK,sBAAsB,KAAK,MAAM,OAAO;AAAA,YACrD;AAAA,UACF;AAAA,QACF;AAEA,sBAAc;AAAA,MAChB;AAEA,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,cAAM,YAAY,CAAC,CAAC,EAAE,MAAM;AAAA,MAC9B;AAEA,aAAO,EAAE,KAAK,OAAO,MAAa;AAAA,IACpC;AAUA,aAAS,mBAAoB,MAAM,WAAW;AAC5C,UAAI;AACJ,YAAM,WAAW,KAAK,mBAAmB,IAAI;AAE7C,aAAO,KAAK,KAAK,WAAW,QAAQ;AAGpC,UAAI,SAAS,KAAK,QAAQ,KAAK,MAAM,SAAS,KAAK;AACjD,cAAM,IAAI,MAAM,MAAM,OAAO,mCACO,KAAK,SAAS,IAAI,IACpD,4BAA4B,KAAK,SAAS,QAAQ,CAAC;AAAA,MACvD;AAGA,UAAI,SAAS,KAAK,SAAS,CAAC,MAAM,mBAAmB,GAAG;AACtD,eAAO,KAAK;AAAA,MACd;AAEA,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,iBAAO,IAAI,YAAY,IAAI;AAAA,QAE7B,KAAK,KAAK;AACR,iBAAO,IAAI,iBAAiB,IAAI;AAAA,QAElC,KAAK,KAAK;AACR,iBAAO,IAAI,UAAU,IAAI;AAAA,QAE3B,KAAK,KAAK;AACR,iBAAO,IAAI,SAAS,IAAI;AAAA,MAC5B;AAAA,IACF;AAiBA,YAAQ,YAAY,SAAS,UAAW,OAAO;AAC7C,aAAO,MAAM,OAAO,SAAU,KAAK,KAAK;AACtC,YAAI,OAAO,QAAQ,UAAU;AAC3B,cAAI,KAAK,mBAAmB,KAAK,IAAI,CAAC;AAAA,QACxC,WAAW,IAAI,MAAM;AACnB,cAAI,KAAK,mBAAmB,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,QACjD;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAUA,YAAQ,aAAa,SAAS,WAAY,MAAM,SAAS;AACvD,YAAM,OAAO,sBAAsB,MAAM,MAAM,mBAAmB,CAAC;AAEnE,YAAM,QAAQ,WAAW,IAAI;AAC7B,YAAM,QAAQ,WAAW,OAAO,OAAO;AACvC,YAAM,OAAO,SAAS,UAAU,MAAM,KAAK,SAAS,KAAK;AAEzD,YAAM,gBAAgB,CAAC;AACvB,eAASD,KAAI,GAAGA,KAAI,KAAK,SAAS,GAAGA,MAAK;AACxC,sBAAc,KAAK,MAAM,MAAM,KAAKA,EAAC,CAAC,EAAE,IAAI;AAAA,MAC9C;AAEA,aAAO,QAAQ,UAAU,cAAc,aAAa,CAAC;AAAA,IACvD;AAYA,YAAQ,WAAW,SAAS,SAAU,MAAM;AAC1C,aAAO,QAAQ;AAAA,QACb,sBAAsB,MAAM,MAAM,mBAAmB,CAAC;AAAA,MACxD;AAAA,IACF;AAAA;AAAA;;;ACzUA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,UAAU;AAChB,QAAM,YAAY;AAClB,QAAM,YAAY;AAClB,QAAM,mBAAmB;AACzB,QAAM,gBAAgB;AACtB,QAAM,cAAc;AACpB,QAAM,SAAS;AACf,QAAM,qBAAqB;AAC3B,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,OAAO;AACb,QAAM,WAAW;AAkCjB,aAAS,mBAAoB,QAAQ,SAAS;AAC5C,YAAM,OAAO,OAAO;AACpB,YAAM,MAAM,cAAc,aAAa,OAAO;AAE9C,eAASE,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,cAAM,MAAM,IAAIA,EAAC,EAAE,CAAC;AACpB,cAAM,MAAM,IAAIA,EAAC,EAAE,CAAC;AAEpB,iBAASC,KAAI,IAAIA,MAAK,GAAGA,MAAK;AAC5B,cAAI,MAAMA,MAAK,MAAM,QAAQ,MAAMA,GAAG;AAEtC,mBAASC,KAAI,IAAIA,MAAK,GAAGA,MAAK;AAC5B,gBAAI,MAAMA,MAAK,MAAM,QAAQ,MAAMA,GAAG;AAEtC,gBAAKD,MAAK,KAAKA,MAAK,MAAMC,OAAM,KAAKA,OAAM,MACxCA,MAAK,KAAKA,MAAK,MAAMD,OAAM,KAAKA,OAAM,MACtCA,MAAK,KAAKA,MAAK,KAAKC,MAAK,KAAKA,MAAK,GAAI;AACxC,qBAAO,IAAI,MAAMD,IAAG,MAAMC,IAAG,MAAM,IAAI;AAAA,YACzC,OAAO;AACL,qBAAO,IAAI,MAAMD,IAAG,MAAMC,IAAG,OAAO,IAAI;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AASA,aAAS,mBAAoB,QAAQ;AACnC,YAAM,OAAO,OAAO;AAEpB,eAASD,KAAI,GAAGA,KAAI,OAAO,GAAGA,MAAK;AACjC,cAAM,QAAQA,KAAI,MAAM;AACxB,eAAO,IAAIA,IAAG,GAAG,OAAO,IAAI;AAC5B,eAAO,IAAI,GAAGA,IAAG,OAAO,IAAI;AAAA,MAC9B;AAAA,IACF;AAUA,aAAS,sBAAuB,QAAQ,SAAS;AAC/C,YAAM,MAAM,iBAAiB,aAAa,OAAO;AAEjD,eAASD,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,cAAM,MAAM,IAAIA,EAAC,EAAE,CAAC;AACpB,cAAM,MAAM,IAAIA,EAAC,EAAE,CAAC;AAEpB,iBAASC,KAAI,IAAIA,MAAK,GAAGA,MAAK;AAC5B,mBAASC,KAAI,IAAIA,MAAK,GAAGA,MAAK;AAC5B,gBAAID,OAAM,MAAMA,OAAM,KAAKC,OAAM,MAAMA,OAAM,KAC1CD,OAAM,KAAKC,OAAM,GAAI;AACtB,qBAAO,IAAI,MAAMD,IAAG,MAAMC,IAAG,MAAM,IAAI;AAAA,YACzC,OAAO;AACL,qBAAO,IAAI,MAAMD,IAAG,MAAMC,IAAG,OAAO,IAAI;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAQA,aAAS,iBAAkB,QAAQ,SAAS;AAC1C,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,QAAQ,eAAe,OAAO;AAC3C,UAAI,KAAK,KAAK;AAEd,eAASF,KAAI,GAAGA,KAAI,IAAIA,MAAK;AAC3B,cAAM,KAAK,MAAMA,KAAI,CAAC;AACtB,cAAMA,KAAI,IAAI,OAAO,IAAI;AACzB,eAAQ,QAAQA,KAAK,OAAO;AAE5B,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI;AAC9B,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI;AAAA,MAChC;AAAA,IACF;AASA,aAAS,gBAAiB,QAAQ,sBAAsB,aAAa;AACnE,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,WAAW,eAAe,sBAAsB,WAAW;AACxE,UAAIA,IAAG;AAEP,WAAKA,KAAI,GAAGA,KAAI,IAAIA,MAAK;AACvB,eAAQ,QAAQA,KAAK,OAAO;AAG5B,YAAIA,KAAI,GAAG;AACT,iBAAO,IAAIA,IAAG,GAAG,KAAK,IAAI;AAAA,QAC5B,WAAWA,KAAI,GAAG;AAChB,iBAAO,IAAIA,KAAI,GAAG,GAAG,KAAK,IAAI;AAAA,QAChC,OAAO;AACL,iBAAO,IAAI,OAAO,KAAKA,IAAG,GAAG,KAAK,IAAI;AAAA,QACxC;AAGA,YAAIA,KAAI,GAAG;AACT,iBAAO,IAAI,GAAG,OAAOA,KAAI,GAAG,KAAK,IAAI;AAAA,QACvC,WAAWA,KAAI,GAAG;AAChB,iBAAO,IAAI,GAAG,KAAKA,KAAI,IAAI,GAAG,KAAK,IAAI;AAAA,QACzC,OAAO;AACL,iBAAO,IAAI,GAAG,KAAKA,KAAI,GAAG,KAAK,IAAI;AAAA,QACrC;AAAA,MACF;AAGA,aAAO,IAAI,OAAO,GAAG,GAAG,GAAG,IAAI;AAAA,IACjC;AAQA,aAAS,UAAW,QAAQ,MAAM;AAChC,YAAM,OAAO,OAAO;AACpB,UAAI,MAAM;AACV,UAAI,MAAM,OAAO;AACjB,UAAI,WAAW;AACf,UAAI,YAAY;AAEhB,eAAS,MAAM,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG;AAC1C,YAAI,QAAQ,EAAG;AAEf,eAAO,MAAM;AACX,mBAASE,KAAI,GAAGA,KAAI,GAAGA,MAAK;AAC1B,gBAAI,CAAC,OAAO,WAAW,KAAK,MAAMA,EAAC,GAAG;AACpC,kBAAI,OAAO;AAEX,kBAAI,YAAY,KAAK,QAAQ;AAC3B,wBAAU,KAAK,SAAS,MAAM,WAAY,OAAO;AAAA,cACnD;AAEA,qBAAO,IAAI,KAAK,MAAMA,IAAG,IAAI;AAC7B;AAEA,kBAAI,aAAa,IAAI;AACnB;AACA,2BAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAEP,cAAI,MAAM,KAAK,QAAQ,KAAK;AAC1B,mBAAO;AACP,kBAAM,CAAC;AACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAUA,aAAS,WAAY,SAAS,sBAAsB,UAAU;AAE5D,YAAM,SAAS,IAAI,UAAU;AAE7B,eAAS,QAAQ,SAAU,MAAM;AAE/B,eAAO,IAAI,KAAK,KAAK,KAAK,CAAC;AAS3B,eAAO,IAAI,KAAK,UAAU,GAAG,KAAK,sBAAsB,KAAK,MAAM,OAAO,CAAC;AAG3E,aAAK,MAAM,MAAM;AAAA,MACnB,CAAC;AAGD,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAC5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AACpF,YAAM,0BAA0B,iBAAiB,oBAAoB;AAOrE,UAAI,OAAO,gBAAgB,IAAI,KAAK,wBAAwB;AAC1D,eAAO,IAAI,GAAG,CAAC;AAAA,MACjB;AAOA,aAAO,OAAO,gBAAgB,IAAI,MAAM,GAAG;AACzC,eAAO,OAAO,CAAC;AAAA,MACjB;AAMA,YAAM,iBAAiB,yBAAyB,OAAO,gBAAgB,KAAK;AAC5E,eAASF,KAAI,GAAGA,KAAI,eAAeA,MAAK;AACtC,eAAO,IAAIA,KAAI,IAAI,KAAO,KAAM,CAAC;AAAA,MACnC;AAEA,aAAO,gBAAgB,QAAQ,SAAS,oBAAoB;AAAA,IAC9D;AAWA,aAAS,gBAAiB,WAAW,SAAS,sBAAsB;AAElE,YAAM,iBAAiB,MAAM,wBAAwB,OAAO;AAG5D,YAAM,mBAAmB,OAAO,uBAAuB,SAAS,oBAAoB;AAGpF,YAAM,qBAAqB,iBAAiB;AAG5C,YAAM,gBAAgB,OAAO,eAAe,SAAS,oBAAoB;AAGzE,YAAM,iBAAiB,iBAAiB;AACxC,YAAM,iBAAiB,gBAAgB;AAEvC,YAAM,yBAAyB,KAAK,MAAM,iBAAiB,aAAa;AAExE,YAAM,wBAAwB,KAAK,MAAM,qBAAqB,aAAa;AAC3E,YAAM,wBAAwB,wBAAwB;AAGtD,YAAM,UAAU,yBAAyB;AAGzC,YAAM,KAAK,IAAI,mBAAmB,OAAO;AAEzC,UAAI,SAAS;AACb,YAAM,SAAS,IAAI,MAAM,aAAa;AACtC,YAAM,SAAS,IAAI,MAAM,aAAa;AACtC,UAAI,cAAc;AAClB,YAAM,SAAS,IAAI,WAAW,UAAU,MAAM;AAG9C,eAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,cAAM,WAAW,IAAI,iBAAiB,wBAAwB;AAG9D,eAAO,CAAC,IAAI,OAAO,MAAM,QAAQ,SAAS,QAAQ;AAGlD,eAAO,CAAC,IAAI,GAAG,OAAO,OAAO,CAAC,CAAC;AAE/B,kBAAU;AACV,sBAAc,KAAK,IAAI,aAAa,QAAQ;AAAA,MAC9C;AAIA,YAAM,OAAO,IAAI,WAAW,cAAc;AAC1C,UAAI,QAAQ;AACZ,UAAIA,IAAGC;AAGP,WAAKD,KAAI,GAAGA,KAAI,aAAaA,MAAK;AAChC,aAAKC,KAAI,GAAGA,KAAI,eAAeA,MAAK;AAClC,cAAID,KAAI,OAAOC,EAAC,EAAE,QAAQ;AACxB,iBAAK,OAAO,IAAI,OAAOA,EAAC,EAAED,EAAC;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAGA,WAAKA,KAAI,GAAGA,KAAI,SAASA,MAAK;AAC5B,aAAKC,KAAI,GAAGA,KAAI,eAAeA,MAAK;AAClC,eAAK,OAAO,IAAI,OAAOA,EAAC,EAAED,EAAC;AAAA,QAC7B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,aAAS,aAAc,MAAM,SAAS,sBAAsB,aAAa;AACvE,UAAI;AAEJ,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,mBAAW,SAAS,UAAU,IAAI;AAAA,MACpC,WAAW,OAAO,SAAS,UAAU;AACnC,YAAI,mBAAmB;AAEvB,YAAI,CAAC,kBAAkB;AACrB,gBAAM,cAAc,SAAS,SAAS,IAAI;AAG1C,6BAAmB,QAAQ,sBAAsB,aAAa,oBAAoB;AAAA,QACpF;AAIA,mBAAW,SAAS,WAAW,MAAM,oBAAoB,EAAE;AAAA,MAC7D,OAAO;AACL,cAAM,IAAI,MAAM,cAAc;AAAA,MAChC;AAGA,YAAM,cAAc,QAAQ,sBAAsB,UAAU,oBAAoB;AAGhF,UAAI,CAAC,aAAa;AAChB,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAGA,UAAI,CAAC,SAAS;AACZ,kBAAU;AAAA,MAGZ,WAAW,UAAU,aAAa;AAChC,cAAM,IAAI;AAAA,UAAM,0HAE0C,cAAc;AAAA,QACxE;AAAA,MACF;AAEA,YAAM,WAAW,WAAW,SAAS,sBAAsB,QAAQ;AAGnE,YAAM,cAAc,MAAM,cAAc,OAAO;AAC/C,YAAM,UAAU,IAAI,UAAU,WAAW;AAGzC,yBAAmB,SAAS,OAAO;AACnC,yBAAmB,OAAO;AAC1B,4BAAsB,SAAS,OAAO;AAMtC,sBAAgB,SAAS,sBAAsB,CAAC;AAEhD,UAAI,WAAW,GAAG;AAChB,yBAAiB,SAAS,OAAO;AAAA,MACnC;AAGA,gBAAU,SAAS,QAAQ;AAE3B,UAAI,MAAM,WAAW,GAAG;AAEtB,sBAAc,YAAY;AAAA,UAAY;AAAA,UACpC,gBAAgB,KAAK,MAAM,SAAS,oBAAoB;AAAA,QAAC;AAAA,MAC7D;AAGA,kBAAY,UAAU,aAAa,OAAO;AAG1C,sBAAgB,SAAS,sBAAsB,WAAW;AAE1D,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAWA,YAAQ,SAAS,SAAS,OAAQ,MAAM,SAAS;AAC/C,UAAI,OAAO,SAAS,eAAe,SAAS,IAAI;AAC9C,cAAM,IAAI,MAAM,eAAe;AAAA,MACjC;AAEA,UAAI,uBAAuB,QAAQ;AACnC,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,YAAY,aAAa;AAElC,+BAAuB,QAAQ,KAAK,QAAQ,sBAAsB,QAAQ,CAAC;AAC3E,kBAAU,QAAQ,KAAK,QAAQ,OAAO;AACtC,eAAO,YAAY,KAAK,QAAQ,WAAW;AAE3C,YAAI,QAAQ,YAAY;AACtB,gBAAM,kBAAkB,QAAQ,UAAU;AAAA,QAC5C;AAAA,MACF;AAEA,aAAO,aAAa,MAAM,SAAS,sBAAsB,IAAI;AAAA,IAC/D;AAAA;AAAA;;;AC9eA,IAAAG,iBAAA;AAAA;AAAA,aAAS,SAAU,KAAK;AACtB,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,SAAS;AAAA,MACrB;AAEA,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,UAAI,UAAU,IAAI,MAAM,EAAE,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE;AACnD,UAAI,QAAQ,SAAS,KAAK,QAAQ,WAAW,KAAK,QAAQ,SAAS,GAAG;AACpE,cAAM,IAAI,MAAM,wBAAwB,GAAG;AAAA,MAC7C;AAGA,UAAI,QAAQ,WAAW,KAAK,QAAQ,WAAW,GAAG;AAChD,kBAAU,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,QAAQ,IAAI,SAAUC,IAAG;AAClE,iBAAO,CAACA,IAAGA,EAAC;AAAA,QACd,CAAC,CAAC;AAAA,MACJ;AAGA,UAAI,QAAQ,WAAW,EAAG,SAAQ,KAAK,KAAK,GAAG;AAE/C,YAAM,WAAW,SAAS,QAAQ,KAAK,EAAE,GAAG,EAAE;AAE9C,aAAO;AAAA,QACL,GAAI,YAAY,KAAM;AAAA,QACtB,GAAI,YAAY,KAAM;AAAA,QACtB,GAAI,YAAY,IAAK;AAAA,QACrB,GAAG,WAAW;AAAA,QACd,KAAK,MAAM,QAAQ,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE;AAAA,MACxC;AAAA,IACF;AAEA,YAAQ,aAAa,SAAS,WAAY,SAAS;AACjD,UAAI,CAAC,QAAS,WAAU,CAAC;AACzB,UAAI,CAAC,QAAQ,MAAO,SAAQ,QAAQ,CAAC;AAErC,YAAM,SAAS,OAAO,QAAQ,WAAW,eACvC,QAAQ,WAAW,QACnB,QAAQ,SAAS,IACf,IACA,QAAQ;AAEZ,YAAM,QAAQ,QAAQ,SAAS,QAAQ,SAAS,KAAK,QAAQ,QAAQ;AACrE,YAAM,QAAQ,QAAQ,SAAS;AAE/B,aAAO;AAAA,QACL;AAAA,QACA,OAAO,QAAQ,IAAI;AAAA,QACnB;AAAA,QACA,OAAO;AAAA,UACL,MAAM,SAAS,QAAQ,MAAM,QAAQ,WAAW;AAAA,UAChD,OAAO,SAAS,QAAQ,MAAM,SAAS,WAAW;AAAA,QACpD;AAAA,QACA,MAAM,QAAQ;AAAA,QACd,cAAc,QAAQ,gBAAgB,CAAC;AAAA,MACzC;AAAA,IACF;AAEA,YAAQ,WAAW,SAAS,SAAU,QAAQ,MAAM;AAClD,aAAO,KAAK,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,IACtD,KAAK,SAAS,SAAS,KAAK,SAAS,KACrC,KAAK;AAAA,IACX;AAEA,YAAQ,gBAAgB,SAAS,cAAe,QAAQ,MAAM;AAC5D,YAAM,QAAQ,QAAQ,SAAS,QAAQ,IAAI;AAC3C,aAAO,KAAK,OAAO,SAAS,KAAK,SAAS,KAAK,KAAK;AAAA,IACtD;AAEA,YAAQ,gBAAgB,SAAS,cAAe,SAAS,IAAI,MAAM;AACjE,YAAM,OAAO,GAAG,QAAQ;AACxB,YAAM,OAAO,GAAG,QAAQ;AACxB,YAAM,QAAQ,QAAQ,SAAS,MAAM,IAAI;AACzC,YAAM,aAAa,KAAK,OAAO,OAAO,KAAK,SAAS,KAAK,KAAK;AAC9D,YAAM,eAAe,KAAK,SAAS;AACnC,YAAM,UAAU,CAAC,KAAK,MAAM,OAAO,KAAK,MAAM,IAAI;AAElD,eAASC,KAAI,GAAGA,KAAI,YAAYA,MAAK;AACnC,iBAASC,KAAI,GAAGA,KAAI,YAAYA,MAAK;AACnC,cAAI,UAAUD,KAAI,aAAaC,MAAK;AACpC,cAAI,UAAU,KAAK,MAAM;AAEzB,cAAID,MAAK,gBAAgBC,MAAK,gBAC5BD,KAAI,aAAa,gBAAgBC,KAAI,aAAa,cAAc;AAChE,kBAAM,OAAO,KAAK,OAAOD,KAAI,gBAAgB,KAAK;AAClD,kBAAM,OAAO,KAAK,OAAOC,KAAI,gBAAgB,KAAK;AAClD,sBAAU,QAAQ,KAAK,OAAO,OAAO,IAAI,IAAI,IAAI,CAAC;AAAA,UACpD;AAEA,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,QAAQ,IAAI,QAAQ;AAC5B,kBAAQ,MAAM,IAAI,QAAQ;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AClGA,IAAAC,kBAAA;AAAA;AAAA,QAAM,QAAQ;AAEd,aAAS,YAAa,KAAK,QAAQ,MAAM;AACvC,UAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAE/C,UAAI,CAAC,OAAO,MAAO,QAAO,QAAQ,CAAC;AACnC,aAAO,SAAS;AAChB,aAAO,QAAQ;AACf,aAAO,MAAM,SAAS,OAAO;AAC7B,aAAO,MAAM,QAAQ,OAAO;AAAA,IAC9B;AAEA,aAAS,mBAAoB;AAC3B,UAAI;AACF,eAAO,SAAS,cAAc,QAAQ;AAAA,MACxC,SAAS,GAAG;AACV,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AAAA,IACF;AAEA,YAAQ,SAAS,SAAS,OAAQ,QAAQ,QAAQ,SAAS;AACzD,UAAI,OAAO;AACX,UAAI,WAAW;AAEf,UAAI,OAAO,SAAS,gBAAgB,CAAC,UAAU,CAAC,OAAO,aAAa;AAClE,eAAO;AACP,iBAAS;AAAA,MACX;AAEA,UAAI,CAAC,QAAQ;AACX,mBAAW,iBAAiB;AAAA,MAC9B;AAEA,aAAO,MAAM,WAAW,IAAI;AAC5B,YAAM,OAAO,MAAM,cAAc,OAAO,QAAQ,MAAM,IAAI;AAE1D,YAAM,MAAM,SAAS,WAAW,IAAI;AACpC,YAAM,QAAQ,IAAI,gBAAgB,MAAM,IAAI;AAC5C,YAAM,cAAc,MAAM,MAAM,QAAQ,IAAI;AAE5C,kBAAY,KAAK,UAAU,IAAI;AAC/B,UAAI,aAAa,OAAO,GAAG,CAAC;AAE5B,aAAO;AAAA,IACT;AAEA,YAAQ,kBAAkB,SAAS,gBAAiB,QAAQ,QAAQ,SAAS;AAC3E,UAAI,OAAO;AAEX,UAAI,OAAO,SAAS,gBAAgB,CAAC,UAAU,CAAC,OAAO,aAAa;AAClE,eAAO;AACP,iBAAS;AAAA,MACX;AAEA,UAAI,CAAC,KAAM,QAAO,CAAC;AAEnB,YAAM,WAAW,QAAQ,OAAO,QAAQ,QAAQ,IAAI;AAEpD,YAAM,OAAO,KAAK,QAAQ;AAC1B,YAAM,eAAe,KAAK,gBAAgB,CAAC;AAE3C,aAAO,SAAS,UAAU,MAAM,aAAa,OAAO;AAAA,IACtD;AAAA;AAAA;;;AC9DA;AAAA;AAAA,QAAM,QAAQ;AAEd,aAAS,eAAgB,OAAO,QAAQ;AACtC,YAAM,QAAQ,MAAM,IAAI;AACxB,YAAM,MAAM,SAAS,OAAO,MAAM,MAAM;AAExC,aAAO,QAAQ,IACX,MAAM,MAAM,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,IAAI,MAChE;AAAA,IACN;AAEA,aAAS,OAAQ,KAAK,GAAGC,IAAG;AAC1B,UAAI,MAAM,MAAM;AAChB,UAAI,OAAOA,OAAM,YAAa,QAAO,MAAMA;AAE3C,aAAO;AAAA,IACT;AAEA,aAAS,SAAU,MAAM,MAAM,QAAQ;AACrC,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,aAAa;AAEjB,eAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,cAAM,MAAM,KAAK,MAAMA,KAAI,IAAI;AAC/B,cAAM,MAAM,KAAK,MAAMA,KAAI,IAAI;AAE/B,YAAI,CAAC,OAAO,CAAC,OAAQ,UAAS;AAE9B,YAAI,KAAKA,EAAC,GAAG;AACX;AAEA,cAAI,EAAEA,KAAI,KAAK,MAAM,KAAK,KAAKA,KAAI,CAAC,IAAI;AACtC,oBAAQ,SACJ,OAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,MAAM,IAC5C,OAAO,KAAK,QAAQ,CAAC;AAEzB,qBAAS;AACT,qBAAS;AAAA,UACX;AAEA,cAAI,EAAE,MAAM,IAAI,QAAQ,KAAKA,KAAI,CAAC,IAAI;AACpC,oBAAQ,OAAO,KAAK,UAAU;AAC9B,yBAAa;AAAA,UACf;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,SAAS,SAAS,OAAQ,QAAQ,SAAS,IAAI;AACrD,YAAM,OAAO,MAAM,WAAW,OAAO;AACrC,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,OAAO,OAAO,QAAQ;AAC5B,YAAM,aAAa,OAAO,KAAK,SAAS;AAExC,YAAM,KAAK,CAAC,KAAK,MAAM,MAAM,IACzB,KACA,WAAW,eAAe,KAAK,MAAM,OAAO,MAAM,IAClD,cAAc,aAAa,MAAM,aAAa;AAElD,YAAM,OACJ,WAAW,eAAe,KAAK,MAAM,MAAM,QAAQ,IACnD,SAAS,SAAS,MAAM,MAAM,KAAK,MAAM,IAAI;AAE/C,YAAM,UAAU,kBAAuB,aAAa,MAAM,aAAa;AAEvE,YAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,eAAe,KAAK,QAAQ;AAEtF,YAAM,SAAS,6CAA6C,QAAQ,UAAU,mCAAmC,KAAK,OAAO;AAE7H,UAAI,OAAO,OAAO,YAAY;AAC5B,WAAG,MAAM,MAAM;AAAA,MACjB;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AChFA;AAAA;AACA,QAAM,aAAa;AAEnB,QAAM,SAAS;AACf,QAAM,iBAAiB;AACvB,QAAM,cAAc;AAEpB,aAAS,aAAc,YAAY,QAAQ,MAAM,MAAM,IAAI;AACzD,YAAM,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACvC,YAAM,UAAU,KAAK;AACrB,YAAM,cAAc,OAAO,KAAK,UAAU,CAAC,MAAM;AAEjD,UAAI,CAAC,eAAe,CAAC,WAAW,GAAG;AACjC,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AAEA,UAAI,aAAa;AACf,YAAI,UAAU,GAAG;AACf,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AAEA,YAAI,YAAY,GAAG;AACjB,eAAK;AACL,iBAAO;AACP,mBAAS,OAAO;AAAA,QAClB,WAAW,YAAY,GAAG;AACxB,cAAI,OAAO,cAAc,OAAO,OAAO,aAAa;AAClD,iBAAK;AACL,mBAAO;AAAA,UACT,OAAO;AACL,iBAAK;AACL,mBAAO;AACP,mBAAO;AACP,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,UAAU,GAAG;AACf,gBAAM,IAAI,MAAM,4BAA4B;AAAA,QAC9C;AAEA,YAAI,YAAY,GAAG;AACjB,iBAAO;AACP,mBAAS,OAAO;AAAA,QAClB,WAAW,YAAY,KAAK,CAAC,OAAO,YAAY;AAC9C,iBAAO;AACP,iBAAO;AACP,mBAAS;AAAA,QACX;AAEA,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,cAAI;AACF,kBAAM,OAAO,OAAO,OAAO,MAAM,IAAI;AACrC,oBAAQ,WAAW,MAAM,QAAQ,IAAI,CAAC;AAAA,UACxC,SAAS,GAAG;AACV,mBAAO,CAAC;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI;AACF,cAAM,OAAO,OAAO,OAAO,MAAM,IAAI;AACrC,WAAG,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC;AAAA,MACzC,SAAS,GAAG;AACV,WAAG,CAAC;AAAA,MACN;AAAA,IACF;AAEA,YAAQ,SAAS,OAAO;AACxB,YAAQ,WAAW,aAAa,KAAK,MAAM,eAAe,MAAM;AAChE,YAAQ,YAAY,aAAa,KAAK,MAAM,eAAe,eAAe;AAG1E,YAAQ,WAAW,aAAa,KAAK,MAAM,SAAU,MAAMC,IAAG,MAAM;AAClE,aAAO,YAAY,OAAO,MAAM,IAAI;AAAA,IACtC,CAAC;AAAA;AAAA;;;ACvED,IAAM,KAAK;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,KAAK,EAAE,OAAO,sBAAsB;AAHvC,IAG0C,KAAqB,gBAAE;AAAA,EAC/D,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,IACP,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,IACV,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,gBAAgB,cAAc;AAAA,EACtC,MAAM,GAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAM,IAAI,GAAG,EAAE,YAAYC,IAAG,UAAUC,IAAG,WAAW,GAAG,UAAU,GAAG,YAAY,EAAE,IAAI,OAAE,CAAC,GAAGC,KAAI,IAAE,IAAI,GAAGC,KAAI,IAAE,IAAI,GAAG,IAAI,IAAE,CAAC,GAAG,IAAI,IAAE,CAAC,GAAG,IAAI,IAAE,CAAC,GAAGC,KAAI,IAAE,KAAE,GAAGC,KAAI,IAAE,EAAE,KAAK,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,IAAE,KAAE,GAAGC,KAAI,SAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,GAAGC,KAAI,SAAE,MAAM;AAC9P,YAAM,IAAI,SAAS,iBAAiBC,KAAI,YAAER,GAAE,SAAS,iBAAiB,CAAC,EAAE,iBAAiB,sBAAsB,EAAE,KAAK,KAAK,SAAS,GAAG,IAAI,YAAEC,GAAE,SAAS,iBAAiB,CAAC,EAAE,iBAAiB,uBAAuB,EAAE,KAAK,KAAK,SAAS,GAAGQ,KAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,IAAI,KAAK,MAAMD,GAAE,KAAK,EAAE,IAAIA,GAAE,KAAKC,EAAC,GAAG,IAAI,KAAK,MAAMD,GAAE,KAAK,EAAE,IAAIA,GAAE,KAAKC,EAAC,GAAGC,KAAI,KAAK,MAAMF,GAAE,KAAK,EAAE,IAAIA,GAAE,KAAKC,EAAC,GAAG,IAAI,KAAK,MAAMD,GAAE,KAAK,EAAE,IAAIA,GAAE,KAAKC,EAAC;AACza,aAAO,OAAO,CAAC,KAAK,CAAC,KAAKC,EAAC,KAAK,CAAC;AAAA,IACnC,CAAC,GAAG,IAAIX;AACR,cAAE,MAAM;AACN,MAAAG,GAAE,SAASC,GAAE,UAAU,EAAE,QAAQD,GAAE,MAAM,cAAcC,GAAE,MAAM,cAAcA,GAAE,MAAM,aAAa;AAAA,IACpG,CAAC,GAAG,MAAE,GAAG,CAAC,GAAGK,OAAM;AACjB,YAAM,SAAMA,OAAM,SAAOG,GAAE,GAAGN,GAAE,QAAQ;AAAA,IAC1C,CAAC;AACD,aAASO,GAAE,GAAG;AACZ,QAAE,QAAQ,SAAS,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,SAASR,GAAE,QAAQ;AAAA,IAChF;AACA,aAASS,GAAE,GAAG;AACZ,UAAI,CAACX,GAAE,SAAS,CAACE,GAAE,SAAS,EAAE,MAAO;AACrC,QAAE,UAAU,SAAS,qBAAqB,EAAE,KAAK,GAAG,EAAE,QAAQ;AAC9D,YAAMI,KAAI,EAAE,QAAQ,CAAC,GAAG,IAAIN,GAAE,MAAM,sBAAsB;AAC1D,UAAI,CAACY,GAAE,GAAGN,GAAE,SAASA,GAAE,OAAO,GAAG;AAC/B,UAAE;AACF;AAAA,MACF;AACA,YAAM,IAAIA,GAAE,UAAU,EAAE;AACxB,QAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,KAAK,IAAI,GAAGF,GAAE,UAAU,EAAE,QAAQ,QAAKA,GAAE,SAAS,CAAC,EAAE,UAAU,EAAE,QAAQ,MAAI,EAAE,cAAc,IAAIS,GAAE,EAAE,KAAK;AAAA,IAC5I;AACA,aAASC,KAAI;AACX,OAACZ,GAAE,SAAS,CAAC,EAAE,UAAU,EAAE,UAAU,SAAS,qBAAqB,EAAE,KAAK,GAAG,EAAE,QAAQ,OAAOE,GAAE,QAAQW,GAAE,IAAI,EAAE;AAAA,IAClH;AACA,aAAS,IAAI;AACX,MAAAN,GAAE;AAAA,IACJ;AACA,aAASM,KAAI;AACX,QAAE,QAAQ,EAAE,OAAOF,GAAE,EAAE,KAAK,GAAGV,GAAE,QAAQ,MAAID,GAAE,QAAQ,OAAI,EAAE,cAAc;AAAA,IAC7E;AACA,aAASO,KAAI;AACX,QAAE,QAAQ,GAAGI,GAAE,EAAE,KAAK,GAAGX,GAAE,QAAQ;AAAA,IACrC;AACA,aAASU,GAAE,GAAGN,IAAG,GAAG;AAClB,YAAM,EAAE,MAAMC,IAAG,KAAK,GAAG,QAAQ,EAAE,IAAI;AACvC,aAAOD,MAAKC,MAAK,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI;AAAA,IAClD;AACA,aAASM,GAAE,GAAG;AACZ,MAAAZ,GAAE,UAAUA,GAAE,MAAM,MAAM,YAAY,cAAc,CAAC;AAAA,IACvD;AACA,WAAO,CAAC,GAAGK,QAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC9B,SAAS;AAAA,MACT,KAAKN;AAAA,MACL,OAAO,eAAE,CAAC,gBAAgB,EAAE,QAAQE,GAAE,OAAO,YAAYC,GAAE,OAAO,UAAU,MAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IACrF,GAAG;AAAA,MACD,gBAAE,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO,eAAE,EAAE,OAAO,EAAE,QAAQ,QAAQ,EAAE,KAAK,iCAAiC,EAAE,CAAC;AAAA,MACjF,GAAG,MAAM,CAAC;AAAA,MACV,gBAAE,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO,eAAE,EAAE,OAAOE,GAAE,MAAM,CAAC;AAAA,MAC7B,GAAG;AAAA,QACD,EAAE,QAAQ,EAAE,KAAK,aAAa,WAAW,UAAE,GAAG,YAAE,GAAG;AAAA,UACjD,KAAK;AAAA,UACL,MAAM,EAAE,KAAK;AAAA,UACb,OAAO,eAAE,eAAe,EAAE,KAAK,SAAS,EAAE,EAAE;AAAA,QAC9C,GAAG,MAAM,GAAG,CAAC,QAAQ,OAAO,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QAC1C,EAAE,SAAS,EAAE,YAAY,UAAE,GAAG,mBAAE,QAAQ,IAAI;AAAA,UAC1C,EAAE,SAAS,UAAE,GAAG,mBAAE,QAAQ;AAAA,YACxB,KAAK;AAAA,YACL,OAAO,eAAE,kBAAkB,EAAE,UAAU;AAAA,UACzC,GAAG,gBAAE,EAAE,KAAK,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UAC7B,EAAE,YAAY,UAAE,GAAG,mBAAE,QAAQ;AAAA,YAC3B,KAAK;AAAA,YACL,OAAO,eAAE,qBAAqB,EAAE,aAAa;AAAA,UAC/C,GAAG,gBAAE,EAAE,QAAQ,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QAClC,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACd,EAAE,QAAQ,EAAE,KAAK,aAAa,WAAW,UAAE,GAAG,YAAE,GAAG;AAAA,UACjD,KAAK;AAAA,UACL,MAAM,EAAE,KAAK;AAAA,UACb,OAAO,eAAE,iBAAiB,EAAE,KAAK,KAAK;AAAA,QACxC,GAAG,MAAM,GAAG,CAAC,QAAQ,OAAO,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAC5C,GAAG,CAAC;AAAA,MACJ,gBAAE,OAAO;AAAA,QACP,SAAS;AAAA,QACT,KAAKJ;AAAA,QACL,OAAO;AAAA,QACP,qBAAqBS;AAAA,QACrB,oBAAoBC;AAAA,QACpB,YAAY,cAAEG,IAAG,CAAC,QAAQ,SAAS,CAAC;AAAA,QACpC,eAAe,cAAE,GAAG,CAAC,QAAQ,SAAS,CAAC;AAAA,MACzC,GAAG;AAAA,QACD,gBAAE,OAAO,IAAI;AAAA,UACX,WAAG,EAAE,QAAQ,UAAU,CAAC,GAAG,MAAM;AAAA,YAC/BX,GAAE,QAAQ,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAE,GAAG;AAAA,cAC/B,KAAK;AAAA,cACL,MAAM;AAAA,YACR,CAAC;AAAA,YACDA,GAAE,SAAS,UAAE,GAAG,YAAEA,IAAI;AAAA,cACpB,KAAK;AAAA,cACL,iBAAiB;AAAA,cACjB,MAAM;AAAA,cACN,WAAW;AAAA,YACb,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UAChB,CAAC;AAAA,QACH,CAAC;AAAA,MACH,GAAG,GAAG;AAAA,IACR,GAAG,CAAC;AAAA,EACN;AACF,CAAC;;;ACtJD,IAAM,IAAI,EAAE,OAAO,gBAAgB;AAAnC,IAAsC,IAAI;AAAA,EACxC,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGGa,KAAI,EAAE,OAAO,cAAc;AAH9B,IAGiC,IAAI;AAAA,EACnC,KAAK;AAAA,EACL,OAAO;AACT;AANA,IAMG,IAAI;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT;AATA,IASG,KAAqB,gBAAE;AAAA,EACxB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,aAAa;AAAA,EACrB,MAAMC,IAAG,EAAE,QAAQC,IAAG,MAAM,EAAE,GAAG;AAC/B,UAAMC,KAAIF,IAAG,EAAE,GAAGG,GAAE,IAAI,QAAE,GAAG,EAAE,OAAO,GAAG,eAAe,GAAG,WAAWC,GAAE,IAAI,OAAEF,EAAC,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,EAAE,GAAGG,KAAI,IAAE,EAAE,GAAGC,KAAI,IAAE,IAAE,GAAGC,KAAI,IAAEH,GAAE,KAAK,GAAG,IAAI,SAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,GAAGI,KAAI;AAClM,UAAE,GAAG,MAAM;AACT,MAAAD,GAAE,QAAQ;AAAA,IACZ,CAAC,GAAG,MAAEH,IAAG,CAAC,MAAM;AACd,MAAAG,GAAE,QAAQ;AAAA,IACZ,CAAC;AACD,aAASE,GAAE,GAAG;AACZ,QAAE,QAAQ,MAAID,GAAE,eAAe,GAAG,IAAE;AAAA,IACtC;AACA,aAAS,EAAE,GAAG;AACZ,QAAE,QAAQ,OAAIA,GAAE,eAAe,GAAG,KAAE;AAAA,IACtC;AACA,aAASE,GAAE,GAAG;AACZ,QAAE,QAAQ;AAAA,IACZ;AACA,aAASC,GAAE,GAAG,GAAG;AACf,MAAAL,GAAE,QAAQ,GAAGD,GAAE,QAAQ;AAAA,IACzB;AACA,aAAS,IAAI;AACX,UAAI,CAACE,GAAE,SAASD,GAAE,MAAO,QAAO;AAChC,UAAI,IAAI,IAAI,IAAI,CAAC;AACjB,YAAM,IAAI,CAAC,MAAM,EAAE,OAAO,CAACM,IAAGC,OAAM;AAClC,cAAM,CAACC,IAAG,GAAGC,EAAC,IAAIF,GAAE,MAAM,GAAG;AAC7B,eAAOD,GAAEE,EAAC,IAAIC,GAAE,KAAK,GAAG,GAAGH;AAAA,MAC7B,GAAG,CAAC,CAAC;AACL,UAAIP,GAAE,OAAO;AACX,cAAM,CAAC,GAAG,GAAGO,EAAC,IAAIP,GAAE,MAAM,MAAM,GAAG;AACnC,YAAI,IAAI,SAAS,CAAC,IAAI,OAAO,KAAKO,EAAC,EAAE,UAAU,OAAO,OAAO,GAAG,EAAEA,EAAC,CAAC,GAAG,KAAK,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG;AACzG,gBAAMC,KAAI,EAAE,MAAM,CAAC,KAAK,IAAI,CAACC,IAAG,GAAGC,EAAC,IAAIF,GAAE,MAAM,GAAG;AACnD,cAAI,SAASC,EAAC,IAAI,OAAO,KAAKC,EAAC,EAAE,UAAU,OAAO,OAAO,GAAG,EAAEA,EAAC,CAAC;AAAA,QAClE;AAAA,MACF;AACA,aAAO,IAAIZ,GAAE,GAAG,CAAC,IAAIA,GAAE,eAAe;AAAA,IACxC;AACA,WAAOF,GAAE;AAAA,MACP,SAASQ;AAAA,MACT,QAAQ;AAAA,MACR,SAASC;AAAA,MACT,YAAYC;AAAA,IACd,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,OAAO,eAAE,CAAC,mBAAmB,EAAE,UAAUX,GAAE,SAAS,GAAG,EAAE,UAAUA,GAAE,SAAS,GAAG,EAAE,QAAQ,EAAE,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,SAASO,GAAE,QAAQ,KAAK,CAACD,GAAE,MAAM,CAAC,CAAC;AAAA,IACjK,GAAG;AAAA,MACD,gBAAE,OAAO,GAAG;AAAA,QACVN,GAAE,QAAQ,CAAC,MAAE,CAAC,KAAK,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,UAClC,YAAE,GAAG;AAAA,YACH,MAAMA,GAAE,KAAK;AAAA,YACb,OAAO,eAAEA,GAAE,KAAK,KAAK;AAAA,UACvB,GAAG,MAAM,GAAG,CAAC,QAAQ,OAAO,CAAC;AAAA,QAC/B,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACd,gBAAE,OAAOD,IAAG;AAAA,UACV,WAAE,EAAE,QAAQ,WAAW;AAAA,YACrB,UAAUC,GAAE;AAAA,YACZ,UAAUA,GAAE;AAAA,YACZ,UAAUA,GAAE;AAAA,YACZ,cAAcS;AAAA,YACd,aAAa;AAAA,YACb,eAAeC;AAAA,YACf,cAAcC;AAAA,UAChB,CAAC;AAAA,UACD,MAAE,CAAC,KAAK,UAAE,GAAG,mBAAE,OAAO;AAAA,YACpB,KAAK;AAAA,YACL,OAAO,eAAE,CAAC,mBAAmBX,GAAE,UAAU,CAAC;AAAA,UAC5C,GAAG;AAAA,YACDA,GAAE,QAAQ,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,cACzB,YAAE,GAAG;AAAA,gBACH,MAAMA,GAAE,KAAK;AAAA,gBACb,OAAO,eAAEA,GAAE,KAAK,KAAK;AAAA,cACvB,GAAG,MAAM,GAAG,CAAC,QAAQ,OAAO,CAAC;AAAA,YAC/B,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YACd,gBAAE,SAAS,MAAM;AAAA,cACf,gBAAE,gBAAE,MAAE,CAAC,CAAC,GAAG,CAAC;AAAA,cACZA,GAAE,YAAY,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,gBAClC,gBAAE,GAAG;AAAA,cACP,GAAG,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,YACpB,CAAC;AAAA,UACH,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AAAA,MACDO,GAAE,QAAQ,KAAK,CAACD,GAAE,SAAS,UAAE,GAAG,mBAAE,OAAO,GAAG,gBAAE,EAAE,CAAC,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,IACpE,GAAG,CAAC;AAAA,EACN;AACF,CAAC;;;ACjID,6BAAoC;AACpC,IAAM,IAAI,CAAC,OAAO;AAAlB,IAAqB,IAAoB,gBAAE;AAAA,EACzC,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO,CAAC,SAAS,QAAQ,SAAS,WAAW;AAAA,EAC7C,MAAMU,IAAG,EAAE,QAAQC,IAAG,MAAM,EAAE,GAAG;AAC/B,UAAMC,KAAIF,IAAG,EAAE,MAAM,GAAG,OAAOG,IAAG,KAAKC,IAAG,KAAK,GAAG,WAAW,GAAG,aAAa,GAAG,aAAaC,GAAE,IAAI,OAAEH,EAAC,GAAGI,KAAI,IAAEH,GAAE,KAAK,GAAGI,KAAI,SAAE,MAAM;AACnI,UAAI,CAACD,GAAE,MAAO,QAAO;AACrB,UAAI,EAAE,UAAU,OAAQ,YAAO,uBAAAE,gBAAEF,GAAE,KAAK,EAAE,OAAO,IAAI;AACrD,UAAID,GAAE,MAAO,YAAO,uBAAAG,gBAAEF,GAAE,KAAK,EAAE,cAAc,QAAQ,EAAE,UAAU,UAAU;AAC3E,YAAM,IAAI,EAAE,UAAU,SAAS,OAAO;AACtC,iBAAO,uBAAAE,gBAAEF,GAAE,KAAK,EAAE,OAAO,CAAC;AAAA,IAC5B,CAAC,GAAG,IAAI,IAAE,KAAE,GAAGG,KAAI,IAAE,MAAM,GAAG,IAAI,IAAE,EAAE,GAAG,IAAI;AAC7C,UAAEN,IAAG,CAAC,MAAM;AACV,MAAAG,GAAE,QAAQ;AAAA,IACZ,CAAC;AACD,aAASI,KAAI;AACX,QAAE,QAAQ,MAAI,EAAE,SAAS,EAAE,QAAQD,GAAE,MAAM,CAAC,GAAGE,GAAE;AAAA,IACnD;AACA,aAAS,IAAI;AACX,QAAE,QAAQ,OAAI,EAAE,QAAQ,EAAE,QAAQF,GAAE,MAAM,CAAC;AAAA,IAC7C;AACA,aAASE,KAAI;AACX,MAAAT,GAAE,sBAAsBA,GAAE,mBAAmB;AAAA,QAC3C,OAAOI,GAAE,SAAS,EAAE,SAASM,GAAE,MAAM;AAAA,QACrC,MAAM,EAAE;AAAA,QACR,OAAO,OAAO,cAAc,OAAO,WAAW,8BAA8B,EAAE,UAAU,SAAS;AAAA,MACnG,CAAC,EAAE,KAAK,CAAC,MAAM;AACb,QAAAC,GAAE,CAAC;AAAA,MACL,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,UAAE,CAAC;AAAA,MACL,CAAC;AAAA,IACH;AACA,aAASA,GAAE,GAAG;AACZ,aAAO,KAAK,aAAaP,GAAE,QAAQM,GAAE,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQH,GAAE,MAAM,GAAGH,GAAE,KAAK,IAAI,EAAE;AAAA,IACxF;AACA,aAAS,EAAE,GAAG;AACZ,QAAE,aAAa,CAAC,GAAG,EAAE;AAAA,IACvB;AACA,aAASM,GAAE,GAAG;AACZ,aAAO,EAAE,QAAQ,EAAE,UAAU,cAAU,uBAAAJ,gBAAE,CAAC,EAAE,QAAQ,GAAG,EAAE,YAAY,QAAI,uBAAAA,gBAAE,CAAC,EAAE,MAAM,GAAG,EAAE,YAAY,IAAI,QAAI,uBAAAA,gBAAE,CAAC,EAAE,YAAY,QAAI,uBAAAA,gBAAE,EAAE,YAAY;AAAA,IACpJ;AACA,aAAS,IAAI;AACX,YAAM,QAAI,uBAAAA,gBAAEF,GAAE,SAAS,EAAE,GAAG,IAAI,EAAE,QAAQ,GAAGQ,KAAIV,GAAE,QAAQ,EAAE,OAAOA,GAAE,KAAK,KAAK,EAAE,QAAQA,GAAE,KAAK,IAAI,MAAIW,KAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,KAAK,EAAE,SAAS,EAAE,KAAK,IAAI,MAAIC,KAAI,EAAE,UAAU,SAAS,OAAO,EAAE,UAAU,aAAa,OAAO;AACtO,aAAO,EAAE,QAAQ,IAAIF,KAAIC,KAAI,KAAK,+BAA2B,uBAAAP,gBAAE,EAAE,KAAK,EAAE,OAAOQ,EAAC,CAAC,KAAK,gCAA4B,uBAAAR,gBAAEJ,GAAE,KAAK,EAAE,OAAOY,EAAC,CAAC,KAAK,gBAAgB,KAAKF,MAAKC;AAAA,IACvK;AACA,WAAOd,GAAE;AAAA,MACP,OAAOK;AAAA,MACP,gBAAgBC;AAAA,MAChB,OAAOG;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,OAAO;AAAA,MACP,SAAS;AAAA,MACT,KAAKD;AAAA,MACL,SAASE;AAAA,IACX,GAAG;AAAA,MACD,gBAAE,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAOJ,GAAE;AAAA,MACX,GAAG,MAAM,GAAG,CAAC;AAAA,IACf,GAAG,GAAG;AAAA,EACR;AACF,CAAC;;;AC/FD,IAAM,KAAqB,gBAAE;AAAA,EAC3B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAO,CAAC,qBAAqB,cAAc,aAAa,eAAe,YAAY;AAAA,EACnF,MAAMU,IAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,UAAMC,KAAI,CAAC,OAAO,QAAQ,YAAY,QAAQ,OAAO,GAAG,IAAID,IAAG,EAAE,UAAUE,IAAG,UAAU,GAAG,UAAU,GAAG,WAAWC,IAAG,WAAW,GAAG,OAAO,GAAG,YAAYC,IAAG,cAAcC,GAAE,IAAI,OAAE,CAAC,GAAG,EAAE,SAAS,GAAG,WAAW,GAAG,WAAWC,IAAG,KAAKC,IAAG,KAAKC,IAAG,WAAWC,GAAE,IAAI,OAAE,CAAC,GAAG,IAAI,KAAGL,GAAE,SAAS,EAAE,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,WAAE,MAAM,GAAGM,KAAI,WAAE,MAAM,GAAGC,KAAI,IAAE,KAAE,GAAGC,KAAI,qBAAE;AAAA,MAC/V,QAAQ,MAAM,OAAO,6BAAoB;AAAA,MACzC,gBAAgB,EAAE,OAAO;AAAA;AAAA,MAEzB,SAAS;AAAA;AAAA,IAEX,CAAC,GAAGC,KAAI,qBAAE;AAAA,MACR,QAAQ,MAAM,OAAO,4BAAmB;AAAA,MACxC,gBAAgB,EAAE,OAAO;AAAA,MACzB,SAAS;AAAA,IACX,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,cAAQ,EAAE,OAAO;AAAA,QACf,KAAK;AACH,iBAAOD;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAOC;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF,CAAC,GAAGC,KAAI,IAAE,EAAE,GAAGC,KAAI,IAAE,IAAE,GAAGC,KAAI,SAAE,MAAM;AACpC,UAAI;AACJ,aAAOf,GAAE,QAAQ,EAAE,KAAK,MAAM,QAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,mBAAmB,EAAE;AAAA,IAC/F,CAAC,GAAGgB,KAAI,SAAE,MAAM;AACd,UAAIC;AACJ,aAAO,EAAE,SAAS,iBAAiB,EAAE,SAASA,KAAI,EAAE,UAAU,OAAO,SAASA,GAAE,cAAc;AAAA,IAChG,CAAC,GAAGC,KAAI,SAAE,MAAM,CAACjB,GAAE,SAASC,GAAE,SAAS,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AACvD,cAAG,MAAM;AACP,UAAIe,IAAG;AACP,UAAI,EAAE,eAAe,EAAE,KAAK,GAAG,EAAE,qBAAqB,EAAE,KAAK,GAAGR,GAAE,WAAWQ,KAAIb,MAAK,OAAO,SAASA,GAAE,UAAU,QAAQa,GAAE,OAAO;AACjI,cAAME,KAAI,iBAAiBV,GAAE,KAAK,EAAE;AACpC,QAAAA,GAAE,MAAM,MAAM,SAASU,KAAI,QAAQA,EAAC,cAAc,IAAIf,GAAE,UAAU,OAAO,SAAS,EAAE,IAAI,2CAA2C;AAAA,MACrI;AAAA,IACF,CAAC,GAAG,MAAG,CAACD,IAAG,CAAC,GAAG,CAAC,CAACc,IAAG,CAAC,MAAM;AACzB,UAAIE;AACJ,QAAE,SAASA,KAAIF,MAAK,MAAM,OAAO,SAASE,GAAE,KAAK,GAAGL,GAAE,QAAQ,MAAID,GAAE,QAAQ,IAAI,EAAE,eAAe,EAAE,KAAK,GAAG,EAAE,qBAAqB,EAAE,KAAK;AAAA,IAC3I,CAAC;AACD,UAAM,IAAI,SAAG,CAACI,OAAM;AAClB,QAAE,eAAeA,EAAC,GAAG,EAAE,qBAAqBA,EAAC;AAAA,IAC/C,GAAG,GAAG;AACN,aAASG,GAAEH,IAAG,GAAG;AACf,UAAI;AACJ,YAAME,KAAIF,GAAE;AACZ,QAAE,SAAS,IAAI,KAAKE,GAAE,UAAU,OAAO,SAAS,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK;AAAA,IACrE;AACA,aAASE,KAAI;AACX,QAAE,QAAQ,IAAIC,GAAE,GAAG,EAAE,eAAe,EAAE,GAAG,EAAE,qBAAqB,EAAE;AAAA,IACpE;AACA,aAASC,GAAEN,IAAG;AACZ,MAAAP,GAAE,UAAUA,GAAE,QAAQ,MAAI,EAAE,cAAcO,EAAC;AAAA,IAC7C;AACA,aAASO,GAAEP,IAAG;AACZ,MAAAP,GAAE,UAAUA,GAAE,QAAQ,OAAI,EAAE,aAAaO,EAAC;AAAA,IAC5C;AACA,aAASK,KAAI;AACX,UAAIL;AACJ,MAAAP,GAAE,UAAUA,GAAE,QAAQ,OAAKO,KAAI,EAAE,UAAU,QAAQA,GAAE,MAAM;AAAA,IAC7D;AACA,aAASQ,KAAI;AACX,UAAIR;AACJ,MAAAP,GAAE,UAAUA,GAAE,QAAQ,QAAKO,KAAI,EAAE,UAAU,QAAQA,GAAE,KAAK;AAAA,IAC5D;AACA,aAASS,KAAI;AACX,YAAM,IAAI;AAAA,QACR,EAAE,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,SAAS,cAAc;AAAA,QAC7D,EAAE,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,EAAE,MAAM,UAAU,OAAO,EAAE,KAAK,GAAG,SAAS,oBAAoB,EAAE,KAAK,GAAG;AAAA,QACjH,EAAE,OAAO,MAAM,CAAC,EAAE,SAAS,CAACrB,GAAE,SAAS,EAAE,MAAM,UAAU,OAAOA,GAAE,KAAK,GAAG,SAAS,oBAAoBA,GAAE,KAAK,GAAG;AAAA,QACjH,EAAE,OAAO,MAAM,EAAE,SAAS,EAAE,QAAQ,IAAI,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,IAAI,MAAI,SAAS,kBAAkB;AAAA,QACvG,EAAE,OAAO,MAAM,EAAE,EAAE,UAAU,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,KAAK,KAAKC,GAAE,QAAQ,OAAO,EAAE,KAAK,KAAK,OAAOA,GAAE,KAAK,IAAI,MAAI,SAAS,oBAAoBA,GAAE,KAAK,GAAG;AAAA,QACpK,EAAE,OAAO,MAAM,EAAE,EAAE,UAAU,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,KAAK,KAAKC,GAAE,QAAQ,OAAO,EAAE,KAAK,KAAK,OAAOA,GAAE,KAAK,IAAI,MAAI,SAAS,oBAAoBA,GAAE,KAAK,GAAG;AAAA,MACtK,EAAE,KAAK,CAACY,OAAM,CAACA,GAAE,MAAM,CAAC;AACxB,UAAI;AACF,QAAAL,GAAE,QAAQ,OAAID,GAAE,QAAQ,EAAE;AAAA,WACvB;AACH,cAAMM,KAAI,CAAC,EAAE,SAAS,CAAC,EAAE;AACzB,gBAAQ,EAAE,OAAO;AAAA,UACf,KAAK;AACH,YAAAL,GAAE,QAAQK,MAAK,QAAG,EAAE,KAAK,GAAGN,GAAE,QAAQC,GAAE,QAAQ,KAAK;AACrD;AAAA,UACF,KAAK;AACH,YAAAA,GAAE,QAAQK,MAAK,MAAG,EAAE,KAAK,GAAGN,GAAE,QAAQC,GAAE,QAAQ,KAAK;AACrD;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,cAAE,UAAUA,GAAE,QAAQK,MAAK,EAAE,MAAM,cAAc,GAAGN,GAAE,QAAQC,GAAE,QAAQ,KAAK,EAAE,MAAM,qBAAqB;AAC1G;AAAA,QACJ;AACA,YAAI,EAAE,UAAU;AACd,gBAAM,IAAI,EAAE,SAAS,EAAE,KAAK;AAC5B,UAAAA,GAAE,QAAQ,EAAE,SAASD,GAAE,QAAQ,EAAE,UAAU,KAAK,EAAE;AAAA,QACpD;AAAA,MACF;AACA,aAAO,EAAE,cAAcC,GAAE,OAAOD,GAAE,KAAK,GAAGC,GAAE;AAAA,IAC9C;AACA,WAAO,EAAE;AAAA,MACP,OAAO;AAAA,MACP,aAAaE;AAAA,MACb,gBAAgBD;AAAA,MAChB,OAAOO;AAAA,MACP,MAAMG;AAAA,MACN,eAAeC;AAAA,MACf,mBAAmBb;AAAA,MACnB,OAAOQ;AAAA,MACP,UAAU,CAACJ,OAAM;AACf,UAAE,QAAQA,IAAG,EAAE,eAAeA,EAAC,GAAG,EAAE,qBAAqBA,EAAC;AAAA,MAC5D;AAAA,IACF,CAAC,GAAG,CAACA,IAAG,OAAO,UAAE,GAAG,mBAAG,OAAO;AAAA,MAC5B,SAAS;AAAA,MACT,KAAKR;AAAA,MACL,OAAO,eAAG,mBAAmB,MAAE,CAAC,CAAC,YAAY;AAAA,IAC/C,GAAG;AAAA,OACA,UAAE,GAAG,YAAE,wBAAG,EAAE,KAAK,GAAG,WAAG;AAAA,QACtB,SAAS;AAAA,QACT,KAAK;AAAA,QACL,OAAO,EAAE;AAAA,QACT,OAAOV,GAAE;AAAA,QACT,MAAM,MAAE,CAAC;AAAA,MACX,GAAG,EAAE,aAAaA,GAAE,aAAa,UAAU,MAAEE,EAAC,GAAG,UAAU,MAAE,CAAC,GAAG,UAAU,MAAE,CAAC,GAAG,gBAAgBF,GAAE,gBAAgB,cAAcA,GAAE,cAAc,aAAaA,GAAE,aAAa,YAAYA,GAAE,YAAY,SAAS,MAAE,CAAC,GAAG,WAAW,MAAES,EAAC,GAAG,WAAW,MAAE,CAAC,GAAG,WAAW,MAAEH,EAAC,GAAG,KAAK,MAAEC,EAAC,GAAG,KAAK,MAAEC,EAAC,GAAG,GAAG,MAAEH,EAAC,EAAE,GAAG;AAAA,QACxS,SAASmB;AAAA,QACT,QAAQC;AAAA,QACR,SAASJ;AAAA,MACX,CAAC,GAAG,MAAM,IAAI,CAAC,SAAS,SAAS,MAAM,CAAC;AAAA,MACxCF,GAAE,SAAS,UAAE,GAAG,YAAE,GAAI;AAAA,QACpB,KAAK;AAAA,QACL,MAAM,EAAE,MAAM,QAAQ;AAAA,QACtB,MAAM;AAAA,QACN,SAAS,cAAGG,IAAG,CAAC,MAAM,CAAC;AAAA,QACvB,OAAO;AAAA,QACP,cAAc;AAAA,MAChB,CAAC,KAAK,mBAAG,IAAI,IAAE;AAAA,MACf,WAAGJ,GAAE,QAAQ,cAAc;AAAA,IAC7B,GAAG,CAAC;AAAA,EACN;AACF,CAAC;;;ACxOD,IAAM,IAAI,EAAE,OAAO,SAAS;AAA5B,IAA+B,IAAI,CAAC,YAAY,UAAU;AAA1D,IAA6D,IAAI,EAAE,OAAO,gBAAgB;AAA1F,IAA6F,IAAoB,gBAAE;AAAA,EACjH,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,aAAa;AAAA,MACX,MAAM;AAAA,IACR;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,qBAAqB,eAAe,YAAY;AAAA,EACxD,MAAMU,IAAG,EAAE,QAAQ,GAAG,MAAMC,GAAE,GAAG;AAC/B,UAAMC,KAAIF,IAAG,EAAE,SAASG,IAAG,UAAU,EAAE,IAAI,OAAED,EAAC,GAAG,IAAI,IAAEC,GAAE,KAAK,GAAG,IAAI,IAAE,MAAM,GAAG,IAAI,IAAE,EAAE,GAAG,IAAIF;AAC/F,UAAE,GAAG,CAACG,OAAM;AACV,QAAE,eAAeA,EAAC,GAAG,EAAE,qBAAqBA,EAAC;AAAA,IAC/C,CAAC;AACD,aAAS,IAAI;AACX,YAAMA,KAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ;AAChC,aAAO,EAAE,QAAQA,KAAI,KAAK,eAAe,EAAE,cAAcA,IAAG,EAAE,KAAK,GAAGA;AAAA,IACxE;AACA,WAAO,EAAE;AAAA,MACP,OAAO;AAAA,MACP,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB,CAAC,GAAG,CAACA,IAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,OAAO,eAAE,CAAC,oBAAoB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAAA,IACrD,GAAG;AAAA,MACD,gBAAE,OAAO,GAAG;AAAA,QACV,eAAE,gBAAE,SAAS;AAAA,UACX,OAAO;AAAA,UACP,SAAS;AAAA,UACT,KAAK;AAAA,UACL,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAACC,OAAM,EAAE,QAAQA;AAAA,UACxD,MAAM;AAAA,UACN,UAAU,MAAE,CAAC;AAAA,UACb,UAAUL,GAAE;AAAA,QACd,GAAG,MAAM,GAAG,CAAC,GAAG;AAAA,UACd,CAAC,gBAAG,EAAE,KAAK;AAAA,QACb,CAAC;AAAA,QACD,gBAAE,OAAO,GAAG;AAAA,UACVA,GAAE,eAAeA,GAAE,iBAAiB,UAAE,GAAG,YAAE,GAAG,eAAE,WAAE,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQA,GAAE,cAAcA,GAAE,aAAa,CAAC,GAAG,MAAM,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,QAClI,CAAC;AAAA,MACH,CAAC;AAAA,IACH,GAAG,CAAC;AAAA,EACN;AACF,CAAC;;;ACxDD,IAAM,IAAI,EAAE,OAAO,mBAAmB;AAAtC,IAAyC,IAAI;AAAA,EAC3C,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,IAAI;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT;AANA,IAMG,IAAI;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT;AATA,IASG,IAAI,EAAE,OAAO,oBAAoB;AATpC,IASuC,IAAoB,gBAAE;AAAA,EAC3D,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAMM,IAAG;AACP,UAAM,EAAE,GAAGC,GAAE,IAAI,QAAE;AACnB,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,MACjCD,GAAE,SAAS,UAAE,GAAG,mBAAE,OAAO,GAAG,gBAAEA,GAAE,KAAK,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACtDA,GAAE,eAAe,UAAE,GAAG,mBAAE,OAAO,GAAG,gBAAEA,GAAE,WAAW,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAClE,EAAE,OAAO,WAAW,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,QACnC,WAAE,EAAE,QAAQ,SAAS;AAAA,MACvB,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACd,gBAAE,OAAO,GAAG;AAAA,QACV,WAAE,EAAE,QAAQ,WAAW,CAAC,GAAG,MAAM;AAAA,UAC/B,YAAE,GAAG;AAAA,YACH,MAAM;AAAA,YACN,OAAO,MAAEC,EAAC,EAAE,WAAW;AAAA,YACvB,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAACC,OAAM,EAAE,MAAM,aAAa;AAAA,UACvD,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF,CAAC;;;ACxCD,IAAM,IAAI,EAAE,OAAO,oBAAoB;AAAvC,IAA0C,IAAI;AAAA,EAC5C,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGGC,KAAoB,gBAAE;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO,CAAC,OAAO;AAAA,EACf,MAAMC,IAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,GAAG,IAAI,IAAE,MAAM,GAAGC,KAAI,IAAE,KAAE,GAAG,IAAI,IAAE,CAAC,CAAC;AAC/C,aAAS,EAAE,GAAG;AACZ,UAAIC;AACJ,UAAID,GAAE,MAAO;AACb,MAAAA,GAAE,QAAQ,OAAKC,KAAI,EAAE,UAAU,QAAQA,GAAE,aAAa,CAAC;AACvD,YAAMC,KAAI,WAAW,MAAM;AACzB,UAAE,SAAS,CAAC,GAAGF,GAAE,QAAQ;AAAA,MAC3B,GAAG,GAAG;AACN,QAAE,MAAM,KAAKE,EAAC;AAAA,IAChB;AACA,WAAO,YAAE,MAAM;AACb,QAAE,MAAM,QAAQ,CAAC,MAAM,aAAa,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC;AAAA,IACtD,CAAC,GAAG,CAAC,GAAGA,QAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,gBAAE,OAAO,GAAG;AAAA,QACV,WAAE,EAAE,QAAQ,MAAM;AAAA,QAClB,EAAE,OAAO,WAAW,UAAE,GAAG,mBAAE,QAAQ,GAAG;AAAA,UACpC,WAAE,EAAE,QAAQ,SAAS;AAAA,QACvB,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACd,YAAE,GAAG,EAAE,MAAM,gBAAgB,CAAC;AAAA,MAChC,CAAC;AAAA,MACD,YAAE,GAAG;AAAA,QACH,SAAS;AAAA,QACT,KAAK;AAAA,MACP,GAAG,MAAM,GAAG;AAAA,IACd,CAAC;AAAA,EACH;AACF,CAAC;;;ACtCD,uBAAc;AACd,oBAAc;AACd,IAAAC,0BAAoC;AAIpC,IAAM,KAAK;AAAX,IAAqgY,KAAK,CAAC,KAAK;AAAhhY,IAAmhY,KAAK,CAAC,KAAK;AAA9hY,IAAiiY,KAAK,CAAC,IAAI;AAA3iY,IAA8iY,KAAK,CAAC,WAAW;AAA/jY,IAAkkY,KAAK;AAAA,EACrkY,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,KAAK,EAAE,OAAO,QAAQ;AAHzB,IAG4B,KAAK,EAAE,OAAO,OAAO;AAHjD,IAGoD,KAAK,EAAE,OAAO,QAAQ;AAH1E,IAG6E,KAAK,EAAE,OAAO,OAAO;AAHlG,IAGqG,KAAqB,gBAAE;AAAA,EAC1H,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS,WAAW,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,cAAc,CAAC,KAAK;AAAA,IACtG;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,GAAG,EAAE,QAAQC,GAAE,GAAG;AACtB,UAAM,EAAE,GAAG,EAAE,IAAI,QAAE,GAAGC,KAAI,GAAG,EAAE,OAAO,GAAG,aAAaC,IAAG,YAAYC,GAAE,IAAI,OAAEF,EAAC,GAAGG,KAAI,SAAS,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,kBAAkB,CAAC,KAAK,IAAIC,KAAI,SAAS,gBAAgB,aAAa,MAAM,GAAGC,KAAI,IAAE,KAAE,GAAG,IAAI,IAAE,EAAE,QAAQF,EAAC,GAAGG,KAAI,IAAE,EAAE,QAAQH,KAAI,GAAG,GAAG,IAAI,IAAE,CAAC,GAAG,IAAI,IAAE,EAAE,GAAG,IAAI,SAAE,MAAM,GAAGF,GAAE,SAAS,MAAM,IAAIC,GAAE,MAAM,QAAQ,iBAAiB,EAAE,CAAC,EAAE,GAAGK,KAAI,SAAE,MAAM,MAAE,YAAY,SAASN,GAAE,KAAK,CAAC,GAAG,IAAI,SAAE,MAAMC,GAAE,UAAU,MAAEA,GAAE,KAAK,KAAKA,GAAE,MAAM,SAAS,GAAG,GAAGM,KAAI,SAAE,MAAM,EAAE,EAAE,QAAQ,MAAM,EAAE,GAAG,IAAI,SAAE,OAAO;AAAA,MAC7hB,OAAO,GAAG,EAAE,KAAK;AAAA,MACjB,QAAQ,GAAGF,GAAE,KAAK;AAAA,MAClB,cAAc,GAAGA,GAAE,QAAQ,EAAE;AAAA,MAC7B,WAAW,WAAW,EAAE,KAAK;AAAA,IAC/B,EAAE,GAAG,IAAI,SAAE,MAAM;AACf,YAAMG,KAAIL,MAAKA,GAAE,WAAW,IAAI,GAAGM,KAAIN,MAAK,CAAC,MAAM,IAAI,EAAE,SAASA,EAAC,GAAG,IAAIK,KAAI,OAAOC,KAAI,OAAO;AAChG,aAAO;AAAA,QACL,UAAU,GAAGJ,GAAE,QAAQ,CAAC;AAAA,QACxB,SAAS,GAAGA,GAAE,QAAQ,KAAK,MAAMA,GAAE,QAAQ,IAAI;AAAA,MACjD;AAAA,IACF,CAAC,GAAG,IAAI,SAAE,MAAMN,GAAE,cAAcO,GAAE,QAAQ;AAAA,MACxC,QAAQD,GAAE,SAAS,EAAE,QAAQ,OAAO,OAAO;AAAA,MAC3C,WAAWA,GAAE,QAAQ,OAAO;AAAA,IAC9B,IAAI;AAAA,MACF,QAAQA,GAAE,QAAQ,OAAO;AAAA,MACzB,WAAWA,GAAE,QAAQ,OAAO;AAAA,IAC9B,IAAI;AAAA,MACF,QAAQA,GAAE,QAAQ,OAAO;AAAA,MACzB,WAAWA,GAAE,QAAQ,OAAO;AAAA,MAC5B,cAAcA,GAAE,QAAQ,QAAQ;AAAA,MAChC,iBAAiB;AAAA,IACnB,CAAC,GAAGK,KAAI,SAAE,OAAO;AAAA,MACf,UAAUL,GAAE,SAASC,GAAE,QAAQ,OAAO,QAAQ;AAAA,MAC9C,YAAYA,GAAE,QAAQ,MAAM;AAAA,MAC5B,SAAS,KAAKD,GAAE,QAAQ,IAAI;AAAA,MAC5B,WAAWC,GAAE,SAAS,CAAC,EAAE,QAAQ,CAACD,GAAE,QAAQ,OAAO,OAAO;AAAA,IAC5D,EAAE;AACF,aAASM,GAAEH,IAAG;AACZ,YAAMC,KAAID,GAAE,QAAQ,IAAIC,GAAE,QAAQA,GAAE,QAAQG,KAAI,EAAE,QAAQP,GAAE;AAC5D,UAAIO,MAAK,EAAE,QAAQ,KAAK,IAAIH,GAAE,OAAO,EAAE,KAAK,GAAGJ,GAAE,QAAQ,EAAE,QAAQ,MAAMA,GAAE,QAAQ,KAAK,IAAII,GAAE,QAAQJ,GAAE,KAAK,GAAG,EAAE,QAAQA,GAAE,QAAQ,IAAID,GAAE,QAAQ;AAAA,IACpJ;AACA,aAASS,GAAEL,KAAI,QAAQC,KAAI,OAAI;AAC7B,YAAM,EAAE,YAAY,GAAG,WAAWG,IAAG,SAASE,GAAE,IAAIf,IAAG,IAAIS,KAAIA,GAAE,UAAU,EAAE,QAAQ,IAAIC;AACzF,OAAC,KAAKG,MAAKE,QAAO,EAAE,QAAQ,IAAI,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAAA,IAC5D;AACA,cAAE,MAAM;AACN,MAAAC,GAAE;AAAA,IACJ,CAAC,GAAG,MAAE,CAACd,IAAGD,EAAC,GAAG,MAAM;AAClB,eAAE,MAAM;AACN,QAAAe,GAAE;AAAA,MACJ,CAAC;AAAA,IACH,CAAC;AACD,mBAAeA,KAAI;AACjB,MAAAhB,GAAE,cAAcA,GAAE,gBAAgBA,GAAE,gBAAgB,MAAE,QAAQ,aAAS,iBAAAiB,SAAE,IAAI,EAAE,KAAK,IAAIjB,GAAE,YAAY;AAAA,QACpG,QAAQA,GAAE;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,cAAc;AAAA,MAChB,CAAC,IAAI,EAAE,QAAQ,MAAM,cAAAkB,QAAE,SAASlB,GAAE,YAAY,EAAE,MAAM,MAAM,CAAC;AAAA,IAC/D;AACA,WAAOD,GAAE;AAAA,MACP,SAASS;AAAA,MACT,UAAUM;AAAA,IACZ,CAAC,GAAG,CAACL,IAAGC,QAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO,eAAE,EAAE,OAAO,GAAG,MAAE,CAAC,IAAI,MAAEP,EAAC,CAAC,KAAK,CAAC;AAAA,IACxC,GAAG;AAAA,MACD,gBAAE,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO,eAAE,EAAE,KAAK;AAAA,QAChB,SAASO,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAAC,MAAMD,GAAE,OAAO,WAAWK,GAAE,CAAC;AAAA,MACzD,GAAG;AAAA,QACD,gBAAE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO,eAAE,EAAE,cAAc,GAAGR,GAAE,QAAQ,EAAE,KAAK,CAAC;AAAA,QAChD,GAAG;AAAA,UACD,eAAE,gBAAE,OAAO;AAAA,YACT,KAAK,EAAE;AAAA,YACP,QAAQM;AAAA,UACV,GAAG,MAAM,IAAI,EAAE,GAAG;AAAA,YAChB,CAAC,OAAGP,GAAE,KAAK;AAAA,UACb,CAAC;AAAA,UACD,eAAE,gBAAE,OAAO,EAAE,KAAK,EAAE,YAAY,GAAG,MAAM,GAAG,EAAE,GAAG;AAAA,YAC/C,CAAC,OAAG,CAACA,GAAE,KAAK;AAAA,UACd,CAAC;AAAA,QACH,GAAG,CAAC;AAAA,QACJ,MAAEH,EAAC,KAAK,EAAE,aAAa,EAAE,WAAW,UAAE,GAAG,mBAAE,OAAO;AAAA,UAChD,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO,eAAE,EAAE,cAAc,GAAGI,GAAE,QAAQ,EAAE,KAAK,CAAC;AAAA,QAChD,GAAG;AAAA,UACD,gBAAE,OAAO;AAAA,YACP,OAAO;AAAA,YACP,OAAO,eAAE,EAAE,KAAK;AAAA,UAClB,GAAG;AAAA,YACD,MAAEJ,EAAC,KAAK,CAACK,GAAE,SAAS,UAAE,GAAG,mBAAE,OAAO;AAAA,cAChC,KAAK;AAAA,cACL,IAAI,EAAE;AAAA,cACN,OAAO;AAAA,YACT,GAAG,MAAM,GAAG,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,YAC3B,MAAEL,EAAC,KAAKK,GAAE,SAAS,UAAE,GAAG,mBAAE,OAAO;AAAA,cAC/B,KAAK;AAAA,cACL,OAAO;AAAA,cACP,WAAW,EAAE;AAAA,YACf,GAAG,MAAM,GAAG,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,UAC7B,GAAG,CAAC;AAAA,UACJ,MAAEL,EAAC,KAAK,CAAC,EAAE,SAAS,UAAE,GAAG,mBAAE,OAAO;AAAA,YAChC,KAAK;AAAA,YACL,OAAO;AAAA,YACP,OAAO,eAAES,GAAE,KAAK;AAAA,UAClB,GAAG,gBAAE,MAAET,EAAC,CAAC,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UAC1B,EAAE,aAAa,EAAE,WAAW,UAAE,GAAG,mBAAE,OAAO;AAAA,YACxC,KAAK;AAAA,YACL,OAAO;AAAA,YACP,OAAO,eAAE,EAAE,KAAK;AAAA,UAClB,GAAG;AAAA,YACD,EAAE,aAAa,UAAE,GAAG,mBAAE,OAAO,IAAI;AAAA,cAC/B,gBAAE,QAAQ,IAAI,gBAAE,MAAE,CAAC,EAAE,WAAW,CAAC,GAAG,CAAC;AAAA,cACrC,gBAAE,QAAQ,IAAI,gBAAE,MAAE,wBAAAiB,cAAC,EAAE,EAAE,SAAS,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AAAA,YACpD,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YACd,EAAE,WAAW,UAAE,GAAG,mBAAE,OAAO;AAAA,cACzB,KAAK;AAAA,cACL,OAAO,eAAE,CAAC,oBAAoB,EAAE,SAAS,MAAE,wBAAAA,cAAC,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;AAAA,YACxE,GAAG;AAAA,cACD,gBAAE,QAAQ,IAAI,gBAAE,MAAE,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC;AAAA,cACvC,gBAAE,QAAQ,IAAI,gBAAE,MAAE,wBAAAA,cAAC,EAAE,EAAE,OAAO,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AAAA,YAClD,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UACnB,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACnB,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACnB,GAAG,CAAC;AAAA,IACN,GAAG,CAAC;AAAA,EACN;AACF,CAAC;;;AC1KD,IAAM,IAAI;AAAV,IAAe,IAAoB,gBAAE;AAAA,EACnC,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,UAAU;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS,OAAO;AAAA,QACd,KAAK;AAAA,QACL,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,kBAAkB;AAAA,EAC1B,MAAMC,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAM,IAAID,IAAG,EAAE,WAAWE,IAAG,YAAY,EAAE,IAAI,OAAE,CAAC,GAAGC,KAAI,WAAW,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,cAAc,CAAC,KAAK,MAAM,IAAI,IAAE,MAAM,GAAGC,KAAI,IAAE,MAAM,GAAGC,KAAI,IAAE,MAAM,GAAGC,KAAI,IAAE,MAAM,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,KAAE,GAAGC,KAAIN;AAChP,cAAE,MAAM;AACN,UAAI;AACJ,MAAAK,GAAE,SAAS,IAAID,GAAE,UAAU,OAAO,SAAS,EAAE,sBAAsB,GAAG,WAAW,MAAM;AACrF,UAAE,QAAQ,MAAI,WAAW,MAAM;AAC7B,YAAE,QAAQ,MAAID,GAAE,SAASA,GAAE,MAAM,WAAWA,GAAE,MAAM,SAAS;AAAA,QAC/D,GAAG,CAAC;AAAA,MACN,GAAG,CAAC;AAAA,IACN,CAAC;AACD,UAAMI,KAAI,SAAE,MAAM;AAChB,UAAI,GAAGC,IAAG,GAAG;AACb,aAAO;AAAA,QACL,OAAO,IAAIP,GAAE,UAAU,OAAO,SAAS,EAAE,UAAUO,KAAIH,GAAE,UAAU,OAAO,SAASG,GAAE,QAAQ;AAAA,QAC7F,QAAQ,IAAIP,GAAE,UAAU,OAAO,SAAS,EAAE,WAAW,IAAII,GAAE,UAAU,OAAO,SAAS,EAAE,SAAS;AAAA,MAClG;AAAA,IACF,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,UAAI,GAAGG;AACP,aAAO;AAAA,QACL,YAAY,aAAa,CAAC;AAAA,QAC1B,KAAK,IAAI,IAAID,GAAE,UAAU,OAAO,SAAS,EAAE,GAAG;AAAA,QAC9C,MAAM,IAAIC,KAAID,GAAE,UAAU,OAAO,SAASC,GAAE,IAAI;AAAA,MAClD;AAAA,IACF,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,UAAI,GAAGA,IAAG,GAAG;AACb,aAAO,qBAAqB,IAAIH,GAAE,UAAU,OAAO,SAAS,EAAE,UAAU,KAAK,CAAC,eAAeG,KAAID,GAAE,UAAU,OAAO,SAASC,GAAE,IAAI,gBAAgB,IAAIH,GAAE,UAAU,OAAO,SAAS,EAAE,WAAW,KAAK,CAAC,eAAe,IAAIE,GAAE,UAAU,OAAO,SAAS,EAAE,GAAG,cAAcL,KAAI,EAAE,KAAK;AAAA,IACrR,CAAC;AACD,aAASO,KAAI;AACX,MAAAN,GAAE,SAAS,CAACA,GAAE,MAAM,WAAWA,GAAE,MAAM,SAAS,GAAG,WAAW,MAAM;AAClE,QAAAO,GAAE;AAAA,MACJ,GAAG,GAAG,KAAKA,GAAE;AAAA,IACf;AACA,aAASA,KAAI;AACX,QAAE,QAAQ,OAAI,EAAE,QAAQ,OAAI,WAAW,MAAM;AAC3C,QAAAJ,GAAE,kBAAkB;AAAA,MACtB,GAAG,CAAC;AAAA,IACN;AACA,aAASK,KAAI;AACX,MAAAR,GAAE,SAASA,GAAE,MAAM,SAAS;AAAA,IAC9B;AACA,WAAO,CAAC,GAAGK,OAAM;AACf,UAAI,GAAG,GAAG,GAAGI,IAAG,GAAG,GAAGC;AACtB,aAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,QACnB,SAAS;AAAA,QACT,KAAKT;AAAA,QACL,OAAO,eAAE,CAAC,UAAU,uBAAuB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAAA,QAChE,SAASK;AAAA,MACX,GAAG;AAAA,QACD,YAAE,IAAG;AAAA,UACH,SAAS;AAAA,UACT,KAAK;AAAA,UACL,OAAO,eAAE,CAAC,cAAc,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAAA,UAC1C,OAAO,MAAE,CAAC;AAAA,UACV,QAAQ,IAAIV,GAAE,aAAa,OAAO,SAAS,EAAE;AAAA,UAC7C,OAAO,eAAE,EAAE,GAAG,EAAE,OAAO,WAAW,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC;AAAA,QAC5D,GAAG,MAAM,GAAG,CAAC,SAAS,SAAS,SAAS,OAAO,CAAC;AAAA,QAChD,YAAE,IAAG;AAAA,UACH,SAAS;AAAA,UACT,KAAKI;AAAA,UACL,OAAO,eAAE,CAAC,eAAe,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AAAA,UAC5C,QAAQ,IAAIJ,GAAE,aAAa,OAAO,SAAS,EAAE;AAAA,UAC7C,aAAa,IAAIA,GAAE,aAAa,OAAO,SAAS,EAAE;AAAA,UAClD,cAAca,KAAIb,GAAE,aAAa,OAAO,SAASa,GAAE;AAAA,UACnD,kBAAkB,IAAIb,GAAE,aAAa,OAAO,SAAS,EAAE;AAAA,UACvD,YAAY,IAAIA,GAAE,aAAa,OAAO,SAAS,EAAE;AAAA,UACjD,UAAUc,KAAId,GAAE,aAAa,OAAO,SAASc,GAAE;AAAA,UAC/C,SAAS,cAAEF,IAAG,CAAC,MAAM,CAAC;AAAA,QACxB,GAAG,MAAM,GAAG,CAAC,SAAS,SAAS,cAAc,eAAe,mBAAmB,aAAa,SAAS,CAAC;AAAA,MACxG,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AACF,CAAC;;;AC3FD,IAAM,IAAI,CAAC,MAAM,IAAI;AAArB,IAAwB,IAAI,CAAC,cAAc;AAA3C,IAA8C,KAAK,CAAC,eAAe,eAAe;AAAlF,IAAqF,KAAK,CAAC,MAAM,IAAI;AAArG,IAAwG,KAAK,CAAC,cAAc;AAA5H,IAA+H,KAAK,CAAC,cAAc,cAAc;AAAjK,IAAoK,KAAK;AAAA,EACvK,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,KAAK;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AACT;AANA,IAMG,KAAqB,gBAAE;AAAA,EACxB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA;AAAA,MAEN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,IACR;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,GAAG,EAAE,QAAQ,EAAE,GAAG;AACtB,UAAMG,KAAI,GAAGC,KAAIC,GAAE,MAAM,GAAGC,KAAID,GAAE,OAAO,GAAGE,KAAIF,GAAE,OAAO,GAAG,IAAI,IAAE,IAAI,GAAGG,KAAI,IAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,UAAU,WAAW,WAAW,OAAO,EAAE,QAAQ,CAAC,MAAM,KAAK,0BAA0B,CAAC,MAAM,GAAGC,KAAI,EAAE,OAAO,GAAG,YAAY,WAAW,WAAW,WAAW,GAAGN,GAAE,SAAS,GAAG,IAAI,EAAE,OAAO,OAAI,UAAU,IAAI,YAAY,GAAG,MAAM,IAAI,SAAS,KAAK,OAAO,WAAW,GAAGA,GAAE,OAAO,GAAGO,KAAI,EAAE,OAAO,MAAI,UAAU,GAAG,YAAY,GAAG,MAAM,GAAG,SAAS,KAAK,OAAO,WAAW,GAAGP,GAAE,SAAS,GAAGQ,KAAI,MAAM;AAC/e,UAAI,KAAKR,GAAE,OAAOA,GAAE,iBAAiB;AACrC,aAAOA,GAAE,cAAcA,GAAE,kBAAkB,MAAMA,GAAE,cAAcA,GAAE,iBAAiB,MAAM;AAAA,IAC5F,GAAGS,KAAI,MAAM;AACX,UAAI,KAAKT,GAAE,OAAOA,GAAE,eAAe;AACnC,aAAOA,GAAE,gBAAgBA,GAAE,gBAAgB,MAAMA,GAAE,gBAAgBA,GAAE,eAAe,MAAM;AAAA,IAC5F,GAAG,IAAI,IAAI,KAAK,KAAKS,GAAE,GAAGC,KAAI,IAAE,IAAI,KAAK,KAAKD,GAAE,CAAC,GAAG,IAAI;AAAA,MACtD,OAAO;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,MACP,SAAS,GAAGT,GAAE,OAAO,CAAC,IAAIA,GAAE,OAAO,CAAC,IAAIA,GAAE,IAAI,IAAIA,GAAE,IAAI;AAAA,IAC1D,GAAGW,KAAI;AAAA,MACL,IAAIX,GAAE;AAAA,MACN,IAAIA,GAAE;AAAA,MACN,GAAGQ,GAAE;AAAA,MACL,QAAQ,EAAER,GAAE,UAAU;AAAA,MACtB,gBAAgBA,GAAE;AAAA,MAClB,MAAMA,GAAE;AAAA,MACR,GAAGA,GAAE,cAAc,EAAE,QAAQ,QAAQI,EAAC,IAAI;AAAA,IAC5C,GAAGQ,KAAI,SAAE,OAAO;AAAA,MACd,IAAIZ,GAAE;AAAA,MACN,IAAIA,GAAE;AAAA,MACN,GAAGS,GAAE;AAAA,MACL,MAAM;AAAA,MACN,gBAAgBT,GAAE;AAAA,MAClB,oBAAoB;AAAA,MACpB,qBAAqBU,GAAE;AAAA,MACvB,kBAAkBV,GAAE;AAAA,MACpB,QAAQA,GAAE,aAAa,QAAQC,EAAC,MAAM,EAAED,GAAE,SAAS;AAAA,MACnD,GAAGA,GAAE,YAAY,EAAE,QAAQ,QAAQG,EAAC,IAAI;AAAA,MACxC,GAAGH,GAAE,cAAc;AAAA,QACjB,OAAO,EAAE,YAAY,qBAAqBA,GAAE,UAAU,KAAK;AAAA,MAC7D;AAAA,IACF,EAAE,GAAG,IAAI;AAAA,MACP,IAAIC;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,mBAAmB,UAAUK,GAAE,KAAK;AAAA,IACtC,GAAG,IAAI;AAAA,MACL,QAAQ;AAAA,MACR,cAAcA,GAAE;AAAA,IAClB,GAAG,IAAI;AAAA,MACL,QAAQ;AAAA,MACR,cAAcA,GAAE;AAAA,IAClB,GAAGO,KAAI;AAAA,MACL,IAAIV;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAGW,KAAI;AAAA,MACL,IAAIV;AAAA,MACJ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG,IAAI;AAAA,MACL,IAAI,EAAE,WAAW;AAAA,MACjB,IAAI,EAAE;AAAA,MACN,cAAc,EAAE;AAAA,MAChB,YAAY,EAAE;AAAA,MACd,cAAc,EAAE;AAAA,IAClB,GAAG,IAAI;AAAA,MACL,IAAIG,GAAE,WAAW;AAAA,MACjB,IAAIA,GAAE;AAAA,MACN,cAAcA,GAAE;AAAA,MAChB,YAAYA,GAAE;AAAA,MACd,cAAcA,GAAE;AAAA,IAClB;AACA,cAAE,MAAM;AACN,MAAAQ,GAAE;AAAA,IACJ,CAAC,GAAG,gBAAE,MAAM;AACV,aAAO,oBAAoB,UAAUA,EAAC;AAAA,IACxC,CAAC,GAAG;AAAA,MACF,MAAMf,GAAE;AAAA,MACR,MAAM;AACJ,QAAAe,GAAE;AAAA,MACJ;AAAA,IACF;AACA,aAASC,GAAE,GAAG;AACZ,UAAI,MAAM,KAAM;AAChB,YAAM,IAAI,EAAE,sBAAsB;AAClC,aAAO,EAAE,OAAO,KAAK,EAAE,QAAQ,KAAK,EAAE,WAAW,OAAO,eAAe,SAAS,gBAAgB,iBAAiB,EAAE,UAAU,OAAO,cAAc,SAAS,gBAAgB;AAAA,IAC7K;AACA,aAAS,IAAI;AACX,YAAM,IAAI,IAAI,KAAK,KAAKP,GAAE;AAC1B,MAAAC,GAAE,QAAQ,IAAI,IAAIV,GAAE,UAAU;AAC9B,YAAM,IAAI,KAAK,MAAM,MAAM,MAAM,IAAIU,GAAE,KAAK;AAC5C,MAAAO,GAAE,CAAC;AAAA,IACL;AACA,aAASA,GAAE,GAAG;AACZ,YAAM,IAAI,IAAIZ,GAAE;AAChB,UAAI,GAAG;AACL,cAAMa,KAAIlB,GAAE,aAAa,KAAK,IAAI,CAAC,GAAGmB,KAAI,YAAY,MAAM;AAC1D,cAAI,KAAKd,GAAE,SAAS,GAAGA,GAAE,SAAS,KAAK,cAAcc,EAAC,MAAMd,GAAE,SAAS,GAAGA,GAAE,SAAS,KAAK,cAAcc,EAAC;AAAA,QAC3G,GAAGD,EAAC;AAAA,MACN;AAAA,IACF;AACA,aAASH,KAAI;AACX,MAAAf,GAAE,YAAY,OAAO,iBAAiB,UAAUe,EAAC,GAAG,EAAE,SAASC,GAAE,EAAE,KAAK,MAAM,OAAO,oBAAoB,UAAUD,EAAC,GAAGf,GAAE,YAAY,EAAE,GAAGA,GAAE,cAAc,OAAOA,GAAE,cAAc,cAAcA,GAAE,WAAW,MAAM,EAAE;AAAA,IACtN;AACA,aAASE,GAAE,IAAI,IAAI,IAAI,IAAI;AACzB,aAAO,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC,IAAI;AAAA,IACvG;AACA,WAAO,EAAE;AAAA,MACP,eAAe;AAAA,IACjB,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,SAAS;AAAA,MACT,KAAK;AAAA,MACL,OAAO,eAAE,EAAE,QAAQ,EAAE,OAAO,MAAM,OAAOF,GAAE,OAAO,KAAK,CAAC;AAAA,MACxD,OAAO;AAAA,IACT,GAAG;AAAA,OACA,UAAE,GAAG,mBAAE,OAAO,eAAE,mBAAE,CAAC,CAAC,GAAG;AAAA,QACtB,EAAE,cAAc,UAAE,GAAG,mBAAE,kBAAkB,eAAE,WAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG;AAAA,UAC5D,gBAAE,QAAQ,eAAE,mBAAE,CAAC,CAAC,GAAG,MAAM,EAAE;AAAA,UAC3B,gBAAE,QAAQ,eAAE,mBAAE,CAAC,CAAC,GAAG,MAAM,EAAE;AAAA,QAC7B,GAAG,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,QAClB,gBAAE,UAAU,WAAEW,IAAG,EAAE,OAAO,oBAAoB,CAAC,GAAG,MAAM,EAAE;AAAA,QAC1D,gBAAE,UAAU,WAAEC,GAAE,OAAO,EAAE,OAAO,mBAAmB,CAAC,GAAG,MAAM,EAAE;AAAA,QAC/D,EAAE,YAAY,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,UAClC,EAAE,UAAU,SAAM,UAAE,GAAG,mBAAE,UAAU,eAAE,WAAE,EAAE,KAAK,EAAE,GAAGC,EAAC,CAAC,GAAG;AAAA,YACtD,gBAAE,gBAAgB,eAAE,mBAAE,CAAC,CAAC,GAAG,MAAM,EAAE;AAAA,UACrC,GAAG,EAAE,MAAM,UAAE,GAAG,mBAAE,UAAU,eAAE,WAAE,EAAE,KAAK,EAAE,GAAGA,EAAC,CAAC,GAAG;AAAA,YAC/C,gBAAE,YAAY;AAAA,cACZ,IAAI,EAAE;AAAA,cACN,IAAI,EAAE;AAAA,YACR,GAAG,MAAM,GAAG,CAAC;AAAA,YACb,gBAAE,kBAAkB;AAAA,cAClB,cAAc,EAAE;AAAA,YAClB,GAAG,MAAM,GAAG,CAAC;AAAA,YACb,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,eAAe;AAAA,cAC/B,UAAU;AAAA,cACV,IAAI;AAAA,cACJ,QAAQ;AAAA,YACV,GAAG,MAAM,EAAE;AAAA,YACX,gBAAE,WAAW;AAAA,cACX,eAAe,EAAE;AAAA,cACjB,iBAAiB,EAAE;AAAA,cACnB,QAAQ;AAAA,YACV,GAAG,MAAM,GAAG,EAAE;AAAA,YACd,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,eAAe;AAAA,cAC/B,UAAU;AAAA,cACV,IAAI;AAAA,cACJ,KAAK;AAAA,cACL,QAAQ;AAAA,YACV,GAAG,MAAM,EAAE;AAAA,YACX,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,eAAe;AAAA,cAC/B,UAAU;AAAA,cACV,IAAI;AAAA,cACJ,KAAK;AAAA,YACP,GAAG,MAAM,EAAE;AAAA,UACb,GAAG,EAAE;AAAA,QACP,GAAG,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,QAClBN,MAAK,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,UACzBA,GAAE,UAAU,SAAM,UAAE,GAAG,mBAAE,UAAU,eAAE,WAAE,EAAE,KAAK,EAAE,GAAGO,EAAC,CAAC,GAAG;AAAA,YACtD,gBAAE,gBAAgB,eAAE,mBAAE,CAAC,CAAC,GAAG,MAAM,EAAE;AAAA,UACrC,GAAG,EAAE,MAAM,UAAE,GAAG,mBAAE,UAAU,eAAE,WAAE,EAAE,KAAK,EAAE,GAAGA,EAAC,CAAC,GAAG;AAAA,YAC/C,gBAAE,YAAY;AAAA,cACZ,IAAI,EAAE;AAAA,cACN,IAAI,EAAE;AAAA,YACR,GAAG,MAAM,GAAG,EAAE;AAAA,YACd,gBAAE,kBAAkB;AAAA,cAClB,cAAc,EAAE;AAAA,YAClB,GAAG,MAAM,GAAG,EAAE;AAAA,YACd,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,eAAe;AAAA,cAC/B,UAAU;AAAA,cACV,IAAI;AAAA,cACJ,QAAQ;AAAA,YACV,GAAG,MAAM,EAAE;AAAA,YACX,gBAAE,WAAW;AAAA,cACX,YAAY,EAAE;AAAA,cACd,cAAc,EAAE;AAAA,cAChB,QAAQ;AAAA,YACV,GAAG,MAAM,GAAG,EAAE;AAAA,YACd,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,eAAe;AAAA,cAC/B,UAAU;AAAA,cACV,IAAI;AAAA,cACJ,KAAK;AAAA,cACL,QAAQ;AAAA,YACV,GAAG,MAAM,EAAE;AAAA,YACX,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,gBAAE,eAAe;AAAA,cAC/B,UAAU;AAAA,cACV,IAAI;AAAA,cACJ,KAAK;AAAA,YACP,GAAG,MAAM,EAAE;AAAA,UACb,GAAG,EAAE;AAAA,QACP,GAAG,EAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,MACpB,GAAG,EAAE;AAAA,MACL,EAAE,eAAe,UAAE,GAAG,mBAAE,QAAQ,IAAI,gBAAET,GAAE,KAAK,IAAI,MAAM,gBAAE,EAAE,IAAI,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAChF,EAAE,OAAO,eAAe,UAAE,GAAG,mBAAE,OAAO,IAAI;AAAA,QACxC,WAAE,EAAE,QAAQ,eAAe,CAAC,GAAG,QAAQ,IAAE;AAAA,MAC3C,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,IAChB,GAAG,CAAC;AAAA,EACN;AACF,CAAC;AA1RD,IA0RI,KAAK,CAAC,GAAG,MAAM;AACjB,QAAML,KAAI,EAAE,aAAa;AACzB,aAAW,CAACC,IAAGE,EAAC,KAAK;AACnB,IAAAH,GAAEC,EAAC,IAAIE;AACT,SAAOH;AACT;AA/RA,IA+RG,KAAqB,GAAG,IAAI,CAAC,CAAC,aAAa,iBAAiB,CAAC,CAAC;;;AC9RjE,IAAMoB,KAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,IACR;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,YAAY;AAAA,EACpB,MAAMC,IAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAMC,KAAID,IAAG,EAAE,WAAW,GAAG,kBAAkBE,IAAG,gBAAgBC,IAAG,YAAY,GAAG,WAAWC,IAAG,WAAW,EAAE,IAAI,OAAEH,EAAC,GAAG,IAAI,IAAE,IAAI,GAAGI,KAAI,IAAE,IAAI,GAAGC,KAAI,IAAE,IAAI,GAAG,IAAI,IAAE,CAAC,GAAG,IAAI,IAAE,CAAC,GAAGC,KAAI,IAAE,KAAE,GAAGC,KAAI,IAAE,KAAE,GAAG,IAAI;AAC9M,cAAE,MAAM;AACN,MAAAD,GAAE,QAAQ;AAAA,IACZ,CAAC,GAAG,YAAE,MAAM;AACV,MAAAA,GAAE,QAAQ;AAAA,IACZ,CAAC;AACD,aAASE,GAAE,GAAG;AACZ,UAAI,CAACJ,GAAE,SAAS,CAAC,EAAE,MAAO;AAC1B,YAAM,EAAE,WAAW,EAAE,IAAIA,GAAE,OAAO,IAAI,EAAE,WAAWC,GAAE,QAAQ,IAAI;AACjE,QAAE,QAAQ,WAAW,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,UAAU,GAAG,EAAE,QAAQ,EAAE;AAAA,IAC7E;AACA,aAAS,EAAE,GAAG;AACZ,UAAI,EAAE,QAAQ,WAAW,KAAK,EAAE,OAAO;AACrC,UAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE;AACvB,cAAM,IAAI,EAAE,QAAQ,EAAE;AACtB,UAAE,SAAS,IAAI,MAAM,EAAE,MAAM,MAAM,YAAY,cAAc,CAAC;AAAA,MAChE;AAAA,IACF;AACA,aAAS,IAAI;AACX,UAAI,CAACD,GAAE,SAAS,CAAC,EAAE,MAAO;AAC1B,YAAM,IAAIA,GAAE,MAAM,eAAeD,GAAE;AACnC,QAAE,QAAQ,EAAE,QAAQ,IAAIM,GAAE,IAAI,EAAE,UAAU,EAAE,MAAM,MAAM,YAAY;AAAA,IACtE;AACA,aAASA,KAAI;AACX,MAAAF,GAAE,QAAQ;AAAA,IACZ;AACA,aAASG,KAAI;AACX,MAAAH,GAAE,QAAQ;AAAA,IACZ;AACA,aAASI,KAAI;AAAA,IACb;AACA,aAAS,IAAI;AACX,MAAAL,GAAE,QAAQ;AAAA,IACZ;AACA,aAASM,KAAI;AACX,QAAE,YAAY;AAAA,IAChB;AACA,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,YAAG;AAAA,MAC1B,MAAM,MAAEX,EAAC,KAAK;AAAA,MACd,SAASS;AAAA,MACT,SAASE;AAAA,IACX,GAAG;AAAA,MACD,SAAS,QAAE,MAAM;AAAA,QACfN,GAAE,SAAS,UAAE,GAAG,mBAAE,OAAO;AAAA,UACvB,KAAK;AAAA,UACL,OAAO;AAAA,UACP,SAAS,cAAEG,IAAG,CAAC,MAAM,CAAC;AAAA,QACxB,GAAG;AAAA,UACD,YAAE,YAAG;AAAA,YACH,MAAM,MAAEP,EAAC,KAAK;AAAA,YACd,SAASS;AAAA,YACT,SAAS;AAAA,UACX,GAAG;AAAA,YACD,SAAS,QAAE,MAAM;AAAA,cACfJ,GAAE,SAAS,UAAE,GAAG,mBAAE,OAAO;AAAA,gBACvB,KAAK;AAAA,gBACL,SAAS;AAAA,gBACT,KAAK;AAAA,gBACL,OAAO;AAAA,gBACP,qBAAqBC;AAAA,gBACrB,oBAAoB;AAAA,gBACpB,YAAY,cAAE,GAAG,CAAC,MAAM,CAAC;AAAA,cAC3B,GAAG;AAAA,gBACD,MAAE,CAAC,KAAK,UAAE,GAAG,mBAAE,OAAO;AAAA,kBACpB,KAAK;AAAA,kBACL,SAAS;AAAA,kBACT,KAAKH;AAAA,kBACL,OAAO;AAAA,gBACT,GAAG,MAAM,GAAG,KAAK,mBAAE,IAAI,IAAE;AAAA,gBACzB,MAAE,CAAC,KAAK,UAAE,GAAG,YAAE,GAAG;AAAA,kBAChB,KAAK;AAAA,kBACL,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,MAAM,EAAE,MAAM,QAAQ;AAAA,kBACtB,SAASI;AAAA,gBACX,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,gBACd,gBAAE,OAAO;AAAA,kBACP,OAAO;AAAA,kBACP,SAAS;AAAA,kBACT,KAAKL;AAAA,gBACP,GAAG;AAAA,kBACD,WAAE,EAAE,QAAQ,WAAW,EAAE,cAAcK,GAAE,CAAC;AAAA,gBAC5C,GAAG,GAAG;AAAA,cACR,GAAG,GAAG,KAAK,mBAAE,IAAI,IAAE;AAAA,YACrB,CAAC;AAAA,YACD,GAAG;AAAA,UACL,GAAG,GAAG,CAAC,MAAM,CAAC;AAAA,QAChB,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAChB,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG,GAAG,CAAC,MAAM,CAAC;AAAA,EAChB;AACF,CAAC;;;ACxHD,IAAAI,0BAAqC;AACrC,IAAM,KAAK;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGGC,MAAK,EAAE,OAAO,sBAAsB;AAHvC,IAG0C,KAAK,EAAE,OAAO,SAAS;AAHjE,IAGoEC,MAAK,EAAE,OAAO,WAAW;AAH7F,IAGgG,KAAK,EAAE,OAAO,SAAS;AAHvH,IAG0H,KAAK;AAAA,EAC7H,KAAK;AAAA,EACL,OAAO;AACT;AANA,IAMGC,MAAK,EAAE,OAAO,SAAS;AAN1B,IAM6B,KAAK,EAAE,OAAO,qBAAqB;AANhE,IAMmE,KAAK;AAAA,EACtE,KAAK;AAAA,EACL,OAAO;AACT;AATA,IASG,KAAK,EAAE,OAAO,aAAa;AAT9B,IASiC,KAAK,EAAE,OAAO,YAAY;AAT3D,IAS8D,KAAK;AAAA,EACjE,KAAK;AAAA,EACL,OAAO;AACT;AAZA,IAYG,KAAK,EAAE,OAAO,aAAa;AAZ9B,IAYiCC,MAAK,EAAE,OAAO,YAAY;AAZ3D,IAY8D,KAAK;AAAA,EACjE,KAAK;AAAA,EACL,OAAO;AACT;AAfA,IAeG,KAAK,EAAE,OAAO,aAAa;AAf9B,IAeiCC,MAAK,EAAE,OAAO,YAAY;AAf3D,IAe8D,KAAK;AAAA,EACjE,KAAK;AAAA,EACL,OAAO;AACT;AAlBA,IAkBG,KAAK,EAAE,OAAO,kBAAkB;AAlBnC,IAkBsC,KAAK,EAAE,OAAO,YAAY;AAlBhE,IAkBmE,KAAK,EAAE,OAAO,SAAS;AAlB1F,IAkB6F,KAAK,EAAE,OAAO,qBAAqB;AAlBhI,IAkBmI,KAAqB,gBAAE;AAAA,EACxJ,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,oBAAoB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,WAAW,SAASC,IAAG;AACrB,eAAO,IAAI,KAAKA,EAAC,EAAE,SAAS,MAAM;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,WAAW,SAASA,IAAG;AACrB,eAAO,IAAI,KAAKA,EAAC,EAAE,SAAS,MAAM;AAAA,MACpC;AAAA,IACF;AAAA,IACA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS,IAAI,KAAK,KAAK,KAAK;AAAA;AAAA,IAE9B;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW,SAASA,IAAG;AACrB,eAAO,CAAC,CAAC;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE,KAAK,CAAC,MAAM,MAAMA,EAAC;AAAA,MACvB;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,cAAc;AAAA,EACtB,MAAMA,IAAG,EAAE,QAAQC,IAAG,MAAM,EAAE,GAAG;AAC/B,UAAM,EAAE,GAAG,EAAE,IAAI,QAAE,GAAGC,KAAIF,IAAG,EAAE,oBAAoBG,IAAG,YAAYC,IAAG,cAAcC,IAAG,eAAe,GAAG,aAAa,GAAG,uBAAuB,EAAE,IAAI,OAAEH,EAAC,GAAGI,KAAI,IAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,IAAI,KAAKD,GAAE,KAAK,EAAE,QAAQ,KAAKD,MAAK,QAAQA,GAAE,QAAQ,IAAI,KAAKA,MAAK,OAAO,SAASA,GAAE,KAAK,EAAE,QAAQ,KAAqB,oBAAI,KAAK,GAAG,QAAQ,EAAE,GAAGG,KAAI,IAAE,CAAC,GAAG,IAAI,SAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,GAAG,IAAI,SAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,OAAO,EAAE,KAAK,GAAG,IAAI,SAAE,MAAM,EAAE,QAAQ,GAAG,GAAGC,KAAI;AAChd,cAAE,MAAM;AACN,QAAE,SAASA,GAAE,cAAc,GAAG,EAAE,SAASC,GAAE,IAAE,KAAKH,GAAE,QAAQ,YAAY,GAAG,GAAG;AAAA,IAChF,CAAC,GAAG,gBAAE,MAAM;AACV,MAAAA,GAAE,SAAS,cAAcA,GAAE,KAAK;AAAA,IAClC,CAAC,GAAG,MAAED,IAAG,CAAC,MAAM;AACd,YAAM,EAAE,QAAQ,IAAI,KAAKA,GAAE,KAAK,EAAE,QAAQ,KAAKD,MAAK,QAAQA,GAAE,QAAQ,IAAI,KAAKA,MAAK,OAAO,SAASA,GAAE,KAAK,EAAE,QAAQ,KAAqB,oBAAI,KAAK,GAAG,QAAQ,IAAI,EAAE,QAAQ,KAAK,EAAE,SAASK,GAAE,KAAE;AAAA,IAClM,CAAC;AACD,UAAMC,KAAI,SAAE,MAAM;AAChB,YAAM,IAAI,EAAE,QAAQH,GAAE,QAAQ,EAAE;AAChC,UAAI,IAAI,EAAG,QAAO,CAAC;AACnB,YAAMI,KAAI;AAAA,QACR,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,GAAG;AAAA,QACvC,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG;AAAA,QACzD,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,OAAO,MAAM,GAAG;AAAA,QAC/C,IAAI,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG;AAAA,MACrC,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAIC,KAAI,OAAO,KAAKD,EAAC,EAAE,UAAU,CAAC,MAAMA,GAAE,CAAC,MAAM,CAAC,GAAGE,KAAID,OAAM,KAAK,OAAO,KAAKD,EAAC,EAAE,SAAS,IAAIC,IAAG,IAAI,EAAE,MAAM,WAAW,MAAM,GAAGE,KAAI,IAAI,EAAE,MAAM,MAAM,GAAG,EAAE,OAAO,CAAC,MAAM,MAAM,MAAM,EAAE,QAAQC,KAAI,CAAC,IAAI,IAAI,IAAI,EAAE,GAAGC,KAAI,MAAMF,EAAC,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,GAAGG,IAAGC,OAAM;AACpS,cAAMC,KAAI,OAAO,KAAKR,EAAC,EAAEO,EAAC;AAC1B,eAAO,KAAKH,GAAEG,EAAC,IAAIP,GAAEQ,EAAC,GAAG;AAAA,MAC3B,GAAG,CAAC;AACJ,aAAO,OAAO,KAAKR,EAAC,EAAE,OAAO,CAAC,GAAGM,IAAGC,OAAM;AACxC,cAAMC,KAAIR,GAAEM,EAAC;AACb,YAAI,KAAKJ,KAAIC,IAAG;AACd,gBAAMM,KAAID,OAAM,KAAKD,KAAIL;AACzB,YAAEI,EAAC,IAAIG,KAAI,KAAK,EAAED,EAAC;AAAA,QACrB;AACE,YAAEF,EAAC,IAAIC,KAAIJ,KAAI,KAAK,EAAEI,OAAMJ,KAAIK,KAAIH,KAAIG,EAAC;AAC3C,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP,CAAC;AACD,aAASV,GAAE,GAAG;AACZ,YAAME,KAAI,KAAK,IAAI,EAAE,KAAK,IAAI,MAAM,MAAM;AAC1C,QAAE,QAAQ,GAAGJ,GAAE,QAAQ,IAAI,KAAK,IAAI,EAAE,KAAK,IAAII,KAAI,GAAGL,GAAE,SAAS,cAAcA,GAAE,KAAK,GAAGA,GAAE,QAAQ,YAAY,IAAI,IAAI,GAAG,GAAG;AAAA,IAC/H;AACA,aAAS,IAAI;AACX,QAAE,QAAQ,OAAO,cAAcA,GAAE,KAAK,GAAGE,GAAE,cAAc,GAAG,EAAE,SAASC,GAAE,IAAE,KAAK,EAAE,SAAS;AAAA,IAC7F;AACA,aAAS,IAAI;AACX,MAAAF,GAAE,SAAS;AAAA,IACb;AACA,aAASc,KAAI;AACX,MAAAf,GAAE,SAAS,cAAcA,GAAE,KAAK;AAAA,IAClC;AACA,WAAOL,GAAE;AAAA,MACP,WAAWoB;AAAA,IACb,CAAC,GAAG,CAAC,GAAGV,QAAO,UAAE,GAAG,mBAAE,QAAQ;AAAA,MAC5B,OAAO,eAAE,EAAE,0BAA0B,MAAI,OAAO,EAAE,MAAM,CAAC;AAAA,IAC3D,GAAG;AAAA,MACD,CAAC,MAAEP,EAAC,KAAK,MAAED,EAAC,KAAK,UAAE,GAAG,mBAAE,QAAQ,IAAI,gBAAE,MAAE,CAAC,EAAE,iCAAiC,CAAC,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAC9F,EAAE,QAAQ,WAAE,EAAE,QAAQ,eAAe,EAAE,KAAK,EAAE,GAAG,MAAM;AAAA,QACrD,gBAAE,QAAQR,KAAI;AAAA,UACZ,gBAAE,QAAQ,IAAI,gBAAE,MAAE,CAAC,EAAE,yBAAyB,CAAC,GAAG,CAAC;AAAA,UACnD,gBAAE,QAAQC,KAAI,gBAAE,MAAE,wBAAA0B,cAAE,EAAE,MAAEjB,EAAC,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AAAA,UAC5C,gBAAE,QAAQ,IAAI,gBAAE,MAAE,CAAC,EAAE,yBAAyB,CAAC,GAAG,CAAC;AAAA,QACrD,CAAC;AAAA,MACH,CAAC,IAAI,mBAAE,IAAI,IAAE;AAAA,MACb,EAAE,SAAS,UAAE,GAAG,mBAAE,QAAQ,IAAI;AAAA,QAC5B,gBAAE,QAAQR,KAAI,gBAAE,EAAE,QAAQ,MAAE,CAAC,EAAE,8BAA8B,IAAI,MAAE,CAAC,EAAE,gCAAgC,CAAC,GAAG,CAAC;AAAA,QAC3G,gBAAE,QAAQ,IAAI;AAAA,UACZa,GAAE,MAAM,MAAM,UAAE,GAAG,mBAAE,QAAQ,IAAI;AAAA,YAC/B,gBAAE,QAAQ,IAAI,gBAAEA,GAAE,MAAM,EAAE,GAAG,CAAC;AAAA,YAC9B,gBAAE,QAAQ,IAAI,gBAAE,MAAE,CAAC,EAAE,wBAAwB,CAAC,GAAG,CAAC;AAAA,UACpD,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UACdA,GAAE,MAAM,MAAM,UAAE,GAAG,mBAAE,QAAQ,IAAI;AAAA,YAC/B,gBAAE,QAAQ,IAAI,gBAAEA,GAAE,MAAM,EAAE,GAAG,CAAC;AAAA,YAC9B,gBAAE,QAAQZ,KAAI,gBAAE,MAAE,CAAC,EAAE,wBAAwB,CAAC,GAAG,CAAC;AAAA,UACpD,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UACdY,GAAE,MAAM,MAAM,UAAE,GAAG,mBAAE,QAAQ,IAAI;AAAA,YAC/B,gBAAE,QAAQ,IAAI,gBAAEA,GAAE,MAAM,EAAE,GAAG,CAAC;AAAA,YAC9B,gBAAE,QAAQX,KAAI,gBAAE,MAAE,CAAC,EAAE,wBAAwB,CAAC,GAAG,CAAC;AAAA,UACpD,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UACdW,GAAE,MAAM,MAAM,UAAE,GAAG,mBAAE,QAAQ,IAAI;AAAA,YAC/B,gBAAE,QAAQ,IAAI,gBAAEA,GAAE,MAAM,EAAE,GAAG,CAAC;AAAA,YAC9B,gBAAE,QAAQ,IAAI,gBAAE,MAAE,CAAC,EAAE,wBAAwB,CAAC,GAAG,CAAC;AAAA,UACpD,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QAChB,CAAC;AAAA,QACD,gBAAE,QAAQ,IAAI,gBAAE,EAAE,QAAQ,MAAE,CAAC,EAAE,8BAA8B,IAAI,MAAE,CAAC,EAAE,gCAAgC,CAAC,GAAG,CAAC;AAAA,MAC7G,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACd,EAAE,SAAS,CAAC,MAAE,CAAC,IAAI,WAAE,EAAE,QAAQ,cAAc,EAAE,KAAK,EAAE,GAAG,MAAM;AAAA,QAC7D,gBAAE,QAAQ,IAAI,gBAAE,MAAE,CAAC,EAAE,iBAAiB,CAAC,GAAG,CAAC;AAAA,MAC7C,CAAC,IAAI,mBAAE,IAAI,IAAE;AAAA,IACf,GAAG,CAAC;AAAA,EACN;AACF,CAAC;;;ACpJD,IAAMa,MAAK;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGGC,MAAqB,gBAAE;AAAA,EACxB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,aAAa;AAAA,MACX,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,eAAe,cAAc,UAAU;AAAA,EAC/C,MAAMC,IAAG,EAAE,QAAQC,IAAG,MAAMC,GAAE,GAAG;AAC/B,UAAM,IAAIF,IAAG,EAAE,aAAa,GAAG,cAAc,GAAG,WAAWG,IAAG,UAAU,EAAE,IAAI,OAAE,CAAC,GAAGC,KAAI,IAAE,IAAI,GAAGC,KAAI,IAAE,IAAI,GAAGC,KAAI,IAAE,IAAI,GAAGC,KAAI,IAAE,CAAC,GAAG,IAAI,IAAE,CAAC,GAAGC,KAAI,IAAE,CAAC,GAAG,IAAI,IAAE,CAAC,GAAG,IAAI,IAAE,KAAE,GAAGC,KAAI,IAAE,IAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,IAAI,GAAGC,KAAIR,IAAGS,KAAI,IAAE,KAAE,GAAGC,KAAI,SAAE,MAAM,KAAK,QAAQ,EAAE,SAAS,KAAK,QAAQ,EAAE,QAAQ,KAAK,OAAO,SAAS,EAAE,QAAQ,CAAC;AAAA,MAC5T,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,IACR,CAAC,CAAC,GAAGC,KAAI,SAAE,MAAM;AACf,UAAIP,GAAE;AACJ,eAAOA,GAAE,UAAU,UAAU,KAAK,OAAO,SAAS,EAAE,QAAQM,MAAK,OAAO,SAASA,GAAE;AAAA,IACvF,CAAC;AACD,aAAS,EAAE,GAAG;AACZ,QAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,SAASJ,GAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,SAASC,GAAE,QAAQ;AAAA,IACxF;AACA,aAAS,EAAE,GAAG;AACZ,UAAI,CAACL,GAAE,SAAS,CAACK,GAAE,SAAS,EAAE,MAAO;AACrC,QAAE,UAAU,SAAS,qBAAqB,EAAE,KAAK,GAAG,EAAE,QAAQ,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,UAAU,EAAE,QAAQF,GAAE;AAClH,YAAM,IAAI,KAAK,IAAI,EAAE,KAAK,IAAI,KAAK,IAAI,EAAE,QAAQ,CAAC,EAAE,UAAUC,GAAE,KAAK;AACrE,UAAI,CAACM,GAAEV,GAAE,MAAM,sBAAsB,GAAG,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG;AACzF,UAAE;AACF;AAAA,MACF;AACA,MAAAE,GAAE,QAAQ,EAAE,QAAQ,IAAI,UAAU,QAAQG,GAAE,QAAQM,GAAET,GAAE,KAAK,GAAGG,GAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,MAAIC,GAAE,YAAY,IAAI,EAAE,QAAQ,sBAAsB,MAAM;AAC5J,QAAAM,GAAE,EAAE,KAAK;AAAA,MACX,CAAC;AAAA,IACH;AACA,aAASC,KAAI;AACX,UAAI,GAAG,GAAGC;AACV,UAAI,CAACd,GAAE,SAAS,CAAC,EAAE,SAAS,EAAE,MAAO;AACrC,UAAI,EAAE,UAAU,SAAS,qBAAqB,EAAE,KAAK,GAAG,EAAE,QAAQ,OAAO,EAAE,QAAQ,OAAIK,GAAE,QAAQ,OAAIN,GAAE,WAAW,IAAIU,GAAE,UAAU,OAAO,SAAS,EAAE,YAAY,KAAK,KAAK,IAAI,EAAE,KAAK,IAAIT,GAAE,MAAM,cAAcD,GAAE;AAC/M,YAAI,IAAIU,GAAE,UAAU,OAAO,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE;AAAA,WAChD;AACH,cAAMM,OAAMD,KAAIb,GAAE,UAAU,OAAO,SAASa,GAAE,sBAAsB,EAAE,UAAU,IAAIE,KAAI,KAAK,IAAI,EAAE,KAAK,IAAID;AAC5G,QAAAZ,GAAE,QAAQa,KAAI,EAAE,QAAQ,IAAID,KAAI,CAACA,KAAI,GAAGZ,GAAE,UAAU,IAAIc,GAAE,KAAKL,GAAET,GAAE,KAAK,GAAGI,GAAE,QAAQ;AAAA,MACvF;AACA,MAAAD,GAAE,YAAYC,GAAE,KAAK;AAAA,IACvB;AACA,aAAS,IAAI;AACX,QAAE,QAAQ,OAAIF,GAAE,QAAQ,OAAIO,GAAET,GAAE,KAAK,GAAGA,GAAE,UAAU,MAAMD,GAAE,QAAQ,OAAOI,GAAE,YAAYC,GAAE,KAAK;AAAA,IAClG;AACA,aAASI,GAAE,GAAG;AACZ,UAAIG,IAAGC;AACP,YAAM,IAAI,CAAC,GAAGD,KAAI,KAAK,OAAO,SAAS,EAAE,UAAU,QAAQA,GAAE,SAAS,IAAI,CAAC,GAAGC,KAAIP,MAAK,OAAO,SAASA,GAAE,UAAU,QAAQO,GAAE;AAC7H,aAAO,MAAM,WAAW,KAAK,MAAM,UAAU;AAAA,IAC/C;AACA,aAASH,GAAE,GAAG;AACZ,MAAAZ,GAAE,UAAUA,GAAE,MAAM,MAAM,YAAY,cAAc,CAAC;AAAA,IACvD;AACA,aAAS,EAAE,GAAG,IAAI,MAAI;AACpB,WAAKiB,GAAE,GAAGX,GAAE,eAAe,CAAC,GAAG,MAAM,aAAa,EAAE,QAAQ;AAAA,IAC9D;AACA,aAASW,KAAI;AACX,MAAAjB,GAAE,UAAUG,GAAE,QAAQ,GAAGD,GAAE,QAAQ,MAAMU,GAAET,GAAE,KAAK,GAAGI,GAAE,QAAQ;AAAA,IACjE;AACA,aAASG,GAAE,GAAG,GAAG,GAAG;AAClB,YAAM,EAAE,MAAMI,IAAG,KAAKC,IAAG,QAAQC,GAAE,IAAI;AACvC,aAAO,KAAKF,MAAK,KAAKC,KAAIC,KAAI,KAAK,KAAKD,KAAIC,KAAI;AAAA,IAClD;AACA,WAAOnB,GAAE;AAAA,MACP,gBAAgBU;AAAA,MAChB,eAAeU;AAAA,IACjB,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,YAAE,YAAG,EAAE,MAAM,WAAW,GAAG;AAAA,MAC7C,SAAS,QAAE,MAAM;AACf,YAAI;AACJ,eAAO;AAAA,UACL,EAAE,QAAQ,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,mBAAE,OAAOvB,KAAI;AAAA,YACvC,gBAAE,OAAO;AAAA,cACP,OAAO;AAAA,cACP,OAAO,eAAE,EAAE,qBAAqB,IAAIe,GAAE,UAAU,OAAO,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,YAC9E,GAAG,MAAM,CAAC;AAAA,YACV,gBAAE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,KAAKR;AAAA,cACL,OAAO,eAAE,6BAA6BC,GAAE,UAAU,SAAS,UAAU,MAAM,EAAE;AAAA,YAC/E,GAAG;AAAA,eACA,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAEO,GAAE,OAAO,CAACK,QAAO,UAAE,GAAG,YAAE,GAAI;AAAA,gBAC/C,OAAO;AAAA,gBACP,KAAKA,GAAE;AAAA,gBACP,OAAO,eAAE,EAAE,iBAAiBA,GAAE,MAAM,CAAC;AAAA,gBACrC,MAAMA,GAAE,OAAO,EAAE,MAAMA,GAAE,KAAK,IAAI;AAAA,gBAClC,OAAOA,GAAE;AAAA,gBACT,YAAY,cAAE,CAACC,OAAM,EAAED,GAAE,KAAKA,GAAE,eAAe,GAAG,CAAC,QAAQ,SAAS,CAAC;AAAA,cACvE,GAAG,MAAM,GAAG,CAAC,SAAS,QAAQ,SAAS,YAAY,CAAC,EAAE,GAAG,GAAG;AAAA,YAC9D,GAAG,CAAC;AAAA,YACJ,gBAAE,OAAO;AAAA,cACP,SAAS;AAAA,cACT,KAAKd;AAAA,cACL,OAAO,eAAE,CAAC,oBAAoB,EAAE,SAASK,GAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAAA,cAC9D,qBAAqB;AAAA,cACrB,oBAAoB;AAAA,cACpB,YAAY,cAAEQ,IAAG,CAAC,QAAQ,SAAS,CAAC;AAAA,cACpC,eAAe,cAAE,GAAG,CAAC,QAAQ,SAAS,CAAC;AAAA,YACzC,GAAG;AAAA,cACD,WAAG,EAAE,QAAQ,SAAS;AAAA,YACxB,GAAG,EAAE;AAAA,UACP,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF,CAAC;;;AC1HD,IAAMK,KAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IAEX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,UAAM,IAAI,GAAG,EAAE,MAAMC,IAAG,OAAOC,IAAG,OAAOC,GAAE,IAAI,OAAE,CAAC,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,IAAE,IAAI,GAAGC,KAAI,IAAE,KAAE,GAAGC,KAAI,IAAE,CAAC,GAAG,IAAI,IAAE,CAAC,GAAGC,KAAI,IAAE,CAAC;AACrH,cAAE,MAAM;AACN,UAAI,EAAE,SAAS,EAAE,OAAO;AACtB,YAAIA,GAAE,QAAQ,EAAE,MAAM,cAAc,EAAE,MAAM,aAAaF,GAAE,QAAQE,GAAE,QAAQ,GAAG,CAACF,GAAE,MAAO;AAC1F,cAAMG,KAAID,GAAE,QAAQJ,GAAE;AACtB,UAAE,MAAM,MAAM,aAAa,OAAOK,EAAC,YAAYJ,GAAE,KAAK,KAAK,EAAE,MAAM,MAAM,YAAY,eAAeG,GAAE,KAAK;AAAA,MAC7G;AAAA,IACF,CAAC;AACD,aAASE,GAAED,IAAG;AACZ,MAAAH,GAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,MAAM,qBAAqB,UAAUC,GAAE,QAAQE,GAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,QAAQE,GAAE,GAAG,EAAE,MAAM,MAAM,YAAY,cAAc,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM,aAAa;AAAA,IACjN;AACA,aAAS,EAAEF,IAAG;AACZ,UAAIH,GAAE,SAASG,GAAE,QAAQ,WAAW,KAAK,EAAE,OAAO;AAChD,cAAM,IAAIA,GAAE,QAAQ,CAAC,EAAE,UAAUF,GAAE,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,CAACC,GAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC;AACzF,UAAE,MAAM,MAAM,YAAY,cAAc,CAAC;AAAA,MAC3C;AAAA,IACF;AACA,aAAS,IAAI;AACX,UAAIF,GAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQK,GAAE,GAAG,KAAK,IAAI,EAAE,KAAK,IAAIH,GAAE,QAAQ;AACjF,cAAMC,MAAKD,GAAE,QAAQ,KAAK,IAAI,EAAE,KAAK,KAAKJ,GAAE;AAC5C,UAAE,MAAM,MAAM,aAAa,OAAOK,EAAC,YAAYJ,GAAE,KAAK,KAAK,EAAE,MAAM,MAAM,YAAY,eAAeG,GAAE,KAAK,OAAO,EAAE,MAAM,MAAM,qBAAqB;AAAA,MACvJ;AAAA,IACF;AACA,aAASG,KAAI;AACX,UAAI,CAAC,EAAE,MAAO,QAAO;AACrB,YAAM,IAAI,iBAAiB,EAAE,KAAK,EAAE;AACpC,UAAI,MAAM,UAAU,CAAC;AACnB,eAAO;AACT,YAAM,IAAI,EAAE,MAAM,gBAAgB;AAClC,aAAO,KAAK,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,UAAU,EAAE,CAAC,KAAK;AAAA,IACpD;AACA,WAAO,CAACF,IAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC9B,SAAS;AAAA,MACT,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG;AAAA,MACD,gBAAE,OAAO;AAAA,QACP,SAAS;AAAA,QACT,KAAK;AAAA,QACL,OAAO;AAAA,QACP,qBAAqBC;AAAA,QACrB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,YAAY;AAAA,MACd,GAAG,gBAAE,MAAEP,EAAC,CAAC,GAAG,GAAG;AAAA,IACjB,GAAG,GAAG;AAAA,EACR;AACF,CAAC;;;ACjED,SAAS,IAAI;AACX,QAAMS,KAAI,SAAE;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,EACR,CAAC;AACD,MAAI,IAAI;AACR,WAASC,GAAE,IAAI,IAAI;AACjB,IAAAD,GAAE,OAAO,MAAIA,GAAE,UAAU,QAAQA,GAAE,OAAO;AAAA,EAC5C;AACA,WAAS,EAAE,GAAG,GAAG,IAAI,MAAM;AACzB,IAAAA,GAAE,UAAU,GAAG,KAAK,aAAa,CAAC,GAAG,IAAI,WAAW,MAAM;AACxD,MAAAA,GAAE,OAAO,OAAI,KAAK,EAAE;AAAA,IACtB,GAAG,CAAC;AAAA,EACN;AACA,SAAO;AAAA,IACL,SAASA;AAAA,IACT,cAAcC;AAAA,IACd,qBAAqB;AAAA,EACvB;AACF;;;ACpBA,SAAS,EAAEC,IAAG;AACZ,QAAM,IAAI,IAAE,KAAK,MAAM,aAAa,QAAQA,EAAC,KAAK,IAAI,KAAK,CAAC,CAAC;AAC7D,WAASC,GAAE,GAAG;AACZ,WAAO,OAAO,EAAE,OAAO,CAAC,GAAG,aAAa,QAAQD,IAAG,KAAK,UAAU,EAAE,KAAK,CAAC;AAAA,EAC5E;AACA,SAAO;AAAA,IACL,mBAAmB;AAAA,IACnB,yBAAyBC;AAAA,EAC3B;AACF;;;ACVO,IAAI;AAAA,CACV,SAAUC,UAAS;AAChB,MAAI;AACJ,GAAC,SAAUC,SAAQ;AACf,IAAAA,QAAO,SAAS,IAAI;AACpB,IAAAA,QAAO,MAAM,IAAI;AACjB,IAAAA,QAAO,OAAO,IAAI;AAAA,EACtB,GAAG,SAASD,SAAQ,WAAWA,SAAQ,SAAS,CAAC,EAAE;AACvD,GAAG,YAAY,UAAU,CAAC,EAAE;;;ACN5B,IAAM,EAAE,SAAS,MAAM,MAAM,IAAI,QAAQ;AAAzC,IAAiD,YAAY;AAE7D,IAAM,YAAY;AAAA,EACd,QAAQ,OAAO,YAAY,QAAQ,aAAa;AAC5C,QAAI,CAAC,YAAY;AACb,aAAO,EAAE,OAAO,EAAE,eAAe,2BAA2B,EAAE;AAAA,IAClE;AACA,UAAM,MAAM,GAAG,UAAU;AACzB,UAAM,cAAc;AACpB,QAAI;AACA,YAAM,WAAW,MAAM,OAAO,OAAO,GAAG,SAAS,EAAE,KAAK,QAAQ,UAAU,YAAY,CAAC;AACvF,aAAO;AAAA,IACX,SACO,OAAO;AACV,aAAO,EAAE,MAAM;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,WAAW,OAAO,IAAI,YAAY;AAC9B,QAAI;AACA,YAAM,OAAO,OAAO,GAAG,MAAM,EAAE,IAAI,SAAS,KAAK,UAAU,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,CAAC;AAAA,IACjG,SACO,OAAO;AACV,aAAO,EAAE,MAAM;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,aAAa,OAAO,OAAO;AACvB,QAAI;AACA,YAAM,OAAO,OAAO,GAAG,OAAO,EAAE,GAAG,CAAC;AAAA,IACxC,SACO,OAAO;AACV,aAAO,EAAE,MAAM;AAAA,IACnB;AAAA,EACJ;AACJ;AA0DA,eAAsB,gBAAgB,OAAO,SAAS;AAClD,QAAM,SAAS,WAAW,KAAK;AAC/B,MAAI,gBAAgB,MAAM,GAAG;AACzB,WAAO,EAAE,OAAO,EAAE,GAAG,OAAO,OAAO,eAAe,4BAA4B,EAAE;AAAA,EACpF;AACA,QAAM,QAAQ,OAAO,QAAQ,OAAO,EAAE,KAAK,CAAC,CAAC,KAAK,QAAQ,MAAM,OAAO,aAAa,QAAQ;AAE5F,MAAI,CAAC,OAAO;AACR,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,WAAW,MAAM,UAAU,YAAY,QAAQ;AACrD,QAAI,gBAAgB,QAAQ,GAAG;AAC3B,aAAO;AAAA,QACH,OAAO;AAAA,UACH,GAAG,SAAS;AAAA,UAAO,eAAe;AAAA,UAA0C;AAAA,UAAU;AAAA,QAC1F;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,EAAE,OAAO,EAAE,eAAe,mCAAmC,UAAU,MAAM,OAAO,MAAM,QAAQ,EAAE;AAAA,EAC/G;AACA,SAAO;AAAA,IACH,KAAK,MAAM,CAAC;AAAA,IACZ,MAAM,OAAO;AAAA,EACjB;AACJ;AAEA,eAAsB,cAAc,OAAO,SAAS;AAChD,QAAM,SAAS,WAAW,KAAK;AAC/B,MAAI,gBAAgB,MAAM,GAAG;AACzB,WAAO,EAAE,OAAO,EAAE,GAAG,OAAO,OAAO,eAAe,0BAA0B,QAAQ,EAAE;AAAA,EAC1F;AACA,QAAM,QAAQ,OAAO,QAAQ,OAAO,EAAE,KAAK,CAAC,CAAC,KAAK,QAAQ,MAAM,OAAO,aAAa,QAAQ;AAC5F,MAAI,CAAC,OAAO;AACR,UAAM,EAAE,SAAS,IAAI;AACrB,WAAO,EAAE,OAAO,EAAE,eAAe,oCAAoC,UAAU,QAAQ,EAAE;AAAA,EAC7F;AAEA,QAAM,YAAY,MAAM,CAAC;AACzB,MAAI,QAAQ,SAAS;AACjB,YAAQ,SAAS,IAAI;AACzB,SAAO,EAAE,UAAU,OAAO,UAAU,KAAK,UAAU;AACvD;AAEA,eAAsB,cAAc,YAAY,SAAS,MAAM;AAC3D,SAAO,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,qBAAqB,QAAQ,EAAE;AAC7E;AACA,SAAS,WAAW,OAAO;AACvB,QAAM,EAAE,IAAI,QAAQ,IAAI,MAAM,UAAU,CAAC;AACzC,QAAM,SAAS,EAAE,UAAU,IAAI,MAAM,CAAC,EAAE;AACxC,MAAI;AACA,QAAI;AACA,aAAO,OAAO,KAAK,MAAM,OAAO;AAAA,EACxC,SACO,OAAO;AACV,WAAO,EAAE,MAAM;AAAA,EACnB;AACA,SAAO;AACX;;;AClJA,SAASE,GAAE,GAAG,GAAG;AACf,QAAMC,KAAI,IAAE,EAAE,GAAGC,KAAI,IAAE,EAAE,GAAGC,KAAI,IAAE,KAAE,GAAG,IAAI,IAAE,CAAC,GAAGC,KAAI,IAAE,EAAE,MAAM,GAAGC,KAAI,SAAE,YAAY;AAClF,UAAM,EAAE,UAAU;AAAA,EACpB,GAAG,GAAG;AACN,QAAED,IAAG,CAAC,MAAM;AACV,WAAO,KAAK,CAAC,EAAE,OAAO,CAACE,OAAM,CAAC,EAAEA,EAAC,CAAC,EAAE,UAAUD,GAAE;AAAA,EAClD,CAAC;AACD,iBAAe,EAAE,GAAG;AAClB,UAAM,IAAI,MAAM,gBAAE,GAAG,EAAE,MAAM;AAC7B,QAAI,gBAAE,CAAC,GAAG;AACR,cAAQ,IAAI,oBAAoB,EAAE,KAAK;AACvC;AAAA,IACF;AACA,UAAM,EAAE,KAAKC,IAAG,MAAMC,GAAE,IAAI;AAC5B,IAAAD,MAAK,EAAE,UAAU,EAAE,KAAKA,IAAG,MAAMC,GAAE,CAAC,GAAGN,GAAE,SAAyB,oBAAI,KAAK,GAAG,YAAY;AAAA,EAC5F;AACA,iBAAeO,GAAE,GAAG;AAClB,UAAM,IAAI,KAAK,MAAM,KAAK,UAAU,EAAE,MAAM,CAAC,GAAGF,KAAI,MAAM,cAAE,GAAG,CAAC;AAChE,QAAI,gBAAEA,EAAC,GAAG;AACR,cAAQ,IAAI,oBAAoBA,GAAE,KAAK;AACvC;AAAA,IACF;AACA,MAAE,QAAQA,EAAC,GAAGF,GAAE,QAAQ,EAAE;AAAA,EAC5B;AACA,iBAAe,EAAE,GAAG;AAClB,UAAM,IAAI,KAAK,MAAM,KAAK,UAAU,EAAE,MAAM,CAAC;AAC7C,MAAE,SAAS,GAAG,EAAE,QAAQ,KAAK,MAAM,cAAE,GAAG,GAAG,CAAC;AAAA,EAC9C;AACA,iBAAeK,KAAI;AACjB,IAAAP,GAAE,SAAyB,oBAAI,KAAK,GAAG,YAAY,GAAG,MAAM,EAAE,UAAU;AAAA,EAC1E;AACA,SAAO,UAAE,YAAY;AACnB,WAAO,iBAAiB,kBAAkB,CAAC,GAAG,OAAO,iBAAiB,gBAAgBM,EAAC,GAAG,OAAO,iBAAiB,gBAAgB,CAAC,GAAG,OAAO,iBAAiB,cAAcC,EAAC,GAAG,MAAM,EAAE,MAAM,GAAGL,GAAE,QAAQ,EAAE,QAAQD,GAAE,QAAQ;AAAA,EACjO,CAAC,GAAG,gBAAE,YAAY;AAChB,WAAO,oBAAoB,kBAAkB,CAAC,GAAG,OAAO,oBAAoB,gBAAgBK,EAAC,GAAG,OAAO,oBAAoB,gBAAgB,CAAC,GAAG,OAAO,oBAAoB,cAAcC,EAAC,GAAG,MAAM,EAAE,IAAI;AAAA,EAC1M,CAAC,GAAG;AAAA,IACF,OAAO;AAAA,IACP,OAAON;AAAA,IACP,UAAUF;AAAA,IACV,YAAYC;AAAA,EACd;AACF;;;AC3CA,IAAMQ,KAAI,EAAE,QAAQ,EAAE,IAAI,MAAM,QAAQ,UAAU,QAAQ,UAAU,QAAQ,SAAS,GAAG,MAAM,EAAE,QAAQ,UAAU,MAAM,QAAQ,QAAQ,SAAS,GAAG,WAAW,EAAE,QAAQ,EAAE,QAAQ,WAAW,QAAQ,GAAG,GAAG,OAAO,eAAe,eAAe,EAAE,QAAQ,WAAW,QAAQ,GAAG,GAAG,aAAa,EAAE,QAAQ,WAAW,QAAQ,GAAG,GAAG,WAAW,EAAE,IAAI,OAAO,IAAI,QAAQ,IAAI,OAAO,IAAI,MAAM,EAAE,GAAG,OAAO,EAAE,gBAAgB,WAAW,2BAA2B,6BAA6B,aAAa,YAAY,gBAAgB,4CAA4C,gBAAgB,4CAA4C,gBAAgB,wBAAwB,gBAAgB,wBAAwB,iBAAiB,gBAAgB,SAAS,WAAW,cAAc,qBAAqB,qBAAqB,2BAA2B,oBAAoB,6BAA6B,sBAAsB,6BAA6B,sBAAsB,4BAA4B,EAAE;AAA/9B,IAAk+BC,KAAI;AAAA,EACp+B,IAAID;AAAA,EACJ,WAAW,EAAE,QAAQ,EAAE,IAAI,KAAK,QAAQ,MAAM,QAAQ,MAAM,QAAQ,KAAK,GAAG,MAAM,EAAE,QAAQ,MAAM,MAAM,MAAM,QAAQ,KAAK,GAAG,WAAW,EAAE,QAAQ,EAAE,QAAQ,IAAI,QAAQ,KAAK,GAAG,OAAO,SAAS,eAAe,EAAE,QAAQ,IAAI,QAAQ,MAAM,GAAG,aAAa,EAAE,QAAQ,MAAM,QAAQ,GAAG,GAAG,WAAW,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,OAAO,EAAE,gBAAgB,MAAM,2BAA2B,aAAa,aAAa,OAAO,gBAAgB,YAAY,gBAAgB,YAAY,gBAAgB,UAAU,gBAAgB,UAAU,iBAAiB,QAAQ,SAAS,MAAM,cAAc,UAAU,qBAAqB,gBAAgB,oBAAoB,gBAAgB,sBAAsB,iBAAiB,sBAAsB,gBAAgB,EAAE;AAAA,EAC9uB,WAAW,EAAE,QAAQ,EAAE,IAAI,KAAK,QAAQ,MAAM,QAAQ,MAAM,QAAQ,KAAK,GAAG,MAAM,EAAE,QAAQ,MAAM,MAAM,MAAM,QAAQ,KAAK,GAAG,WAAW,EAAE,QAAQ,EAAE,QAAQ,IAAI,QAAQ,KAAK,GAAG,OAAO,SAAS,eAAe,EAAE,QAAQ,IAAI,QAAQ,MAAM,GAAG,aAAa,EAAE,QAAQ,MAAM,QAAQ,GAAG,GAAG,WAAW,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,OAAO,EAAE,gBAAgB,MAAM,2BAA2B,aAAa,aAAa,OAAO,gBAAgB,YAAY,gBAAgB,YAAY,gBAAgB,UAAU,gBAAgB,UAAU,iBAAiB,QAAQ,SAAS,MAAM,cAAc,UAAU,qBAAqB,eAAe,oBAAoB,eAAe,sBAAsB,iBAAiB,sBAAsB,gBAAgB,EAAE;AAC9uB;AAJA,IAIG,IAAI,WAAE;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAUC;AACZ,CAAC;", "names": ["Barcode", "i", "CODE39", "characterValue", "i", "CODE128", "matchSetALength", "matchSetBLength", "matchSetC", "CODE128AUTO", "i", "CODE128A", "i", "CODE128B", "i", "CODE128C", "require_CODE128", "require_constants", "encode", "i", "EAN", "i", "checksum", "a", "EAN13", "i", "checksum", "a", "EAN8", "i", "checksum", "a", "EAN5", "i", "EAN2", "i", "UPC", "i", "UPCE", "c", "require_constants", "i", "ITF", "i", "checksum", "ITF14", "require_ITF", "i", "MSI", "i", "MSI10", "MSI11", "MSI1010", "MSI1110", "require_MSI", "i", "pharmacode", "z", "i", "codabar", "i", "GenericBarcode", "i", "i", "i", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "y", "i", "<PERSON><PERSON><PERSON><PERSON>", "y", "i", "O<PERSON><PERSON><PERSON><PERSON>", "InvalidInputException", "InvalidElementException", "NoElementException", "i", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "API", "JsBarcode", "name", "i", "i", "i", "j", "module", "i", "k", "j", "i", "y", "i", "j", "i", "i", "i", "i", "v", "t", "a", "i", "j", "i", "r", "c", "require_utils", "c", "i", "j", "require_canvas", "y", "i", "_", "I", "W", "A", "m", "r", "v", "h", "B", "j", "t", "i", "G", "X", "D", "L", "U", "$", "O", "Q", "Q", "t", "K", "z", "j", "w", "h", "v", "r", "B", "E", "I", "O", "i", "g", "k", "m", "w", "D", "m", "c", "r", "I", "t", "v", "a", "i", "L", "h", "g", "O", "y", "_", "k", "v", "A", "B", "U", "$", "m", "y", "g", "h", "O", "C", "r", "j", "D", "c", "i", "L", "Y", "a", "G", "t", "J", "E", "I", "K", "Q", "W", "X", "c", "r", "h", "m", "t", "k", "t", "r", "$", "y", "E", "t", "r", "c", "import_format_datetime", "h", "g", "O", "a", "w", "E", "C", "A", "D", "Y", "r", "c", "y", "X", "Q", "z", "J", "j", "U", "K", "v", "r", "I", "v", "O", "a", "y", "c", "w", "i", "t", "B", "C", "E", "h", "g", "t", "v", "B", "w", "W", "c", "C", "a", "I", "m", "k", "_", "E", "O", "$", "h", "j", "L", "U", "D", "G", "C", "w", "B", "E", "g", "t", "h", "c", "v", "Y", "r", "A", "L", "$", "import_format_datetime", "ne", "se", "le", "ve", "he", "m", "w", "L", "O", "a", "g", "c", "I", "A", "j", "r", "i", "B", "U", "C", "K", "z", "k", "_", "h", "W", "E", "ee", "le", "ae", "Y", "z", "D", "B", "r", "X", "c", "i", "$", "v", "w", "m", "h", "k", "U", "O", "y", "L", "t", "a", "g", "C", "k", "y", "i", "c", "r", "v", "a", "t", "h", "m", "t", "i", "t", "r", "Actions", "Remote", "C", "a", "i", "c", "r", "k", "t", "y", "w", "v", "m", "i"]}