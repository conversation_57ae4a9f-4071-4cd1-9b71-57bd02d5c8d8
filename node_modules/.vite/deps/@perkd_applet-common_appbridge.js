import {
  __publicField
} from "./chunk-EQCVQC35.js";

// node_modules/@perkd/applet-common/dist/appbridge.js
(function() {
  const s6 = () => Math.floor((1 + Math.random()) * 16777216).toString(16).substring(1);
  const evtId = () => s6() + s6();
  const PENDING = {};
  const DATA = {};
  const BAG = {};
  const setDATA = (data) => {
    Object.assign(DATA, data);
    Object.keys(DATA).forEach(function(prop) {
      if (typeof window.$data[prop] !== "undefined")
        return;
      Object.defineProperty(window.$data, prop, {
        get() {
          return DATA[prop];
        },
        set(value) {
          DATA[prop] = value;
        },
        enumerable: true
      });
    });
    window.dispatchEvent(new CustomEvent("data.changed"));
  };
  window.$perkd = {
    onMessage(param) {
      const pending = PENDING[param.id];
      if (pending) {
        if (param.error)
          pending.reject(param.error);
        else
          pending.resolve(param.data);
      } else if (param.name === "data.changed")
        setDATA(param.data);
      else if (param.name === "bag.changed") {
        Object.assign(BAG, param.data);
        window.dispatchEvent(new CustomEvent("bag.changed"));
      } else
        window.dispatchEvent(new CustomEvent(param.name, { detail: param.data }));
    },
    emit(name, data) {
      const id = evtId(), event = JSON.stringify({ id, name, data });
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(event);
      } else {
        window.parent.postMessage(JSON.parse(event), "*");
      }
      return id;
    },
    do(action, param) {
      const id = window.$perkd.emit("do", { action, param });
      return new Promise(function(resolve, reject) {
        PENDING[id] = { resolve, reject };
      });
    }
  };
  class Data {
    save() {
      window.$perkd.do("data.save", DATA);
    }
    add(prop) {
      Object.defineProperty(this, prop, {
        get() {
          return DATA[prop];
        },
        set(value) {
          DATA[prop] = value;
        },
        enumerable: true
      });
    }
  }
  window.$data = new Data();
  class Bag {
    constructor() {
      __publicField(this, "items");
      __publicField(this, "amount");
    }
    addItems(items) {
      window.$perkd.do("bag.addItems", { items });
    }
    updateItems(items) {
      window.$perkd.do("bag.updateItems", { items });
    }
    removeItems(items) {
      window.$perkd.do("bag.removeItems", { items });
    }
  }
  window.$bag = new Bag();
  const params = ["items", "amount"];
  params.forEach((prop) => Object.defineProperty(window.$bag, prop, {
    get() {
      return BAG[prop];
    },
    enumerable: true
  }));
  window.$perkd.do("init").then(setDATA);
})();
//# sourceMappingURL=@perkd_applet-common_appbridge.js.map
