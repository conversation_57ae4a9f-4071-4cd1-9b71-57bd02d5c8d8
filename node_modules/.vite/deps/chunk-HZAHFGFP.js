import {
  useI18n
} from "./chunk-ILBRJJGN.js";
import {
  Q
} from "./chunk-54FI6OKY.js";
import {
  createBlock,
  createCommentVNode,
  createElementBlock,
  defineComponent,
  normalizeClass,
  openBlock,
  renderSlot,
  toDisplayString,
  unref
} from "./chunk-U3LI7FBV.js";

// node_modules/@perkd/vue-components/dist/components/UINavigationBar.js
var v = ["theme"];
var h = {
  key: 2,
  class: "status"
};
var B = {
  key: 3,
  class: "status"
};
var I = defineComponent({
  __name: "UINavigationBar",
  props: {
    navBack: {
      type: Object
    },
    theme: {
      type: String,
      default: "perkd"
    },
    leftClass: {
      type: String,
      default: ""
    },
    centerClass: {
      type: String,
      default: ""
    },
    rightClass: {
      type: String,
      default: ""
    },
    title: String,
    titleClass: {
      type: String,
      default: ""
    },
    isOnline: {
      type: Boolean,
      default: true
    },
    status: {
      type: String,
      default: ""
    }
  },
  setup(t) {
    const { t: o } = useI18n();
    return (l, S) => {
      var r, u, k, f;
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(["navigation-bar", t.isOnline ? "online" : "offline"]),
        theme: t.theme
      }, [
        l.$slots.leftContent || t.navBack || !t.isOnline || t.isOnline && t.status ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: normalizeClass("left-container " + t.leftClass)
        }, [
          t.navBack && ((r = t.navBack) == null ? void 0 : r.type) === "back" ? (openBlock(), createBlock(Q, {
            key: 0,
            type: "clear",
            icon: { name: "back" },
            class: "back-button",
            onClick: (u = t.navBack) == null ? void 0 : u.onClick
          }, null, 8, ["onClick"])) : createCommentVNode("", true),
          t.navBack && ((k = t.navBack) == null ? void 0 : k.type) === "cancel" ? (openBlock(), createBlock(Q, {
            key: 1,
            type: "clear",
            title: unref(o)("button.cancel"),
            onClick: (f = t.navBack) == null ? void 0 : f.onClick
          }, null, 8, ["title", "onClick"])) : createCommentVNode("", true),
          t.isOnline ? createCommentVNode("", true) : (openBlock(), createElementBlock("span", h, toDisplayString(unref(o)("error.offline_status")), 1)),
          t.isOnline && t.status ? (openBlock(), createElementBlock("span", B, toDisplayString(t.status), 1)) : createCommentVNode("", true),
          renderSlot(l.$slots, "leftContent")
        ], 2)) : createCommentVNode("", true),
        l.$slots.centerContent || t.title ? (openBlock(), createElementBlock("div", {
          key: 1,
          class: normalizeClass("center-container " + t.centerClass)
        }, [
          t.title ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: normalizeClass("navigation-title " + t.titleClass)
          }, toDisplayString(t.title), 3)) : createCommentVNode("", true),
          renderSlot(l.$slots, "centerContent")
        ], 2)) : createCommentVNode("", true),
        l.$slots.rightContent ? (openBlock(), createElementBlock("div", {
          key: 2,
          class: normalizeClass("right-container " + t.rightClass)
        }, [
          renderSlot(l.$slots, "rightContent")
        ], 2)) : createCommentVNode("", true)
      ], 10, v);
    };
  }
});

export {
  I
};
//# sourceMappingURL=chunk-HZAHFGFP.js.map
