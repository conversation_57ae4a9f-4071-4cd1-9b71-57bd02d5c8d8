{"version": 3, "sources": ["../../@perkd/vue-components/dist/components/UINavigationBar.js"], "sourcesContent": ["import { defineComponent as m, createElementBlock as a, openBlock as e, normalizeClass as i, createCommentVNode as n, createBlock as C, renderSlot as s, unref as y, toDisplayString as c } from \"vue\";\nimport { useI18n as g } from \"vue-i18n\";\nimport d from \"./UIButton.js\";\nconst v = [\"theme\"], h = {\n  key: 2,\n  class: \"status\"\n}, B = {\n  key: 3,\n  class: \"status\"\n}, I = /* @__PURE__ */ m({\n  __name: \"UINavigationBar\",\n  props: {\n    navBack: {\n      type: Object\n    },\n    theme: {\n      type: String,\n      default: \"perkd\"\n    },\n    leftClass: {\n      type: String,\n      default: \"\"\n    },\n    centerClass: {\n      type: String,\n      default: \"\"\n    },\n    rightClass: {\n      type: String,\n      default: \"\"\n    },\n    title: String,\n    titleClass: {\n      type: String,\n      default: \"\"\n    },\n    isOnline: {\n      type: Boolean,\n      default: !0\n    },\n    status: {\n      type: String,\n      default: \"\"\n    }\n  },\n  setup(t) {\n    const { t: o } = g();\n    return (l, S) => {\n      var r, u, k, f;\n      return e(), a(\"div\", {\n        class: i([\"navigation-bar\", t.isOnline ? \"online\" : \"offline\"]),\n        theme: t.theme\n      }, [\n        l.$slots.leftContent || t.navBack || !t.isOnline || t.isOnline && t.status ? (e(), a(\"div\", {\n          key: 0,\n          class: i(\"left-container \" + t.leftClass)\n        }, [\n          t.navBack && ((r = t.navBack) == null ? void 0 : r.type) === \"back\" ? (e(), C(d, {\n            key: 0,\n            type: \"clear\",\n            icon: { name: \"back\" },\n            class: \"back-button\",\n            onClick: (u = t.navBack) == null ? void 0 : u.onClick\n          }, null, 8, [\"onClick\"])) : n(\"\", !0),\n          t.navBack && ((k = t.navBack) == null ? void 0 : k.type) === \"cancel\" ? (e(), C(d, {\n            key: 1,\n            type: \"clear\",\n            title: y(o)(\"button.cancel\"),\n            onClick: (f = t.navBack) == null ? void 0 : f.onClick\n          }, null, 8, [\"title\", \"onClick\"])) : n(\"\", !0),\n          t.isOnline ? n(\"\", !0) : (e(), a(\"span\", h, c(y(o)(\"error.offline_status\")), 1)),\n          t.isOnline && t.status ? (e(), a(\"span\", B, c(t.status), 1)) : n(\"\", !0),\n          s(l.$slots, \"leftContent\")\n        ], 2)) : n(\"\", !0),\n        l.$slots.centerContent || t.title ? (e(), a(\"div\", {\n          key: 1,\n          class: i(\"center-container \" + t.centerClass)\n        }, [\n          t.title ? (e(), a(\"div\", {\n            key: 0,\n            class: i(\"navigation-title \" + t.titleClass)\n          }, c(t.title), 3)) : n(\"\", !0),\n          s(l.$slots, \"centerContent\")\n        ], 2)) : n(\"\", !0),\n        l.$slots.rightContent ? (e(), a(\"div\", {\n          key: 2,\n          class: i(\"right-container \" + t.rightClass)\n        }, [\n          s(l.$slots, \"rightContent\")\n        ], 2)) : n(\"\", !0)\n      ], 10, v);\n    };\n  }\n});\nexport {\n  I as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,IAAM,IAAI,CAAC,OAAO;AAAlB,IAAqB,IAAI;AAAA,EACvB,KAAK;AAAA,EACL,OAAO;AACT;AAHA,IAGG,IAAI;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AACT;AANA,IAMG,IAAoB,gBAAE;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,UAAM,EAAE,GAAG,EAAE,IAAI,QAAE;AACnB,WAAO,CAAC,GAAG,MAAM;AACf,UAAI,GAAG,GAAG,GAAG;AACb,aAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,QACnB,OAAO,eAAE,CAAC,kBAAkB,EAAE,WAAW,WAAW,SAAS,CAAC;AAAA,QAC9D,OAAO,EAAE;AAAA,MACX,GAAG;AAAA,QACD,EAAE,OAAO,eAAe,EAAE,WAAW,CAAC,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,UAAE,GAAG,mBAAE,OAAO;AAAA,UAC1F,KAAK;AAAA,UACL,OAAO,eAAE,oBAAoB,EAAE,SAAS;AAAA,QAC1C,GAAG;AAAA,UACD,EAAE,aAAa,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE,UAAU,UAAU,UAAE,GAAG,YAAE,GAAG;AAAA,YAC/E,KAAK;AAAA,YACL,MAAM;AAAA,YACN,MAAM,EAAE,MAAM,OAAO;AAAA,YACrB,OAAO;AAAA,YACP,UAAU,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE;AAAA,UAChD,GAAG,MAAM,GAAG,CAAC,SAAS,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UACpC,EAAE,aAAa,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE,UAAU,YAAY,UAAE,GAAG,YAAE,GAAG;AAAA,YACjF,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAO,MAAE,CAAC,EAAE,eAAe;AAAA,YAC3B,UAAU,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE;AAAA,UAChD,GAAG,MAAM,GAAG,CAAC,SAAS,SAAS,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UAC7C,EAAE,WAAW,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,mBAAE,QAAQ,GAAG,gBAAE,MAAE,CAAC,EAAE,sBAAsB,CAAC,GAAG,CAAC;AAAA,UAC9E,EAAE,YAAY,EAAE,UAAU,UAAE,GAAG,mBAAE,QAAQ,GAAG,gBAAE,EAAE,MAAM,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UACvE,WAAE,EAAE,QAAQ,aAAa;AAAA,QAC3B,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACjB,EAAE,OAAO,iBAAiB,EAAE,SAAS,UAAE,GAAG,mBAAE,OAAO;AAAA,UACjD,KAAK;AAAA,UACL,OAAO,eAAE,sBAAsB,EAAE,WAAW;AAAA,QAC9C,GAAG;AAAA,UACD,EAAE,SAAS,UAAE,GAAG,mBAAE,OAAO;AAAA,YACvB,KAAK;AAAA,YACL,OAAO,eAAE,sBAAsB,EAAE,UAAU;AAAA,UAC7C,GAAG,gBAAE,EAAE,KAAK,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,UAC7B,WAAE,EAAE,QAAQ,eAAe;AAAA,QAC7B,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACjB,EAAE,OAAO,gBAAgB,UAAE,GAAG,mBAAE,OAAO;AAAA,UACrC,KAAK;AAAA,UACL,OAAO,eAAE,qBAAqB,EAAE,UAAU;AAAA,QAC5C,GAAG;AAAA,UACD,WAAE,EAAE,QAAQ,cAAc;AAAA,QAC5B,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACnB,GAAG,IAAI,CAAC;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}