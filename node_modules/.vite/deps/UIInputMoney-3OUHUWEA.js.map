{"version": 3, "sources": ["../../vue-currency-input/dist/index.mjs", "../../@perkd/vue-components/dist/components/CurrencyInput.js", "../../@perkd/vue-components/dist/components/UIInputMoney.js"], "sourcesContent": ["/**\n * Vue Currency Input 3.2.1\n * (c) 2018-2025 <PERSON>\n * @license MIT\n */\nimport { ref, getCurrentInstance, version, computed, watch } from 'vue';\n\nvar CurrencyDisplay;\n(function (CurrencyDisplay) {\n    CurrencyDisplay[\"symbol\"] = \"symbol\";\n    CurrencyDisplay[\"narrowSymbol\"] = \"narrowSymbol\";\n    CurrencyDisplay[\"code\"] = \"code\";\n    CurrencyDisplay[\"name\"] = \"name\";\n    CurrencyDisplay[\"hidden\"] = \"hidden\";\n})(CurrencyDisplay || (CurrencyDisplay = {}));\nvar ValueScaling;\n(function (ValueScaling) {\n    ValueScaling[\"precision\"] = \"precision\";\n    ValueScaling[\"thousands\"] = \"thousands\";\n    ValueScaling[\"tenThousands\"] = \"tenThousands\";\n    ValueScaling[\"millions\"] = \"millions\";\n    ValueScaling[\"billions\"] = \"billions\";\n})(ValueScaling || (ValueScaling = {}));\n\nconst escapeRegExp = (str) => {\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n};\nconst removeLeadingZeros = (str) => {\n    return str.replace(/^0+(0$|[^0])/, '$1');\n};\nconst count = (str, search) => {\n    return (str.match(new RegExp(escapeRegExp(search), 'g')) || []).length;\n};\nconst substringBefore = (str, search) => {\n    return str.substring(0, str.indexOf(search));\n};\n\nconst DECIMAL_SEPARATORS = [\n    ',',\n    '.',\n    '٫',\n    '。'\n];\nconst INTEGER_PATTERN = '(0|[1-9]\\\\d*)';\nclass CurrencyFormat {\n    constructor(options) {\n        var _a, _b, _c, _d, _e, _f;\n        const { currency, currencyDisplay, locale, precision, accountingSign, useGrouping } = options;\n        this.locale = locale;\n        this.options = {\n            currency,\n            useGrouping,\n            style: 'currency',\n            currencySign: accountingSign ? 'accounting' : undefined,\n            currencyDisplay: currencyDisplay !== CurrencyDisplay.hidden ? currencyDisplay : undefined\n        };\n        const numberFormat = new Intl.NumberFormat(locale, this.options);\n        const formatParts = numberFormat.formatToParts(123456);\n        this.currency = (_a = formatParts.find(({ type }) => type === 'currency')) === null || _a === void 0 ? void 0 : _a.value;\n        this.digits = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => i.toLocaleString(locale));\n        this.decimalSymbol = (_b = formatParts.find(({ type }) => type === 'decimal')) === null || _b === void 0 ? void 0 : _b.value;\n        this.groupingSymbol = (_c = formatParts.find(({ type }) => type === 'group')) === null || _c === void 0 ? void 0 : _c.value;\n        this.minusSign = (_d = numberFormat.formatToParts(-1).find(({ type }) => type === 'minusSign')) === null || _d === void 0 ? void 0 : _d.value;\n        if (this.decimalSymbol === undefined) {\n            this.minimumFractionDigits = this.maximumFractionDigits = 0;\n        }\n        else if (typeof precision === 'number') {\n            this.minimumFractionDigits = this.maximumFractionDigits = precision;\n        }\n        else {\n            this.minimumFractionDigits = (_e = precision === null || precision === void 0 ? void 0 : precision.min) !== null && _e !== void 0 ? _e : numberFormat.resolvedOptions().minimumFractionDigits;\n            this.maximumFractionDigits = (_f = precision === null || precision === void 0 ? void 0 : precision.max) !== null && _f !== void 0 ? _f : numberFormat.resolvedOptions().maximumFractionDigits;\n        }\n        const getPrefix = (str) => {\n            return substringBefore(str, this.digits[1]);\n        };\n        const getSuffix = (str) => {\n            return str.substring(str.lastIndexOf(this.decimalSymbol ? this.digits[0] : this.digits[1]) + 1);\n        };\n        this.prefix = getPrefix(numberFormat.format(1));\n        this.suffix = getSuffix(numberFormat.format(1));\n        this.negativePrefix = getPrefix(numberFormat.format(-1));\n        this.negativeSuffix = getSuffix(numberFormat.format(-1));\n    }\n    parse(str) {\n        if (str) {\n            const negative = this.isNegative(str);\n            str = this.normalizeDigits(str);\n            str = this.stripCurrency(str, negative);\n            str = this.stripSignLiterals(str);\n            const fraction = this.decimalSymbol ? `(?:${escapeRegExp(this.decimalSymbol)}(\\\\d*))?` : '';\n            const match = this.stripGroupingSeparator(str).match(new RegExp(`^${INTEGER_PATTERN}${fraction}$`));\n            if (match && this.isValidIntegerFormat(this.decimalSymbol ? str.split(this.decimalSymbol)[0] : str, Number(match[1]))) {\n                return Number(`${negative ? '-' : ''}${this.onlyDigits(match[1])}.${this.onlyDigits(match[2] || '')}`);\n            }\n        }\n        return null;\n    }\n    isValidIntegerFormat(formattedNumber, integerNumber) {\n        const options = { ...this.options, minimumFractionDigits: 0 };\n        return [\n            this.stripCurrency(this.normalizeDigits(integerNumber.toLocaleString(this.locale, { ...options, useGrouping: true })), false),\n            this.stripCurrency(this.normalizeDigits(integerNumber.toLocaleString(this.locale, { ...options, useGrouping: false })), false)\n        ].includes(formattedNumber);\n    }\n    format(value, options = {\n        minimumFractionDigits: this.minimumFractionDigits,\n        maximumFractionDigits: this.maximumFractionDigits\n    }) {\n        return value != null ? value.toLocaleString(this.locale, { ...this.options, ...options }) : '';\n    }\n    toFraction(str) {\n        return `${this.digits[0]}${this.decimalSymbol}${this.onlyLocaleDigits(str.substring(1)).substring(0, this.maximumFractionDigits)}`;\n    }\n    isFractionIncomplete(str) {\n        return !!this.normalizeDigits(this.stripGroupingSeparator(str)).match(new RegExp(`^${INTEGER_PATTERN}${escapeRegExp(this.decimalSymbol)}$`));\n    }\n    isNegative(str) {\n        return (str.startsWith(this.negativePrefix) ||\n            (this.minusSign === undefined && (str.startsWith('(') || str.startsWith('-'))) ||\n            (this.minusSign !== undefined && str.replace('-', this.minusSign).startsWith(this.minusSign)));\n    }\n    insertCurrency(str, negative) {\n        return `${negative ? this.negativePrefix : this.prefix}${str}${negative ? this.negativeSuffix : this.suffix}`;\n    }\n    stripGroupingSeparator(str) {\n        return this.groupingSymbol !== undefined ? str.replace(new RegExp(escapeRegExp(this.groupingSymbol), 'g'), '') : str;\n    }\n    stripSignLiterals(str) {\n        if (this.minusSign !== undefined) {\n            return str.replace('-', this.minusSign).replace(this.minusSign, '');\n        }\n        else {\n            return str.replace(/[-()]/g, '');\n        }\n    }\n    stripCurrency(str, negative) {\n        return str.replace(negative ? this.negativePrefix : this.prefix, '').replace(negative ? this.negativeSuffix : this.suffix, '');\n    }\n    normalizeDecimalSeparator(str, from) {\n        DECIMAL_SEPARATORS.forEach((s) => {\n            str = str.substring(0, from) + str.substring(from).replace(s, this.decimalSymbol);\n        });\n        return str;\n    }\n    normalizeDigits(str) {\n        if (this.digits[0] !== '0') {\n            this.digits.forEach((digit, index) => {\n                str = str.replace(new RegExp(digit, 'g'), String(index));\n            });\n        }\n        return str;\n    }\n    onlyDigits(str) {\n        return this.normalizeDigits(str).replace(/\\D+/g, '');\n    }\n    onlyLocaleDigits(str) {\n        return str.replace(new RegExp(`[^${this.digits.join('')}]*`, 'g'), '');\n    }\n}\n\nclass AbstractInputMask {\n    constructor(currencyFormat) {\n        this.currencyFormat = currencyFormat;\n    }\n}\nclass DefaultInputMask extends AbstractInputMask {\n    conformToMask(str, previousConformedValue = '') {\n        const negative = this.currencyFormat.isNegative(str);\n        const isEmptyNegativeValue = (str) => str === '' &&\n            negative &&\n            !(this.currencyFormat.minusSign === undefined\n                ? previousConformedValue === this.currencyFormat.negativePrefix + this.currencyFormat.negativeSuffix\n                : previousConformedValue === this.currencyFormat.negativePrefix);\n        const checkIncompleteValue = (str) => {\n            if (isEmptyNegativeValue(str)) {\n                return '';\n            }\n            else if (this.currencyFormat.maximumFractionDigits > 0) {\n                if (this.currencyFormat.isFractionIncomplete(str)) {\n                    return str;\n                }\n                else if (str.startsWith(this.currencyFormat.decimalSymbol)) {\n                    return this.currencyFormat.toFraction(str);\n                }\n            }\n            return null;\n        };\n        let value = str;\n        value = this.currencyFormat.stripCurrency(value, negative);\n        value = this.currencyFormat.stripSignLiterals(value);\n        const incompleteValue = checkIncompleteValue(value);\n        if (incompleteValue != null) {\n            return this.currencyFormat.insertCurrency(incompleteValue, negative);\n        }\n        const [integer, ...fraction] = value.split(this.currencyFormat.decimalSymbol);\n        const integerDigits = removeLeadingZeros(this.currencyFormat.onlyDigits(integer));\n        const fractionDigits = this.currencyFormat.onlyDigits(fraction.join('')).substring(0, this.currencyFormat.maximumFractionDigits);\n        const invalidFraction = fraction.length > 0 && fractionDigits.length === 0;\n        const invalidNegativeValue = integerDigits === '' &&\n            negative &&\n            (this.currencyFormat.minusSign === undefined\n                ? previousConformedValue === str.slice(0, -2) + this.currencyFormat.negativeSuffix\n                : previousConformedValue === str.slice(0, -1));\n        if (invalidFraction || invalidNegativeValue || isEmptyNegativeValue(integerDigits)) {\n            return previousConformedValue;\n        }\n        else if (integerDigits.match(/\\d+/)) {\n            return {\n                numberValue: Number(`${negative ? '-' : ''}${integerDigits}.${fractionDigits}`),\n                fractionDigits\n            };\n        }\n        else {\n            return '';\n        }\n    }\n}\nclass AutoDecimalDigitsInputMask extends AbstractInputMask {\n    conformToMask(str, previousConformedValue = '') {\n        if (str === '' ||\n            (this.currencyFormat.parse(previousConformedValue) === 0 &&\n                this.currencyFormat.stripCurrency(previousConformedValue, true).slice(0, -1) === this.currencyFormat.stripCurrency(str, true))) {\n            return '';\n        }\n        const negative = this.currencyFormat.isNegative(str);\n        const numberValue = this.currencyFormat.stripSignLiterals(str) === ''\n            ? -0\n            : Number(`${negative ? '-' : ''}${removeLeadingZeros(this.currencyFormat.onlyDigits(str))}`) / Math.pow(10, this.currencyFormat.maximumFractionDigits);\n        return {\n            numberValue,\n            fractionDigits: numberValue.toFixed(this.currencyFormat.maximumFractionDigits).slice(-this.currencyFormat.maximumFractionDigits)\n        };\n    }\n}\n\nconst DEFAULT_OPTIONS = {\n    locale: undefined,\n    currency: undefined,\n    currencyDisplay: undefined,\n    hideGroupingSeparatorOnFocus: true,\n    hideCurrencySymbolOnFocus: true,\n    hideNegligibleDecimalDigitsOnFocus: true,\n    precision: undefined,\n    autoDecimalDigits: false,\n    valueRange: undefined,\n    useGrouping: undefined,\n    valueScaling: undefined\n};\nclass CurrencyInput {\n    constructor(args) {\n        this.el = args.el;\n        this.onInput = args.onInput;\n        this.onChange = args.onChange;\n        this.addEventListener();\n        this.init(args.options);\n    }\n    setOptions(options) {\n        this.init(options);\n        this.format(this.currencyFormat.format(this.validateValueRange(this.numberValue)));\n        this.onChange(this.getValue());\n    }\n    getValue() {\n        const numberValue = this.valueScaling && this.numberValue != null ? this.toInteger(this.numberValue, this.valueScaling) : this.numberValue;\n        return { number: numberValue, formatted: this.formattedValue };\n    }\n    setValue(value) {\n        const newValue = this.valueScaling !== undefined && value != null ? this.toFloat(value, this.valueScaling) : value;\n        if (newValue !== this.numberValue) {\n            this.format(this.currencyFormat.format(this.validateValueRange(newValue)));\n            this.onChange(this.getValue());\n        }\n    }\n    init(options) {\n        this.options = {\n            ...DEFAULT_OPTIONS,\n            ...options\n        };\n        if (this.options.autoDecimalDigits) {\n            this.options.hideNegligibleDecimalDigitsOnFocus = false;\n        }\n        if (!this.el.getAttribute('inputmode')) {\n            this.el.setAttribute('inputmode', this.options.autoDecimalDigits ? 'numeric' : 'decimal');\n        }\n        this.currencyFormat = new CurrencyFormat(this.options);\n        this.numberMask = this.options.autoDecimalDigits ? new AutoDecimalDigitsInputMask(this.currencyFormat) : new DefaultInputMask(this.currencyFormat);\n        const valueScalingOptions = {\n            [ValueScaling.precision]: this.currencyFormat.maximumFractionDigits,\n            [ValueScaling.thousands]: 3,\n            [ValueScaling.tenThousands]: 4,\n            [ValueScaling.millions]: 6,\n            [ValueScaling.billions]: 9\n        };\n        this.valueScaling = this.options.valueScaling ? valueScalingOptions[this.options.valueScaling] : undefined;\n        this.valueScalingFractionDigits =\n            this.valueScaling !== undefined && this.options.valueScaling !== ValueScaling.precision\n                ? this.valueScaling + this.currencyFormat.maximumFractionDigits\n                : this.currencyFormat.maximumFractionDigits;\n        this.minValue = this.getMinValue();\n        this.maxValue = this.getMaxValue();\n    }\n    getMinValue() {\n        var _a, _b;\n        let min = this.toFloat(-Number.MAX_SAFE_INTEGER);\n        if (((_a = this.options.valueRange) === null || _a === void 0 ? void 0 : _a.min) !== undefined) {\n            min = Math.max((_b = this.options.valueRange) === null || _b === void 0 ? void 0 : _b.min, this.toFloat(-Number.MAX_SAFE_INTEGER));\n        }\n        return min;\n    }\n    getMaxValue() {\n        var _a, _b;\n        let max = this.toFloat(Number.MAX_SAFE_INTEGER);\n        if (((_a = this.options.valueRange) === null || _a === void 0 ? void 0 : _a.max) !== undefined) {\n            max = Math.min((_b = this.options.valueRange) === null || _b === void 0 ? void 0 : _b.max, this.toFloat(Number.MAX_SAFE_INTEGER));\n        }\n        return max;\n    }\n    toFloat(value, maxFractionDigits) {\n        return value / Math.pow(10, maxFractionDigits !== null && maxFractionDigits !== void 0 ? maxFractionDigits : this.valueScalingFractionDigits);\n    }\n    toInteger(value, maxFractionDigits) {\n        return Number(value\n            .toFixed(maxFractionDigits !== null && maxFractionDigits !== void 0 ? maxFractionDigits : this.valueScalingFractionDigits)\n            .split('.')\n            .join(''));\n    }\n    validateValueRange(value) {\n        return value != null ? Math.min(Math.max(value, this.minValue), this.maxValue) : value;\n    }\n    format(value, hideNegligibleDecimalDigits = false) {\n        if (value != null) {\n            if (this.decimalSymbolInsertedAt !== undefined) {\n                value = this.currencyFormat.normalizeDecimalSeparator(value, this.decimalSymbolInsertedAt);\n                this.decimalSymbolInsertedAt = undefined;\n            }\n            const conformedValue = this.numberMask.conformToMask(value, this.formattedValue);\n            let formattedValue;\n            if (typeof conformedValue === 'object') {\n                const { numberValue, fractionDigits } = conformedValue;\n                let { maximumFractionDigits, minimumFractionDigits } = this.currencyFormat;\n                if (this.focus) {\n                    minimumFractionDigits = hideNegligibleDecimalDigits\n                        ? fractionDigits.replace(/0+$/, '').length\n                        : Math.min(maximumFractionDigits, fractionDigits.length);\n                }\n                else if (Number.isInteger(numberValue) && !this.options.autoDecimalDigits && (this.options.precision === undefined || minimumFractionDigits === 0)) {\n                    minimumFractionDigits = maximumFractionDigits = 0;\n                }\n                formattedValue =\n                    this.toInteger(Math.abs(numberValue)) > Number.MAX_SAFE_INTEGER\n                        ? this.formattedValue\n                        : this.currencyFormat.format(numberValue, {\n                            useGrouping: this.options.useGrouping !== false && !(this.focus && this.options.hideGroupingSeparatorOnFocus),\n                            minimumFractionDigits,\n                            maximumFractionDigits\n                        });\n            }\n            else {\n                formattedValue = conformedValue;\n            }\n            if (this.maxValue <= 0 && !this.currencyFormat.isNegative(formattedValue) && this.currencyFormat.parse(formattedValue) !== 0) {\n                formattedValue = formattedValue.replace(this.currencyFormat.prefix, this.currencyFormat.negativePrefix);\n            }\n            if (this.minValue >= 0) {\n                formattedValue = formattedValue.replace(this.currencyFormat.negativePrefix, this.currencyFormat.prefix);\n            }\n            if (this.options.currencyDisplay === CurrencyDisplay.hidden || (this.focus && this.options.hideCurrencySymbolOnFocus)) {\n                formattedValue = formattedValue\n                    .replace(this.currencyFormat.negativePrefix, this.currencyFormat.minusSign !== undefined ? this.currencyFormat.minusSign : '(')\n                    .replace(this.currencyFormat.negativeSuffix, this.currencyFormat.minusSign !== undefined ? '' : ')')\n                    .replace(this.currencyFormat.prefix, '')\n                    .replace(this.currencyFormat.suffix, '');\n            }\n            this.el.value = formattedValue;\n            this.numberValue = this.currencyFormat.parse(formattedValue);\n        }\n        else {\n            this.el.value = '';\n            this.numberValue = null;\n        }\n        this.formattedValue = this.el.value;\n        this.onInput(this.getValue());\n    }\n    addEventListener() {\n        this.el.addEventListener('input', (e) => {\n            const { value, selectionStart } = this.el;\n            const inputEvent = e;\n            if (selectionStart && inputEvent.data && DECIMAL_SEPARATORS.includes(inputEvent.data)) {\n                this.decimalSymbolInsertedAt = selectionStart - 1;\n            }\n            this.format(value);\n            if (this.focus && selectionStart != null) {\n                const getCaretPositionAfterFormat = () => {\n                    const { prefix, suffix, decimalSymbol, maximumFractionDigits, groupingSymbol } = this.currencyFormat;\n                    let caretPositionFromLeft = value.length - selectionStart;\n                    const newValueLength = this.formattedValue.length;\n                    if (this.currencyFormat.minusSign === undefined && (value.startsWith('(') || value.startsWith('-')) && !value.endsWith(')')) {\n                        return newValueLength - this.currencyFormat.negativeSuffix.length > 1 ? this.formattedValue.substring(selectionStart).length : 1;\n                    }\n                    if (this.formattedValue.substring(selectionStart, 1) === groupingSymbol &&\n                        count(this.formattedValue, groupingSymbol) === count(value, groupingSymbol) + 1) {\n                        return newValueLength - caretPositionFromLeft - 1;\n                    }\n                    if (newValueLength < caretPositionFromLeft) {\n                        return selectionStart;\n                    }\n                    if (decimalSymbol !== undefined && value.indexOf(decimalSymbol) !== -1) {\n                        const decimalSymbolPosition = value.indexOf(decimalSymbol) + 1;\n                        if (Math.abs(newValueLength - value.length) > 1 && selectionStart <= decimalSymbolPosition) {\n                            return this.formattedValue.indexOf(decimalSymbol) + 1;\n                        }\n                        else {\n                            if (!this.options.autoDecimalDigits && selectionStart > decimalSymbolPosition) {\n                                if (this.currencyFormat.onlyDigits(value.substring(decimalSymbolPosition)).length - 1 === maximumFractionDigits) {\n                                    caretPositionFromLeft -= 1;\n                                }\n                            }\n                        }\n                    }\n                    return this.options.hideCurrencySymbolOnFocus || this.options.currencyDisplay === CurrencyDisplay.hidden\n                        ? newValueLength - caretPositionFromLeft\n                        : Math.max(newValueLength - Math.max(caretPositionFromLeft, suffix.length), prefix.length);\n                };\n                this.setCaretPosition(getCaretPositionAfterFormat());\n            }\n        });\n        this.el.addEventListener('focus', () => {\n            this.focus = true;\n            this.numberValueOnFocus = this.numberValue;\n            setTimeout(() => {\n                const { value, selectionStart, selectionEnd } = this.el;\n                this.format(value, this.options.hideNegligibleDecimalDigitsOnFocus);\n                if (selectionStart != null && selectionEnd != null && Math.abs(selectionStart - selectionEnd) > 0) {\n                    this.setCaretPosition(0, this.el.value.length);\n                }\n                else if (selectionStart != null) {\n                    const caretPositionOnFocus = this.getCaretPositionOnFocus(value, selectionStart);\n                    this.setCaretPosition(caretPositionOnFocus);\n                }\n            });\n        });\n        this.el.addEventListener('blur', () => {\n            this.focus = false;\n            this.format(this.currencyFormat.format(this.validateValueRange(this.numberValue)));\n            if (this.numberValueOnFocus !== this.numberValue) {\n                this.onChange(this.getValue());\n            }\n        });\n    }\n    getCaretPositionOnFocus(value, selectionStart) {\n        if (this.numberValue == null) {\n            return selectionStart;\n        }\n        const { prefix, negativePrefix, suffix, negativeSuffix, groupingSymbol, currency } = this.currencyFormat;\n        const isNegative = this.numberValue < 0;\n        const currentPrefix = isNegative ? negativePrefix : prefix;\n        const prefixLength = currentPrefix.length;\n        if (this.options.hideCurrencySymbolOnFocus || this.options.currencyDisplay === CurrencyDisplay.hidden) {\n            if (isNegative) {\n                if (selectionStart <= 1) {\n                    return 1;\n                }\n                else if (value.endsWith(')') && selectionStart > value.indexOf(')')) {\n                    return this.formattedValue.length - 1;\n                }\n            }\n        }\n        else {\n            const suffixLength = isNegative ? negativeSuffix.length : suffix.length;\n            if (selectionStart >= value.length - suffixLength) {\n                return this.formattedValue.length - suffixLength;\n            }\n            else if (selectionStart < prefixLength) {\n                return prefixLength;\n            }\n        }\n        let result = selectionStart;\n        if (this.options.hideCurrencySymbolOnFocus &&\n            this.options.currencyDisplay !== CurrencyDisplay.hidden &&\n            selectionStart >= prefixLength &&\n            currency !== undefined &&\n            currentPrefix.includes(currency)) {\n            result -= prefixLength;\n            if (isNegative) {\n                result += 1;\n            }\n        }\n        if (this.options.hideGroupingSeparatorOnFocus && groupingSymbol !== undefined) {\n            result -= count(value.substring(0, selectionStart), groupingSymbol);\n        }\n        return result;\n    }\n    setCaretPosition(start, end = start) {\n        this.el.setSelectionRange(start, end);\n    }\n}\n\nconst findInput = (el) => ((el === null || el === void 0 ? void 0 : el.matches('input')) ? el : el === null || el === void 0 ? void 0 : el.querySelector('input'));\nfunction useCurrencyInput(options, autoEmit) {\n    var _a, _b, _c, _d;\n    let currencyInput;\n    const inputRef = ref(null);\n    const formattedValue = ref(null);\n    const numberValue = ref(null);\n    const vm = getCurrentInstance();\n    const emit = (vm === null || vm === void 0 ? void 0 : vm.emit) || ((_b = (_a = vm === null || vm === void 0 ? void 0 : vm.proxy) === null || _a === void 0 ? void 0 : _a.$emit) === null || _b === void 0 ? void 0 : _b.bind(vm === null || vm === void 0 ? void 0 : vm.proxy));\n    const props = ((vm === null || vm === void 0 ? void 0 : vm.props) || ((_c = vm === null || vm === void 0 ? void 0 : vm.proxy) === null || _c === void 0 ? void 0 : _c.$props));\n    const isVue3 = version.startsWith('3');\n    const lazyModel = isVue3 && ((_d = vm === null || vm === void 0 ? void 0 : vm.attrs.modelModifiers) === null || _d === void 0 ? void 0 : _d.lazy);\n    const modelValue = computed(() => props === null || props === void 0 ? void 0 : props[isVue3 ? 'modelValue' : 'value']);\n    const inputEvent = isVue3 ? 'update:modelValue' : 'input';\n    const changeEvent = lazyModel ? 'update:modelValue' : 'change';\n    watch(inputRef, (value) => {\n        var _a;\n        if (value) {\n            const el = findInput((_a = value === null || value === void 0 ? void 0 : value.$el) !== null && _a !== void 0 ? _a : value);\n            if (el) {\n                currencyInput = new CurrencyInput({\n                    el,\n                    options,\n                    onInput: (value) => {\n                        if (!lazyModel && autoEmit !== false && modelValue.value !== value.number) {\n                            emit === null || emit === void 0 ? void 0 : emit(inputEvent, value.number);\n                        }\n                        numberValue.value = value.number;\n                        formattedValue.value = value.formatted;\n                    },\n                    onChange: (value) => {\n                        if (autoEmit !== false) {\n                            emit === null || emit === void 0 ? void 0 : emit(changeEvent, value.number);\n                        }\n                    }\n                });\n                currencyInput.setValue(modelValue.value);\n            }\n            else {\n                console.error('No input element found. Please make sure that the \"inputRef\" template ref is properly assigned.');\n            }\n        }\n        else {\n            currencyInput = null;\n        }\n    });\n    return {\n        inputRef,\n        numberValue,\n        formattedValue,\n        setValue: (value) => currencyInput === null || currencyInput === void 0 ? void 0 : currencyInput.setValue(value),\n        setOptions: (options) => currencyInput === null || currencyInput === void 0 ? void 0 : currencyInput.setOptions(options)\n    };\n}\n\nexport { CurrencyDisplay, ValueScaling, useCurrencyInput };\n", "import { defineComponent as I, toRefs as N, computed as y, ref as D, watch as V, createElementBlock as b, openBlock as _, unref as k, createElementVNode as G } from \"vue\";\nimport { CurrencyDisplay as O, useCurrencyInput as B } from \"vue-currency-input\";\nimport { formatAmount as h } from \"@perkd/applet-common/utils\";\nconst E = { key: 1 }, R = [\"value\"], A = /* @__PURE__ */ I({\n  __name: \"CurrencyInput\",\n  props: {\n    modelValue: {\n      type: Number\n    },\n    currencyConfig: {\n      type: Object\n    }\n  },\n  emits: [\"update:modelValue\", \"focus\", \"blur\", \"change\"],\n  setup(S, { expose: g, emit: x }) {\n    const i = S, { currencyConfig: e } = N(i), o = y(() => {\n      var u, r, t, p, f;\n      return {\n        locale: ((u = e == null ? void 0 : e.value) == null ? void 0 : u.locale) || \"en-SG\",\n        currency: ((r = e == null ? void 0 : e.value) == null ? void 0 : r.currency) || \"SGD\",\n        precision: ((t = e == null ? void 0 : e.value) == null ? void 0 : t.precision) ?? 2,\n        currencyDisplay: ((p = e == null ? void 0 : e.value) == null ? void 0 : p.currencyDisplay) || O.narrowSymbol,\n        hideCurrencySymbolOnFocus: !1,\n        hideGroupingSeparatorOnFocus: !1,\n        hideNegligibleDecimalDigitsOnFocus: !1,\n        autoDecimalDigits: ((f = e == null ? void 0 : e.value) == null ? void 0 : f.autoDecimalDigits) ?? !0,\n        useGrouping: !0,\n        accountingSign: !1\n      };\n    }), n = \"formatToParts\" in Intl.NumberFormat.prototype, l = n ? B(o.value) : null, a = D(i.modelValue), F = y(() => Math.pow(10, o.value.precision)), s = D(i.modelValue ? `${i.modelValue}` : \"\"), c = x;\n    V(() => i.modelValue, (u) => {\n      n ? l == null || l.setValue(u ?? null) : a.value = u ?? void 0;\n    }), V(a, (u) => {\n      var r, t, p;\n      n || (s.value = u ? o.value.autoDecimalDigits ? h(a == null ? void 0 : a.value, (r = o.value) == null ? void 0 : r.currency, (t = o.value) == null ? void 0 : t.precision, (p = o.value) == null ? void 0 : p.precision) : String(u) : \"\");\n    });\n    function m(u) {\n      if (n) {\n        const t = l == null ? void 0 : l.numberValue.value;\n        c(\"update:modelValue\", t);\n        return;\n      }\n      const r = u.target.value.replace(/[\\D]/g, \"\");\n      a.value = Number(r) / F.value, s.value = h(a.value, o.value.currency, o.value.precision, o.value.precision), c(\"update:modelValue\", a.value);\n    }\n    function v(u) {\n      c(\"focus\", u);\n    }\n    function d(u) {\n      c(\"blur\", u);\n    }\n    return g({\n      value: n ? l == null ? void 0 : l.numberValue : a,\n      formattedValue: n ? l == null ? void 0 : l.formattedValue : s\n    }), (u, r) => {\n      var t;\n      return n ? (_(), b(\"input\", {\n        key: 0,\n        ref: (t = k(l)) == null ? void 0 : t.inputRef,\n        type: \"text\",\n        onInput: m,\n        onBlur: d,\n        onFocus: v\n      }, null, 544)) : (_(), b(\"div\", E, [\n        G(\"input\", {\n          type: \"text\",\n          inputmode: \"numeric\",\n          value: s.value,\n          onInput: m,\n          onBlur: d,\n          onFocus: v\n        }, null, 40, R)\n      ]));\n    };\n  }\n});\nexport {\n  A as default\n};\n", "import { defineComponent as q, toRefs as I, ref as o, computed as j, watch as V, createElementBlock as F, openBlock as U, createVNode as A, mergeProps as E, unref as u } from \"vue\";\nimport { formatAmount as S } from \"@perkd/applet-common/utils\";\nimport O from \"./CurrencyInput.js\";\nconst G = /* @__PURE__ */ q({\n  __name: \"UIInputMoney\",\n  props: {\n    value: {\n      type: String,\n      default: \"\"\n    },\n    type: {\n      type: String,\n      default: \"text\"\n    },\n    disabled: {\n      type: Boolean,\n      default: !1\n    },\n    required: {\n      type: Boolean,\n      default: !1\n    },\n    placeholder: {\n      type: String,\n      default: \"\"\n    },\n    min: {\n      type: String,\n      default: \"\"\n    },\n    max: {\n      type: String,\n      default: \"\"\n    },\n    currencyConfig: {\n      type: Object\n    }\n  },\n  emits: [\"input\", \"clearInput\", \"blur\", \"focus\", \"focusChange\"],\n  setup(h, { expose: N, emit: k }) {\n    var g, _;\n    const B = h, { value: d, disabled: C, required: R, placeholder: w, min: n, max: l, currencyConfig: r } = I(B), v = o(!1), y = o(\"\"), i = o(void 0), s = o(void 0), M = j(() => s.value ? s.value.formattedValue : \"\"), t = o(Number((_ = (g = d.value) == null ? void 0 : g.match(/[0-9]*/g)) == null ? void 0 : _.join(\"\")) || void 0), f = k;\n    V(d, (e) => {\n      e === \"\" && (t.value = void 0, p());\n    }), V(t, (e) => {\n      f(\"input\", { target: i.value }, String(e ?? \"\"));\n    });\n    function b() {\n      v.value = !0, f(\"focus\", { target: i.value });\n    }\n    function p() {\n      v.value = !1, f(\"blur\", { target: i.value });\n    }\n    function $() {\n      const e = t.value || 0, a = n.value ? e >= Number(n.value) : !0, m = l.value ? e <= Number(l.value) : !0, { currency: x, precision: c } = (r == null ? void 0 : r.value) || {};\n      return y.value = a ? m ? \"\" : `below_maximum_amount|amount:${S(Number(l.value), x, c, c)}` : `above_minimum_amount|amount:${S(Number(n.value), x, c, c)}`, a && m;\n    }\n    return N({\n      value: String(t.value || \"\"),\n      formattedValue: M,\n      focus: b,\n      blur: p,\n      checkValidity: $,\n      validationMessage: y\n    }), (e, a) => (U(), F(\"div\", {\n      class: \"input-wrapper--money\",\n      ref_key: \"inputContainerRef\",\n      ref: i\n    }, [\n      A(O, E({\n        ref_key: \"inputRef\",\n        ref: s,\n        type: \"text\",\n        modelValue: t.value,\n        \"onUpdate:modelValue\": a[0] || (a[0] = (m) => t.value = m)\n      }, { disabled: u(C), required: u(R), placeholder: u(w), min: u(n), max: u(l), currencyConfig: u(r) }, {\n        onFocus: b,\n        onBlur: p\n      }), null, 16, [\"modelValue\"])\n    ], 512));\n  }\n});\nexport {\n  G as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAOA,IAAI;AAAA,CACH,SAAUA,kBAAiB;AACxB,EAAAA,iBAAgB,QAAQ,IAAI;AAC5B,EAAAA,iBAAgB,cAAc,IAAI;AAClC,EAAAA,iBAAgB,MAAM,IAAI;AAC1B,EAAAA,iBAAgB,MAAM,IAAI;AAC1B,EAAAA,iBAAgB,QAAQ,IAAI;AAChC,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAUC,eAAc;AACrB,EAAAA,cAAa,WAAW,IAAI;AAC5B,EAAAA,cAAa,WAAW,IAAI;AAC5B,EAAAA,cAAa,cAAc,IAAI;AAC/B,EAAAA,cAAa,UAAU,IAAI;AAC3B,EAAAA,cAAa,UAAU,IAAI;AAC/B,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAEtC,IAAM,eAAe,CAAC,QAAQ;AAC1B,SAAO,IAAI,QAAQ,uBAAuB,MAAM;AACpD;AACA,IAAM,qBAAqB,CAAC,QAAQ;AAChC,SAAO,IAAI,QAAQ,gBAAgB,IAAI;AAC3C;AACA,IAAM,QAAQ,CAAC,KAAK,WAAW;AAC3B,UAAQ,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG;AACpE;AACA,IAAM,kBAAkB,CAAC,KAAK,WAAW;AACrC,SAAO,IAAI,UAAU,GAAG,IAAI,QAAQ,MAAM,CAAC;AAC/C;AAEA,IAAM,qBAAqB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,kBAAkB;AACxB,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,SAAS;AACjB,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,UAAM,EAAE,UAAU,iBAAiB,QAAQ,WAAW,gBAAgB,YAAY,IAAI;AACtF,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,MACX;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,cAAc,iBAAiB,eAAe;AAAA,MAC9C,iBAAiB,oBAAoB,gBAAgB,SAAS,kBAAkB;AAAA,IACpF;AACA,UAAM,eAAe,IAAI,KAAK,aAAa,QAAQ,KAAK,OAAO;AAC/D,UAAM,cAAc,aAAa,cAAc,MAAM;AACrD,SAAK,YAAY,KAAK,YAAY,KAAK,CAAC,EAAE,KAAK,MAAM,SAAS,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACnH,SAAK,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,MAAM,CAAC;AAChF,SAAK,iBAAiB,KAAK,YAAY,KAAK,CAAC,EAAE,KAAK,MAAM,SAAS,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACvH,SAAK,kBAAkB,KAAK,YAAY,KAAK,CAAC,EAAE,KAAK,MAAM,SAAS,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACtH,SAAK,aAAa,KAAK,aAAa,cAAc,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,SAAS,WAAW,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACxI,QAAI,KAAK,kBAAkB,QAAW;AAClC,WAAK,wBAAwB,KAAK,wBAAwB;AAAA,IAC9D,WACS,OAAO,cAAc,UAAU;AACpC,WAAK,wBAAwB,KAAK,wBAAwB;AAAA,IAC9D,OACK;AACD,WAAK,yBAAyB,KAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS,QAAQ,OAAO,SAAS,KAAK,aAAa,gBAAgB,EAAE;AACxK,WAAK,yBAAyB,KAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS,QAAQ,OAAO,SAAS,KAAK,aAAa,gBAAgB,EAAE;AAAA,IAC5K;AACA,UAAM,YAAY,CAAC,QAAQ;AACvB,aAAO,gBAAgB,KAAK,KAAK,OAAO,CAAC,CAAC;AAAA,IAC9C;AACA,UAAM,YAAY,CAAC,QAAQ;AACvB,aAAO,IAAI,UAAU,IAAI,YAAY,KAAK,gBAAgB,KAAK,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,IAAI,CAAC;AAAA,IAClG;AACA,SAAK,SAAS,UAAU,aAAa,OAAO,CAAC,CAAC;AAC9C,SAAK,SAAS,UAAU,aAAa,OAAO,CAAC,CAAC;AAC9C,SAAK,iBAAiB,UAAU,aAAa,OAAO,EAAE,CAAC;AACvD,SAAK,iBAAiB,UAAU,aAAa,OAAO,EAAE,CAAC;AAAA,EAC3D;AAAA,EACA,MAAM,KAAK;AACP,QAAI,KAAK;AACL,YAAM,WAAW,KAAK,WAAW,GAAG;AACpC,YAAM,KAAK,gBAAgB,GAAG;AAC9B,YAAM,KAAK,cAAc,KAAK,QAAQ;AACtC,YAAM,KAAK,kBAAkB,GAAG;AAChC,YAAM,WAAW,KAAK,gBAAgB,MAAM,aAAa,KAAK,aAAa,CAAC,aAAa;AACzF,YAAM,QAAQ,KAAK,uBAAuB,GAAG,EAAE,MAAM,IAAI,OAAO,IAAI,eAAe,GAAG,QAAQ,GAAG,CAAC;AAClG,UAAI,SAAS,KAAK,qBAAqB,KAAK,gBAAgB,IAAI,MAAM,KAAK,aAAa,EAAE,CAAC,IAAI,KAAK,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG;AACnH,eAAO,OAAO,GAAG,WAAW,MAAM,EAAE,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;AAAA,MACzG;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,iBAAiB,eAAe;AACjD,UAAM,UAAU,EAAE,GAAG,KAAK,SAAS,uBAAuB,EAAE;AAC5D,WAAO;AAAA,MACH,KAAK,cAAc,KAAK,gBAAgB,cAAc,eAAe,KAAK,QAAQ,EAAE,GAAG,SAAS,aAAa,KAAK,CAAC,CAAC,GAAG,KAAK;AAAA,MAC5H,KAAK,cAAc,KAAK,gBAAgB,cAAc,eAAe,KAAK,QAAQ,EAAE,GAAG,SAAS,aAAa,MAAM,CAAC,CAAC,GAAG,KAAK;AAAA,IACjI,EAAE,SAAS,eAAe;AAAA,EAC9B;AAAA,EACA,OAAO,OAAO,UAAU;AAAA,IACpB,uBAAuB,KAAK;AAAA,IAC5B,uBAAuB,KAAK;AAAA,EAChC,GAAG;AACC,WAAO,SAAS,OAAO,MAAM,eAAe,KAAK,QAAQ,EAAE,GAAG,KAAK,SAAS,GAAG,QAAQ,CAAC,IAAI;AAAA,EAChG;AAAA,EACA,WAAW,KAAK;AACZ,WAAO,GAAG,KAAK,OAAO,CAAC,CAAC,GAAG,KAAK,aAAa,GAAG,KAAK,iBAAiB,IAAI,UAAU,CAAC,CAAC,EAAE,UAAU,GAAG,KAAK,qBAAqB,CAAC;AAAA,EACpI;AAAA,EACA,qBAAqB,KAAK;AACtB,WAAO,CAAC,CAAC,KAAK,gBAAgB,KAAK,uBAAuB,GAAG,CAAC,EAAE,MAAM,IAAI,OAAO,IAAI,eAAe,GAAG,aAAa,KAAK,aAAa,CAAC,GAAG,CAAC;AAAA,EAC/I;AAAA,EACA,WAAW,KAAK;AACZ,WAAQ,IAAI,WAAW,KAAK,cAAc,KACrC,KAAK,cAAc,WAAc,IAAI,WAAW,GAAG,KAAK,IAAI,WAAW,GAAG,MAC1E,KAAK,cAAc,UAAa,IAAI,QAAQ,KAAK,KAAK,SAAS,EAAE,WAAW,KAAK,SAAS;AAAA,EACnG;AAAA,EACA,eAAe,KAAK,UAAU;AAC1B,WAAO,GAAG,WAAW,KAAK,iBAAiB,KAAK,MAAM,GAAG,GAAG,GAAG,WAAW,KAAK,iBAAiB,KAAK,MAAM;AAAA,EAC/G;AAAA,EACA,uBAAuB,KAAK;AACxB,WAAO,KAAK,mBAAmB,SAAY,IAAI,QAAQ,IAAI,OAAO,aAAa,KAAK,cAAc,GAAG,GAAG,GAAG,EAAE,IAAI;AAAA,EACrH;AAAA,EACA,kBAAkB,KAAK;AACnB,QAAI,KAAK,cAAc,QAAW;AAC9B,aAAO,IAAI,QAAQ,KAAK,KAAK,SAAS,EAAE,QAAQ,KAAK,WAAW,EAAE;AAAA,IACtE,OACK;AACD,aAAO,IAAI,QAAQ,UAAU,EAAE;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,cAAc,KAAK,UAAU;AACzB,WAAO,IAAI,QAAQ,WAAW,KAAK,iBAAiB,KAAK,QAAQ,EAAE,EAAE,QAAQ,WAAW,KAAK,iBAAiB,KAAK,QAAQ,EAAE;AAAA,EACjI;AAAA,EACA,0BAA0B,KAAK,MAAM;AACjC,uBAAmB,QAAQ,CAAC,MAAM;AAC9B,YAAM,IAAI,UAAU,GAAG,IAAI,IAAI,IAAI,UAAU,IAAI,EAAE,QAAQ,GAAG,KAAK,aAAa;AAAA,IACpF,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,KAAK;AACjB,QAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AACxB,WAAK,OAAO,QAAQ,CAAC,OAAO,UAAU;AAClC,cAAM,IAAI,QAAQ,IAAI,OAAO,OAAO,GAAG,GAAG,OAAO,KAAK,CAAC;AAAA,MAC3D,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AAAA,EACA,WAAW,KAAK;AACZ,WAAO,KAAK,gBAAgB,GAAG,EAAE,QAAQ,QAAQ,EAAE;AAAA,EACvD;AAAA,EACA,iBAAiB,KAAK;AAClB,WAAO,IAAI,QAAQ,IAAI,OAAO,KAAK,KAAK,OAAO,KAAK,EAAE,CAAC,MAAM,GAAG,GAAG,EAAE;AAAA,EACzE;AACJ;AAEA,IAAM,oBAAN,MAAwB;AAAA,EACpB,YAAY,gBAAgB;AACxB,SAAK,iBAAiB;AAAA,EAC1B;AACJ;AACA,IAAM,mBAAN,cAA+B,kBAAkB;AAAA,EAC7C,cAAc,KAAK,yBAAyB,IAAI;AAC5C,UAAM,WAAW,KAAK,eAAe,WAAW,GAAG;AACnD,UAAM,uBAAuB,CAACC,SAAQA,SAAQ,MAC1C,YACA,EAAE,KAAK,eAAe,cAAc,SAC9B,2BAA2B,KAAK,eAAe,iBAAiB,KAAK,eAAe,iBACpF,2BAA2B,KAAK,eAAe;AACzD,UAAM,uBAAuB,CAACA,SAAQ;AAClC,UAAI,qBAAqBA,IAAG,GAAG;AAC3B,eAAO;AAAA,MACX,WACS,KAAK,eAAe,wBAAwB,GAAG;AACpD,YAAI,KAAK,eAAe,qBAAqBA,IAAG,GAAG;AAC/C,iBAAOA;AAAA,QACX,WACSA,KAAI,WAAW,KAAK,eAAe,aAAa,GAAG;AACxD,iBAAO,KAAK,eAAe,WAAWA,IAAG;AAAA,QAC7C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,QAAI,QAAQ;AACZ,YAAQ,KAAK,eAAe,cAAc,OAAO,QAAQ;AACzD,YAAQ,KAAK,eAAe,kBAAkB,KAAK;AACnD,UAAM,kBAAkB,qBAAqB,KAAK;AAClD,QAAI,mBAAmB,MAAM;AACzB,aAAO,KAAK,eAAe,eAAe,iBAAiB,QAAQ;AAAA,IACvE;AACA,UAAM,CAAC,SAAS,GAAG,QAAQ,IAAI,MAAM,MAAM,KAAK,eAAe,aAAa;AAC5E,UAAM,gBAAgB,mBAAmB,KAAK,eAAe,WAAW,OAAO,CAAC;AAChF,UAAM,iBAAiB,KAAK,eAAe,WAAW,SAAS,KAAK,EAAE,CAAC,EAAE,UAAU,GAAG,KAAK,eAAe,qBAAqB;AAC/H,UAAM,kBAAkB,SAAS,SAAS,KAAK,eAAe,WAAW;AACzE,UAAM,uBAAuB,kBAAkB,MAC3C,aACC,KAAK,eAAe,cAAc,SAC7B,2BAA2B,IAAI,MAAM,GAAG,EAAE,IAAI,KAAK,eAAe,iBAClE,2BAA2B,IAAI,MAAM,GAAG,EAAE;AACpD,QAAI,mBAAmB,wBAAwB,qBAAqB,aAAa,GAAG;AAChF,aAAO;AAAA,IACX,WACS,cAAc,MAAM,KAAK,GAAG;AACjC,aAAO;AAAA,QACH,aAAa,OAAO,GAAG,WAAW,MAAM,EAAE,GAAG,aAAa,IAAI,cAAc,EAAE;AAAA,QAC9E;AAAA,MACJ;AAAA,IACJ,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,IAAM,6BAAN,cAAyC,kBAAkB;AAAA,EACvD,cAAc,KAAK,yBAAyB,IAAI;AAC5C,QAAI,QAAQ,MACP,KAAK,eAAe,MAAM,sBAAsB,MAAM,KACnD,KAAK,eAAe,cAAc,wBAAwB,IAAI,EAAE,MAAM,GAAG,EAAE,MAAM,KAAK,eAAe,cAAc,KAAK,IAAI,GAAI;AACpI,aAAO;AAAA,IACX;AACA,UAAM,WAAW,KAAK,eAAe,WAAW,GAAG;AACnD,UAAM,cAAc,KAAK,eAAe,kBAAkB,GAAG,MAAM,KAC7D,KACA,OAAO,GAAG,WAAW,MAAM,EAAE,GAAG,mBAAmB,KAAK,eAAe,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,KAAK,IAAI,IAAI,KAAK,eAAe,qBAAqB;AACzJ,WAAO;AAAA,MACH;AAAA,MACA,gBAAgB,YAAY,QAAQ,KAAK,eAAe,qBAAqB,EAAE,MAAM,CAAC,KAAK,eAAe,qBAAqB;AAAA,IACnI;AAAA,EACJ;AACJ;AAEA,IAAM,kBAAkB;AAAA,EACpB,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAClB;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,MAAM;AACd,SAAK,KAAK,KAAK;AACf,SAAK,UAAU,KAAK;AACpB,SAAK,WAAW,KAAK;AACrB,SAAK,iBAAiB;AACtB,SAAK,KAAK,KAAK,OAAO;AAAA,EAC1B;AAAA,EACA,WAAW,SAAS;AAChB,SAAK,KAAK,OAAO;AACjB,SAAK,OAAO,KAAK,eAAe,OAAO,KAAK,mBAAmB,KAAK,WAAW,CAAC,CAAC;AACjF,SAAK,SAAS,KAAK,SAAS,CAAC;AAAA,EACjC;AAAA,EACA,WAAW;AACP,UAAM,cAAc,KAAK,gBAAgB,KAAK,eAAe,OAAO,KAAK,UAAU,KAAK,aAAa,KAAK,YAAY,IAAI,KAAK;AAC/H,WAAO,EAAE,QAAQ,aAAa,WAAW,KAAK,eAAe;AAAA,EACjE;AAAA,EACA,SAAS,OAAO;AACZ,UAAM,WAAW,KAAK,iBAAiB,UAAa,SAAS,OAAO,KAAK,QAAQ,OAAO,KAAK,YAAY,IAAI;AAC7G,QAAI,aAAa,KAAK,aAAa;AAC/B,WAAK,OAAO,KAAK,eAAe,OAAO,KAAK,mBAAmB,QAAQ,CAAC,CAAC;AACzE,WAAK,SAAS,KAAK,SAAS,CAAC;AAAA,IACjC;AAAA,EACJ;AAAA,EACA,KAAK,SAAS;AACV,SAAK,UAAU;AAAA,MACX,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AACA,QAAI,KAAK,QAAQ,mBAAmB;AAChC,WAAK,QAAQ,qCAAqC;AAAA,IACtD;AACA,QAAI,CAAC,KAAK,GAAG,aAAa,WAAW,GAAG;AACpC,WAAK,GAAG,aAAa,aAAa,KAAK,QAAQ,oBAAoB,YAAY,SAAS;AAAA,IAC5F;AACA,SAAK,iBAAiB,IAAI,eAAe,KAAK,OAAO;AACrD,SAAK,aAAa,KAAK,QAAQ,oBAAoB,IAAI,2BAA2B,KAAK,cAAc,IAAI,IAAI,iBAAiB,KAAK,cAAc;AACjJ,UAAM,sBAAsB;AAAA,MACxB,CAAC,aAAa,SAAS,GAAG,KAAK,eAAe;AAAA,MAC9C,CAAC,aAAa,SAAS,GAAG;AAAA,MAC1B,CAAC,aAAa,YAAY,GAAG;AAAA,MAC7B,CAAC,aAAa,QAAQ,GAAG;AAAA,MACzB,CAAC,aAAa,QAAQ,GAAG;AAAA,IAC7B;AACA,SAAK,eAAe,KAAK,QAAQ,eAAe,oBAAoB,KAAK,QAAQ,YAAY,IAAI;AACjG,SAAK,6BACD,KAAK,iBAAiB,UAAa,KAAK,QAAQ,iBAAiB,aAAa,YACxE,KAAK,eAAe,KAAK,eAAe,wBACxC,KAAK,eAAe;AAC9B,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,WAAW,KAAK,YAAY;AAAA,EACrC;AAAA,EACA,cAAc;AACV,QAAI,IAAI;AACR,QAAI,MAAM,KAAK,QAAQ,CAAC,OAAO,gBAAgB;AAC/C,UAAM,KAAK,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,QAAW;AAC5F,YAAM,KAAK,KAAK,KAAK,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,QAAQ,CAAC,OAAO,gBAAgB,CAAC;AAAA,IACrI;AACA,WAAO;AAAA,EACX;AAAA,EACA,cAAc;AACV,QAAI,IAAI;AACR,QAAI,MAAM,KAAK,QAAQ,OAAO,gBAAgB;AAC9C,UAAM,KAAK,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,QAAW;AAC5F,YAAM,KAAK,KAAK,KAAK,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,QAAQ,OAAO,gBAAgB,CAAC;AAAA,IACpI;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,OAAO,mBAAmB;AAC9B,WAAO,QAAQ,KAAK,IAAI,IAAI,sBAAsB,QAAQ,sBAAsB,SAAS,oBAAoB,KAAK,0BAA0B;AAAA,EAChJ;AAAA,EACA,UAAU,OAAO,mBAAmB;AAChC,WAAO,OAAO,MACT,QAAQ,sBAAsB,QAAQ,sBAAsB,SAAS,oBAAoB,KAAK,0BAA0B,EACxH,MAAM,GAAG,EACT,KAAK,EAAE,CAAC;AAAA,EACjB;AAAA,EACA,mBAAmB,OAAO;AACtB,WAAO,SAAS,OAAO,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK,QAAQ,GAAG,KAAK,QAAQ,IAAI;AAAA,EACrF;AAAA,EACA,OAAO,OAAO,8BAA8B,OAAO;AAC/C,QAAI,SAAS,MAAM;AACf,UAAI,KAAK,4BAA4B,QAAW;AAC5C,gBAAQ,KAAK,eAAe,0BAA0B,OAAO,KAAK,uBAAuB;AACzF,aAAK,0BAA0B;AAAA,MACnC;AACA,YAAM,iBAAiB,KAAK,WAAW,cAAc,OAAO,KAAK,cAAc;AAC/E,UAAI;AACJ,UAAI,OAAO,mBAAmB,UAAU;AACpC,cAAM,EAAE,aAAa,eAAe,IAAI;AACxC,YAAI,EAAE,uBAAuB,sBAAsB,IAAI,KAAK;AAC5D,YAAI,KAAK,OAAO;AACZ,kCAAwB,8BAClB,eAAe,QAAQ,OAAO,EAAE,EAAE,SAClC,KAAK,IAAI,uBAAuB,eAAe,MAAM;AAAA,QAC/D,WACS,OAAO,UAAU,WAAW,KAAK,CAAC,KAAK,QAAQ,sBAAsB,KAAK,QAAQ,cAAc,UAAa,0BAA0B,IAAI;AAChJ,kCAAwB,wBAAwB;AAAA,QACpD;AACA,yBACI,KAAK,UAAU,KAAK,IAAI,WAAW,CAAC,IAAI,OAAO,mBACzC,KAAK,iBACL,KAAK,eAAe,OAAO,aAAa;AAAA,UACtC,aAAa,KAAK,QAAQ,gBAAgB,SAAS,EAAE,KAAK,SAAS,KAAK,QAAQ;AAAA,UAChF;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACb,OACK;AACD,yBAAiB;AAAA,MACrB;AACA,UAAI,KAAK,YAAY,KAAK,CAAC,KAAK,eAAe,WAAW,cAAc,KAAK,KAAK,eAAe,MAAM,cAAc,MAAM,GAAG;AAC1H,yBAAiB,eAAe,QAAQ,KAAK,eAAe,QAAQ,KAAK,eAAe,cAAc;AAAA,MAC1G;AACA,UAAI,KAAK,YAAY,GAAG;AACpB,yBAAiB,eAAe,QAAQ,KAAK,eAAe,gBAAgB,KAAK,eAAe,MAAM;AAAA,MAC1G;AACA,UAAI,KAAK,QAAQ,oBAAoB,gBAAgB,UAAW,KAAK,SAAS,KAAK,QAAQ,2BAA4B;AACnH,yBAAiB,eACZ,QAAQ,KAAK,eAAe,gBAAgB,KAAK,eAAe,cAAc,SAAY,KAAK,eAAe,YAAY,GAAG,EAC7H,QAAQ,KAAK,eAAe,gBAAgB,KAAK,eAAe,cAAc,SAAY,KAAK,GAAG,EAClG,QAAQ,KAAK,eAAe,QAAQ,EAAE,EACtC,QAAQ,KAAK,eAAe,QAAQ,EAAE;AAAA,MAC/C;AACA,WAAK,GAAG,QAAQ;AAChB,WAAK,cAAc,KAAK,eAAe,MAAM,cAAc;AAAA,IAC/D,OACK;AACD,WAAK,GAAG,QAAQ;AAChB,WAAK,cAAc;AAAA,IACvB;AACA,SAAK,iBAAiB,KAAK,GAAG;AAC9B,SAAK,QAAQ,KAAK,SAAS,CAAC;AAAA,EAChC;AAAA,EACA,mBAAmB;AACf,SAAK,GAAG,iBAAiB,SAAS,CAAC,MAAM;AACrC,YAAM,EAAE,OAAO,eAAe,IAAI,KAAK;AACvC,YAAM,aAAa;AACnB,UAAI,kBAAkB,WAAW,QAAQ,mBAAmB,SAAS,WAAW,IAAI,GAAG;AACnF,aAAK,0BAA0B,iBAAiB;AAAA,MACpD;AACA,WAAK,OAAO,KAAK;AACjB,UAAI,KAAK,SAAS,kBAAkB,MAAM;AACtC,cAAM,8BAA8B,MAAM;AACtC,gBAAM,EAAE,QAAQ,QAAQ,eAAe,uBAAuB,eAAe,IAAI,KAAK;AACtF,cAAI,wBAAwB,MAAM,SAAS;AAC3C,gBAAM,iBAAiB,KAAK,eAAe;AAC3C,cAAI,KAAK,eAAe,cAAc,WAAc,MAAM,WAAW,GAAG,KAAK,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,SAAS,GAAG,GAAG;AACzH,mBAAO,iBAAiB,KAAK,eAAe,eAAe,SAAS,IAAI,KAAK,eAAe,UAAU,cAAc,EAAE,SAAS;AAAA,UACnI;AACA,cAAI,KAAK,eAAe,UAAU,gBAAgB,CAAC,MAAM,kBACrD,MAAM,KAAK,gBAAgB,cAAc,MAAM,MAAM,OAAO,cAAc,IAAI,GAAG;AACjF,mBAAO,iBAAiB,wBAAwB;AAAA,UACpD;AACA,cAAI,iBAAiB,uBAAuB;AACxC,mBAAO;AAAA,UACX;AACA,cAAI,kBAAkB,UAAa,MAAM,QAAQ,aAAa,MAAM,IAAI;AACpE,kBAAM,wBAAwB,MAAM,QAAQ,aAAa,IAAI;AAC7D,gBAAI,KAAK,IAAI,iBAAiB,MAAM,MAAM,IAAI,KAAK,kBAAkB,uBAAuB;AACxF,qBAAO,KAAK,eAAe,QAAQ,aAAa,IAAI;AAAA,YACxD,OACK;AACD,kBAAI,CAAC,KAAK,QAAQ,qBAAqB,iBAAiB,uBAAuB;AAC3E,oBAAI,KAAK,eAAe,WAAW,MAAM,UAAU,qBAAqB,CAAC,EAAE,SAAS,MAAM,uBAAuB;AAC7G,2CAAyB;AAAA,gBAC7B;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO,KAAK,QAAQ,6BAA6B,KAAK,QAAQ,oBAAoB,gBAAgB,SAC5F,iBAAiB,wBACjB,KAAK,IAAI,iBAAiB,KAAK,IAAI,uBAAuB,OAAO,MAAM,GAAG,OAAO,MAAM;AAAA,QACjG;AACA,aAAK,iBAAiB,4BAA4B,CAAC;AAAA,MACvD;AAAA,IACJ,CAAC;AACD,SAAK,GAAG,iBAAiB,SAAS,MAAM;AACpC,WAAK,QAAQ;AACb,WAAK,qBAAqB,KAAK;AAC/B,iBAAW,MAAM;AACb,cAAM,EAAE,OAAO,gBAAgB,aAAa,IAAI,KAAK;AACrD,aAAK,OAAO,OAAO,KAAK,QAAQ,kCAAkC;AAClE,YAAI,kBAAkB,QAAQ,gBAAgB,QAAQ,KAAK,IAAI,iBAAiB,YAAY,IAAI,GAAG;AAC/F,eAAK,iBAAiB,GAAG,KAAK,GAAG,MAAM,MAAM;AAAA,QACjD,WACS,kBAAkB,MAAM;AAC7B,gBAAM,uBAAuB,KAAK,wBAAwB,OAAO,cAAc;AAC/E,eAAK,iBAAiB,oBAAoB;AAAA,QAC9C;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,SAAK,GAAG,iBAAiB,QAAQ,MAAM;AACnC,WAAK,QAAQ;AACb,WAAK,OAAO,KAAK,eAAe,OAAO,KAAK,mBAAmB,KAAK,WAAW,CAAC,CAAC;AACjF,UAAI,KAAK,uBAAuB,KAAK,aAAa;AAC9C,aAAK,SAAS,KAAK,SAAS,CAAC;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,wBAAwB,OAAO,gBAAgB;AAC3C,QAAI,KAAK,eAAe,MAAM;AAC1B,aAAO;AAAA,IACX;AACA,UAAM,EAAE,QAAQ,gBAAgB,QAAQ,gBAAgB,gBAAgB,SAAS,IAAI,KAAK;AAC1F,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,gBAAgB,aAAa,iBAAiB;AACpD,UAAM,eAAe,cAAc;AACnC,QAAI,KAAK,QAAQ,6BAA6B,KAAK,QAAQ,oBAAoB,gBAAgB,QAAQ;AACnG,UAAI,YAAY;AACZ,YAAI,kBAAkB,GAAG;AACrB,iBAAO;AAAA,QACX,WACS,MAAM,SAAS,GAAG,KAAK,iBAAiB,MAAM,QAAQ,GAAG,GAAG;AACjE,iBAAO,KAAK,eAAe,SAAS;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ,OACK;AACD,YAAM,eAAe,aAAa,eAAe,SAAS,OAAO;AACjE,UAAI,kBAAkB,MAAM,SAAS,cAAc;AAC/C,eAAO,KAAK,eAAe,SAAS;AAAA,MACxC,WACS,iBAAiB,cAAc;AACpC,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,SAAS;AACb,QAAI,KAAK,QAAQ,6BACb,KAAK,QAAQ,oBAAoB,gBAAgB,UACjD,kBAAkB,gBAClB,aAAa,UACb,cAAc,SAAS,QAAQ,GAAG;AAClC,gBAAU;AACV,UAAI,YAAY;AACZ,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,gCAAgC,mBAAmB,QAAW;AAC3E,gBAAU,MAAM,MAAM,UAAU,GAAG,cAAc,GAAG,cAAc;AAAA,IACtE;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB,OAAO,MAAM,OAAO;AACjC,SAAK,GAAG,kBAAkB,OAAO,GAAG;AAAA,EACxC;AACJ;AAEA,IAAM,YAAY,CAAC,QAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,OAAO;AAChK,SAAS,iBAAiB,SAAS,UAAU;AACzC,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI;AACJ,QAAM,WAAW,IAAI,IAAI;AACzB,QAAM,iBAAiB,IAAI,IAAI;AAC/B,QAAM,cAAc,IAAI,IAAI;AAC5B,QAAM,KAAK,mBAAmB;AAC9B,QAAM,QAAQ,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,MAAM,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC7Q,QAAM,SAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AACtK,QAAM,SAAS,QAAQ,WAAW,GAAG;AACrC,QAAM,YAAY,YAAY,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5I,QAAM,aAAa,SAAS,MAAM,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,SAAS,eAAe,OAAO,CAAC;AACtH,QAAM,aAAa,SAAS,sBAAsB;AAClD,QAAM,cAAc,YAAY,sBAAsB;AACtD,QAAM,UAAU,CAAC,UAAU;AACvB,QAAIC;AACJ,QAAI,OAAO;AACP,YAAM,KAAK,WAAWA,MAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,SAAS,QAAQA,QAAO,SAASA,MAAK,KAAK;AAC1H,UAAI,IAAI;AACJ,wBAAgB,IAAI,cAAc;AAAA,UAC9B;AAAA,UACA;AAAA,UACA,SAAS,CAACC,WAAU;AAChB,gBAAI,CAAC,aAAa,aAAa,SAAS,WAAW,UAAUA,OAAM,QAAQ;AACvE,uBAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,YAAYA,OAAM,MAAM;AAAA,YAC7E;AACA,wBAAY,QAAQA,OAAM;AAC1B,2BAAe,QAAQA,OAAM;AAAA,UACjC;AAAA,UACA,UAAU,CAACA,WAAU;AACjB,gBAAI,aAAa,OAAO;AACpB,uBAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,aAAaA,OAAM,MAAM;AAAA,YAC9E;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,sBAAc,SAAS,WAAW,KAAK;AAAA,MAC3C,OACK;AACD,gBAAQ,MAAM,iGAAiG;AAAA,MACnH;AAAA,IACJ,OACK;AACD,sBAAgB;AAAA,IACpB;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,CAAC,UAAU,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,KAAK;AAAA,IAC/G,YAAY,CAACC,aAAY,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAWA,QAAO;AAAA,EAC3H;AACJ;;;ACniBA,IAAM,IAAI,EAAE,KAAK,EAAE;AAAnB,IAAsB,IAAI,CAAC,OAAO;AAAlC,IAAqC,IAAoB,gBAAE;AAAA,EACzD,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,YAAY;AAAA,MACV,MAAM;AAAA,IACR;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAO,CAAC,qBAAqB,SAAS,QAAQ,QAAQ;AAAA,EACtD,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,UAAM,IAAI,GAAG,EAAE,gBAAgB,EAAE,IAAI,OAAE,CAAC,GAAG,IAAI,SAAE,MAAM;AACrD,UAAI,GAAG,GAAG,GAAG,GAAG;AAChB,aAAO;AAAA,QACL,UAAU,IAAI,KAAK,OAAO,SAAS,EAAE,UAAU,OAAO,SAAS,EAAE,WAAW;AAAA,QAC5E,YAAY,IAAI,KAAK,OAAO,SAAS,EAAE,UAAU,OAAO,SAAS,EAAE,aAAa;AAAA,QAChF,aAAa,IAAI,KAAK,OAAO,SAAS,EAAE,UAAU,OAAO,SAAS,EAAE,cAAc;AAAA,QAClF,mBAAmB,IAAI,KAAK,OAAO,SAAS,EAAE,UAAU,OAAO,SAAS,EAAE,oBAAoB,gBAAE;AAAA,QAChG,2BAA2B;AAAA,QAC3B,8BAA8B;AAAA,QAC9B,oCAAoC;AAAA,QACpC,qBAAqB,IAAI,KAAK,OAAO,SAAS,EAAE,UAAU,OAAO,SAAS,EAAE,sBAAsB;AAAA,QAClG,aAAa;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC,GAAG,IAAI,mBAAmB,KAAK,aAAa,WAAW,IAAI,IAAI,iBAAE,EAAE,KAAK,IAAI,MAAM,IAAI,IAAE,EAAE,UAAU,GAAG,IAAI,SAAE,MAAM,KAAK,IAAI,IAAI,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,IAAE,EAAE,aAAa,GAAG,EAAE,UAAU,KAAK,EAAE,GAAG,IAAI;AACxM,UAAE,MAAM,EAAE,YAAY,CAAC,MAAM;AAC3B,UAAI,KAAK,QAAQ,EAAE,SAAS,KAAK,IAAI,IAAI,EAAE,QAAQ,KAAK;AAAA,IAC1D,CAAC,GAAG,MAAE,GAAG,CAAC,MAAM;AACd,UAAI,GAAG,GAAG;AACV,YAAM,EAAE,QAAQ,IAAI,EAAE,MAAM,oBAAoB,aAAE,KAAK,OAAO,SAAS,EAAE,QAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,WAAW,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,YAAY,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,IACzO,CAAC;AACD,aAAS,EAAE,GAAG;AACZ,UAAI,GAAG;AACL,cAAM,IAAI,KAAK,OAAO,SAAS,EAAE,YAAY;AAC7C,UAAE,qBAAqB,CAAC;AACxB;AAAA,MACF;AACA,YAAM,IAAI,EAAE,OAAO,MAAM,QAAQ,SAAS,EAAE;AAC5C,QAAE,QAAQ,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,aAAE,EAAE,OAAO,EAAE,MAAM,UAAU,EAAE,MAAM,WAAW,EAAE,MAAM,SAAS,GAAG,EAAE,qBAAqB,EAAE,KAAK;AAAA,IAC7I;AACA,aAAS,EAAE,GAAG;AACZ,QAAE,SAAS,CAAC;AAAA,IACd;AACA,aAAS,EAAE,GAAG;AACZ,QAAE,QAAQ,CAAC;AAAA,IACb;AACA,WAAO,EAAE;AAAA,MACP,OAAO,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;AAAA,MAChD,gBAAgB,IAAI,KAAK,OAAO,SAAS,EAAE,iBAAiB;AAAA,IAC9D,CAAC,GAAG,CAAC,GAAG,MAAM;AACZ,UAAI;AACJ,aAAO,KAAK,UAAE,GAAG,mBAAE,SAAS;AAAA,QAC1B,KAAK;AAAA,QACL,MAAM,IAAI,MAAE,CAAC,MAAM,OAAO,SAAS,EAAE;AAAA,QACrC,MAAM;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,GAAG,MAAM,GAAG,MAAM,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,QACjC,gBAAE,SAAS;AAAA,UACT,MAAM;AAAA,UACN,WAAW;AAAA,UACX,OAAO,EAAE;AAAA,UACT,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,SAAS;AAAA,QACX,GAAG,MAAM,IAAI,CAAC;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACxED,IAAM,IAAoB,gBAAE;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,OAAO,CAAC,SAAS,cAAc,QAAQ,SAAS,aAAa;AAAA,EAC7D,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,QAAI,GAAG;AACP,UAAM,IAAI,GAAG,EAAE,OAAO,GAAG,UAAU,GAAG,UAAUC,IAAG,aAAa,GAAG,KAAK,GAAG,KAAK,GAAG,gBAAgB,EAAE,IAAI,OAAE,CAAC,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,EAAE,GAAG,IAAI,IAAE,MAAM,GAAG,IAAI,IAAE,MAAM,GAAG,IAAI,SAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,iBAAiB,EAAE,GAAG,IAAI,IAAE,QAAQ,KAAK,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,MAAM,SAAS,MAAM,OAAO,SAAS,EAAE,KAAK,EAAE,CAAC,KAAK,MAAM,GAAG,IAAI;AAC7U,UAAE,GAAG,CAAC,MAAM;AACV,YAAM,OAAO,EAAE,QAAQ,QAAQ,EAAE;AAAA,IACnC,CAAC,GAAG,MAAE,GAAG,CAAC,MAAM;AACd,QAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,KAAK,EAAE,CAAC;AAAA,IACjD,CAAC;AACD,aAAS,IAAI;AACX,QAAE,QAAQ,MAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,IAC9C;AACA,aAAS,IAAI;AACX,QAAE,QAAQ,OAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,IAC7C;AACA,aAAS,IAAI;AACX,YAAM,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,QAAQ,KAAK,OAAO,EAAE,KAAK,IAAI,MAAI,IAAI,EAAE,QAAQ,KAAK,OAAO,EAAE,KAAK,IAAI,MAAI,EAAE,UAAU,GAAG,WAAW,EAAE,KAAK,KAAK,OAAO,SAAS,EAAE,UAAU,CAAC;AAC7K,aAAO,EAAE,QAAQ,IAAI,IAAI,KAAK,+BAA+B,aAAE,OAAO,EAAE,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,+BAA+B,aAAE,OAAO,EAAE,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK;AAAA,IAClK;AACA,WAAO,EAAE;AAAA,MACP,OAAO,OAAO,EAAE,SAAS,EAAE;AAAA,MAC3B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB,CAAC,GAAG,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3B,OAAO;AAAA,MACP,SAAS;AAAA,MACT,KAAK;AAAA,IACP,GAAG;AAAA,MACD,YAAE,GAAG,WAAE;AAAA,QACL,SAAS;AAAA,QACT,KAAK;AAAA,QACL,MAAM;AAAA,QACN,YAAY,EAAE;AAAA,QACd,uBAAuB,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;AAAA,MAC1D,GAAG,EAAE,UAAU,MAAE,CAAC,GAAG,UAAU,MAAEA,EAAC,GAAG,aAAa,MAAE,CAAC,GAAG,KAAK,MAAE,CAAC,GAAG,KAAK,MAAE,CAAC,GAAG,gBAAgB,MAAE,CAAC,EAAE,GAAG;AAAA,QACpG,SAAS;AAAA,QACT,QAAQ;AAAA,MACV,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC;AAAA,IAC9B,GAAG,GAAG;AAAA,EACR;AACF,CAAC;", "names": ["CurrencyDisplay", "ValueScaling", "str", "_a", "value", "options", "R"]}