{"version": 3, "sources": ["../../@perkd/applet-common/dist/utilsColor.js"], "sourcesContent": ["export const rgbToRgbo = (color) => {\n    const regex = /rgba?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*(?:,\\s*(0|1|0?\\.\\d+)\\s*)?\\)/;\n    const match = color?.match(regex);\n    if (!match)\n        return { r: 0, g: 0, b: 0, o: 1 };\n    return {\n        r: parseInt(match[1]),\n        g: parseInt(match[2]),\n        b: parseInt(match[3]),\n        o: match[4] ? parseFloat(match[4]) : 1\n    };\n};\nexport const hexToRgbo = (color) => {\n    const validHexColor = color && color.match(/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/);\n    if (!validHexColor)\n        return { r: 0, g: 0, b: 0, o: 1 };\n    const c = (color.length > 3) ? color : '#' + color.slice(1, color.length).replace(/(.{1})/g, '$1$1');\n    const r = parseInt(c.slice(1, 3), 16), g = parseInt(c.slice(3, 5), 16), b = parseInt(c.slice(5, 7), 16), o = c.slice(7, 9) ? Math.round(parseInt(c.slice(7, 9), 16) * 100 / 255) / 100 : 1;\n    return { r, g, b, o };\n};\nexport const colorToRgbo = (color) => {\n    return color.startsWith('#') ? hexToRgbo(color) : rgbToRgbo(color);\n};\nexport function rgbToHsl(r, g, b) {\n    r /= 255;\n    g /= 255;\n    b /= 255;\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    let h = 0, s = 0;\n    const l = (max + min) / 2;\n    if (max !== min) {\n        const d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h * 360, s, l };\n}\nexport const getContrastColor = (color) => {\n    const { r, g, b } = typeof color === 'string' ? colorToRgbo(color) : color;\n    const sRGBtoLin = (colorChannel) => colorChannel <= 0.03928 ? colorChannel / 12.92 : Math.pow((colorChannel + 0.055) / 1.055, 2.4);\n    const rgbToY = (r, g, b) => 0.2126 * sRGBtoLin(r) + 0.7152 * sRGBtoLin(g) + 0.0722 * sRGBtoLin(b);\n    const luminance = rgbToY(r / 255, g / 255, b / 255);\n    const hsl = rgbToHsl(r, g, b);\n    // Special case 1: Very dark greens always get white text\n    if (hsl.h >= 90 && hsl.h <= 150 && hsl.l < 0.3) {\n        return '#FFFFFF';\n    }\n    // Special case 2: Bright yellows need a higher threshold (black text at higher luminance)\n    if (hsl.h > 40 && hsl.h < 70 && hsl.l >= 0.5) {\n        return luminance < 0.60 ? '#FFFFFF' : '#000000';\n    }\n    // Special case 3: Reds and oranges need a higher threshold\n    if ((hsl.h >= 0 && hsl.h <= 15 && hsl.s > 0.6) ||\n        (hsl.h > 15 && hsl.h <= 40 && hsl.s > 0.7)) {\n        return luminance < 0.55 ? '#FFFFFF' : '#000000';\n    }\n    // Special case 4: Saturated greens need a higher threshold\n    if (hsl.h >= 90 && hsl.h <= 150 && hsl.s > 0.6) {\n        return luminance < 0.55 ? '#FFFFFF' : '#000000';\n    }\n    // Blue-greens to blues - modified to handle lower saturation teals\n    if (hsl.h > 150 && hsl.h < 190) {\n        return luminance < 0.48 ? '#FFFFFF' : '#000000';\n    }\n    // - Purples (hsl.h >= 270 && hsl.h <= 330 && hsl.s > 0.4)\n    if (hsl.h >= 270 && hsl.h <= 330 && hsl.s > 0.4) {\n        return luminance < 0.42 ? '#FFFFFF' : '#000000';\n    }\n    // Standard threshold (0.45) cases:\n    // - Darker yellows (hsl.h > 40 && hsl.h < 70 && hsl.l < 0.5)\n    // - Yellowish-greens (hsl.h >= 70 && hsl.h < 90 && hsl.s > 0.5)\n    // - Less saturated greens (hsl.h >= 90 && hsl.h <= 150 && hsl.s <= 0.6)\n    // - Blues (hsl.h >= 190 && hsl.h <= 250 && hsl.s > 0.6)\n    // - Grays (hsl.s < 0.1)\n    // - Earth tones/browns (hsl.h >= 20 && hsl.h <= 40 && hsl.s >= 0.4 && hsl.s <= 0.6 && hsl.l <= 0.6)\n    if ((hsl.h > 40 && hsl.h < 70 && hsl.l < 0.5) ||\n        (hsl.h >= 70 && hsl.h < 90 && hsl.s > 0.5) ||\n        (hsl.h >= 90 && hsl.h <= 150 && hsl.s <= 0.6) ||\n        (hsl.h >= 190 && hsl.h <= 250 && hsl.s > 0.6) ||\n        (hsl.s < 0.1) ||\n        (hsl.h >= 20 && hsl.h <= 40 && hsl.s >= 0.4 && hsl.s <= 0.6 && hsl.l <= 0.6)) {\n        return luminance < 0.45 ? '#FFFFFF' : '#000000';\n    }\n    const whiteContrast = (1.0 + 0.05) / (luminance + 0.05);\n    const blackContrast = (luminance + 0.05) / (0.0 + 0.05);\n    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';\n};\nexport const blendWithBackground = (frontColor, backColor) => {\n    const frontRgbo = typeof frontColor === 'string' ? colorToRgbo(frontColor) : frontColor;\n    const backRgbo = typeof backColor === 'string' ? colorToRgbo(backColor) : backColor;\n    const { r, g, b, o } = frontRgbo;\n    return {\n        r: Math.round(r * o + backRgbo.r * (1 - o)),\n        g: Math.round(g * o + backRgbo.g * (1 - o)),\n        b: Math.round(b * o + backRgbo.b * (1 - o)),\n        o: 1\n    };\n};\nexport const applyOpacity = (color, opacity) => {\n    const { r, g, b } = colorToRgbo(color);\n    return `rgba(${r}, ${g}, ${b}, ${opacity})`;\n};\n"], "mappings": ";AAAO,IAAM,YAAY,CAAC,UAAU;AAChC,QAAM,QAAQ;AACd,QAAM,QAAQ,+BAAO,MAAM;AAC3B,MAAI,CAAC;AACD,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AACpC,SAAO;AAAA,IACH,GAAG,SAAS,MAAM,CAAC,CAAC;AAAA,IACpB,GAAG,SAAS,MAAM,CAAC,CAAC;AAAA,IACpB,GAAG,SAAS,MAAM,CAAC,CAAC;AAAA,IACpB,GAAG,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAAA,EACzC;AACJ;AACO,IAAM,YAAY,CAAC,UAAU;AAChC,QAAM,gBAAgB,SAAS,MAAM,MAAM,mDAAmD;AAC9F,MAAI,CAAC;AACD,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AACpC,QAAM,IAAK,MAAM,SAAS,IAAK,QAAQ,MAAM,MAAM,MAAM,GAAG,MAAM,MAAM,EAAE,QAAQ,WAAW,MAAM;AACnG,QAAM,IAAI,SAAS,EAAE,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,SAAS,EAAE,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,SAAS,EAAE,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,KAAK,MAAM,SAAS,EAAE,MAAM,GAAG,CAAC,GAAG,EAAE,IAAI,MAAM,GAAG,IAAI,MAAM;AACzL,SAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AACxB;AACO,IAAM,cAAc,CAAC,UAAU;AAClC,SAAO,MAAM,WAAW,GAAG,IAAI,UAAU,KAAK,IAAI,UAAU,KAAK;AACrE;AACO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,OAAK;AACL,OAAK;AACL,OAAK;AACL,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,MAAI,IAAI,GAAG,IAAI;AACf,QAAM,KAAK,MAAM,OAAO;AACxB,MAAI,QAAQ,KAAK;AACb,UAAM,IAAI,MAAM;AAChB,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,IACR;AACA,SAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,EAAE;AAC9B;AACO,IAAM,mBAAmB,CAAC,UAAU;AACvC,QAAM,EAAE,GAAG,GAAG,EAAE,IAAI,OAAO,UAAU,WAAW,YAAY,KAAK,IAAI;AACrE,QAAM,YAAY,CAAC,iBAAiB,gBAAgB,UAAU,eAAe,QAAQ,KAAK,KAAK,eAAe,SAAS,OAAO,GAAG;AACjI,QAAM,SAAS,CAACA,IAAGC,IAAGC,OAAM,SAAS,UAAUF,EAAC,IAAI,SAAS,UAAUC,EAAC,IAAI,SAAS,UAAUC,EAAC;AAChG,QAAM,YAAY,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAClD,QAAM,MAAM,SAAS,GAAG,GAAG,CAAC;AAE5B,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK;AAC5C,WAAO;AAAA,EACX;AAEA,MAAI,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,KAAK;AAC1C,WAAO,YAAY,MAAO,YAAY;AAAA,EAC1C;AAEA,MAAK,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,IAAI,OACrC,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,IAAI,KAAM;AAC5C,WAAO,YAAY,OAAO,YAAY;AAAA,EAC1C;AAEA,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK;AAC5C,WAAO,YAAY,OAAO,YAAY;AAAA,EAC1C;AAEA,MAAI,IAAI,IAAI,OAAO,IAAI,IAAI,KAAK;AAC5B,WAAO,YAAY,OAAO,YAAY;AAAA,EAC1C;AAEA,MAAI,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK;AAC7C,WAAO,YAAY,OAAO,YAAY;AAAA,EAC1C;AAQA,MAAK,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OACpC,IAAI,KAAK,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OACrC,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,OACxC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,IAAI,OACxC,IAAI,IAAI,OACR,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,KAAM;AAC9E,WAAO,YAAY,OAAO,YAAY;AAAA,EAC1C;AACA,QAAM,iBAAiB,IAAM,SAAS,YAAY;AAClD,QAAM,iBAAiB,YAAY,SAAS,IAAM;AAClD,SAAO,gBAAgB,gBAAgB,YAAY;AACvD;", "names": ["r", "g", "b"]}