import type { ScriptSetupRanges } from '../../parsers/scriptSetupRanges';
import type { Code, Sfc } from '../../types';
import type { ScriptCodegenContext } from './context';
import type { ScriptCodegenOptions } from './index';
export declare function generateComponent(options: ScriptCodegenOptions, ctx: ScriptCodegenContext, scriptSetup: NonNullable<Sfc['scriptSetup']>, scriptSetupRanges: ScriptSetupRanges): Generator<Code>;
export declare function generateComponentSetupReturns(scriptSetupRanges: ScriptSetupRanges): Generator<Code>;
export declare function generateEmitsOption(options: ScriptCodegenOptions, scriptSetupRanges: ScriptSetupRanges): Generator<Code>;
export declare function generatePropsOption(options: ScriptCodegenOptions, ctx: ScriptCodegenContext, scriptSetup: NonNullable<Sfc['scriptSetup']>, scriptSetupRanges: ScriptSetupRanges, hasEmitsOption: boolean, inheritAttrs: boolean): Generator<Code>;
