{"name": "@vue/eslint-config-prettier", "version": "10.2.0", "description": "eslint-config-prettier for create-vue", "type": "commonjs", "main": "index.js", "files": ["index.js", "index.d.ts", "skip-formatting.js", "skip-formatting.d.ts"], "exports": {".": "./index.js", "./skip-formatting": "./skip-formatting.js"}, "publishConfig": {"access": "public", "provenance": true}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/eslint-config-prettier.git"}, "keywords": ["vue", "create-vue", "create-eslint-config", "eslint", "prettier"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/eslint-config-prettier/issues"}, "homepage": "https://github.com/vuejs/eslint-config-prettier#readme", "dependencies": {"eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2"}, "devDependencies": {"eslint": "^9.18.0", "prettier": "^3.4.2"}, "peerDependencies": {"eslint": ">= 8.21.0", "prettier": ">= 3.0.0"}, "scripts": {"lint": "eslint . --fix", "format": "prettier *.{js,md} --write"}}