{"name": "@rollup/plugin-babel", "version": "6.0.4", "publishConfig": {"access": "public"}, "description": "Seamless integration between Rollup and Babel.", "license": "MIT", "repository": {"url": "rollup/plugins", "directory": "packages/babel"}, "author": "<PERSON>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/babel#readme", "bugs": "https://github.com/rollup/plugins/issues", "main": "./dist/cjs/index.js", "module": "./dist/es/index.js", "exports": {"types": "./types/index.d.ts", "import": "./dist/es/index.js", "default": "./dist/cjs/index.js"}, "engines": {"node": ">=14.0.0"}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm build && pnpm lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm test -- --verbose", "prebuild": "del-cli dist", "prepare": "if [ ! -d 'dist' ]; then pnpm build; fi", "prerelease": "pnpm build", "pretest": "pnpm build", "release": "pnpm --workspace-root plugin:release --pkg $npm_package_name", "test": "ava", "test:ts": "tsc types/index.d.ts test/types.ts --noEmit"}, "files": ["dist", "!dist/**/*.map", "types", "README.md", "LICENSE"], "keywords": ["rollup", "plugin", "rollup-plugin", "babel", "es2015", "es6"], "peerDependencies": {"@babel/core": "^7.0.0", "@types/babel__core": "^7.1.9", "rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}, "@types/babel__core": {"optional": true}}, "dependencies": {"@babel/helper-module-imports": "^7.18.6", "@rollup/pluginutils": "^5.0.1"}, "devDependencies": {"@babel/core": "^7.19.1", "@babel/plugin-external-helpers": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.19.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.19.1", "@babel/preset-env": "^7.19.1", "@rollup/plugin-json": "^5.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@types/babel__core": "^7.1.9", "rollup": "^4.0.0-24", "source-map": "^0.7.4"}, "types": "./types/index.d.ts", "ava": {"files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}, "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/Andarist)"]}