export declare const tryPkg: (pkg: string) => string | undefined;
export declare const isPkgAvailable: (pkg: string) => boolean;
export declare const tryFile: (filePath?: string[] | string, includeDir?: boolean) => string;
export declare const tryExtensions: (filepath: string, extensions?: string[]) => string;
export declare const findUp: (searchEntry: string, searchFileOrIncludeDir?: boolean | string, includeDir?: boolean) => string;
