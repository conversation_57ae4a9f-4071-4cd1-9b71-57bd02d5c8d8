# @perkd/wallet

A TypeScript utility package for extracting and processing payload data from headers of incoming API calls from the Perkd Wallet app.

## Installation

```bash
yarn add @perkd/wallet
```

## Features

- Extract and validate tenant information
- Extract and validate user information
- Parse installation data
- Handle location information
- Process staff card data
- Token expiration validation

## Usage

```typescript
import { tenant, user, installation, location, card, tokenExpired } from '@perkd/wallet'

// Example with Express.js
app.get('/api/profile', (req, res) => {
  // Get tenant info
  const tenantInfo = tenant(req.headers)
  
  // Get user info
  const userInfo = user(req.headers)
  
  // Check if token is expired
  const isExpired = tokenExpired(req.headers)
  
  // Get installation details
  const installInfo = installation(req.headers)
  
  // Get location data
  const locationInfo = location(req.headers)
  
  // Get staff card info
  const cardInfo = card(req.headers)
})
```

## API Reference

### Headers

The package expects the following headers:

- `x-access-token`: JWT token containing tenant and user information
- `perkd-install`: Installation information
- `perkd-location`: Base64 encoded location data
- `card`: Staff card information

### Functions

#### `tenant(headers)`
Extracts tenant information from the access token.
- Returns: `Tenant | void`
- Properties:
  - `code`: Tenant code
  - `expired`: Boolean indicating if the token is expired

#### `user(headers)`
Extracts user information from the access token.
- Returns: `User | void`
- Properties:
  - `accountId`: User's account ID
  - `personId`: User's person ID
  - `expired`: Boolean indicating if the token is expired

#### `tokenExpired(headers)`
Checks if the access token has expired.
- Returns: `boolean`

#### `installation(headers)`
Extracts installation information.
- Returns: `Wallet.Installation | void`
- Contains details about the app, device, OS, and locale settings

#### `location(headers)`
Decodes and parses location information.
- Returns: `Wallet.Location | void`
- Contains geographical and address information

#### `card(headers)`
Extracts staff card information.
- Returns: `Wallet.Card | void`
- Contains tenant and user details specific to staff cards
