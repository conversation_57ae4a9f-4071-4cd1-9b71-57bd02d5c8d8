export declare enum Platform {
    IOS = "ios",
    ANDROID = "android"
}
export type User = {
    accountId: string;
    personId: string;
    expired?: boolean;
};
export type Tenant = {
    code: string;
    expired?: boolean;
};
type Brand = {
    short: string;
    long: string;
    color?: string;
    logo?: {
        id: string;
        url: string;
    };
};
export declare namespace Wallet {
    interface App {
        id: string;
        version: string;
        env: 'prod' | 'dev';
    }
    interface Device {
        uuid: string;
        name: string;
        brand: string;
        manufacturer: string;
        model: string;
        ip?: string;
    }
    interface OS {
        name: Platform.IOS | Platform.ANDROID;
        version: string;
    }
    interface Locale {
        languages: string[];
        currency: string;
        country: string;
        timeZone: string;
        numberFormat: {
            decimalSeparator: string;
            groupingSeparator: string;
        };
        useMetric: boolean;
        temperature: string;
        calendar: string;
    }
    type Coordinates = [number, number];
    interface Geo {
        type: 'Point' | 'MultiPoint' | 'LineString' | 'Polygon' | 'MultiPolygon' | 'GeometryCollection';
        coordinates: Coordinates[];
    }
    interface Place {
        id: string;
        name: string;
        brand?: Brand;
        geo: Geo;
        distance: number;
        address: string;
    }
    interface Installation {
        id: string;
        app: App;
        device: Device;
        os: OS;
        locale: Locale;
        regions: string[];
    }
    interface Address {
        id?: string;
        unit: string;
        level: string;
        house: string;
        street: string;
        city: string;
        state?: string;
        postCode: string;
        country: string;
        short?: string;
        geo?: Geo;
    }
    type Position = {
        key: string;
        value: string;
    }[];
    type Spot = Partial<Address> & {
        name: string;
        type: string;
        position: Position;
    };
    interface Location {
        type: string;
        name: string;
        formatted: string;
        short: string;
        house: string;
        street: string;
        city: string;
        state: string;
        postCode: string;
        country: string;
        geo: Geo;
        spot: Spot;
        places: Place[];
    }
    interface Card {
        tenant: Tenant;
        user: {
            id: string;
            usename: string;
            email: string;
            staffId: string;
            emailVerified: boolean;
            roles: any[];
        };
    }
}
export {};
