import { Tenant, User, Wallet } from './types';
export declare function tenant(headers?: any): Tenant | void;
export declare function user(headers?: any): User | void;
export declare function tokenExpired(headers?: any): boolean;
export declare function installation(headers?: any): Wallet.Installation | void;
export declare function location(headers?: any): Wallet.Location | void;
export declare function card(headers?: any): Wallet.Card | void;
declare const _default: {
    tenant: typeof tenant;
    user: typeof user;
    tokenExpired: typeof tokenExpired;
    installation: typeof installation;
    location: typeof location;
    card: typeof card;
};
export default _default;
