"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tenant = tenant;
exports.user = user;
exports.tokenExpired = tokenExpired;
exports.installation = installation;
exports.location = location;
exports.card = card;
const jws_1 = require("jws");
const // headers
ACCESS_TOKEN = 'x-access-token', INSTALLATION = 'perkd-install', LOCATION = 'perkd-location', CARD = 'card'; // staff card
// ---  App info from headers
function tenant(headers = {}) {
    const token = headers[ACCESS_TOKEN], { tenant, exp } = payloadOf(token), expired = hasExpired(exp);
    return token ? { ...tenant, expired } : undefined;
}
function user(headers = {}) {
    const token = headers[ACCESS_TOKEN], { user, exp } = payloadOf(token), expired = hasExpired(exp);
    return token ? { ...user, expired } : undefined;
}
function tokenExpired(headers = {}) {
    const token = headers[ACCESS_TOKEN], { exp } = payloadOf(token);
    return hasExpired(exp);
}
function installation(headers = {}) {
    const token = headers[INSTALLATION], installation = payloadOf(token);
    return token ? installation : undefined;
}
function location(headers = {}) {
    try {
        const encoded = headers[LOCATION];
        if (!encoded)
            return;
        const location = Buffer.from(encoded, 'base64').toString('utf-8');
        return JSON.parse(location);
    }
    catch (error) { }
}
function card(headers = {}) {
    const card = headers[CARD];
    return card ? JSON.parse(card) : undefined;
}
// return true if timestamp undefined
function hasExpired(timestamp) {
    return timestamp ? timestamp * 1000 < Date.now() : true;
}
// apps >= 5.0.1 no longer stringify
function payloadOf(token) {
    if (!token)
        return {};
    try {
        const { payload } = (0, jws_1.decode)(token) || {};
        return typeof payload === 'string' ? JSON.parse(payload) : payload;
    }
    catch (error) {
        console.error('[wallet]payloadOf:', { error, token });
        return {};
    }
}
exports.default = {
    tenant,
    user,
    tokenExpired,
    installation,
    location,
    card
};
//# sourceMappingURL=wallet.js.map