{"name": "@perkd/wallet", "version": "0.4.5", "description": "Extract payload from headers of incoming API calls from Wallet app", "author": "<EMAIL>", "license": "MIT", "engines": {"node": ">=18"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npx tsc", "prestart": "yarn run build", "start": "node dist/index.js", "update": "ncu -u && rm -rf node_modules/ yarn.lock && yarn install", "test": "mocha -r ts-node/register tests/**/*.test.ts", "test-one": "mocha -r ts-node/register", "reinstall": "rm -rf node_modules/ yarn.lock; yarn; rm -rf dist/ tsconfig.tsbuildinfo; tsc", "release": "rm -rf dist/ tsconfig.tsbuildinfo && tsc && rm -rf node_modules/ yarn.lock && yarn install"}, "repository": {"type": "git", "url": "git+ssh://**************/perkd/wallet.git"}, "bugs": {"url": "https://github.com/perkd/wallet/issues"}, "homepage": "https://github.com/perkd/wallet#readme", "files": ["README.md", "dist", "!*/__tests__", "!tsconfig.tsbuildinfo"], "dependencies": {"jws": "^4.0.0", "tslib": "^2.8.1"}, "devDependencies": {"@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.2", "@types/jws": "^3.2.10", "@types/node": "^22.10.5"}, "packageManager": "yarn@4.6.0"}