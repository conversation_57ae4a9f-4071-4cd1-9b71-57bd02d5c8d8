# @crm/types

CRM type definitions package providing TypeScript interfaces and enums for a comprehensive CRM system.

## Installation

Install the package using yarn:

```bash
yarn add @crm/types
```

## System Overview

```mermaid
graph TD
    subgraph CRM System
        BM[Business Management]
        CM[Content & Marketing]
        CP[Commerce & Payments]
        LR[Loyalty & Rewards]
        OT[Operational Tools]
        AR[Analytics & Reporting]
        GL[Geographic & Localization]
        IA[Integration & APIs]

        BM --> PM[Person Management]
        BM --> BP[Business Profiles]
        BM --> MP[Membership Programs]

        CM --> CMS[Content Management]
        CM --> CAM[Campaign Management]
        CM --> MA[Marketing Automation]

        CP --> PCM[Product Catalog]
        CP --> OP[Order Processing]
        CP --> PG[Payment Gateways]

        LR --> RP[Reward Programs]
        LR --> PS[Points Systems]
        LR --> OM[Offer Management]
    end
```

## Features

This package provides TypeScript definitions for:

### Business & Customer Management
- Person & Contact Management
- Business Profiles & Settings
- Membership Programs & Tiers
- User Management & Authentication
- Permission Controls
- Staff Management & Roles

### Content & Marketing
- Content Management (Microsites, Emails, Images, etc.)
- Campaign Management & Triggers
- Marketing Automation
- Messaging Systems
- Form Builders
- Image Processing

### Commerce & Payments
- Product Catalog Management
- Order Processing & Fulfillment
- Payment Gateway Integrations
- Pricing & Discount Rules
- Billing Systems
- Receipt Generation
- Inventory Management

### Loyalty & Rewards
- Reward Program Definitions
- Points & Stamps Systems
- Digital Wallet Integration
- Offer Management
- Gift Card Systems
- Store Credit Management

### Operational Tools
- Booking & Appointment Systems
- Kitchen Display Systems
- POS Integration
- Printer Management
- Vending Machine Support
- Service Request Handling

### Analytics & Reporting
- Customer Behavior Tracking
- Performance Analytics
- Report Generation
- Dashboard Configurations
- Health Monitoring
- Audit Logging

### Geographic & Localization
- Multi-country Support
- Geographic Data Handling
- Language Management
- Currency Handling
- Timezone Management
- Regional Settings

### Integration & APIs
- API Type Definitions
- External Provider Integration
- Webhook Management
- Context Handling
- Installation Management
- Module Configuration

## Key Type Namespaces

### Type Hierarchy Overview

```mermaid
classDiagram
    class Contents {
        +MediaType
        +State
        +ResourceType
        +ThemeSupport
    }
    class Payments {
        +GATEWAY_METHODS
        +CARDS
        +TransactionStates
    }
    class Orders {
        +OrderStates
        +FulfillmentSteps
        +DeliveryOptions
    }
    class Memberships {
        +ProgramTypes
        +States
        +TierManagement
    }
    class Context {
        +App_User
        +Installation
        +Localization
    }

    Contents --> ResourceType
    Payments --> TransactionStates
    Orders --> FulfillmentSteps
    Memberships --> TierManagement
    Context --> App_User
```

### Contents

Provides types for content management including:

- Media Types (HTML, JSON, Images, etc.)
- Content States (Draft, Complete, Archived)
- Resource Types (Microsite, Email, Applet)
- Theme Support (Light/Dark modes)

Example usage:

```typescript
import { Contents } from '@crm/types'

const mediaType: Contents.MediaType = Contents.MediaType.HTML
const state: Contents.State = Contents.State.DRAFT
```

### Payments

Defines payment-related types including:

- Payment Methods (Card, Alipay, LinePay, GrabPay, etc.)
- Transaction States
- Gateway Integrations
- Error Handling
- Manual Payment Types
- Card Processing

Example usage:

```typescript
import { Payments } from '@crm/types'

const supportedMethods = Payments.GATEWAY_METHODS
const cardTypes = Payments.CARDS
```

### Orders

Provides order management types:

- Order States (Pending, Committed, Expired, etc.)
- Fulfillment Steps (New, Received, Packed, etc.)
- Delivery Options
- Status Tracking
- Transaction Management

### Memberships

Handles membership program types:

- Program Types (Free, Paid, Earned, etc.)
- Membership States
- Tier Management
- Program Settings
- Status Tracking

### Rewards

Manages reward system types:

- Reward Master Policies
- State Management
- Transaction Types
- Points & Stamps Systems
- Qualification Rules

### Installations

Defines installation and device management:

- Installation States
- Feature Support
- Device Capabilities
- Permission Management
- Biometric Authentication
- App Configuration

### Context

Provides context management types:

- User Context
- Installation Context
- Card Context
- Authentication
- Localization Settings
- Device Information

Example usage:

```typescript
import { Context } from '@crm/types'

type AppUser = Context.App_User
type Installation = Context.Installation
```

### Settings

Manages business and system settings:

- Marketplace Configuration
- Order Management
- Kitchen Display Systems
- Staff Management
- Printing Systems
- Default Configurations

### Reports

Defines reporting system types:

- Report States (Incomplete, Ready, Scheduled, etc.)
- Performance Metrics
- Analytics Data Structures
- Dashboard Configurations

### Payment Flow

```mermaid
stateDiagram-v2
    [*] --> INITIATED
    INITIATED --> PROCESSING
    PROCESSING --> AUTHORIZED
    PROCESSING --> FAILED
    AUTHORIZED --> CAPTURED
    AUTHORIZED --> VOIDED
    CAPTURED --> REFUNDED
    CAPTURED --> [*]
    FAILED --> [*]
    VOIDED --> [*]
    REFUNDED --> [*]
```

## Development

### Setup

1. Clone the repository:

```bash
<NAME_EMAIL>:perkd/crm-types.git
```

2. Install dependencies:

```bash
yarn install

```README.md

3. Build the project:

```bash
yarn build
```
