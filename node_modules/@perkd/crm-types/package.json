{"name": "@crm/types", "version": "1.9.8", "description": "CRM type definitions", "author": "<EMAIL>", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npx tsc", "prestart": "yarn run build", "start": "node dist/index.js", "test": "mocha -r ts-node/register tests/**/*.test.ts", "test-one": "mocha -r ts-node/register", "reinstall": "rm -rf node_modules/ yarn.lock; yarn; rm -rf dist/ tsconfig.tsbuildinfo; tsc", "release": "rm -rf dist/ tsconfig.tsbuildinfo && tsc && rm -rf node_modules/ yarn.lock && yarn install"}, "repository": {"type": "git", "url": "git+ssh://**************/perkd/crm-types.git"}, "bugs": {"url": "https://github.com/perkd/crm-types/issues"}, "homepage": "https://github.com/perkd/crm-types#readme", "files": ["README.md", "dist", "!*/__tests__", "!tsconfig.tsbuildinfo"], "dependencies": {"@perkd/wallet": "github:perkd/wallet#semver:^0.4.5", "tslib": "^2.8.1"}, "devDependencies": {"@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.2", "@types/node": "^22.13.8"}, "packageManager": "yarn@4.7.0"}