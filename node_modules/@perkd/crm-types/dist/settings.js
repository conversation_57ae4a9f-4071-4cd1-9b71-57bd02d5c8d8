"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Settings = void 0;
const users_1 = require("./users");
const providers_1 = require("./providers");
const { KITCHEN, COLLECT, SERVICE } = users_1.Users.Roles, { FEIE } = providers_1.Providers.PROVIDER;
var Settings;
(function (Settings) {
    let Name;
    (function (Name) {
        Name["LOCALE"] = "locale";
        Name["EMAIL"] = "email";
        Name["SMS"] = "sms";
        Name["PROVIDER"] = "provider";
        Name["ORDER"] = "order";
        Name["PAYMENT"] = "payment";
        Name["FULFILLMENT"] = "fulfillment";
        Name["BOOKING"] = "booking";
        Name["QUEUING"] = "queuing";
        Name["STAFF"] = "staff";
        Name["PRINT"] = "print";
        Name["ACTION"] = "action";
        Name["DASHBOARD"] = "dashboard";
        Name["MARKETPLACE"] = "marketplace";
    })(Name = Settings.Name || (Settings.Name = {}));
    Settings.LOCALE = Name.LOCALE, Settings.ORDER = Name.ORDER, Settings.PAYMENT = Name.PAYMENT, Settings.FULFILLMENT = Name.FULFILLMENT, Settings.BOOKING = Name.BOOKING, Settings.QUEUING = Name.QUEUING, Settings.STAFF = Name.STAFF, Settings.PRINT = Name.PRINT, Settings.MARKETPLACE = Name.MARKETPLACE;
    let ActionKey;
    (function (ActionKey) {
        ActionKey["SIGN_UP"] = "signup";
        ActionKey["STAFF_CHECKIN"] = "staffcheckin";
        ActionKey["VENDING_CHECKIN"] = "vendingcheckin";
    })(ActionKey = Settings.ActionKey || (Settings.ActionKey = {}));
    Settings.DEFAULT = {
        TIMEZONE: 'Asia/Singapore',
        ORDER: {
            numbering: {
                length: 4,
                last: 0,
                lastNumber: 9999,
                reset: {
                    daily: true,
                    firstNumber: 1
                }
            }
        },
        KITCHEN: {
            orderTicketTemplate: '40w30h',
            prepareLookahead: 60 * 1000, // 60 secs (ms)
            maxScheduleWindow: 60 * 60 * 1000, // 1 hour (ms)
            maxShelfLife: 20 * 60 * 1000, // 20 mins (ms)
            minFirstEtaTime: 3 * 60 * 1000, // 3 mins (ms)
            defaultItemPrepTime: 30, // seconds
            capacity: 1,
        },
        STAFF: {
            TEMPLATES: {
                kitchenRequested: {
                    name: 'staff:kitchen:fulfillRequested',
                    widget: 'admin-kitchen',
                    roles: [KITCHEN]
                },
                kitchenPacked: {
                    name: 'staff:kitchen:fulfillPacked',
                    widget: 'admin-collect',
                    roles: [COLLECT]
                },
                serviceRequest: {
                    name: 'staff:service:request',
                    widget: 'admin-service',
                    roles: [SERVICE]
                }
            },
            CHECKIN: {
                ttl: 480, // minutes, 8 hours
            }
        },
        PRINT: {
            provider: FEIE
        },
        BOOKING: {
            table: {
                minSize: 2,
                maxSize: 12,
                leadTime: 180, // 3 hours
                maxAdvanceBooking: 14, // days
                minPartySize: 2, // pax
                maxPartySize: 20, // pax
                maxCombined: 4, // tables
                minDuration: 60, // 1 hour
                maxDuration: 180, // 3 hours
                minCapacity: 0.8, // 80 %
                maxCapacity: 1.5 // 150 %
            }
        },
        QUEUING: {
            table: {
                holdTime: 5 * 60 * 1000, // 5 minutes
                validity: 3 * 60, // 3 hours
                duration: {
                    default: 90, // 90 minutes
                    peak: 75, // 75 minutes
                    offPeak: 120 // 120 minutes
                },
                priority: {
                    asap: 100, // score
                    vip: 10, // score
                    regular: 0, // score
                    waitingTime: {
                        factor: 1, // multiplier
                        maxBonus: 5 // max bonus
                    }
                }
            }
        }
    };
})(Settings || (exports.Settings = Settings = {}));
//# sourceMappingURL=settings.js.map