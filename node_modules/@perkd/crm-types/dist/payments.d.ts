import { Providers } from './providers';
export declare namespace Payments {
    enum Status {
        AUTHORIZED = "authorized",
        CHARGEABLE = "chargeable",
        PENDING = "pending",
        CANCELLED = "cancelled",
        PAID = "paid",
        PARTIALLY_PAID = "partially_paid",
        REFUNDED = "refunded",
        PARTIALLY_REFUNDED = "partially_refunded",
        REQUIRES_APP_ACTION = "requires_action",// this is forwarded to & used by app
        SUCCEEDED = "succeeded",
        FAILED = "failed",
        VOIDED = "voided"
    }
    enum TransactionType {
        INTENT = "intent",
        CHARGE = "charge",
        REFUND = "refund",
        PAYOUT = "payout"
    }
    enum AppMethod {
        MANUAL = "manual",// offline payments (in-store or perkdpay)
        CREDITS = "credits",// store credits
        VOUCHER = "voucher",// cash voucher (perkdpay)
        APPLEPAY = "applepay",
        GOOGLEPAY = "googlepay",
        ALIPAY = "alipay",
        LINEPAY = "linepay",// TW
        GRABPAY = "grabpay",// SG, MY, PH
        SHOPEEPAY = "shopeepay",// SG, MY
        PAYNOW = "paynow",// SG
        DUITNOW = "duitnow",// MY
        TNG = "tng",// MY
        BOOST = "boost",// MY
        GCASH = "gcash",// PH
        PAYMAYA = "paymaya"
    }
    enum Method {
        MERCHANT = "merchant",// multi-methods merchant account
        BANK = "bank",
        CARD = "card",
        WALLET = "wallet",
        CASH = "cash",
        STOREDVALUE = "storedvalue",
        CRYPTO = "crypto",
        CREDITCREDITS = "creditcredits",
        REDEEMEDCREDITS = "redeemedcredits",
        REDEEMEDCASH = "redeemedcash",
        SETTLEDCREDITS = "settledcredits",
        MANUAL = "manual",
        CREDITS = "credits",
        VOUCHER = "voucher",
        APPLEPAY = "applepay",
        GOOGLEPAY = "googlepay",
        ALIPAY = "alipay",
        LINEPAY = "linepay",
        GRABPAY = "grabpay",
        SHOPEEPAY = "shopeepay",
        PAYNOW = "paynow",
        DUITNOW = "duitnow",
        TNG = "tng",
        BOOST = "boost",
        GCASH = "gcash",
        PAYMAYA = "paymaya"
    }
    type Intent = any;
    /**
     * Provider-specific, varies based on payment method. For PerkdPay:
     * 	STOREDVALUE: {
     * 		cardId: string		// digital cardId
     * 	}
     * 	VOUCHER: {
     * 		offerId: string		// digital offerId
     * 	}
     * 	MANUAL: {
     * 		type: CASH
     * 	}
     */
    type Charge = any;
    type Refund = any;
    type Payout = any;
    const GATEWAY_METHODS: Method[];
    const CARDS: string[];
    const MANUALS: string[];
    const PART_PAY: Method[];
    interface Transaction {
        id?: string;
        type: TransactionType;
        method: string;
        amount: number;
        currency: string;
        status: Status;
        provider: string;
        referenceId?: string;
        details?: any;
        balance?: number;
        action?: any;
        orderId?: string;
    }
    interface Payment {
        id: string;
        method: Method;
        amount: number;
        currency: string;
        referenceId?: string;
        balance?: number;
        orderId?: string;
    }
    const ERROR: {
        PROVIDER_NOT_FOUND: string;
        AMOUNT_INVALID: string;
        PAYOUT_FAILED: string;
        CANCEL_FAILED: string;
        REFUND_FAILED: string;
        PAYMENT_PENDING: string;
        PAYMENT_DECLINED: string;
        PAYMENT_CARD_DECLINED: string;
        PAYMENT_CARD_EXPIRED: string;
        PAYMENT_INSUFFICIENT_FUNDS: string;
        PAYMENT_IN_PROGRESS: string;
        PAYMENT_TYPE_NOT_FOUND: string;
        PAYMENT_TYPE_NOT_SUPPORTED: string;
        PAYMENT_CANCELED: string;
        PAYMENT_EXPIRED: string;
        PAYMENT_FAILED: string;
        PAYMENT_ACCOUNT_NOT_FOUND: string;
        PAYMENT_INTENT_NOT_FOUND: string;
        PAYMENT_DESTINATION_MISSING: string;
        PAYMENT_DESTINATION_NOT_FOUND: string;
        PAYMENT_NOT_AUTHORIZED: string;
        AMOUNT_TOO_SMALL: string;
    };
    namespace ApplePay {
        type PaymentMethod = {
            displayName: string;
            network: string;
            type: string;
        };
        type PaymentData = {
            data: string;
            signature: string;
            header: {
                publicKeyHash: string;
                ephemeralPublicKey: string;
                transactionId: string;
            };
            version: string;
        };
        type Token = {
            paymentData: PaymentData;
            paymentMethod: PaymentMethod;
            transactionIdentifier: string;
            merchantIdentifier?: string;
        };
    }
    namespace GooglePay {
        enum Environment {
            PRODUCTION = "PRODUCTION",
            TEST = "TEST"
        }
        type CardInfo = {
            cardDetails: string;
            cardNetwork: string;
        };
        type PaymentData = string;
        type Token = {
            cardInfo: CardInfo;
            paymentData: PaymentData;
        };
    }
    export import Processor = Providers.Payment;
    enum Brand {
        VISA = "visa",
        MASTERCARD = "mastercard",
        AMEX = "amex",
        JCB = "jcb",
        DINERS = "diners",
        UNIONPAY = "unionpay",
        DISCOVER = "discover",
        UNKNOWN = "unknown"
    }
    enum Wallet {
        APPLEPAY = "applepay",
        GOOGLEPAY = "googlepay",
        ALIPAY = "alipay",
        LINEPAY = "linepay",
        GRABPAY = "grabpay",
        SAMSUNGPAY = "samsungpay",
        MASTERPASS = "masterpass",
        VISACHECKOUT = "visacheckout",
        AMEXEXPRESSCHECKOUT = "amexexpresscheckout"
    }
    enum Mode {
        CAPTURE = "capture"
    }
    enum RiskLevel {
        NORMAL = "normal",
        ELEVATED = "elevated",
        HIGHEST = "highest"
    }
}
