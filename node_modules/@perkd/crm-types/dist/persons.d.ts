import { Contacts } from './contacts';
export declare namespace Persons {
    enum Gender {
        MALE = "m",
        FEMALE = "f"
    }
    enum NameOrder {
        FAMILY_GIVEN = "familygiven",
        GIVEN_FAMILY = "givenfamily"
    }
    enum Identities {
        PERKD = "perkd",
        USER = "user",
        CUSTOMER = "customer",
        SMARTCOLLECTION = "smartcollection",
        NATIONAL = "national",
        REGISTRATION = "registration",
        PASSPORT = "passport",
        DRIVER = "driver",
        PET = "pet"
    }
    enum PermissionChannel {
        MOBILE = "mobile",
        EMAIL = "email",
        POSTAL = "postal",
        SERVICE_TERMS = "serviceTerms",
        PRIVACY_POLICY = "privacyPolicy"
    }
    enum PermissionStatus {
        DO_NOT_DISTURB = -2,
        OPTOUT = -1,
        UNKNOWN = 0,
        OPTIN = 1
    }
    enum Residency {
        CITIZEN = "citizen",
        RESIDENT = "resident",
        EMPLOYMENT = "employment"
    }
    enum BloodType {
        A_POSITIVE = "A+",
        A_NEGATIVE = "A-",
        B_POSITIVE = "B+",
        B_NEGATIVE = "B-",
        O_POSITIVE = "O+",
        O_NEGATIVE = "O-",
        AB_POSITIVE = "AB+",
        AB_NEGATIVE = "AB-"
    }
    enum Species {
        DOG = "dog",
        CAT = "cat",
        BIRD = "bird",
        RABBIT = "rabbit",
        RODENT = "rodent"
    }
    type Nationality = {
        country: string;
        status: Residency;
    };
    type Religion = {
        code: string;
        branch?: string;
        state?: {
            name: string;
            date?: Date;
            at?: string;
            by?: string;
        };
    };
    type Body = Partial<{
        height: number;
        bust: number;
        crossback: number;
        waist: number;
        hip: number;
        footLength: number;
        weight: number;
        bloodType: BloodType;
        neutered: Boolean;
    }>;
    type Identity = {
        id?: string;
        provider: string;
        externalId: string;
        identity: string;
        type?: string;
        credentials?: any;
        country?: string;
        data?: any;
        authScheme?: string;
        valid?: {
            start: Date;
            end: Date;
        };
    };
    type Permission = {
        channel: PermissionChannel;
        status: PermissionStatus;
        grantedAt?: string | null;
        revokedAt?: string | null;
    };
    type Company = {
        name: string;
        position?: string;
        registrationNumber?: string;
        industry?: string;
        startTime?: Date;
        endTime?: Date;
    };
    type Person = {
        familyName?: string;
        givenName: string;
        alias?: string;
        name?: {
            order: NameOrder;
            language?: string;
            salutation?: string;
        };
        gender?: Gender;
        locale?: Contacts.Locale;
        ethinicity?: number;
        nationality?: Nationality;
        religion?: Religion;
        body?: Body;
        phoneList?: Contacts.Phone[];
        emailList?: Contacts.Email[];
        dateList?: {
            name: string;
            date?: Date;
            year: number;
            month: number;
            day: number;
        }[];
        permissionList?: Permission[];
        identityList?: Identity[];
        companyList?: Company[];
        profileImageUrl?: string;
        tags?: {
            system: string[];
            user: string[];
        };
        species?: Species;
        breed?: string;
    };
    const PROFILE: {
        FAMILY_GIVEN: NameOrder;
        GIVEN_FAMILY: NameOrder;
        NAME_ORDER: NameOrder[];
        MOBILE: Contacts.Type;
        BIRTH_DATE: Contacts.Dates;
    };
}
export declare namespace Socials {
    enum Relationship {
        FRIEND = "friend",
        SPOUSE = "spouse",
        PARENT = "parent",
        CHILD = "child",
        SIBLING = "sibling",
        COLLEAGUE = "colleague",
        CLASSMATE = "classmate",
        PET = "pet"
    }
    enum InverseRelationship {
        parent = "child",
        child = "parent",
        pet = "owner"
    }
}
