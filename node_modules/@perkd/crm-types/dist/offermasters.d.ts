import { Barcodes } from './barcodes';
import { Discounts } from './offers';
import { Touchpoints } from './touchpoints';
export declare namespace OfferMasters {
    enum Kind {
        DISCOUNT = "discount",
        VOUCHER = "voucher",
        TICKET = "ticket",
        PICKUP = "pickup"
    }
    enum Channel {
        STORE = "store",
        ONLINE = "online",
        PERKD = "perkd"
    }
    enum State {
        INCOMPLETE = "incomplete",
        READY = "ready",
        ACTIVE = "active",
        EXPIRED = "expired",
        ARCHIVED = "archived",
        UNCOMMIT = "uncommit"
    }
    enum CodeFormat {
        FIXED = "fixed",
        SERIAL = "serial",
        PRESET = "preset",
        RANDOM = "random"
    }
    enum Authorize {
        SCAN = "scan",
        NFC = "nfc"
    }
    type Issuance = {
        start?: any;
        end?: any;
        limit: number | null;
        remain: number | null;
        count: number;
        max?: {
            perPerson: number | null;
            perMembership: number | null;
            count: number | null;
        };
        tiers?: {
            programId: string;
            level: number;
        }[];
        channels: {
            type: Channel[];
        };
        triggers?: any;
    };
    type Redemption = {
        start?: any;
        end?: any;
        limit: number | null;
        remain: number | null;
        count: number;
        max?: {
            perPerson: number | null;
            perMembership: number | null;
        };
        channels: {
            type: Channel[];
        };
        authorize?: Authorize;
        through?: Touchpoints.Touchpoint;
    };
    type ButtonLink = {
        mode: ('app' | 'web' | 'url' | 'sso')[];
        app?: any;
        web?: string;
        url?: string;
        sso?: string;
        platform?: {
            ios: any;
            android: any;
        };
    };
    type OfferMaster = {
        id: string;
        key: string;
        name: string;
        shortName: string;
        kind: Kind;
        discount: Discounts.Discount;
        title?: string;
        description?: string;
        terms?: string;
        barcodeType?: Barcodes.Type;
        barcode?: string;
        codeFormat?: CodeFormat;
        codeLength?: number;
        code?: string;
        activePeriod: {
            startTime: Date;
            endTime?: Date | null;
        };
        issuance: Issuance;
        redemption?: Redemption;
        images?: any[];
        qualifiers?: any;
        enabled: boolean;
        state: State;
        options: {
            appOnly?: boolean;
            redeemOnline?: boolean;
            payment?: boolean;
            hideButton?: boolean;
            buttonLabel?: string;
            buttonLink?: ButtonLink;
            action?: any;
            groupCheckin?: boolean;
            showVenueName?: boolean;
        };
        external?: any;
        globalize?: any;
        deletedAt?: Date;
    };
}
