import { Places } from './places';
export declare namespace Fulfillments {
    export enum Type {
        DIGITAL = "digital",
        KITCHEN = "kitchen",
        STORE = "store",
        PICKUP = "pickup",
        DELIVER = "deliver",
        DINEIN = "dinein",
        VENDING_MACHINE = "vending"
    }
    export enum Service {
        NONE = "none",
        STORE = "store",
        DIGITAL = "digital",
        KITCHEN = "kitchen",
        VENDING_MACHINE = "vending",
        SHOPIFY = "shopify"
    }
    export enum Priority {
        NORMAL = "normal",
        HIGH = "high",
        HIGHEST = "highest"
    }
    export enum Status {
        NULL = "null",
        PENDING = "pending",
        OPEN = "open",
        SUCCESS = "success",
        CANCELLED = "cancelled",
        FAILURE = "failure",
        ERROR = "error",
        PARTIAL = "partial"
    }
    export enum DestinationType {
        STORE = "store",
        C_STORE = "convenience_store",
        VENDING_MACHINE = "vending"
    }
    export enum Step {
        ORDERED = "ordered",
        REQUESTED = "requested",
        ALLOCATED = "allocated",
        QUEUED = "queued",
        PREPARE = "prepare",
        PACKED = "packed",
        COLLECTED = "collected",
        DELIVERED = "delivered"
    }
    export enum DeliverStatus {
        LABEL_PRINTED = "labelprinted",
        READY_FOR_PICKUP = "readyforpickup",
        ATTEMPTED_DELIVERY = "attempteddelivery",
        IN_TRANSSIT = "intransit",
        DELIVERING = "outfordelivery",
        DELIVERED = "delivered",
        FAILURE = "failure"
    }
    export const STEPS: {
        dinein: Step[];
        store: Step[];
        pickup: Step[];
        deliver: Step[];
        vending: Step[];
        kitchen: Step[];
    };
    export type Deliver = {
        carrierName?: string;
        carrierId?: string;
        serviceName?: string;
        serviceCode?: string;
        cost?: number;
        currency?: string;
        pickup?: Date;
        status?: DeliverStatus;
        trackingNumber?: string;
        trackingUrl?: string;
        ticket?: string;
    };
    export type Item = {
        id: string;
        productId?: string;
        variantId: string;
        quantity: number;
        units?: number;
        fulfilled?: {
            quantity: number;
            units?: number;
        };
        filfilledAt?: Date;
        digitalIds?: string[];
    };
    export type Flow = {
        at: number;
        steps: Step[];
    };
    export type MinMaxTime = {
        minTime: Date;
        maxTime: Date;
    };
    export type Recipient = {
        fullName: string;
        givenName?: string;
        familyName?: string;
        gender?: string;
        countryCode?: number;
        phone?: string;
        email?: string;
        address?: string;
        profileImageUrl?: string;
        membershipId?: string;
    };
    type When = {
        [name: string]: Date | null;
    };
    export type Fulfillment = {
        type: Type;
        recipient?: Recipient;
        priority?: Priority;
        destination?: Places.Spot;
        origin?: Places.Spot;
        scheduled?: MinMaxTime;
        minTime?: Date;
        maxTime?: Date;
        itemList?: Item[];
        currency?: string;
        unitPrice?: number;
        discountAmount?: number;
        tax?: number;
        price?: number;
        note?: string;
        flow?: Flow;
        status: Status;
        prepare?: {
            startTime: Date;
            endTime: Date;
        };
        pickup?: MinMaxTime;
        deliver?: Deliver;
        provider?: string;
        receipt?: {
            queue?: string;
            authorisation?: string;
        };
        external?: any;
        when?: When;
        placeId?: string;
        mainFulfillmentType?: Omit<Type, Type.DIGITAL | Type.KITCHEN | Type.STORE | Type.VENDING_MACHINE>;
        mainFulfillmentId?: string;
    };
    export namespace Delivery {
        enum Status {
            PENDING = "pending",
            PICKING_UP = "pickingup",
            DELIVERING = "delivering",
            COMPLETED = "completed",
            CANCELLED = "cancelled",
            FAILED = "failed",
            RETURNED = "returned"
        }
    }
    export namespace Pickup {
        enum Type {
            STORE = "store",
            C_STORE = "convenience_store",
            VENDING_MACHINE = "vending"
        }
    }
    export {};
}
