"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bookings = void 0;
var Bookings;
(function (Bookings) {
    let Status;
    (function (Status) {
        Status["PENDING"] = "pending";
        Status["OPEN"] = "open";
        Status["SUCCESS"] = "success";
        Status["ENDED"] = "ended";
        Status["CANCELLED"] = "cancelled";
        Status["NOSHOW"] = "noshow";
        Status["ERROR"] = "error";
    })(Status = Bookings.Status || (Bookings.Status = {}));
    let Unit;
    (function (Unit) {
        Unit["SECOND"] = "second";
        Unit["MINUTE"] = "minute";
        Unit["HOUR"] = "hour";
        Unit["DAY"] = "day";
    })(Unit = Bookings.Unit || (Bookings.Unit = {}));
})(Bookings || (exports.Bookings = Bookings = {}));
//# sourceMappingURL=bookings.js.map