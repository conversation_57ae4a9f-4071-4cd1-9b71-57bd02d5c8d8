"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Countries = void 0;
const tslib_1 = require("tslib");
/**
 * Primarily used by Wallet app
 */
const Codes_json_1 = tslib_1.__importDefault(require("./country/Codes.json"));
const Names_json_1 = tslib_1.__importDefault(require("./country/Names.json"));
const Headers_json_1 = tslib_1.__importDefault(require("./country/Headers.json"));
const Filters_json_1 = tslib_1.__importDefault(require("./country/Filters.json"));
const ISO3166_json_1 = tslib_1.__importDefault(require("./country/ISO3166.json"));
var Countries;
(function (Countries) {
    /* source: https://countrycode.org/ */
    Countries.CODE = Codes_json_1.default;
    /* CLDR Unicode source: https://www.npmjs.com/package/cldr */
    Countries.NAME = Names_json_1.default;
    Countries.HEADER = Headers_json_1.default;
    Countries.FILTER = Filters_json_1.default;
    // https://www.iso.org/obp/ui/#search
    // https://zh.wikipedia.org/wiki/ISO_3166
    // https://zh.wikipedia.org/wiki/ISO_3166-1
    // https://github.com/vtex/country-iso-3-to-2/blob/master/index.js
    Countries.ISO3166 = ISO3166_json_1.default;
})(Countries || (exports.Countries = Countries = {}));
//# sourceMappingURL=countries.js.map