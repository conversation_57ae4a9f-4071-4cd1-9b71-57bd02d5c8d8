import { Fulfillments } from './fulfillments';
import { Places } from './places';
import { Orders } from './orders';
import { Discounts } from './offers';
import { Providers } from './providers';
import { Payments } from './payments';
/**
 * Incoming order from app/service
 */
export declare namespace AppOrders {
    type Tax = Orders.Tax;
    type Discount = Discounts.Discount;
    type Option = Orders.Option;
    type Bundle = Item[];
    type Item = Omit<Orders.Item, 'product' | 'variant' | 'discountAmount'> & {
        productId: string;
        variantId: string;
        discount: Discount;
        bundles?: Bundle;
        options?: Option[];
    };
    type Receipt = {
        number?: string;
        taxId?: string;
        merchantTaxId?: string;
        invoice?: any;
        send?: boolean;
    };
    type Fulfillment = {
        type: Fulfillments.Type;
        items?: Fulfillments.Item[];
        status: Fulfillments.Status;
        price: number;
        discount: Discount;
        tax?: number;
        unitPrice?: number;
        currency?: string;
        origin?: Places.Spot;
        destination?: Places.Spot;
        recipient?: Fulfillments.Recipient;
        minTime?: Date;
        maxTime?: Date;
        scheduled?: {
            minTime: Date;
            maxTime: Date;
        };
        note?: string;
    };
    type Order = {
        id?: string;
        channel?: string;
        items?: Item[];
        currency?: string;
        subtotalPrice: number;
        shippingPrice?: number;
        totalDiscounts?: number;
        totalTax: number;
        totalPrice: number;
        discounts?: Discount[];
        taxes?: Tax[];
        taxIncluded?: boolean;
        receipt?: Receipt;
        fulfillment?: Fulfillment;
        storeId?: string;
        external?: any;
    };
    type ExtendedOrder = Order & {
        userId?: string;
        personId?: string;
        memberId?: string;
        membershipId?: string;
        sourceType?: string;
    };
    type Payment = {
        method: Payments.AppMethod;
        amount: number;
        provider?: Providers.Payment;
        intent?: {
            paymentMethodId: string;
            customerId?: string;
            cardId?: string;
            destinationId?: string;
        };
    };
}
