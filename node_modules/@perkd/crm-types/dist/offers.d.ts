import { OfferMasters } from './offermasters';
import { Billings } from './billings';
import { Barcodes } from './barcodes';
type Tax = Billings.Tax;
export declare namespace Discounts {
    enum Kind {
        FIXED = "fixed",
        PERCENTAGE = "percentage"
    }
    enum Use {
        ALWAYS = "always",
        ALONE = "alone",
        ONE = "one",
        COMBINE = "combine",
        SINGLE = "single"
    }
    enum TargetType {
        ITEM = "item",// discount applies to line items
        SHIPPING = "shipping"
    }
    enum TargetSelection {
        ALL = "all",
        ENTITLED = "entitled"
    }
    enum AllocationMethod {
        EACH = "each",// discount applied to each entitled items
        ACROSS = "across"
    }
    type Qualifiers = {
        redeem?: string;
        prerequisite?: string;
        entitled?: string;
    };
    type Options = {
        name: string;
        required: boolean;
        multiple?: number;
        values: any[];
    };
    type Item = {
        variantId: string;
        title: string;
        unitPrice: number;
        price: number;
        options?: Options[];
        taxes?: Tax[];
        images?: string[];
        tags?: string[];
    };
    type Discount = {
        id?: string;
        name: string;
        kind: Kind;
        value: number;
        currency?: string;
        amount?: number;
        use?: Use;
        priority?: number;
        qualifiers?: Qualifiers;
        targetType?: TargetType;
        targetSelection?: TargetSelection;
        allocation?: {
            method: AllocationMethod;
            limit: number | null;
        };
        entitled?: {
            prerequisiteQuantity?: number;
            entitledQuantity?: number;
            items?: Item[];
        };
        excludeOffers?: string[];
        code?: string;
        immutable?: boolean;
        external?: any;
        offerId?: string;
        offerMasterId?: string;
    };
}
export declare namespace Offers {
    enum Kind {
        DISCOUNT = "discount",
        TICKET = "ticket",
        VOUCHER = "voucher",// cash voucher (sold), treat as payment method when discount.kind is fixed (enable part-pay)
        PICKUP = "pickup"
    }
    enum State {
        PENDING = "pending",
        ACTIVE = "active",
        AUTHORIZED = "authorized",// used by VOUCHER only
        REDEEMED = "redeemed",
        FULLY_REDEEMED = "fullyredeemed",
        CANCELLED = "cancelled",
        EXPIRED = "expired",
        TRANSFERRING = "transferring",
        TRANSFERRED = "transferred"
    }
    export import RedemptionChannel = OfferMasters.Channel;
    type Offer = {
        id: string;
        kind: OfferMasters.Kind;
        code: {
            store?: string;
            online?: string;
        };
        barcodeType?: Barcodes.Type;
        barcode?: string;
        startTime?: Date;
        endTime?: Date;
        images?: Array<{
            url: string;
        }>;
        discount: Discounts.Discount;
        redemption?: OfferMasters.Redemption;
        options: {
            appOnly?: boolean;
            redeemOnline?: boolean;
            payment?: boolean;
            hideButton?: boolean;
            buttonLabel?: string;
            buttonLink?: OfferMasters.ButtonLink;
            action?: any;
            groupCheckin?: boolean;
            showVenueName?: boolean;
        };
        state: State;
        digital?: {
            id?: string;
        };
        external?: any;
        style?: any;
        venue?: any;
        fields?: Array<{
            key: string;
            value: string;
        }>;
        applet?: any;
        checkin?: {
            startTime: Date;
            endTime: Date;
            resourceId?: string;
        };
        items?: Array<{
            id: string;
            variantId: string;
            sku?: string;
            quantity: number;
        }>;
        sharer?: any;
        when?: {
            sent?: Date;
            issued?: Date;
            received?: Date;
            notified?: Date;
            viewed?: Date;
            authorized?: Date;
            redeemed?: Date;
            cancelled?: Date;
            recovered?: Date;
            transferred?: Date;
        };
        reasons?: Record<string, any>;
        visible?: boolean;
        createdAt?: Date;
        modifiedAt?: Date;
        deletedAt?: Date;
        masterId?: string;
        personId?: string;
        memberId?: string;
        membershipId?: string;
        campaignId?: string;
        orderId?: string;
        bookingId?: string;
        staffId?: string;
    };
}
export {};
