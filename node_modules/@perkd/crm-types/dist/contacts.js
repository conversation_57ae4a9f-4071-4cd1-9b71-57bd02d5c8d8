"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Contacts = void 0;
var Contacts;
(function (Contacts) {
    let Type;
    (function (Type) {
        Type["MOBILE"] = "mobile";
        Type["HOME"] = "home";
        Type["WORK"] = "work";
        Type["OTHERS"] = "others";
    })(Type = Contacts.Type || (Contacts.Type = {}));
    let Dates;
    (function (Dates) {
        Dates["BIRTH"] = "birth";
        Dates["GRADUATE"] = "graduate";
        Dates["MARRIED"] = "married";
        Dates["BAPTISED"] = "baptised";
        // business
        Dates["CONTRACT_START"] = "contractstart";
        Dates["CONTRACT_END"] = "contractend";
    })(Dates = Contacts.Dates || (Contacts.Dates = {}));
    let UrlKind;
    (function (UrlKind) {
        UrlKind["WEBSITE"] = "website";
        UrlKind["EMAIL"] = "email";
        UrlKind["SOCIAL"] = "social";
        UrlKind["CUSTOM"] = "custom";
    })(UrlKind = Contacts.UrlKind || (Contacts.UrlKind = {}));
})(Contacts || (exports.Contacts = Contacts = {}));
//# sourceMappingURL=contacts.js.map