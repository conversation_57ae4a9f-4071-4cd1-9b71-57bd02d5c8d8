"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Payments = void 0;
const providers_1 = require("./providers");
var Payments;
(function (Payments) {
    let Status;
    (function (Status) {
        Status["AUTHORIZED"] = "authorized";
        Status["CHARGEABLE"] = "chargeable";
        Status["PENDING"] = "pending";
        Status["CANCELLED"] = "cancelled";
        Status["PAID"] = "paid";
        Status["PARTIALLY_PAID"] = "partially_paid";
        Status["REFUNDED"] = "refunded";
        Status["PARTIALLY_REFUNDED"] = "partially_refunded";
        Status["REQUIRES_APP_ACTION"] = "requires_action";
        Status["SUCCEEDED"] = "succeeded";
        Status["FAILED"] = "failed";
        Status["VOIDED"] = "voided";
    })(Status = Payments.Status || (Payments.Status = {}));
    let TransactionType;
    (function (TransactionType) {
        TransactionType["INTENT"] = "intent";
        TransactionType["CHARGE"] = "charge";
        TransactionType["REFUND"] = "refund";
        TransactionType["PAYOUT"] = "payout";
    })(TransactionType = Payments.TransactionType || (Payments.TransactionType = {}));
    let AppMethod;
    (function (AppMethod) {
        AppMethod["MANUAL"] = "manual";
        AppMethod["CREDITS"] = "credits";
        AppMethod["VOUCHER"] = "voucher";
        AppMethod["APPLEPAY"] = "applepay";
        AppMethod["GOOGLEPAY"] = "googlepay";
        AppMethod["ALIPAY"] = "alipay";
        AppMethod["LINEPAY"] = "linepay";
        AppMethod["GRABPAY"] = "grabpay";
        AppMethod["SHOPEEPAY"] = "shopeepay";
        AppMethod["PAYNOW"] = "paynow";
        AppMethod["DUITNOW"] = "duitnow";
        AppMethod["TNG"] = "tng";
        AppMethod["BOOST"] = "boost";
        AppMethod["GCASH"] = "gcash";
        AppMethod["PAYMAYA"] = "paymaya";
    })(AppMethod = Payments.AppMethod || (Payments.AppMethod = {}));
    let Method;
    (function (Method) {
        // app: type
        Method["MERCHANT"] = "merchant";
        Method["BANK"] = "bank";
        Method["CARD"] = "card";
        Method["WALLET"] = "wallet";
        Method["CASH"] = "cash";
        Method["STOREDVALUE"] = "storedvalue";
        Method["CRYPTO"] = "crypto";
        // wallets for stored value
        Method["CREDITCREDITS"] = "creditcredits";
        Method["REDEEMEDCREDITS"] = "redeemedcredits";
        Method["REDEEMEDCASH"] = "redeemedcash";
        Method["SETTLEDCREDITS"] = "settledcredits";
        // app: method
        Method["MANUAL"] = "manual";
        Method["CREDITS"] = "credits";
        Method["VOUCHER"] = "voucher";
        Method["APPLEPAY"] = "applepay";
        Method["GOOGLEPAY"] = "googlepay";
        Method["ALIPAY"] = "alipay";
        Method["LINEPAY"] = "linepay";
        Method["GRABPAY"] = "grabpay";
        Method["SHOPEEPAY"] = "shopeepay";
        Method["PAYNOW"] = "paynow";
        Method["DUITNOW"] = "duitnow";
        Method["TNG"] = "tng";
        Method["BOOST"] = "boost";
        Method["GCASH"] = "gcash";
        Method["PAYMAYA"] = "paymaya";
    })(Method = Payments.Method || (Payments.Method = {}));
    const { CARD, ALIPAY, LINEPAY, GRABPAY, TNG, BANK, CRYPTO, STOREDVALUE, MANUAL, WALLET, VOUCHER, CASH, PAYNOW } = Method;
    Payments.GATEWAY_METHODS = [CARD, ALIPAY, LINEPAY, GRABPAY, TNG, BANK, CRYPTO, STOREDVALUE, MANUAL, VOUCHER];
    Payments.CARDS = ['visa', 'mastercard', 'amex', 'jcb', 'diners', 'unionpay', 'discover'];
    Payments.MANUALS = [CARD, GRABPAY, TNG, CASH, PAYNOW, 'nets', 'atome', 'favpay', 'shopback'];
    Payments.PART_PAY = [STOREDVALUE, WALLET, VOUCHER]; // supports multi-part payment
    Payments.ERROR = {
        PROVIDER_NOT_FOUND: 'provider_not_found',
        AMOUNT_INVALID: 'payment_amount_invalid',
        PAYOUT_FAILED: 'payment_payout_failed',
        CANCEL_FAILED: 'payment_cancel_failed',
        REFUND_FAILED: 'payment_refund_failed',
        PAYMENT_PENDING: 'payment_pending',
        PAYMENT_DECLINED: 'payment_declined',
        PAYMENT_CARD_DECLINED: 'payment_card_declined',
        PAYMENT_CARD_EXPIRED: 'payment_card_expired',
        PAYMENT_INSUFFICIENT_FUNDS: 'payment_insufficient_funds',
        PAYMENT_IN_PROGRESS: 'payment_in_progress',
        PAYMENT_TYPE_NOT_FOUND: 'payment_type_not_found',
        PAYMENT_TYPE_NOT_SUPPORTED: 'payment_type_not_supported',
        PAYMENT_CANCELED: 'payment_canceled',
        PAYMENT_EXPIRED: 'payment_expired',
        PAYMENT_FAILED: 'payment_failed',
        PAYMENT_ACCOUNT_NOT_FOUND: 'payment_account_not_found',
        PAYMENT_INTENT_NOT_FOUND: 'payment_intent_not_found',
        PAYMENT_DESTINATION_MISSING: 'payment_destination_missing',
        PAYMENT_DESTINATION_NOT_FOUND: 'payment_destination_not_found',
        PAYMENT_NOT_AUTHORIZED: 'payment_not_authorized',
        AMOUNT_TOO_SMALL: 'amount_too_small'
    };
    let GooglePay;
    (function (GooglePay) {
        let Environment;
        (function (Environment) {
            Environment["PRODUCTION"] = "PRODUCTION";
            Environment["TEST"] = "TEST";
        })(Environment = GooglePay.Environment || (GooglePay.Environment = {}));
    })(GooglePay = Payments.GooglePay || (Payments.GooglePay = {}));
    Payments.Processor = providers_1.Providers.Payment;
    let Brand;
    (function (Brand) {
        Brand["VISA"] = "visa";
        Brand["MASTERCARD"] = "mastercard";
        Brand["AMEX"] = "amex";
        Brand["JCB"] = "jcb";
        Brand["DINERS"] = "diners";
        Brand["UNIONPAY"] = "unionpay";
        Brand["DISCOVER"] = "discover";
        Brand["UNKNOWN"] = "unknown";
    })(Brand = Payments.Brand || (Payments.Brand = {}));
    let Wallet;
    (function (Wallet) {
        Wallet["APPLEPAY"] = "applepay";
        Wallet["GOOGLEPAY"] = "googlepay";
        Wallet["ALIPAY"] = "alipay";
        Wallet["LINEPAY"] = "linepay";
        Wallet["GRABPAY"] = "grabpay";
        Wallet["SAMSUNGPAY"] = "samsungpay";
        Wallet["MASTERPASS"] = "masterpass";
        Wallet["VISACHECKOUT"] = "visacheckout";
        Wallet["AMEXEXPRESSCHECKOUT"] = "amexexpresscheckout";
    })(Wallet = Payments.Wallet || (Payments.Wallet = {}));
    let Mode;
    (function (Mode) {
        Mode["CAPTURE"] = "capture";
    })(Mode = Payments.Mode || (Payments.Mode = {}));
    let RiskLevel;
    (function (RiskLevel) {
        RiskLevel["NORMAL"] = "normal";
        RiskLevel["ELEVATED"] = "elevated";
        RiskLevel["HIGHEST"] = "highest";
    })(RiskLevel = Payments.RiskLevel || (Payments.RiskLevel = {}));
})(Payments || (exports.Payments = Payments = {}));
//# sourceMappingURL=payments.js.map