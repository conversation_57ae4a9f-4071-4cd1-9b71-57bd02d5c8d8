"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Triggers = void 0;
var Triggers;
(function (Triggers) {
    let Type;
    (function (Type) {
        Type["TIMER"] = "timer";
        Type["EVENT"] = "event";
        Type["REQUEST"] = "request";
    })(Type = Triggers.Type || (Triggers.Type = {}));
    let State;
    (function (State) {
        State["SCHEDULED"] = "scheduled";
        State["RUNNING"] = "running";
        State["PAUSED"] = "paused";
        State["COMPLETED"] = "completed";
        State["FAILED"] = "failed";
        State["ENDED"] = "ended";
    })(State = Triggers.State || (Triggers.State = {}));
})(Triggers || (exports.Triggers = Triggers = {}));
//# sourceMappingURL=triggers.js.map