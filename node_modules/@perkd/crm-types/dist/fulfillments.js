"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Fulfillments = void 0;
const places_1 = require("./places");
var Fulfillments;
(function (Fulfillments) {
    let Type;
    (function (Type) {
        Type["DIGITAL"] = "digital";
        Type["KITCHEN"] = "kitchen";
        Type["STORE"] = "store";
        Type["PICKUP"] = "pickup";
        Type["DELIVER"] = "deliver";
        Type["DINEIN"] = "dinein";
        Type["VENDING_MACHINE"] = "vending";
    })(Type = Fulfillments.Type || (Fulfillments.Type = {}));
    let Service;
    (function (Service) {
        Service["NONE"] = "none";
        Service["STORE"] = "store";
        Service["DIGITAL"] = "digital";
        Service["KITCHEN"] = "kitchen";
        Service["VENDING_MACHINE"] = "vending";
        Service["SHOPIFY"] = "shopify";
    })(Service = Fulfillments.Service || (Fulfillments.Service = {}));
    let Priority;
    (function (Priority) {
        Priority["NORMAL"] = "normal";
        Priority["HIGH"] = "high";
        Priority["HIGHEST"] = "highest";
    })(Priority = Fulfillments.Priority || (Fulfillments.Priority = {}));
    let Status;
    (function (Status) {
        Status["NULL"] = "null";
        Status["PENDING"] = "pending";
        Status["OPEN"] = "open";
        Status["SUCCESS"] = "success";
        Status["CANCELLED"] = "cancelled";
        Status["FAILURE"] = "failure";
        Status["ERROR"] = "error";
        Status["PARTIAL"] = "partial";
    })(Status = Fulfillments.Status || (Fulfillments.Status = {}));
    let DestinationType;
    (function (DestinationType) {
        DestinationType["STORE"] = "store";
        DestinationType["C_STORE"] = "convenience_store";
        DestinationType["VENDING_MACHINE"] = "vending";
    })(DestinationType = Fulfillments.DestinationType || (Fulfillments.DestinationType = {}));
    let Step;
    (function (Step) {
        Step["ORDERED"] = "ordered";
        Step["REQUESTED"] = "requested";
        Step["ALLOCATED"] = "allocated";
        Step["QUEUED"] = "queued";
        Step["PREPARE"] = "prepare";
        Step["PACKED"] = "packed";
        Step["COLLECTED"] = "collected";
        Step["DELIVERED"] = "delivered";
    })(Step = Fulfillments.Step || (Fulfillments.Step = {}));
    let DeliverStatus;
    (function (DeliverStatus) {
        DeliverStatus["LABEL_PRINTED"] = "labelprinted";
        DeliverStatus["READY_FOR_PICKUP"] = "readyforpickup";
        DeliverStatus["ATTEMPTED_DELIVERY"] = "attempteddelivery";
        DeliverStatus["IN_TRANSSIT"] = "intransit";
        DeliverStatus["DELIVERING"] = "outfordelivery";
        DeliverStatus["DELIVERED"] = "delivered";
        DeliverStatus["FAILURE"] = "failure";
    })(DeliverStatus = Fulfillments.DeliverStatus || (Fulfillments.DeliverStatus = {}));
    Fulfillments.STEPS = {
        [Type.DINEIN]: [Step.ORDERED, Step.REQUESTED, Step.PREPARE, Step.PACKED],
        [Type.STORE]: [Step.ORDERED, Step.REQUESTED, Step.PREPARE, Step.PACKED, Step.COLLECTED],
        [Type.PICKUP]: [Step.ORDERED, Step.REQUESTED, Step.PREPARE, Step.PACKED, Step.COLLECTED],
        [Type.DELIVER]: [Step.ORDERED, Step.REQUESTED, Step.ALLOCATED, Step.COLLECTED, Step.DELIVERED],
        [Type.VENDING_MACHINE]: [Step.ORDERED, Step.REQUESTED, Step.COLLECTED],
        // sub-fulfillments
        [Type.KITCHEN]: [Step.ORDERED, Step.REQUESTED, Step.QUEUED, Step.PREPARE, Step.PACKED]
    };
    let Delivery;
    (function (Delivery) {
        let Status;
        (function (Status) {
            Status["PENDING"] = "pending";
            Status["PICKING_UP"] = "pickingup";
            Status["DELIVERING"] = "delivering";
            Status["COMPLETED"] = "completed";
            Status["CANCELLED"] = "cancelled";
            Status["FAILED"] = "failed";
            Status["RETURNED"] = "returned";
        })(Status = Delivery.Status || (Delivery.Status = {}));
    })(Delivery = Fulfillments.Delivery || (Fulfillments.Delivery = {}));
    let Pickup;
    (function (Pickup) {
        let Type;
        (function (Type) {
            Type["STORE"] = "store";
            Type["C_STORE"] = "convenience_store";
            Type["VENDING_MACHINE"] = "vending";
        })(Type = Pickup.Type || (Pickup.Type = {}));
    })(Pickup = Fulfillments.Pickup || (Fulfillments.Pickup = {}));
})(Fulfillments || (exports.Fulfillments = Fulfillments = {}));
//# sourceMappingURL=fulfillments.js.map