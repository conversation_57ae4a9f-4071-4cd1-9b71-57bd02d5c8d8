"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Apis = void 0;
var Apis;
(function (Apis) {
    let Headers;
    (function (Headers) {
        // app api
        Headers["X_ACCESS_TOKEN"] = "x-access-token";
        Headers["INSTALL"] = "perkd-install";
        Headers["LOCATION"] = "perkd-location";
        Headers["CARD"] = "card";
        // between loopback services
        /*
            LB3 requesting to LB3 - tenant-user
            LB3 requestion to LB4 - tenant-user
            LB4 requesting to LB3 - authorization
            LB4 requesting to LB4 - authorization
        */
        Headers["AUTHORIZATION"] = "authorization";
        Headers["TENANT"] = "tenant-code";
        Headers["USER"] = "tenant-user";
        // to deprecate
        Headers["FORWARDED_FOR"] = "x-forwarded-for";
        Headers["IDEMPOTENCY_KEY"] = "idempotency-key";
        Headers["USER_AGENT"] = "user-agent";
        Headers["BEARER"] = "Bearer ";
        Headers["TIMEZONE"] = "timezone";
        Headers["ORIGIN"] = "origin";
        Headers["APP_NAME"] = "app-name";
    })(Headers = Apis.Headers || (Apis.Headers = {}));
    let Parameters;
    (function (Parameters) {
        Parameters["ACCESS_TOKEN"] = "access_token";
        Parameters["TENANT"] = "tenant_code";
    })(Parameters = Apis.Parameters || (Apis.Parameters = {}));
    let ENV;
    (function (ENV) {
        ENV["DEV"] = "development";
        ENV["TEST"] = "test";
        ENV["PROD"] = "production";
    })(ENV = Apis.ENV || (Apis.ENV = {}));
})(Apis || (exports.Apis = Apis = {}));
//# sourceMappingURL=apis.js.map