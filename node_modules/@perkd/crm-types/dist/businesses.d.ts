type Time = {
    time: string;
};
type DayTime = Time & {
    day: number;
};
type YearMonthDay = {
    year: number;
    month: number;
    day: number;
};
export declare namespace Businesses {
    enum Identities {
        NATIONAL = "national",
        REGISTRATION = "registration"
    }
    enum UrlKind {
        SIGNUP = "signup"
    }
    type Brand = {
        long: string;
        short?: string;
        color?: string;
        logo?: {
            id: string;
            url: string;
        };
    };
    type Period = {
        open: DayTime;
        close: DayTime;
        busy?: DayTime;
    };
    type TimePeriod = {
        open: Time;
        close: Time;
        busy?: Time;
    };
    type Hours = {
        specific?: {
            date: YearMonthDay;
            periods: TimePeriod[];
        }[];
        periods: Period[];
    };
}
export declare namespace Staffs {
    enum Title {
        OWNER = "owner"
    }
}
export {};
