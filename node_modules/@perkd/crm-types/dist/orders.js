"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Orders = void 0;
const fulfillments_1 = require("./fulfillments");
const { Type } = fulfillments_1.Fulfillments;
var Orders;
(function (Orders) {
    let Step;
    (function (Step) {
        Step["NEW"] = "new";
        Step["RECEIVED"] = "received";
        Step["PACKED"] = "packed";
        Step["ALLOCATED"] = "allocated";
        Step["COLLECTED"] = "collected";
        Step["DELIVERED"] = "delivered";
        Step["ACCEPTED"] = "accepted";
        Step["DECLINED"] = "declined";
        Step["FULFILLED"] = "fulfilled";
        Step["CANCELLED"] = "cancelled";
        Step["RETURNED"] = "returned";
    })(Step = Orders.Step || (Orders.Step = {}));
    let State;
    (function (State) {
        State["PENDING"] = "pending";
        State["COMMITTED"] = "committed";
        State["EXPIRED"] = "expired";
        State["FAILED"] = "failed";
        State["ERROR"] = "error";
        State["CANCELLED"] = "cancelled";
        State["REPEATED"] = "repeated";
    })(State = Orders.State || (Orders.State = {}));
    let Status;
    (function (Status) {
        Status["PENDING"] = "pending";
        Status["PAID"] = "paid";
        Status["COLLECTED"] = "collected";
        Status["DELIVERED"] = "delivered";
        Status["CANCELLED"] = "cancelled";
        Status["RETURNED"] = "returned";
    })(Status = Orders.Status || (Orders.Status = {}));
    let ItemKind;
    (function (ItemKind) {
        ItemKind["PRODUCT"] = "product";
        ItemKind["MEMBERSHIP"] = "membership";
        ItemKind["BOOKING"] = "booking";
        ItemKind["STOREDVALUE"] = "storedvalue";
    })(ItemKind = Orders.ItemKind || (Orders.ItemKind = {}));
    const { RECEIVED, PACKED, COLLECTED, ALLOCATED, DELIVERED } = Step;
    Orders.STEPS = {
        [Type.STORE]: [RECEIVED, PACKED, COLLECTED],
        [Type.DINEIN]: [RECEIVED, PACKED],
        [Type.PICKUP]: [RECEIVED, PACKED, COLLECTED],
        [Type.DELIVER]: [RECEIVED, ALLOCATED, COLLECTED, DELIVERED],
        [Type.VENDING_MACHINE]: [RECEIVED, COLLECTED]
    };
})(Orders || (exports.Orders = Orders = {}));
//# sourceMappingURL=orders.js.map