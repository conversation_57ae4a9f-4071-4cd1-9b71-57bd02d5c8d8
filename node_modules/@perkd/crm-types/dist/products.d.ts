import { Places } from './places';
import { Businesses } from './businesses';
export declare namespace Products {
    export enum Kind {
        PRODUCT = "product",
        MEMBERSHIP = "membership",
        GIFT_CARD = "giftcard",
        STOREDVALUE = "storedvalue",
        BOOKING = "booking",
        TICKET = "ticket",
        VOUCHER = "voucher",// cash voucher (sold), treat as payment method when discount.kind is fixed (enable part-pay)
        WIDGET = "widget",
        NFT = "nft"
    }
    export enum Channel {
        STORE = "store",
        WEBSITE = "website",
        PERKD = "perkd",
        MENU = "menu",
        SHOPIFY = "shopify",
        GRABFOOD = "grabfood",
        GRABMART = "grabmart",
        UBEREATS = "ubereats",
        FOODPANDA = "foodpanda"
    }
    export enum Unit {
        COUNT = "count",
        KILOGRAM = "kg",
        GRAM = "g",
        MILLILITRE = "ml",
        LITRE = "l",
        POUND = "lb",
        OUNCE = "oz",
        MINUTE = "minute",
        HOUR = "hour",
        DAY = "day"
    }
    export enum ResourceKind {
        EVENT = "event",
        VENUE = "venue",
        TABLE = "table",
        PERSON = "person"
    }
    export enum FulfillmentService {
        NONE = "none",
        STORE = "store",
        DIGITAL = "digital",
        SHOPIFY = "shopify",
        KITCHEN = "kitchen",
        VENDING = "vending"
    }
    export enum Availability {
        ACTIVE = "active",
        SOLDOUT = "soldout",
        HIDE = "hide",
        PREORDER = "preorder"
    }
    export enum Policy {
        DENY = "deny",
        CONTINUE = "continue"
    }
    export enum PriceName {
        BASE = "base",// standard
        MIN = "min",// minimum for variable pricing
        MAX = "max"
    }
    export enum TagType {
        CATEGORY = "category",
        KITCHEN = "kitchen"
    }
    type Inventory = {
        policy: Policy;
        quantity?: number;
        management?: string;
    };
    type Amount = {
        value: number;
        currency?: string;
    };
    type Price = {
        name: PriceName | string;
        price: Amount;
        salePrice?: Amount;
        increment?: number;
        paymentMethods?: string[];
        notPaymentMethods?: string[];
        countries?: string[];
        fee?: any;
    };
    type Variation = {
        title: string;
        value: any;
    };
    type External = {
        [provider: string]: {
            [key: string]: any;
        };
    };
    type GTIN = {
        value: string;
        code?: string;
    };
    type Weight = {
        unit: Unit;
        value: number;
        count?: number;
    };
    export type UnitMeasure = {
        unit: Unit;
        value: number;
    };
    export type OptionValue = {
        title: string;
        price?: number;
        image?: string;
        variantId?: string;
    };
    export type Option = {
        key: string;
        title: string;
        values: OptionValue[];
        value?: any;
        required?: boolean;
        unique?: boolean;
        min?: number;
        max?: number;
        type?: string;
        icon?: string;
        tip?: string;
        properties?: any;
        style?: any;
    };
    export type BundleValue = {
        variantId: string;
        price?: number;
        image?: string;
    };
    export type Bundle = {
        key: string;
        title?: string;
        values: BundleValue[];
        required?: boolean;
        unique?: boolean;
        min?: number;
        max?: number;
    };
    export type Product = {
        title: string;
        description: string;
        brand: string;
        external?: External;
        tags: {
            system?: string[];
            category?: string[];
            user?: string[];
            kitchen?: string[];
        };
        visible: boolean;
        createdAt: Date;
        modifiedAt?: Date;
    };
    export type Variant = {
        id?: string;
        kind?: Kind;
        title: string;
        sku?: string;
        gtin?: GTIN;
        weight?: Weight;
        channels: Channel[];
        inventory: Inventory;
        position?: number;
        prices: Price[];
        variations?: Variation[];
        options?: Option[];
        bundles?: Bundle[];
        attributes?: any;
        minOrderQuantity?: number;
        unitPriceMeasure?: UnitMeasure;
        unitCostMeasure?: UnitMeasure;
        fulfillmentService?: FulfillmentService;
        digital?: {
            masterId?: string;
            widgetKey?: string;
        };
        preparation?: {
            time?: number;
            placeId?: string;
        };
        storedValue?: {
            balance?: number;
            currency?: {
                code: string;
                precision: number;
            };
        };
        taxable: boolean;
        taxCode?: string;
        visible: boolean;
        external?: External;
        createdAt: Date;
        modifiedAt?: Date;
        imageIds?: string[];
    };
    export type Image = {
        id?: string;
        position?: number;
        external?: External;
        original: {
            url: string;
        };
        ownerId: string;
        createdAt: Date;
        modifiedAt?: Date;
    };
    export type Resource = {
        id?: string;
        kind: ResourceKind;
        name: string;
        description: string;
        position?: Places.Position;
        timeZone?: string;
        hours: Businesses.Hours;
        startTime?: Date;
        endTime?: Date;
        capacity: number;
        shared: boolean;
        interval: number;
        leadTime: number;
        calendarId?: string;
        productId?: string;
        placeId?: string;
    };
    export {};
}
