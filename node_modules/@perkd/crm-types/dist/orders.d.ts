import { Fulfillments } from './fulfillments';
import { Contacts } from './contacts';
import { Discounts } from './offers';
import { Products } from './products';
import { Billings } from './billings';
type Fulfillment = Fulfillments.Fulfillment;
type Discount = Discounts.Discount;
type UnitMeasure = Products.UnitMeasure;
export declare namespace Orders {
    enum Step {
        NEW = "new",
        RECEIVED = "received",
        PACKED = "packed",
        ALLOCATED = "allocated",
        COLLECTED = "collected",
        DELIVERED = "delivered",
        ACCEPTED = "accepted",
        DECLINED = "declined",
        FULFILLED = "fulfilled",
        CANCELLED = "cancelled",
        RETURNED = "returned"
    }
    enum State {
        PENDING = "pending",
        COMMITTED = "committed",
        EXPIRED = "expired",
        FAILED = "failed",
        ERROR = "error",
        CANCELLED = "cancelled",
        REPEATED = "repeated"
    }
    enum Status {
        PENDING = "pending",
        PAID = "paid",
        COLLECTED = "collected",
        DELIVERED = "delivered",
        CANCELLED = "cancelled",
        RETURNED = "returned"
    }
    enum ItemKind {
        PRODUCT = "product",
        MEMBERSHIP = "membership",
        BOOKING = "booking",
        STOREDVALUE = "storedvalue"
    }
    const STEPS: {
        store: Step[];
        dinein: Step[];
        pickup: Step[];
        deliver: Step[];
        vending: Step[];
    };
    type Bundled = string[];
    type OptionValue = Omit<Products.OptionValue, 'image'> & {
        quantity: number;
    };
    type Option = Omit<Products.Option, 'required' | 'unique' | 'min' | 'max' | 'type' | 'icon' | 'tip' | 'properties' | 'style'> & {
        values: OptionValue[];
        value: any;
    };
    type Custom = Partial<{
        id: string;
        bookingId: string;
        productIds: string[];
        from: string;
        duration: number;
        start: string;
        end: string;
        tenure: string;
        membership: {
            id: string;
            programId: string;
            memberId: string;
            personId: string;
        };
        qualification: {
            programId: string;
            memberId: string;
            personId: string;
            tenure: string;
            qualifier: string;
            qualified: any;
        };
        sponsor: string;
    }>;
    type Item = {
        id?: string;
        kind?: ItemKind;
        product: {
            id: string;
            title?: string;
            brand?: string;
            attributes?: any;
            tags?: any;
            behaviors?: any;
            external?: {
                [provider: string]: {
                    [key: string]: any;
                };
            };
            businessId?: string;
        };
        variant: {
            id: string;
            sku?: string;
            title?: string;
            digital?: {
                masterId?: string;
                widgetKey?: string;
            };
            fulfillmentService?: Fulfillments.Service;
            inventory?: {
                management?: string;
                policy?: string;
                quantity?: number;
                lowQuantityWarningThreshold?: number;
            };
            prices?: [
                {
                    name: string;
                    price?: {
                        value?: number;
                    };
                    salePrice?: {
                        value?: number;
                    };
                }
            ];
            external?: {
                [provider: string]: {
                    [key: string]: any;
                };
            };
        };
        quantity: number;
        units?: number;
        discountAmount: number;
        tax?: number;
        price: number;
        unitPrice: number;
        unitPriceMeasure?: UnitMeasure;
        unitCostMeasure?: UnitMeasure;
        variantOptions: Option[];
        bundled?: Bundled;
        bundleId?: string;
        grossMargin?: number;
        discount?: Discount;
        fulfillmentType?: Fulfillments.Type;
        shippingMethodId?: string;
        images?: string[];
        custom?: Custom;
        properties?: any[];
        admit?: number;
    };
    type Tax = Billings.Tax;
    type Billing = Billings.Billing;
    type Receipt = {
        number?: string;
        taxId?: string;
        send?: boolean;
        invoice?: Invoice;
    };
    type Flow = {
        at: number;
        steps: Step[];
    };
    type Customer = {
        id?: string;
        fullName: string;
        givenName?: string;
        familyName?: string;
        gender?: string;
        email?: string;
        phone?: Contacts.Phone;
        address?: Contacts.Address;
        profileImageUrl?: string;
    };
    type Order = {
        id?: string;
        receipt?: Receipt;
        quantity: number;
        currency: string;
        discountAmount: number;
        shippingPrice?: number;
        subtotalPrice: number;
        amount: number;
        taxAmount: number;
        taxIncluded: boolean;
        taxes?: Tax[];
        flow?: Flow;
        note?: string;
        external?: any;
        tags: {
            system: string[];
        };
        program?: {
            id: string;
            tierLevel: number;
            cardNumber: string;
        };
        personId?: string;
        storeId?: string;
        itemList: Item[];
        discountList: Discount[];
        billingList: Billing[];
        fulfillmentList: Fulfillment[];
        acquired?: any;
        through?: any;
        expiresAt?: Date;
        when?: any;
        sourceType?: string;
        createdAt?: Date;
    };
    type Invoice = {
        number: string;
        taxId?: string;
        amount?: number;
        currency?: string;
        tax?: number;
        taxRate?: number;
    };
}
export {};
