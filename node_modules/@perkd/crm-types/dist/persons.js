"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Socials = exports.Persons = void 0;
const contacts_1 = require("./contacts");
var Persons;
(function (Persons) {
    let Gender;
    (function (Gender) {
        Gender["MALE"] = "m";
        Gender["FEMALE"] = "f";
    })(Gender = Persons.Gender || (Persons.Gender = {}));
    let NameOrder;
    (function (NameOrder) {
        NameOrder["FAMILY_GIVEN"] = "familygiven";
        NameOrder["GIVEN_FAMILY"] = "givenfamily";
    })(NameOrder = Persons.NameOrder || (Persons.NameOrder = {}));
    let Identities;
    (function (Identities) {
        Identities["PERKD"] = "perkd";
        Identities["USER"] = "user";
        Identities["CUSTOMER"] = "customer";
        Identities["SMARTCOLLECTION"] = "smartcollection";
        Identities["NATIONAL"] = "national";
        Identities["REGISTRATION"] = "registration";
        Identities["PASSPORT"] = "passport";
        Identities["DRIVER"] = "driver";
        Identities["PET"] = "pet";
    })(Identities = Persons.Identities || (Persons.Identities = {}));
    let PermissionChannel;
    (function (PermissionChannel) {
        PermissionChannel["MOBILE"] = "mobile";
        PermissionChannel["EMAIL"] = "email";
        PermissionChannel["POSTAL"] = "postal";
        PermissionChannel["SERVICE_TERMS"] = "serviceTerms";
        PermissionChannel["PRIVACY_POLICY"] = "privacyPolicy";
    })(PermissionChannel = Persons.PermissionChannel || (Persons.PermissionChannel = {}));
    let PermissionStatus;
    (function (PermissionStatus) {
        PermissionStatus[PermissionStatus["DO_NOT_DISTURB"] = -2] = "DO_NOT_DISTURB";
        PermissionStatus[PermissionStatus["OPTOUT"] = -1] = "OPTOUT";
        PermissionStatus[PermissionStatus["UNKNOWN"] = 0] = "UNKNOWN";
        PermissionStatus[PermissionStatus["OPTIN"] = 1] = "OPTIN";
    })(PermissionStatus = Persons.PermissionStatus || (Persons.PermissionStatus = {}));
    let Residency;
    (function (Residency) {
        Residency["CITIZEN"] = "citizen";
        Residency["RESIDENT"] = "resident";
        Residency["EMPLOYMENT"] = "employment";
    })(Residency = Persons.Residency || (Persons.Residency = {}));
    let BloodType;
    (function (BloodType) {
        BloodType["A_POSITIVE"] = "A+";
        BloodType["A_NEGATIVE"] = "A-";
        BloodType["B_POSITIVE"] = "B+";
        BloodType["B_NEGATIVE"] = "B-";
        BloodType["O_POSITIVE"] = "O+";
        BloodType["O_NEGATIVE"] = "O-";
        BloodType["AB_POSITIVE"] = "AB+";
        BloodType["AB_NEGATIVE"] = "AB-";
    })(BloodType = Persons.BloodType || (Persons.BloodType = {}));
    let Species;
    (function (Species) {
        Species["DOG"] = "dog";
        Species["CAT"] = "cat";
        Species["BIRD"] = "bird";
        Species["RABBIT"] = "rabbit";
        Species["RODENT"] = "rodent";
    })(Species = Persons.Species || (Persons.Species = {}));
    Persons.PROFILE = {
        FAMILY_GIVEN: NameOrder.FAMILY_GIVEN,
        GIVEN_FAMILY: NameOrder.GIVEN_FAMILY,
        NAME_ORDER: [NameOrder.FAMILY_GIVEN, NameOrder.GIVEN_FAMILY],
        MOBILE: contacts_1.Contacts.Type.MOBILE,
        BIRTH_DATE: contacts_1.Contacts.Dates.BIRTH,
    };
})(Persons || (exports.Persons = Persons = {}));
var Socials;
(function (Socials) {
    let Relationship;
    (function (Relationship) {
        Relationship["FRIEND"] = "friend";
        Relationship["SPOUSE"] = "spouse";
        Relationship["PARENT"] = "parent";
        Relationship["CHILD"] = "child";
        Relationship["SIBLING"] = "sibling";
        Relationship["COLLEAGUE"] = "colleague";
        Relationship["CLASSMATE"] = "classmate";
        Relationship["PET"] = "pet";
    })(Relationship = Socials.Relationship || (Socials.Relationship = {}));
    let InverseRelationship;
    (function (InverseRelationship) {
        InverseRelationship["parent"] = "child";
        InverseRelationship["child"] = "parent";
        InverseRelationship["pet"] = "owner";
    })(InverseRelationship = Socials.InverseRelationship || (Socials.InverseRelationship = {}));
})(Socials || (exports.Socials = Socials = {}));
//# sourceMappingURL=persons.js.map