"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Memberships = exports.Programs = void 0;
var Programs;
(function (Programs) {
    let Type;
    (function (Type) {
        Type["FREE"] = "free";
        Type["PAID"] = "paid";
        Type["EARNED"] = "earned";
        Type["MIXED"] = "mixed";
        Type["INVITED"] = "invited";
        Type["GIFT_CARD"] = "giftcard";
        Type["STAFF"] = "staff";
    })(Type = Programs.Type || (Programs.Type = {}));
    let State;
    (function (State) {
        State["INCOMPLETE"] = "incomplete";
        State["READY"] = "ready";
        State["ACTIVE"] = "active";
        State["STOPPED"] = "stopped";
        State["ARCHIVED"] = "archived";
    })(State = Programs.State || (Programs.State = {}));
    Programs.PRESET = {
        SHOPIFY_STAFF_APP: 'shopifystaffapp'
    };
})(Programs || (exports.Programs = Programs = {}));
var Memberships;
(function (Memberships) {
    let State;
    (function (State) {
        State["PENDING"] = "pending";
        State["ACTIVE"] = "active";
        State["JOINED"] = "joined";
        State["RENEWED"] = "renewed";
        State["EXPIRED"] = "expired";
        State["UPGRADED"] = "upgraded";
        State["DOWNGRADED"] = "downgraded";
        State["TIERCHANGED"] = "tierchanged";
        State["SUSPENDED"] = "suspended";
        State["CANCELLED"] = "cancelled";
        State["TERMINATED"] = "terminated";
        State["TRANSFERRED"] = "transferred";
        State["BLACKLISTED"] = "blacklisted";
        State["EXTENDED"] = "extended";
    })(State = Memberships.State || (Memberships.State = {}));
    let QualifyMethod;
    (function (QualifyMethod) {
        QualifyMethod["MANUAL"] = "manual";
        QualifyMethod["COMPLIMENTARY"] = "complimentary";
    })(QualifyMethod = Memberships.QualifyMethod || (Memberships.QualifyMethod = {}));
    let Qualifier;
    (function (Qualifier) {
        Qualifier["JOIN"] = "join";
        Qualifier["RENEW"] = "renew";
        Qualifier["UPGRADE"] = "upgrade";
        Qualifier["DOWNGRADE"] = "downgrade";
        Qualifier["TIERCHANGE"] = "tierchange";
        Qualifier["EXTEND"] = "extend";
        Qualifier["TERMINATE"] = "terminate";
        Qualifier["TRANSFER"] = "transfer";
        Qualifier["SHARE"] = "share";
    })(Qualifier = Memberships.Qualifier || (Memberships.Qualifier = {}));
    let QualifiedStates;
    (function (QualifiedStates) {
        QualifiedStates["join"] = "joined";
        QualifiedStates["renew"] = "renewed";
        QualifiedStates["upgrade"] = "upgraded";
        QualifiedStates["downgrade"] = "downgraded";
        QualifiedStates["tierchange"] = "tierchanged";
        QualifiedStates["extend"] = "extended";
    })(QualifiedStates = Memberships.QualifiedStates || (Memberships.QualifiedStates = {}));
})(Memberships || (exports.Memberships = Memberships = {}));
//# sourceMappingURL=memberships.js.map