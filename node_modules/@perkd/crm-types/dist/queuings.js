"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Queuings = void 0;
var Queuings;
(function (Queuings) {
    let Status;
    (function (Status) {
        Status["WAITING"] = "waiting";
        Status["ASSIGNED"] = "assigned";
        Status["NOTIFIED"] = "notified";
        Status["CONFIRMED"] = "confirmed";
        Status["COMPLETED"] = "completed";
        Status["SKIPPED"] = "skipped";
        Status["LEFT"] = "left";
    })(Status = Queuings.Status || (Queuings.Status = {}));
    let Source;
    (function (Source) {
        Source["WALKIN"] = "walkin";
        Source["BOOKING"] = "booking";
    })(Source = Queuings.Source || (Queuings.Source = {}));
    let Tier;
    (function (Tier) {
        Tier["REGULAR"] = "regular";
        Tier["VIP"] = "vip";
        Tier["ASAP"] = "asap";
    })(Tier = Queuings.Tier || (Queuings.Tier = {}));
})(Queuings || (exports.Queuings = Queuings = {}));
//# sourceMappingURL=queuings.js.map