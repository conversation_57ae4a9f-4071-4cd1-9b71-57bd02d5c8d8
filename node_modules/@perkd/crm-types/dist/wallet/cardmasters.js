"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardMasters = void 0;
var CardMasters;
(function (CardMasters) {
    let State;
    (function (State) {
        State["INCOMPLETE"] = "incomplete";
        State["READY"] = "ready";
        State["ACTIVE"] = "active";
        State["STOPPED"] = "stopped";
        State["ARCHIVED"] = "archived";
    })(State = CardMasters.State || (CardMasters.State = {}));
    let Step;
    (function (Step) {
        Step["REQUEST"] = "request";
        Step["SHARE"] = "share";
        Step["SHARE_OR_USE"] = "shareoruse";
        Step["APPROVAL"] = "approval";
        Step["ACTIVATE"] = "activate";
        Step["KYC"] = "kyc";
        Step["PAYMENT"] = "payment";
        Step["REGISTER"] = "register";
        Step["DONE"] = "done";
        Step["UPDATE"] = "update";
        Step["EXPENDED"] = "expended";
    })(Step = CardMasters.Step || (CardMasters.Step = {}));
    let Categories;
    (function (Categories) {
        Categories["ASSOCIATIONS"] = "Associations & Non-profits";
        Categories["CHILDREN"] = "Children";
        Categories["FASHION"] = "Fashion & Accessories";
        Categories["FOOD"] = "Food & Beverages";
        Categories["HEALTH"] = "Health & Beauty";
        Categories["LIFESTYLE"] = "Lifestyle & Services";
        Categories["MALLS"] = "Malls & Department Stores";
        Categories["TRAVEL"] = "Travel";
    })(Categories = CardMasters.Categories || (CardMasters.Categories = {}));
    let StoredValueKind;
    (function (StoredValueKind) {
        StoredValueKind["STORE_CREDITS"] = "storecredits";
        StoredValueKind["GIFT_CARD"] = "giftcard";
    })(StoredValueKind = CardMasters.StoredValueKind || (CardMasters.StoredValueKind = {}));
})(CardMasters || (exports.CardMasters = CardMasters = {}));
//# sourceMappingURL=cardmasters.js.map