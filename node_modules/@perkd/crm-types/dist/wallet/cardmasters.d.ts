export declare namespace CardMasters {
    enum State {
        INCOMPLETE = "incomplete",
        READY = "ready",
        ACTIVE = "active",
        STOPPED = "stopped",
        ARCHIVED = "archived"
    }
    enum Step {
        REQUEST = "request",
        SHARE = "share",
        SHARE_OR_USE = "shareoruse",
        APPROVAL = "approval",
        ACTIVATE = "activate",
        KYC = "kyc",
        PAYMENT = "payment",
        REGISTER = "register",
        DONE = "done",
        UPDATE = "update",// update app required
        EXPENDED = "expended"
    }
    enum Categories {
        ASSOCIATIONS = "Associations & Non-profits",
        CHILDREN = "Children",
        FASHION = "Fashion & Accessories",
        FOOD = "Food & Beverages",
        HEALTH = "Health & Beauty",
        LIFESTYLE = "Lifestyle & Services",
        MALLS = "Malls & Department Stores",
        TRAVEL = "Travel"
    }
    enum StoredValueKind {
        STORE_CREDITS = "storecredits",
        GIFT_CARD = "giftcard"
    }
    type Flow = {
        at: number;
        steps: Step[];
    };
}
