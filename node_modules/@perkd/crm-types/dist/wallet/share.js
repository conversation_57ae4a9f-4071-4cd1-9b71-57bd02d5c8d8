"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Share = void 0;
var Share;
(function (Share) {
    let State;
    (function (State) {
        State["TRANSFERRING"] = "transferring";
        State["TRANSFERRED"] = "transferred";
    })(State = Share.State || (Share.State = {}));
    let Mode;
    (function (Mode) {
        Mode["INVITE"] = "invite";
        Mode["CLONE"] = "clone";
        Mode["SEND"] = "send";
        Mode["TRANSFER"] = "transfer";
        Mode["SUBOBJECT"] = "subobject";
    })(Mode = Share.Mode || (Share.Mode = {}));
})(Share || (exports.Share = Share = {}));
//# sourceMappingURL=share.js.map