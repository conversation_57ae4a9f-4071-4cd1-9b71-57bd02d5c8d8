import { CardMasters } from './cardmasters';
import { Billings } from '../billings';
import { Places } from '../places';
import { Fulfillments } from '../fulfillments';
import { Products } from '../products';
type Currency = Billings.Currency;
type Tax = Billings.Tax;
type Source = {
    name: 'bag' | 'message' | string;
};
type FulfillmentType = Fulfillments.Type;
type Spot = Places.Spot;
type Allocation = {
    amount: number;
    index: number;
};
type Discount = {
    amount: number;
    allocations: Allocation[];
};
type Recipient = {
    fullName: string;
    phone: string;
    email?: string;
};
type Scheduled = {
    minTime: Date;
    maxTime: Date;
};
type Status = {
    soldout: boolean;
    remain: number;
};
export declare namespace Widgets {
    enum Key {
        MESSAGE = "message",
        OFFER = "offer",
        REWARD = "reward",
        BAG = "bag",
        PLACE = "place",
        TICKET = "ticket",
        RECEIPT = "receipt",
        PRODUCT = "product",
        INVITE = "invite",
        TRANSFER = "transfer",
        CLONE = "clone",
        SERVICE = "service",
        CALL = "call",
        EMAIL = "email",
        PAYMENT = "payment",
        POINTS = "points",
        SHOP = "shop",
        MEMBERSHIP_STATUS = "memberstatus",
        ORDER_URI = "orderuri",
        TICKET_URI = "ticketuri",
        WEB_URI = "weburi",
        ACCOUNT_URI = "accounturi",
        SHOP_URI = "shopuri",
        BOOK_URI = "bookuri",
        BOOK_HOTEL_URI = "bookhoteluri",
        BOOK_FLIGHT_URI = "bookflighturi",
        FACEBOOK = "facebook",
        INSTAGRAM = "insta",
        LINE = "line",
        WIFI = "wifi",
        WHATSAPP = "whatsapp",
        SPONSORED = "sponsored",
        TOPUP = "topup",
        STAFF_CHECKIN = "staff-checkin"
    }
    enum Kind {
        APPLET = "applet",
        ACTION = "action",
        BAG = "bag",
        OFFER = "offer",
        REWARD = "reward",
        MESSAGE = "message",// includes receipts
        TICKET = "ticket",
        PRODUCT = "product",
        PLACE = "place",
        CHECKIN = "checkin",// toggles checkout
        SHARE = "share",
        ACCOUNT = "account",
        WEB = "web",// website, shop
        PAY = "pay",
        SHOPIFY = "shopify",
        WIFI = "wifi",
        PAYMENT = "payment",
        NOTIFY = "notify"
    }
    type Config = {
        key: string;
        param: any;
        startTime?: Date;
        endTime?: Date;
    };
    export import Step = CardMasters.Step;
}
export declare namespace Bags {
    enum Type {
        RETAIL = "retail",
        RESTAURANT = "restaurant",
        FAST_FOOD = "fastfood",
        FAST_DRINK = "fastdrink"
    }
    enum ItemKind {
        PRODUCT = "product",
        BOOKING = "booking",
        MEMBERSHIP = "membership",
        STORED_VALUE = "storedvalue"
    }
    type BundleValue = Products.BundleValue & {
        sku: string;
        kind: ItemKind;
        options?: Option[];
        quantity: number;
        selected: boolean;
    };
    type Bundle = Products.Bundle & {
        values: BundleValue[];
    };
    type OptionValue = Products.OptionValue & {
        selected: boolean;
        quantity: number;
    };
    type Option = Products.Option & {
        values: OptionValue[];
    };
    type Property = {
        name: string;
        value: string;
    };
    type Item = {
        id: string;
        kind: ItemKind;
        variantId: string;
        sku: string;
        title: string;
        unitPrice?: number;
        units: number;
        quantity: number;
        price: number;
        discount: Discount;
        fulfillments: string[];
        properties?: Property[];
        bundles?: string[];
        options?: Option[];
        taxes: Tax[];
        images?: string[];
        tags?: string[];
        status: Status;
    };
    type Fulfillment = {
        type: FulfillmentType;
        unitPrice?: number;
        units: number;
        quantity: number;
        price: number;
        discount: Discount;
        taxes: Tax[];
        recipient?: Recipient;
        spot?: Spot;
        destination?: Spot;
        origin?: Spot;
        scheduled?: Scheduled;
        images?: string[];
        tags?: string[];
    };
    type Bag = {
        id: string;
        name: string;
        type: Type;
        totalDiscounts: number;
        subtotalPrice: number;
        totalTax: number;
        totalPrice: number;
        currency: Currency;
        taxIncluded: boolean;
        taxes: Tax[];
        items: Item[];
        injectedItems?: Item[];
        discountsApplied: any[];
        fulfillment: Fulfillment;
        source: Source;
        startTime?: Date;
        endTime?: Date;
        modifiedAt?: Date;
    };
    type Favorite = {
        sku: string;
        title: string;
        description: string;
        images: string[];
        gtin?: string;
        variantId?: string;
        tags?: string[];
    };
}
export {};
