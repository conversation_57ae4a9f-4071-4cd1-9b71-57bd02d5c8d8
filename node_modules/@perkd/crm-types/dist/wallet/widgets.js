"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bags = exports.Widgets = void 0;
const cardmasters_1 = require("./cardmasters");
var Widgets;
(function (Widgets) {
    let Key;
    (function (Key) {
        Key["MESSAGE"] = "message";
        Key["OFFER"] = "offer";
        Key["REWARD"] = "reward";
        Key["BAG"] = "bag";
        Key["PLACE"] = "place";
        Key["TICKET"] = "ticket";
        Key["RECEIPT"] = "receipt";
        Key["PRODUCT"] = "product";
        Key["INVITE"] = "invite";
        Key["TRANSFER"] = "transfer";
        Key["CLONE"] = "clone";
        Key["SERVICE"] = "service";
        Key["CALL"] = "call";
        Key["EMAIL"] = "email";
        Key["PAYMENT"] = "payment";
        Key["POINTS"] = "points";
        Key["SHOP"] = "shop";
        Key["MEMBERSHIP_STATUS"] = "memberstatus";
        Key["ORDER_URI"] = "orderuri";
        Key["TICKET_URI"] = "ticketuri";
        Key["WEB_URI"] = "weburi";
        Key["ACCOUNT_URI"] = "accounturi";
        Key["SHOP_URI"] = "shopuri";
        Key["BOOK_URI"] = "bookuri";
        Key["BOOK_HOTEL_URI"] = "bookhoteluri";
        Key["BOOK_FLIGHT_URI"] = "bookflighturi";
        Key["FACEBOOK"] = "facebook";
        Key["INSTAGRAM"] = "insta";
        Key["LINE"] = "line";
        Key["WIFI"] = "wifi";
        Key["WHATSAPP"] = "whatsapp";
        Key["SPONSORED"] = "sponsored";
        Key["TOPUP"] = "topup";
        Key["STAFF_CHECKIN"] = "staff-checkin";
    })(Key = Widgets.Key || (Widgets.Key = {}));
    let Kind;
    (function (Kind) {
        Kind["APPLET"] = "applet";
        Kind["ACTION"] = "action";
        Kind["BAG"] = "bag";
        Kind["OFFER"] = "offer";
        Kind["REWARD"] = "reward";
        Kind["MESSAGE"] = "message";
        Kind["TICKET"] = "ticket";
        Kind["PRODUCT"] = "product";
        Kind["PLACE"] = "place";
        Kind["CHECKIN"] = "checkin";
        Kind["SHARE"] = "share";
        Kind["ACCOUNT"] = "account";
        Kind["WEB"] = "web";
        Kind["PAY"] = "pay";
        Kind["SHOPIFY"] = "shopify";
        Kind["WIFI"] = "wifi";
        // used by PerkdID
        Kind["PAYMENT"] = "payment";
        Kind["NOTIFY"] = "notify";
    })(Kind = Widgets.Kind || (Widgets.Kind = {}));
    Widgets.Step = cardmasters_1.CardMasters.Step;
})(Widgets || (exports.Widgets = Widgets = {}));
var Bags;
(function (Bags) {
    let Type;
    (function (Type) {
        Type["RETAIL"] = "retail";
        Type["RESTAURANT"] = "restaurant";
        Type["FAST_FOOD"] = "fastfood";
        Type["FAST_DRINK"] = "fastdrink";
    })(Type = Bags.Type || (Bags.Type = {}));
    let ItemKind;
    (function (ItemKind) {
        ItemKind["PRODUCT"] = "product";
        ItemKind["BOOKING"] = "booking";
        ItemKind["MEMBERSHIP"] = "membership";
        ItemKind["STORED_VALUE"] = "storedvalue";
    })(ItemKind = Bags.ItemKind || (Bags.ItemKind = {}));
})(Bags || (exports.Bags = Bags = {}));
//# sourceMappingURL=widgets.js.map