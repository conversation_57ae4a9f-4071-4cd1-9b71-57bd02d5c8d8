export declare namespace Actions {
    enum Kind {
        LOCAL = "local",
        REMOTE = "remote",
        TEMPLATE = "template",
        MULTI = "multi",
        INTENT = "intent"
    }
    enum Object {
        ACTION = "action",
        ENGAGE = "engage",
        CARD = "card"
    }
    enum ObjectAction {
        ACTION = "action",
        REMOTE = "remote",
        ADD = "add"
    }
    enum NameSpace {
        ACTION = "action",// also defined in '@perkd/actions'
        ONCE_TOKEN = "actiononce"
    }
    const MAX_LEN = 2048;
    const TTL: number;
    type Callback = {
        method: string;
        url: string;
        headers: any;
        name?: string;
        rate?: {
            limit: number;
            interval: number;
            tokensPerInterval: number;
        };
    };
    type Action = {
        id: string;
        minAppVersion?: string;
        key?: string;
        kind?: Kind;
        description?: string;
        startTime?: Date | string;
        endTime?: Date | string;
        object: string;
        action: string;
        actions: Record<string, any>;
        data?: Record<string, any>;
        triggers?: Record<string, unknown>;
        callback?: Callback;
        enabled?: boolean;
        once?: boolean;
        published?: boolean;
        transient?: boolean;
        ttl?: number;
        tenant?: {
            code?: string;
        };
        personId?: string;
        deviceId?: string;
        triggeredAt?: Date | string;
        purgeTime?: Date | string;
        createdAt?: Date | string;
        modifiedAt?: Date | string;
        deletedAt?: Date | string;
    };
}
