"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Actions = void 0;
var Actions;
(function (Actions) {
    let Kind;
    (function (Kind) {
        Kind["LOCAL"] = "local";
        Kind["REMOTE"] = "remote";
        Kind["TEMPLATE"] = "template";
        Kind["MULTI"] = "multi";
        Kind["INTENT"] = "intent";
    })(Kind = Actions.Kind || (Actions.Kind = {}));
    let Object;
    (function (Object) {
        Object["ACTION"] = "action";
        Object["ENGAGE"] = "engage";
        Object["CARD"] = "card";
    })(Object = Actions.Object || (Actions.Object = {}));
    let ObjectAction;
    (function (ObjectAction) {
        ObjectAction["ACTION"] = "action";
        ObjectAction["REMOTE"] = "remote";
        ObjectAction["ADD"] = "add";
    })(ObjectAction = Actions.ObjectAction || (Actions.ObjectAction = {}));
    let NameSpace;
    (function (NameSpace) {
        NameSpace["ACTION"] = "action";
        NameSpace["ONCE_TOKEN"] = "actiononce";
    })(NameSpace = Actions.NameSpace || (Actions.NameSpace = {}));
    Actions.MAX_LEN = 2048; // of url
    Actions.TTL = 1 * 60 * 60; // 1hr
})(Actions || (exports.Actions = Actions = {}));
//# sourceMappingURL=actions.js.map