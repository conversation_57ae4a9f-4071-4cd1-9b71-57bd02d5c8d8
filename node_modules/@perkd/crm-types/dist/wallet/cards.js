"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cards = void 0;
const cardmasters_1 = require("./cardmasters");
var Cards;
(function (Cards) {
    Cards.PERKD_ID = '000000000000000000000000';
    Cards.CUSTOM_CARD = '000000000000000000000001';
    let State;
    (function (State) {
        State["PENDING"] = "pending";
        State["ACTIVE"] = "active";
        State["TRANSFERRING"] = "transferring";
        State["TRANSFERRED"] = "transferred";
        State["EXPIRED"] = "expired";
        State["REVOKED"] = "revoked";
        State["CANCELLED"] = "cancelled";
        State["TERMINATED"] = "terminated";
        State["EXPENDED"] = "expended";
    })(State = Cards.State || (Cards.State = {}));
    let Sort;
    (function (Sort) {
        Sort["BRAND"] = "brand";
        Sort["LOCATION"] = "location";
        Sort["CUSTOM"] = "custom";
    })(Sort = Cards.Sort || (Cards.Sort = {}));
    let View;
    (function (View) {
        View["BIG"] = "big";
        View["SMALL"] = "small";
    })(View = Cards.View || (Cards.View = {}));
    let FlowName;
    (function (FlowName) {
        FlowName["SIGNUP"] = "signup";
        FlowName["PAY"] = "pay";
        FlowName["SHARED"] = "shared";
    })(FlowName = Cards.FlowName || (Cards.FlowName = {}));
    Cards.Step = cardmasters_1.CardMasters.Step;
    const { REQUEST, REGISTER, PAYMENT, DONE } = Cards.Step;
    Cards.FLOW = {
        DEFAULT: { at: 0, steps: [REQUEST, REGISTER, DONE] },
        PAY2JOIN: { at: 0, steps: [PAYMENT, REGISTER, DONE] },
        SHARE: { at: 0, steps: [REGISTER, DONE] },
    };
})(Cards || (exports.Cards = Cards = {}));
//# sourceMappingURL=cards.js.map