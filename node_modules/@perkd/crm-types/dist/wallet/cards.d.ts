import { CardMasters } from './cardmasters';
import { Widgets } from './widgets';
export declare namespace Cards {
    const PERKD_ID = "000000000000000000000000";
    const CUSTOM_CARD = "000000000000000000000001";
    enum State {
        PENDING = "pending",
        ACTIVE = "active",
        TRANSFERRING = "transferring",
        TRANSFERRED = "transferred",
        EXPIRED = "expired",
        REVOKED = "revoked",
        CANCELLED = "cancelled",
        TERMINATED = "terminated",
        EXPENDED = "expended"
    }
    enum Sort {
        BRAND = "brand",
        LOCATION = "location",
        CUSTOM = "custom"
    }
    enum View {
        BIG = "big",
        SMALL = "small"
    }
    enum FlowName {
        SIGNUP = "signup",
        PAY = "pay",
        SHARED = "shared"
    }
    export import Step = CardMasters.Step;
    type Flow = {
        at: number;
        steps: Step[];
    };
    const FLOW: {
        DEFAULT: {
            at: number;
            steps: Step[];
        };
        PAY2JOIN: {
            at: number;
            steps: Step[];
        };
        SHARE: {
            at: number;
            steps: Step[];
        };
    };
    type IssueOptions = {
        newCardId?: string;
        at?: Date;
        flow?: Flow;
        registeredAt?: Date;
        paid?: boolean;
        formData?: any;
        credentials?: any;
        noNotify?: boolean;
        notification?: any;
        through?: any;
        widgets?: Widgets.Config[];
        userId?: string;
    };
    type UpdateOptions = {
        qualified?: string;
        startTime?: Date;
        endTime?: Date;
        paid?: Date;
        widgets?: any[];
        expended?: boolean;
        noNotify?: boolean;
        notification?: any;
    };
    type TerminateOptions = {
        reason?: string;
        toDelete?: boolean;
        noNotify?: boolean;
        notification?: any;
    };
}
