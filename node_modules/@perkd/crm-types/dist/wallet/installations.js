"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Installations = void 0;
var Installations;
(function (Installations) {
    let State;
    (function (State) {
        State["ONLINE"] = "online";
        State["OFFLINE"] = "offline";
        State["SUSPEND"] = "suspend";
        State["UNINSTALLED"] = "uninstalled";
    })(State = Installations.State || (Installations.State = {}));
    let Feature;
    (function (Feature) {
        Feature["CAMERA"] = "camera";
        Feature["PHOTOS"] = "photos";
        Feature["PAYMENT"] = "payment";
        Feature["CONTACTS"] = "contacts";
        Feature["CALENDAR"] = "calendar";
        Feature["LOCATION"] = "location";
        Feature["BIOMETRICS"] = "biometrics";
        Feature["NOTIFICATIONS"] = "notifications";
        Feature["BACKGROUND_FETCH"] = "backgroundfetch";
        Feature["CELLULAR_DATA"] = "cellulardata";
        Feature["BLUETOOTH"] = "bluetooth";
    })(Feature = Installations.Feature || (Installations.Feature = {}));
    let Capability;
    (function (Capability) {
        Capability["PAYMENT"] = "payment";
        Capability["BIOMETRICS"] = "biometrics";
    })(Capability = Installations.Capability || (Installations.Capability = {}));
    let Biometric;
    (function (Biometric) {
        Biometric["FACEID"] = "faceid";
        Biometric["TOUCHID"] = "touchid";
        Biometric["IRIS"] = "iris";
    })(Biometric = Installations.Biometric || (Installations.Biometric = {}));
})(Installations || (exports.Installations = Installations = {}));
//# sourceMappingURL=installations.js.map