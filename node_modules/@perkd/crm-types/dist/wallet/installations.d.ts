export declare namespace Installations {
    enum State {
        ONLINE = "online",
        OFFLINE = "offline",
        SUSPEND = "suspend",
        UNINSTALLED = "uninstalled"
    }
    enum Feature {
        CAMERA = "camera",
        PHOTOS = "photos",
        PAYMENT = "payment",
        CONTACTS = "contacts",
        CALENDAR = "calendar",
        LOCATION = "location",
        BIOMETRICS = "biometrics",
        NOTIFICATIONS = "notifications",
        BACKGROUND_FETCH = "backgroundfetch",
        CELLULAR_DATA = "cellulardata",
        BLUETOOTH = "bluetooth"
    }
    enum Capability {
        PAYMENT = "payment",
        BIOMETRICS = "biometrics"
    }
    enum Biometric {
        FACEID = "faceid",
        TOUCHID = "touchid",
        IRIS = "iris"
    }
    type App = {
        id: string;
        version: string;
        env: string;
    };
    type Carrier = {
        name: string;
        country: string;
        mobileCountryCode: number;
        changed: boolean;
    };
    type Locale = {
        country: string;
        languages: string[];
    };
    type Capabilities = {
        name: string;
        support: any;
    };
    type Permission = {
        feature: string;
        status: string;
        options?: string[];
        grantedAt?: Date;
        revokedAt?: Date;
    };
    type GeoCoordinates = {
        lat: number;
        lng: number;
    };
    type PaymentMethod = {
        id: string;
        key: string;
        type: string;
        icon: string;
        supported: boolean;
    };
    type Payments = {
        [type: string]: {
            methods?: PaymentMethod[];
        };
    };
}
