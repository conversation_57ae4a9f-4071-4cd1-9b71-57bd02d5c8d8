"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Campaigns = void 0;
var Campaigns;
(function (Campaigns) {
    let State;
    (function (State) {
        State["INCOMPLETE"] = "incomplete";
        State["READY"] = "ready";
        State["SCHEDULED"] = "scheduled";
        State["RUNNING"] = "running";
        State["SUSPENDED"] = "suspended";
        State["PAUSED"] = "paused";
        State["FAILED"] = "failed";
        State["ENDED"] = "ended";
        State["ERROR"] = "error";
    })(State = Campaigns.State || (Campaigns.State = {}));
    let Status;
    (function (Status) {
        Status["SCHEDULED"] = "scheduled";
        Status["CANCELLED"] = "cancelled";
        Status["STARTED"] = "started";
        Status["PAUSED"] = "paused";
        Status["RESUMED"] = "resumed";
        Status["ENDED"] = "ended";
        Status["RESETTED"] = "resetted";
    })(Status = Campaigns.Status || (Campaigns.Status = {}));
    let HandlerType;
    (function (HandlerType) {
        HandlerType["TIMER"] = "timer";
        HandlerType["EVENT"] = "event";
        HandlerType["REQUEST"] = "request";
    })(HandlerType = Campaigns.HandlerType || (Campaigns.HandlerType = {}));
})(Campaigns || (exports.Campaigns = Campaigns = {}));
//# sourceMappingURL=campaigns.js.map