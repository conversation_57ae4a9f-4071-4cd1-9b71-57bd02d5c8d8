"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Barcodes = void 0;
var Barcodes;
(function (Barcodes) {
    let Type;
    (function (Type) {
        Type["QRCODE"] = "QRCODE";
        Type["CODE39"] = "CODE39";
        Type["ISBN10"] = "ISBN10";
        Type["CODE128"] = "CODE128";
        Type["UPCA"] = "UPCA";
        Type["UPCE"] = "UPCE";
        Type["EAN13"] = "EAN13";
        Type["EAN8"] = "EAN8";
        Type["ITF"] = "ITF";
        Type["DATABAR"] = "DATABAR";
        Type["DATABAREXP"] = "DATABAREXP";
        Type["ISBN13"] = "ISBN13";
        Type["PDF417"] = "PDF417";
        Type["CODABAR"] = "CODABAR";
        Type["AZTEC"] = "AZTEC";
        Type["DATAMATRIX"] = "DATAMATRIX";
    })(Type = Barcodes.Type || (Barcodes.Type = {}));
})(Barcodes || (exports.Barcodes = Barcodes = {}));
//# sourceMappingURL=barcodes.js.map