"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Geo = void 0;
var Geo;
(function (Geo) {
    let GeoType;
    (function (GeoType) {
        GeoType["POINT"] = "Point";
        GeoType["MULTI_POINT"] = "MultiPoint";
        GeoType["LINE_STRING"] = "LineString";
        GeoType["MULTI_LINE_STRING"] = "MultiLineString";
        GeoType["POLYGON"] = "Polygon";
        GeoType["MULTI_POLYGON"] = "MultiPolygon";
        GeoType["GEOMETRY_COLLECTION"] = "GeometryCollection";
    })(GeoType = Geo.GeoType || (Geo.GeoType = {}));
})(Geo || (exports.Geo = Geo = {}));
//# sourceMappingURL=geo.js.map