export declare namespace Permissions {
    enum Type {
        SERVICETERMS = "serviceTerms",
        PRIVACYPOLICY = "privacyPolicy",
        MOBILE = "mobile",
        EMAIL = "email",
        POST = "postal",
        VOICE = "voice",
        PUSH = "push",
        LOCATION = "location",
        CAMERA = "camera",
        PHOTOS = "photos",
        CONTACTS = "contacts",
        CALENDAR = "calendar",
        HOMEKIT = "homeKit",
        HEALTH = "health",
        SPEECH_RECOGNITION = "speechRecognition",
        BLE_CENTRAL = "bleCentral",
        BLE_PERIPHERAL = "blePeripheral",
        MICROPHONE = "microphone",
        MOTION_FITNESS = "motionFitness",
        NOTIFICATIONS = "notifications"
    }
    enum Status {
        ALLOWED = "allowed",
        DENIED = "denied",
        BLOCKED = "blocked",
        UNKNOWN = "unknown"
    }
    enum Options {
        DONOTDISTURB = "doNotDisturb"
    }
}
