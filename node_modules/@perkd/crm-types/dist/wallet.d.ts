import { Actions as ActionNS } from './wallet/actions';
import { AppEvents as AppEventsNS } from './wallet/appevents';
import { CardMasters as CardMastersNS } from './wallet/cardmasters';
import { Cards as CardsNS } from './wallet/cards';
import { Widgets as WidgetsNS, Bags as BagsNS } from './wallet/widgets';
import { Installations as InstallationsNS } from './wallet/installations';
import { Messages as MessagesNS } from './wallet/messages';
import { Orders as OrdersNS } from './wallet/orders';
import { Notify as NotifyNS } from './wallet/notify';
import { Share as ShareNS } from './wallet/share';
export declare namespace Wallet {
    export import Actions = ActionNS;
    export import AppEvents = AppEventsNS;
    export import CardMasters = CardMastersNS;
    export import Cards = CardsNS;
    export import Widgets = WidgetsNS;
    export import Bags = BagsNS;
    export import Installations = InstallationsNS;
    export import Messages = MessagesNS;
    export import Orders = OrdersNS;
    export import Notify = NotifyNS;
    export import Share = ShareNS;
}
