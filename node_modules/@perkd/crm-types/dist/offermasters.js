"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfferMasters = void 0;
var OfferMasters;
(function (OfferMasters) {
    let Kind;
    (function (Kind) {
        Kind["DISCOUNT"] = "discount";
        Kind["VOUCHER"] = "voucher";
        Kind["TICKET"] = "ticket";
        Kind["PICKUP"] = "pickup"; // vending machine collection
    })(Kind = OfferMasters.Kind || (OfferMasters.Kind = {}));
    let Channel;
    (function (Channel) {
        Channel["STORE"] = "store";
        Channel["ONLINE"] = "online";
        Channel["PERKD"] = "perkd";
    })(Channel = OfferMasters.Channel || (OfferMasters.Channel = {}));
    let State;
    (function (State) {
        State["INCOMPLETE"] = "incomplete";
        State["READY"] = "ready";
        State["ACTIVE"] = "active";
        State["EXPIRED"] = "expired";
        State["ARCHIVED"] = "archived";
        State["UNCOMMIT"] = "uncommit";
    })(State = OfferMasters.State || (OfferMasters.State = {}));
    let CodeFormat;
    (function (CodeFormat) {
        CodeFormat["FIXED"] = "fixed";
        CodeFormat["SERIAL"] = "serial";
        CodeFormat["PRESET"] = "preset";
        CodeFormat["RANDOM"] = "random";
    })(CodeFormat = OfferMasters.CodeFormat || (OfferMasters.CodeFormat = {}));
    let Authorize;
    (function (Authorize) {
        Authorize["SCAN"] = "scan";
        Authorize["NFC"] = "nfc";
    })(Authorize = OfferMasters.Authorize || (OfferMasters.Authorize = {}));
})(OfferMasters || (exports.OfferMasters = OfferMasters = {}));
//# sourceMappingURL=offermasters.js.map