import { Businesses } from './businesses';
import { Contacts } from './contacts';
import { Geo } from './geo';
export declare namespace Places {
    enum Type {
        STORE = "store",
        VENDING_MACHINE = "vending",// perkd specific
        KIOSK = "kiosk",// self-serve kiosk (perkd specific)
        RESTAURANT = "restaurant",
        LODGING = "lodging",// hotel
        DOCTOR = "doctor",// clinic
        PHARMACY = "pharmacy",
        C_STORE = "convenience_store",
        D_STORE = "department_store",
        MOVIE_THEATER = "movie_theater",// cinema
        SUPERMARKET = "supermarket",
        SHOPPING_MALL = "shopping_mall",
        AIRPORT = "airport",
        LIBRARY = "library"
    }
    enum PositionKey {
        VENDING_MACHINE = "vending"
    }
    type Position = {
        key: string;
        value: string;
    }[];
    type Spot = Contacts.Address & {
        name?: string;
        type: Type;
        position: Position;
        placeId: string;
        resourceId?: string;
    };
    type Place = {
        id?: string;
        type: string;
        name: string;
        brand: Businesses.Brand;
        geo: Geo.Geometry;
        geoFence?: {
            radius: number;
        };
        locale?: Contacts.Locale;
        openingHours?: Businesses.Hours;
        urls?: any[];
        startTime?: Date;
        endTime?: Date;
        dinein?: {
            available: boolean;
            hours: Businesses.Hours;
        };
        pickup?: {
            available: boolean;
            hours: Businesses.Hours;
            timeslot: number;
            minFulfillTime?: number;
            maxFulfillTime?: number;
            instructions?: string;
        };
        deliver?: {
            available: boolean;
            hours: Businesses.Hours;
            timeslot: number;
            minFulfillTime?: number;
            maxFulfillTime?: number;
            instructions?: string;
            distance?: number;
            price?: number;
        };
        unlisted?: boolean;
        external?: any;
        visible: boolean;
        phoneList?: Contacts.Phone[];
        addressList?: Contacts.Address[];
    };
}
