"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Rewards = exports.RewardMasters = void 0;
var RewardMasters;
(function (RewardMasters) {
    let Policies;
    (function (Policies) {
        Policies["ISSUE"] = "issue";
        Policies["WITHDRAW"] = "withdraw";
        Policies["EXTEND"] = "extend";
        Policies["ISSUE_STAMPS"] = "issuestamps";
        Policies["DEDUCT_STAMPS"] = "deductstamps";
        Policies["CARRYOVER_STAMPS"] = "carryoverstamps";
    })(Policies = RewardMasters.Policies || (RewardMasters.Policies = {}));
    let State;
    (function (State) {
        State["INCOMPLETE"] = "incomplete";
        State["READY"] = "ready";
        State["ACTIVE"] = "active";
        State["EXPIRED"] = "expired";
        State["ARCHIVED"] = "archived";
        State["UNCOMMIT"] = "uncommit";
    })(State = RewardMasters.State || (RewardMasters.State = {}));
    const { ISSUE, EXTEND, WITHDRAW, ISSUE_STAMPS, DEDUCT_STAMPS, CARRYOVER_STAMPS } = Policies;
    RewardMasters.QUALIFIERS = {
        SET: [ISSUE, EXTEND, WITHDRAW],
        STAMPS: [ISSUE_STAMPS, DEDUCT_STAMPS, CARRYOVER_STAMPS],
    };
})(RewardMasters || (exports.RewardMasters = RewardMasters = {}));
var Rewards;
(function (Rewards) {
    let State;
    (function (State) {
        State["PENDING"] = "pending";
        State["ACTIVE"] = "active";
        State["WITHDRAWN"] = "withdrawn";
        State["CANCELLED"] = "cancelled";
        State["EXPIRED"] = "expired";
    })(State = Rewards.State || (Rewards.State = {}));
    let TransactionType;
    (function (TransactionType) {
        TransactionType["ISSUE"] = "issue";
        TransactionType["DEDUCT"] = "deduct";
        TransactionType["CARRYOVER"] = "carryover";
    })(TransactionType = Rewards.TransactionType || (Rewards.TransactionType = {}));
    // ACTION = {
    // 	ISSUE: 'issue',
    // 	WITHDRAW: 'withdraw',
    // 	EXTEND: 'extend',
    // 	ISSUE_STAMPS: 'issuestamps',
    // 	DEDUCT_STAMPS: 'deductstamps',
    // 	CARRYOVER_STAMPS: 'carryoverstamps',
    // 	ISSUE_OFFER: 'issueoffer',
    // 	CANCEL_OFFER: 'canceloffer',
    // }
})(Rewards || (exports.Rewards = Rewards = {}));
//# sourceMappingURL=rewards.js.map