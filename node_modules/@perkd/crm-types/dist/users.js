"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Users = void 0;
var Users;
(function (Users) {
    let Roles;
    (function (Roles) {
        Roles["SUPER_ADMIN"] = "super_admin";
        Roles["ADMIN"] = "admin";
        Roles["SYSTEM"] = "system";
        Roles["CRM_ADMIN_APP"] = "crm_admin_app";
        Roles["STORE_FRONT_ADMIN_APP"] = "store_front_admin_app";
        Roles["CLIENT_ADMIN"] = "client_admin";
        Roles["REPORT_ONLY"] = "report_only";
        Roles["ANALYSIS"] = "analysis";
        Roles["DASHBOARD_ONLY"] = "dashboard_only";
        Roles["CRM_MANAGER"] = "crm_manager";
        Roles["MERCHANT"] = "merchant";
        Roles["TW_TEAM"] = "tw_team";
        Roles["CUSTOMER_SUPPORT"] = "customer_support";
        Roles["WAVEO_STAFF"] = "waveo_staff";
        Roles["PERKD_STAFF"] = "perkd_staff";
        Roles["DEV_TEAM"] = "dev_team";
        Roles["OPS"] = "ops";
        Roles["REWARD_MANAGER"] = "reward_manager";
        Roles["MANAGER"] = "manager";
        Roles["ACL_ERR_MANAGER"] = "acl_err_manager";
        Roles["ACL_ERR_CAMPAIGN"] = "acl_err_campaign";
        Roles["MEMBERSHIP"] = "membership";
        Roles["STAFF"] = "staff";
        Roles["PLACE"] = "place";
        Roles["CONTENT_ONLY"] = "content_only";
        Roles["MARKETING"] = "marketing";
        Roles["CAMPAIGN"] = "campaign";
        Roles["DASHBOARD"] = "dashboard";
        Roles["VENDING"] = "vending";
        Roles["DEMO"] = "demo";
        // dinein
        Roles["KITCHEN"] = "kitchen";
        Roles["COLLECT"] = "collect";
        Roles["SERVICE"] = "service";
    })(Roles = Users.Roles || (Users.Roles = {}));
    let Type;
    (function (Type) {
        Type["ADMIN"] = "admin";
        Type["SYSTEM"] = "system";
        Type["APP"] = "app";
        Type["PARTNER"] = "partner";
    })(Type = Users.Type || (Users.Type = {}));
})(Users || (exports.Users = Users = {}));
//# sourceMappingURL=users.js.map