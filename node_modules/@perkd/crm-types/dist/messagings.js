"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Messagings = void 0;
var Messagings;
(function (Messagings) {
    let Service;
    (function (Service) {
        Service["SMS"] = "sms";
        Service["EMAIL"] = "email";
        Service["VOICE"] = "voice";
        Service["PUSH"] = "push";
        Service["WHATSAPP"] = "whatsapp";
        Service["NOTIFY"] = "notify";
        Service["RICH"] = "rich";
        Service["CARD"] = "card";
        Service["OFFER"] = "offer";
    })(Service = Messagings.Service || (Messagings.Service = {}));
    let State;
    (function (State) {
        State["PENDING"] = "pending";
        State["SENDING"] = "sending";
        State["PAUSED"] = "paused";
        State["STOPPED"] = "stopped";
        State["COMPLETED"] = "completed";
    })(State = Messagings.State || (Messagings.State = {}));
})(Messagings || (exports.Messagings = Messagings = {}));
//# sourceMappingURL=messagings.js.map