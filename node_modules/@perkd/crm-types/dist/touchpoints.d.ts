import { Places } from './places';
export declare namespace Touchpoints {
    enum Type {
        STORE = "store",
        ONLINE = "online",// generic
        WEBSITE = "website",
        SOCIAL = "social",
        EMAIL = "email",
        SMS = "sms",
        MIRCOSITE = "microsite",
        ADVERTISEMENT = "advertisement",
        QRCODE = "qrcode",
        NFC = "nfc",
        PERKD = "perkd",
        POS = "pos",
        APP = "app",
        CRM = "crm",
        X = "x",
        ECOMMERCE = "ecommerce",
        UNKNOWN = "unknown"
    }
    enum Attributed {
        SYSTEM = "system",
        STAFF = "staff",
        USER = "user",
        CAMPAIGN = "campaign",
        BOOKING = "booking",
        FACEBOOK = "facebook",
        INSTAGRAM = "instagram",
        WECHAT = "wechat",
        WEBSITE = "website",
        MICROSITE = "microsite",
        LANDINGPAGE = "landingpage",
        PARTNER = "partner",
        LOCALMEDIA = "localmedia",
        KOL = "kol"
    }
    enum Format {
        SHARE = "share",
        STAFF_CARD = "staffcard",
        VENDING_MACHINE = "vending",
        KIOSK = "kiosk",// self-serve kiosk
        TERMINAL = "terminal",// payment termina, eg. ingenico, nxiot
        SHOP = "shop",// MyShop
        BAG = "bag",// Bag
        SCAN = "scan",// scan QR-Code, NFC
        MESSAGE = "message",// rich message
        LIBRARY = "lib",// Discover section
        APPLINK = "applink",// app/action url link
        WIDGET = "widget",// Widget (generic), actual will be 'widget.<name>', eg widget.place
        SOCIAL = "social",
        NEWSPAPER = "newspaper",
        MAGAZINE = "magazine"
    }
    type Touchpoint = {
        type?: Type;
        format?: Format;
        location?: Places.Spot;
        attributedTo?: {
            type: Attributed;
            id?: string;
            name?: string;
            userId?: string;
            userName?: string;
        };
        context?: any;
        instrument?: {
            type?: string;
            id?: string;
            name?: string;
        };
        touchedAt?: Date;
    };
}
