export declare namespace Apis {
    enum Headers {
        X_ACCESS_TOKEN = "x-access-token",
        INSTALL = "perkd-install",
        LOCATION = "perkd-location",
        CARD = "card",// used by staff cards
        AUTHORIZATION = "authorization",
        TENANT = "tenant-code",
        USER = "tenant-user",// FIXME: deprecate tenant-user to be more secure
        FORWARDED_FOR = "x-forwarded-for",// event-gateway is using it, but not in context
        IDEMPOTENCY_KEY = "idempotency-key",// no use?
        USER_AGENT = "user-agent",// device activation, but not in context
        BEARER = "Bearer ",// communicate with 3rd party, e.g. sfcc provider
        TIMEZONE = "timezone",
        ORIGIN = "origin",
        APP_NAME = "app-name"
    }
    enum Parameters {
        ACCESS_TOKEN = "access_token",
        TENANT = "tenant_code"
    }
    enum ENV {
        DEV = "development",
        TEST = "test",
        PROD = "production"
    }
}
