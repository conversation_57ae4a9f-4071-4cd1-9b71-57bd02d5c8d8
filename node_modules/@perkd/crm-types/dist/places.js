"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Places = void 0;
var Places;
(function (Places) {
    // ref: https://developers.google.com/maps/documentation/places/web-service/supported_types
    let Type;
    (function (Type) {
        Type["STORE"] = "store";
        Type["VENDING_MACHINE"] = "vending";
        Type["KIOSK"] = "kiosk";
        Type["RESTAURANT"] = "restaurant";
        Type["LODGING"] = "lodging";
        Type["DOCTOR"] = "doctor";
        Type["PHARMACY"] = "pharmacy";
        Type["C_STORE"] = "convenience_store";
        Type["D_STORE"] = "department_store";
        Type["MOVIE_THEATER"] = "movie_theater";
        Type["SUPERMARKET"] = "supermarket";
        Type["SHOPPING_MALL"] = "shopping_mall";
        Type["AIRPORT"] = "airport";
        Type["LIBRARY"] = "library";
    })(Type = Places.Type || (Places.Type = {}));
    let PositionKey;
    (function (PositionKey) {
        PositionKey["VENDING_MACHINE"] = "vending";
    })(PositionKey = Places.PositionKey || (Places.PositionKey = {}));
})(Places || (exports.Places = Places = {}));
//# sourceMappingURL=places.js.map