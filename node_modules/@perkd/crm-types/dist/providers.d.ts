export declare namespace Providers {
    enum Payment {
        PERKDPAY = "perkdpay",
        STRIPE = "stripe",
        SHOPIFY = "shopify",
        ADYEN = "adyen",
        MYPAY = "mypay",
        RAZER = "razer",
        GKASH = "gkash",
        FLEXM = "flexm"
    }
    enum Order {
        MENU = "menu",// perkd f&b web ordering
        SHOPIFY = "shopify",
        GRABFOOD = "grabfood",
        GRABMART = "grabmart",
        UBEREATS = "ubereats",
        FOODPANDA = "foodpanda",
        LAZADA = "lazada",
        SHOPEE = "shopee",
        SALESFORCE = "sfcc"
    }
    enum Vending {
        ZEAL = "zeal",
        CLOUDRETAIL = "cloudretail"
    }
    const PROVIDER: {
        AWS: string;
        GOOGLE: string;
        FACEBOOK: string;
        LALAMOVE: string;
        FEIE: string;
        EZRECEIPT: string;
        NXIOT: string;
        POS: string;
        UNIFI: string;
        ZEAL: Vending.ZEAL;
        CLOUDRETAIL: Vending.CLOUDRETAIL;
        MENU: Order.MENU;
        SHOPIFY: Order.SHOPIFY;
        GRABFOOD: Order.GRABFOOD;
        GRABMART: Order.GRABMART;
        UBEREATS: Order.UBEREATS;
        FOODPANDA: Order.FOODPANDA;
        LAZADA: Order.LAZADA;
        SHOPEE: Order.SHOPEE;
        SALESFORCE: Order.SALESFORCE;
        PERKDPAY: Payment.PERKDPAY;
        STRIPE: Payment.STRIPE;
        ADYEN: Payment.ADYEN;
        MYPAY: Payment.MYPAY;
        RAZER: Payment.RAZER;
        GKASH: Payment.GKASH;
        FLEXM: Payment.FLEXM;
        PERKD: string;
    };
    const PAYMENT_PROVIDERS: Payment[];
    const ORDER_PROVIDERS: Order[];
    const FULFILLMENT_PROVIDERS: string[];
    const PRINT_PROVIDERS: string[];
    const INVOICE_PROVIDERS: string[];
    enum Type {
        SHARED = "shared",
        TENANTED = "tenanted",
        INSTANCE = "instance"
    }
    enum Service {
        CUSTOMER = "customer",
        ORDER = "order",
        FULFILLMENT = "fulfillment",
        PRODUCT = "product",
        DISCOUNT = "discount",
        PLACE = "place",
        INVENTORY = "inventory",
        STORE = "store",
        DIGITAL = "digital",
        KITCHEN = "kitchen",
        SHOPIFY = "shopify",
        INVOICE = "invoice",
        SMS = "sms",
        EMAIL = "email",
        VOICE = "voice",
        WHATSAPP = "whatsapp",
        NOTIFY = "notify",
        RICH = "rich",
        OFFER = "offer",
        PUSH = "push",// X-only
        CARD = "card",// X-only
        PRINT = "print",// cloud printing
        STORAGE = "storage",// cloud storage (eg. s3)
        DASHBOARD = "dashboard",// quicksight
        DATA = "data",// athena
        MAP = "map",// geo location
        CALENDAR = "calendar",// cloud calendar
        SOCIAL = "social",// Facebook, etc
        WIFI = "wifi"
    }
    enum Module {
        CUSTOMERS = "customers",
        BUSINESSES = "businesses",
        OFFERS = "offers",
        DISCOUNTS = "discounts",
        ORDERS = "orders",
        FULFILLMENTS = "fulfillments",
        PRODUCTS = "products",
        PLACES = "places",
        SMARTCOLLECTIONS = "smartcollections"
    }
}
