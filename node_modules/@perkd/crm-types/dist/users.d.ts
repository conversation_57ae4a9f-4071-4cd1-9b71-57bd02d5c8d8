export declare namespace Users {
    enum Roles {
        SUPER_ADMIN = "super_admin",
        ADMIN = "admin",
        SYSTEM = "system",
        CRM_ADMIN_APP = "crm_admin_app",
        STORE_FRONT_ADMIN_APP = "store_front_admin_app",
        CLIENT_ADMIN = "client_admin",
        REPORT_ONLY = "report_only",
        ANALYSIS = "analysis",
        DASHBOARD_ONLY = "dashboard_only",
        CRM_MANAGER = "crm_manager",
        MERCHANT = "merchant",
        TW_TEAM = "tw_team",
        CUSTOMER_SUPPORT = "customer_support",
        WAVEO_STAFF = "waveo_staff",
        PERKD_STAFF = "perkd_staff",
        DEV_TEAM = "dev_team",
        OPS = "ops",
        REWARD_MANAGER = "reward_manager",
        MANAGER = "manager",
        ACL_ERR_MANAGER = "acl_err_manager",
        ACL_ERR_CAMPAIGN = "acl_err_campaign",
        MEMBERSHIP = "membership",
        STAFF = "staff",
        PLACE = "place",
        CONTENT_ONLY = "content_only",
        MARKETING = "marketing",
        CAMPAIGN = "campaign",
        DASHBOARD = "dashboard",
        VENDING = "vending",
        DEMO = "demo",
        KITCHEN = "kitchen",
        COLLECT = "collect",
        SERVICE = "service"
    }
    enum Type {
        ADMIN = "admin",
        SYSTEM = "system",
        APP = "app",
        PARTNER = "partner"
    }
}
