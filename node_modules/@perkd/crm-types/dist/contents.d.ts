export declare namespace Contents {
    enum State {
        DRAFT = "draft",
        INCOMPLETE = "incomplete",
        COMPLETE = "complete",
        ARCHIVED = "archived"
    }
    enum Resource {
        MICROSITE = "microsite",
        EMAIL = "email",
        APPLET = "applet",
        IMAGE = "image",
        FONT = "font",
        ATHENA = "athena",
        OTHERS = "others"
    }
    enum Theme {
        LIGHT = "light",
        DARK = "dark",
        DEFAULT = "light"
    }
    enum MediaType {
        JSON_DATA = "application/json",
        FORM_DATA = "multipart/form-data",
        FORM_URLENCODED = "application/x-www-form-urlencoded",
        HTML = "text/html",
        CSV = "text/csv",
        TEXT = "text/plain",
        JAVASCRIPT = "text/javascript",
        CALENDAR = "text/calendar",
        JPEG = "image/jpeg",
        PNG = "image/png",
        GIF = "image/gif",
        TIFF = "image/tiff",
        MPEG = "video/mpeg",
        MP4 = "video/mp4",
        PDF = "application/pdf",
        ZIP = "application/zip",
        EXCEL = "application/vnd.ms-excel",
        TTF = "font/ttf",
        OTF = "font/otf",
        SFNT = "font/sfnt",
        WOFF = "font/woff",
        WOFF2 = "font/woff2"
    }
    const TYPE_FOR_EXTENSION: {
        '.jpg': MediaType;
        '.jpeg': MediaType;
        '.png': MediaType;
        '.tiff': MediaType;
    };
}
export declare namespace Emails {
    const INDEX_FILE = "index.html";
    const ContentType = Contents.MediaType.HTML;
}
export declare namespace Microsites {
    enum State {
        INCOMPLETE = "incomplete",
        READY = "ready",
        INUSE = "inuse",
        ARCHIVED = "archived"
    }
    const PUBLIC_SITES: string[], INDEX_FILE = "index.html", EJS_OPTIONS: {
        delimiter: string;
    }, PUBLIC = "public", ContentType = Contents.MediaType.HTML, CacheControl = "public, max-age=180";
}
export declare namespace Messages {
    const COVER = "cover", BODY = "body", COVER_TEMPLATE = "cover.htm", BODY_TEMPLATE = "body.htm", EJS_OPTIONS: {
        delimiter: string;
    };
}
export declare namespace Fonts {
    enum Source {
        GOOGLE = "google",
        CUSTOM = "custom"
    }
}
export declare namespace Applets {
    const INDEX_FILE = "index.html";
    const MIME_TYPE_HTML = Contents.MediaType.HTML;
}
