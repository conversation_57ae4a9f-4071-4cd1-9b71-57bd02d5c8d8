{"version": 3, "file": "settings.js", "sourceRoot": "", "sources": ["../src/settings.ts"], "names": [], "mappings": ";;;AAAA,mCAA+B;AAE/B,2CAAuC;AAGvC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,aAAK,CAAC,KAAK,EAChD,EAAE,IAAI,EAAE,GAAG,qBAAS,CAAC,QAAQ,CAAA;AAE9B,IAAiB,QAAQ,CA0SxB;AA1SD,WAAiB,QAAQ;IAExB,IAAY,IAeX;IAfD,WAAY,IAAI;QACf,yBAAiB,CAAA;QACjB,uBAAe,CAAA;QACf,mBAAW,CAAA;QACX,6BAAqB,CAAA;QACrB,uBAAe,CAAA;QACf,2BAAmB,CAAA;QACnB,mCAA2B,CAAA;QAC3B,2BAAmB,CAAA;QACnB,2BAAmB,CAAA;QACnB,uBAAe,CAAA;QACf,uBAAe,CAAA;QACf,yBAAiB,CAAA;QACjB,+BAAuB,CAAA;QACvB,mCAA2B,CAAA;IAC5B,CAAC,EAfW,IAAI,GAAJ,aAAI,KAAJ,aAAI,QAef;IAEc,eAAM,GAA+E,IAAI,OAAnF,EAAE,cAAK,GAAwE,IAAI,MAA5E,EAAE,gBAAO,GAA+D,IAAI,QAAnE,EAAE,oBAAW,GAAkD,IAAI,YAAtD,EAAE,gBAAO,GAAyC,IAAI,QAA7C,EAAE,gBAAO,GAAgC,IAAI,QAApC,EAAE,cAAK,GAAyB,IAAI,MAA7B,EAAE,cAAK,GAAkB,IAAI,MAAtB,EAAE,oBAAW,GAAK,IAAI,YAAT,CAAS;IAmLxG,IAAY,SAIX;IAJD,WAAY,SAAS;QACpB,+BAAkB,CAAA;QAClB,2CAA8B,CAAA;QAC9B,+CAAkC,CAAA;IACnC,CAAC,EAJW,SAAS,GAAT,kBAAS,KAAT,kBAAS,QAIpB;IAaY,gBAAO,GAAG;QACtB,QAAQ,EAAE,gBAAgB;QAC1B,KAAK,EAAE;YACN,SAAS,EAAE;gBACV,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;gBACP,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE;oBACN,KAAK,EAAE,IAAI;oBACX,WAAW,EAAE,CAAC;iBACd;aACD;SACD;QACD,OAAO,EAAE;YACR,mBAAmB,EAAE,QAAQ;YAC7B,gBAAgB,EAAE,EAAE,GAAG,IAAI,EAAG,eAAe;YAC7C,iBAAiB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,cAAc;YACjD,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAG,eAAe;YAC9C,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAG,cAAc;YAC/C,mBAAmB,EAAE,EAAE,EAAI,UAAU;YACrC,QAAQ,EAAE,CAAC;SACX;QACD,KAAK,EAAE;YACN,SAAS,EAAE;gBACV,gBAAgB,EAAE;oBACjB,IAAI,EAAE,gCAAgC;oBACtC,MAAM,EAAE,eAAe;oBACvB,KAAK,EAAE,CAAE,OAAO,CAAE;iBAClB;gBACD,aAAa,EAAE;oBACd,IAAI,EAAE,6BAA6B;oBACnC,MAAM,EAAE,eAAe;oBACvB,KAAK,EAAE,CAAE,OAAO,CAAE;iBAClB;gBACD,cAAc,EAAE;oBACf,IAAI,EAAE,uBAAuB;oBAC7B,MAAM,EAAE,eAAe;oBACvB,KAAK,EAAE,CAAE,OAAO,CAAE;iBAClB;aACD;YACD,OAAO,EAAE;gBACR,GAAG,EAAE,GAAG,EAAG,mBAAmB;aAC9B;SACD;QACD,KAAK,EAAE;YACN,QAAQ,EAAE,IAAI;SACd;QACD,OAAO,EAAE;YACR,KAAK,EAAE;gBACN,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,EAAE;gBACX,QAAQ,EAAE,GAAG,EAAK,UAAU;gBAC5B,iBAAiB,EAAE,EAAE,EAAG,OAAO;gBAC/B,YAAY,EAAE,CAAC,EAAI,MAAM;gBACzB,YAAY,EAAE,EAAE,EAAI,MAAM;gBAC1B,WAAW,EAAE,CAAC,EAAK,SAAS;gBAC5B,WAAW,EAAE,EAAE,EAAI,SAAS;gBAC5B,WAAW,EAAE,GAAG,EAAI,UAAU;gBAC9B,WAAW,EAAE,GAAG,EAAI,OAAO;gBAC3B,WAAW,EAAE,GAAG,CAAG,QAAQ;aAC3B;SACD;QACD,OAAO,EAAE;YACR,KAAK,EAAE;gBACN,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;gBACrC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAI,UAAU;gBAC9B,QAAQ,EAAE;oBACT,OAAO,EAAE,EAAE,EAAI,aAAa;oBAC5B,IAAI,EAAE,EAAE,EAAK,aAAa;oBAC1B,OAAO,EAAE,GAAG,CAAG,cAAc;iBAC7B;gBACD,QAAQ,EAAE;oBACT,IAAI,EAAE,GAAG,EAAK,QAAQ;oBACtB,GAAG,EAAE,EAAE,EAAK,QAAQ;oBACpB,OAAO,EAAE,CAAC,EAAK,QAAQ;oBACvB,WAAW,EAAE;wBACZ,MAAM,EAAE,CAAC,EAAI,aAAa;wBAC1B,QAAQ,EAAE,CAAC,CAAG,YAAY;qBAC1B;iBACD;aACD;SACD;KACD,CAAA;AACF,CAAC,EA1SgB,QAAQ,wBAAR,QAAQ,QA0SxB"}