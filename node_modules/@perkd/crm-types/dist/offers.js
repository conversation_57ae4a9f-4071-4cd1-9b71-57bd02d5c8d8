"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Offers = exports.Discounts = void 0;
const offermasters_1 = require("./offermasters");
var Discounts;
(function (Discounts) {
    let Kind;
    (function (Kind) {
        Kind["FIXED"] = "fixed";
        Kind["PERCENTAGE"] = "percentage";
    })(Kind = Discounts.Kind || (Discounts.Kind = {}));
    let Use;
    (function (Use) {
        Use["ALWAYS"] = "always";
        Use["ALONE"] = "alone";
        Use["ONE"] = "one";
        Use["COMBINE"] = "combine";
        Use["SINGLE"] = "single";
    })(Use = Discounts.Use || (Discounts.Use = {}));
    let TargetType;
    (function (TargetType) {
        TargetType["ITEM"] = "item";
        TargetType["SHIPPING"] = "shipping";
    })(TargetType = Discounts.TargetType || (Discounts.TargetType = {}));
    let TargetSelection;
    (function (TargetSelection) {
        TargetSelection["ALL"] = "all";
        TargetSelection["ENTITLED"] = "entitled";
    })(TargetSelection = Discounts.TargetSelection || (Discounts.TargetSelection = {}));
    let AllocationMethod;
    (function (AllocationMethod) {
        AllocationMethod["EACH"] = "each";
        AllocationMethod["ACROSS"] = "across";
    })(AllocationMethod = Discounts.AllocationMethod || (Discounts.AllocationMethod = {}));
})(Discounts || (exports.Discounts = Discounts = {}));
var Offers;
(function (Offers) {
    let Kind;
    (function (Kind) {
        Kind["DISCOUNT"] = "discount";
        Kind["TICKET"] = "ticket";
        Kind["VOUCHER"] = "voucher";
        Kind["PICKUP"] = "pickup";
    })(Kind = Offers.Kind || (Offers.Kind = {}));
    let State;
    (function (State) {
        State["PENDING"] = "pending";
        State["ACTIVE"] = "active";
        State["AUTHORIZED"] = "authorized";
        State["REDEEMED"] = "redeemed";
        State["FULLY_REDEEMED"] = "fullyredeemed";
        State["CANCELLED"] = "cancelled";
        State["EXPIRED"] = "expired";
        State["TRANSFERRING"] = "transferring";
        State["TRANSFERRED"] = "transferred";
    })(State = Offers.State || (Offers.State = {}));
    Offers.RedemptionChannel = offermasters_1.OfferMasters.Channel;
})(Offers || (exports.Offers = Offers = {}));
//# sourceMappingURL=offers.js.map