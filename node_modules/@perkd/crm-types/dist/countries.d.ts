type CountryName = {
    [code: string]: string;
};
type CountryCode = {
    [code: string]: number;
};
type ISO3166Mapping = {
    [code: string]: string;
};
export declare namespace Countries {
    type Info = {
        code: string;
        name: string;
        phone: number;
    };
    const CODE: CountryCode;
    const NAME: {
        [language: string]: CountryName;
    };
    const HEADER: {
        en: {
            AF: string;
            BS: string;
            KH: string;
            DK: string;
            EC: string;
            FK: string;
            GA: string;
            HT: string;
            IS: string;
            JM: string;
            KZ: string;
            LA: string;
            MO: string;
            NA: string;
            OM: string;
            PK: string;
            QA: string;
            RO: string;
            SH: string;
            TW: string;
            VI: string;
            VU: string;
            WF: string;
            YE: string;
            ZM: string;
        };
    };
    const FILTER: {
        AF: string;
        AL: string;
        DZ: string;
        AS: string;
        AD: string;
        AO: string;
        AI: string;
        AQ: string;
        AG: string;
        AR: string;
        AM: string;
        AW: string;
        AU: string;
        AT: string;
        AZ: string;
        BS: string;
        BH: string;
        BD: string;
        BB: string;
        BY: string;
        BE: string;
        BZ: string;
        BJ: string;
        BM: string;
        BT: string;
        BO: string;
        BA: string;
        BW: string;
        BV: string;
        BR: string;
        BQ: string;
        IO: string;
        VG: string;
        BN: string;
        BG: string;
        BF: string;
        BI: string;
        KH: string;
        CM: string;
        CA: string;
        CT: string;
        CV: string;
        KY: string;
        CF: string;
        TD: string;
        CL: string;
        CN: string;
        CX: string;
        CC: string;
        CO: string;
        KM: string;
        CG: string;
        CD: string;
        CK: string;
        CR: string;
        HR: string;
        CU: string;
        CY: string;
        CZ: string;
        CI: string;
        DK: string;
        DJ: string;
        DM: string;
        DO: string;
        NQ: string;
        DD: string;
        EC: string;
        EG: string;
        SV: string;
        GQ: string;
        ER: string;
        EE: string;
        ET: string;
        FK: string;
        FO: string;
        FJ: string;
        FI: string;
        FR: string;
        GF: string;
        PF: string;
        TF: string;
        FQ: string;
        GA: string;
        GM: string;
        GE: string;
        DE: string;
        GH: string;
        GI: string;
        GR: string;
        GL: string;
        GD: string;
        GP: string;
        GU: string;
        GT: string;
        GG: string;
        GN: string;
        GW: string;
        GY: string;
        HT: string;
        HM: string;
        HN: string;
        HK: string;
        HU: string;
        IS: string;
        IN: string;
        ID: string;
        IR: string;
        IQ: string;
        IE: string;
        IM: string;
        IL: string;
        IT: string;
        JM: string;
        JP: string;
        JE: string;
        JT: string;
        JO: string;
        KZ: string;
        KE: string;
        KI: string;
        KW: string;
        KG: string;
        LA: string;
        LV: string;
        LB: string;
        LS: string;
        LR: string;
        LY: string;
        LI: string;
        LT: string;
        LU: string;
        MO: string;
        MK: string;
        MG: string;
        MW: string;
        MY: string;
        MV: string;
        ML: string;
        MT: string;
        MH: string;
        MQ: string;
        MR: string;
        MU: string;
        YT: string;
        FX: string;
        MX: string;
        FM: string;
        MI: string;
        MD: string;
        MC: string;
        MN: string;
        ME: string;
        MS: string;
        MA: string;
        MZ: string;
        MM: string;
        NA: string;
        NR: string;
        NP: string;
        NL: string;
        AN: string;
        NT: string;
        NC: string;
        NZ: string;
        NI: string;
        NE: string;
        NG: string;
        NU: string;
        NF: string;
        KP: string;
        VD: string;
        MP: string;
        NO: string;
        OM: string;
        PC: string;
        PK: string;
        PW: string;
        PS: string;
        PA: string;
        PZ: string;
        PG: string;
        PY: string;
        YD: string;
        PE: string;
        PH: string;
        PN: string;
        PL: string;
        PT: string;
        PR: string;
        QA: string;
        RO: string;
        RU: string;
        RW: string;
        RE: string;
        BL: string;
        SH: string;
        KN: string;
        LC: string;
        MF: string;
        PM: string;
        VC: string;
        WS: string;
        SM: string;
        SA: string;
        SN: string;
        RS: string;
        CS: string;
        SC: string;
        SL: string;
        SG: string;
        SK: string;
        SI: string;
        SB: string;
        SO: string;
        ZA: string;
        GS: string;
        KR: string;
        ES: string;
        LK: string;
        SD: string;
        SR: string;
        SJ: string;
        SZ: string;
        SE: string;
        CH: string;
        SY: string;
        ST: string;
        TW: string;
        TJ: string;
        TZ: string;
        TH: string;
        TL: string;
        TG: string;
        TK: string;
        TO: string;
        TT: string;
        TN: string;
        TR: string;
        TM: string;
        TC: string;
        TV: string;
        UM: string;
        PU: string;
        VI: string;
        UG: string;
        UA: string;
        SU: string;
        AE: string;
        GB: string;
        US: string;
        UY: string;
        UZ: string;
        VU: string;
        VA: string;
        VE: string;
        VN: string;
        WK: string;
        WF: string;
        EH: string;
        YE: string;
        ZM: string;
        ZW: string;
        AX: string;
    };
    const ISO3166: ISO3166Mapping;
}
export {};
