export declare namespace Notify {
    namespace Templates {
        enum Prefix {
            WALLET = "wallet",
            TICKET = "ticket",
            RECEIPT = "receipt",
            STAFF = "staff"
        }
        enum Wallet {
            STOREDVALUE_ADDED = "wallet:storedvalue:added"
        }
        const TICKET: {
            reservationAvailable: string;
        };
        namespace Staff {
            enum Kitchen {
                REQUESTED = "kitchenRequested",
                PACKED = "kitchenPacked"
            }
            enum Service {
                REQUEST = "serviceRequest"
            }
        }
    }
}
