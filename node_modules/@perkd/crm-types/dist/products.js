"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Products = void 0;
var Products;
(function (Products) {
    let Kind;
    (function (Kind) {
        Kind["PRODUCT"] = "product";
        Kind["MEMBERSHIP"] = "membership";
        Kind["GIFT_CARD"] = "giftcard";
        Kind["STOREDVALUE"] = "storedvalue";
        Kind["BOOKING"] = "booking";
        Kind["TICKET"] = "ticket";
        Kind["VOUCHER"] = "voucher";
        Kind["WIDGET"] = "widget";
        Kind["NFT"] = "nft";
    })(Kind = Products.Kind || (Products.Kind = {}));
    let Channel;
    (function (Channel) {
        Channel["STORE"] = "store";
        Channel["WEBSITE"] = "website";
        // providers
        Channel["PERKD"] = "perkd";
        Channel["MENU"] = "menu";
        Channel["SHOPIFY"] = "shopify";
        Channel["GRABFOOD"] = "grabfood";
        Channel["GRABMART"] = "grabmart";
        Channel["UBEREATS"] = "ubereats";
        Channel["FOODPANDA"] = "foodpanda";
    })(Channel = Products.Channel || (Products.Channel = {}));
    let Unit;
    (function (Unit) {
        Unit["COUNT"] = "count";
        Unit["KILOGRAM"] = "kg";
        Unit["GRAM"] = "g";
        Unit["MILLILITRE"] = "ml";
        Unit["LITRE"] = "l";
        Unit["POUND"] = "lb";
        Unit["OUNCE"] = "oz";
        // resources
        Unit["MINUTE"] = "minute";
        Unit["HOUR"] = "hour";
        Unit["DAY"] = "day";
    })(Unit = Products.Unit || (Products.Unit = {}));
    let ResourceKind;
    (function (ResourceKind) {
        ResourceKind["EVENT"] = "event";
        ResourceKind["VENUE"] = "venue";
        ResourceKind["TABLE"] = "table";
        ResourceKind["PERSON"] = "person";
    })(ResourceKind = Products.ResourceKind || (Products.ResourceKind = {}));
    let FulfillmentService;
    (function (FulfillmentService) {
        FulfillmentService["NONE"] = "none";
        FulfillmentService["STORE"] = "store";
        FulfillmentService["DIGITAL"] = "digital";
        FulfillmentService["SHOPIFY"] = "shopify";
        FulfillmentService["KITCHEN"] = "kitchen";
        FulfillmentService["VENDING"] = "vending";
    })(FulfillmentService = Products.FulfillmentService || (Products.FulfillmentService = {}));
    let Availability;
    (function (Availability) {
        Availability["ACTIVE"] = "active";
        Availability["SOLDOUT"] = "soldout";
        Availability["HIDE"] = "hide";
        Availability["PREORDER"] = "preorder";
    })(Availability = Products.Availability || (Products.Availability = {}));
    let Policy;
    (function (Policy) {
        Policy["DENY"] = "deny";
        Policy["CONTINUE"] = "continue";
    })(Policy = Products.Policy || (Products.Policy = {}));
    let PriceName;
    (function (PriceName) {
        PriceName["BASE"] = "base";
        PriceName["MIN"] = "min";
        PriceName["MAX"] = "max";
    })(PriceName = Products.PriceName || (Products.PriceName = {}));
    let TagType;
    (function (TagType) {
        TagType["CATEGORY"] = "category";
        TagType["KITCHEN"] = "kitchen";
    })(TagType = Products.TagType || (Products.TagType = {}));
})(Products || (exports.Products = Products = {}));
//# sourceMappingURL=products.js.map