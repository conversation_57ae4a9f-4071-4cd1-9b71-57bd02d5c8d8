"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Permissions = void 0;
var Permissions;
(function (Permissions) {
    let Type;
    (function (Type) {
        Type["SERVICETERMS"] = "serviceTerms";
        Type["PRIVACYPOLICY"] = "privacyPolicy";
        // -- used by CRM --
        Type["MOBILE"] = "mobile";
        Type["EMAIL"] = "email";
        Type["POST"] = "postal";
        Type["VOICE"] = "voice";
        // -- used by Perkd --
        Type["PUSH"] = "push";
        Type["LOCATION"] = "location";
        Type["CAMERA"] = "camera";
        Type["PHOTOS"] = "photos";
        Type["CONTACTS"] = "contacts";
        Type["CALENDAR"] = "calendar";
        Type["HOMEKIT"] = "homeKit";
        Type["HEALTH"] = "health";
        Type["SPEECH_RECOGNITION"] = "speechRecognition";
        Type["BLE_CENTRAL"] = "bleCentral";
        Type["BLE_PERIPHERAL"] = "blePeripheral";
        Type["MICROPHONE"] = "microphone";
        Type["MOTION_FITNESS"] = "motionFitness";
        Type["NOTIFICATIONS"] = "notifications";
    })(Type = Permissions.Type || (Permissions.Type = {}));
    let Status;
    (function (Status) {
        Status["ALLOWED"] = "allowed";
        Status["DENIED"] = "denied";
        Status["BLOCKED"] = "blocked";
        Status["UNKNOWN"] = "unknown";
    })(Status = Permissions.Status || (Permissions.Status = {}));
    let Options;
    (function (Options) {
        Options["DONOTDISTURB"] = "doNotDisturb";
    })(Options = Permissions.Options || (Permissions.Options = {}));
})(Permissions || (exports.Permissions = Permissions = {}));
//# sourceMappingURL=permissions.js.map