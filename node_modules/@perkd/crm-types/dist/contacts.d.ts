import { Geo } from './geo';
export declare namespace Contacts {
    enum Type {
        MOBILE = "mobile",
        HOME = "home",
        WORK = "work",
        OTHERS = "others"
    }
    enum Dates {
        BIRTH = "birth",
        GRADUATE = "graduate",
        MARRIED = "married",
        BAPTISED = "baptised",
        CONTRACT_START = "contractstart",
        CONTRACT_END = "contractend"
    }
    enum UrlKind {
        WEBSITE = "website",
        EMAIL = "email",
        SOCIAL = "social",
        CUSTOM = "custom"
    }
    type Phone = {
        id?: string;
        type: Type;
        fullNumber: string;
        countryCode: string;
        number: string;
        regionCode?: string;
        areaCode?: string;
        lineType?: string;
        carrier?: string;
        geo?: Geo.Geometry;
        optIn?: boolean;
        valid?: boolean;
    };
    type Address = {
        id?: string;
        type: Omit<Type, 'mobile'>;
        unit: string;
        level: string;
        house: string;
        street: string;
        city: string;
        state?: string;
        postCode?: string;
        country: string;
        short?: string;
        formatted?: string;
        geo?: Geo.Geometry;
        optIn?: boolean;
        valid?: boolean;
    };
    type Email = {
        id?: string;
        type: Omit<Type, 'mobile'>;
        address: string;
        optIn?: boolean;
        valid?: boolean;
    };
    type Locale = {
        languages: string[];
        currency: string;
        country: string;
        timeZone?: string;
        regions?: string[];
        useMetric?: boolean;
        temperature?: 'celsius' | 'fahrenheit';
    };
}
