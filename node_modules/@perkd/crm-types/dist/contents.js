"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Applets = exports.Fonts = exports.Messages = exports.Microsites = exports.Emails = exports.Contents = void 0;
var Contents;
(function (Contents) {
    let State;
    (function (State) {
        State["DRAFT"] = "draft";
        State["INCOMPLETE"] = "incomplete";
        State["COMPLETE"] = "complete";
        State["ARCHIVED"] = "archived";
    })(State = Contents.State || (Contents.State = {}));
    let Resource;
    (function (Resource) {
        Resource["MICROSITE"] = "microsite";
        Resource["EMAIL"] = "email";
        Resource["APPLET"] = "applet";
        Resource["IMAGE"] = "image";
        Resource["FONT"] = "font";
        Resource["ATHENA"] = "athena";
        Resource["OTHERS"] = "others";
    })(Resource = Contents.Resource || (Contents.Resource = {}));
    let Theme;
    (function (Theme) {
        Theme["LIGHT"] = "light";
        Theme["DARK"] = "dark";
        Theme["DEFAULT"] = "light";
    })(Theme = Contents.Theme || (Contents.Theme = {}));
    let MediaType;
    (function (MediaType) {
        // form/body
        MediaType["JSON_DATA"] = "application/json";
        MediaType["FORM_DATA"] = "multipart/form-data";
        MediaType["FORM_URLENCODED"] = "application/x-www-form-urlencoded";
        // text
        MediaType["HTML"] = "text/html";
        MediaType["CSV"] = "text/csv";
        MediaType["TEXT"] = "text/plain";
        MediaType["JAVASCRIPT"] = "text/javascript";
        MediaType["CALENDAR"] = "text/calendar";
        // image
        MediaType["JPEG"] = "image/jpeg";
        MediaType["PNG"] = "image/png";
        MediaType["GIF"] = "image/gif";
        MediaType["TIFF"] = "image/tiff";
        // video
        MediaType["MPEG"] = "video/mpeg";
        MediaType["MP4"] = "video/mp4";
        // application
        MediaType["PDF"] = "application/pdf";
        MediaType["ZIP"] = "application/zip";
        MediaType["EXCEL"] = "application/vnd.ms-excel";
        // font
        MediaType["TTF"] = "font/ttf";
        MediaType["OTF"] = "font/otf";
        MediaType["SFNT"] = "font/sfnt";
        MediaType["WOFF"] = "font/woff";
        MediaType["WOFF2"] = "font/woff2";
    })(MediaType = Contents.MediaType || (Contents.MediaType = {}));
    Contents.TYPE_FOR_EXTENSION = {
        '.jpg': MediaType.JPEG,
        '.jpeg': MediaType.JPEG,
        '.png': MediaType.PNG,
        '.tiff': MediaType.TIFF,
    };
})(Contents || (exports.Contents = Contents = {}));
var Emails;
(function (Emails) {
    Emails.INDEX_FILE = 'index.html';
    Emails.ContentType = Contents.MediaType.HTML;
})(Emails || (exports.Emails = Emails = {}));
var Microsites;
(function (Microsites) {
    let State;
    (function (State) {
        State["INCOMPLETE"] = "incomplete";
        State["READY"] = "ready";
        State["INUSE"] = "inuse";
        State["ARCHIVED"] = "archived";
    })(State = Microsites.State || (Microsites.State = {}));
    Microsites.PUBLIC_SITES = ['signup'], Microsites.INDEX_FILE = 'index.html', Microsites.EJS_OPTIONS = { delimiter: '%' }, Microsites.PUBLIC = 'public', Microsites.ContentType = Contents.MediaType.HTML, Microsites.CacheControl = 'public, max-age=180';
})(Microsites || (exports.Microsites = Microsites = {}));
var Messages;
(function (Messages) {
    Messages.COVER = 'cover', Messages.BODY = 'body', Messages.COVER_TEMPLATE = 'cover.htm', Messages.BODY_TEMPLATE = 'body.htm', Messages.EJS_OPTIONS = { delimiter: '$' };
})(Messages || (exports.Messages = Messages = {}));
var Fonts;
(function (Fonts) {
    let Source;
    (function (Source) {
        Source["GOOGLE"] = "google";
        Source["CUSTOM"] = "custom";
    })(Source = Fonts.Source || (Fonts.Source = {}));
})(Fonts || (exports.Fonts = Fonts = {}));
var Applets;
(function (Applets) {
    Applets.INDEX_FILE = 'index.html';
    Applets.MIME_TYPE_HTML = Contents.MediaType.HTML;
})(Applets || (exports.Applets = Applets = {}));
//# sourceMappingURL=contents.js.map