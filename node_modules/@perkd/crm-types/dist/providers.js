"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Providers = void 0;
var Providers;
(function (Providers) {
    let Payment;
    (function (Payment) {
        Payment["PERKDPAY"] = "perkdpay";
        Payment["STRIPE"] = "stripe";
        Payment["SHOPIFY"] = "shopify";
        Payment["ADYEN"] = "adyen";
        Payment["MYPAY"] = "mypay";
        Payment["RAZER"] = "razer";
        Payment["GKASH"] = "gkash";
        Payment["FLEXM"] = "flexm";
    })(Payment = Providers.Payment || (Providers.Payment = {}));
    let Order;
    (function (Order) {
        Order["MENU"] = "menu";
        Order["SHOPIFY"] = "shopify";
        Order["GRABFOOD"] = "grabfood";
        Order["GRABMART"] = "grabmart";
        Order["UBEREATS"] = "ubereats";
        Order["FOODPANDA"] = "foodpanda";
        Order["LAZADA"] = "lazada";
        Order["SHOPEE"] = "shopee";
        Order["SALESFORCE"] = "sfcc";
    })(Order = Providers.Order || (Providers.Order = {}));
    let Vending;
    (function (Vending) {
        Vending["ZEAL"] = "zeal";
        Vending["CLOUDRETAIL"] = "cloudretail";
    })(Vending = Providers.Vending || (Providers.Vending = {}));
    Providers.PROVIDER = {
        PERKD: 'perkd',
        ...Payment,
        ...Order,
        ...Vending,
        AWS: 'aws',
        // social
        GOOGLE: 'google',
        FACEBOOK: 'facebook',
        // fulfillment
        LALAMOVE: 'lalamove',
        // print
        FEIE: 'feie',
        // invoice
        EZRECEIPT: 'ezreceipt',
        // pos
        NXIOT: 'nxiot',
        POS: 'pos',
        // wifi
        UNIFI: 'unifi',
    };
    const { PERKDPAY, STRIPE, ADYEN, MYPAY, RAZER, GKASH, FLEXM, EZRECEIPT } = Providers.PROVIDER;
    const { SHOPIFY, GRABFOOD, GRABMART, UBEREATS, FOODPANDA, MENU, FEIE, LALAMOVE } = Providers.PROVIDER;
    Providers.PAYMENT_PROVIDERS = [PERKDPAY, STRIPE, ADYEN, MYPAY, RAZER, GKASH, FLEXM];
    Providers.ORDER_PROVIDERS = [SHOPIFY, GRABFOOD, GRABMART, UBEREATS, FOODPANDA, MENU];
    Providers.FULFILLMENT_PROVIDERS = [LALAMOVE];
    Providers.PRINT_PROVIDERS = [FEIE];
    Providers.INVOICE_PROVIDERS = [EZRECEIPT, MYPAY];
    let Type;
    (function (Type) {
        Type["SHARED"] = "shared";
        Type["TENANTED"] = "tenanted";
        Type["INSTANCE"] = "instance";
    })(Type = Providers.Type || (Providers.Type = {}));
    let Service;
    (function (Service) {
        // commerce
        Service["CUSTOMER"] = "customer";
        Service["ORDER"] = "order";
        Service["FULFILLMENT"] = "fulfillment";
        Service["PRODUCT"] = "product";
        Service["DISCOUNT"] = "discount";
        Service["PLACE"] = "place";
        Service["INVENTORY"] = "inventory";
        // fulfillment
        Service["STORE"] = "store";
        Service["DIGITAL"] = "digital";
        Service["KITCHEN"] = "kitchen";
        Service["SHOPIFY"] = "shopify";
        // payment
        Service["INVOICE"] = "invoice";
        // from Messaging.Service
        Service["SMS"] = "sms";
        Service["EMAIL"] = "email";
        Service["VOICE"] = "voice";
        Service["WHATSAPP"] = "whatsapp";
        Service["NOTIFY"] = "notify";
        Service["RICH"] = "rich";
        Service["OFFER"] = "offer";
        Service["PUSH"] = "push";
        Service["CARD"] = "card";
        // specialised
        Service["PRINT"] = "print";
        Service["STORAGE"] = "storage";
        Service["DASHBOARD"] = "dashboard";
        Service["DATA"] = "data";
        Service["MAP"] = "map";
        Service["CALENDAR"] = "calendar";
        Service["SOCIAL"] = "social";
        Service["WIFI"] = "wifi";
    })(Service = Providers.Service || (Providers.Service = {}));
    let Module;
    (function (Module) {
        // commerce
        Module["CUSTOMERS"] = "customers";
        Module["BUSINESSES"] = "businesses";
        Module["OFFERS"] = "offers";
        Module["DISCOUNTS"] = "discounts";
        Module["ORDERS"] = "orders";
        Module["FULFILLMENTS"] = "fulfillments";
        Module["PRODUCTS"] = "products";
        Module["PLACES"] = "places";
        Module["SMARTCOLLECTIONS"] = "smartcollections";
    })(Module = Providers.Module || (Providers.Module = {}));
})(Providers || (exports.Providers = Providers = {}));
//# sourceMappingURL=providers.js.map