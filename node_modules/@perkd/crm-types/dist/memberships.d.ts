export type Numbering = {
    length: number;
    lastNumber?: number;
    last?: number;
    prefix?: string;
    suffix?: string;
    runoutThreshold?: number;
    reset?: {
        daily: boolean;
        firstNumber: number;
    };
};
export declare namespace Programs {
    enum Type {
        FREE = "free",
        PAID = "paid",
        EARNED = "earned",
        MIXED = "mixed",
        INVITED = "invited",
        GIFT_CARD = "giftcard",
        STAFF = "staff"
    }
    enum State {
        INCOMPLETE = "incomplete",
        READY = "ready",
        ACTIVE = "active",
        STOPPED = "stopped",
        ARCHIVED = "archived"
    }
    const PRESET: {
        SHOPIFY_STAFF_APP: string;
    };
}
export declare namespace Memberships {
    export enum State {
        PENDING = "pending",
        ACTIVE = "active",
        JOINED = "joined",
        RENEWED = "renewed",
        EXPIRED = "expired",
        UPGRADED = "upgraded",
        DOWNGRADED = "downgraded",
        TIERCHANGED = "tierchanged",
        SUSPENDED = "suspended",
        CANCELLED = "cancelled",
        TERMINATED = "terminated",
        TRANSFERRED = "transferred",
        BLACKLISTED = "blacklisted",
        EXTENDED = "extended"
    }
    export enum QualifyMethod {
        MANUAL = "manual",
        COMPLIMENTARY = "complimentary"
    }
    export enum Qualifier {
        JOIN = "join",
        RENEW = "renew",
        UPGRADE = "upgrade",
        DOWNGRADE = "downgrade",
        TIERCHANGE = "tierchange",
        EXTEND = "extend",
        TERMINATE = "terminate",
        TRANSFER = "transfer",
        SHARE = "share"
    }
    export enum QualifiedStates {
        join = "joined",
        renew = "renewed",
        upgrade = "upgraded",
        downgrade = "downgraded",
        tierchange = "tierchanged",
        extend = "extended"
    }
    type DigitalCard = {
        id: string;
        registeredAt: Date;
    };
    export type Tier = {
        level: number;
        name: string;
    };
    export type Membership = {
        id?: string;
        cardNumber: string;
        startTime: Date;
        endTime: Date;
        state: string;
        digitalCard: DigitalCard;
        tierLevel: number;
        program: {
            shortName: string;
            tierList: Tier[];
        };
    };
    export {};
}
