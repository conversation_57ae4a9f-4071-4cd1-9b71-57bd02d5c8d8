import { Users } from './users';
import { Contacts } from './contacts';
import { Numbering } from './memberships';
export declare namespace Settings {
    enum Name {
        LOCALE = "locale",
        EMAIL = "email",
        SMS = "sms",
        PROVIDER = "provider",
        ORDER = "order",
        PAYMENT = "payment",
        FULFILLMENT = "fulfillment",
        BOOKING = "booking",
        QUEUING = "queuing",
        STAFF = "staff",
        PRINT = "print",
        ACTION = "action",
        DASHBOARD = "dashboard",
        MARKETPLACE = "marketplace"
    }
    const LOCALE: Name, ORDER: Name, PAYMENT: Name, FULFILLMENT: Name, BOOKING: Name, QUEUING: Name, STAFF: Name, PRINT: Name, MARKETPLACE: Name;
    type Locale = Contacts.Locale;
    type Email = {
        from: {
            email: string;
            name: string;
        };
        replyTo: {
            email: string;
            name: string;
        };
        enableEdit: boolean;
    };
    type SMS = {
        sender: {
            language: string;
            label: string;
        }[];
    };
    type Provider = {
        [name: string]: boolean;
    };
    type Dashboard = {};
    type Order = {
        numbering?: Numbering;
        noInvite?: boolean;
        nonMemberFirstSale?: boolean;
        webMenu?: boolean;
        sendReceipt?: boolean;
        receiptTemplateId?: string;
        receiptTTL?: number;
        noReceiptNumber?: boolean;
        printReceiptOnPay?: boolean;
        handleInvoice?: boolean;
        invoiceTemplate?: string;
        invoiceHeader?: string;
        invoiceFooter?: string;
        physicalReceiptTemplate?: string;
    };
    type Fulfillment = {
        store?: {
            enabled: boolean;
            ticketTemplate?: string;
        };
        dinein?: {
            enabled: boolean;
            ticketTemplate?: string;
        };
        deliver?: {
            enabled: boolean;
            fulfillFrom: string;
            manualCollect: boolean;
            pickupBufferTime?: number;
            ticketTemplate?: string;
            ticketHeader?: string;
            ticketFooter?: string;
        };
        pickup?: {
            enabled: boolean;
            ticketTemplate?: string;
        };
        kitchen?: {
            enabled: boolean;
            maxScheduleWindow?: number;
            prepareLookahead?: number;
            minTimer?: number;
            busyWaitTime?: number;
            maxShelfLife?: number;
            defaultItemPrepTime?: number;
            batchMultiplier?: number;
            minFirstEtaTime?: number;
            printTicket?: boolean;
            ticketTemplate?: string;
            printSubtickets?: string[];
            itemSubticket?: string[];
            subticketTemplate?: string;
            capacity?: number;
            autoPrepare?: boolean;
            autoPack?: boolean;
        };
        vending?: {
            enabled: boolean;
            pickupOfferMasterId?: string;
        };
    };
    type Booking = {
        calendar?: {
            enabled: boolean;
            user: string;
            deleteWithResource: boolean;
        };
        table?: {
            enabled: boolean;
            minSize: number;
            maxSize: number;
            leadTime: number;
            maxAdvanceBooking: number;
            minPartySize: number;
            maxPartySize: number;
            maxCombined: number;
            minDuration: number;
            maxDuration: number;
            minCapacity: number;
            maxCapacity: number;
        };
    };
    type Queuing = {
        table?: {
            enabled: boolean;
            holdTime: number;
            validity: number;
            duration: {
                default: number;
                peak: number;
                offPeak: number;
            };
            priority: {
                asap: number;
                vip: number;
                regular: number;
                waitingTime: {
                    factor: number;
                    maxBonus: number;
                };
            };
        };
    };
    type WidgetConfig = {
        key: string;
        param?: any;
        applet?: string;
        startTime?: Date;
        endTime?: Date;
    };
    type Staff = {
        widgets?: {
            [role: string]: WidgetConfig[];
        };
        templates?: {
            [key: string]: {
                name: string;
                widget?: string;
                roles?: string[];
            };
        };
        checkin?: {
            ttl: number;
        };
    };
    type Payment = {
        multiMerchant?: boolean;
        merchantId?: string;
    };
    type Print = {
        provider: string;
        initPage?: string;
    };
    enum ActionKey {
        SIGN_UP = "signup",
        STAFF_CHECKIN = "staffcheckin",
        VENDING_CHECKIN = "vendingcheckin"
    }
    type Action = {
        [actionkey: string]: string;
    };
    type Marketplace = {
        ownerProgram?: {
            id: string;
            tierLevel: number;
        };
    };
    const DEFAULT: {
        TIMEZONE: string;
        ORDER: {
            numbering: {
                length: number;
                last: number;
                lastNumber: number;
                reset: {
                    daily: boolean;
                    firstNumber: number;
                };
            };
        };
        KITCHEN: {
            orderTicketTemplate: string;
            prepareLookahead: number;
            maxScheduleWindow: number;
            maxShelfLife: number;
            minFirstEtaTime: number;
            defaultItemPrepTime: number;
            capacity: number;
        };
        STAFF: {
            TEMPLATES: {
                kitchenRequested: {
                    name: string;
                    widget: string;
                    roles: Users.Roles[];
                };
                kitchenPacked: {
                    name: string;
                    widget: string;
                    roles: Users.Roles[];
                };
                serviceRequest: {
                    name: string;
                    widget: string;
                    roles: Users.Roles[];
                };
            };
            CHECKIN: {
                ttl: number;
            };
        };
        PRINT: {
            provider: string;
        };
        BOOKING: {
            table: {
                minSize: number;
                maxSize: number;
                leadTime: number;
                maxAdvanceBooking: number;
                minPartySize: number;
                maxPartySize: number;
                maxCombined: number;
                minDuration: number;
                maxDuration: number;
                minCapacity: number;
                maxCapacity: number;
            };
        };
        QUEUING: {
            table: {
                holdTime: number;
                validity: number;
                duration: {
                    default: number;
                    peak: number;
                    offPeak: number;
                };
                priority: {
                    asap: number;
                    vip: number;
                    regular: number;
                    waitingTime: {
                        factor: number;
                        maxBonus: number;
                    };
                };
            };
        };
    };
}
