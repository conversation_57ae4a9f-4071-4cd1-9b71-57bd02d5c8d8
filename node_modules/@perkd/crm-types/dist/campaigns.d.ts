export declare namespace Campaigns {
    enum State {
        INCOMPLETE = "incomplete",
        READY = "ready",
        SCHEDULED = "scheduled",
        RUNNING = "running",
        SUSPENDED = "suspended",
        PAUSED = "paused",
        FAILED = "failed",
        ENDED = "ended",
        ERROR = "error"
    }
    enum Status {
        SCHEDULED = "scheduled",
        CANCELLED = "cancelled",
        STARTED = "started",
        PAUSED = "paused",
        RESUMED = "resumed",
        ENDED = "ended",
        RESETTED = "resetted"
    }
    enum HandlerType {
        TIMER = "timer",
        EVENT = "event",
        REQUEST = "request"
    }
}
