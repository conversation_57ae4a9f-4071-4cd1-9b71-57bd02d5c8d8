export declare namespace Geo {
    enum GeoType {
        POINT = "Point",
        MULTI_POINT = "MultiPoint",
        LINE_STRING = "LineString",
        MULTI_LINE_STRING = "MultiLineString",
        POLYGON = "Polygon",
        MULTI_POLYGON = "MultiPolygon",
        GEOMETRY_COLLECTION = "GeometryCollection"
    }
    type Geometry = {
        id?: string;
        type: GeoType;
        coordinates: any[];
    };
}
