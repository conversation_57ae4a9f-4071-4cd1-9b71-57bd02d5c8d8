"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Touchpoints = void 0;
const places_1 = require("./places");
var Touchpoints;
(function (Touchpoints) {
    let Type;
    (function (Type) {
        Type["STORE"] = "store";
        Type["ONLINE"] = "online";
        Type["WEBSITE"] = "website";
        Type["SOCIAL"] = "social";
        Type["EMAIL"] = "email";
        Type["SMS"] = "sms";
        Type["MIRCOSITE"] = "microsite";
        Type["ADVERTISEMENT"] = "advertisement";
        Type["QRCODE"] = "qrcode";
        Type["NFC"] = "nfc";
        Type["PERKD"] = "perkd";
        Type["POS"] = "pos";
        Type["APP"] = "app";
        Type["CRM"] = "crm";
        Type["X"] = "x";
        Type["ECOMMERCE"] = "ecommerce";
        Type["UNKNOWN"] = "unknown";
    })(Type = Touchpoints.Type || (Touchpoints.Type = {}));
    let Attributed;
    (function (Attributed) {
        Attributed["SYSTEM"] = "system";
        Attributed["STAFF"] = "staff";
        Attributed["USER"] = "user";
        Attributed["CAMPAIGN"] = "campaign";
        Attributed["BOOKING"] = "booking";
        Attributed["FACEBOOK"] = "facebook";
        Attributed["INSTAGRAM"] = "instagram";
        Attributed["WECHAT"] = "wechat";
        Attributed["WEBSITE"] = "website";
        Attributed["MICROSITE"] = "microsite";
        Attributed["LANDINGPAGE"] = "landingpage";
        Attributed["PARTNER"] = "partner";
        Attributed["LOCALMEDIA"] = "localmedia";
        Attributed["KOL"] = "kol";
    })(Attributed = Touchpoints.Attributed || (Touchpoints.Attributed = {}));
    let Format;
    (function (Format) {
        Format["SHARE"] = "share";
        Format["STAFF_CARD"] = "staffcard";
        Format["VENDING_MACHINE"] = "vending";
        Format["KIOSK"] = "kiosk";
        Format["TERMINAL"] = "terminal";
        // wallet app specific
        Format["SHOP"] = "shop";
        Format["BAG"] = "bag";
        Format["SCAN"] = "scan";
        Format["MESSAGE"] = "message";
        Format["LIBRARY"] = "lib";
        Format["APPLINK"] = "applink";
        Format["WIDGET"] = "widget";
        // external
        Format["SOCIAL"] = "social";
        Format["NEWSPAPER"] = "newspaper";
        Format["MAGAZINE"] = "magazine";
    })(Format = Touchpoints.Format || (Touchpoints.Format = {}));
})(Touchpoints || (exports.Touchpoints = Touchpoints = {}));
//# sourceMappingURL=touchpoints.js.map