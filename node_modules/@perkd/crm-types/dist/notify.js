"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notify = void 0;
var Notify;
(function (Notify) {
    let Templates;
    (function (Templates) {
        let Prefix;
        (function (Prefix) {
            Prefix["WALLET"] = "wallet";
            Prefix["TICKET"] = "ticket";
            Prefix["RECEIPT"] = "receipt";
            Prefix["STAFF"] = "staff";
        })(Prefix = Templates.Prefix || (Templates.Prefix = {}));
        // payment
        let Wallet;
        (function (Wallet) {
            Wallet["STOREDVALUE_ADDED"] = "wallet:storedvalue:added";
        })(Wallet = Templates.Wallet || (Templates.Wallet = {}));
        Templates.TICKET = {
            reservationAvailable: 'ticket:reservation:available',
        };
        let Staff;
        (function (Staff) {
            let Kitchen;
            (function (Kitchen) {
                Kitchen["REQUESTED"] = "kitchenRequested";
                Kitchen["PACKED"] = "kitchenPacked";
            })(Kitchen = Staff.Kitchen || (Staff.Kitchen = {}));
            let Service;
            (function (Service) {
                Service["REQUEST"] = "serviceRequest";
            })(Service = Staff.Service || (Staff.Service = {}));
        })(Staff = Templates.Staff || (Templates.Staff = {}));
    })(Templates = Notify.Templates || (Notify.Templates = {}));
})(Notify || (exports.Notify = Notify = {}));
//# sourceMappingURL=notify.js.map