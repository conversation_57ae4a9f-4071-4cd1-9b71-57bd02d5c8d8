export declare namespace RewardMasters {
    enum Policies {
        ISSUE = "issue",
        WITHDRAW = "withdraw",
        EXTEND = "extend",
        ISSUE_STAMPS = "issuestamps",
        DEDUCT_STAMPS = "deductstamps",
        CARRYOVER_STAMPS = "carryoverstamps"
    }
    enum State {
        INCOMPLETE = "incomplete",
        READY = "ready",// after setup completion
        ACTIVE = "active",// base on reward master active start date
        EXPIRED = "expired",// base on reward master active end date
        ARCHIVED = "archived",// base on reward master achive date or account settings
        UNCOMMIT = "uncommit"
    }
    const QUALIFIERS: {
        SET: Policies[];
        STAMPS: Policies[];
    };
}
export declare namespace Rewards {
    enum State {
        PENDING = "pending",
        ACTIVE = "active",
        WITHDRAWN = "withdrawn",
        CANCELLED = "cancelled",
        EXPIRED = "expired"
    }
    enum TransactionType {
        ISSUE = "issue",
        DEDUCT = "deduct",
        CARRYOVER = "carryover"
    }
}
