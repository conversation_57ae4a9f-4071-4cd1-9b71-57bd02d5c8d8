import { Payments } from './payments';
export declare namespace Billings {
    enum Rate {
        STANDARD = "standard",
        FREE = "free",
        CUSTOM = "custom"
    }
    type Currency = {
        code: string;
        precision: number;
        symbol?: string;
    };
    type Tax = {
        rate: number;
        title?: string;
        price?: number;
    };
    type Billing = {
        id?: string;
        paymentMethod?: {
            method: Payments.Method;
            processor?: Payments.Processor;
            brand?: Payments.Brand;
            wallet?: Payments.Wallet;
            paymentPackageId?: string;
            paymentSettingsKey?: string;
            transactionId?: string;
            amount?: number;
            status?: Payments.Status;
            mode?: Payments.Mode;
            riskLevel?: Payments.RiskLevel;
            intent?: Payments.Intent;
            transactions?: Payments.Transaction[];
            redirectUrl?: string;
            items?: {
                id?: number;
                productId?: string;
                variantId?: string;
                quantity?: string;
            }[];
            metadata?: object;
            createdAt?: Date;
        };
        invoice?: {
            shipping?: number;
            subtotal?: number;
            taxes?: number;
            discounts?: number;
            total?: number;
        };
        currency?: {
            userCurrency?: string;
            exchangeRate?: number;
        };
        address?: {
            fullName?: string;
            country?: string;
            state?: string;
            city?: string;
            postCode?: string;
            formatted?: string;
            phoneNumber?: string;
            isShippingDefault?: boolean;
            isBillingDefault?: boolean;
            isCommercial?: boolean;
            failedValidation?: boolean;
        };
    };
}
