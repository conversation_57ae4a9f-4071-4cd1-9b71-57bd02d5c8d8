export declare namespace Triggers {
    enum Type {
        TIMER = "timer",
        EVENT = "event",
        REQUEST = "request"
    }
    enum State {
        SCHEDULED = "scheduled",
        RUNNING = "running",
        PAUSED = "paused",
        COMPLETED = "completed",
        FAILED = "failed",
        ENDED = "ended"
    }
    type Callback = {
        id: string;
        name: string;
        method: string;
        url: string;
        headers?: any;
        payload?: any;
        rate?: {
            limit: number;
            interval: number;
        };
        accessToken?: string;
    };
    type Trigger = {
        name: string;
        description: string;
        type: Type;
        callback: Callback;
        priority?: number;
        start?: Date;
        end?: Date;
        persistAfterEnd?: boolean;
        events?: string[];
        condition?: any;
        repeatInterval?: string | number;
        repeat?: number;
        repeatRemaining?: number;
        timezone?: string;
        state: State;
        active: boolean;
        jobId: string;
        nextRunAt?: Date;
        lastRunAt?: Date;
        lastFinishedAt?: Date;
        failedAt?: Date;
        failReason?: any;
        visible?: boolean;
    };
}
