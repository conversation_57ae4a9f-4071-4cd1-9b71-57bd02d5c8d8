import { type PropType } from 'vue';
import { type CurrencyInputOptions } from 'vue-currency-input';
declare function onFocus(): void;
declare function onBlur(): void;
declare function checkValidity(): boolean;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    currencyConfig: {
        type: PropType<CurrencyInputOptions>;
    };
}>, {
    value: string;
    formattedValue: import("vue").ComputedRef<any>;
    focus: typeof onFocus;
    blur: typeof onBlur;
    checkValidity: typeof checkValidity;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    focusChange: (...args: any[]) => void;
    clearInput: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    currencyConfig: {
        type: PropType<CurrencyInputOptions>;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    type: string;
    disabled: boolean;
    required: boolean;
    value: string;
    placeholder: string;
    min: string;
    max: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
