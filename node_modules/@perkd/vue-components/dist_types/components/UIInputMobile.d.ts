declare function onFocus(): void;
declare function onBlur(): void;
declare function checkValidity(): boolean;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    showDialCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    dropdownOptions: {
        type: ObjectConstructor;
    };
    defaultCountry: {
        type: StringConstructor;
        default: string;
    };
    preferredCountries: {
        type: {
            (arrayLength: number): String[][];
            (...items: String[][]): String[][];
            new (arrayLength: number): String[][];
            new (...items: String[][]): String[][];
            isArray(arg: any): arg is any[];
            readonly prototype: any[];
            from<T>(arrayLike: ArrayLike<T>): T[];
            from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
            from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            of<T>(...items: T[]): T[];
            readonly [Symbol.species]: ArrayConstructor;
        };
        default: string[];
    };
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").Ref<string, string>;
    countryCode: import("vue").Ref<string, string>;
    focus: typeof onFocus;
    blur: typeof onBlur;
    checkValidity: typeof checkValidity;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    focusChange: (...args: any[]) => void;
    clearInput: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    showDialCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    dropdownOptions: {
        type: ObjectConstructor;
    };
    defaultCountry: {
        type: StringConstructor;
        default: string;
    };
    preferredCountries: {
        type: {
            (arrayLength: number): String[][];
            (...items: String[][]): String[][];
            new (arrayLength: number): String[][];
            new (...items: String[][]): String[][];
            isArray(arg: any): arg is any[];
            readonly prototype: any[];
            from<T>(arrayLike: ArrayLike<T>): T[];
            from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
            from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            of<T>(...items: T[]): T[];
            readonly [Symbol.species]: ArrayConstructor;
        };
        default: string[];
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    type: string;
    pattern: string;
    disabled: boolean;
    required: boolean;
    readonly: boolean;
    value: string;
    autocomplete: string;
    placeholder: string;
    showDialCode: boolean;
    defaultCountry: string;
    preferredCountries: String[][];
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
