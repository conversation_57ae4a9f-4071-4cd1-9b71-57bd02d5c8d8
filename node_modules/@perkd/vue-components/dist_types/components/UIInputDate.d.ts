declare function onFocus(): void;
declare function onBlur(): void;
declare function checkValidity(): boolean;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    defaultDate: {
        type: StringConstructor;
        default: string;
    };
    timeStamp: {
        type: () => "start" | "end" | "";
        default: string;
    };
    smartFormat: {
        type: BooleanConstructor;
        default: boolean;
    };
    openDateTimePicker: {
        type: FunctionConstructor;
        required: true;
    };
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").ComputedRef<string>;
    focus: typeof onFocus;
    blur: typeof onBlur;
    checkValidity: typeof checkValidity;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    showError: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    defaultDate: {
        type: StringConstructor;
        default: string;
    };
    timeStamp: {
        type: () => "start" | "end" | "";
        default: string;
    };
    smartFormat: {
        type: BooleanConstructor;
        default: boolean;
    };
    openDateTimePicker: {
        type: FunctionConstructor;
        required: true;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onShowError?: ((...args: any[]) => any) | undefined;
}>, {
    type: string;
    value: string;
    min: string;
    max: string;
    defaultDate: string;
    timeStamp: "" | "start" | "end";
    smartFormat: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
