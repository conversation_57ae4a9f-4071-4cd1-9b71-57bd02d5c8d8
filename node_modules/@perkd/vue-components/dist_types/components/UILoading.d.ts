declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    colorBackground: {
        type: BooleanConstructor;
        default: boolean;
    };
    success: {
        type: BooleanConstructor;
        default: undefined;
    };
    thickness: {
        type: StringConstructor;
        default: string;
    };
    size: {
        type: StringConstructor;
        default: string;
    };
    color: {
        type: StringConstructor;
        default: string;
    };
    emptyColor: {
        type: StringConstructor;
        default: string;
    };
    successColor: {
        type: StringConstructor;
        default: string;
    };
    failedColor: {
        type: StringConstructor;
        default: string;
    };
    text: {
        type: StringConstructor;
        default: string;
    };
}>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    colorBackground: {
        type: BooleanConstructor;
        default: boolean;
    };
    success: {
        type: BooleanConstructor;
        default: undefined;
    };
    thickness: {
        type: StringConstructor;
        default: string;
    };
    size: {
        type: StringConstructor;
        default: string;
    };
    color: {
        type: StringConstructor;
        default: string;
    };
    emptyColor: {
        type: StringConstructor;
        default: string;
    };
    successColor: {
        type: StringConstructor;
        default: string;
    };
    failedColor: {
        type: StringConstructor;
        default: string;
    };
    text: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    color: string;
    success: boolean;
    text: string;
    colorBackground: boolean;
    thickness: string;
    size: string;
    emptyColor: string;
    successColor: string;
    failedColor: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
