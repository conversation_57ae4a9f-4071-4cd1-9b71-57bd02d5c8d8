import { type PropType } from 'vue';
import Icon from '@/components/UIIcon.vue';
import { type Applets } from '@perkd/applet-common/types/applets';
declare const label: import("vue").Ref<string, string>;
declare const isFocused: import("vue").Ref<boolean, boolean>;
declare const isValid: import("vue").Ref<boolean, boolean>;
declare const errorShowed: import("vue").Ref<number, number>;
declare const isActive: import("vue").ComputedRef<boolean | "">;
declare function onFocus(event: FocusEvent): void;
declare function onBlur(event: FocusEvent): void;
declare function onInput(value: any): void;
declare function inputCheck(valid: boolean, message: string): void;
/**
 * validationMessage format
 *  a. is_required
 *  b. before_minimum_date|date:20 Jan 2023 (errorkey|params, params: key:value)
 * when use as error key add prefix: error.
 * become: error.is_required, error.before_minimum_date
 */
declare function getErrorMessage(): string;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_4: {
    required: boolean;
    disabled: boolean;
    readonly: boolean;
    onInputCheck: typeof inputCheck;
    onInputChange: typeof onInput;
    onInputBlur: typeof onBlur;
    onInputFocus: typeof onFocus;
};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    default?: (props: typeof __VLS_4) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    icon: {
        type: PropType<Applets.Icon>;
        required: false;
    };
    label: {
        type: StringConstructor;
        default: string;
    };
    labelClass: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    errorMessages: {
        type: () => {
            [key: string]: string;
        };
    };
    showError: {
        type: NumberConstructor;
        default: number;
    };
}>, {
    Icon: typeof Icon;
    label: typeof label;
    isFocused: typeof isFocused;
    isValid: typeof isValid;
    errorShowed: typeof errorShowed;
    isActive: typeof isActive;
    onFocus: typeof onFocus;
    onBlur: typeof onBlur;
    onInput: typeof onInput;
    inputCheck: typeof inputCheck;
    getErrorMessage: typeof getErrorMessage;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    focusChange: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    icon: {
        type: PropType<Applets.Icon>;
        required: false;
    };
    label: {
        type: StringConstructor;
        default: string;
    };
    labelClass: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    errorMessages: {
        type: () => {
            [key: string]: string;
        };
    };
    showError: {
        type: NumberConstructor;
        default: number;
    };
}>> & Readonly<{
    onFocusChange?: ((...args: any[]) => any) | undefined;
}>, {
    label: string;
    disabled: boolean;
    required: boolean;
    labelClass: string;
    readonly: boolean;
    showError: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    icon: {
        type: PropType<Applets.Icon>;
        required: false;
    };
    label: {
        type: StringConstructor;
        default: string;
    };
    labelClass: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    errorMessages: {
        type: () => {
            [key: string]: string;
        };
    };
    showError: {
        type: NumberConstructor;
        default: number;
    };
}>, {
    onFocus: typeof onFocus;
    onBlur: typeof onBlur;
    onInput: typeof onInput;
    inputCheck: typeof inputCheck;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    focusChange: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    icon: {
        type: PropType<Applets.Icon>;
        required: false;
    };
    label: {
        type: StringConstructor;
        default: string;
    };
    labelClass: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    errorMessages: {
        type: () => {
            [key: string]: string;
        };
    };
    showError: {
        type: NumberConstructor;
        default: number;
    };
}>> & Readonly<{
    onFocusChange?: ((...args: any[]) => any) | undefined;
}>, {
    label: string;
    disabled: boolean;
    required: boolean;
    labelClass: string;
    readonly: boolean;
    showError: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
