import { type PropType } from 'vue';
import Icon from '@/components/UIIcon.vue';
import Loading from '@/components/UILoading.vue';
interface ICON {
    name: string;
    type?: string;
    position?: string;
    class?: string;
}
declare const disabled: import("vue").Ref<boolean, boolean>;
declare const slideButtonRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
declare const slideThumbRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
declare const thumbPosition: import("vue").Ref<number, number>;
declare const isSliding: import("vue").Ref<boolean, boolean>;
declare const proceeding: import("vue").Ref<boolean, boolean>;
declare const textColor: import("vue").ComputedRef<string>;
declare function onTouchStart(event: TouchEvent): void;
declare function onTouchMove(event: TouchEvent): void;
declare function onTouchEnd(): void;
declare function onTouchCancel(): void;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_7: {};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    status?: (props: typeof __VLS_7) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    color: {
        type: () => "primary" | "accent" | "success" | "warning" | "error";
        default: string;
    };
    icon: {
        type: PropType<ICON>;
        required: false;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    subtitle: StringConstructor;
    subtitleClass: {
        type: StringConstructor;
        default: string;
    };
    startColor: {
        type: StringConstructor;
        default: string;
    };
    endColor: {
        type: StringConstructor;
        default: string;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    proceeding: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    Icon: typeof Icon;
    Loading: typeof Loading;
    disabled: typeof disabled;
    slideButtonRef: typeof slideButtonRef;
    slideThumbRef: typeof slideThumbRef;
    thumbPosition: typeof thumbPosition;
    isSliding: typeof isSliding;
    proceeding: typeof proceeding;
    textColor: typeof textColor;
    onTouchStart: typeof onTouchStart;
    onTouchMove: typeof onTouchMove;
    onTouchEnd: typeof onTouchEnd;
    onTouchCancel: typeof onTouchCancel;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    confirmClick: (...args: any[]) => void;
    qualifyClick: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    color: {
        type: () => "primary" | "accent" | "success" | "warning" | "error";
        default: string;
    };
    icon: {
        type: PropType<ICON>;
        required: false;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    subtitle: StringConstructor;
    subtitleClass: {
        type: StringConstructor;
        default: string;
    };
    startColor: {
        type: StringConstructor;
        default: string;
    };
    endColor: {
        type: StringConstructor;
        default: string;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    proceeding: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onConfirmClick?: ((...args: any[]) => any) | undefined;
    onQualifyClick?: ((...args: any[]) => any) | undefined;
}>, {
    color: "primary" | "accent" | "success" | "warning" | "error";
    titleClass: string;
    subtitleClass: string;
    disabled: boolean;
    startColor: string;
    endColor: string;
    threshold: number;
    proceeding: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    color: {
        type: () => "primary" | "accent" | "success" | "warning" | "error";
        default: string;
    };
    icon: {
        type: PropType<ICON>;
        required: false;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    subtitle: StringConstructor;
    subtitleClass: {
        type: StringConstructor;
        default: string;
    };
    startColor: {
        type: StringConstructor;
        default: string;
    };
    endColor: {
        type: StringConstructor;
        default: string;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    proceeding: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    confirmClick: (...args: any[]) => void;
    qualifyClick: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    color: {
        type: () => "primary" | "accent" | "success" | "warning" | "error";
        default: string;
    };
    icon: {
        type: PropType<ICON>;
        required: false;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    subtitle: StringConstructor;
    subtitleClass: {
        type: StringConstructor;
        default: string;
    };
    startColor: {
        type: StringConstructor;
        default: string;
    };
    endColor: {
        type: StringConstructor;
        default: string;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    proceeding: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onConfirmClick?: ((...args: any[]) => any) | undefined;
    onQualifyClick?: ((...args: any[]) => any) | undefined;
}>, {
    color: "primary" | "accent" | "success" | "warning" | "error";
    titleClass: string;
    subtitleClass: string;
    disabled: boolean;
    startColor: string;
    endColor: string;
    threshold: number;
    proceeding: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
