declare function flipCard(event?: undefined | MouseEvent, clockwise?: boolean): void;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    image: {
        type: StringConstructor;
        require: boolean;
    };
    placeholder: {
        type: StringConstructor;
        require: boolean;
        default: any;
    };
    width: {
        type: NumberConstructor;
        default: number;
    };
    cardNumber: {
        type: StringConstructor;
        default: string;
    };
    barcodeType: {
        type: StringConstructor;
        default: string;
    };
    barcodePatterns: {
        type: StringConstructor;
        default: string;
    };
    startTime: {
        type: StringConstructor;
        default: string;
    };
    endTime: {
        type: StringConstructor;
        default: string;
    };
}>, {
    isFront: import("vue").ComputedRef<boolean>;
    flipCard: typeof flipCard;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    image: {
        type: StringConstructor;
        require: boolean;
    };
    placeholder: {
        type: StringConstructor;
        require: boolean;
        default: any;
    };
    width: {
        type: NumberConstructor;
        default: number;
    };
    cardNumber: {
        type: StringConstructor;
        default: string;
    };
    barcodeType: {
        type: StringConstructor;
        default: string;
    };
    barcodePatterns: {
        type: StringConstructor;
        default: string;
    };
    startTime: {
        type: StringConstructor;
        default: string;
    };
    endTime: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    width: number;
    placeholder: string;
    cardNumber: string;
    barcodeType: string;
    barcodePatterns: string;
    startTime: string;
    endTime: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
