declare const circleProgressRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
declare const currentPercent: import("vue").Ref<number, number>;
declare const shadow: {
    inset: boolean;
    vertical: number;
    horizontal: number;
    blur: number;
    opacity: number;
    color: string;
};
declare const bgShadow: {
    inset: boolean;
    vertical: number;
    horizontal: number;
    blur: number;
    opacity: number;
    color: string;
};
declare const svgAttr: {
    style: {
        transform: string;
        overflow: string;
    };
    xmlns: string;
    viewBox: string;
};
declare const circleBgAttr: {
    filter?: string | undefined;
    cx: number;
    cy: number;
    r: number;
    stroke: string;
    "stroke-width": number;
    fill: string;
};
declare const circleFgAttr: import("vue").ComputedRef<{
    style?: {
        transition: string;
    } | undefined;
    filter?: string | undefined;
    cx: number;
    cy: number;
    r: number;
    fill: string;
    "stroke-width": number;
    "stroke-dasharray": number;
    "stroke-dashoffset": number;
    "stroke-linecap": "inherit" | "round" | "butt" | "square" | undefined;
    stroke: string;
}>;
declare const gradientAttr: {
    id: string;
    x1: string;
    y1: string;
    x2: string;
    y2: string;
    gradientTransform: string;
};
declare const gradientStartAttr: {
    offset: number;
    "stop-color": string;
};
declare const gradientStopAttr: {
    offset: number;
    "stop-color": string;
};
declare const shadowAttr: {
    id: string;
    width: string;
    height: string;
    x: string;
    y: string;
};
declare const bgShadowAttr: {
    id: string;
    width: string;
    height: string;
    x: string;
    y: string;
};
declare const feShadowAttr: {
    dx: number;
    dy: number;
    stdDeviation: number;
    floodColor: string;
    floodOpacity: number;
};
declare const feBgShadowAttr: {
    dx: number;
    dy: number;
    stdDeviation: number;
    floodColor: string;
    floodOpacity: number;
};
declare function updatePercent(): void;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_1: {};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    textContent?: (props: typeof __VLS_1) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    size: {
        type: NumberConstructor;
        default: number;
    };
    borderWidth: {
        type: NumberConstructor;
        default: number;
    };
    borderBgWidth: {
        type: NumberConstructor;
        default: number;
    };
    fillColor: {
        type: StringConstructor;
        default: string;
    };
    emptyColor: {
        type: StringConstructor;
        default: string;
    };
    background: {
        type: StringConstructor;
        default: string;
    };
    percent: {
        type: NumberConstructor;
        default: number;
    };
    linecap: {
        type: () => "inherit" | "round" | "butt" | "square" | undefined;
        default: string;
    };
    transition: {
        type: NumberConstructor;
        default: number;
    };
    isGradient: {
        type: BooleanConstructor;
        default: boolean;
    };
    gradient: {
        type: ObjectConstructor;
    };
    isShadow: {
        type: BooleanConstructor;
        default: boolean;
    };
    shadow: {
        type: ObjectConstructor;
    };
    isBgShadow: {
        type: BooleanConstructor;
        default: boolean;
    };
    bgShadow: {
        type: ObjectConstructor;
    };
    viewport: {
        type: BooleanConstructor;
        default: boolean;
    };
    onViewport: {
        type: FunctionConstructor;
    };
    showPercent: {
        type: BooleanConstructor;
        default: boolean;
    };
    unit: {
        type: StringConstructor;
        default: string;
    };
}>, {
    circleProgressRef: typeof circleProgressRef;
    currentPercent: typeof currentPercent;
    shadow: typeof shadow;
    bgShadow: typeof bgShadow;
    svgAttr: typeof svgAttr;
    circleBgAttr: typeof circleBgAttr;
    circleFgAttr: typeof circleFgAttr;
    gradientAttr: typeof gradientAttr;
    gradientStartAttr: typeof gradientStartAttr;
    gradientStopAttr: typeof gradientStopAttr;
    shadowAttr: typeof shadowAttr;
    bgShadowAttr: typeof bgShadowAttr;
    feShadowAttr: typeof feShadowAttr;
    feBgShadowAttr: typeof feBgShadowAttr;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    size: {
        type: NumberConstructor;
        default: number;
    };
    borderWidth: {
        type: NumberConstructor;
        default: number;
    };
    borderBgWidth: {
        type: NumberConstructor;
        default: number;
    };
    fillColor: {
        type: StringConstructor;
        default: string;
    };
    emptyColor: {
        type: StringConstructor;
        default: string;
    };
    background: {
        type: StringConstructor;
        default: string;
    };
    percent: {
        type: NumberConstructor;
        default: number;
    };
    linecap: {
        type: () => "inherit" | "round" | "butt" | "square" | undefined;
        default: string;
    };
    transition: {
        type: NumberConstructor;
        default: number;
    };
    isGradient: {
        type: BooleanConstructor;
        default: boolean;
    };
    gradient: {
        type: ObjectConstructor;
    };
    isShadow: {
        type: BooleanConstructor;
        default: boolean;
    };
    shadow: {
        type: ObjectConstructor;
    };
    isBgShadow: {
        type: BooleanConstructor;
        default: boolean;
    };
    bgShadow: {
        type: ObjectConstructor;
    };
    viewport: {
        type: BooleanConstructor;
        default: boolean;
    };
    onViewport: {
        type: FunctionConstructor;
    };
    showPercent: {
        type: BooleanConstructor;
        default: boolean;
    };
    unit: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    size: number;
    emptyColor: string;
    transition: number;
    borderWidth: number;
    borderBgWidth: number;
    fillColor: string;
    background: string;
    percent: number;
    linecap: "inherit" | "round" | "butt" | "square" | undefined;
    isGradient: boolean;
    isShadow: boolean;
    isBgShadow: boolean;
    viewport: boolean;
    showPercent: boolean;
    unit: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    size: {
        type: NumberConstructor;
        default: number;
    };
    borderWidth: {
        type: NumberConstructor;
        default: number;
    };
    borderBgWidth: {
        type: NumberConstructor;
        default: number;
    };
    fillColor: {
        type: StringConstructor;
        default: string;
    };
    emptyColor: {
        type: StringConstructor;
        default: string;
    };
    background: {
        type: StringConstructor;
        default: string;
    };
    percent: {
        type: NumberConstructor;
        default: number;
    };
    linecap: {
        type: () => "inherit" | "round" | "butt" | "square" | undefined;
        default: string;
    };
    transition: {
        type: NumberConstructor;
        default: number;
    };
    isGradient: {
        type: BooleanConstructor;
        default: boolean;
    };
    gradient: {
        type: ObjectConstructor;
    };
    isShadow: {
        type: BooleanConstructor;
        default: boolean;
    };
    shadow: {
        type: ObjectConstructor;
    };
    isBgShadow: {
        type: BooleanConstructor;
        default: boolean;
    };
    bgShadow: {
        type: ObjectConstructor;
    };
    viewport: {
        type: BooleanConstructor;
        default: boolean;
    };
    onViewport: {
        type: FunctionConstructor;
    };
    showPercent: {
        type: BooleanConstructor;
        default: boolean;
    };
    unit: {
        type: StringConstructor;
        default: string;
    };
}>, {
    updatePercent: typeof updatePercent;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    size: {
        type: NumberConstructor;
        default: number;
    };
    borderWidth: {
        type: NumberConstructor;
        default: number;
    };
    borderBgWidth: {
        type: NumberConstructor;
        default: number;
    };
    fillColor: {
        type: StringConstructor;
        default: string;
    };
    emptyColor: {
        type: StringConstructor;
        default: string;
    };
    background: {
        type: StringConstructor;
        default: string;
    };
    percent: {
        type: NumberConstructor;
        default: number;
    };
    linecap: {
        type: () => "inherit" | "round" | "butt" | "square" | undefined;
        default: string;
    };
    transition: {
        type: NumberConstructor;
        default: number;
    };
    isGradient: {
        type: BooleanConstructor;
        default: boolean;
    };
    gradient: {
        type: ObjectConstructor;
    };
    isShadow: {
        type: BooleanConstructor;
        default: boolean;
    };
    shadow: {
        type: ObjectConstructor;
    };
    isBgShadow: {
        type: BooleanConstructor;
        default: boolean;
    };
    bgShadow: {
        type: ObjectConstructor;
    };
    viewport: {
        type: BooleanConstructor;
        default: boolean;
    };
    onViewport: {
        type: FunctionConstructor;
    };
    showPercent: {
        type: BooleanConstructor;
        default: boolean;
    };
    unit: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    size: number;
    emptyColor: string;
    transition: number;
    borderWidth: number;
    borderBgWidth: number;
    fillColor: string;
    background: string;
    percent: number;
    linecap: "inherit" | "round" | "butt" | "square" | undefined;
    isGradient: boolean;
    isShadow: boolean;
    isBgShadow: boolean;
    viewport: boolean;
    showPercent: boolean;
    unit: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
