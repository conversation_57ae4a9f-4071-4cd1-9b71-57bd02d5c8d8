import { type PropType } from 'vue';
import NavigationBar from '@/components/UINavigationBar.vue';
import Button from '@/components/UIButton.vue';
import Loading from '@/components/UILoading.vue';
import { type Applets } from '@perkd/applet-common/types/applets';
declare const $slots: Readonly<{
    [name: string]: import("vue").Slot<any> | undefined;
}>;
declare const title: import("vue").Ref<string, string>, showClose: import("vue").Ref<boolean, boolean>, loading: import("vue").Ref<boolean, boolean>;
declare const screenContentRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
declare const keyboardRef: import("vue").Ref<HTMLElement | undefined, HTMLElement | undefined>;
declare const navBack: import("vue").ComputedRef<Applets.Navback | undefined>;
declare const navigationProps: import("vue").ComputedRef<{
    title: string;
    titleClass: string;
    theme: string | undefined;
    isOnline: boolean;
    status: string;
    navBack: Applets.Navback | undefined;
}>;
declare const screenClasses: import("vue").ComputedRef<string[]>;
declare function onInputFocusChange(event: FocusEvent, isFocused: boolean): void;
declare function scrollBy(top?: number, left?: number): void;
declare function scrollToTop(position?: number): void;
declare function scrollToLeft(position?: number): void;
declare function onCloseWindow(): void;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_1: {}, __VLS_17: {}, __VLS_19: {
    focusChange: typeof onInputFocusChange;
}, __VLS_21: {}, __VLS_23: {}, __VLS_29: {}, __VLS_38: {};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    navigationBar?: (props: typeof __VLS_1) => any;
} & {
    notify?: (props: typeof __VLS_17) => any;
} & {
    content?: (props: typeof __VLS_19) => any;
} & {
    footer?: (props: typeof __VLS_21) => any;
} & {
    tabBar?: (props: typeof __VLS_23) => any;
} & {
    loading?: (props: typeof __VLS_29) => any;
} & {
    dialog?: (props: typeof __VLS_38) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    title: {
        type: StringConstructor;
        default: string;
    };
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    navigationBarTheme: StringConstructor;
    disableNavBack: {
        type: BooleanConstructor;
        default: boolean;
    };
    isOnline: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    onContentScroll: {
        type: PropType<(event: Event) => void>;
    };
}>, {
    NavigationBar: typeof NavigationBar;
    Button: typeof Button;
    Loading: typeof Loading;
    $slots: typeof $slots;
    title: typeof title;
    showClose: typeof showClose;
    loading: typeof loading;
    screenContentRef: typeof screenContentRef;
    keyboardRef: typeof keyboardRef;
    navBack: typeof navBack;
    navigationProps: typeof navigationProps;
    screenClasses: typeof screenClasses;
    onInputFocusChange: typeof onInputFocusChange;
    onCloseWindow: typeof onCloseWindow;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    goPreviousPage: (...args: any[]) => void;
    closeWindow: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    title: {
        type: StringConstructor;
        default: string;
    };
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    navigationBarTheme: StringConstructor;
    disableNavBack: {
        type: BooleanConstructor;
        default: boolean;
    };
    isOnline: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    onContentScroll: {
        type: PropType<(event: Event) => void>;
    };
}>> & Readonly<{
    onGoPreviousPage?: ((...args: any[]) => any) | undefined;
    onCloseWindow?: ((...args: any[]) => any) | undefined;
}>, {
    title: string;
    titleClass: string;
    loading: boolean;
    isOnline: boolean;
    status: string;
    disableNavBack: boolean;
    showClose: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    title: {
        type: StringConstructor;
        default: string;
    };
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    navigationBarTheme: StringConstructor;
    disableNavBack: {
        type: BooleanConstructor;
        default: boolean;
    };
    isOnline: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    onContentScroll: {
        type: PropType<(event: Event) => void>;
    };
}>, {
    scrollBy: typeof scrollBy;
    scrollToTop: typeof scrollToTop;
    scrollToLeft: typeof scrollToLeft;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    goPreviousPage: (...args: any[]) => void;
    closeWindow: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    title: {
        type: StringConstructor;
        default: string;
    };
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    navigationBarTheme: StringConstructor;
    disableNavBack: {
        type: BooleanConstructor;
        default: boolean;
    };
    isOnline: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
    onContentScroll: {
        type: PropType<(event: Event) => void>;
    };
}>> & Readonly<{
    onGoPreviousPage?: ((...args: any[]) => any) | undefined;
    onCloseWindow?: ((...args: any[]) => any) | undefined;
}>, {
    title: string;
    titleClass: string;
    loading: boolean;
    isOnline: boolean;
    status: string;
    disableNavBack: boolean;
    showClose: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
