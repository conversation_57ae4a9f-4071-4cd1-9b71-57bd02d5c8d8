import { type PropType } from 'vue';
import Button from '@/components/UIButton.vue';
type ValidateResult = {
    isValid: boolean;
    validationMessage: string;
};
declare const disabled: import("vue").Ref<boolean, boolean>, required: import("vue").Ref<boolean, boolean>, readonly: import("vue").Ref<boolean, boolean>, inputType: import("vue").Ref<string, string>, inputOptions: import("vue").Ref<Record<string, any>, Record<string, any>>;
declare const pattern: import("vue").Ref<string, string>, minlength: import("vue").Ref<string, string>, maxlength: import("vue").Ref<string, string>, min: import("vue").Ref<string, string>, max: import("vue").Ref<string, string>, inputmode: import("vue").Ref<string, string>;
declare const inputValue: import("vue").Ref<string, string>;
declare const inputRef: import("vue").ShallowRef<import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    showDialCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    dropdownOptions: {
        type: ObjectConstructor;
    };
    defaultCountry: {
        type: StringConstructor;
        default: string;
    };
    preferredCountries: {
        type: {
            (arrayLength: number): String[][];
            (...items: String[][]): String[][];
            new (arrayLength: number): String[][];
            new (...items: String[][]): String[][];
            isArray(arg: any): arg is any[];
            readonly prototype: any[];
            from<T>(arrayLike: ArrayLike<T>): T[];
            from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
            from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            of<T>(...items: T[]): T[];
            readonly [Symbol.species]: ArrayConstructor;
        };
        default: string[];
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").Ref<string, string>;
    countryCode: import("vue").Ref<string, string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    focusChange: (...args: any[]) => void;
    clearInput: (...args: any[]) => void;
}, import("vue").PublicProps, {
    type: string;
    pattern: string;
    disabled: boolean;
    required: boolean;
    readonly: boolean;
    value: string;
    autocomplete: string;
    placeholder: string;
    showDialCode: boolean;
    defaultCountry: string;
    preferredCountries: String[][];
}, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
    P: {};
    B: {};
    D: {};
    C: {};
    M: {};
    Defaults: {};
}, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    showDialCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    dropdownOptions: {
        type: ObjectConstructor;
    };
    defaultCountry: {
        type: StringConstructor;
        default: string;
    };
    preferredCountries: {
        type: {
            (arrayLength: number): String[][];
            (...items: String[][]): String[][];
            new (arrayLength: number): String[][];
            new (...items: String[][]): String[][];
            isArray(arg: any): arg is any[];
            readonly prototype: any[];
            from<T>(arrayLike: ArrayLike<T>): T[];
            from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
            from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            of<T>(...items: T[]): T[];
            readonly [Symbol.species]: ArrayConstructor;
        };
        default: string[];
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").Ref<string, string>;
    countryCode: import("vue").Ref<string, string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, {
    type: string;
    pattern: string;
    disabled: boolean;
    required: boolean;
    readonly: boolean;
    value: string;
    autocomplete: string;
    placeholder: string;
    showDialCode: boolean;
    defaultCountry: string;
    preferredCountries: String[][];
}> | HTMLInputElement | import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    currencyConfig: {
        type: PropType<import("vue-currency-input").CurrencyInputOptions>;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    value: string;
    formattedValue: import("vue").ComputedRef<any>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    focusChange: (...args: any[]) => void;
    clearInput: (...args: any[]) => void;
}, import("vue").PublicProps, {
    type: string;
    disabled: boolean;
    required: boolean;
    value: string;
    placeholder: string;
    min: string;
    max: string;
}, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
    P: {};
    B: {};
    D: {};
    C: {};
    M: {};
    Defaults: {};
}, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    currencyConfig: {
        type: PropType<import("vue-currency-input").CurrencyInputOptions>;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    value: string;
    formattedValue: import("vue").ComputedRef<any>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, {
    type: string;
    disabled: boolean;
    required: boolean;
    value: string;
    placeholder: string;
    min: string;
    max: string;
}> | import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    defaultDate: {
        type: StringConstructor;
        default: string;
    };
    timeStamp: {
        type: () => "start" | "end" | "";
        default: string;
    };
    smartFormat: {
        type: BooleanConstructor;
        default: boolean;
    };
    openDateTimePicker: {
        type: FunctionConstructor;
        required: true;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onShowError?: ((...args: any[]) => any) | undefined;
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").ComputedRef<string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    showError: (...args: any[]) => void;
}, import("vue").PublicProps, {
    type: string;
    value: string;
    min: string;
    max: string;
    defaultDate: string;
    timeStamp: "" | "start" | "end";
    smartFormat: boolean;
}, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
    P: {};
    B: {};
    D: {};
    C: {};
    M: {};
    Defaults: {};
}, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    defaultDate: {
        type: StringConstructor;
        default: string;
    };
    timeStamp: {
        type: () => "start" | "end" | "";
        default: string;
    };
    smartFormat: {
        type: BooleanConstructor;
        default: boolean;
    };
    openDateTimePicker: {
        type: FunctionConstructor;
        required: true;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onShowError?: ((...args: any[]) => any) | undefined;
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").ComputedRef<string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, {
    type: string;
    value: string;
    min: string;
    max: string;
    defaultDate: string;
    timeStamp: "" | "start" | "end";
    smartFormat: boolean;
}> | undefined, import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    showDialCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    dropdownOptions: {
        type: ObjectConstructor;
    };
    defaultCountry: {
        type: StringConstructor;
        default: string;
    };
    preferredCountries: {
        type: {
            (arrayLength: number): String[][];
            (...items: String[][]): String[][];
            new (arrayLength: number): String[][];
            new (...items: String[][]): String[][];
            isArray(arg: any): arg is any[];
            readonly prototype: any[];
            from<T>(arrayLike: ArrayLike<T>): T[];
            from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
            from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            of<T>(...items: T[]): T[];
            readonly [Symbol.species]: ArrayConstructor;
        };
        default: string[];
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").Ref<string, string>;
    countryCode: import("vue").Ref<string, string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    focusChange: (...args: any[]) => void;
    clearInput: (...args: any[]) => void;
}, import("vue").PublicProps, {
    type: string;
    pattern: string;
    disabled: boolean;
    required: boolean;
    readonly: boolean;
    value: string;
    autocomplete: string;
    placeholder: string;
    showDialCode: boolean;
    defaultCountry: string;
    preferredCountries: String[][];
}, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
    P: {};
    B: {};
    D: {};
    C: {};
    M: {};
    Defaults: {};
}, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    showDialCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    dropdownOptions: {
        type: ObjectConstructor;
    };
    defaultCountry: {
        type: StringConstructor;
        default: string;
    };
    preferredCountries: {
        type: {
            (arrayLength: number): String[][];
            (...items: String[][]): String[][];
            new (arrayLength: number): String[][];
            new (...items: String[][]): String[][];
            isArray(arg: any): arg is any[];
            readonly prototype: any[];
            from<T>(arrayLike: ArrayLike<T>): T[];
            from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
            from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            of<T>(...items: T[]): T[];
            readonly [Symbol.species]: ArrayConstructor;
        };
        default: string[];
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").Ref<string, string>;
    countryCode: import("vue").Ref<string, string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, {
    type: string;
    pattern: string;
    disabled: boolean;
    required: boolean;
    readonly: boolean;
    value: string;
    autocomplete: string;
    placeholder: string;
    showDialCode: boolean;
    defaultCountry: string;
    preferredCountries: String[][];
}> | HTMLInputElement | import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    currencyConfig: {
        type: PropType<import("vue-currency-input").CurrencyInputOptions>;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    value: string;
    formattedValue: import("vue").ComputedRef<any>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    focusChange: (...args: any[]) => void;
    clearInput: (...args: any[]) => void;
}, import("vue").PublicProps, {
    type: string;
    disabled: boolean;
    required: boolean;
    value: string;
    placeholder: string;
    min: string;
    max: string;
}, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
    P: {};
    B: {};
    D: {};
    C: {};
    M: {};
    Defaults: {};
}, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    currencyConfig: {
        type: PropType<import("vue-currency-input").CurrencyInputOptions>;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    value: string;
    formattedValue: import("vue").ComputedRef<any>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, {
    type: string;
    disabled: boolean;
    required: boolean;
    value: string;
    placeholder: string;
    min: string;
    max: string;
}> | import("vue").CreateComponentPublicInstanceWithMixins<Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    defaultDate: {
        type: StringConstructor;
        default: string;
    };
    timeStamp: {
        type: () => "start" | "end" | "";
        default: string;
    };
    smartFormat: {
        type: BooleanConstructor;
        default: boolean;
    };
    openDateTimePicker: {
        type: FunctionConstructor;
        required: true;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onShowError?: ((...args: any[]) => any) | undefined;
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").ComputedRef<string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    showError: (...args: any[]) => void;
}, import("vue").PublicProps, {
    type: string;
    value: string;
    min: string;
    max: string;
    defaultDate: string;
    timeStamp: "" | "start" | "end";
    smartFormat: boolean;
}, true, {}, {}, import("vue").GlobalComponents, import("vue").GlobalDirectives, string, {}, any, import("vue").ComponentProvideOptions, {
    P: {};
    B: {};
    D: {};
    C: {};
    M: {};
    Defaults: {};
}, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    defaultDate: {
        type: StringConstructor;
        default: string;
    };
    timeStamp: {
        type: () => "start" | "end" | "";
        default: string;
    };
    smartFormat: {
        type: BooleanConstructor;
        default: boolean;
    };
    openDateTimePicker: {
        type: FunctionConstructor;
        required: true;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onShowError?: ((...args: any[]) => any) | undefined;
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").ComputedRef<string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, {
    type: string;
    value: string;
    min: string;
    max: string;
    defaultDate: string;
    timeStamp: "" | "start" | "end";
    smartFormat: boolean;
}> | undefined>;
declare const inputContainerRef: import("vue").ShallowRef<HTMLDivElement | undefined, HTMLDivElement | undefined>;
declare const inputComponent: import("vue").ComputedRef<"input" | import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    showDialCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    dropdownOptions: {
        type: ObjectConstructor;
    };
    defaultCountry: {
        type: StringConstructor;
        default: string;
    };
    preferredCountries: {
        type: {
            (arrayLength: number): String[][];
            (...items: String[][]): String[][];
            new (arrayLength: number): String[][];
            new (...items: String[][]): String[][];
            isArray(arg: any): arg is any[];
            readonly prototype: any[];
            from<T>(arrayLike: ArrayLike<T>): T[];
            from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
            from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            of<T>(...items: T[]): T[];
            readonly [Symbol.species]: ArrayConstructor;
        };
        default: string[];
    };
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").Ref<string, string>;
    countryCode: import("vue").Ref<string, string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    focusChange: (...args: any[]) => void;
    clearInput: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    showDialCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    dropdownOptions: {
        type: ObjectConstructor;
    };
    defaultCountry: {
        type: StringConstructor;
        default: string;
    };
    preferredCountries: {
        type: {
            (arrayLength: number): String[][];
            (...items: String[][]): String[][];
            new (arrayLength: number): String[][];
            new (...items: String[][]): String[][];
            isArray(arg: any): arg is any[];
            readonly prototype: any[];
            from<T>(arrayLike: ArrayLike<T>): T[];
            from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            from<T>(iterable: Iterable<T> | ArrayLike<T>): T[];
            from<T, U>(iterable: Iterable<T> | ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[];
            of<T>(...items: T[]): T[];
            readonly [Symbol.species]: ArrayConstructor;
        };
        default: string[];
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    type: string;
    pattern: string;
    disabled: boolean;
    required: boolean;
    readonly: boolean;
    value: string;
    autocomplete: string;
    placeholder: string;
    showDialCode: boolean;
    defaultCountry: string;
    preferredCountries: String[][];
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any> | import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    currencyConfig: {
        type: PropType<import("vue-currency-input").CurrencyInputOptions>;
    };
}>, {
    value: string;
    formattedValue: import("vue").ComputedRef<any>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    focusChange: (...args: any[]) => void;
    clearInput: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    currencyConfig: {
        type: PropType<import("vue-currency-input").CurrencyInputOptions>;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onFocusChange?: ((...args: any[]) => any) | undefined;
    onClearInput?: ((...args: any[]) => any) | undefined;
}>, {
    type: string;
    disabled: boolean;
    required: boolean;
    value: string;
    placeholder: string;
    min: string;
    max: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any> | import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    defaultDate: {
        type: StringConstructor;
        default: string;
    };
    timeStamp: {
        type: () => "start" | "end" | "";
        default: string;
    };
    smartFormat: {
        type: BooleanConstructor;
        default: boolean;
    };
    openDateTimePicker: {
        type: FunctionConstructor;
        required: true;
    };
}>, {
    value: import("vue").Ref<string, string>;
    formattedValue: import("vue").ComputedRef<string>;
    focus: () => void;
    blur: () => void;
    checkValidity: () => boolean;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    input: (...args: any[]) => void;
    blur: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    showError: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    value: {
        type: StringConstructor;
        default: string;
    };
    type: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    defaultDate: {
        type: StringConstructor;
        default: string;
    };
    timeStamp: {
        type: () => "start" | "end" | "";
        default: string;
    };
    smartFormat: {
        type: BooleanConstructor;
        default: boolean;
    };
    openDateTimePicker: {
        type: FunctionConstructor;
        required: true;
    };
}>> & Readonly<{
    onInput?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    onShowError?: ((...args: any[]) => any) | undefined;
}>, {
    type: string;
    value: string;
    min: string;
    max: string;
    defaultDate: string;
    timeStamp: "" | "start" | "end";
    smartFormat: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any> | "textarea">;
declare const showCloseButton: import("vue").ComputedRef<boolean>;
declare function onInput(event: Event, value?: string): void;
declare function clearInput(): void;
declare function onFocus(event: FocusEvent): void;
declare function onBlur(event: FocusEvent): void;
declare function focus(): void;
declare function blur(): void;
/**
 * based on value to check isRequried, minlength, maxlength, min, max, pattern
 * based on type to check if the value is valid (email, url, date, telphone)
 * also set the validation message (error key & params) during the whole validation process
 * validation message format see function getErrorMessage()
 */
declare function checkValidity(): boolean;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_20: {};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    customButton?: (props: typeof __VLS_20) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    modelValue: {
        type: StringConstructor;
        default: string;
    };
    inputType: {
        type: StringConstructor;
        default: string;
    };
    inputClass: {
        type: StringConstructor;
        default: string;
    };
    inputmode: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocapitalize: {
        type: StringConstructor;
        default: string;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    autocorrect: {
        type: StringConstructor;
        default: string;
    };
    spellcheck: {
        type: BooleanConstructor;
        default: boolean;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    minlength: {
        type: StringConstructor;
        default: string;
    };
    maxlength: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    inputOptions: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    validate: {
        type: PropType<(value: string) => ValidateResult>;
    };
}>, {
    Button: typeof Button;
    disabled: typeof disabled;
    required: typeof required;
    readonly: typeof readonly;
    inputType: typeof inputType;
    inputOptions: typeof inputOptions;
    pattern: typeof pattern;
    minlength: typeof minlength;
    maxlength: typeof maxlength;
    min: typeof min;
    max: typeof max;
    inputmode: typeof inputmode;
    inputValue: typeof inputValue;
    inputRef: typeof inputRef;
    inputContainerRef: typeof inputContainerRef;
    inputComponent: typeof inputComponent;
    showCloseButton: typeof showCloseButton;
    onInput: typeof onInput;
    clearInput: typeof clearInput;
    onFocus: typeof onFocus;
    onBlur: typeof onBlur;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    inputCheck: (isValid: boolean, message: string) => any;
    "update:modelValue": (value: string) => any;
    inputFocus: (event: FocusEvent) => any;
    inputBlur: (event: FocusEvent) => any;
    inputChange: (value: string) => any;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    modelValue: {
        type: StringConstructor;
        default: string;
    };
    inputType: {
        type: StringConstructor;
        default: string;
    };
    inputClass: {
        type: StringConstructor;
        default: string;
    };
    inputmode: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocapitalize: {
        type: StringConstructor;
        default: string;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    autocorrect: {
        type: StringConstructor;
        default: string;
    };
    spellcheck: {
        type: BooleanConstructor;
        default: boolean;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    minlength: {
        type: StringConstructor;
        default: string;
    };
    maxlength: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    inputOptions: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    validate: {
        type: PropType<(value: string) => ValidateResult>;
    };
}>> & Readonly<{
    onInputCheck?: ((isValid: boolean, message: string) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string) => any) | undefined;
    onInputFocus?: ((event: FocusEvent) => any) | undefined;
    onInputBlur?: ((event: FocusEvent) => any) | undefined;
    onInputChange?: ((value: string) => any) | undefined;
}>, {
    pattern: string;
    disabled: boolean;
    required: boolean;
    readonly: boolean;
    value: string;
    autocomplete: string;
    placeholder: string;
    modelValue: string;
    inputOptions: Record<string, any>;
    inputmode: string;
    min: string;
    max: string;
    inputType: string;
    inputClass: string;
    clearable: boolean;
    autocapitalize: string;
    autocorrect: string;
    spellcheck: boolean;
    minlength: string;
    maxlength: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    modelValue: {
        type: StringConstructor;
        default: string;
    };
    inputType: {
        type: StringConstructor;
        default: string;
    };
    inputClass: {
        type: StringConstructor;
        default: string;
    };
    inputmode: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocapitalize: {
        type: StringConstructor;
        default: string;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    autocorrect: {
        type: StringConstructor;
        default: string;
    };
    spellcheck: {
        type: BooleanConstructor;
        default: boolean;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    minlength: {
        type: StringConstructor;
        default: string;
    };
    maxlength: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    inputOptions: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    validate: {
        type: PropType<(value: string) => ValidateResult>;
    };
}>, {
    value: import("vue").Ref<string, string>;
    countryCode: import("vue").ComputedRef<string>;
    formattedValue: import("vue").ComputedRef<any>;
    focus: typeof focus;
    blur: typeof blur;
    checkValidity: typeof checkValidity;
    validationMessage: import("vue").Ref<string, string>;
    clear: typeof clearInput;
    setValue: (val: string) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    inputCheck: (isValid: boolean, message: string) => any;
    "update:modelValue": (value: string) => any;
    inputFocus: (event: FocusEvent) => any;
    inputBlur: (event: FocusEvent) => any;
    inputChange: (value: string) => any;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    modelValue: {
        type: StringConstructor;
        default: string;
    };
    inputType: {
        type: StringConstructor;
        default: string;
    };
    inputClass: {
        type: StringConstructor;
        default: string;
    };
    inputmode: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: StringConstructor;
        default: string;
    };
    placeholder: {
        type: StringConstructor;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    autocapitalize: {
        type: StringConstructor;
        default: string;
    };
    autocomplete: {
        type: StringConstructor;
        default: string;
    };
    autocorrect: {
        type: StringConstructor;
        default: string;
    };
    spellcheck: {
        type: BooleanConstructor;
        default: boolean;
    };
    pattern: {
        type: StringConstructor;
        default: string;
    };
    minlength: {
        type: StringConstructor;
        default: string;
    };
    maxlength: {
        type: StringConstructor;
        default: string;
    };
    min: {
        type: StringConstructor;
        default: string;
    };
    max: {
        type: StringConstructor;
        default: string;
    };
    inputOptions: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    validate: {
        type: PropType<(value: string) => ValidateResult>;
    };
}>> & Readonly<{
    onInputCheck?: ((isValid: boolean, message: string) => any) | undefined;
    "onUpdate:modelValue"?: ((value: string) => any) | undefined;
    onInputFocus?: ((event: FocusEvent) => any) | undefined;
    onInputBlur?: ((event: FocusEvent) => any) | undefined;
    onInputChange?: ((value: string) => any) | undefined;
}>, {
    pattern: string;
    disabled: boolean;
    required: boolean;
    readonly: boolean;
    value: string;
    autocomplete: string;
    placeholder: string;
    modelValue: string;
    inputOptions: Record<string, any>;
    inputmode: string;
    min: string;
    max: string;
    inputType: string;
    inputClass: string;
    clearable: boolean;
    autocapitalize: string;
    autocorrect: string;
    spellcheck: boolean;
    minlength: string;
    maxlength: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
