import { type PropType } from 'vue';
import type { Cards } from '@perkd/applet-common/types/cards';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    cardInfo: {
        type: PropType<Cards.Card>;
    };
    startRect: {
        type: {
            new (x?: number, y?: number, width?: number, height?: number): DOMRect;
            prototype: DOMRect;
            fromRect(other?: DOMRectInit): DOMRect;
        };
        default: () => {
            top: number;
            left: number;
        };
    };
    startWidth: {
        type: NumberConstructor;
        default: number;
    };
}>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    closeCardOverlay: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    cardInfo: {
        type: PropType<Cards.Card>;
    };
    startRect: {
        type: {
            new (x?: number, y?: number, width?: number, height?: number): DOMRect;
            prototype: DOMRect;
            fromRect(other?: DOMRectInit): DOMRect;
        };
        default: () => {
            top: number;
            left: number;
        };
    };
    startWidth: {
        type: NumberConstructor;
        default: number;
    };
}>> & Readonly<{
    onCloseCardOverlay?: ((...args: any[]) => any) | undefined;
}>, {
    startRect: DOMRect;
    startWidth: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
