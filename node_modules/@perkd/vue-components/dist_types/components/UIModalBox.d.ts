import Button from '@/components/UIButton.vue';
declare const showClose: import("vue").Ref<boolean, boolean>, overlayAnimation: import("vue").Ref<string | undefined, string | undefined> | undefined, modalAnimation: import("vue").Ref<string | undefined, string | undefined> | undefined, showHandle: import("vue").Ref<boolean, boolean>;
declare const modalContainerRef: import("vue").Ref<HTMLDivElement | null, HTMLDivElement | null>;
declare const modalRef: import("vue").Ref<HTMLDivElement | null, HTMLDivElement | null>;
declare const handleRef: import("vue").Ref<HTMLDivElement | null, HTMLDivElement | null>;
declare const showOverlay: import("vue").Ref<boolean, boolean>;
declare const showModal: import("vue").Ref<boolean, boolean>;
declare function onTouchStart(event: TouchEvent): void;
declare function onTouchMove(event: TouchEvent): void;
declare function onTouchEnd(): void;
declare function closeModal(): void;
declare function onOverlayEnter(): void;
declare function onModalEnter(): void;
declare function onModalLeave(): void;
declare function onOverlayLeave(): void;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_26: {
    onCloseModal: typeof closeModal;
};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    default?: (props: typeof __VLS_26) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    overlayAnimation: {
        type: StringConstructor;
    };
    modalAnimation: {
        type: StringConstructor;
    };
    showHandle: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchable: {
        type: BooleanConstructor;
        default: boolean;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
}>, {
    Button: typeof Button;
    showClose: typeof showClose;
    overlayAnimation: typeof overlayAnimation;
    modalAnimation: typeof modalAnimation;
    showHandle: typeof showHandle;
    modalContainerRef: typeof modalContainerRef;
    modalRef: typeof modalRef;
    handleRef: typeof handleRef;
    showOverlay: typeof showOverlay;
    showModal: typeof showModal;
    onTouchStart: typeof onTouchStart;
    onTouchMove: typeof onTouchMove;
    onTouchEnd: typeof onTouchEnd;
    closeModal: typeof closeModal;
    onOverlayEnter: typeof onOverlayEnter;
    onModalEnter: typeof onModalEnter;
    onModalLeave: typeof onModalLeave;
    onOverlayLeave: typeof onOverlayLeave;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    closeModal: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    overlayAnimation: {
        type: StringConstructor;
    };
    modalAnimation: {
        type: StringConstructor;
    };
    showHandle: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchable: {
        type: BooleanConstructor;
        default: boolean;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
}>> & Readonly<{
    onCloseModal?: ((...args: any[]) => any) | undefined;
}>, {
    threshold: number;
    showClose: boolean;
    showHandle: boolean;
    touchable: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    overlayAnimation: {
        type: StringConstructor;
    };
    modalAnimation: {
        type: StringConstructor;
    };
    showHandle: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchable: {
        type: BooleanConstructor;
        default: boolean;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
}>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    closeModal: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    overlayAnimation: {
        type: StringConstructor;
    };
    modalAnimation: {
        type: StringConstructor;
    };
    showHandle: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchable: {
        type: BooleanConstructor;
        default: boolean;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
}>> & Readonly<{
    onCloseModal?: ((...args: any[]) => any) | undefined;
}>, {
    threshold: number;
    showClose: boolean;
    showHandle: boolean;
    touchable: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
