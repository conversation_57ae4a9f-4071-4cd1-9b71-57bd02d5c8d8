import { type PropType } from 'vue';
import { type CurrencyInputOptions } from 'vue-currency-input';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    modelValue: {
        type: NumberConstructor;
    };
    currencyConfig: {
        type: PropType<CurrencyInputOptions>;
    };
}>, {
    value: import("vue").Ref<number | null, number | null> | import("vue").Ref<number | undefined, number | undefined> | undefined;
    formattedValue: import("vue").Ref<string | null, string | null> | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    blur: (...args: any[]) => void;
    change: (...args: any[]) => void;
    focus: (...args: any[]) => void;
    "update:modelValue": (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    modelValue: {
        type: NumberConstructor;
    };
    currencyConfig: {
        type: PropType<CurrencyInputOptions>;
    };
}>> & Readonly<{
    onBlur?: ((...args: any[]) => any) | undefined;
    onChange?: ((...args: any[]) => any) | undefined;
    onFocus?: ((...args: any[]) => any) | undefined;
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
