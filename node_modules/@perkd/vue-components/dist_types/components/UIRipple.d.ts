declare function createRipple(event: MouseEvent): void;
declare const _default: import("vue").DefineComponent<{}, {
    createRipple: typeof createRipple;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
