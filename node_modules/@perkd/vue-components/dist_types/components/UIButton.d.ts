import { type PropType } from 'vue';
import Icon from '@/components/UIIcon.vue';
import Loading from '@/components/UILoading.vue';
import Ripple from '@/components/UIRipple.vue';
interface ICON {
    name: string;
    type?: string;
    position?: string;
    class?: string;
}
declare const loading: import("vue").Ref<boolean, boolean>;
declare const buttonRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
declare const rippleRef: import("vue").Ref<import("vue").DefineComponent<{}, {
    createRipple: (event: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any> | undefined, import("vue").DefineComponent<{}, {
    createRipple: (event: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any> | undefined>;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_4: {}, __VLS_13: {};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    content?: (props: typeof __VLS_4) => any;
} & {
    status?: (props: typeof __VLS_13) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    type: {
        type: () => "solid" | "outline" | "clear" | "circle";
        default: string;
    };
    color: {
        type: () => "primary" | "accent" | "success" | "warning" | "error";
        default: string;
    };
    icon: {
        type: PropType<ICON>;
        required: false;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    subtitle: StringConstructor;
    subtitleClass: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    Icon: typeof Icon;
    Loading: typeof Loading;
    Ripple: typeof Ripple;
    loading: typeof loading;
    buttonRef: typeof buttonRef;
    rippleRef: typeof rippleRef;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    click: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: () => "solid" | "outline" | "clear" | "circle";
        default: string;
    };
    color: {
        type: () => "primary" | "accent" | "success" | "warning" | "error";
        default: string;
    };
    icon: {
        type: PropType<ICON>;
        required: false;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    subtitle: StringConstructor;
    subtitleClass: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onClick?: ((...args: any[]) => any) | undefined;
}>, {
    color: "primary" | "accent" | "success" | "warning" | "error";
    type: "circle" | "solid" | "outline" | "clear";
    titleClass: string;
    subtitleClass: string;
    disabled: boolean;
    loading: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    type: {
        type: () => "solid" | "outline" | "clear" | "circle";
        default: string;
    };
    color: {
        type: () => "primary" | "accent" | "success" | "warning" | "error";
        default: string;
    };
    icon: {
        type: PropType<ICON>;
        required: false;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    subtitle: StringConstructor;
    subtitleClass: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    click: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: () => "solid" | "outline" | "clear" | "circle";
        default: string;
    };
    color: {
        type: () => "primary" | "accent" | "success" | "warning" | "error";
        default: string;
    };
    icon: {
        type: PropType<ICON>;
        required: false;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    subtitle: StringConstructor;
    subtitleClass: {
        type: StringConstructor;
        default: string;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
    loading: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onClick?: ((...args: any[]) => any) | undefined;
}>, {
    color: "primary" | "accent" | "success" | "warning" | "error";
    type: "circle" | "solid" | "outline" | "clear";
    titleClass: string;
    subtitleClass: string;
    disabled: boolean;
    loading: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
