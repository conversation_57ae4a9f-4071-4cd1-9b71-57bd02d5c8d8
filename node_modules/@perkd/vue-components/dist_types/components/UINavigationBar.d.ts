import { type PropType } from 'vue';
import Button from '@/components/UIButton.vue';
import { type Applets } from '@perkd/applet-common/types/applets';
declare const t: import("vue-i18n").ComposerTranslation<{
    [x: string]: import("@intlify/core-base").LocaleMessage<import("vue-i18n").VueMessageType>;
}, string, import("@intlify/core-base").RemoveIndexSignature<{
    [x: string]: import("vue-i18n").LocaleMessageValue<import("vue-i18n").VueMessageType>;
}>, never, never, never>;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_15: {}, __VLS_17: {}, __VLS_19: {};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    leftContent?: (props: typeof __VLS_15) => any;
} & {
    centerContent?: (props: typeof __VLS_17) => any;
} & {
    rightContent?: (props: typeof __VLS_19) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    navBack: {
        type: PropType<Applets.Navback>;
    };
    theme: {
        type: StringConstructor;
        default: string;
    };
    leftClass: {
        type: StringConstructor;
        default: string;
    };
    centerClass: {
        type: StringConstructor;
        default: string;
    };
    rightClass: {
        type: StringConstructor;
        default: string;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    isOnline: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
}>, {
    Button: typeof Button;
    t: typeof t;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    navBack: {
        type: PropType<Applets.Navback>;
    };
    theme: {
        type: StringConstructor;
        default: string;
    };
    leftClass: {
        type: StringConstructor;
        default: string;
    };
    centerClass: {
        type: StringConstructor;
        default: string;
    };
    rightClass: {
        type: StringConstructor;
        default: string;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    isOnline: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    titleClass: string;
    theme: string;
    leftClass: string;
    centerClass: string;
    rightClass: string;
    isOnline: boolean;
    status: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    navBack: {
        type: PropType<Applets.Navback>;
    };
    theme: {
        type: StringConstructor;
        default: string;
    };
    leftClass: {
        type: StringConstructor;
        default: string;
    };
    centerClass: {
        type: StringConstructor;
        default: string;
    };
    rightClass: {
        type: StringConstructor;
        default: string;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    isOnline: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
}>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    navBack: {
        type: PropType<Applets.Navback>;
    };
    theme: {
        type: StringConstructor;
        default: string;
    };
    leftClass: {
        type: StringConstructor;
        default: string;
    };
    centerClass: {
        type: StringConstructor;
        default: string;
    };
    rightClass: {
        type: StringConstructor;
        default: string;
    };
    title: StringConstructor;
    titleClass: {
        type: StringConstructor;
        default: string;
    };
    isOnline: {
        type: BooleanConstructor;
        default: boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    titleClass: string;
    theme: string;
    leftClass: string;
    centerClass: string;
    rightClass: string;
    isOnline: boolean;
    status: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
