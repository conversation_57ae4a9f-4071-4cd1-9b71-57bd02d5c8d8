import { type PropType } from 'vue';
import { type Applets } from '@perkd/applet-common/types/applets';
declare function checkValidity(): boolean;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    checkedIcon: {
        type: PropType<Applets.Icon>;
    };
    unCheckedIcon: {
        type: PropType<Applets.Icon>;
    };
    checked: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    value: import("vue").Ref<boolean, boolean>;
    checkValidity: typeof checkValidity;
    validationMessage: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    inputCheck: (...args: any[]) => void;
    "update:modelValue": (...args: any[]) => void;
    inputChange: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    checkedIcon: {
        type: PropType<Applets.Icon>;
    };
    unCheckedIcon: {
        type: PropType<Applets.Icon>;
    };
    checked: {
        type: BooleanConstructor;
        default: boolean;
    };
    required: {
        type: BooleanConstructor;
        default: boolean;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onInputCheck?: ((...args: any[]) => any) | undefined;
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
    onInputChange?: ((...args: any[]) => any) | undefined;
}>, {
    disabled: boolean;
    required: boolean;
    checked: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
