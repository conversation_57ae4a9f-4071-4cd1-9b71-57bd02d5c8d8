import { type PropType } from 'vue';
import Button from '@/components/UIButton.vue';
interface ACTION {
    key: string;
    color: string;
    text?: string;
    icon?: string;
    closeAfterClick?: boolean;
}
declare enum SwipeDirection {
    LEFT = "left",
    RIGHT = "right"
}
declare const contentRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
declare const actionContainerRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
declare const swipeDirection: import("vue").Ref<SwipeDirection | null, SwipeDirection | null>;
declare const isSwiping: import("vue").Ref<boolean, boolean>;
declare const swipeable: import("vue").Ref<boolean, boolean>;
declare const isDeleted: import("vue").Ref<boolean, boolean>;
declare const currentActions: import("vue").ComputedRef<ACTION[] | undefined>;
declare function onTouchStart(event: TouchEvent): void;
declare function onTouchMove(event: TouchEvent): void;
declare function onTouchEnd(): void;
declare function onTouchCancel(): void;
declare function onClick(actionKey: string, closeAfterClick?: boolean): void;
declare function closeSwipeout(): void;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_12: {};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    default?: (props: typeof __VLS_12) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    leftActions: {
        type: PropType<ACTION[]>;
    };
    rightActions: {
        type: PropType<ACTION[]>;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    Button: typeof Button;
    SwipeDirection: typeof SwipeDirection;
    contentRef: typeof contentRef;
    actionContainerRef: typeof actionContainerRef;
    swipeDirection: typeof swipeDirection;
    isSwiping: typeof isSwiping;
    swipeable: typeof swipeable;
    isDeleted: typeof isDeleted;
    currentActions: typeof currentActions;
    onTouchStart: typeof onTouchStart;
    onTouchMove: typeof onTouchMove;
    onTouchEnd: typeof onTouchEnd;
    onTouchCancel: typeof onTouchCancel;
    onClick: typeof onClick;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    clickAction: (...args: any[]) => void;
    startSwipe: (...args: any[]) => void;
    endSwipe: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    leftActions: {
        type: PropType<ACTION[]>;
    };
    rightActions: {
        type: PropType<ACTION[]>;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onClickAction?: ((...args: any[]) => any) | undefined;
    onStartSwipe?: ((...args: any[]) => any) | undefined;
    onEndSwipe?: ((...args: any[]) => any) | undefined;
}>, {
    disabled: boolean;
    threshold: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    leftActions: {
        type: PropType<ACTION[]>;
    };
    rightActions: {
        type: PropType<ACTION[]>;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    isSwipeoutOpen: import("vue").Ref<boolean, boolean>;
    closeSwipeout: typeof closeSwipeout;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    clickAction: (...args: any[]) => void;
    startSwipe: (...args: any[]) => void;
    endSwipe: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    leftActions: {
        type: PropType<ACTION[]>;
    };
    rightActions: {
        type: PropType<ACTION[]>;
    };
    threshold: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onClickAction?: ((...args: any[]) => any) | undefined;
    onStartSwipe?: ((...args: any[]) => any) | undefined;
    onEndSwipe?: ((...args: any[]) => any) | undefined;
}>, {
    disabled: boolean;
    threshold: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
