import { type PropType } from 'vue';
import { formatDateTime as dayjs } from '@perkd/format-datetime';
declare const t: import("vue-i18n").ComposerTranslation<{
    [x: string]: import("@intlify/core-base").LocaleMessage<import("vue-i18n").VueMessageType>;
}, string, import("@intlify/core-base").RemoveIndexSignature<{
    [x: string]: import("vue-i18n").LocaleMessageValue<import("vue-i18n").VueMessageType>;
}>, never, never, never>;
declare const serverTimeRequired: import("vue").Ref<boolean, boolean>, serverTime: import("vue").Ref<string | number | undefined, string | number | undefined> | undefined, endCountTime: import("vue").Ref<string | number, string | number>, countUpAfterCountDown: import("vue").Ref<boolean, boolean>;
declare const isCountUp: import("vue").Ref<boolean, boolean>;
declare const isBeforeCount: import("vue").ComputedRef<boolean>;
declare const isCounting: import("vue").ComputedRef<boolean>;
declare const isAfterCount: import("vue").ComputedRef<boolean>;
declare const timeValues: import("vue").ComputedRef<any>;
declare function stopCount(): void;
declare const __VLS_ctx: InstanceType<__VLS_PickNotAny<typeof __VLS_self, new () => {}>>;
declare var __VLS_1: {}, __VLS_3: {};
type __VLS_Slots = __VLS_PrettifyGlobal<__VLS_OmitStringIndex<typeof __VLS_ctx.$slots> & {
    beforeCount?: (props: typeof __VLS_1) => any;
} & {
    afterCount?: (props: typeof __VLS_3) => any;
}>;
declare const __VLS_self: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    serverTimeRequired: {
        type: BooleanConstructor;
        default: boolean;
    };
    serverTime: {
        type: PropType<number | string>;
        validator: (value: string | number) => boolean;
    };
    endCountTime: {
        required: true;
        type: PropType<number | string>;
        validator: (value: string | number) => boolean;
    };
    countDistance: {
        type: NumberConstructor;
        default: number;
    };
    timerFormat: {
        type: StringConstructor;
        default: string;
        validator: (value: string) => boolean;
    };
    countUpAfterCountDown: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    dayjs: typeof dayjs;
    t: typeof t;
    serverTimeRequired: typeof serverTimeRequired;
    serverTime: typeof serverTime;
    endCountTime: typeof endCountTime;
    countUpAfterCountDown: typeof countUpAfterCountDown;
    isCountUp: typeof isCountUp;
    isBeforeCount: typeof isBeforeCount;
    isCounting: typeof isCounting;
    isAfterCount: typeof isAfterCount;
    timeValues: typeof timeValues;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    endCountDown: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    serverTimeRequired: {
        type: BooleanConstructor;
        default: boolean;
    };
    serverTime: {
        type: PropType<number | string>;
        validator: (value: string | number) => boolean;
    };
    endCountTime: {
        required: true;
        type: PropType<number | string>;
        validator: (value: string | number) => boolean;
    };
    countDistance: {
        type: NumberConstructor;
        default: number;
    };
    timerFormat: {
        type: StringConstructor;
        default: string;
        validator: (value: string) => boolean;
    };
    countUpAfterCountDown: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onEndCountDown?: ((...args: any[]) => any) | undefined;
}>, {
    serverTimeRequired: boolean;
    countDistance: number;
    timerFormat: string;
    countUpAfterCountDown: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const __VLS_component: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    serverTimeRequired: {
        type: BooleanConstructor;
        default: boolean;
    };
    serverTime: {
        type: PropType<number | string>;
        validator: (value: string | number) => boolean;
    };
    endCountTime: {
        required: true;
        type: PropType<number | string>;
        validator: (value: string | number) => boolean;
    };
    countDistance: {
        type: NumberConstructor;
        default: number;
    };
    timerFormat: {
        type: StringConstructor;
        default: string;
        validator: (value: string) => boolean;
    };
    countUpAfterCountDown: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, {
    stopCount: typeof stopCount;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    endCountDown: (...args: any[]) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    serverTimeRequired: {
        type: BooleanConstructor;
        default: boolean;
    };
    serverTime: {
        type: PropType<number | string>;
        validator: (value: string | number) => boolean;
    };
    endCountTime: {
        required: true;
        type: PropType<number | string>;
        validator: (value: string | number) => boolean;
    };
    countDistance: {
        type: NumberConstructor;
        default: number;
    };
    timerFormat: {
        type: StringConstructor;
        default: string;
        validator: (value: string) => boolean;
    };
    countUpAfterCountDown: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{
    onEndCountDown?: ((...args: any[]) => any) | undefined;
}>, {
    serverTimeRequired: boolean;
    countDistance: number;
    timerFormat: string;
    countUpAfterCountDown: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithSlots<typeof __VLS_component, __VLS_Slots>;
export default _default;
type __VLS_WithSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
