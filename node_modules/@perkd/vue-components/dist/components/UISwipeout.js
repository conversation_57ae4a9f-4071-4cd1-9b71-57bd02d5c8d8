import { defineComponent as j, toRefs as G, ref as l, computed as A, createBlock as E, openBlock as S, Transition as H, withCtx as J, createElementBlock as I, createCommentVNode as Q, createElementVNode as T, normalizeStyle as N, normalizeClass as W, Fragment as Z, renderList as K, withModifiers as _, renderSlot as ee } from "vue";
import te from "./UIButton.js";
const le = {
  key: 0,
  class: "swipeout"
}, ae = /* @__PURE__ */ j({
  __name: "UISwipeout",
  props: {
    leftActions: {
      type: Array
    },
    rightActions: {
      type: Array
    },
    threshold: {
      type: Number,
      default: 0.8
    },
    disabled: {
      type: Boolean,
      default: !1
    }
  },
  emits: ["clickAction", "startSwipe", "endSwipe"],
  setup(Y, { expose: z, emit: D }) {
    const P = Y, { leftActions: o, rightActions: d, threshold: B, disabled: b } = G(P), r = l(null), X = l(null), c = l(null), i = l(0), M = l(0), $ = l(0), s = l(0), p = l(!1), v = l(!0), x = l(!1), f = l(null), w = D, m = l(!1), h = A(() => o != null && o.value || d != null && d.value ? d == null ? void 0 : d.value : [{
      key: "delete",
      color: "var(--color-background-error)",
      icon: "trash-o"
    }]), k = A(() => {
      if (c.value)
        return c.value === "right" ? o == null ? void 0 : o.value : h == null ? void 0 : h.value;
    });
    function V(e) {
      b.value || (M.value = e.touches[0].clientX, $.value = e.touches[0].clientY, v.value = !0);
    }
    function q(e) {
      if (!r.value || !v.value || b.value) return;
      f.value !== null && (cancelAnimationFrame(f.value), f.value = null), s.value = e.touches[0].clientX - M.value + i.value;
      const n = Math.abs(s.value) > Math.abs(e.touches[0].clientY - $.value);
      if (!U(r.value.getBoundingClientRect(), e.touches[0].clientX, e.touches[0].clientY) || !n) {
        F();
        return;
      }
      c.value = s.value > 0 ? "right" : "left", v.value = O(c.value), v.value && (p.value || (p.value = !0, w("startSwipe")), f.value = requestAnimationFrame(() => {
        y(s.value);
      }));
    }
    function L() {
      var n, u, t;
      if (!r.value || !p.value || b.value) return;
      if (f.value !== null && (cancelAnimationFrame(f.value), f.value = null), p.value = !1, v.value = !1, B.value && ((n = k.value) == null ? void 0 : n.length) === 1 && Math.abs(s.value) > r.value.offsetWidth * B.value)
        R(((u = k.value) == null ? void 0 : u[0].key) || "");
      else {
        const a = ((t = X.value) == null ? void 0 : t.getBoundingClientRect().width) || 80, g = Math.abs(s.value) > a;
        i.value = g ? s.value > 0 ? a : -a : 0, i.value === 0 ? C() : (y(i.value), m.value = !0);
      }
      w("endSwipe", m.value);
    }
    function F() {
      p.value = !1, v.value = !1, y(i.value), i.value === 0 && (c.value = null), w("endSwipe", m.value);
    }
    function O(e) {
      var t, a;
      const n = !!((t = o == null ? void 0 : o.value) != null && t.length), u = !!((a = h == null ? void 0 : h.value) != null && a.length);
      return e === "right" && n || e === "left" && u;
    }
    function y(e) {
      r.value && (r.value.style.transform = `translateX(${e}px)`);
    }
    function R(e, n = !0) {
      n && C(), w("clickAction", e), e === "delete" && (x.value = !0);
    }
    function C() {
      r.value && (i.value = 0, c.value = null, y(i.value), m.value = !1);
    }
    function U(e, n, u) {
      const { left: t, top: a, height: g } = e;
      return n >= t && u >= a - g / 2 && u <= a + g * 2;
    }
    return z({
      isSwipeoutOpen: m,
      closeSwipeout: C
    }), (e, n) => (S(), E(H, { name: "scale-up" }, {
      default: J(() => {
        var u;
        return [
          x.value ? Q("", !0) : (S(), I("div", le, [
            T("div", {
              class: "swipeout-background",
              style: N({ "background-color": (u = k.value) == null ? void 0 : u[0].color })
            }, null, 4),
            T("div", {
              ref_key: "actionContainerRef",
              ref: X,
              class: W(`swipeout-action-container ${c.value === "left" ? "right" : "left"}`)
            }, [
              (S(!0), I(Z, null, K(k.value, (t) => (S(), E(te, {
                class: "swipeout-action",
                key: t.key,
                style: N({ backgroundColor: t.color }),
                icon: t.icon ? { name: t.icon } : void 0,
                title: t.text,
                onTouchend: _((a) => R(t.key, t.closeAfterClick), ["stop", "prevent"])
              }, null, 8, ["style", "icon", "title", "onTouchend"]))), 128))
            ], 2),
            T("div", {
              ref_key: "contentRef",
              ref: r,
              class: W(["swipeout-content", { swiping: v.value && p.value }]),
              onTouchstartPassive: V,
              onTouchmovePassive: q,
              onTouchend: _(L, ["stop", "prevent"]),
              onTouchcancel: _(F, ["stop", "prevent"])
            }, [
              ee(e.$slots, "default")
            ], 34)
          ]))
        ];
      }),
      _: 3
    }));
  }
});
export {
  ae as default
};
