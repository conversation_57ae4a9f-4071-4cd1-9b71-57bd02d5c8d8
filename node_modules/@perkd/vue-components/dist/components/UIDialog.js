import { defineComponent as a, createElementBlock as o, openBlock as n, createCommentVNode as i, createElementVNode as d, toDisplayString as l, renderSlot as c, createVNode as m, unref as u } from "vue";
import { useI18n as f } from "vue-i18n";
import p from "./UIButton.js";
const g = { class: "dialog-container" }, k = {
  key: 0,
  class: "dialog-title"
}, y = {
  key: 1,
  class: "dialog-desc"
}, v = {
  key: 2,
  class: "dialog-content"
}, _ = { class: "actions-container" }, D = /* @__PURE__ */ a({
  __name: "UIDialog",
  props: {
    title: {
      type: String,
      default: ""
    },
    description: {
      type: String,
      default: ""
    }
  },
  setup(t) {
    const { t: r } = f();
    return (e, s) => (n(), o("div", g, [
      t.title ? (n(), o("div", k, l(t.title), 1)) : i("", !0),
      t.description ? (n(), o("div", y, l(t.description), 1)) : i("", !0),
      e.$slots.content ? (n(), o("div", v, [
        c(e.$slots, "content")
      ])) : i("", !0),
      d("div", _, [
        c(e.$slots, "buttons", {}, () => [
          m(p, {
            type: "clear",
            title: u(r)("button.ok"),
            onClick: s[0] || (s[0] = ($) => e.$emit("closeDialog"))
          }, null, 8, ["title"])
        ])
      ])
    ]));
  }
});
export {
  D as default
};
