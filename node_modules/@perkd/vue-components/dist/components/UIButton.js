import { defineComponent as x, toRefs as I, ref as h, onMounted as M, onBeforeUnmount as U, createElementBlock as d, openBlock as u, normalizeClass as f, createElementVNode as j, createVNode as b, normalizeStyle as q, unref as S, createBlock as T, createCommentVNode as s, renderSlot as $, toDisplayString as w, Transition as D, withCtx as O } from "vue";
import p from "./UIIcon.js";
import X from "./UILoading.js";
import Y from "./UIRipple.js";
const A = {
  key: 1,
  class: "button-title-container"
}, F = {
  key: 0,
  class: "status-container"
}, Q = /* @__PURE__ */ x({
  __name: "UIButton",
  props: {
    type: {
      type: String,
      default: "solid"
    },
    color: {
      type: String,
      default: "accent"
    },
    icon: {
      type: Object,
      required: !1
    },
    title: String,
    titleClass: {
      type: String,
      default: ""
    },
    subtitle: String,
    subtitleClass: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: !1
    },
    loading: {
      type: Boolean,
      default: !1
    }
  },
  emits: ["click"],
  setup(e, { emit: P }) {
    const z = e, { loading: v } = I(z), g = P, l = h(!1), o = h(null), m = h(void 0), y = "ontouchstart" in window;
    let r;
    M(() => {
      var t, n, i, a, c;
      y ? ((t = o.value) == null || t.addEventListener("touchstart", B, { passive: !0 }), (n = o.value) == null || n.addEventListener("touchmove", C, { passive: !0 }), (i = o.value) == null || i.addEventListener("touchend", E, { passive: !0 }), (a = o.value) == null || a.addEventListener("touchcancel", R)) : (c = o.value) == null || c.addEventListener("click", k);
    }), U(() => {
      var t, n, i, a, c;
      r && clearTimeout(r), y ? ((t = o.value) == null || t.removeEventListener("touchstart", B), (n = o.value) == null || n.removeEventListener("touchmove", C), (i = o.value) == null || i.removeEventListener("touchend", E), (a = o.value) == null || a.removeEventListener("touchcancel", R)) : (c = o.value) == null || c.removeEventListener("click", k);
    });
    function k(t) {
      var n;
      t.stopPropagation(), !(l.value || v.value) && (l.value = !0, (n = m.value) == null || n.createRipple(t), r && clearTimeout(r), r = setTimeout(() => {
        g("click", t), l.value = !1;
      }, 200));
    }
    function B(t) {
      var n;
      o.value && !l.value && !v.value && (l.value = !0, (n = m.value) == null || n.createRipple(t));
    }
    function C(t) {
      if (o.value) {
        const n = t.touches[0], i = o.value.getBoundingClientRect();
        L(i, n.clientX, n.clientY) || (l.value = !1);
      }
    }
    function E(t) {
      if (t.stopPropagation(), o.value && l.value) {
        const n = t.changedTouches[0], i = o.value.getBoundingClientRect();
        L(i, n.clientX, n.clientY) && g("click", t), l.value = !1;
      }
    }
    function R(t) {
      t.stopPropagation(), o.value && l.value && (l.value = !1);
    }
    function L(t, n, i) {
      const { left: a, top: c, width: N, height: V } = t;
      return n >= a && n <= a + N && i >= c && i <= c + V;
    }
    return (t, n) => (u(), d("div", {
      ref_key: "buttonRef",
      ref: o,
      class: f(["button", e.type, e.color, e.disabled ? "disabled" : "", e.icon && !(e.title || e.subtitle) && e.type === "clear" ? "clear-icon" : ""])
    }, [
      j("div", {
        class: "button-wrapper",
        style: q({ opacity: S(v) ? 0 : 1 })
      }, [
        e.icon && e.icon.position !== "right" ? (u(), T(p, {
          key: 0,
          name: e.icon.name,
          class: f(`button-icon ${e.icon.class || ""}`)
        }, null, 8, ["name", "class"])) : s("", !0),
        e.title || e.subtitle || t.$slots.content ? (u(), d("span", A, [
          e.title ? (u(), d("span", {
            key: 0,
            class: f("button-title " + e.titleClass)
          }, w(e.title), 3)) : s("", !0),
          e.subtitle ? (u(), d("span", {
            key: 1,
            class: f("button-subtitle " + e.subtitleClass)
          }, w(e.subtitle), 3)) : s("", !0),
          $(t.$slots, "content")
        ])) : s("", !0),
        e.icon && e.icon.position === "right" ? (u(), T(p, {
          key: 2,
          name: e.icon.name,
          class: f("button-icon " + e.icon.class)
        }, null, 8, ["name", "class"])) : s("", !0)
      ], 4),
      b(D, { name: "fade" }, {
        default: O(() => [
          S(v) ? (u(), d("div", F, [
            $(t.$slots, "status", {}, () => [
              b(X, {
                size: "xxs",
                colorBackground: e.type === "solid"
              }, null, 8, ["colorBackground"])
            ])
          ])) : s("", !0)
        ]),
        _: 3
      }),
      b(Y, {
        ref_key: "rippleRef",
        ref: m
      }, null, 512)
    ], 2));
  }
});
export {
  Q as default
};
