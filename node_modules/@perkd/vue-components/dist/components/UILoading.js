import { defineComponent as x, useCssVars as F, computed as s, createElementBlock as c, openBlock as n, createElementVNode as l, createCommentVNode as y, normalizeClass as C, toDisplayString as v } from "vue";
const k = { class: "loading-container" }, B = {
  key: 0,
  class: "loading-text"
}, h = /* @__PURE__ */ x({
  __name: "UILoading",
  props: {
    colorBackground: {
      type: Boolean,
      default: !1
    },
    success: {
      type: Boolean,
      default: void 0
    },
    thickness: {
      type: String,
      default: ""
    },
    size: {
      type: String,
      default: "md"
    },
    color: {
      type: String,
      default: ""
    },
    emptyColor: {
      type: String,
      default: ""
    },
    successColor: {
      type: String,
      default: ""
    },
    failedColor: {
      type: String,
      default: ""
    },
    text: {
      type: String,
      default: ""
    }
  },
  setup(o) {
    F((t) => ({
      "3b55d032": u.value,
      "016ed342": d.value,
      "178fa1ae": i.value,
      "5b848406": m.value,
      "4a23360b": p.value,
      "51a6d6fd": f.value
    }));
    const e = o, u = s(() => r(e.color || (e.colorBackground ? "#FFFFFF" : "accent"))), d = s(() => r(e.emptyColor || (e.colorBackground ? "rgba(255,255,255,0.2)" : "var(--color-background-heavy)"))), i = s(() => r(e.successColor || (e.colorBackground ? "#FFFFFF" : "success"))), m = s(() => r(e.failedColor || (e.colorBackground ? "#FFFFFF" : "error"))), p = s(() => {
      const t = e.thickness || e.size;
      switch (t) {
        case "xxs":
          return "2px";
        case "xs":
          return "2px";
        case "sm":
          return "4px";
        case "md":
          return "6px";
        case "lg":
          return "8px";
        case "xl":
          return "10px";
        default:
          return t || "6px";
      }
    }), f = s(() => {
      switch (e.size) {
        case "xxs":
          return "1em";
        case "xs":
          return "2em";
        case "sm":
          return "3em";
        case "md":
          return "5em";
        case "lg":
          return "7em";
        case "xl":
          return "10em";
        default:
          return e.size || "5em";
      }
    }), g = s(() => e.success === !0 ? "success" : e.success === !1 ? "failed" : "");
    function r(t) {
      return ["primary", "accent", "success", "warning", "error"].indexOf(t) !== -1 ? `var(--color-background-${t})` : t;
    }
    return (t, a) => (n(), c("div", k, [
      l("div", {
        class: C(`circle-loader ${g.value}`)
      }, a[0] || (a[0] = [
        l("div", { class: "status draw" }, null, -1)
      ]), 2),
      o.text ? (n(), c("div", B, v(o.text), 1)) : y("", !0)
    ]));
  }
});
export {
  h as default
};
