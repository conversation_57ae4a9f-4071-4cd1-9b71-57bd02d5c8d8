import { defineComponent as I, toRefs as N, computed as y, ref as D, watch as V, createElementBlock as b, openBlock as _, unref as k, createElementVNode as G } from "vue";
import { CurrencyDisplay as O, useCurrencyInput as B } from "vue-currency-input";
import { formatAmount as h } from "@perkd/applet-common/utils";
const E = { key: 1 }, R = ["value"], A = /* @__PURE__ */ I({
  __name: "CurrencyInput",
  props: {
    modelValue: {
      type: Number
    },
    currencyConfig: {
      type: Object
    }
  },
  emits: ["update:modelValue", "focus", "blur", "change"],
  setup(S, { expose: g, emit: x }) {
    const i = S, { currencyConfig: e } = N(i), o = y(() => {
      var u, r, t, p, f;
      return {
        locale: ((u = e == null ? void 0 : e.value) == null ? void 0 : u.locale) || "en-SG",
        currency: ((r = e == null ? void 0 : e.value) == null ? void 0 : r.currency) || "SGD",
        precision: ((t = e == null ? void 0 : e.value) == null ? void 0 : t.precision) ?? 2,
        currencyDisplay: ((p = e == null ? void 0 : e.value) == null ? void 0 : p.currencyDisplay) || O.narrowSymbol,
        hideCurrencySymbolOnFocus: !1,
        hideGroupingSeparatorOnFocus: !1,
        hideNegligibleDecimalDigitsOnFocus: !1,
        autoDecimalDigits: ((f = e == null ? void 0 : e.value) == null ? void 0 : f.autoDecimalDigits) ?? !0,
        useGrouping: !0,
        accountingSign: !1
      };
    }), n = "formatToParts" in Intl.NumberFormat.prototype, l = n ? B(o.value) : null, a = D(i.modelValue), F = y(() => Math.pow(10, o.value.precision)), s = D(i.modelValue ? `${i.modelValue}` : ""), c = x;
    V(() => i.modelValue, (u) => {
      n ? l == null || l.setValue(u ?? null) : a.value = u ?? void 0;
    }), V(a, (u) => {
      var r, t, p;
      n || (s.value = u ? o.value.autoDecimalDigits ? h(a == null ? void 0 : a.value, (r = o.value) == null ? void 0 : r.currency, (t = o.value) == null ? void 0 : t.precision, (p = o.value) == null ? void 0 : p.precision) : String(u) : "");
    });
    function m(u) {
      if (n) {
        const t = l == null ? void 0 : l.numberValue.value;
        c("update:modelValue", t);
        return;
      }
      const r = u.target.value.replace(/[\D]/g, "");
      a.value = Number(r) / F.value, s.value = h(a.value, o.value.currency, o.value.precision, o.value.precision), c("update:modelValue", a.value);
    }
    function v(u) {
      c("focus", u);
    }
    function d(u) {
      c("blur", u);
    }
    return g({
      value: n ? l == null ? void 0 : l.numberValue : a,
      formattedValue: n ? l == null ? void 0 : l.formattedValue : s
    }), (u, r) => {
      var t;
      return n ? (_(), b("input", {
        key: 0,
        ref: (t = k(l)) == null ? void 0 : t.inputRef,
        type: "text",
        onInput: m,
        onBlur: d,
        onFocus: v
      }, null, 544)) : (_(), b("div", E, [
        G("input", {
          type: "text",
          inputmode: "numeric",
          value: s.value,
          onInput: m,
          onBlur: d,
          onFocus: v
        }, null, 40, R)
      ]));
    };
  }
});
export {
  A as default
};
