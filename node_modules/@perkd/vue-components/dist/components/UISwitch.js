import { defineComponent as v, toRefs as C, ref as i, watch as y, createElementBlock as b, openBlock as s, normalizeClass as I, createElementVNode as a, withDirectives as q, unref as w, vModelCheckbox as B, createBlock as V, createCommentVNode as x, normalizeProps as g, mergeProps as R } from "vue";
import _ from "./UIIcon.js";
const j = { class: "switch" }, z = ["required", "disabled"], E = { class: "switch-slider" }, O = /* @__PURE__ */ v({
  __name: "UISwitch",
  props: {
    checkedIcon: {
      type: Object
    },
    unCheckedIcon: {
      type: Object
    },
    checked: {
      type: Boolean,
      default: !1
    },
    required: {
      type: Boolean,
      default: !1
    },
    disabled: {
      type: Boolean,
      default: !1
    }
  },
  emits: ["update:modelValue", "inputChange", "inputCheck"],
  setup(c, { expose: u, emit: r }) {
    const h = c, { checked: m, required: l } = C(h), e = i(m.value), p = i(void 0), n = i(""), o = r;
    y(e, (t) => {
      o("inputChange", t), o("update:modelValue", t);
    });
    function f() {
      const t = l.value ? !!e.value : !0;
      return n.value = t ? "" : "is_required", o("inputCheck", t, n.value), t;
    }
    return u({
      value: e,
      checkValidity: f,
      validationMessage: n
    }), (t, d) => (s(), b("div", {
      class: I(["switch-container", { checked: e.value }])
    }, [
      a("div", j, [
        q(a("input", {
          class: "switch-input",
          ref_key: "inputRef",
          ref: p,
          "onUpdate:modelValue": d[0] || (d[0] = (k) => e.value = k),
          type: "checkbox",
          required: w(l),
          disabled: c.disabled
        }, null, 8, z), [
          [B, e.value]
        ]),
        a("div", E, [
          c.checkedIcon || c.unCheckedIcon ? (s(), V(_, g(R({ key: 0 }, e.value ? c.checkedIcon : c.unCheckedIcon)), null, 16)) : x("", !0)
        ])
      ])
    ], 2));
  }
});
export {
  O as default
};
