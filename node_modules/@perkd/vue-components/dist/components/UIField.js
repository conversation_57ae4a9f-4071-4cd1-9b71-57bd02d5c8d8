import { defineComponent as A, toRefs as D, ref as c, computed as R, watch as q, createElementBlock as a, openBlock as l, normalizeClass as y, createElementVNode as C, createCommentVNode as u, unref as p, createVNode as N, renderSlot as U, createTextVNode as S, toDisplayString as $, Fragment as G } from "vue";
import { useI18n as H } from "vue-i18n";
import x from "./UIIcon.js";
const J = { class: "field-wrapper" }, L = {
  key: 0,
  class: "icon-container"
}, Q = { class: "field-inner" }, W = {
  key: 0,
  class: "icon-container"
}, X = {
  key: 0,
  class: "field-error error"
}, ee = /* @__PURE__ */ A({
  __name: "UIField",
  props: {
    icon: {
      type: Object,
      required: !1
    },
    label: {
      type: String,
      default: ""
    },
    labelClass: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: !1
    },
    readonly: {
      type: <PERSON><PERSON><PERSON>,
      default: !1
    },
    required: {
      type: Boolean,
      default: !1
    },
    errorMessages: {
      type: Object
    },
    showError: {
      type: Number,
      default: 0
    }
  },
  emits: ["focusChange"],
  setup(t, { expose: K, emit: M }) {
    const z = t, { t: j } = H(), { label: d, errorMessages: o, showError: w } = D(z), f = c(!1), b = c(""), h = c(""), v = c(!0), r = c(w.value), P = R(() => (f.value || b.value) && !!d.value), B = M;
    q(b, () => {
      r.value = 0;
    }), q(w, (e) => {
      r.value = e;
    });
    function E(e) {
      f.value = !0, B("focusChange", e, !0);
    }
    function F(e) {
      f.value = !1, B("focusChange", e, !1);
    }
    function I(e) {
      b.value = e;
    }
    function O(e, n) {
      v.value = e, h.value = n;
    }
    function T() {
      if (!r.value || v.value) return "";
      let e = "", n = {};
      const V = (s) => s.reduce((i, g) => {
        const [k, ...m] = g.split(":");
        return i[k] = m.join(":"), i;
      }, {});
      if (h.value) {
        const [s, ...i] = h.value.split("|");
        if (e = `error.${s}`, Object.keys(i).length && Object.assign(n, V(i)), o != null && o.value && o.value[s]) {
          const g = o.value[s] || "", [k, ...m] = g.split("|");
          e = `error.${k}`, Object.keys(m).length && Object.assign(n, V(m));
        }
      }
      return e ? j(e, n) : j("error.invalid");
    }
    return K({
      onFocus: E,
      onBlur: F,
      onInput: I,
      inputCheck: O
    }), (e, n) => (l(), a("div", {
      class: y(["field-container", { disabled: t.disabled }, { readonly: t.readonly }, { active: P.value }, { focus: f.value }, { invalid: r.value > 0 && !v.value }])
    }, [
      C("div", J, [
        t.icon && !p(d) ? (l(), a("div", L, [
          N(x, {
            name: t.icon.name,
            class: y(t.icon.class)
          }, null, 8, ["name", "class"])
        ])) : u("", !0),
        C("div", Q, [
          U(e.$slots, "default", {
            required: t.required,
            disabled: t.disabled,
            readonly: t.readonly,
            onInputFocus: E,
            onInputBlur: F,
            onInputChange: I,
            onInputCheck: O
          }),
          p(d) ? (l(), a("div", {
            key: 0,
            class: y(["label-container", t.labelClass])
          }, [
            t.icon ? (l(), a("div", W, [
              N(x, {
                name: t.icon.name,
                class: y(t.icon.class)
              }, null, 8, ["name", "class"])
            ])) : u("", !0),
            C("label", null, [
              S($(p(d)), 1),
              t.required ? (l(), a(G, { key: 0 }, [
                S("*")
              ], 64)) : u("", !0)
            ])
          ], 2)) : u("", !0)
        ])
      ]),
      r.value > 0 && !v.value ? (l(), a("div", X, $(T()), 1)) : u("", !0)
    ], 2));
  }
});
export {
  ee as default
};
