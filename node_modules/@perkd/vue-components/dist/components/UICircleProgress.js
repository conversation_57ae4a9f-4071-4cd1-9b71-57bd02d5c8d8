import { defineComponent as H, ref as z, computed as T, onMounted as q, onBeforeUnmount as J, watch as K, createElementBlock as n, openBlock as i, normalizeStyle as Q, createCommentVNode as g, normalizeProps as l, guardReactiveProps as y, createElementVNode as r, mergeProps as d, Fragment as A, toDisplayString as G, renderSlot as X } from "vue";
const Y = ["dx", "dy"], Z = ["stdDeviation"], tt = ["flood-color", "flood-opacity"], et = ["dx", "dy"], ot = ["stdDeviation"], rt = ["floodColor", "floodOpacity"], nt = {
  key: 0,
  class: "current-counter"
}, it = {
  key: 1,
  class: "text-container"
}, lt = /* @__PURE__ */ H({
  __name: "UICircleProgress",
  props: {
    size: {
      type: Number,
      default: 180
    },
    borderWidth: {
      type: Number,
      default: 10
    },
    borderBgWidth: {
      type: Number,
      default: 10
    },
    fillColor: {
      type: String,
      default: "accent"
    },
    emptyColor: {
      type: String,
      default: "var(--color-background-strong)"
    },
    background: {
      type: String,
      default: "none"
    },
    percent: {
      type: Number,
      default: 0
    },
    linecap: {
      type: String,
      default: "round"
    },
    transition: {
      type: Number,
      // speed, animation time
      default: 400
    },
    isGradient: {
      type: Boolean,
      default: !1
    },
    gradient: {
      type: Object
    },
    isShadow: {
      type: Boolean,
      default: !1
    },
    shadow: {
      type: Object
    },
    isBgShadow: {
      type: Boolean,
      default: !1
    },
    bgShadow: {
      type: Object
    },
    viewport: {
      type: Boolean,
      default: !1
    },
    onViewport: {
      type: Function
    },
    showPercent: {
      type: Boolean,
      default: !1
    },
    unit: {
      type: String,
      default: ""
    }
  },
  setup(s, { expose: b }) {
    const t = s, v = B("grd_"), w = B("shd1_"), W = B("shd2_"), S = z(null), c = z(0), P = (o) => ["primary", "accent", "success", "warning", "error"].indexOf(o) !== -1 ? `var(--color-background-${o})` : o, C = { angle: 0, startColor: "#ff0000", stopColor: "#ffff00", ...t.gradient }, u = { inset: !1, vertical: 10, horizontal: 0, blur: 10, opacity: 0.5, color: "#000000", ...t.shadow }, a = { inset: !0, vertical: 3, horizontal: 0, blur: 3, opacity: 0.4, color: "#000000", ...t.bgShadow }, I = () => {
      let o = (t.size - t.borderBgWidth) * 0.5;
      return t.borderWidth > t.borderBgWidth && (o -= (t.borderWidth - t.borderBgWidth) * 0.5), o;
    }, m = () => {
      let o = (t.size - t.borderWidth) * 0.5;
      return t.borderBgWidth > t.borderWidth && (o -= (t.borderBgWidth - t.borderWidth) * 0.5), o;
    }, M = 2 * Math.PI * m(), k = z(2 * Math.PI * m()), V = {
      style: {
        transform: "rotate(-90deg)",
        overflow: "visible"
      },
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: `${t.size / 2} ${t.size / 2} ${t.size} ${t.size}`
    }, _ = {
      cx: t.size,
      cy: t.size,
      r: I(),
      stroke: P(t.emptyColor),
      "stroke-width": t.borderBgWidth,
      fill: t.background,
      ...t.isBgShadow && { filter: `url(#${W})` }
    }, E = T(() => ({
      cx: t.size,
      cy: t.size,
      r: m(),
      fill: "none",
      "stroke-width": t.borderWidth,
      "stroke-dasharray": M,
      "stroke-dashoffset": k.value,
      "stroke-linecap": t.linecap,
      stroke: t.isGradient ? `url(#${v})` : P(t.fillColor),
      ...t.isShadow && { filter: `url(#${w})` },
      ...t.transition && {
        style: { transition: `stroke-dashoffset ${t.transition}ms` }
      }
    })), N = {
      id: v,
      x1: "0%",
      y1: "0%",
      x2: "0%",
      y2: "100%",
      gradientTransform: `rotate(${C.angle}, .5, .5)`
    }, F = {
      offset: 0,
      "stop-color": C.startColor
    }, R = {
      offset: 100,
      "stop-color": C.stopColor
    }, O = {
      id: w,
      width: "500%",
      height: "500%",
      x: "-250%",
      y: "-250%"
    }, $ = {
      id: W,
      width: "500%",
      height: "500%",
      x: "-250%",
      y: "-250%"
    }, f = {
      dx: u.vertical * -1,
      dy: u.horizontal,
      stdDeviation: u.blur,
      floodColor: u.color,
      floodOpacity: u.opacity
    }, p = {
      dx: a.vertical * -1,
      dy: a.horizontal,
      stdDeviation: a.blur,
      floodColor: a.color,
      floodOpacity: a.opacity
    };
    q(() => {
      h();
    }), J(() => {
      window.removeEventListener("scroll", h);
    }), K(
      () => t.percent,
      () => {
        h();
      }
    );
    function j(o) {
      if (o === null) return;
      const e = o.getBoundingClientRect();
      return e.top >= 0 && e.left >= 0 && e.bottom <= (window.innerHeight || document.documentElement.clientHeight) && e.right <= (window.innerWidth || document.documentElement.clientWidth);
    }
    function x() {
      const o = 2 * Math.PI * m();
      k.value = o - o * t.percent / 100;
      const e = Math.round(100 - 100 / o * k.value);
      L(e);
    }
    function L(o) {
      const e = o - c.value;
      if (e) {
        const U = t.transition / Math.abs(e), D = setInterval(() => {
          e > 0 ? (c.value += 1, c.value >= o && clearInterval(D)) : (c.value -= 1, c.value <= o && clearInterval(D));
        }, U);
      }
    }
    function h() {
      t.viewport ? (window.addEventListener("scroll", h), S.value && j(S.value) && (window.removeEventListener("scroll", h), t.viewport && x(), t.onViewport && typeof t.onViewport == "function" && t.onViewport())) : x();
    }
    function B(o = "", e = "") {
      return o + Math.random().toString(36).substring(2, 8) + Math.random().toString(36).substring(2, 8) + e;
    }
    return b({
      updatePercent: x
    }), (o, e) => (i(), n("div", {
      ref_key: "circleProgressRef",
      ref: S,
      style: Q({ height: s.size + "px", width: t.size + "px" }),
      class: "circular-progress"
    }, [
      (i(), n("svg", l(y(V)), [
        s.isGradient ? (i(), n("linearGradient", l(d({ key: 0 }, N)), [
          r("stop", l(y(F)), null, 16),
          r("stop", l(y(R)), null, 16)
        ], 16)) : g("", !0),
        r("circle", d(_, { class: "circle-background" }), null, 16),
        r("circle", d(E.value, { class: "circle-forground" }), null, 16),
        s.isShadow ? (i(), n(A, { key: 1 }, [
          u.inset === !1 ? (i(), n("filter", l(d({ key: 0 }, O)), [
            r("feDropShadow", l(y(f)), null, 16)
          ], 16)) : (i(), n("filter", l(d({ key: 1 }, O)), [
            r("feOffset", {
              dx: f.dx,
              dy: f.dy
            }, null, 8, Y),
            r("feGaussianBlur", {
              stdDeviation: f.stdDeviation
            }, null, 8, Z),
            e[0] || (e[0] = r("feComposite", {
              operator: "out",
              in: "SourceGraphic",
              result: "inverse"
            }, null, -1)),
            r("feFlood", {
              "flood-color": f.floodColor,
              "flood-opacity": f.floodOpacity,
              result: "color"
            }, null, 8, tt),
            e[1] || (e[1] = r("feComposite", {
              operator: "in",
              in: "color",
              in2: "inverse",
              result: "shadow"
            }, null, -1)),
            e[2] || (e[2] = r("feComposite", {
              operator: "over",
              in: "shadow",
              in2: "SourceGraphic"
            }, null, -1))
          ], 16))
        ], 64)) : g("", !0),
        a ? (i(), n(A, { key: 2 }, [
          a.inset === !1 ? (i(), n("filter", l(d({ key: 0 }, $)), [
            r("feDropShadow", l(y(p)), null, 16)
          ], 16)) : (i(), n("filter", l(d({ key: 1 }, $)), [
            r("feOffset", {
              dx: p.dx,
              dy: p.dy
            }, null, 8, et),
            r("feGaussianBlur", {
              stdDeviation: p.stdDeviation
            }, null, 8, ot),
            e[3] || (e[3] = r("feComposite", {
              operator: "out",
              in: "SourceGraphic",
              result: "inverse"
            }, null, -1)),
            r("feFlood", {
              floodColor: p.floodColor,
              floodOpacity: p.floodOpacity,
              result: "color"
            }, null, 8, rt),
            e[4] || (e[4] = r("feComposite", {
              operator: "in",
              in: "color",
              in2: "inverse",
              result: "shadow"
            }, null, -1)),
            e[5] || (e[5] = r("feComposite", {
              operator: "over",
              in: "shadow",
              in2: "SourceGraphic"
            }, null, -1))
          ], 16))
        ], 64)) : g("", !0)
      ], 16)),
      s.showPercent ? (i(), n("span", nt, G(c.value) + " " + G(s.unit), 1)) : g("", !0),
      o.$slots.textContent ? (i(), n("div", it, [
        X(o.$slots, "textContent", {}, void 0, !0)
      ])) : g("", !0)
    ], 4));
  }
}), st = (s, b) => {
  const t = s.__vccOpts || s;
  for (const [v, w] of b)
    t[v] = w;
  return t;
}, dt = /* @__PURE__ */ st(lt, [["__scopeId", "data-v-7da27c7b"]]);
export {
  dt as default
};
