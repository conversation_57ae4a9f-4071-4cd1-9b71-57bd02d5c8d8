import { defineComponent as B, toRefs as E, ref as l, computed as F, watch as M, createElementBlock as C, openBlock as P, createElementVNode as R } from "vue";
import { formatDateTime as a } from "@perkd/format-datetime";
const $ = ["value"], A = /* @__PURE__ */ B({
  __name: "UIInputDate",
  props: {
    value: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      default: ""
    },
    min: {
      type: String,
      default: ""
    },
    max: {
      type: String,
      default: ""
    },
    defaultDate: {
      type: String,
      default: ""
    },
    timeStamp: {
      type: String,
      default: ""
    },
    smartFormat: {
      type: Boolean,
      default: !1
    },
    openDateTimePicker: {
      type: Function,
      required: !0
    }
  },
  emits: ["input", "blur", "focus", "showError"],
  setup(w, { expose: D, emit: T }) {
    const m = w, { type: u, value: c, min: r, max: n, timeStamp: d, defaultDate: x, smartFormat: I } = E(m), t = l(c.value), v = F(() => {
      if (!t.value) return "";
      if (u.value === "time") return a(t.value).format("LT");
      if (I.value) return a(t.value).smartDateTime(void 0, u.value === "datetime");
      const e = u.value === "date" ? "ll" : "LL";
      return a(t.value).format(e);
    }), p = l(!1), i = l(void 0), S = l(""), o = T;
    M(c, (e) => {
      t.value = e;
    });
    function L() {
      p.value = !0, o("focus", { target: i.value }), h();
    }
    function f() {
      p.value = !1, o("blur", { target: i.value });
    }
    function h() {
      m.openDateTimePicker && m.openDateTimePicker({
        value: t.value || x.value || g(void 0),
        mode: u.value,
        theme: window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"
      }).then((e) => {
        O(e);
      }).catch((e) => {
        V(e);
      });
    }
    function O(e) {
      typeof e == "string" && (t.value = g(e), o("input", { target: i.value }, t.value)), f();
    }
    function V(e) {
      o("showError", e), f();
    }
    function g(e) {
      return d.value ? d.value === "start" ? a(e).startOf("d").toISOString() : a(e).endOf("d").toISOString() : e ? a(e).toISOString() : a().toISOString();
    }
    function b() {
      const e = a(t.value || ""), s = e.isValid(), y = r.value ? e.isSame(r.value) || e.isAfter(r.value) : !0, _ = n.value ? e.isSame(n.value) || e.isBefore(n.value) : !0, k = u.value === "date" ? "ll" : u.value === "datetime" ? "LL" : "LT";
      return S.value = s ? y ? _ ? "" : `after_maximum_date|date:${a(n.value).format(k)}` : `before_minimum_date|date:${a(r.value).format(k)}` : "invalid_date", s && y && _;
    }
    return D({
      value: t,
      formattedValue: v,
      focus: L,
      blur: f,
      checkValidity: b,
      validationMessage: S
    }), (e, s) => (P(), C("div", {
      class: "input-wrapper--date",
      ref_key: "inputContainerRef",
      ref: i,
      onClick: h
    }, [
      R("input", {
        readonly: "",
        value: v.value
      }, null, 8, $)
    ], 512));
  }
});
export {
  A as default
};
