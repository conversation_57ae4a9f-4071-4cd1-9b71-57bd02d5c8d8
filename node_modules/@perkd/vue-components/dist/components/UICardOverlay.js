import { defineComponent as F, toRefs as M, ref as n, onMounted as W, computed as u, createElementBlock as z, openBlock as V, normalizeClass as f, createVNode as $, normalizeStyle as j, unref as A, withModifiers as D } from "vue";
import k from "./UICard.js";
const m = 300, G = /* @__PURE__ */ F({
  __name: "UICardOverlay",
  props: {
    cardInfo: {
      type: Object
    },
    startRect: {
      type: DOMRect,
      default: () => ({
        top: 0,
        left: 0
      })
    },
    startWidth: {
      type: Number,
      default: 9
    }
  },
  emits: ["closeCardOverlay"],
  setup(r, { emit: I }) {
    const x = r, { startRect: v, startWidth: p } = M(x), O = parseFloat(getComputedStyle(document.documentElement).getPropertyValue("--width-card")) || 28.8, S = n(void 0), a = n(void 0), y = n(void 0), c = n(void 0), s = n(!1), d = n(!1), w = I;
    W(() => {
      var e;
      c.value = (e = y.value) == null ? void 0 : e.getBoundingClientRect(), setTimeout(() => {
        s.value = !0, setTimeout(() => {
          d.value = !0, a.value && a.value.isFront && a.value.flipCard();
        }, m);
      }, 0);
    });
    const i = u(() => {
      var e, t, l, o;
      return {
        top: ((e = v.value) == null ? void 0 : e.top) - (((t = c.value) == null ? void 0 : t.top) || 0),
        left: ((l = v.value) == null ? void 0 : l.left) - (((o = c.value) == null ? void 0 : o.left) || 0)
      };
    }), N = u(() => {
      var e, t;
      return {
        transition: `transform ${m}ms ease-in-out`,
        top: `${(e = i.value) == null ? void 0 : e.top}px`,
        left: `${(t = i.value) == null ? void 0 : t.left}px`
      };
    }), P = u(() => {
      var e, t, l, o;
      return `translate(calc(${(((e = c.value) == null ? void 0 : e.width) || 0) / 2}px - 50% - ${(t = i.value) == null ? void 0 : t.left}px), calc(${(((l = c.value) == null ? void 0 : l.height) || 0) / 2}px - 50% - ${(o = i.value) == null ? void 0 : o.top}px)) scale(${O / p.value})`;
    });
    function B() {
      a.value && !a.value.isFront ? (a.value.flipCard(), setTimeout(() => {
        C();
      }, 800)) : C();
    }
    function C() {
      d.value = !1, s.value = !1, setTimeout(() => {
        w("closeCardOverlay");
      }, m);
    }
    function E() {
      a.value && a.value.flipCard();
    }
    return (e, t) => {
      var l, o, T, h, b, R, g;
      return V(), z("div", {
        ref_key: "screenRef",
        ref: y,
        class: f(["screen", "card-overlay-screen", { overlay: d.value }]),
        onClick: B
      }, [
        $(k, {
          ref_key: "duppedCardRef",
          ref: S,
          class: f(["duppedCard", { hide: d.value }]),
          width: A(p),
          image: (l = r.cardInfo) == null ? void 0 : l.image,
          style: j({ ...N.value, transform: s.value ? P.value : "" })
        }, null, 8, ["class", "width", "image", "style"]),
        $(k, {
          ref_key: "overlayCardRef",
          ref: a,
          class: f(["overlayCard", { hide: !d.value }]),
          image: (o = r.cardInfo) == null ? void 0 : o.image,
          cardNumber: (T = r.cardInfo) == null ? void 0 : T.cardNumber,
          barcodeType: (h = r.cardInfo) == null ? void 0 : h.barcodeType,
          barcodePatterns: (b = r.cardInfo) == null ? void 0 : b.barcodePatterns,
          startTime: (R = r.cardInfo) == null ? void 0 : R.startTime,
          endTime: (g = r.cardInfo) == null ? void 0 : g.endTime,
          onClick: D(E, ["stop"])
        }, null, 8, ["class", "image", "cardNumber", "barcodeType", "barcodePatterns", "startTime", "endTime"])
      ], 2);
    };
  }
});
export {
  G as default
};
