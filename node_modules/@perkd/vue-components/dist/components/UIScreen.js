import { defineComponent as J, useSlots as K, toRefs as Q, ref as C, computed as b, onMounted as X, onBeforeUnmount as Y, createElementBlock as u, openBlock as i, Fragment as Z, createElementVNode as B, createVNode as v, mergeProps as T, renderSlot as r, createBlock as x, createCommentVNode as f, unref as t, normalizeProps as ee, createSlots as oe, withCtx as p, normalizeClass as P, Transition as _ } from "vue";
import te from "./UINavigationBar.js";
import ne from "./UIButton.js";
import se from "./UILoading.js";
const le = {
  key: 0,
  theme: "light",
  class: "notify-container"
}, ae = {
  key: 0,
  class: "content"
}, ie = {
  key: 0,
  class: "screen overlay"
}, re = {
  key: 0,
  class: "screen overlay"
}, ve = /* @__PURE__ */ J({
  __name: "UIScreen",
  props: {
    title: {
      type: String,
      default: ""
    },
    titleClass: {
      type: String,
      default: ""
    },
    navigationBarTheme: String,
    disableNavBack: {
      type: Boolean,
      default: !1
    },
    isOnline: {
      type: Boolean,
      default: !0
    },
    status: {
      type: String,
      default: ""
    },
    showClose: {
      type: Boolean,
      default: !0
    },
    loading: {
      type: Boolean,
      default: !1
    },
    onContentScroll: {
      type: Function
    }
  },
  emits: ["goPreviousPage", "closeWindow"],
  setup(N, { expose: R, emit: E }) {
    const s = K(), c = N, { title: h, titleClass: L, navigationBarTheme: y, isOnline: O, status: F, showClose: g, loading: I } = Q(c), H = window.innerHeight / 3, V = window.innerHeight / 2, w = C(0), l = C(void 0), a = C(void 0);
    let m;
    const $ = E, k = b(() => {
      var e;
      return (e = window.history.state) != null && e.back && !c.disableNavBack ? { type: "back", onClick: () => $("goPreviousPage") } : void 0;
    }), W = b(() => ({
      title: h.value,
      titleClass: L.value,
      theme: y == null ? void 0 : y.value,
      isOnline: O.value,
      status: F.value,
      navBack: k.value
    })), j = b(() => {
      const e = [];
      return (s.navigationBar || h.value || k.value || g.value) && e.push("with-navigation-bar"), s.tabBar && e.push("with-tab-bar"), e;
    });
    X(() => {
      var e;
      c.onContentScroll && ((e = l.value) == null || e.addEventListener("scroll", S));
    }), Y(() => {
      var e;
      c.onContentScroll && ((e = l.value) == null || e.removeEventListener("scroll", S));
    });
    function S(e) {
      c.onContentScroll && c.onContentScroll(e);
    }
    function z(e, o) {
      m && (clearTimeout(m), m = void 0), o ? U(e) : M();
    }
    function U(e) {
      const o = e.target;
      w.value = o.getBoundingClientRect().top;
      const n = w.value - H;
      a.value && (a.value.classList.remove("close"), a.value.style.height = V + "px"), setTimeout(() => {
        var d;
        (d = l.value) == null || d.scrollBy({ top: n, behavior: "smooth" });
      }, 0);
    }
    function M() {
      m = setTimeout(() => {
        a.value && a.value.classList.add("close");
      }, 500);
    }
    function q(e, o) {
      var d;
      const n = { behavior: "smooth" };
      e !== void 0 && Object.assign(n, { top: e }), o !== void 0 && Object.assign(n, { left: o }), (d = l.value) == null || d.scrollBy(n);
    }
    function A(e) {
      var o;
      (o = l.value) == null || o.scrollTo({ top: e || 0, behavior: "smooth" });
    }
    function D(e) {
      var o;
      (o = l.value) == null || o.scrollTo({ left: e || 0, behavior: "smooth" });
    }
    function G() {
      $("closeWindow");
    }
    return R({
      scrollBy: q,
      scrollToTop: A,
      scrollToLeft: D
    }), (e, o) => {
      var n;
      return i(), u(Z, null, [
        B("div", T({
          class: ["screen", ...j.value]
        }, e.$attrs), [
          r(e.$slots, "navigationBar", {}, () => [
            t(h) || k.value || t(g) ? (i(), x(te, ee(T({ key: 0 }, W.value)), oe({ _: 2 }, [
              t(g) ? {
                name: "rightContent",
                fn: p(() => [
                  v(ne, {
                    type: "circle",
                    icon: { name: "close" },
                    onClick: G
                  })
                ]),
                key: "0"
              } : void 0
            ]), 1040)) : f("", !0)
          ]),
          B("div", {
            ref_key: "screenContentRef",
            ref: l,
            class: P(`screen-content ${t(s).footer ? "screen-content-with-footer" : ""}`)
          }, [
            v(_, { name: "swipe-down" }, {
              default: p(() => [
                t(s).notify ? (i(), u("div", le, [
                  r(e.$slots, "notify")
                ])) : f("", !0)
              ]),
              _: 3
            }),
            t(s).content ? (i(), u("div", ae, [
              r(e.$slots, "content", { focusChange: z })
            ])) : f("", !0),
            t(s).footer ? (i(), u("div", {
              key: 1,
              class: P(`footer ${(n = a.value) != null && n.style.height ? "footer-before-keyboard" : ""}`)
            }, [
              r(e.$slots, "footer")
            ], 2)) : f("", !0),
            B("div", {
              ref_key: "keyboardRef",
              ref: a,
              class: "screen-keyboard"
            }, null, 512)
          ], 2),
          r(e.$slots, "tabBar")
        ], 16),
        v(_, { name: "fade" }, {
          default: p(() => [
            t(I) ? (i(), u("div", ie, [
              r(e.$slots, "loading", {}, () => [
                v(se)
              ])
            ])) : f("", !0)
          ]),
          _: 3
        }),
        v(_, { name: "fade" }, {
          default: p(() => [
            t(s).dialog ? (i(), u("div", re, [
              r(e.$slots, "dialog")
            ])) : f("", !0)
          ]),
          _: 3
        })
      ], 64);
    };
  }
});
export {
  ve as default
};
