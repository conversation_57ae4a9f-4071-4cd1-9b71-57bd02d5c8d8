import { defineComponent as O, toRefs as V, ref as o, onMounted as P, onUnmounted as U, createBlock as y, openBlock as a, Transition as p, unref as i, withCtx as k, createElementBlock as m, createCommentVNode as f, withModifiers as _, createVNode as D, createElementVNode as I, renderSlot as j } from "vue";
import q from "./UIButton.js";
const G = /* @__PURE__ */ O({
  __name: "UIModalBox",
  props: {
    showClose: {
      type: Boolean,
      default: !0
    },
    overlayAnimation: {
      type: String
    },
    modalAnimation: {
      type: String
    },
    showHandle: {
      type: Boolean,
      default: !1
    },
    touchable: {
      type: Boolean,
      default: !0
    },
    threshold: {
      type: Number,
      default: 0.35
    }
  },
  emits: ["closeModal"],
  setup(C, { emit: M }) {
    const w = C, { showClose: T, overlayAnimation: B, modalAnimation: E, showHandle: R, threshold: g, touchable: d } = V(w), l = o(null), t = o(null), h = o(null), s = o(0), u = o(0), c = o(!1), v = o(!1), S = M;
    P(() => {
      c.value = !0;
    }), U(() => {
      c.value = !1;
    });
    function Y(e) {
      if (!t.value || !d.value) return;
      const { scrollTop: n } = t.value, H = e.target === h.value ? 0 : n;
      e.touches.length === 1 && (s.value = e.touches[0].clientY + H, u.value = s.value);
    }
    function b(e) {
      if (e.touches.length === 1 && d.value) {
        u.value = e.touches[0].clientY;
        const n = u.value - s.value;
        l.value && n > 0 && (l.value.style.transform = `translateY(${n}px)`);
      }
    }
    function x() {
      if (!t.value || !d.value) return;
      const e = t.value.clientHeight * g.value;
      u.value - s.value > e ? r() : l.value && (l.value.style.transform = "");
    }
    function r() {
      v.value = !1;
    }
    function A() {
      v.value = !0;
    }
    function L() {
    }
    function N() {
      c.value = !1;
    }
    function $() {
      S("closeModal");
    }
    return (e, n) => (a(), y(p, {
      name: i(B) || "fade",
      onEnter: A,
      onLeave: $
    }, {
      default: k(() => [
        c.value ? (a(), m("div", {
          key: 0,
          class: "screen overlay modal-screen",
          onClick: _(r, ["self"])
        }, [
          D(p, {
            name: i(E) || "slide-up",
            onEnter: L,
            onLeave: N
          }, {
            default: k(() => [
              v.value ? (a(), m("div", {
                key: 0,
                ref_key: "modalContainerRef",
                ref: l,
                class: "modal-container",
                onTouchstartPassive: Y,
                onTouchmovePassive: b,
                onTouchend: _(x, ["stop"])
              }, [
                i(R) ? (a(), m("div", {
                  key: 0,
                  ref_key: "handleRef",
                  ref: h,
                  class: "handle"
                }, null, 512)) : f("", !0),
                i(T) ? (a(), y(q, {
                  key: 1,
                  class: "close-button",
                  type: "circle",
                  icon: { name: "close" },
                  onClick: r
                })) : f("", !0),
                I("div", {
                  class: "modal",
                  ref_key: "modalRef",
                  ref: t
                }, [
                  j(e.$slots, "default", { onCloseModal: r })
                ], 512)
              ], 544)) : f("", !0)
            ]),
            _: 3
          }, 8, ["name"])
        ])) : f("", !0)
      ]),
      _: 3
    }, 8, ["name"]));
  }
});
export {
  G as default
};
