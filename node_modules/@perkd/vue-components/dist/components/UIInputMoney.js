import { defineComponent as q, toRefs as I, ref as o, computed as j, watch as V, createElementBlock as F, openBlock as U, createVNode as A, mergeProps as E, unref as u } from "vue";
import { formatAmount as S } from "@perkd/applet-common/utils";
import O from "./CurrencyInput.js";
const G = /* @__PURE__ */ q({
  __name: "UIInputMoney",
  props: {
    value: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      default: "text"
    },
    disabled: {
      type: Boolean,
      default: !1
    },
    required: {
      type: Boolean,
      default: !1
    },
    placeholder: {
      type: String,
      default: ""
    },
    min: {
      type: String,
      default: ""
    },
    max: {
      type: String,
      default: ""
    },
    currencyConfig: {
      type: Object
    }
  },
  emits: ["input", "clearInput", "blur", "focus", "focusChange"],
  setup(h, { expose: N, emit: k }) {
    var g, _;
    const B = h, { value: d, disabled: C, required: R, placeholder: w, min: n, max: l, currencyConfig: r } = I(B), v = o(!1), y = o(""), i = o(void 0), s = o(void 0), M = j(() => s.value ? s.value.formattedValue : ""), t = o(Number((_ = (g = d.value) == null ? void 0 : g.match(/[0-9]*/g)) == null ? void 0 : _.join("")) || void 0), f = k;
    V(d, (e) => {
      e === "" && (t.value = void 0, p());
    }), V(t, (e) => {
      f("input", { target: i.value }, String(e ?? ""));
    });
    function b() {
      v.value = !0, f("focus", { target: i.value });
    }
    function p() {
      v.value = !1, f("blur", { target: i.value });
    }
    function $() {
      const e = t.value || 0, a = n.value ? e >= Number(n.value) : !0, m = l.value ? e <= Number(l.value) : !0, { currency: x, precision: c } = (r == null ? void 0 : r.value) || {};
      return y.value = a ? m ? "" : `below_maximum_amount|amount:${S(Number(l.value), x, c, c)}` : `above_minimum_amount|amount:${S(Number(n.value), x, c, c)}`, a && m;
    }
    return N({
      value: String(t.value || ""),
      formattedValue: M,
      focus: b,
      blur: p,
      checkValidity: $,
      validationMessage: y
    }), (e, a) => (U(), F("div", {
      class: "input-wrapper--money",
      ref_key: "inputContainerRef",
      ref: i
    }, [
      A(O, E({
        ref_key: "inputRef",
        ref: s,
        type: "text",
        modelValue: t.value,
        "onUpdate:modelValue": a[0] || (a[0] = (m) => t.value = m)
      }, { disabled: u(C), required: u(R), placeholder: u(w), min: u(n), max: u(l), currencyConfig: u(r) }, {
        onFocus: b,
        onBlur: p
      }), null, 16, ["modelValue"])
    ], 512));
  }
});
export {
  G as default
};
