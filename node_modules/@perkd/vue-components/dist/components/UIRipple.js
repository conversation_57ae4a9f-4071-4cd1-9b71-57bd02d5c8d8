import { defineComponent as _, ref as r, onUnmounted as x, createElementBlock as s, openBlock as c, Fragment as y, renderList as v, normalizeStyle as g } from "vue";
const k = { class: "ripple-container" }, B = /* @__PURE__ */ _({
  __name: "UIRipple",
  setup($, { expose: a }) {
    const n = r([]), l = r([]);
    function u(t) {
      const e = t.currentTarget.getBoundingClientRect(), o = Math.max(e.width, e.height), m = t.clientX - e.left - o / 2, h = t.clientY - e.top - o / 2, i = Date.now();
      n.value.push({
        key: i,
        style: {
          width: `${o}px`,
          height: `${o}px`,
          top: `${h}px`,
          left: `${m}px`
        }
      });
      const d = setTimeout(() => {
        n.value = n.value.filter((f) => f.key !== i);
      }, 600);
      l.value.push(d);
    }
    return x(() => {
      l.value.forEach((t) => clearTimeout(t)), l.value = [];
    }), a({
      createRipple: u
    }), (t, p) => (c(), s("div", k, [
      (c(!0), s(y, null, v(n.value, (e) => (c(), s("div", {
        class: "ripple",
        key: e.key,
        style: g(e.style)
      }, null, 4))), 128))
    ]));
  }
});
export {
  B as default
};
