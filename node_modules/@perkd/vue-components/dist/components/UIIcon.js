import { defineComponent as n, createElementBlock as r, openBlock as o, normalizeClass as t } from "vue";
const i = /* @__PURE__ */ n({
  __name: "UIIcon",
  props: {
    name: {
      type: String,
      require: !0
    },
    color: {
      type: String,
      require: !1
    }
  },
  setup(e) {
    return (c, a) => (o(), r("span", {
      class: t(["picon", `picon-${e.name}`, e.color || ""])
    }, null, 2));
  }
});
export {
  i as default
};
