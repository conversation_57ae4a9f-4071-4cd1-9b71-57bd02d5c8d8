import { defineComponent as _, ref as n, onUnmounted as d, createElementBlock as i, openBlock as l, createElementVNode as v, createVNode as a, renderSlot as m, createCommentVNode as k } from "vue";
import $ from "./UIIcon.js";
import h from "./UIRipple.js";
const C = { class: "selection-wrapper" }, w = {
  key: 0,
  class: "selection-content"
}, y = /* @__PURE__ */ _({
  __name: "UISelection",
  emits: ["click"],
  setup(E, { emit: u }) {
    const f = u, s = n(void 0), t = n(!1), o = n([]);
    function p(e) {
      var r;
      if (t.value) return;
      t.value = !0, (r = s.value) == null || r.createRipple(e);
      const c = setTimeout(() => {
        f("click", e), t.value = !1;
      }, 200);
      o.value.push(c);
    }
    return d(() => {
      o.value.forEach((e) => clearTimeout(e)), o.value = [];
    }), (e, c) => (l(), i("div", {
      class: "selection-container",
      onClick: p
    }, [
      v("div", C, [
        m(e.$slots, "icon"),
        e.$slots.content ? (l(), i("span", w, [
          m(e.$slots, "content")
        ])) : k("", !0),
        a($, { name: "arrow-forward" })
      ]),
      a(h, {
        ref_key: "rippleRef",
        ref: s
      }, null, 512)
    ]));
  }
});
export {
  y as default
};
