import { defineComponent as Z, toRefs as w, ref as b, shallowRef as F, defineAsyncComponent as R, h as _, computed as k, onMounted as ee, watch as ae, createElementBlock as te, openBlock as M, normalizeClass as ue, unref as n, createBlock as z, createCommentVNode as le, renderSlot as ne, resolveDynamicComponent as ie, mergeProps as oe, withModifiers as re } from "vue";
import { debounce as ce, isUrl as se, isEmail as ve } from "@perkd/applet-common/utils";
import de from "./UIButton.js";
import me from "./UIInputDate.js";
const he = /* @__PURE__ */ Z({
  __name: "UIInput",
  props: {
    modelValue: {
      type: String,
      default: ""
    },
    inputType: {
      type: String,
      default: "text"
    },
    inputClass: {
      type: String,
      default: ""
    },
    inputmode: {
      type: String,
      default: ""
    },
    value: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String,
      default: ""
    },
    clearable: {
      type: Boolean,
      default: !0
    },
    disabled: {
      type: Boolean,
      default: !1
    },
    readonly: {
      type: Boolean,
      default: !1
    },
    required: {
      type: Boolean,
      default: !1
    },
    autocapitalize: {
      type: String,
      default: "off"
    },
    autocomplete: {
      type: String,
      default: "off"
    },
    autocorrect: {
      type: String,
      default: "off"
    },
    spellcheck: {
      type: Boolean,
      default: !1
    },
    pattern: {
      type: String,
      default: ""
    },
    minlength: {
      type: String,
      default: ""
    },
    maxlength: {
      type: String,
      default: ""
    },
    min: {
      type: String,
      default: ""
    },
    max: {
      type: String,
      default: ""
    },
    inputOptions: {
      type: Object,
      default: () => ({})
    },
    validate: {
      type: Function
    }
  },
  emits: ["update:modelValue", "inputFocus", "inputBlur", "inputChange", "inputCheck"],
  setup(v, { expose: T, emit: q }) {
    const A = ["tel", "date", "datetime", "time", "money"], p = v, { disabled: B, required: x, readonly: P, clearable: U, inputType: d, value: N, modelValue: $, inputOptions: m } = w(p), { pattern: S, minlength: f, maxlength: y, min: g, max: h, inputmode: O } = w(p), e = b(($.value || N.value || "").trim()), o = F(void 0), C = F(void 0), r = b(!1), j = R({
      loader: () => import("./UIInputMobile.js"),
      errorComponent: _("input"),
      // Create a virtual node for the input element
      timeout: 3e3
      // Add timeout
    }), D = R({
      loader: () => import("./UIInputMoney.js"),
      errorComponent: _("input"),
      timeout: 3e3
    }), V = k(() => {
      switch (d.value) {
        case "tel":
          return j;
        case "date":
        case "datetime":
        case "time":
          return me;
        case "money":
          return D;
        case "textarea":
          return "textarea";
        default:
          return "input";
      }
    }), c = b(""), i = b(!0), L = k(() => {
      var l;
      return A.indexOf(d.value) !== -1 && ((l = o.value) == null ? void 0 : l.formattedValue) || e.value;
    }), Y = k(() => {
      var a;
      return o.value && "countryCode" in o.value ? (a = o.value) == null ? void 0 : a.countryCode : "";
    }), G = k(() => !B.value && U.value && !!e.value), u = q;
    ee(() => {
      var a, l;
      if (u("inputChange", e.value), u("update:modelValue", e.value), C.value && ((a = m == null ? void 0 : m.value) != null && a.rows)) {
        const t = getComputedStyle(C.value).fontSize;
        C.value.style.height = t ? `calc(${t} * 1.21 * ${(l = m.value) == null ? void 0 : l.rows} + var(--height-input) * 0.33 + 0.7em)` : "var(--height-input)";
      }
    }), ae([$, N], ([a, l]) => {
      var t;
      e.value = (t = a || l) == null ? void 0 : t.trim(), i.value = !0, c.value = "", u("inputChange", e.value), u("update:modelValue", e.value);
    });
    const H = ce((a) => {
      u("inputChange", a), u("update:modelValue", a);
    }, 100);
    function J(a, l) {
      var s;
      const t = a.target;
      e.value = (s = l || t.value) == null ? void 0 : s.trim(), H(e.value);
    }
    function E() {
      e.value = "", I(), u("inputChange", ""), u("update:modelValue", "");
    }
    function K(a) {
      r.value || (r.value = !0, u("inputFocus", a));
    }
    function Q(a) {
      r.value && (r.value = !1, u("inputBlur", a));
    }
    function I() {
      var a;
      r.value || (r.value = !0, (a = o.value) == null || a.focus());
    }
    function W() {
      var a;
      r.value && (r.value = !1, (a = o.value) == null || a.blur());
    }
    function X() {
      const l = [
        { check: () => !x.value || !!e.value, message: "is_required" },
        { check: () => !e.value || !f.value || e.value.length >= Number(f.value), message: `minimum_length|n:${f.value}` },
        { check: () => !e.value || !y.value || e.value.length <= Number(y.value), message: `maximum_length|n:${y.value}` },
        { check: () => e.value && S.value ? new RegExp(S.value).test(e.value) : !0, message: "invalid_pattern" },
        { check: () => !(V.value !== "input") && !!e.value && !isNaN(+e.value) && g.value ? Number(e.value) >= Number(g.value) : !0, message: `minimum_number|n:${g.value}` },
        { check: () => !(V.value !== "input") && !!e.value && !isNaN(+e.value) && h.value ? Number(e.value) <= Number(h.value) : !0, message: `maximum_number|n:${h.value}` }
      ].find((t) => !t.check());
      if (l)
        i.value = !1, c.value = l.message;
      else {
        const t = !x.value && !e.value;
        switch (d.value) {
          case "email":
            i.value = t || ve(e.value), c.value = i.value ? "" : "invalid";
            break;
          case "url":
            i.value = t || se(e.value), c.value = i.value ? "" : "invalid";
            break;
          case "date":
          case "datetime":
          case "time":
          case "tel":
          case "money":
            o.value && (i.value = t || o.value.checkValidity(), c.value = i.value ? "" : o.value.validationMessage || "");
            break;
        }
        if (p.validate) {
          const s = p.validate(e.value);
          i.value = s.isValid, c.value = s.isValid ? "" : s.validationMessage;
        }
      }
      return u("inputCheck", i.value, c.value), i.value;
    }
    return T({
      value: e,
      countryCode: Y,
      formattedValue: L,
      focus: I,
      blur: W,
      checkValidity: X,
      validationMessage: c,
      clear: E,
      setValue: (a) => {
        e.value = a, u("inputChange", a), u("update:modelValue", a);
      }
    }), (a, l) => (M(), te("div", {
      ref_key: "inputContainerRef",
      ref: C,
      class: ue(`input-container ${n(d)}-container`)
    }, [
      (M(), z(ie(V.value), oe({
        ref_key: "inputRef",
        ref: o,
        value: e.value,
        class: v.inputClass,
        type: n(d)
      }, { placeholder: v.placeholder, disabled: n(B), readonly: n(P), required: n(x), autocapitalize: v.autocapitalize, autocomplete: v.autocomplete, autocorrect: v.autocorrect, spellcheck: v.spellcheck, pattern: n(S), inputmode: n(O), minlength: n(f), maxlength: n(y), min: n(g), max: n(h), ...n(m) }, {
        onFocus: K,
        onBlur: Q,
        onInput: J
      }), null, 16, ["value", "class", "type"])),
      G.value ? (M(), z(de, {
        key: 0,
        icon: { name: "close" },
        type: "circle",
        onClick: re(E, ["stop"]),
        class: "clear-button",
        "aria-label": "Clear input"
      })) : le("", !0),
      ne(a.$slots, "customButton")
    ], 2));
  }
});
export {
  he as default
};
