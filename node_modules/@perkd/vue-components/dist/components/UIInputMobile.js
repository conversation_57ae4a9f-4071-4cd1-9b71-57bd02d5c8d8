import { defineComponent as J, toRefs as K, useAttrs as Q, ref as e, watch as X, createElement<PERSON>lock as Y, openBlock as Z, createVNode as y, unref as x, withCtx as V } from "vue";
import $ from "./UIIcon.js";
import { VueTelInput as ee } from "vue-tel-input";
import { useI18n as te } from "vue-i18n";
const ue = /* @__PURE__ */ J({
  __name: "UIInputMobile",
  props: {
    value: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      default: "tel"
    },
    disabled: {
      type: Boolean,
      default: !1
    },
    readonly: {
      type: Boolean,
      default: !1
    },
    required: {
      type: Boolean,
      default: !1
    },
    autocomplete: {
      type: String,
      default: "off"
    },
    pattern: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String,
      default: ""
    },
    showDialCode: {
      type: Boolean,
      default: !1
    },
    dropdownOptions: {
      type: Object
    },
    defaultCountry: {
      type: String,
      default: ""
    },
    preferredCountries: {
      type: Array,
      default: ["sg", "my", "id", "ph", "cn", "tw", "hk", "mo", "jp", "kr"]
    }
  },
  emits: ["input", "blur", "clearInput", "focus", "focusChange"],
  setup(l, { expose: k, emit: D }) {
    var O, B;
    const { t: R } = te(), _ = {
      showDialCodeInList: !0,
      showFlags: !0,
      showSearchBox: !0,
      searchBoxPlaceholder: R("form.search"),
      showDialCodeInSelection: !0
    }, A = l, { value: a, type: C, readonly: F, required: N, autocomplete: P, pattern: T, placeholder: U, showDialCode: q, dropdownOptions: f } = K(A), E = Q(), L = e({ type: C, readonly: F, required: N, autocomplete: P, pattern: T, placeholder: U, showDialCode: q, tabindex: E.tabindex }), M = e({ ..._, ...f == null ? void 0 : f.value }), u = e(((O = a.value.match(/[0-9]*/g)) == null ? void 0 : O.join("")) || ""), r = e(((B = a.value) == null ? void 0 : B[0]) === "+" ? a.value : a.value ? `+${a.value}` : ""), h = e(""), w = e(""), c = e(!1), g = e(!1), b = e(!1), s = e(!1), I = e(""), i = e(void 0), p = e(void 0), v = D;
    X(a, (t) => {
      t || (u.value = t, r.value = t, d());
    });
    function d() {
      var t;
      b.value = !0, v("focus", { target: p.value }), s.value || (t = i.value) == null || t.focus();
    }
    function m() {
      var t;
      b.value = !1, v("blur", { target: p.value }), (t = i.value) == null || t.blur();
    }
    function j(t, o) {
      const { countryCallingCode: n, nationalNumber: H, formatted: S } = o;
      u.value = `${n || ""}${H || ""}` || r.value, c.value = o.valid, g.value = o.possible, h.value = n && S ? `+${n || ""} ${S || ""}` : "", w.value = n, v("input", { target: i.value }, u.value);
    }
    function W() {
      return c.value || (I.value = g.value ? "" : "invalid"), c.value;
    }
    function z() {
      s.value = !0, d();
    }
    function G() {
      s.value = !1, m();
    }
    return k({
      value: u,
      formattedValue: h,
      countryCode: w,
      focus: d,
      blur: m,
      checkValidity: W,
      validationMessage: I
    }), (t, o) => (Z(), Y("div", {
      class: "input-wrapper--tel",
      ref_key: "inputContainerRef",
      ref: p
    }, [
      y(x(ee), {
        ref_key: "inputRef",
        ref: i,
        disabled: l.disabled,
        type: x(C),
        modelValue: r.value,
        "onUpdate:modelValue": o[0] || (o[0] = (n) => r.value = n),
        mode: "national",
        defaultCountry: l.defaultCountry,
        inputOptions: L.value,
        dropdownOptions: M.value,
        preferredCountries: l.preferredCountries,
        onFocus: d,
        onBlur: m,
        onOnInput: j,
        onOpen: z,
        onClose: G
      }, {
        "arrow-icon": V(() => [
          y($, {
            name: s.value ? "arrow-down" : "arrow-up",
            class: "country-code-arrow"
          }, null, 8, ["name"])
        ]),
        "search-icon": V(() => [
          y($, {
            name: "search",
            class: "search-icon"
          })
        ]),
        _: 1
      }, 8, ["disabled", "type", "modelValue", "defaultCountry", "inputOptions", "dropdownOptions", "preferredCountries"])
    ], 512));
  }
});
export {
  ue as default
};
