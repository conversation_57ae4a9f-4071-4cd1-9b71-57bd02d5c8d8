import { defineComponent as m, createElementBlock as a, openBlock as e, normalizeClass as i, createCommentVNode as n, createBlock as C, renderSlot as s, unref as y, toDisplayString as c } from "vue";
import { useI18n as g } from "vue-i18n";
import d from "./UIButton.js";
const v = ["theme"], h = {
  key: 2,
  class: "status"
}, B = {
  key: 3,
  class: "status"
}, I = /* @__PURE__ */ m({
  __name: "UINavigationBar",
  props: {
    navBack: {
      type: Object
    },
    theme: {
      type: String,
      default: "perkd"
    },
    leftClass: {
      type: String,
      default: ""
    },
    centerClass: {
      type: String,
      default: ""
    },
    rightClass: {
      type: String,
      default: ""
    },
    title: String,
    titleClass: {
      type: String,
      default: ""
    },
    isOnline: {
      type: Boolean,
      default: !0
    },
    status: {
      type: String,
      default: ""
    }
  },
  setup(t) {
    const { t: o } = g();
    return (l, S) => {
      var r, u, k, f;
      return e(), a("div", {
        class: i(["navigation-bar", t.isOnline ? "online" : "offline"]),
        theme: t.theme
      }, [
        l.$slots.leftContent || t.navBack || !t.isOnline || t.isOnline && t.status ? (e(), a("div", {
          key: 0,
          class: i("left-container " + t.leftClass)
        }, [
          t.navBack && ((r = t.navBack) == null ? void 0 : r.type) === "back" ? (e(), C(d, {
            key: 0,
            type: "clear",
            icon: { name: "back" },
            class: "back-button",
            onClick: (u = t.navBack) == null ? void 0 : u.onClick
          }, null, 8, ["onClick"])) : n("", !0),
          t.navBack && ((k = t.navBack) == null ? void 0 : k.type) === "cancel" ? (e(), C(d, {
            key: 1,
            type: "clear",
            title: y(o)("button.cancel"),
            onClick: (f = t.navBack) == null ? void 0 : f.onClick
          }, null, 8, ["title", "onClick"])) : n("", !0),
          t.isOnline ? n("", !0) : (e(), a("span", h, c(y(o)("error.offline_status")), 1)),
          t.isOnline && t.status ? (e(), a("span", B, c(t.status), 1)) : n("", !0),
          s(l.$slots, "leftContent")
        ], 2)) : n("", !0),
        l.$slots.centerContent || t.title ? (e(), a("div", {
          key: 1,
          class: i("center-container " + t.centerClass)
        }, [
          t.title ? (e(), a("div", {
            key: 0,
            class: i("navigation-title " + t.titleClass)
          }, c(t.title), 3)) : n("", !0),
          s(l.$slots, "centerContent")
        ], 2)) : n("", !0),
        l.$slots.rightContent ? (e(), a("div", {
          key: 2,
          class: i("right-container " + t.rightClass)
        }, [
          s(l.$slots, "rightContent")
        ], 2)) : n("", !0)
      ], 10, v);
    };
  }
});
export {
  I as default
};
