import { defineComponent as q, toRefs as V, ref as b, computed as o, onMounted as F, watch as W, nextTick as L, createElementBlock as l, openBlock as i, normalizeStyle as I, unref as t, createElementVNode as d, createCommentVNode as B, withDirectives as k, vShow as G, toDisplayString as m, normalizeClass as P } from "vue";
import U from "jsbarcode";
import K from "qrcode";
import { formatDateTime as v } from "@perkd/format-datetime";
import { useI18n as $ } from "vue-i18n";
import { isUrl as _ } from "@perkd/applet-common/utils";
import { Cards as Z } from "@perkd/applet-common/types/cards";
const AA = "data:image/png;base64,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", eA = ["src"], tA = ["src"], cA = ["id"], aA = ["innerHTML"], gA = {
  key: 0,
  class: "card-date-join"
}, uA = { class: "label" }, dA = { class: "date" }, rA = { class: "label" }, oA = { class: "date" }, bA = /* @__PURE__ */ q({
  __name: "UICard",
  props: {
    image: {
      type: String,
      require: !0
    },
    placeholder: {
      type: String,
      require: !0,
      default: AA
    },
    width: {
      type: Number,
      default: parseFloat(getComputedStyle(document.documentElement).getPropertyValue("--width-card")) || 28.8
    },
    cardNumber: {
      type: String,
      default: ""
    },
    barcodeType: {
      type: String,
      default: ""
    },
    barcodePatterns: {
      type: String,
      default: ""
    },
    startTime: {
      type: String,
      default: ""
    },
    endTime: {
      type: String,
      default: ""
    }
  },
  setup(e, { expose: h }) {
    const { t: M } = $(), g = e, { width: N, barcodeType: O, cardNumber: a } = V(g), w = parseInt(getComputedStyle(document.documentElement).getPropertyValue("--font-size-base")) || 10, E = document.documentElement.getAttribute("lang"), C = b(!1), n = b(N.value * w), A = b(N.value * w / 1.6), s = b(0), p = b(""), T = o(() => `${O.value || "card"}-${a.value.replace(/[^a-zA-Z0-9]/g, "")}`), D = o(() => Z.CODE_SQUARE.includes(O.value)), H = o(() => a.value && (_(a.value) || a.value.length > 30)), Y = o(() => !(s.value / 180 % 2)), f = o(() => ({
      width: `${n.value}px`,
      height: `${A.value}px`,
      borderRadius: `${A.value / 10}px`,
      transform: `rotateY(${s.value}deg)`
    })), R = o(() => {
      const r = E && E.startsWith("zh"), c = E && ["ja", "ko"].includes(E), u = r ? 0.08 : c ? 0.07 : 0.1;
      return {
        fontSize: `${A.value * u}px`,
        padding: `${A.value * 0.045}px ${A.value * 0.08}px`
      };
    }), S = o(() => g.barcodeType ? D.value ? {
      height: A.value * (H.value ? 0.75 : 0.6) + "px",
      marginTop: A.value * 0.02 + "px"
    } : {
      height: A.value * 0.42 + "px",
      marginTop: A.value * 0.08 + "px"
    } : {
      height: A.value * 0.24 + "px",
      marginTop: A.value * 0.15 + "px",
      marginBottom: A.value * 0.075 + "px",
      backgroundColor: "var(--color-background-maximal)"
    }), y = o(() => ({
      fontSize: A.value * (D.value ? 0.12 : 0.17) + "px",
      lineHeight: D.value ? 1.3 : 1.6,
      padding: `0 ${A.value * 0.08}px`,
      marginTop: D.value && !H.value ? -A.value * 0.02 + "px" : 0
    }));
    function X(r) {
      const c = r.target, u = c.width / c.height, Q = n.value / A.value;
      u > Q ? (n.value = Math.min(c.width, n.value), A.value = n.value / u) : (A.value = Math.min(c.height, A.value), n.value = A.value * u), C.value = !0;
    }
    function z(r = void 0, c = !1) {
      const { cardNumber: u, startTime: Q, endTime: J } = g, x = r ? r.offsetX < n.value / 2 : c;
      (u || Q || J) && (s.value = x ? s.value - 180 : s.value + 180);
    }
    F(() => {
      j();
    }), W([a, O], () => {
      L(() => {
        j();
      });
    });
    async function j() {
      g.cardNumber && g.barcodeType && (g.barcodeType !== Z.Barcode.QRCODE ? U(`#${T.value}`, g.cardNumber, {
        format: g.barcodeType,
        width: 4,
        height: 100,
        displayValue: !1
      }) : p.value = await K.toString(g.cardNumber, { type: "svg" }));
    }
    return h({
      isFront: Y,
      flipCard: z
    }), (r, c) => (i(), l("div", {
      theme: "light",
      class: "card-container",
      style: I({ width: `${t(N) * t(w)}px` })
    }, [
      d("div", {
        class: "card",
        style: I(f.value),
        onClick: c[0] || (c[0] = (u) => r.$attrs.onClick || z(u))
      }, [
        d("div", {
          class: "card-front",
          style: I({ borderRadius: `${A.value / 10}px` })
        }, [
          k(d("img", {
            src: e.image,
            onLoad: X
          }, null, 40, eA), [
            [G, C.value]
          ]),
          k(d("img", { src: e.placeholder }, null, 8, tA), [
            [G, !C.value]
          ])
        ], 4),
        t(a) || e.startTime || e.endTime ? (i(), l("div", {
          key: 0,
          class: "card-back",
          style: I({ borderRadius: `${A.value / 10}px` })
        }, [
          d("div", {
            class: "card-code",
            style: I(S.value)
          }, [
            t(a) && !D.value ? (i(), l("svg", {
              key: 0,
              id: T.value,
              class: "barcode"
            }, null, 8, cA)) : B("", !0),
            t(a) && D.value ? (i(), l("div", {
              key: 1,
              class: "qrcode",
              innerHTML: p.value
            }, null, 8, aA)) : B("", !0)
          ], 4),
          t(a) && !H.value ? (i(), l("div", {
            key: 0,
            class: "card-number",
            style: I(y.value)
          }, m(t(a)), 5)) : B("", !0),
          e.startTime || e.endTime ? (i(), l("div", {
            key: 1,
            class: "card-dates",
            style: I(R.value)
          }, [
            e.startTime ? (i(), l("div", gA, [
              d("span", uA, m(t(M)("form.join")), 1),
              d("span", dA, m(t(v)(e.startTime).format("ll")), 1)
            ])) : B("", !0),
            e.endTime ? (i(), l("div", {
              key: 1,
              class: P(["card-date-expire", { expired: t(v)(e.endTime).isBefore() }])
            }, [
              d("span", rA, m(t(M)("form.expire")), 1),
              d("span", oA, m(t(v)(e.endTime).format("ll")), 1)
            ], 2)) : B("", !0)
          ], 4)) : B("", !0)
        ], 4)) : B("", !0)
      ], 4)
    ], 4));
  }
});
export {
  bA as default
};
