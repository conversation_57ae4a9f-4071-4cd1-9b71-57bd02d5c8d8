import { defineComponent as J, toRefs as K, ref as a, computed as w, onMounted as Y, watch as Z, createElementBlock as y, openBlock as u, normalizeClass as g, unref as _, createElementVNode as C, normalizeStyle as E, createBlock as k, createCommentVNode as c, toDisplayString as q, withModifiers as z, renderSlot as ee } from "vue";
import R from "./UIIcon.js";
import te from "./UILoading.js";
import { colorToRgbo as V } from "@perkd/applet-common/utilsColor";
const le = {
  key: 1,
  class: "button-title-container"
}, ne = { class: "slide-thumb-content" }, se = /* @__PURE__ */ J({
  __name: "UISlideButton",
  props: {
    color: {
      type: String,
      default: "accent"
    },
    icon: {
      type: Object,
      required: !1
    },
    title: String,
    titleClass: {
      type: String,
      default: ""
    },
    subtitle: String,
    subtitleClass: {
      type: String,
      default: ""
    },
    startColor: {
      type: String,
      default: ""
    },
    endColor: {
      type: String,
      default: ""
    },
    threshold: {
      type: Number,
      default: 0.6
    },
    disabled: {
      type: Boolean,
      default: !1
    },
    proceeding: {
      type: <PERSON>olean,
      default: !1
    }
  },
  emits: ["confirmClick", "qualifyClick"],
  setup(e, { emit: I }) {
    const N = e, { startColor: W, endColor: A, threshold: x, disabled: p, proceeding: F } = K(N), m = a(null), r = a(null), f = a(0), n = a(0), M = a(0), v = a(!1), h = a(F.value), d = a(null), S = a(!1), B = w(() => n.value >= f.value * x.value), j = w(() => {
      const l = document.documentElement, t = V(W.value || getComputedStyle(l).getPropertyValue("--color-text-neutral").trim() || "#000000"), o = V(A.value || getComputedStyle(l).getPropertyValue("--color-text-contrast").trim() || "#FFFFFF"), i = n.value / (f.value * x.value), s = Math.floor(t.r + (o.r - t.r) * i), b = Math.floor(t.g + (o.g - t.g) * i), G = Math.floor(t.b + (o.b - t.b) * i), H = Math.floor(t.o + (o.o - t.o) * i);
      return `rgb(${s}, ${b}, ${G}, ${H})`;
    }), P = I;
    Y(() => {
      m.value && r.value && (f.value = m.value.offsetWidth - r.value.offsetWidth - r.value.offsetLeft * 2);
    }), Z(F, (l, t) => {
      l === !1 && t === !0 && (X(), h.value = !1);
    });
    function D(l) {
      l.touches.length > 1 || p.value || (M.value = l.touches[0].clientX, v.value = !0);
    }
    function L(l) {
      if (!m.value || !v.value || p.value) return;
      d.value !== null && (cancelAnimationFrame(d.value), d.value = null);
      const t = l.touches[0], o = m.value.getBoundingClientRect();
      if (!U(o, t.clientX, t.clientY)) {
        T();
        return;
      }
      const s = t.clientX - M.value;
      n.value = s > 0 ? Math.min(s, f.value) : 0, B.value || (S.value = !1), B.value && !S.value && (S.value = !0, P("qualifyClick")), $(n.value);
    }
    function O() {
      !v.value || !f.value || (d.value !== null && (cancelAnimationFrame(d.value), d.value = null), B.value ? Q() : T());
    }
    function T() {
      X();
    }
    function Q() {
      n.value = f.value, $(n.value), h.value = !0, v.value = !1, P("confirmClick");
    }
    function X() {
      n.value = 0, $(n.value), v.value = !1;
    }
    function U(l, t, o) {
      const { left: i, top: s, height: b } = l;
      return t >= i && o >= s - b / 2 && o <= s + b * 2;
    }
    function $(l) {
      r.value && (r.value.style.transform = `translateX(${l}px)`);
    }
    return (l, t) => (u(), y("div", {
      ref_key: "slideButtonRef",
      ref: m,
      class: g(["slide-button", { active: v.value, proceeding: h.value, disabled: _(p) }])
    }, [
      C("div", {
        class: "progress",
        style: E({ width: n.value ? `calc(${n.value}px + var(--size-button) / 2)` : 0 })
      }, null, 4),
      C("div", {
        class: "slide-button-content",
        style: E({ color: j.value })
      }, [
        e.icon && e.icon.position !== "right" ? (u(), k(R, {
          key: 0,
          name: e.icon.name,
          class: g(`button-icon ${e.icon.class || ""}`)
        }, null, 8, ["name", "class"])) : c("", !0),
        e.title || e.subtitle ? (u(), y("span", le, [
          e.title ? (u(), y("span", {
            key: 0,
            class: g("button-title " + e.titleClass)
          }, q(e.title), 3)) : c("", !0),
          e.subtitle ? (u(), y("span", {
            key: 1,
            class: g("button-subtitle " + e.subtitleClass)
          }, q(e.subtitle), 3)) : c("", !0)
        ])) : c("", !0),
        e.icon && e.icon.position === "right" ? (u(), k(R, {
          key: 2,
          name: e.icon.name,
          class: g("button-icon " + e.icon.class)
        }, null, 8, ["name", "class"])) : c("", !0)
      ], 4),
      C("div", {
        ref_key: "slideThumbRef",
        ref: r,
        class: "slide-thumb",
        onTouchstartPassive: D,
        onTouchmovePassive: L,
        onTouchend: z(O, ["stop", "prevent"]),
        onTouchcancel: z(T, ["stop", "prevent"])
      }, [
        C("div", ne, [
          ee(l.$slots, "status", {}, () => [
            h.value ? c("", !0) : (u(), k(R, {
              key: 0,
              name: "arrow-forward"
            })),
            h.value ? (u(), k(te, {
              key: 1,
              colorBackground: "",
              size: "1.5em",
              thickness: "xxs"
            })) : c("", !0)
          ])
        ])
      ], 544)
    ], 2));
  }
});
export {
  se as default
};
