import { defineComponent as S, toRefs as X, ref as u, onMounted as T, createElementBlock as _, openBlock as $, createElementVNode as M, toDisplayString as g, unref as b } from "vue";
const k = /* @__PURE__ */ S({
  __name: "UIMarquee",
  props: {
    text: {
      type: String,
      required: !0
    },
    speed: {
      type: Number,
      default: 25
      // 25px per second
    },
    delay: {
      type: Number,
      default: 2
    }
  },
  setup(p) {
    const d = p, { text: y, speed: i, delay: c } = X(d), o = u(null), e = u(null), r = u(!1), v = u(0), n = u(0), a = u(0);
    T(() => {
      if (o.value && e.value) {
        if (a.value = e.value.clientWidth - o.value.clientWidth, r.value = a.value > 0, !r.value) return;
        const t = a.value / i.value;
        e.value.style.transition = `all ${t}s linear ${c.value}s`, e.value.style.transform = `translateX(-${a.value}px)`;
      }
    });
    function h(t) {
      r.value && o.value && e.value && (e.value.style.animationPlayState = "paused", v.value = t.touches[0].clientX, n.value = m(), e.value.style.transform = `translateX(${n.value}px)`, e.value.style.transition = "none");
    }
    function x(t) {
      if (r.value && t.touches.length === 1 && e.value) {
        const l = t.touches[0].clientX - v.value, s = Math.min(Math.max(-a.value, n.value + l), 0);
        e.value.style.transform = `translateX(${s}px)`;
      }
    }
    function f() {
      if (r.value && o.value && e.value && (n.value = m(), Math.abs(n.value) < a.value)) {
        const t = (a.value - Math.abs(n.value)) / i.value;
        e.value.style.transition = `all ${t}s linear ${c.value}s`, e.value.style.transform = `translateX(-${a.value}px)`, e.value.style.animationPlayState = "running";
      }
    }
    function m() {
      if (!e.value) return 0;
      const l = getComputedStyle(e.value).transform;
      if (l === "none" || !l)
        return 0;
      const s = l.match(/matrix\((.+)\)/);
      return s && s[1].split(",").map(parseFloat)[4] || 0;
    }
    return (t, l) => ($(), _("div", {
      ref_key: "containerRef",
      ref: o,
      class: "marquee-container"
    }, [
      M("div", {
        ref_key: "contentRef",
        ref: e,
        class: "marquee-content",
        onTouchstartPassive: h,
        onTouchmovePassive: x,
        onTouchcancel: f,
        onTouchend: f
      }, g(b(y)), 545)
    ], 512));
  }
});
export {
  k as default
};
