import { defineComponent as Z, toRefs as G, ref as D, computed as y, onMounted as J, onBeforeUnmount as P, watch as Q, createElementBlock as f, openBlock as v, normalizeClass as X, createCommentVNode as d, renderSlot as $, unref as e, toDisplayString as n, createElementVNode as t } from "vue";
import { useI18n as Y } from "vue-i18n";
import { formatDateTime as ee } from "@perkd/format-datetime";
const te = {
  key: 0,
  class: "js-countdown-offline"
}, ne = { class: "js-countdown-before" }, oe = { class: "prefix" }, se = { class: "end-date" }, ae = { class: "suffix" }, ue = {
  key: 2,
  class: "js-countdown-ing"
}, le = { class: "prefix" }, ie = { class: "js-countdown-timer" }, ce = {
  key: 0,
  class: "time-block time-block-days"
}, re = { class: "time-value" }, de = { class: "time-unit" }, me = {
  key: 1,
  class: "time-block time-block-hours"
}, fe = { class: "time-value" }, ve = { class: "time-unit" }, pe = {
  key: 2,
  class: "time-block time-block-mins"
}, _e = { class: "time-value" }, he = { class: "time-unit" }, we = {
  key: 3,
  class: "time-block time-block-secs"
}, ge = { class: "time-value secs" }, be = { class: "time-unit" }, Ce = { class: "suffix" }, ke = { class: "js-countdown-after" }, Te = /* @__PURE__ */ Z({
  __name: "CountDown",
  props: {
    serverTimeRequired: {
      type: Boolean,
      default: !0
    },
    serverTime: {
      type: [Number, String],
      validator: function(m) {
        return new Date(m).toString() !== "Invalid Date";
      }
    },
    endCountTime: {
      required: !0,
      type: [Number, String],
      validator: function(m) {
        return new Date(m).toString() !== "Invalid Date";
      }
    },
    countDistance: {
      type: Number,
      default: 2 * 24 * 60 * 60 * 1e3
      // '2 days'
    },
    timerFormat: {
      type: String,
      default: "auto",
      validator: function(m) {
        return !![
          "auto",
          "dd-hh-mm-ss",
          "hh-mm-ss",
          "mm-ss",
          "auto-mm-ss",
          "auto-hh-mm-ss"
        ].find((x) => x === m);
      }
    },
    countUpAfterCountDown: {
      type: Boolean,
      default: !1
    }
  },
  emits: ["endCountDown"],
  setup(m, { expose: w, emit: x }) {
    const { t: u } = Y(), L = m, { serverTimeRequired: O, serverTime: a, endCountTime: g, countDistance: V, timerFormat: M, countUpAfterCountDown: b } = G(L), c = D(), p = D(!1), l = D(new Date(g.value).getTime() - (a != null && a.value ? new Date(a == null ? void 0 : a.value).getTime() : (/* @__PURE__ */ new Date()).getTime())), I = D(0), q = y(() => l.value > V.value), F = y(() => l.value <= V.value && l.value >= 1e3 || b.value), S = y(() => l.value < 1e3), A = x;
    J(() => {
      S.value ? (A("endCountDown"), b.value && j(!0)) : c.value = setInterval(N, 1e3);
    }), P(() => {
      c.value && clearInterval(c.value);
    }), Q(g, (o) => {
      o && (l.value = new Date(g.value).getTime() - (a != null && a.value ? new Date(a == null ? void 0 : a.value).getTime() : (/* @__PURE__ */ new Date()).getTime()), l.value > 0 && p.value && j(!1));
    });
    const r = y(() => {
      const o = p.value ? I.value : l.value;
      if (o < 0) return {};
      const i = {
        dd: Math.floor(o / (1e3 * 60 * 60 * 24)),
        hh: Math.floor(o % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60)),
        mm: Math.floor(o % (1e3 * 60 * 60) / (1e3 * 60)),
        ss: Math.floor(o % (1e3 * 60) / 1e3)
      }, T = (s) => s <= 9 ? `0${s}` : `${s}`, B = Object.keys(i).findIndex((s) => i[s] !== 0), U = B === -1 ? Object.keys(i).length - 1 : B, H = M.value.startsWith("auto"), C = 4 - M.value.split("-").filter((s) => s !== "auto").length, K = [24, 60, 60, 60], z = Array(C).fill(0).reduce((s, k, _) => {
        const h = Object.keys(i)[_];
        return s += K[_] * i[h], s;
      }, 0);
      return Object.keys(i).reduce((s, k, _) => {
        const h = i[k];
        if (H && U < C) {
          const W = h === 0 && _ < U;
          s[k] = W ? "" : T(h);
        } else
          s[k] = _ < C ? "" : T(_ === C ? h + z : h);
        return s;
      }, {});
    });
    function j(o) {
      const i = Math.abs(l.value) < 1e3 ? 1e3 : 0;
      p.value = o, I.value = o ? Math.abs(l.value) + i : 0, c.value && clearInterval(c.value), c.value = setInterval(o ? R : N, 1e3);
    }
    function N() {
      l.value < 1e3 ? (clearInterval(c.value), A("endCountDown"), b.value && j(!0)) : l.value -= 1e3;
    }
    function R() {
      I.value += 1e3;
    }
    function E() {
      c.value && clearInterval(c.value);
    }
    return w({
      stopCount: E
    }), (o, i) => (v(), f("span", {
      class: X({ "js-countdown-container": !0, error: p.value })
    }, [
      !e(a) && e(O) ? (v(), f("span", te, n(e(u)("error.failed_to_get_server_time")), 1)) : d("", !0),
      q.value ? $(o.$slots, "beforeCount", { key: 1 }, () => [
        t("span", ne, [
          t("span", oe, n(e(u)("countdown.before.prefix")), 1),
          t("span", se, n(e(ee)(e(g)).format("LL")), 1),
          t("span", ae, n(e(u)("countdown.before.suffix")), 1)
        ])
      ]) : d("", !0),
      F.value ? (v(), f("span", ue, [
        t("span", le, n(p.value ? e(u)("countdown.counting_up.prefix") : e(u)("countdown.counting_down.prefix")), 1),
        t("span", ie, [
          r.value.dd ? (v(), f("span", ce, [
            t("span", re, n(r.value.dd), 1),
            t("span", de, n(e(u)("countdown.time_unit.dd")), 1)
          ])) : d("", !0),
          r.value.hh ? (v(), f("span", me, [
            t("span", fe, n(r.value.hh), 1),
            t("span", ve, n(e(u)("countdown.time_unit.hh")), 1)
          ])) : d("", !0),
          r.value.mm ? (v(), f("span", pe, [
            t("span", _e, n(r.value.mm), 1),
            t("span", he, n(e(u)("countdown.time_unit.mm")), 1)
          ])) : d("", !0),
          r.value.ss ? (v(), f("span", we, [
            t("span", ge, n(r.value.ss), 1),
            t("span", be, n(e(u)("countdown.time_unit.ss")), 1)
          ])) : d("", !0)
        ]),
        t("span", Ce, n(p.value ? e(u)("countdown.counting_up.suffix") : e(u)("countdown.counting_down.suffix")), 1)
      ])) : d("", !0),
      S.value && !e(b) ? $(o.$slots, "afterCount", { key: 3 }, () => [
        t("span", ke, n(e(u)("countdown.after")), 1)
      ]) : d("", !0)
    ], 2));
  }
});
export {
  Te as default
};
