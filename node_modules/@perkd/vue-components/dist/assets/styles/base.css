:root {


  --spacing-xxs: 0.2rem;
  --spacing-xs: 0.4rem;
  --spacing-sm: 0.8rem;
  --spacing-md: 1.6rem;
  --spacing-lg: 2.4rem;
  --spacing-xl: 3.2rem;

  --width-min: 44px;
  --height-statusBar: 44px;
  --height-navigationBar: 88px;
  --height-tabBar: 83px;
  --height-minBottom: 34px;

  --device-ios: true;
  --device-longScreen: true;

  --width-card: 28.8rem;
  --size-button: 2.2em;
  --height-input: 3em;

  --font-size-base: 10px;
}

[theme='light'] {
  --color-background-primary: #FFB300;
  --color-background-accent: #39B5FF;
  --color-background-success: #00BC0C;
  --color-background-warning: #FFF176;
  --color-background-error: #FF0000;
  --color-background-accent-blur: rgba(221, 242, 255, 0.8);

  --color-background-lighter: rgba(255, 255, 255, 0.9);
  --color-background-neutral: #FAFAFA;
  --color-background-darker: rgba(0, 0, 0, 0.06);
  --color-background-strong: rgba(0, 0, 0, 0.1);
  --color-background-heavy: rgba(0, 0, 0, 0.3);
  --color-background-maximal: rgba(0, 0, 0, 0.42);
  --color-background-neutral-blur: rgba(250, 250, 250, 0.8);
  --color-background-disable-blur: rgba(228, 228, 228, 0.8);

  --color-text-primary: #E8A300;
  --color-text-accent: #0AA3FF;
  --color-text-success: #00BC0C;
  --color-text-warning: #CFBA00;
  --color-text-error: #EC0000;

  --color-text-neutral: #000000;
  --color-text-soft: rgba(0, 0, 0, 0.5);
  --color-text-subtle: rgba(0, 0, 0, 0.35);
  --color-text-muted: rgba(0, 0, 0, 0.2);
  --color-text-minimal: rgba(0, 0, 0, 0.1);
  --color-text-contrast: #FFFFFF;
  --color-text-in-theme-background: #FFFFFF;

  --color-ripple: rgba(255, 255, 255, 0.3);
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-overlay-blur: rgba(0, 0, 0, 0.6);
  --color-overlay: rgba(0, 0, 0, 0.8);
  --color-text-opacity: 0.75;
}

[theme='dark'] {
  --color-background-primary: #FFB300;
  --color-background-accent: #0AA3FF;
  --color-background-success: #00C20D;
  --color-background-warning: #F9E000;
  --color-background-error: #F00000;
  --color-background-accent-blur: rgba(49, 76, 93, 0.8);

  --color-background-lighter: rgba(0, 0, 0, 0.15);
  --color-background-neutral: #3D3D3D;
  --color-background-darker: rgba(255, 255, 255, 0.06);
  --color-background-strong: rgba(255, 255, 255, 0.15);
  --color-background-heavy: rgba(255, 255, 255, 0.3);
  --color-background-maximal: rgba(255, 255, 255, 0.42);
  --color-background-neutral-blur: rgba(61, 61, 61, 0.8);
  --color-background-disable-blur: rgba(80, 80, 80, 0.8);

  --color-text-primary: #E8A300;
  --color-text-accent: #0AA3FF;
  --color-text-success: #00D30E;
  --color-text-warning: #FFEC43;
  --color-text-error: #FF0000;

  --color-text-neutral: #FFFFFF;
  --color-text-soft: rgba(255, 255, 255, 0.75);
  --color-text-subtle: rgba(255, 255, 255, 0.6);
  --color-text-muted: rgba(255, 255, 255, 0.35);
  --color-text-minimal: rgba(255, 255, 255, 0.14);
  --color-text-contrast: #000000;
  --color-text-in-theme-background: #FFFFFF;

  --color-ripple: rgba(255, 255, 255, 0.3);
  --color-shadow: rgba(255, 255, 255, 0.2);
  --color-overlay-blur: rgba(0, 0, 0, 0.6);
  --color-overlay: rgba(0, 0, 0, 0.8);
  --color-text-opacity: 0.6;
}

[theme='perkd'] {
  --color-background-primary: #FAFAFA;
  --color-background-accent: #4FBDFF;
  --color-background-success: #09C615;
  --color-background-warning: #FFF069;
  --color-background-error: #FF1C1C;

  --color-background-lighter: rgba(255, 255, 255, 0.1);
  --color-background-neutral: #FFB300;
  --color-background-darker: rgba(0, 0, 0, 0.06);
  --color-background-strong: rgba(0, 0, 0, 0.10);
  --color-background-heavy: rgba(0, 0, 0, 0.2);
  --color-background-maximal: rgba(0, 0, 0, 0.36);

  --color-text-primary: #FAFAFA;
  --color-text-accent: #0AA3FF;
  --color-text-success: #00BC0C;
  --color-text-warning: #FFF069;
  --color-text-error: #D30000;

  --color-text-neutral: #FFFFFF;
  --color-text-soft: rgba(255, 255, 255, 0.82);
  --color-text-subtle: rgba(255, 255, 255, 0.7);
  --color-text-muted: rgba(255, 255, 255, 0.55);
  --color-text-minimal: rgba(255, 255, 255, 0.4);

  --color-ripple: rgba(255, 255, 255, 0.3);
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-overlay: rgba(0, 0, 0, 0.8);
  --color-text-opacity: 0.75;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
}

html {
  font-family: Melbourne, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
    Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: var(--font-size-base);
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: none;
  touch-action: manipulation;
}

body {
  min-height: 100vh;
  font-size: 1.6rem;
}

input,
textarea,
button,
select {
  -webkit-appearance: none;
  appearance: none;
  font-family: inherit;
  font-size: inherit;
}

textarea {
  resize: none;
}

a {
  text-decoration: none;
  color: var(--color-text-accent);
  transition: 0.4s;
}

.primary {
  color: var(--color-text-primary);
}

.accent {
  color: var(--color-text-accent);
}

.success {
  color: var(--color-text-success);
}

.warning {
  color: var(--color-text-warning);
}

.error {
  color: var(--color-text-error);
}

.blur {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

html[theme="light"],
html[theme="dark"] {
  color: var(--color-text-neutral);
  background: var(--color-background-neutral);
}