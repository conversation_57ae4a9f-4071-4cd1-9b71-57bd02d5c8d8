.picon {
  font-family: 'picon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.picon-badge-1:before {
  content: "\e600";
}
.picon-badge-2:before {
  content: "\e601";
}
.picon-alert:before {
  content: "\e602";
}
.picon-camera:before {
  content: "\e603";
}
.picon-lock:before {
  content: "\e604";
}
.picon-rotate:before {
  content: "\e605";
}
.picon-list-small-card:before {
  content: "\e606";
}
.picon-list-big-card:before {
  content: "\e607";
}
.picon-list:before {
  content: "\e608";
}
.picon-map:before {
  content: "\e609";
}
.picon-car:before {
  content: "\e60a";
}
.picon-walk:before {
  content: "\e60b";
}
.picon-location-1:before {
  content: "\e60c";
}
.picon-location-2:before {
  content: "\e60d";
}
.picon-add-location:before {
  content: "\e60e";
}
.picon-sort-abc:before {
  content: "\e60f";
}
.picon-sort-custom:before {
  content: "\e610";
}
.picon-sort-location:before {
  content: "\e611";
}
.picon-add-card:before {
  content: "\e612";
}
.picon-check:before {
  content: "\e618";
}
.picon-close:before {
  content: "\e613";
}
.picon-back:before {
  content: "\e614";
}
.picon-next:before {
  content: "\e615";
}
.picon-search:before {
  content: "\e616";
}
.picon-edit:before {
  content: "\e617";
}
.picon-direction:before {
  content: "\e619";
}
.picon-i:before {
  content: "\e9a1";
}
.picon-info-outline:before {
  content: "\e621";
}
.picon-info:before {
  content: "\e61b";
}
.picon-clock:before {
  content: "\e61d";
}
.picon-phone:before {
  content: "\e61e";
}
.picon-share:before {
  content: "\e61f";
}
.picon-email:before {
  content: "\e61a";
}
.picon-message:before {
  content: "\e620";
}
.picon-drag-handle:before {
  content: "\e622";
}
.picon-nearest:before {
  content: "\e625";
}
.picon-cloud:before {
  content: "\e626";
}
.picon-wifi-1:before {
  content: "\e627";
}
.picon-wifi-2:before {
  content: "\e628";
}
.picon-wifi-3:before {
  content: "\e629";
}
.picon-wifi-4:before {
  content: "\e62a";
}
.picon-wifi:before {
  content: "\e9ba";
}
.picon-card-library:before {
  content: "\e62b";
}
.picon-discover-outline:before {
  content: "\e62c";
}
.picon-discover:before {
  content: "\e62d";
}
.picon-card-outline:before {
  content: "\e62e";
}
.picon-card:before {
  content: "\e62f";
}
.picon-user-outline:before {
  content: "\e630";
}
.picon-user:before {
  content: "\e631";
}
.picon-setting-outline:before {
  content: "\e632";
}
.picon-setting:before {
  content: "\e633";
}
.picon-scan-outline:before {
  content: "\e634";
}
.picon-scan:before {
  content: "\e635";
}
.picon-scan-barcode:before {
  content: "\e9b7";
}
.picon-scan-anything:before {
  content: "\e988";
}
.picon-badge-3:before {
  content: "\e636";
}
.picon-barcode:before {
  content: "\e637";
}
.picon-checkin:before {
  content: "\e638";
}
.picon-membership:before {
  content: "\e639";
}
.picon-member:before {
  content: "\e9bb";
}
.picon-member-outline:before {
  content: "\e9bc";
}
.picon-member-female:before {
  content: "\e9be";
}
.picon-member-outline-female:before {
  content: "\e9bf";
}
.picon-member-male:before {
  content: "\e9c0";
}
.picon-member-outline-male:before {
  content: "\e9c1";
}
.picon-arrow:before {
  content: "\e63a";
}
.picon-refresh:before {
  content: "\e63b";
}
.picon-wave-1:before {
  content: "\e63c";
}
.picon-wave-2:before {
  content: "\e63d";
}
.picon-wave-3:before {
  content: "\e63e";
}
.picon-wave-4:before {
  content: "\e63f";
}
.picon-wave:before {
  content: "\e9bd";
}
.picon-wave-circle:before {
  content: "\e9c2";
}
.picon-camera-button:before {
  content: "\ef02";
}
.picon-camera-ready:before {
  content: "\ef03";
}
.picon-camera-close:before {
  content: "\ef01";
}
.picon-photo-crop:before {
  content: "\ef04";
}
.picon-photo-rotate:before {
  content: "\ef05";
}
.picon-flash-off:before {
  content: "\ef06";
}
.picon-flash-auto:before {
  content: "\ef07";
}
.picon-flash-on:before {
  content: "\ef08";
}
.picon-item-add:before {
  content: "\e641";
}
.picon-item-minus:before {
  content: "\e642";
}
.picon-plus-rectangle:before {
  content: "\e975";
}
.picon-minus-rectangle:before {
  content: "\e974";
}
.picon-url-filled:before {
  content: "\e903";
}
.picon-url:before {
  content: "\e61c";
}
.picon-order-filled:before {
  content: "\e907";
}
.picon-order:before {
  content: "\e643";
}
.picon-order-add:before {
  content: "\e644";
}
.picon-bag-filled:before {
  content: "\e906";
}
.picon-shopping-bag:before {
  content: "\e640";
}
.picon-tag:before {
  content: "\e976";
}
.picon-offer-filled:before {
  content: "\e901";
}
.picon-offer:before {
  content: "\e904";
}
.picon-reward-filled:before {
  content: "\e93f";
}
.picon-reward:before {
  content: "\e909";
}
.picon-ribbon:before {
  content: "\e900";
}
.picon-service-filled:before {
  content: "\e902";
}
.picon-service-outline:before {
  content: "\e90b";
}
.picon-share-filled:before {
  content: "\e905";
}
.picon-share-outline:before {
  content: "\e90a";
}
.picon-call-center:before {
  content: "\e940";
}
.picon-gift-fill:before {
  content: "\e99e";
}
.picon-gift:before {
  content: "\e942";
}
.picon-recycling:before {
  content: "\e908";
}
.picon-name-switch:before {
  content: "\e90c";
}
.picon-slide-arrow:before {
  content: "\e90d";
}
.picon-calendar:before {
  content: "\e90e";
}
.picon-clock-outline:before {
  content: "\e943";
}
.picon-timer:before {
  content: "\e90f";
}
.picon-alarm:before {
  content: "\e94b";
}
.picon-truck-delivery-on-time:before {
  content: "\e941";
}
.picon-truck-delivery:before {
  content: "\e910";
}
.picon-moto-delivery:before {
  content: "\e9ab";
}
.picon-takeaway:before {
  content: "\e9ac";
}
.picon-takeaway-bubbletea:before {
  content: "\e9ad";
}
.picon-takeaway-fastfood:before {
  content: "\e9ae";
}
.picon-takeaway-food:before {
  content: "\e9b3";
}
.picon-dine-in-with-service:before {
  content: "\e9a2";
}
.picon-trash-o:before {
  content: "\e911";
}
.picon-Face-ID:before {
  content: "\e913";
}
.picon-fingerprint:before {
  content: "\e960";
}
.picon-frame-bottom-right:before {
  content: "\e914";
}
.picon-frame-bottom-left:before {
  content: "\e915";
}
.picon-frame-top-right:before {
  content: "\e916";
}
.picon-frame-top-left:before {
  content: "\e917";
}
.picon-flashlight-on:before {
  content: "\e918";
}
.picon-flashlight-off:before {
  content: "\e919";
}
.picon-briefcase:before {
  content: "\e91a";
}
.picon-home:before {
  content: "\e91b";
}
.picon-storefront:before {
  content: "\e912";
}
.picon-document-with-stamp:before {
  content: "\e91e";
}
.picon-note:before {
  content: "\e91f";
}
.picon-ticket-filled:before {
  content: "\e920";
}
.picon-ticket:before {
  content: "\e923";
}
.picon-dine-in:before {
  content: "\e9b5";
}
.picon-reserve-table:before {
  content: "\e924";
}
.picon-book-hotel:before {
  content: "\e925";
}
.picon-book-flight:before {
  content: "\e926";
}
.picon-make-appointment:before {
  content: "\e927";
}
.picon-appointment:before {
  content: "\e928";
}
.picon-room:before {
  content: "\e99f";
}
.picon-seat:before {
  content: "\e9a0";
}
.picon-sos:before {
  content: "\e929";
}
.picon-like-fill:before {
  content: "\e92a";
}
.picon-like:before {
  content: "\e92b";
}
.picon-donate-money:before {
  content: "\e92c";
}
.picon-donate:before {
  content: "\e92d";
}
.picon-asian-cutlery:before {
  content: "\e92e";
}
.picon-cutlery:before {
  content: "\e91c";
}
.picon-drink:before {
  content: "\e9af";
}
.picon-fast-food:before {
  content: "\e92f";
}
.picon-glass:before {
  content: "\e99b";
}
.picon-wineglass:before {
  content: "\e9a3";
}
.picon-wine:before {
  content: "\e9a4";
}
.picon-bubbletea:before {
  content: "\e9a5";
}
.picon-colddrink:before {
  content: "\e9a6";
}
.picon-hotdrink:before {
  content: "\e9a7";
}
.picon-coffee:before {
  content: "\e9a8";
}
.picon-tea1:before {
  content: "\e9a9";
}
.picon-teabag:before {
  content: "\e9aa";
}
.picon-cupsize:before {
  content: "\e9b0";
}
.picon-icecube:before {
  content: "\e9b1";
}
.picon-toppings:before {
  content: "\e9b4";
}
.picon-sugar:before {
  content: "\e9b2";
}
.picon-vending-machine:before {
  content: "\e9b6";
}
.picon-printer:before {
  content: "\e9b8";
}
.picon-paw:before {
  content: "\e9c3";
}
.picon-paw-outline:before {
  content: "\e9c4";
}
.picon-weibo:before {
  content: "\e922";
}
.picon-facebook-messenger-logo:before {
  content: "\e930";
}
.picon-facebook-messenger:before {
  content: "\e931";
}
.picon-facebook-logo:before {
  content: "\e932";
}
.picon-facebook:before {
  content: "\e933";
}
.picon-whatsapp-logo:before {
  content: "\e934";
}
.picon-whatsapp:before {
  content: "\e935";
}
.picon-wechat-logo:before {
  content: "\e936";
}
.picon-wechat:before {
  content: "\e937";
}
.picon-twitter-logo:before {
  content: "\e938";
}
.picon-twitter:before {
  content: "\e939";
}
.picon-line-logo:before {
  content: "\e93a";
}
.picon-line:before {
  content: "\e93b";
}
.picon-linepay:before {
  content: "\e93b";
}
.picon-instagram-logo:before {
  content: "\e93c";
}
.picon-instagram:before {
  content: "\e93d";
}
.picon-youtube-logo:before {
  content: "\e989";
}
.picon-youtube:before {
  content: "\e98a";
}
.picon-tiktok-logo:before {
  content: "\e98b";
}
.picon-tiktok:before {
  content: "\e98c";
}
.picon-social-logo:before {
  content: "\e98e";
}
.picon-social-outline:before {
  content: "\e98f";
}
.picon-social:before {
  content: "\e990";
}
.picon-alipay:before {
  content: "\e91d";
}
.picon-grab:before {
  content: "\e978";
}
.picon-grabpay:before {
  content: "\e97d";
}
.picon-applepay:before {
  content: "\e979";
}
.picon-googlepay:before {
  content: "\e97a";
}
.picon-paypal:before {
  content: "\e97b";
}
.picon-credit-card:before {
  content: "\e97c";
}
.picon-payment:before {
  content: "\e921";
}
.picon-topup:before {
  content: "\e93e";
}
.picon-payment-outline:before {
  content: "\e99c";
}
.picon-topup-outline:before {
  content: "\e99d";
}
.picon-dollar:before {
  content: "\e98d";
}
.picon-radiobutton-unchecked:before {
  content: "\e944";
}
.picon-checkcircle-unchecked:before {
  content: "\e944";
}
.picon-radiobutton-checked:before {
  content: "\e945";
}
.picon-radiobutton-fill:before {
  content: "\e645";
}
.picon-checkbox-unchecked:before {
  content: "\e946";
}
.picon-checkbox-checked:before {
  content: "\e947";
}
.picon-checkcircle-checked-outline:before {
  content: "\e948";
}
.picon-checkcircle-checked:before {
  content: "\e949";
}
.picon-small-check:before {
  content: "\e94a";
}
.picon-small-close:before {
  content: "\e953";
}
.picon-small-switch:before {
  content: "\e9b9";
}
.picon-notifications-active:before {
  content: "\e94c";
}
.picon-circle-notifications:before {
  content: "\e94d";
}
.picon-notifications-paused:before {
  content: "\e94e";
}
.picon-notifications-off:before {
  content: "\e94f";
}
.picon-notifications:before {
  content: "\e950";
}
.picon-lock-closed-outline:before {
  content: "\e954";
}
.picon-lock-open-outline:before {
  content: "\e955";
}
.picon-lock-open:before {
  content: "\e956";
}
.picon-lock-closed:before {
  content: "\e957";
}
.picon-picture:before {
  content: "\e95a";
}
.picon-camera-outline:before {
  content: "\e95b";
}
.picon-camera-off-outline:before {
  content: "\e95c";
}
.picon-navigation-2:before {
  content: "\e95d";
}
.picon-navigation:before {
  content: "\e95e";
}
.picon-verified_user:before {
  content: "\e95f";
}
.picon-comment-outline:before {
  content: "\e961";
}
.picon-comment:before {
  content: "\e962";
}
.picon-bug-outline:before {
  content: "\e963";
}
.picon-bug_report:before {
  content: "\e964";
}
.picon-flight-takeoff:before {
  content: "\e965";
}
.picon-flight-land:before {
  content: "\e966";
}
.picon-directions-airplane-off:before {
  content: "\e967";
}
.picon-directions-airplane:before {
  content: "\e968";
}
.picon-directions-car:before {
  content: "\e969";
}
.picon-directions-subway:before {
  content: "\e96a";
}
.picon-directions-railway:before {
  content: "\e96b";
}
.picon-directions-bus:before {
  content: "\e96c";
}
.picon-directions-bike:before {
  content: "\e96d";
}
.picon-directions-run:before {
  content: "\e96e";
}
.picon-directions-walk:before {
  content: "\e96f";
}
.picon-directions-ferry:before {
  content: "\e970";
}
.picon-barcode1:before {
  content: "\e971";
}
.picon-qrcode:before {
  content: "\e972";
}
.picon-content_copy:before {
  content: "\e973";
}
.picon-package:before {
  content: "\e977";
}
.picon-playstore:before {
  content: "\e97f";
}
.picon-appstore:before {
  content: "\e980";
}
.picon-arrow-back:before {
  content: "\e951";
}
.picon-arrow-forward:before {
  content: "\e952";
}
.picon-arrow-down:before {
  content: "\e959";
}
.picon-expand:before {
  content: "\e959";
}
.picon-arrow-up:before {
  content: "\e958";
}
.picon-fold:before {
  content: "\e958";
}
.picon-arrow-right-2:before {
  content: "\e97e";
}
.picon-arrow-left-2:before {
  content: "\e984";
}
.picon-arrow-down-2:before {
  content: "\e985";
}
.picon-arrow-up-2:before {
  content: "\e986";
}
.picon-arrow-right-circle:before {
  content: "\e981";
}
.picon-arrow-left-circle:before {
  content: "\e982";
}
.picon-arrow-up-circle:before {
  content: "\e987";
}
.picon-arrow-down-circle:before {
  content: "\e983";
}
.picon-warning-outline:before {
  content: "\e991";
}
.picon-warning:before {
  content: "\e992";
}
.picon-member-card:before {
  content: "\e9c6";
}
.picon-member-card-outline:before {
  content: "\e9c7";
}
.picon-issue-card-outline:before {
  content: "\e993";
}
.picon-issue-card:before {
  content: "\e994";
}
.picon-deactivate-card-outline:before {
  content: "\e995";
}
.picon-deactivate-card:before {
  content: "\e996";
}
.picon-receive-payment:before {
  content: "\e997";
}
.picon-receive-payment-outline:before {
  content: "\e998";
}
.picon-pay-outline:before {
  content: "\e999";
}
.picon-pay:before {
  content: "\e99a";
}
.picon-favorite-outline:before {
  content: "\e9c8";
}
.picon-favorite:before {
  content: "\e9c9";
}
