.button{position:relative;display:inline-flex;align-items:center;justify-content:center;border:1px solid transparent;border-radius:var(--spacing-sm);padding:0 var(--spacing-md);font-size:2rem;min-width:var(--size-button);height:var(--size-button);cursor:pointer;font-weight:700;vertical-align:middle;-webkit-user-select:none;user-select:none;overflow:hidden}.button .button-wrapper{position:relative;display:inline-flex;align-items:center;justify-content:center;pointer-events:none;transition:opacity .3s ease}.button.solid{color:#fff}.button.solid.accent{background:var(--color-background-accent);border-color:var(--color-background-accent)}.button.solid.primary{background:var(--color-background-primary);border-color:var(--color-background-primary)}.button.solid.success{background:var(--color-background-success);border-color:var(--color-background-success)}.button.solid.warning{background:var(--color-background-warning);border-color:var(--color-background-warning)}.button.solid.error{background:var(--color-background-error);border-color:var(--color-background-error)}.button.clear{background:transparent;border:none}.button.outline{background:transparent;border-color:currentColor}.button.clear-icon{padding:0;border-radius:99em}.button.circle{padding:0;border-radius:99em;color:var(--color-text-soft)}.button.circle:before{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:2em;height:2em;content:"";background-color:var(--color-background-darker);border-radius:99em}.button.circle span{position:relative;z-index:1}.button .button-title-container{display:inline-block;white-space:nowrap}.button .button-title-container .button-subtitle{font-weight:400;font-size:.8em;opacity:.8}.button .button-title-container .button-title+.button-subtitle{margin-left:.5rem}.button .button-icon.picon:first-child{margin-right:.4rem}.button .button-icon.picon:last-child{margin-left:.4rem}.button .button-icon.picon:only-child{margin:0}.button.disabled{filter:grayscale(1);pointer-events:none}.button .ripple-container{pointer-events:none}.button.solid>.ripple-container .ripple{background-color:var(--color-ripple)}.button.clear-icon>.ripple-container,.button.circle>.ripple-container{top:50%;left:50%;right:unset;bottom:unset;border-radius:99em;width:2em;height:2em;transform:translate(-50%,-50%)}.button .status-container{position:absolute;pointer-events:none}[theme=perkd] .button.circle:before{background-color:var(--color-background-heavy)}
