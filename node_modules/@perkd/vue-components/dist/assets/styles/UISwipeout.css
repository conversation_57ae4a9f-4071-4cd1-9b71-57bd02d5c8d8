.swipeout{position:relative;display:flex;overflow:hidden;touch-action:pan-y}.swipeout .swipeout-content{flex-grow:1;z-index:1;display:flex;align-items:center;justify-content:flex-start;padding:var(--spacing-sm) var(--spacing-md);background-color:var(--color-background-neutral);transition:transform .3s ease}.swipeout .swipeout-content:before{position:absolute;top:0;left:0;right:0;bottom:0;content:"";background-color:var(--color-background-neutral);pointer-events:none;z-index:-1}.swipeout .swipeout-content.swiping{transition:none}.swipeout .swipeout-content.swiping:before{background-color:var(--color-background-darker)}.swipeout .swipeout-background{position:absolute;top:0;bottom:0;right:0;left:0;z-index:0}.swipeout .swipeout-action-container{position:absolute;top:0;bottom:0;right:0;display:flex;align-items:stretch;justify-content:flex-end;width:auto;font-weight:700;color:#fff}.swipeout .swipeout-action-container.left{left:0;right:auto;justify-content:flex-start}.swipeout .swipeout-action-container .swipeout-action{display:flex;align-items:center;justify-content:center;padding:0 var(--spacing-md);min-height:auto;height:auto;color:#fff;border-radius:0;cursor:pointer;background-color:transparent;border:none}.scale-up-enter-active,.scale-up-leave-active{transition:transform .3s ease,opacity .3s ease}.scale-up-enter,.scale-up-leave-to{transform:scaleY(0);opacity:0}
