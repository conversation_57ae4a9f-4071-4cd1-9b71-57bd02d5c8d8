import { ref as o, watch as E, onMounted as S, onBeforeUnmount as g } from "vue";
import { onSocketMessage as b, onSocketClose as L, onSocketError as O } from "@perkd/applet-common/websocket";
import { debounce as J, isErrorResponse as m } from "@perkd/applet-common/utils";
function C(e, f) {
  const a = o(""), i = o(""), c = o(!1), u = o(0), r = o(e.status), k = J(async () => {
    await e.keepAlive();
  }, 300);
  E(r, (s) => {
    Object.keys(s).filter((t) => !s[t]).length && k();
  });
  async function d(s) {
    const n = await b(s, e.status);
    if (m(n)) {
      console.log("WebSocket Error:", n.error);
      return;
    }
    const { key: t, data: y } = n;
    t && e.onMessage({ key: t, data: y }), a.value = (/* @__PURE__ */ new Date()).toISOString();
  }
  async function w(s) {
    const n = JSON.parse(JSON.stringify(e.status)), t = await L(s, n);
    if (m(t)) {
      console.log("WebSocket Error:", t.error);
      return;
    }
    e.onClose(t), r.value = e.status;
  }
  async function l(s) {
    const n = JSON.parse(JSON.stringify(e.status));
    u.value += 1, u.value < 5 && await O(f, n, s);
  }
  async function v() {
    i.value = (/* @__PURE__ */ new Date()).toISOString(), await e.keepAlive();
  }
  return S(async () => {
    window.addEventListener("socket.message", d), window.addEventListener("socket.close", w), window.addEventListener("socket.error", l), window.addEventListener("app.resume", v), await e.start(), r.value = e.status, c.value = !0;
  }), g(async () => {
    window.removeEventListener("socket.message", d), window.removeEventListener("socket.close", w), window.removeEventListener("socket.error", l), window.removeEventListener("app.resume", v), await e.end();
  }), {
    topic: e,
    ready: c,
    syncTime: a,
    resumeTime: i
  };
}
export {
  C as useWebsocket
};
