import { reactive as c } from "vue";
function r() {
  const t = c({
    show: !1,
    success: void 0,
    text: ""
  });
  let e = null;
  function i(s = "") {
    t.show = !0, t.success = void 0, t.text = s;
  }
  function n(s, o, u = 1500) {
    t.success = s, e && clearTimeout(e), e = setTimeout(() => {
      t.show = !1, o && o();
    }, u);
  }
  return {
    loading: t,
    startLoading: i,
    endLoadingWithDelay: n
  };
}
export {
  r as useLoading
};
