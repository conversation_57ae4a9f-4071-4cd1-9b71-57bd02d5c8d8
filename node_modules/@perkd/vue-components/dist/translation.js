import { createI18n as e } from "vue-i18n";
const m = { button: { ok: "Ok", cancel: "Cancel", submit: "Submit", delete: "Delete" }, form: { search: "search", join: "join", expire: "expire" }, countdown: { before: { prefix: "end on ", suffix: "" }, after: "Event ended", counting_down: { prefix: "end in ", suffix: "" }, counting_up: { prefix: "overdue", suffix: "" }, time_unit: { dd: "day", hh: "hour", mm: "min", ss: "sec" } }, error: { offline_status: "offline", failed_to_get_server_time: "Failed to get server time", is_required: "required", minimum_length: "min. {n} character | min. {n} characters", maximum_length: "max. {n} character | max. {n} characters", minimum_number: "must be {n} or above", maximum_number: "must be {n} or below", invalid_pattern: "wrong format", invalid: "invalid", invalid_date: "wrong date format", before_minimum_date: "must be {date} or later", after_maximum_date: "must be {date} or earlier", above_minimum_amount: "must be {amount} or above", below_maximum_amount: "must be {amount} or below" } }, i = {
  en: m,
  "zh-Hans": { button: { ok: "好", cancel: "取消", submit: "提交", delete: "删除" }, form: { search: "搜索", join: "加入", expire: "到期" }, countdown: { before: { prefix: "", suffix: "结束" }, after: "活动已结束", counting_down: { prefix: "", suffix: "后结束" }, counting_up: { prefix: "超时", suffix: "" }, time_unit: { dd: "天", hh: "时", mm: "分", ss: "秒" } }, error: { offline_status: "离线", failed_to_get_server_time: "服务器时间获取失败", is_required: "必填项", minimum_length: "最少{n}个字符", maximum_length: "最多{n}个字符", minimum_number: "最少为{n}", maximum_number: "最多为{n}", invalid_pattern: "格式错误", invalid: "错误", invalid_date: "时间格式错误", before_minimum_date: "应该在{date}或之后", after_maximum_date: "应该在{date}或之前", above_minimum_amount: "金额至少为{amount}", below_maximum_amount: "金额最多为{amount}" } },
  "zh-Hant": { button: { ok: "好", cancel: "取消", submit: "提交", delete: "刪除" }, form: { search: "搜寻", join: "加入", expire: "到期" }, countdown: { before: { prefix: "", suffix: "結束" }, after: "活動已結束", counting_down: { prefix: "", suffix: "後結束" }, counting_up: { prefix: "超時", suffix: "" }, time_unit: { dd: "天", hh: "时", mm: "分", ss: "秒" } }, error: { offline_status: "離線", failed_to_get_server_time: "服務器時間獲取失敗", is_required: "必填項", minimum_length: "最少{n}個字符", maximum_length: "最多{n}個字符", minimum_number: "最少为{n}", maximum_number: "最多为{n}", invalid_pattern: "格式錯誤", invalid: "錯誤", invalid_date: "時間格式錯誤", before_minimum_date: "應該在{date}之後", after_maximum_date: "應該在{date}之前", above_minimum_amount: "金額至少為{amount}", below_maximum_amount: "金額最多為{amount}" } }
}, t = e({
  legacy: !1,
  locale: "en",
  messages: i
});
export {
  t as default
};
