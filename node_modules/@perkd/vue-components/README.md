# Perkd Vue Components

A Vue 3 component library for mobile web applet development.

```bash
**************:perkd/vue-components.git
```

## Component Relations

```mermaid
graph TB
    %% Core Layout
    subgraph Layout["Layout"]
        direction TB
        UIScreen["UIScreen"]
        UINavigationBar["UINavigationBar"]
        UIModalBox["UIModalBox"]
        UIDialog["UIDialog"]
    end
    
    %% Basic Components
    subgraph Basic["Basic"]
        direction TB
        UIIcon["UIIcon"]
        UILoading["UILoading"]
        UIButton["UIButton"]
        UIRipple["UIRipple"]
    end
    
    %% Interactive Components
    subgraph Interactive["Interactive"]
        direction TB
        UICard["UICard"]
        UICardOverlay["UICardOverlay"]
        UISlideButton["UISlideButton"]
        UIMarquee["UIMarquee"]
        CountDown["CountDown"]
        UISwipeout["UISwipeout"]
        UISelection["UISelection"]
    end
    
    %% Form System
    subgraph Form["Form"]
        direction TB
        UIField["UIField"]
        UIInput["UIInput"]
        UISwitch["UISwitch"]
        subgraph InputTypes["Input Types"]
            direction TB
            InputMoney["InputMoney"]
            InputMobile["InputMobile"]
            InputDate["InputDate"]
        end
    end

    %% Core Relations
    UIScreen --> UINavigationBar
    UIScreen --> UILoading
    UIScreen --> UIDialog
    UIScreen --> UIModalBox
    
    %% Form Relations
    UIField --> UIIcon
    UIField --> UIInput
    UIInput --> |"money"| InputMoney
    UIInput --> |"tel"| InputMobile
    UIInput --> |"date/datetime"| InputDate
    
    %% Button Relations
    UIButton --> UIIcon
    UIButton --> UILoading
    UIButton --> UIRipple
    UIModalBox --> UIButton
    UISlideButton --> UIButton
    
    %% Card Relations
    UICardOverlay --> UICard

    classDef default fill:#2d2d2d,stroke:#666,color:white
    classDef subgraphStyle fill:none,stroke:#444,color:white
    class Layout,Form,Basic,Interactive,InputTypes subgraphStyle
```

## Installation

```bash
yarn add @perkd/vue-components
# or
npm install @perkd/vue-components
```

## Available Components

### Layout
- `UIScreen` - Base screen layout with:
  - Navigation bar
  - Content area
  - Footer
  - Loading/Dialog support
- `UINavigationBar` - Header navigation with customizable actions
- `UIModalBox` - Modal container with gesture support

### Basic
- `UIIcon` - Icon component with customizable styles
- `UIButton` - Button with multiple types (solid, outline, clear, circle)
- `UILoading` - Loading spinner with customizable sizes and colors
- `UIRipple` - Material design ripple effect

### Interactive
- `UICard` - Membership Card display component with flip click
- `UICardOverlay` - Modal overlay for cards
- `UISlideButton` - Slide to confirm button
- `UIMarquee` - Text marquee animation
- `CountDown` - Countdown timer with multiple formats
- `UISwipeout` - Swipeable action buttons
- `UISelection` - Selection list item
- `UICircleProgress` - Circular progress indicator

### Form
- `UIField` - Form field wrapper with validation
- `UIInput` - Input component supporting:
  - Text input
  - Date/time picker (`InputDate`)
  - Mobile number with country code (`InputMobile`)
  - Currency input (`InputMoney`)
- `UISwitch` - Toggle switch with custom icons

## Example Usage

```vue
<template>
  <UIScreen>
    <template #navigationBar>
      <UINavigationBar :title="title" />
    </template>
    
    <template #content>
      <UIField label="Name" required>
        <UIInput v-model="name" />
      </UIField>
      
      <UIButton 
        type="solid"
        color="accent"
        :title="submitText"
        @click="handleSubmit"
      />
    </template>
  </UIScreen>
</template>
```

## Composables

### `useLoading`
Loading state management:
```typescript
const { loading, startLoading, endLoadingWithDelay } = useLoading()
```

### `useStorage`
Local storage management:
```typescript
const { preferenceStorage, updatePreferenceStorage } = useStorage<PreferenceType>('key')
```

### `useWebsocket`
WebSocket connection management:
```typescript
const { topic, ready, syncTime } = useWebsocket(topic, 'applet-name')
```

## Development

To explore components in Storybook:

```bash
yarn
yarn storybook
```

### [Storybook Playground](https://test-depot.s3-ap-southeast-1.amazonaws.com/applet/vuecomponent/index.html)

## Documentation

- [How to create a vue project with storybook](./doc/CREATEPROJECT.md)
- [Vue development guide](./doc/VUE.md)

## Browser Support

- iOS Safari 12+
- Android Chrome 63+