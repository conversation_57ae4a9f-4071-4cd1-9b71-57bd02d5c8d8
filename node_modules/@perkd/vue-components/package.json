{"name": "vue-components", "version": "1.8.0", "private": true, "type": "module", "files": ["dist", "dist_types"], "main": "./dist/main.js", "types": "./dist_types/main.d.ts", "exports": {".": {"import": "./dist/main.js", "types": "./dist_types/main.d.ts"}, "./components/*": {"import": "./dist/components/*.js", "types": "./dist_types/components/*.d.ts"}, "./composables/*": {"import": "./dist/composables/*.js", "types": "./dist_types/composables/*.d.ts"}, "./assets/styles/*": "./dist/assets/styles/*", "./assets/images/*": "./dist/assets/images/*", "./assets/fonts/*": "./dist/assets/fonts/*"}, "scripts": {"dev": "vite", "build": "vite build && vue-tsc --declaration --emitDeclarationOnly && node build/renameTypes.js && node build/generateMainCss.js", "types": "vue-tsc", "preview": "vite preview", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@perkd/applet-common": "github:perkd/applet-common#semver:^v0.3.13", "@perkd/format-datetime": "github:perkd/format-datetime#semver:^v1.3.0", "jsbarcode": "^3.11.6", "qrcode": "^1.5.4", "vue": "^3.5.13", "vue-currency-input": "^3.2.1", "vue-i18n": "^11.1.1", "vue-router": "^4.5.0", "vue-tel-input": "^9.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.5", "@storybook/addon-essentials": "^8.6.3", "@storybook/addon-interactions": "^8.6.3", "@storybook/addon-links": "^8.6.3", "@storybook/blocks": "^8.6.3", "@storybook/testing-library": "^0.2.2", "@storybook/vue3": "^8.6.3", "@storybook/vue3-vite": "^8.6.3", "@tsconfig/node18": "^18.2.4", "@types/jsdom": "^21.1.7", "@types/node": "^22.13.9", "@types/qrcode": "^1.5.5", "@types/vue-tel-input": "^2.1.7", "@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-core": "^3.5.13", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.4.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "@yarnpkg/sdks": "3.2.1", "eslint": "^9.21.0", "eslint-plugin-storybook": "^0.11.3", "eslint-plugin-vue": "^9.32.0", "jsdom": "^26.0.0", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "sass": "^1.85.1", "storybook": "^8.6.3", "terser": "^5.39.0", "tslib": "^2.8.1", "typescript": "~5.8.2", "vite": "^6.2.0", "vite-plugin-static-copy": "^2.3.0", "vitest": "^3.0.7", "vue-tsc": "^2.2.8"}}