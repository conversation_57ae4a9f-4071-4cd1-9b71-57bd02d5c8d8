import fs from 'fs';
import path from 'path';
const __dirname = process.cwd();
const readFile = (filePath) => fs.readFileSync(filePath, 'utf-8');
// Read the content of the main.ts file
const mainFileContent = readFile(path.resolve(__dirname, 'src/main.ts'));
const indexFileContent = readFile(path.resolve(__dirname, 'index.html'));
const packageJson = JSON.parse(readFile(path.resolve(__dirname, 'package.json')));
// configuration
const hasFormatDateTime = !!packageJson.dependencies['@perkd/format-datetime'];
const embedded = process.env.VITE_SINGLE_PAGE === 'true';
const getCommonImports = (isIndex) => isIndex ? [
    'appbridge-1.0.min.js',
    'vue-3.4.min.js',
    'vue-i18n-9.13.min.js'
] : [
    "@perkd/vue-components/assets/styles/font.css",
    "@perkd/vue-components/assets/styles/picon.css",
    "@perkd/applet-common/appbridge"
];
const getLocaleImports = (isIndex) => isIndex ? [
    'formatDateTime-1.2.js',
    'formatDateTime-en.js',
    'formatDateTime-zh-cn.js',
    'formatDateTime-zh-hk.js',
    'formatDateTime-zh-tw.js'
] : [
    "@perkd/format-datetime/dist/locale/en.js",
    "@perkd/format-datetime/dist/locale/zh-cn.js",
    "@perkd/format-datetime/dist/locale/zh-hk.js",
    "@perkd/format-datetime/dist/locale/zh-tw.js"
];
const getRequiredImports = (isIndex) => hasFormatDateTime
    ? [...getCommonImports(isIndex), ...getLocaleImports(isIndex)]
    : getCommonImports(isIndex);
const checkImportsInFile = (imports, fileContent, shouldExist) => {
    const checkFunc = shouldExist ? 'every' : 'some';
    return imports[checkFunc](filePath => {
        const found = fileContent.includes(filePath);
        if (shouldExist && !found)
            console.log(`🛑 Missing import: ${filePath}`);
        if (!shouldExist && found)
            console.log(`🛑 Unnecessary import: ${filePath}`);
        return found;
    });
};
// Pre-build checks
try {
    let errorMessages = [];
    const requiredMainImports = getRequiredImports(false);
    const requiredIndexImports = getRequiredImports(true);
    console.log('single page applet: ', embedded);
    if (hasFormatDateTime) {
        const hasSetupLocale = mainFileContent.includes("setupLanguage(ENVIRONMENT, LOCALE_LANGUAGE,");
        if (!hasSetupLocale) {
            errorMessages.push('🛑 setupLanguage with LOCALE_LANGUAGE is missing in main.ts');
        }
    }
    if (embedded) {
        const unnecessaryInMain = checkImportsInFile(requiredMainImports, mainFileContent, false);
        const missingInIndex = !checkImportsInFile(requiredIndexImports, indexFileContent, true);
        if (unnecessaryInMain)
            errorMessages.push('🛑 Unnecessary files are still imported in main.ts');
        if (missingInIndex)
            errorMessages.push('🛑 Required files are missing in index.html');
    }
    else {
        const missingInMain = !checkImportsInFile(requiredMainImports, mainFileContent, true);
        const unnecessaryInIndex = checkImportsInFile(requiredIndexImports, indexFileContent, false);
        if (missingInMain)
            errorMessages.push('🛑 Required files are missing in main.ts');
        if (unnecessaryInIndex)
            errorMessages.push('🛑 Unnecessary files are still imported in index.html');
    }
    if (errorMessages.length > 0) {
        throw new Error(errorMessages.join('. '));
    }
}
catch (error) {
    console.error(error.message);
    process.exit(1); // Stop the build process if validation fails
}
