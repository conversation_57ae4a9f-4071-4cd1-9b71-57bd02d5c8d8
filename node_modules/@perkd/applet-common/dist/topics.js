import { startSocket, closeSocket } from "./websocket.js";
import { isErrorResponse } from "./utils.js";
export class Topics {
    name;
    queue; // messages received
    config;
    subscribed;
    constructor(name, subjects, config) {
        this.name = name;
        this.config = config;
        this.subscribed = [];
        this.queue = [];
        for (const subject of Object.entries(subjects)) {
            const [key, value] = subject;
            this.subscribed.push({ key, subject: value, alive: false });
        }
    }
    get status() {
        return this.subscribed.reduce((res, s) => {
            const { key, socketId } = s;
            Object.assign(res, { [key]: socketId });
            return res;
        }, {});
    }
    async start() {
        const { subscribed, config } = this;
        for (const subscription of subscribed) {
            const { key, subject } = subscription;
            if (subscription.alive)
                continue;
            const res = await startSocket(key, { subject, ...config });
            if (isErrorResponse(res))
                continue;
            const { [key]: id } = res;
            subscription.socketId = id;
            subscription.alive = true;
        }
    }
    onMessage(message) {
        this.queue.push(message);
    }
    onClose(message) {
        const { subscribed } = this;
        const { socketId } = message;
        const subscription = subscribed.find(s => s.socketId === socketId);
        if (subscription) {
            subscription.socketId = '';
            subscription.alive = false;
        }
    }
    next() {
        return this.queue.shift();
    }
    async keepAlive() {
        const { subscribed, config } = this;
        const disconnected = subscribed.filter(s => !s.alive);
        for (const subscription of disconnected) {
            const { key, subject } = subscription;
            if (subscription.alive)
                continue;
            const res = await startSocket(key, { subject, ...config });
            if (isErrorResponse(res))
                continue;
            const { [key]: id } = res;
            subscription.socketId = id;
            subscription.alive = true;
        }
    }
    async end() {
        const { subscribed } = this;
        for (const { socketId, alive } of subscribed) {
            if (!alive || !socketId)
                continue;
            await closeSocket(socketId);
        }
        this.subscribed = [];
    }
}
