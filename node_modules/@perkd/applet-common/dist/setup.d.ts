import { Applets } from "./types/applets.js";
export declare const setupEnvironment: (constants: Applets.Constants) => Applets.Environment;
export declare const setupWidget: (data: Applets.Environment, appletName: string) => Applets.Widget;
export declare const setupFont: (data: Applets.Environment, embeddedFont?: boolean) => void;
export declare const setupTheme: (data: Applets.Environment, appletName?: string) => void;
export declare const setupLanguage: (data: Applets.Environment, locales?: Record<string, string>, dayjs?: any) => Promise<void>;
export declare const setupI18n: (translations: any) => any;
