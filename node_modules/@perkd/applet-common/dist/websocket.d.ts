import type { Applets, ErrorResponse } from "./types/applets";
type Config = Applets.SocketConfig;
type Subject = Applets.SocketSubject;
export declare namespace Websockets {
    type EventData = {
        id: string;
        message: string;
    };
    type Record = {
        [key: string]: string;
    };
    type Param = Config & {
        subject: Subject;
    };
    type Data = {
        key: string;
        data: any;
    };
}
export declare const Order: {
    created: (storeId: string, query?: any) => {
        source: string;
        key: string;
        query: any;
    };
    paid: (storeId: string, query?: any) => {
        source: string;
        key: string;
        query: any;
    };
    fulfilled: (storeId: string, query?: any) => {
        source: string;
        key: string;
        query: any;
    };
    cancelled: (storeId: string, query?: any) => {
        source: string;
        key: string;
        query: any;
    };
};
export declare const Attendance: {
    Metrics: {
        booking: (resourceId: string) => {
            query: string;
        };
        checkin: (resourceId: string) => {
            query: string;
        };
    };
    List: {
        booking: (resourceId: string, start: string, end: string) => {
            query: string;
            start: string;
            end: string;
        };
        checkin: (resourceId: string, start: string, end: string) => {
            query: string;
            start: string;
            end: string;
        };
    };
};
export declare const Membership: {
    joined: (query?: any) => {
        source: string;
        key: string;
        query: any;
    };
    registered: (query?: any) => {
        source: string;
        key: string;
        query: any;
    };
    cancelled: (query?: any) => {
        source: string;
        key: string;
        query: any;
    };
};
export declare const Fulfillment: {
    created: (placeId: string, stations?: string[]) => {
        source: string;
        key: string;
        query: {
            placeId: string;
            'itemList.fulfilledAt': null;
        };
    };
    queued: (placeId: string, stations?: string[]) => {
        source: string;
        key: string;
        query: {
            placeId: string;
            'itemList.fulfilledAt': null;
        };
    };
    packed: (placeId: string) => {
        source: string;
        key: string;
        query: {
            placeId: string;
        };
    };
    relocated: (placeId: string) => {
        source: string;
        key: string;
        query: {
            placeId: string;
        };
    };
    cancelled: (placeId: string) => {
        source: string;
        key: string;
        query: {
            placeId: string;
        };
    };
    fulfilled: (placeId: string) => {
        source: string;
        key: string;
        query: {
            placeId: string;
        };
    };
};
export declare function startSocket(key: string, param: Websockets.Param): Promise<Websockets.Record | ErrorResponse>;
export declare function closeSocket(socketId: string): Promise<void | ErrorResponse>;
export declare function onSocketMessage(event: CustomEvent<Websockets.EventData>, sockets: Websockets.Record): Promise<Websockets.Data | ErrorResponse>;
export declare function onSocketClose(event: CustomEvent<Websockets.EventData>, sockets: Websockets.Record): Promise<{
    socketId: string;
    key: string;
} | ErrorResponse>;
export declare function onSocketError(appletName: string, sockets: Websockets.Record, data: any): Promise<void | ErrorResponse>;
export {};
