import { Persons as Crm<PERSON>ersons } from '@perkd/crm-types/dist/persons.js';
import { Persons } from './types/persons.js';
import { Cards } from './types/cards.js';
import { Applets } from './types/applets.js';
export const isUrl = (str) => {
    const exp = /[-a-zA-Z0-9@:%._\\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)?/gi;
    return !!str && !!str.match(new RegExp(exp));
};
export const isEmail = (str) => {
    const exp = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return !!str && !!str.match(new RegExp(exp));
};
export const isZero = (str) => {
    const numbers = str.match(/\d+/g);
    const sum = numbers?.map(Number).reduce((acc, num) => acc + num, 0) || 0;
    return sum === 0;
};
export const isObject = (item) => {
    return item && typeof item === 'object' && !Array.isArray(item);
};
export const mergeObject = (target, source) => {
    if (!isObject(target) || !isObject(source)) {
        return source === undefined ? target : source;
    }
    const result = Array.isArray(target) ? [] : {};
    // Ensure we copy all properties from the target
    for (const key in target) {
        if (target.hasOwnProperty(key)) {
            result[key] = isObject(target[key]) ? mergeObject({}, target[key]) : target[key];
        }
    }
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            if (Array.isArray(source[key])) {
                // Replace arrays, do not merge
                result[key] = source[key] == undefined ? target[key] : source[key];
            }
            else if (isObject(source[key])) {
                if (!(key in target)) {
                    result[key] = source[key];
                }
                else {
                    result[key] = mergeObject(target[key], source[key]);
                }
            }
            else {
                result[key] = source[key] === undefined ? target[key] : source[key];
            }
        }
    }
    return result;
};
export function generateCSV(arrData, name) {
    const contents = [
        Object.keys(arrData[0]).join(","),
        ...arrData.map((item) => Object.values(item).join(","))
    ]
        .join("\n")
        .replace(/(^\[)|(\]$)/gm, "");
    return {
        fileName: `${name}.csv`,
        contents,
    };
}
export function getErrorMessage(error, t) {
    const { statusCode, statusMessage = '', code, message = '' } = error || {};
    const errorKey = statusMessage || message;
    const translation = t(`error.${errorKey}`);
    const isTranslationValid = !!translation && translation !== `error.${errorKey}`;
    const title = isTranslationValid ? translation : t('error.message');
    const errorCode = `${statusCode || code || ''}`;
    const part1 = errorCode && errorCode !== message.trim() ? errorCode : '';
    const part2 = (errorKey !== message || !isTranslationValid) ? message.trim() : '';
    const description = `${part1} ${part2}`.trim();
    return `${title}${description ? ': ' : ''}${description}`;
}
export function getDisplayAs(familyName, givenName, nameOrder = Persons.DEFAULT_NAME_ORDER) {
    if (!(givenName && familyName))
        return '';
    if (nameOrder === CrmPersons.NameOrder.GIVEN_FAMILY)
        return `${givenName} ${familyName}`;
    return `${familyName} ${givenName}`;
}
export function getFormFields(form) {
    return (Object.keys(form.schema.properties) || []).map((key) => key);
}
export function toProfile(person) {
    const { familyName, givenName, fullName, gender, name, phones, emails, birthDate } = person || {};
    const { year, month, day, date } = birthDate || {};
    const { countryCode = '', fullNumber = '', optIn: mobileOpt } = phones?.[0] || {};
    const { address = '', optIn: emailOpt } = emails?.[0] || {};
    return {
        familyName,
        givenName,
        nameOrder: name?.order,
        fullName: fullName || getDisplayAs(familyName, givenName, name?.order),
        gender,
        birthDate: date ? date : (year && month && day ? `${year}-${month}-${day}` : (month && day ? `${month}-${day}` : '')),
        countryCode: phones?.[0]?.countryCode,
        formattedMobile: formatPhoneNumber(fullNumber.replace(countryCode, ''), countryCode),
        mobile: fullNumber,
        mobileOpt,
        email: address,
        emailOpt
    };
}
export function toMembershipCard(data, programs, cardMaster) {
    const { membershipId, programId, tierLevel, cardNumber, startTime, endTime } = data || {};
    const program = programs.find((p) => p.programId === programId);
    const tier = program?.tiers.find((tier) => tier?.level === tierLevel);
    return {
        name: tier?.name || '',
        image: tier?.card?.image?.original,
        cardNumber,
        startTime,
        endTime,
        barcodeType: cardMaster?.barcodeTypes?.[0] || Cards.DEFAULT_BARCODE_TYPE,
        barcodePatterns: cardMaster?.barcodePatterns || '',
        membershipId,
        programId,
        programName: program?.name || '',
        tierLevel,
        type: program?.type || '',
    };
}
export function toQueryString(searchParams) {
    const parts = [];
    Object.keys(searchParams).forEach(key => {
        const value = searchParams[key];
        if (value !== undefined) {
            if (typeof value === 'object') {
                parts.push(`${key}=${encodeURIComponent(JSON.stringify(value))}`);
            }
            else {
                parts.push(`${key}=${encodeURIComponent(value)}`);
            }
        }
    });
    return parts.join('&');
}
export function getTransLang(lang, provisions = Applets.LANGUAGES.SUPPORTED) {
    if (provisions.includes(lang)) {
        return lang;
    }
    const { FALLBACKS, DEFAULT } = Applets.LANGUAGES;
    if (Object.keys(FALLBACKS).includes(lang)) {
        const langFallbacks = FALLBACKS[lang] || FALLBACKS.default;
        return langFallbacks.find((l) => provisions.includes(l)) || DEFAULT;
    }
    return DEFAULT;
}
export function formatAmount(amount, currency, minDigits, maxDigits, showFullrSign = false) {
    if (amount === undefined)
        return '';
    const CURRENCY = {
        CNY: { dollarSign: '¥', fullDollarSign: 'CN$', decimal: 2 },
        HKD: { dollarSign: '$', fullDollarSign: 'HK$', decimal: 2 },
        SGD: { dollarSign: '$', fullDollarSign: 'S$', decimal: 2 },
        TWD: { dollarSign: '$', fullDollarSign: 'NT$', decimal: 0 },
        others: { dollarSign: '$', fullDollarSign: '$', decimal: 2 },
    };
    const selected = CURRENCY[currency || 'others'] || CURRENCY.others;
    const { fullDollarSign, dollarSign, decimal } = selected;
    return `${showFullrSign ? fullDollarSign : dollarSign}${amount.toLocaleString(undefined, {
        minimumFractionDigits: minDigits ?? decimal ?? 2,
        maximumFractionDigits: maxDigits ?? decimal ?? 2,
    })}`;
}
export function formatPhoneNumber(number, countryCode) {
    const cleanNumber = number.replace(/\D/g, '');
    let formatted = countryCode ? `+${countryCode} ` : '';
    // Calculate optimal grouping
    // max part 4, mini part 2, even distribution every part
    // put the shortest part at the beginning
    // Common formatting patterns by length
    const patterns = {
        '7': [3, 4], // +XX XXX XXXX
        '8': [4, 4], // +XX XXXX XXXX
        '9': [3, 3, 3], // +XX XXX XXX XXX
        '10': [3, 3, 4], // +XX XXX XXX XXXX
        '11': [3, 4, 4] // +XX XXX XXXX XXXX
    };
    const pattern = patterns[cleanNumber.length.toString()] || calculateGroups(cleanNumber.length);
    // Apply the grouping
    let position = 0;
    for (let i = 0; i < pattern.length; i++) {
        if (i > 0)
            formatted += " ";
        formatted += cleanNumber.substring(position, position + pattern[i]);
        position += pattern[i];
    }
    return formatted;
}
function calculateGroups(length) {
    // For longer numbers, calculate optimal grouping
    const numGroups = Math.ceil(length / 4);
    const groups = [];
    // Calculate base group size
    const baseSize = Math.floor(length / numGroups);
    let remainder = length % numGroups;
    // Create groups, filling from end to start
    for (let i = 0; i < numGroups; i++) {
        // Assign extra digit to later groups first when remainder exists
        const groupSize = (i >= numGroups - remainder) ? baseSize + 1 : baseSize;
        groups.push(groupSize);
    }
    // Ensure shorter parts are at the beginning
    groups.sort();
    return groups;
}
export function getCookies() {
    const fields = (document.cookie || '').split(';');
    const params = {};
    for (var i = 0; i < fields.length; i++) {
        var pair = fields[i].split('=');
        if (!pair || pair.length < 2)
            continue;
        Object.assign(params, { [pair[0].trim()]: pair[1] });
    }
    return params;
}
export function getUrlParameters(url = '') {
    const hasParams = url && url.split('?').length > 0;
    if (!hasParams)
        return {};
    const params = new URL(url).searchParams;
    return Object.fromEntries(params);
}
export function detectLanguage(str) {
    if (typeof str !== 'string')
        return '';
    if (str.match(/[\uac00-\ud7ff]|[\u1100-\u11FF]|[\u3130-\u318F]/g))
        return 'ko';
    if (str.match(/[\uff66-\uff9d]|[\u3041-\u309f]|[\u30a0-\u30ff]|[\u31F0-\u31FF]/g))
        return 'ja';
    if (str.match(/[\u4e00-\u9fa5]/g))
        return 'zh-Hant';
    return 'en';
}
export function isErrorResponse(resp) {
    return (!!resp && typeof resp === 'object' && 'error' in resp);
}
export function debounce(fn, delay) {
    let timeoutId;
    return (...args) => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
            fn(...args);
        }, delay);
    };
}
