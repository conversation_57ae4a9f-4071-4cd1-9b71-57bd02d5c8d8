import { Billings } from "@perkd/crm-types/dist/billings.js";
import { Discounts } from "@perkd/crm-types/dist/offers.js";
import { Fulfillments } from './types/crm/fulfillments.js';
import { Orders } from './types/crm/orders.js';
import { Receipts } from './types/receipts.js';
export declare function toItems(itemList: Orders.Item[], currency?: string): Receipts.Item[];
export declare function toBagItems(items: Orders.Item[]): {
    productId: string;
    variantId: string;
    title: string;
    unitPrice: number;
    quantity: number;
    images: string[] | undefined;
    kind: import("@perkd/crm-types").Orders.ItemKind | undefined;
    options: {
        key: string;
        title: string;
        name?: string;
        value?: any;
        values: any[];
    }[];
    properties: any[] | undefined;
    units: number | undefined;
    unitPriceMeasure: import("@perkd/crm-types").Products.UnitMeasure | undefined;
    custom: Partial<{
        id: string;
        bookingId: string;
        productIds: string[];
        from: string;
        duration: number;
        start: string;
        end: string;
        tenure: string;
        membership: {
            id: string;
            programId: string;
            memberId: string;
            personId: string;
        };
        qualification: {
            programId: string;
            memberId: string;
            personId: string;
            tenure: string;
            qualifier: string;
            qualified: any;
        };
        sponsor: string;
    }> | undefined;
    admit: number | undefined;
}[];
export declare function toFulfill(fulfillment?: Fulfillments.Fulfillment, order?: Orders.Order): undefined | Receipts.Fulfillment;
export declare function toDiscounts(discountList: Discounts.Discount[], currency: string): Receipts.Discount[] | undefined;
export declare function toPaymentMethods(billingList: Billings.Billing[], currency: string): Receipts.PaymentMethod[] | undefined;
export declare function toBill(order: Orders.Order, fulfillment?: Fulfillments.Fulfillment): any;
export declare function getScheduled(type: Fulfillments.Type, scheduled?: Fulfillments.MinMaxTime): string;
export declare function getOrderId(order: Orders.Order): string;
export declare function getOrderStatus(order: Orders.Order, fulfillment?: Fulfillments.Fulfillment): string;
export declare function getItemsType(order: Orders.Order): import("@perkd/crm-types").Fulfillments.Service | "mixed" | undefined;
export declare function getItemName(title?: string, variantName?: string): string;
export declare function getOptions(variantOptions: Orders.VariantOption[]): {
    name: string;
    value: string;
}[];
export declare function isPaid(order: Orders.Order, fulfillment?: Fulfillments.Fulfillment): boolean | null;
export declare function isManualPayment(order: Orders.Order): boolean | undefined;
