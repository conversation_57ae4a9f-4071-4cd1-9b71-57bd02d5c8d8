import fs from 'fs';
import path from 'path';
const __dirname = process.cwd();
// Define the replacements
const replacements = [
    {
        pattern: /import{([^}]+)}from"(vue|vue-i18n|@perkd\/format-datetime)"/g,
        replace: (match, imports, library) => {
            const importStatements = imports.split(',').map(imp => {
                const [importName, alias] = imp.split(' as ').map(part => part.trim());
                return alias ? `${importName}:${alias}` : `${importName}`;
            }).join(',');
            const globalVar = library === 'vue'
                ? 'Vue'
                : library === 'vue-i18n'
                    ? 'VueI18n' :
                    library === '@perkd/format-datetime'
                        ? 'formatDateTime' : '';
            return `const {${importStatements}}=${globalVar}`;
        }
    }
];
// Function to process the file content
function processFileContent(content) {
    replacements.forEach(({ pattern, replace }) => {
        if (typeof replace === 'function') {
            content = content.replace(pattern, (...args) => replace(...args));
        }
        else {
            content = content.replace(pattern, replace);
        }
    });
    return content;
}
// Define the directory containing the built files
const buildDir = path.resolve(__dirname + '/dist');
// Process all JS files in the build directory
fs.readdir(buildDir, (err, files) => {
    if (err) {
        console.error('Error reading build directory:', err);
        process.exit(1);
    }
    files.forEach(file => {
        const filePath = path.join(buildDir, file);
        if (filePath.endsWith('.html')) {
            fs.readFile(filePath, 'utf8', (err, content) => {
                if (err) {
                    console.error('Error reading file:', filePath, err);
                    return;
                }
                const newContent = processFileContent(content);
                fs.writeFile(filePath, newContent, 'utf8', err => {
                    if (err) {
                        console.error('Error writing file:', filePath, err);
                    }
                    else {
                        console.log('Processed file:', filePath);
                    }
                });
            });
        }
    });
});
