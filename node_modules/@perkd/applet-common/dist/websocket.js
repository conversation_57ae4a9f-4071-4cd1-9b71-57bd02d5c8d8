import { isErrorResponse } from "./utils.js";
import { Actions } from './types/actions.js';
const { CONNECT, SEND, CLOSE } = Actions.Remote, SUBSCRIBE = 'subscribe';
// ---  Actions
const Websocket = {
    create: async (SOCKET_URL, cardId, masterId) => {
        if (!SOCKET_URL) {
            return { error: { statusMessage: 'websocket.config_missing' } };
        }
        const url = `${SOCKET_URL}/metrics-push?access_token={{x-access-token}}`;
        const credentials = 'perkd';
        try {
            const socketId = await window.$perkd.do(CONNECT, { url, cardId, masterId, credentials });
            return socketId;
        }
        catch (error) {
            return { error };
        }
    },
    subscribe: async (id, message) => {
        try {
            await window.$perkd.do(SEND, { id, message: JSON.stringify({ type: SUBSCRIBE, ...message }) });
        }
        catch (error) {
            return { error };
        }
    },
    unsubscribe: async (id) => {
        try {
            await window.$perkd.do(CLOSE, { id });
        }
        catch (error) {
            return { error };
        }
    },
};
// Websocket Message
export const Order = {
    created: (storeId, query) => ({ source: 'eventbus', key: 'sales.order.created', query: { storeId, ...query } }),
    paid: (storeId, query) => ({ source: 'eventbus', key: 'sales.order.paid', query: { storeId, ...query } }),
    fulfilled: (storeId, query) => ({ source: 'eventbus', key: 'sales.order.fulfilled', query: { storeId, ...query } }),
    cancelled: (storeId, query) => ({ source: 'eventbus', key: 'sales.order.cancelled', query: { storeId, ...query } })
};
export const Attendance = {
    Metrics: {
        booking: (resourceId) => ({ query: `sum(c_sales_booking_confirm{resourceId="${resourceId}"})` }),
        checkin: (resourceId) => ({ query: `sum(c_person_person_attend_checkin{resourceId="${resourceId}"})` })
    },
    List: {
        booking: (resourceId, start, end) => ({ query: `c_sales_booking_confirm{resourceId="${resourceId}"}`, start, end }),
        checkin: (resourceId, start, end) => ({ query: `c_person_person_attend_checkin{resourceId="${resourceId}"}`, start, end }),
    }
};
export const Membership = {
    joined: (query) => ({ source: 'eventbus', key: 'membership.membership.joined', query }),
    registered: (query) => ({ source: 'eventbus', key: 'membership.card.registered', query }),
    cancelled: (query) => ({ source: 'eventbus', key: 'membership.membership.cancelled', query }),
};
export const Fulfillment = {
    created: (placeId, stations) => {
        const query = { placeId, 'itemList.fulfilledAt': null };
        if (stations)
            Object.assign(query, { 'itemList.product.tags.kitchen': { $in: stations } });
        return { source: 'eventbus', key: 'sales.fulfillment.requested.kitchen', query };
    },
    queued: (placeId, stations) => {
        const query = { placeId, 'itemList.fulfilledAt': null };
        if (stations)
            Object.assign(query, { 'itemList.product.tags.kitchen': { $in: stations } });
        return { source: 'eventbus', key: 'sales.fulfillment.queued.kitchen', query };
    },
    packed: (placeId) => ({ source: 'eventbus', key: 'sales.fulfillment.packed.kitchen', query: { placeId } }),
    relocated: (placeId) => ({ source: 'eventbus', key: 'sales.fulfillment.relocated.kitchen', query: { placeId } }),
    cancelled: (placeId) => ({ source: 'eventbus', key: 'sales.fulfillment.cancelled', query: { placeId } }),
    fulfilled: (placeId) => ({ source: 'eventbus', key: 'sales.fulfillment.success.item', query: { placeId } }),
};
export async function startSocket(key, param) {
    const { socketUrl, cardId, masterId, subject } = param;
    const socketId = await Websocket.create(socketUrl, cardId, masterId);
    if (isErrorResponse(socketId))
        return socketId;
    const subscribe = await Websocket.subscribe(socketId, subject);
    if (isErrorResponse(subscribe))
        return subscribe;
    return { [key]: socketId };
}
export async function closeSocket(socketId) {
    const result = await Websocket.unsubscribe(socketId);
    if (isErrorResponse(result)) {
        return { error: { ...result.error, statusMessage: 'websocket.onclose_error' } };
    }
}
// window event socket.message
export async function onSocketMessage(event, sockets) {
    const result = parseEvent(event);
    if (isErrorResponse(result)) {
        return { error: { ...result.error, statusMessage: 'websocket.onmessage_error' } };
    }
    const found = Object.entries(sockets).find(([key, socketId]) => result.socketId === socketId);
    // receive unknown socket message
    if (!found) {
        const { socketId } = result;
        const response = await Websocket.unsubscribe(socketId);
        if (isErrorResponse(response)) {
            return {
                error: {
                    ...response.error, statusMessage: 'websocket.onmessage_unsubscribe_failed', socketId, sockets
                }
            };
        }
        return { error: { statusMessage: 'websocket.onmessage_unsubscribe', socketId, data: result.data, sockets } };
    }
    return {
        key: found[0],
        data: result.data
    };
}
// window event socket.close
export async function onSocketClose(event, sockets) {
    const result = parseEvent(event);
    if (isErrorResponse(result)) {
        return { error: { ...result.error, statusMessage: 'websocket.close_failed', sockets } };
    }
    const found = Object.entries(sockets).find(([key, socketId]) => result.socketId === socketId);
    if (!found) {
        const { socketId } = result;
        return { error: { statusMessage: 'websocket.onclose_unknown_socket', socketId, sockets } };
    }
    // clear socket id if close
    const closedKey = found[0];
    if (sockets[closedKey])
        sockets[closedKey] = '';
    return { socketId: result.socketId, key: closedKey };
}
// window event socket.error
export async function onSocketError(appletName, sockets, data) {
    return { error: { ...data, statusMessage: 'websocket.onerror', sockets } };
}
function parseEvent(event) {
    const { id, message } = event.detail || {};
    const result = { socketId: id, data: {} };
    try {
        if (message)
            result.data = JSON.parse(message);
    }
    catch (error) {
        return { error };
    }
    return result;
}
