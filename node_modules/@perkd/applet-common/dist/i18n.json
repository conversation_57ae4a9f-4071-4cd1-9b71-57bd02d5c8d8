{"en": {"button": {"back": "Back", "cancel": "Cancel", "clear": "Clear", "confirm": "Confirm", "done": "Done", "next": "Next", "no": "No", "no_match": "No Match", "ok": "Ok", "rejoin": "Rejoin", "renew": "<PERSON>w", "terminate": "Terminate", "yes": "Yes", "submit": "Submit", "delete": "Delete"}, "form": {"search": "search", "birth": "birthdate", "cardNumber": "card number", "createAt": "create at", "displayAs": "display as", "email": "email", "emailOptIn": "email opt in", "endTime": "expire", "expire": "expire", "familyName": "family name", "gender": "gender", "givenName": "given name", "isPaid": "{name} paid", "join": "join", "mobile": "mobile", "mobileOptIn": "mobile opt in", "paidTime": "paid time", "startTime": "join", "terminated": "terminated"}, "countdown": {"before": {"prefix": "end on ", "suffix": ""}, "after": "Event ended", "counting_down": {"prefix": "end in ", "suffix": ""}, "counting_up": {"prefix": "overdue", "suffix": ""}, "time_unit": {"dd": "day", "hh": "hour", "mm": "min", "ss": "sec"}}, "error": {"oops": "Oops", "above_minimum_amount": "must be {amount} or above", "below_maximum_amount": "must be {amount} or below", "after_maximum_date": "must be {date} or earlier", "before_minimum_date": "must be {date} or later", "expire": {"before_minimum_date": "must from today & join date onwards"}, "fallback": "Operation error <br/> {{error}}", "invalid": "invalid", "invalid_date": "wrong date format", "invalid_mobile": "Invalid mobile number", "invalid_pattern": "wrong format", "is_required": "required", "join": {"after_maximum_date": "must before expire date"}, "maximum_length": "max. {n} character | max. {n} characters", "minimum_length": "min. {n} character | min. {n} characters", "maximum_number": "must be {n} or below", "minimum_number": "must be {n} or above", "message": "Ops, something wrong, please try again later", "missing_active_member": "Found an active member, but failed to load the data", "offline": "You are offline, please check the network settings", "offline_status": "offline", "staff_info_missing": "Staff info is missing", "timeout": "Operation timeout", "all_fields_required": "All fields are required", "already_cancelled": "Already cancelled", "already_expired": "Already expired", "already_terminated": "Already terminated", "already_upgraded": "Already upgraded", "amount_not_numeric": "Incorrect amount format", "cardId_missing": "Card Id is missing", "checkinInfo_missing": "Check-in info missing", "duplicated_receipt": "Duplicated receipt", "endTime_missing": "End time is required", "given_and_family_required": "Given name & family name are required", "invalid_currency": "Invalid currency", "invalid_endTime": "Invalid expiration date", "invalid_nameorder": "Invalid name order", "location_missing": "Location is disabled or missing", "mobile_required": "Mobile is required", "not_active_membership": "Not an active membership", "not_qualify_extension": "Not qualify extension", "not_supported_order": "Not supportted order", "ordersummary_or_order_required": "Order / Order Summary is required", "past_expire_time": "End time is past expire time", "personId_or_profile_required": "Person Id / Profile is required", "quantity_not_numeric": "Incorrect quantity format", "query_or_cardnumber_required": "Query must not be blank", "staff_missing": "Staff is missing", "staff_not_found": "<PERSON><PERSON> found the staff", "config_missing": "Config is missing", "websocket": {"config_missing": "Config is missing, failed to connect websocket", "onmessage_error": "Failed to get websocket data from onMessage event", "onmessage_unsubscribe_failed": "Received unknown websocket data, failed to close", "onmessage_unsubscribe": "Received unknown websocket data, now closed", "onclose_unknown_socket": "Unknown websocket closed", "onclose_error": "Failed to get websocket data from onClose event", "onerror": "Unknown websocket error", "close_failed": "Failed to close event"}}}, "zh-Hans": {"button": {"back": "返回", "cancel": "取消", "clear": "清除", "confirm": "确定", "done": "完成", "next": "下一步", "no": "否", "no_match": "不匹配", "ok": "好", "rejoin": "重新加入", "renew": "更新", "terminate": "终止", "yes": "是", "submit": "提交", "delete": "删除"}, "form": {"search": "搜索", "birth": "生日", "cardNumber": "卡号", "createAt": "创建时间", "displayAs": "名字展示", "email": "邮件地址", "emailOptIn": "邮件订阅", "endTime": "到期", "expire": "到期", "familyName": "姓", "gender": "性别", "givenName": "名", "isPaid": "{name}已付", "join": "加入", "mobile": "手机号码", "mobileOptIn": "手机订阅", "paidTime": "付费时间", "startTime": "加入", "terminated": "已终止"}, "countdown": {"before": {"prefix": "", "suffix": "结束"}, "after": "活动已结束", "counting_down": {"prefix": "", "suffix": "后结束"}, "counting_up": {"prefix": "超时", "suffix": ""}, "time_unit": {"dd": "天", "hh": "时", "mm": "分", "ss": "秒"}}, "error": {"oops": "哎呀", "above_minimum_amount": "金额至少为{amount}", "below_maximum_amount": "金额最多为{amount}", "after_maximum_date": "应该在{date}之前", "before_minimum_date": "应该在{date}之后", "expire": {"before_minimum_date": "选择今天和加入日期之后的日期"}, "fallback": "操作错误 <br/> {{error}}", "invalid": "错误", "invalid_date": "时间格式错误", "invalid_mobile": "手机号码无效", "invalid_pattern": "格式错误", "is_required": "必填项", "join": {"after_maximum_date": "选择过期日期之前的日期"}, "maximum_length": "最多{n}个字符", "minimum_length": "最少{n}个字符", "maximum_number": "最多为{n}", "minimum_number": "最少为{n}", "message": "呃，出错了，请稍后再试", "missing_active_member": "找到一个有效会员，但是数据加载失败", "offline": "当前离线状态, 请检查网路设置", "offline_status": "离线", "staff_info_missing": "员工数据缺失", "timeout": "操作超时", "all_fields_required": "所有字段都必填", "already_cancelled": "已经取消了", "already_expired": "已经过期了", "already_terminated": "已经终止了", "already_upgraded": "已经升级了", "amount_not_numeric": "金额格式不对", "cardId_missing": "Card ID缺失", "checkinInfo_missing": "签到数据缺失", "duplicated_receipt": "该收据已存在", "endTime_missing": "过期时间必填", "given_and_family_required": "姓名必填", "invalid_currency": "币种错误", "invalid_endTime": "过期时间错误", "invalid_nameorder": "名字顺序错误", "location_missing": "位置数据缺失", "mobile_required": "手机号码必填", "not_active_membership": "不是有效的会员", "not_qualify_extension": "该会籍不符合延期条件", "not_supported_order": "不支持该订单", "ordersummary_or_order_required": "请提供订单详情", "past_expire_time": "结束时间超过过期时间", "personId_or_profile_required": "个人 ID/资料缺失", "quantity_not_numeric": "数量的格式错误", "query_or_cardnumber_required": "请提供需要搜寻的内容或者卡号", "staff_missing": "员工资料缺失", "staff_not_found": "找不到该员工记录", "config_missing": "配置缺失", "websocket": {"config_missing": "配置缺失, 导致连接 WebSocket 失败", "onmessage_error": "从 onMessage 事件中获取 WebSocket 数据失败", "onmessage_unsubscribe_failed": "接收到无法识别的 WebSocket 数据, 关闭失败", "onmessage_unsubscribe": "接收到无法识别的 WebSocket 数据, 现已关闭", "onclose_unknown_socket": "已关闭无法识别的 WebSocket", "onclose_error": "从 onClose 事件中获取 WebSocket 数据失败", "onerror": "无法识别的 WebSocket 错误", "close_failed": "WebSocket 关闭失败"}}}, "zh-Hant": {"button": {"back": "返回", "cancel": "取消", "clear": "清除", "confirm": "確定", "done": "完成", "next": "下一步", "no": "否", "no_match": "不匹配", "ok": "好", "rejoin": "重新加入", "renew": "更新", "terminate": "终止", "yes": "是", "submit": "提交", "delete": "刪除"}, "form": {"search": "搜寻", "birth": "生日", "cardNumber": "卡號", "createAt": "創建時間", "displayAs": "名字展示", "email": "郵件地址", "emailOptIn": "郵件訂閱", "endTime": "到期", "expire": "到期", "familyName": "姓", "gender": "性別", "givenName": "名", "isPaid": "{name}已付", "join": "加入", "mobile": "手機號碼", "mobileOptIn": "手機訂閱", "paidTime": "付費時間", "startTime": "加入", "terminated": "已終止"}, "countdown": {"before": {"prefix": "", "suffix": "結束"}, "after": "活動已結束", "counting_down": {"prefix": "", "suffix": "後結束"}, "counting_up": {"prefix": "超時", "suffix": ""}, "time_unit": {"dd": "天", "hh": "时", "mm": "分", "ss": "秒"}}, "error": {"oops": "哎呀", "above_minimum_amount": "金額至少為{amount}", "below_maximum_amount": "金額最多為{amount}", "after_maximum_date": "應該在{date}之前", "before_minimum_date": "應該在{date}之後", "expire": {"before_minimum_date": "選擇今天和加入日期之後的日期"}, "fallback": "操作錯誤 <br/> {{error}}", "invalid": "錯誤", "invalid_date": "時間格式錯誤", "invalid_mobile": "手機號碼無效", "invalid_pattern": "格式錯誤", "is_required": "必填項", "join": {"after_maximum_date": "選擇過期日期之前的日期"}, "maximum_length": "最多{n}個字符", "minimum_length": "最少{n}個字符", "minimum_number": "最少为{n}", "maximum_number": "最多为{n}", "message": "呃，出錯了，請稍後再試", "missing_active_member": "找到一個有效會員，但是數據加載失敗", "offline": "當前離線狀態, 請檢查網路設置", "offline_status": "離線", "staff_info_missing": "員工數據缺失", "timeout": "操作超時", "all_fields_required": "所有字段都必填", "already_cancelled": "已經取消了", "already_expired": "已經過期了", "already_terminated": "已經終止了", "already_upgraded": "已經升級了", "amount_not_numeric": "金額格式不對", "cardId_missing": "Card ID缺失", "checkinInfo_missing": "簽到數據缺失", "duplicated_receipt": "該收據已存在", "endTime_missing": "過期時間必填", "given_and_family_required": "姓名必填", "invalid_currency": "幣種錯誤", "invalid_endTime": "過期時間錯誤", "invalid_nameorder": "名字順序錯誤", "location_missing": "位置數據缺失", "mobile_required": "手機號碼必填", "not_active_membership": "不是有效的會員", "not_qualify_extension": "該會籍不符合延期條件", "not_supported_order": "不支持該訂單", "ordersummary_or_order_required": "請提供訂單詳情", "past_expire_time": "結束時間超過過期時間", "personId_or_profile_required": "個人 ID/資料缺失", "quantity_not_numeric": "數量的格式錯誤", "query_or_cardnumber_required": "請提供需要搜尋的內容或者卡號", "staff_missing": "員工資料缺失", "staff_not_found": "找不到該員工記錄", "config_missing": "配置缺失", "websocket": {"config_missing": "配置缺失, 導致連線 WebSocket 失敗", "onmessage_error": "從 onMessage 事件中取得 WebSocket 資料失敗", "onmessage_unsubscribe_failed": "接收到無法辨識的 WebSocket 資料, 關閉失敗", "onmessage_unsubscribe": "接收到無法辨識的 WebSocket 資料, 現已關閉", "onclose_unknown_socket": "已關閉無法辨識的 WebSocket", "onclose_error": "從 onClose 事件中取得 WebSocket 資料失敗", "onerror": "無法辨識的 WebSocket 錯誤", "close_failed": "WebSocket 關閉失敗"}}}}