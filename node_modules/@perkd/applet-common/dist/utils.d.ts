import { Persons } from './types/persons.js';
import { Memberships } from './types/memberships.js';
import { Cards } from './types/cards.js';
import { CardMasters } from './types/cardMasters.js';
import { Applets, type Error, type ErrorResponse } from './types/applets.js';
import { Forms } from './types/forms.js';
export declare const isUrl: (str: string) => boolean;
export declare const isEmail: (str: string) => boolean;
export declare const isZero: (str: string) => boolean;
export declare const isObject: (item: any) => boolean;
export declare const mergeObject: (target: any, source: any) => any;
export declare function generateCSV(arrData: any[], name: string): {
    fileName: string;
    contents: string;
};
export declare function getErrorMessage(error: Error, t: Function): string;
export declare function getDisplayAs(familyName: string, givenName: string, nameOrder?: string): string;
export declare function getFormFields(form: Forms.Form): string[];
export declare function toProfile(person: Persons.Person): Persons.Profile;
export declare function toMembershipCard(data: Memberships.Membership, programs: Memberships.Program[], cardMaster: CardMasters.Master): Cards.MembershipCard;
export declare function toQueryString(searchParams: {
    [key: string]: any;
}): string;
export declare function getTransLang(lang: string, provisions?: Applets.Language[]): string;
export declare function formatAmount(amount?: number, currency?: string, minDigits?: number, maxDigits?: number, showFullrSign?: boolean): string;
export declare function formatPhoneNumber(number: string, countryCode?: string): string;
export declare function getCookies(): {
    [key: string]: string;
};
export declare function getUrlParameters(url?: string): {
    [key: string]: string;
};
export declare function detectLanguage(str: string): "en" | "zh-Hant" | "ja" | "ko" | "";
export declare function isErrorResponse(resp: any): resp is ErrorResponse;
export declare function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): (...args: Parameters<T>) => void;
