import { Applets } from "./types/applets.js";
import commonTranslate from './i18n.json';
export function closeWindow() {
    window.$perkd.do('window.close');
}
export async function getConstants() {
    try {
        const response = await window.$perkd.do('constants');
        const { constants } = response || {};
        return constants;
    }
    catch (error) {
        return { error };
    }
}
export async function openSlotPicker(param) {
    const { value, theme = Applets.ColorScheme.LIGHT, options } = param || {};
    try {
        const response = await window.$perkd.do("form.showpicker", {
            type: 'slotPicker',
            theme,
            options,
            value
        });
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function openDurationPicker(param) {
    const { value, theme = Applets.ColorScheme.LIGHT, options } = param || {};
    try {
        const response = await window.$perkd.do("form.showpicker", {
            type: 'durationPicker',
            theme,
            options,
            value
        });
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function openDateTimePicker({ value = '', mode = Applets.DateTimePickerType.DATE, theme = Applets.ColorScheme.LIGHT }) {
    // app typo (should be datetime, typo: dateTime )
    try {
        const response = await window.$perkd.do("form.showpicker", {
            type: 'dateTimePicker',
            theme,
            mode: mode === Applets.DateTimePickerType.DATETIME ? 'dateTime' : mode,
            value
        });
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function openScanner(payload) {
    try {
        const response = await window.$perkd.do('media.scan', payload);
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function navToApplet(appletName, cardInstanceId, params = {}) {
    try {
        await window.$perkd.do('app.navto', {
            route: [
                { 'card': { 'id': cardInstanceId } },
                { [appletName]: params }
            ]
        });
    }
    catch (error) {
        return { error };
    }
}
export function openDialog(params) {
    const { title, message, buttons, lang = 'en' } = params;
    return window.$perkd.do('interact.dialog', {
        title,
        message,
        buttons: buttons && buttons.length > 0 ? buttons : [{
                text: (commonTranslate[lang] || commonTranslate.en)?.button.ok,
                action: { object: 'window', action: 'close' },
                style: 'default'
            }],
        cancelable: true
    });
}
export async function emailTo(email, subject) {
    try {
        await window.$perkd.do('communicate.email', {
            address: email,
            cc: '<EMAIL>',
            subject
        });
    }
    catch (error) {
        return { error };
    }
}
export async function callTo(number) {
    try {
        await window.$perkd.do('communicate.call', { number });
    }
    catch (error) {
        return { error };
    }
}
export async function copyText(text) {
    try {
        await window.$perkd.do('system.toclipboard', { text });
        await window.$perkd.do('interact.toast', { message: text });
    }
    catch (error) {
        return { error };
    }
}
export async function showToast(text) {
    try {
        await window.$perkd.do('interact.toast', { message: text });
    }
    catch (error) {
        return { error };
    }
}
export async function addToBag(params) {
    try {
        const items = params.items.map((item) => {
            const { channel, variantId, productId, sku, title, quantity, status, unitPrice, price, amount, discount, fulfillments, paymentMethods, taxes, images, tags, properties, options, ttl, attributes, kind, unit, custom, expiresAt, capacity, admit, unitPriceMeasure, units, description, inventory } = item;
            return {
                channel, variantId, productId, sku, title, quantity, status, unitPrice, price, amount, discount, fulfillments, paymentMethods,
                taxes, images, tags, properties, options, ttl, attributes, kind, unit, custom, expiresAt,
                capacity, admit, unitPriceMeasure, units, description, inventory
            };
        });
        const response = await window.$perkd.do('bag.additems', { ...params, items });
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function openBag() {
    try {
        await window.$perkd.do('bag.open');
    }
    catch (error) {
        return { error };
    }
}
export async function orderProducts(params) {
    try {
        const response = await window.$perkd.do('order.products', params);
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function reorderFromReceipt(params) {
    try {
        const response = await window.$perkd.do('bag.from', params);
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function perkdRemote(data) {
    const { method, base, endpoint, cardId, ...rest } = data;
    if (!(base && endpoint)) {
        return { error: { statusMessage: 'config_missing' } };
    }
    const payload = {
        method,
        url: `${base}/${endpoint}`,
        cardId,
        credentials: 'perkd',
        ...rest
    };
    try {
        const response = await window.$perkd.do('remote.api', payload);
        return response;
    }
    catch (error) {
        await trackWatch(`${base}/${endpoint}`, error, payload);
        return { error };
    }
}
// payload
// {
//     value: string,
//     options: { encode: boolean, description: string }
// }
export async function writeNFC(payload) {
    try {
        // nothing will be returned when success
        const response = await window.$perkd.do('nfc.write', payload);
        return response;
    }
    catch (error) {
        return { error };
    }
}
// payload
// {
//     options: { encode: boolean }
// }
export async function readNFC(payload) {
    try {
        const response = await window.$perkd.do('nfc.read', payload);
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function lockNFC(password, options) {
    try {
        // nothing will be returned when success
        const response = await window.$perkd.do('nfc.setpassword', { password, options });
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function fileShare(file) {
    try {
        const response = await window.$perkd.do('file.share', { file });
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function trackWatch(message, error, data) {
    try {
        await window.$perkd.do('track.watch', { message, error, data });
    }
    catch (error) {
        return { error };
    }
}
export var SoundEffect;
(function (SoundEffect) {
    SoundEffect["Achieved"] = "achieved";
    SoundEffect["Beep"] = "beep";
    SoundEffect["BellChord"] = "bellchord";
    SoundEffect["Cash"] = "cash";
    SoundEffect["Cashier"] = "cashier";
    SoundEffect["Chord"] = "chord";
    SoundEffect["Correct"] = "correct";
    SoundEffect["Done"] = "done";
    SoundEffect["Fail"] = "fail";
    SoundEffect["Happy"] = "happy";
    SoundEffect["Magic"] = "magic";
    SoundEffect["Notify"] = "notify";
    SoundEffect["Scan"] = "scan";
    SoundEffect["ServiceBell"] = "servicebell";
    SoundEffect["Success"] = "success";
    SoundEffect["SuccessBell"] = "successbell";
    SoundEffect["Upsell"] = "upsell";
    SoundEffect["WifiOn"] = "wifion";
})(SoundEffect || (SoundEffect = {}));
export async function playSound(name) {
    try {
        await window.$perkd.do('interact.playsound', { name });
    }
    catch (error) {
        return { error };
    }
}
export var VibrateEffect;
(function (VibrateEffect) {
    VibrateEffect["Selection"] = "selection";
    VibrateEffect["ImpactLight"] = "impactLight";
    VibrateEffect["ImpactMedium"] = "impactMedium";
    VibrateEffect["ImpactHeavy"] = "impactHeavy";
    VibrateEffect["NotificationSuccess"] = "notificationSuccess";
    VibrateEffect["NotificationWarning"] = "notificationWarning";
    VibrateEffect["NotificationError"] = "notificationError";
})(VibrateEffect || (VibrateEffect = {}));
export async function vibrate(type) {
    try {
        await window.$perkd.do('interact.vibrate', { type });
    }
    catch (error) {
        return { error };
    }
}
export var WebMethod;
(function (WebMethod) {
    WebMethod["NATIVE"] = "native";
    WebMethod["BROWSER"] = "browser";
    WebMethod["IN_APP"] = "web";
})(WebMethod || (WebMethod = {}));
export async function openLink(url, method = WebMethod.BROWSER, context) {
    try {
        const param = { [method]: url };
        if (context) {
            param.context = context;
        }
        await window.$perkd.do('web.open', { param });
    }
    catch (error) {
        return { error };
    }
}
export async function getWidgetData(widgetName, params = {}) {
    try {
        const payload = {
            key: widgetName
        };
        if (params?.id !== undefined) {
            payload.id = params.id;
        }
        const response = await window.$perkd.do('widget.data', payload);
        return response;
    }
    catch (error) {
        return { error };
    }
}
export async function openHoursSettings(hours) {
    try {
        const response = await window.$perkd.do('form.hoursSettings', { hours: hours || { periods: [] } });
        return response;
    }
    catch (error) {
        return { error };
    }
}
