import { Websockets } from "./websocket.js";
import type { Applets } from "./types/applets";
type Config = Applets.SocketConfig;
type Subject = Applets.SocketSubject;
type Subscription = {
    key: string;
    subject: Subject;
    socketId?: string;
    alive: boolean;
};
export declare class Topics {
    readonly name: string;
    readonly queue: any[];
    private config;
    protected subscribed: Subscription[];
    constructor(name: string, subjects: {
        [key: string]: Subject;
    }, config: Config);
    get status(): Websockets.Record;
    start(): Promise<void>;
    onMessage(message: any): void;
    onClose(message: any): void;
    next(): any;
    keepAlive(): Promise<void>;
    end(): Promise<void>;
}
export {};
