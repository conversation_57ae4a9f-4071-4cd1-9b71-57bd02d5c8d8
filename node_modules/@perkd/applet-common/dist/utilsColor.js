export const rgbToRgbo = (color) => {
    const regex = /rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(0|1|0?\.\d+)\s*)?\)/;
    const match = color?.match(regex);
    if (!match)
        return { r: 0, g: 0, b: 0, o: 1 };
    return {
        r: parseInt(match[1]),
        g: parseInt(match[2]),
        b: parseInt(match[3]),
        o: match[4] ? parseFloat(match[4]) : 1
    };
};
export const hexToRgbo = (color) => {
    const validHexColor = color && color.match(/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/);
    if (!validHexColor)
        return { r: 0, g: 0, b: 0, o: 1 };
    const c = (color.length > 3) ? color : '#' + color.slice(1, color.length).replace(/(.{1})/g, '$1$1');
    const r = parseInt(c.slice(1, 3), 16), g = parseInt(c.slice(3, 5), 16), b = parseInt(c.slice(5, 7), 16), o = c.slice(7, 9) ? Math.round(parseInt(c.slice(7, 9), 16) * 100 / 255) / 100 : 1;
    return { r, g, b, o };
};
export const colorToRgbo = (color) => {
    return color.startsWith('#') ? hexToRgbo(color) : rgbToRgbo(color);
};
export function rgbToHsl(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0;
    const l = (max + min) / 2;
    if (max !== min) {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
            case r:
                h = (g - b) / d + (g < b ? 6 : 0);
                break;
            case g:
                h = (b - r) / d + 2;
                break;
            case b:
                h = (r - g) / d + 4;
                break;
        }
        h /= 6;
    }
    return { h: h * 360, s, l };
}
export const getContrastColor = (color) => {
    const { r, g, b } = typeof color === 'string' ? colorToRgbo(color) : color;
    const sRGBtoLin = (colorChannel) => colorChannel <= 0.03928 ? colorChannel / 12.92 : Math.pow((colorChannel + 0.055) / 1.055, 2.4);
    const rgbToY = (r, g, b) => 0.2126 * sRGBtoLin(r) + 0.7152 * sRGBtoLin(g) + 0.0722 * sRGBtoLin(b);
    const luminance = rgbToY(r / 255, g / 255, b / 255);
    const hsl = rgbToHsl(r, g, b);
    // Special case 1: Very dark greens always get white text
    if (hsl.h >= 90 && hsl.h <= 150 && hsl.l < 0.3) {
        return '#FFFFFF';
    }
    // Special case 2: Bright yellows need a higher threshold (black text at higher luminance)
    if (hsl.h > 40 && hsl.h < 70 && hsl.l >= 0.5) {
        return luminance < 0.60 ? '#FFFFFF' : '#000000';
    }
    // Special case 3: Reds and oranges need a higher threshold
    if ((hsl.h >= 0 && hsl.h <= 15 && hsl.s > 0.6) ||
        (hsl.h > 15 && hsl.h <= 40 && hsl.s > 0.7)) {
        return luminance < 0.55 ? '#FFFFFF' : '#000000';
    }
    // Special case 4: Saturated greens need a higher threshold
    if (hsl.h >= 90 && hsl.h <= 150 && hsl.s > 0.6) {
        return luminance < 0.55 ? '#FFFFFF' : '#000000';
    }
    // Blue-greens to blues - modified to handle lower saturation teals
    if (hsl.h > 150 && hsl.h < 190) {
        return luminance < 0.48 ? '#FFFFFF' : '#000000';
    }
    // - Purples (hsl.h >= 270 && hsl.h <= 330 && hsl.s > 0.4)
    if (hsl.h >= 270 && hsl.h <= 330 && hsl.s > 0.4) {
        return luminance < 0.42 ? '#FFFFFF' : '#000000';
    }
    // Standard threshold (0.45) cases:
    // - Darker yellows (hsl.h > 40 && hsl.h < 70 && hsl.l < 0.5)
    // - Yellowish-greens (hsl.h >= 70 && hsl.h < 90 && hsl.s > 0.5)
    // - Less saturated greens (hsl.h >= 90 && hsl.h <= 150 && hsl.s <= 0.6)
    // - Blues (hsl.h >= 190 && hsl.h <= 250 && hsl.s > 0.6)
    // - Grays (hsl.s < 0.1)
    // - Earth tones/browns (hsl.h >= 20 && hsl.h <= 40 && hsl.s >= 0.4 && hsl.s <= 0.6 && hsl.l <= 0.6)
    if ((hsl.h > 40 && hsl.h < 70 && hsl.l < 0.5) ||
        (hsl.h >= 70 && hsl.h < 90 && hsl.s > 0.5) ||
        (hsl.h >= 90 && hsl.h <= 150 && hsl.s <= 0.6) ||
        (hsl.h >= 190 && hsl.h <= 250 && hsl.s > 0.6) ||
        (hsl.s < 0.1) ||
        (hsl.h >= 20 && hsl.h <= 40 && hsl.s >= 0.4 && hsl.s <= 0.6 && hsl.l <= 0.6)) {
        return luminance < 0.45 ? '#FFFFFF' : '#000000';
    }
    const whiteContrast = (1.0 + 0.05) / (luminance + 0.05);
    const blackContrast = (luminance + 0.05) / (0.0 + 0.05);
    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
};
export const blendWithBackground = (frontColor, backColor) => {
    const frontRgbo = typeof frontColor === 'string' ? colorToRgbo(frontColor) : frontColor;
    const backRgbo = typeof backColor === 'string' ? colorToRgbo(backColor) : backColor;
    const { r, g, b, o } = frontRgbo;
    return {
        r: Math.round(r * o + backRgbo.r * (1 - o)),
        g: Math.round(g * o + backRgbo.g * (1 - o)),
        b: Math.round(b * o + backRgbo.b * (1 - o)),
        o: 1
    };
};
export const applyOpacity = (color, opacity) => {
    const { r, g, b } = colorToRgbo(color);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};
