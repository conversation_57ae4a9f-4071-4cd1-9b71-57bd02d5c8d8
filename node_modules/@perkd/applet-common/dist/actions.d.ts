import { Applets, type ErrorResponse } from "./types/applets.js";
import { type Products as CrmProducts, type Billings as CrmBillings, Touchpoints } from "@perkd/crm-types";
import { type Orders } from './types/crm/orders.js';
import { type Bookings } from "./types/bookings.js";
import { type Fulfillments } from "./types/crm/fulfillments.js";
import { type Businesses } from "./types/businesses.js";
export type Item = {
    channel: string;
    kind: string;
    variantId: string;
    productId: string;
    sku: string;
    title: string;
    description: string;
    quantity: number;
    amount: number;
    price: number;
    unitPriceMeasure?: CrmProducts.UnitMeasure;
    unitPrice: number;
    units: number;
    unit: number;
    taxes: CrmBillings.Tax;
    images: string[];
    tags: Record<string, string[]>;
    options?: Orders.VariantOption[];
    inventory: number;
    status: {
        soldout: boolean;
        remain: number;
    };
    discount?: {
        amount: number;
        allocations: {
            amount: number;
            index: number;
        }[];
    };
    paymentMethods: string[];
    fulfillments: any[];
    properties: {
        name: string;
        value: string;
    }[];
    ttl: number;
    attributes?: any;
    expiresAt: string;
    capacity: number;
    admit?: number;
    custom?: Record<string, any>;
};
export type OrderParams = {
    masterId: string;
    cardId: string;
    withoutPayment: boolean;
    currency: string;
    items: Item[];
};
export type ReceiptParams = {
    receipt: Orders.Item[];
    fulfillment?: Fulfillments.Fulfillment;
    through?: Touchpoints.Touchpoint;
};
export declare function closeWindow(): void;
export declare function getConstants(): Promise<Applets.Constants | ErrorResponse>;
export type SlotPickerValue = {
    minTime: Date | string;
    maxTime: Date | string;
};
export type SlotPickerParam = {
    theme: Applets.ColorScheme;
    value: {
        minTime?: Date | string;
        maxTime?: Date | string;
    };
    options: {
        instant: boolean;
        leadTime: number;
        range: number;
        timeslot: number;
        schedule: Bookings.DailySchedule[];
        mode: Applets.DateTimePickerType;
    };
};
export declare function openSlotPicker(param: SlotPickerParam): Promise<SlotPickerValue | ErrorResponse>;
export type DurationPickerParam = {
    theme: Applets.ColorScheme;
    value?: number;
    options: {
        time: Date | string;
        duration: number;
        min: number;
        schedule: Bookings.DailySchedule[];
    };
};
export declare function openDurationPicker(param: DurationPickerParam): Promise<number | ErrorResponse>;
export declare function openDateTimePicker({ value, mode, theme }: {
    value?: string | undefined;
    mode?: Applets.DateTimePickerType | undefined;
    theme?: Applets.ColorScheme | undefined;
}): Promise<string | ErrorResponse>;
export declare function openScanner<T>(payload?: any): Promise<T | ErrorResponse>;
export declare function navToApplet(appletName: string, cardInstanceId: string, params?: any): Promise<void | ErrorResponse>;
export declare function openDialog<T>(params: {
    title?: string;
    message?: string;
    buttons?: any[];
    lang?: string;
}): Promise<T | ErrorResponse>;
export declare function emailTo(email: string, subject: string): Promise<void | ErrorResponse>;
export declare function callTo(number: string): Promise<void | ErrorResponse>;
export declare function copyText(text: string): Promise<void | ErrorResponse>;
export declare function showToast(text: string): Promise<void | ErrorResponse>;
export declare function addToBag<T>(params: any): Promise<T | ErrorResponse>;
export declare function openBag(): Promise<void | ErrorResponse>;
export declare function orderProducts<T>(params: OrderParams): Promise<T | ErrorResponse>;
export declare function reorderFromReceipt<T>(params: ReceiptParams): Promise<T | ErrorResponse>;
export declare function perkdRemote<T>(data: any): Promise<T | ErrorResponse>;
export declare function writeNFC<T>(payload: any): Promise<T | ErrorResponse>;
export declare function readNFC<T>(payload: any): Promise<T | ErrorResponse>;
export declare function lockNFC<T>(password: string, options?: any): Promise<T | ErrorResponse>;
export declare function fileShare<T>(file: any): Promise<T | ErrorResponse>;
export declare function trackWatch(message: string, error: any, data: any): Promise<void | ErrorResponse>;
export declare enum SoundEffect {
    Achieved = "achieved",
    Beep = "beep",
    BellChord = "bellchord",
    Cash = "cash",
    Cashier = "cashier",
    Chord = "chord",
    Correct = "correct",
    Done = "done",
    Fail = "fail",
    Happy = "happy",
    Magic = "magic",
    Notify = "notify",
    Scan = "scan",
    ServiceBell = "servicebell",
    Success = "success",
    SuccessBell = "successbell",
    Upsell = "upsell",
    WifiOn = "wifion"
}
export declare function playSound(name: SoundEffect): Promise<void | ErrorResponse>;
export declare enum VibrateEffect {
    Selection = "selection",
    ImpactLight = "impactLight",
    ImpactMedium = "impactMedium",
    ImpactHeavy = "impactHeavy",
    NotificationSuccess = "notificationSuccess",
    NotificationWarning = "notificationWarning",
    NotificationError = "notificationError"
}
export declare function vibrate(type: VibrateEffect): Promise<void | ErrorResponse>;
export declare enum WebMethod {
    NATIVE = "native",
    BROWSER = "browser",
    IN_APP = "web"
}
export declare function openLink(url: string, method?: WebMethod, context?: any): Promise<void | ErrorResponse>;
export declare function getWidgetData<T>(widgetName: string, params?: any): Promise<T | ErrorResponse>;
export declare function openHoursSettings<T>(hours?: Businesses.Hours): Promise<T | ErrorResponse>;
