import { formatDateTime as dayjs } from "@perkd/format-datetime";
import { formatAmount } from "./utils.js";
import { Discounts } from "@perkd/crm-types/dist/offers.js";
import { Fulfillments } from './types/crm/fulfillments.js';
import { Orders } from './types/crm/orders.js';
export function toItems(itemList, currency) {
    if (!itemList || itemList?.length === 0)
        return [];
    // format all items
    const updatedItemList = itemList.reduce((list, p) => {
        const unit = p.unitPriceMeasure?.unit;
        const price = p.bundleId ? (p.unitPrice || 0) * p.quantity : p.price;
        let measure = {};
        if (unit && p.units) {
            // booking room days/hours/minutes
            const quantity = p.units / p.quantity;
            switch (unit) {
                case 'day':
                    measure.unit = quantity > 1 ? unit : 'hour';
                    measure.value = quantity > 1 ? quantity : quantity * 24;
                    break;
                case 'minute':
                    const minutesToHours = quantity / 60;
                    measure.unit = minutesToHours >= 1 ? 'hour' : unit;
                    measure.value = minutesToHours >= 1 ? minutesToHours : quantity;
                    break;
                case 'hour':
                    measure.unit = unit;
                    measure.value = quantity;
                    break;
            }
        }
        const item = {
            id: p.id,
            productId: p.product.id,
            variantId: p.variant?.id,
            kind: p.kind,
            images: p.images,
            name: getItemName(p.product?.title, p.variant?.title),
            options: getOptions(p.variantOptions),
            quantity: p.quantity,
            measure: unit ? measure : undefined,
            amount: formatAmount(price, currency),
            originalAmount: formatAmount((p.discountAmount || 0) + price, currency),
            custom: p.custom,
            bundled: p.bundled,
            bundleId: p.bundleId
        };
        list.push(item);
        return list;
    }, []);
    // move bundledItems into the item
    const itemListWithBundleId = updatedItemList.filter((item) => !!item.bundleId);
    if (!itemListWithBundleId.length)
        return updatedItemList;
    return updatedItemList.filter((item) => !item.bundleId)
        .reduce((list, p) => {
        const bundledItems = updatedItemList.filter((item) => item.bundleId === p.id);
        list.push({
            ...p,
            bundledItems
        });
        return list;
    }, []);
}
export function toBagItems(items) {
    return items.map((item) => {
        const { product, variant, variantOptions, unitPrice, quantity, images, kind, properties, units, unitPriceMeasure, custom, admit } = item;
        const { id: productId, title: productTitle } = product || {};
        const { id: variantId, title: variantTitle } = variant || {};
        const title = getItemName(productTitle, variantTitle);
        const options = variantOptions.map((o) => {
            const { values, ...rest } = o;
            const updatedValues = values.map((v) => ({ ...v, selected: true }));
            return { values: updatedValues, ...rest };
        });
        return { productId, variantId, title, unitPrice, quantity, images, kind, options, properties, units, unitPriceMeasure, custom, admit };
    });
}
export function toFulfill(fulfillment, order) {
    if (!fulfillment)
        return undefined;
    const { when } = order || {};
    const { destination, type, recipient, receipt, scheduled, note } = fulfillment || {};
    const { brand, name: oriName, position, unitLevel, short, formatted } = destination || {};
    const address = short || formatted;
    const result = { type, recipient, receipt, note };
    switch (type) {
        case Fulfillments.Type.DINEIN: {
            const spots = position?.reduce((res, p) => {
                const { key, value } = p;
                res.push(`${key} ${value}`);
                return res;
            }, []) || [];
            Object.assign(result, { [type]: { icon: 'storefront', name: oriName, spots, address, scheduled: { desc: getScheduled(type, scheduled), time: when?.received && dayjs(when?.received).humane(), icon: 'clock-outline' } } });
            break;
        }
        case Fulfillments.Type.PICKUP:
        case Fulfillments.Type.STORE: {
            const name = [oriName, unitLevel].filter((s = '') => !!s).join('');
            const icon = type === Fulfillments.Type.PICKUP ? 'takeaway' : 'storefront';
            Object.assign(result, { [type]: { icon, name, brand, address, scheduled: { desc: getScheduled(type, scheduled), time: when?.collected && dayjs(when?.collected).humane(), icon: 'clock-outline' } } });
            break;
        }
        case Fulfillments.Type.DELIVER: {
            const name = [oriName, unitLevel].filter((s = '') => !!s).join('');
            const deliver = { icon: 'map', name, address, scheduled: { desc: getScheduled(type, scheduled), time: when?.delivered && dayjs(when?.delivered).humane(), icon: 'moto-delivery' } };
            if (fulfillment.deliver) {
                const { carrierName, trackingNumber, trackingUrl } = fulfillment.deliver || {};
                Object.assign(deliver, { tracking: { carrierName, trackingNumber, trackingUrl } });
            }
            if (fulfillment.when?.delivered) {
                Object.assign(deliver, { deliveredAt: dayjs(fulfillment.when.delivered).format('L') });
            }
            Object.assign(result, { [type]: deliver });
            break;
        }
    }
    return result;
}
export function toDiscounts(discountList, currency) {
    if (!discountList || !discountList.length)
        return;
    return discountList.reduce((list, d) => {
        const offer = {
            offerId: d.offerId || '',
            name: d.name || '',
            amount: d.amount !== undefined ? formatAmount(d.amount, currency) : ''
        };
        list.push(offer);
        return list;
    }, []);
}
export function toPaymentMethods(billingList, currency) {
    if (!billingList || !billingList.length)
        return;
    return billingList.reduce((list, d) => {
        const method = {
            method: d.paymentMethod?.wallet || d.paymentMethod?.method || '',
            amount: d.paymentMethod?.amount !== undefined ? formatAmount(d.paymentMethod.amount, currency) : ''
        };
        list.push(method);
        return list;
    }, []);
}
export function toBill(order, fulfillment) {
    const { currency, discountList, itemList } = order || {};
    const { discountAmount = 0, taxAmount = 0, amount = 0, subtotalPrice = 0, shippingPrice = 0 } = order || {};
    const fulfillDiscount = discountList
        ?.filter(discount => discount.targetType === Discounts.TargetType.SHIPPING)
        .reduce((res, discount) => res + (discount.amount || 0), 0) || 0;
    const fulfillAmount = shippingPrice + fulfillDiscount;
    return {
        quantity: itemList.reduce((res, item) => res + (!item.bundleId ? item.quantity : 0), 0),
        total: formatAmount(amount, currency),
        subtotal: formatAmount(subtotalPrice + discountAmount, currency),
        discount: formatAmount(discountAmount, currency),
        fulfill: {
            type: fulfillment?.type,
            fulfillAmount,
            amount: formatAmount(shippingPrice, currency),
            originalAmount: formatAmount(fulfillAmount, currency),
        },
        tax: formatAmount(taxAmount, currency),
    };
}
export function getScheduled(type, scheduled) {
    if (!scheduled)
        return '';
    const todayObj = dayjs();
    const { minTime, maxTime } = scheduled;
    if (minTime && maxTime) {
        const isSameDay = dayjs(minTime).isSame(maxTime, 'd');
        const afterPrepareStart = dayjs(minTime).isBefore(todayObj);
        const beforePrepareEnd = dayjs(maxTime).isAfter(todayObj);
        const showMax = (type === Fulfillments.Type.DINEIN || type === Fulfillments.Type.STORE || afterPrepareStart) && beforePrepareEnd;
        const minWithInTwo = Math.abs(dayjs(minTime).diff(todayObj, 'd')) < 2;
        const maxWithInTwo = Math.abs(dayjs(maxTime).diff(todayObj, 'd')) < 2;
        if (showMax)
            return dayjs(maxTime).smartDateTime();
        return dayjs.getHumanePeriod(minTime, maxTime, undefined, isSameDay || (minWithInTwo && maxWithInTwo));
    }
    else if (minTime || maxTime) {
        return dayjs(minTime || maxTime).smartDateTime();
    }
    else {
        return '';
    }
}
export function getOrderId(order) {
    return order.receipt?.number || order.id || '';
}
export function getOrderStatus(order, fulfillment) {
    if (order.when?.returned)
        return Orders.Step.RETURNED;
    if (order.when?.cancelled)
        return Orders.Step.CANCELLED;
    if (order.when?.declined)
        return Orders.Step.DECLINED;
    const orderStep = order.flow?.steps[order.flow?.at || 0] || '';
    const fulfillStep = fulfillment?.flow?.steps?.[fulfillment?.flow?.at || 0] || '';
    if (orderStep === Orders.Step.RECEIVED || orderStep === Orders.Step.PACKED) {
        return fulfillStep || Fulfillments.Step.REQUESTED;
    }
    // for dine in order, no order flow, show fulfill setp instead
    return orderStep || fulfillStep;
}
export function getItemsType(order) {
    const fulfillmentServices = order.itemList?.map((item) => item.variant?.fulfillmentService)
        .filter((k, i, arry) => i === arry.findIndex((ak) => ak === k)) || [];
    // 'none' || 'kitchen' || 'digital'
    return (fulfillmentServices.length > 1) ? 'mixed' : fulfillmentServices[0];
}
export function getItemName(title, variantName) {
    return title !== variantName ? [title, variantName].filter((t) => !!t).join(' - ') : (title || '');
}
export function getOptions(variantOptions) {
    return variantOptions.reduce((res, option) => {
        const { name, title, values } = option;
        const valueStrings = values.map(v => v.name || v.title);
        if (valueStrings.length > 0) {
            res.push({ name: name || title, value: valueStrings.join(',') });
        }
        return res;
    }, []);
}
export function isPaid(order, fulfillment) {
    const steps = [Orders.Step.DECLINED, Orders.Step.CANCELLED, Orders.Step.RETURNED];
    const status = getOrderStatus(order, fulfillment);
    return steps.includes(status) ? null : !!order.when.paid;
}
export function isManualPayment(order) {
    const { billingList } = order;
    return !!billingList.find((b) => b.paymentMethod?.method === 'manual');
}
