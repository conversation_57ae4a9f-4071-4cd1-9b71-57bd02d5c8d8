import { Memberships } from "./types/memberships.js";
import { Microsites } from "./types/microsites.js";
import { Orders } from "./types/orders.js";
import { Persons } from "./types/persons.js";
import { Sales } from "./types/sales.js";
import { Staffs } from "./types/staffs.js";
import { Businesses } from "./types/businesses.js";
import { Products } from "./types/products.js";
import { Offers } from "./types/offers.js";
import { Places } from "./types/places.js";
import { Fulfillments } from "./types/crm/fulfillments.js";
import { type ErrorResponse } from './types/applets.js';
import { Bookings } from "./types/bookings.js";
export declare const Membership: {
    API: {
        qualify: {
            method: string;
            path: string;
        };
        join: {
            method: string;
            path: string;
        };
        cancel: {
            method: string;
            path: string;
        };
        renew: {
            method: string;
            path: string;
        };
        update: {
            method: string;
            path: string;
        };
        terminate: {
            method: string;
            path: string;
        };
        recent: {
            method: string;
            path: string;
        };
        export: {
            method: string;
            path: string;
        };
        recruit: {
            method: string;
            path: string;
        };
    };
    qualify: (base: string, cardId: string, body: Memberships.QualifyParams) => Promise<ErrorResponse | Memberships.QualifyResponse>;
    join: (base: string, cardId: string, body: any) => Promise<ErrorResponse | Memberships.JoinResponse>;
    cancel: (base: string, cardId: string, membershipId: string, reason: string) => Promise<ErrorResponse | Memberships.Membership>;
    renew: (base: string, cardId: string, membershipId: string, endTime?: string) => Promise<ErrorResponse | Memberships.Membership>;
    update: (base: string, cardId: string, membershipId: string, endTime: string) => Promise<ErrorResponse | Memberships.Membership>;
    terminate: (base: string, cardId: string, membershipId: string, reason: string) => Promise<ErrorResponse | Memberships.Membership>;
    recent: (base: string, cardId: string) => Promise<ErrorResponse | Memberships.MembershipWithProfile[]>;
    export: (base: string, cardId: string) => Promise<ErrorResponse | Memberships.ExportResponse>;
    recruitByDate: (base: string, cardId: string, filter: Memberships.RecruitFilter) => Promise<ErrorResponse | Memberships.DailyRecruit[]>;
};
export declare const Order: {
    API: {
        qualify: {
            method: string;
            path: string;
        };
        create: {
            method: string;
            path: string;
        };
        cancel: {
            method: string;
            path: string;
        };
        recent: {
            method: string;
            path: string;
        };
        getList: {
            method: string;
            path: string;
        };
        getPackedList: {
            method: string;
            path: string;
        };
        issueInvoice: {
            method: string;
            path: string;
        };
        printReceipt: {
            method: string;
            path: string;
        };
        cancelUnfulfilled: {
            method: string;
            path: string;
        };
        relocate: {
            method: string;
            path: string;
        };
        markCollected: {
            method: string;
            path: string;
        };
    };
    qualify: (base: string, cardId: string, body: Orders.QualifyParams) => Promise<ErrorResponse | Orders.QualifyResponse>;
    create: (base: string, cardId: string, body: Orders.CreateParams) => Promise<ErrorResponse | Orders.Order>;
    cancel: (base: string, cardId: string, orderId: string) => Promise<ErrorResponse | Orders.Order>;
    recent: (base: string, cardId: string) => Promise<ErrorResponse | Orders.OrderWithProfile[]>;
    getList: (base: string, cardId: string) => Promise<ErrorResponse | Orders.OrderWithFulfillments[]>;
    getPackedList: (base: string, cardId: string) => Promise<ErrorResponse | {
        nextEta: string;
    } | Orders.OrderWithFulfilled[]>;
    issueInvoice: (base: string, cardId: string, orderId: string, taxId: string) => Promise<ErrorResponse | {
        print: boolean;
        taxId: string;
    }>;
    printReceipt: (base: string, cardId: string, orderId: string) => Promise<void | ErrorResponse>;
    cancelUnfulfilled: (base: string, cardId: string, orderId: string, reason?: string) => Promise<ErrorResponse | Orders.Order>;
    relocate: (base: string, cardId: string, body: Orders.RelocateParams) => Promise<ErrorResponse | string[]>;
    markCollected: (base: string, cardId: string, orderId: string) => Promise<void | ErrorResponse | {
        nextEta: string;
    }>;
};
export declare const Sale: {
    API: {
        getUnpaid: {
            method: string;
            path: string;
        };
        getPaid: {
            method: string;
            path: string;
        };
        markPaid: {
            method: string;
            path: string;
        };
    };
    getUnpaid: (base: string, cardId: string) => Promise<ErrorResponse | Sales.GetUnpaidResponse>;
    getPaid: (base: string, cardId: string) => Promise<ErrorResponse | Sales.GetUnpaidResponse>;
    markPaid: (base: string, cardId: string, orderId: string, body?: {
        method: string;
        amount?: number;
    }) => Promise<ErrorResponse | Sales.MarkPaidResponse>;
};
export declare const Person: {
    API: {
        search: {
            method: string;
            path: string;
        };
    };
    search: (base: string, cardId: string, search: Persons.Search) => Promise<ErrorResponse | Persons.PersonWithMembership[]>;
};
export declare const Staff: {
    API: {
        getList: {
            method: string;
            path: string;
        };
        qrCodeLogin: {
            method: string;
            path: string;
        };
    };
    getList: (base: string, cardId: string) => Promise<ErrorResponse | Staffs.Staff[]>;
    qrCodeLogin: (base: string, cardId: string) => Promise<string | ErrorResponse>;
};
export declare const Business: {
    API: {
        provisionMerchant: {
            method: string;
            path: string;
        };
        verifyMerchant: {
            method: string;
            path: string;
        };
        provisionPrinter: {
            method: string;
            path: string;
        };
        detachPrinter: {
            method: string;
            path: string;
        };
        deletePrinter: {
            method: string;
            path: string;
        };
    };
    provisionMerchant: (base: string, cardId: string, placeId: string, body: {
        [key: string]: any;
    }) => Promise<ErrorResponse | undefined>;
    verifyMerchant: (base: string, cardId: string, placeId: string) => Promise<ErrorResponse | Businesses.Merchant>;
    provisionPrinter: (base: string, cardId: string, placeId: string, body: {
        [key: string]: any;
    }) => Promise<ErrorResponse | undefined>;
    detachPrinter: (base: string, cardId: string, printerId: string) => Promise<ErrorResponse | undefined>;
    deletePrinter: (base: string, cardId: string, printerId: string) => Promise<ErrorResponse | undefined>;
};
export declare const Microsite: {
    API: {
        signup: {
            method: string;
            path: string;
        };
        download: {
            method: string;
            path: string;
        };
        provisionPlace: {
            method: string;
            path: string;
        };
    };
    signup: (countryCode: string, phoneNumber: string, language: string, eventId: string) => Promise<Microsites.SignupResponse | ErrorResponse>;
    appDownload(): void;
};
export declare const Event: {
    API: {
        getList: {
            method: string;
            path: string;
        };
    };
    getList: (base: string, cardId: string, body: {
        [key: string]: any;
    }) => Promise<ErrorResponse | undefined>;
};
export declare const Product: {
    API: {
        getList: {
            method: string;
            path: string;
        };
        setAvailability: {
            method: string;
            path: string;
        };
    };
    getList: (base: string, cardId: string, placeId: string, channel?: string) => Promise<ErrorResponse | Products.Product[]>;
    setAvailability: (base: string, cardId: string, variantId: string, status: Products.Availability) => Promise<ErrorResponse | undefined>;
};
export declare const Fulfillment: {
    API: {
        getList: {
            method: string;
            path: string;
        };
        getCancelledList: {
            method: string;
            path: string;
        };
        fulfillItems: {
            method: string;
            path: string;
        };
        getDetail: {
            method: string;
            path: string;
        };
        startQueue: {
            method: string;
            path: string;
        };
        getPrepareList: {
            method: string;
            path: string;
        };
        startPrepare: {
            method: string;
            path: string;
        };
        cancelPrepare: {
            method: string;
            path: string;
        };
        markPacked: {
            method: string;
            path: string;
        };
    };
    getList: (base: string, cardId: string, filter?: {
        type?: string;
        stations?: string[];
    }) => Promise<ErrorResponse | Fulfillments.Fulfillment[] | {
        nextEta: string;
    }>;
    getCancelledList: (base: string, cardId: string, filter?: {
        type?: string;
        stations?: string[];
    }) => Promise<ErrorResponse | Fulfillments.Fulfillment[] | {
        nextEta: string;
    }>;
    fulfillItems: (base: string, cardId: string, fulfillmentId: string, items: {
        id: string;
        quantity: number;
    }[]) => Promise<void | ErrorResponse>;
    getDetail: (base: string, cardId: string, fulfillmentId: string) => Promise<ErrorResponse | Fulfillments.Fulfillment>;
    startQueue: (base: string, cardId: string, fulfillmentId: string) => Promise<ErrorResponse | Fulfillments.Fulfillment>;
    getPrepareList: (base: string, cardId: string) => Promise<ErrorResponse | Fulfillments.Fulfillment[] | {
        nextEta: string;
    }>;
    startPrepare: (base: string, cardId: string, fulfillmentId: string) => Promise<void | ErrorResponse>;
    cancelPrepare: (base: string, cardId: string, fulfillmentId: string) => Promise<void | ErrorResponse>;
    markPacked: (base: string, cardId: string, fulfillmentId: string) => Promise<void | ErrorResponse | {
        nextEta: string;
    }>;
};
export declare const Booking: {
    API: {
        getVenues: {
            method: string;
            path: string;
        };
        getAvailability: {
            method: string;
            path: string;
        };
        getItems: {
            method: string;
            path: string;
        };
    };
    getVenues: (base: string, cardId: string, masterId: string, tenantCode: string) => Promise<ErrorResponse | Bookings.Venue[]>;
    getAvailability: (base: string, cardId: string, masterId: string, tenantCode: string, body: Bookings.BodyParams) => Promise<ErrorResponse | Bookings.Availability>;
    getItems: (base: string, cardId: string, masterId: string, tenantCode: string, body: Bookings.BodyParams) => Promise<ErrorResponse | Bookings.Item[]>;
};
export declare const Offer: {
    API: {
        getQualified: {
            method: string;
            path: string;
        };
        issue: {
            method: string;
            path: string;
        };
    };
    getQualified: (base: string, cardId: string, body: Offers.QualifiedParams) => Promise<ErrorResponse | Offers.QualifiedResponse>;
    issue: (base: string, cardId: string, body: Offers.IssueParams) => Promise<ErrorResponse | Offers.IssueResponse>;
};
export declare const Place: {
    API: {
        updateHours: {
            method: string;
            path: string;
        };
    };
    updateHours: (base: string, cardId: string, placeId: string, body: Places.hoursParams) => Promise<ErrorResponse | Places.Place>;
};
