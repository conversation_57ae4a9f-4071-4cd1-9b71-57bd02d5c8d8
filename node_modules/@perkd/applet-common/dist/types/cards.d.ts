export declare namespace Cards {
    enum Barcode {
        QRCODE = "QRCODE",
        AZTEC = "AZTEC",
        DATAMATRIX = "DATAMATRIX",
        CODE128 = "CODE128"
    }
    const DEFAULT_BARCODE_TYPE = Barcode.CODE128;
    const CODE_SQUARE: string[];
    type Card = {
        image?: string;
        cardNumber?: string;
        startTime?: string;
        endTime?: string;
        barcodeType?: string;
        barcodePatterns?: string;
    };
    type MembershipCard = Card & {
        name: string;
        membershipId: string;
        programId: string;
        programName: string;
        tierLevel: number;
        type: string;
    };
    type Position = {
        top: number;
        left: number;
    };
}
