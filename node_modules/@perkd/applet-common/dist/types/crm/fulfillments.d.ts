import { Fulfillments as CrmFulfillments } from '@perkd/crm-types/dist/fulfillments.js';
import { Orders as CrmOrders } from '@perkd/crm-types/dist/orders.js';
import { Places } from '../places';
export declare namespace Fulfillments {
    enum Type {
        DIGITAL = "digital",
        KITCHEN = "kitchen",
        STORE = "store",
        PICKUP = "pickup",
        DELIVER = "deliver",
        DINEIN = "dinein",
        VENDING_MACHINE = "vending"
    }
    enum Step {
        ORDERED = "ordered",
        REQUESTED = "requested",
        ALLOCATED = "allocated",
        QUEUED = "queued",
        PREPARE = "prepare",
        PACKED = "packed",
        COLLECTED = "collected",
        DELIVERED = "delivered"
    }
    type MinMaxTime = {
        minTime: Date;
        maxTime: Date;
    };
    type Product = {
        id: string;
        bundleList: any[];
        title: string;
        brand: string;
        external?: any;
        tags?: any;
        behaviors?: any;
    };
    type Variant = {
        id: string;
        productId: string;
        kind: string;
        title: string;
        gtin: {
            value: string;
        };
        sku?: string;
        inventory?: {
            management: string;
            policy: string;
            lowQuantityWarningThreshold: number;
            quantity: number;
        };
        prices?: Array<{
            name: string;
            price: {
                value: number;
            };
            salePrice: {
                value: number;
            };
            countries: string[];
        }>;
        taxable?: boolean;
        options?: any[];
        fulfillmentService?: string;
        preparation?: {
            time: number;
            placeId: string;
        };
        external?: {
            shopify: {
                variantId: string;
                inventoryItemId: string;
            };
        };
        imageIds?: string[];
    };
    type Item = CrmOrders.Item & {
        id: string;
        fulfilledAt?: string;
        fulfilled?: {
            quantity: number;
        };
    };
    type Fulfillment = {
        id: string;
        type: Type;
        recipient?: CrmFulfillments.Recipient;
        priority?: CrmFulfillments.Priority;
        origin?: Places.Spot;
        scheduled?: MinMaxTime & {
            firstEta?: Date;
        };
        minTime?: Date;
        maxTime?: Date;
        itemList?: Item[];
        currency?: string;
        unitPrice?: number;
        discount?: number;
        price?: number;
        note?: string;
        flow?: CrmFulfillments.Flow;
        status: CrmFulfillments.Status;
        prepare?: {
            startTime: Date;
            endTime: Date;
        };
        pickup?: MinMaxTime;
        deliver?: CrmFulfillments.Deliver;
        provider?: string;
        receipt?: {
            queue?: string;
            authorisation?: string;
        };
        external?: any;
        placeId?: string;
        mainFulfillmentType?: Omit<Type, Type.DIGITAL | Type.KITCHEN | Type.STORE | Type.VENDING_MACHINE>;
        mainFulfillmentId?: string;
        destination: {
            type?: string;
            placeId?: string;
            formatted?: string;
            unitLevel?: string;
            postCode?: string;
            country?: string;
            geo?: {
                type: string;
                coordinates: number[];
            };
            brand?: string;
            position?: Places.Position;
            short?: string;
            name?: string;
            ttl?: number;
            expiresAt?: string;
        };
        when?: {
            ordered: string | null;
            requested: string | null;
            allocated: string | null;
            queued: string | null;
            prepare: string | null;
            packed: string | null;
            collected: string | null;
            delivered: string | null;
        };
    };
}
