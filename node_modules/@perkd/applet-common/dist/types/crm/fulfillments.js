export var Fulfillments;
(function (Fulfillments) {
    let Type;
    (function (Type) {
        Type["DIGITAL"] = "digital";
        Type["KITCHEN"] = "kitchen";
        Type["STORE"] = "store";
        Type["PICKUP"] = "pickup";
        Type["DELIVER"] = "deliver";
        Type["DINEIN"] = "dinein";
        Type["VENDING_MACHINE"] = "vending";
    })(Type = Fulfillments.Type || (Fulfillments.Type = {}));
    let Step;
    (function (Step) {
        Step["ORDERED"] = "ordered";
        Step["REQUESTED"] = "requested";
        Step["ALLOCATED"] = "allocated";
        Step["QUEUED"] = "queued";
        Step["PREPARE"] = "prepare";
        Step["PACKED"] = "packed";
        Step["COLLECTED"] = "collected";
        Step["DELIVERED"] = "delivered";
    })(Step = Fulfillments.Step || (Fulfillments.Step = {}));
})(Fulfillments || (Fulfillments = {}));
