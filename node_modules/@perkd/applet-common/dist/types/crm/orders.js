export var Orders;
(function (Orders) {
    let Step;
    (function (Step) {
        Step["NEW"] = "new";
        Step["RECEIVED"] = "received";
        Step["PACKED"] = "packed";
        Step["ALLOCATED"] = "allocated";
        Step["COLLECTED"] = "collected";
        Step["DELIVERED"] = "delivered";
        Step["ACCEPTED"] = "accepted";
        Step["DECLINED"] = "declined";
        Step["FULFILLED"] = "fulfilled";
        Step["CANCELLED"] = "cancelled";
        Step["RETURNED"] = "returned";
    })(Step = Orders.Step || (Orders.Step = {}));
    let CancelReason;
    (function (CancelReason) {
        CancelReason["CUSTOMER"] = "customer";
        CancelReason["EXPIRED"] = "expired";
        CancelReason["DELETED"] = "deleted";
        CancelReason["REFUND"] = "refund";
        CancelReason["EXCHANGE"] = "exchange";
        CancelReason["ERROR"] = "error";
        CancelReason["INVENTORY"] = "inventory";
        CancelReason["FRAUD"] = "fraud";
        CancelReason["PATCH"] = "_patch";
    })(CancelReason = Orders.CancelReason || (Orders.CancelReason = {}));
})(Orders || (Orders = {}));
