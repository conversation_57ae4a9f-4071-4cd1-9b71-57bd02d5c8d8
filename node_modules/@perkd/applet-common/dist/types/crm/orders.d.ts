import { Orders as OriOrders } from '@perkd/crm-types/dist/orders.js';
import { Billings } from '@perkd/crm-types/dist/billings.js';
import { Touchpoints } from '@perkd/crm-types/dist/touchpoints.js';
import { Fulfillments } from './fulfillments.js';
export declare namespace Orders {
    enum Step {
        NEW = "new",
        RECEIVED = "received",
        PACKED = "packed",
        ALLOCATED = "allocated",
        COLLECTED = "collected",
        DELIVERED = "delivered",
        ACCEPTED = "accepted",
        DECLINED = "declined",
        FULFILLED = "fulfilled",
        CANCELLED = "cancelled",
        RETURNED = "returned"
    }
    enum CancelReason {
        CUSTOMER = "customer",
        EXPIRED = "expired",
        DELETED = "deleted",
        REFUND = "refund",
        EXCHANGE = "exchange",
        ERROR = "error",
        INVENTORY = "inventory",
        FRAUD = "fraud",
        PATCH = "_patch"
    }
    type Variant = {
        id: string;
        productId: string;
        kind: string;
        title: string;
        gtin: {
            value: string;
        };
        sku: string;
        inventory: {
            management: string;
            policy: string;
            lowQuantityWarningThreshold: number;
            quantity: number;
        };
        prices: {
            name: string;
            price: {
                value: number;
            };
            salePrice: {
                value: number;
            };
            paymentMethods: any[];
            countries: any[];
        }[];
        taxable: boolean;
        options: any[];
        fulfillmentService: string;
        preparation: {
            time: number;
        };
        external: {
            shopify: {
                variantId: string;
                inventoryItemId: string;
            };
        };
        imageIds: string[];
    };
    type VariantOption = {
        key: string;
        title: string;
        name?: string;
        values: {
            name?: string;
            title: string;
            price?: number;
            image?: string;
            variantId?: string;
        }[];
        value?: any;
    };
    type Item = OriOrders.Item & {
        variant: Variant;
        price: number;
    };
    type Order = OriOrders.Order & {
        receipt: {
            number?: string;
            send?: boolean;
            taxId?: string;
            merchantTaxId?: string;
            invoice?: object;
        };
        cost?: number;
        tags: {
            system: string[];
            user: string[];
        };
        visible: boolean;
        acquired?: Touchpoints.Touchpoint;
        external?: {
            perkd?: {
                userId: string;
                cardId: string;
            };
            shopify?: {
                customerId: string;
                orderId: string;
            };
        };
        when?: {
            received: string | null;
            paid: string | null;
            packed: string | null;
            collected: string | null;
            dispatched: string | null;
            delivered: string | null;
            accepted: string | null;
            declined: string | null;
            fulfilled: string | null;
            returned: string | null;
            cancelled: string | null;
        };
        reasons?: {
            cancelled?: CancelReason;
        };
        modifiedAt: string | null;
        deletedAt: string | null;
        itemList: Item[];
        billingList?: Billings.Billing[];
        fulfillments?: Fulfillments.Fulfillment[];
    };
}
