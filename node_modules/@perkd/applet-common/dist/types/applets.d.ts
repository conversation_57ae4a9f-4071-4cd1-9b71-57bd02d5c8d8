declare global {
    interface Window {
        $perkd: {
            onMessage(param: MessageParam): void;
            emit(name: string, data: any): string;
            do: (action: string, param?: any) => Promise<any>;
        };
        $data: $Data;
        $bag: $Bag;
        ReactNativeWebView?: {
            postMessage(message: string): void;
        };
    }
    interface WindowEventMap {
        "app.pause": CustomEvent;
        "app.resume": CustomEvent;
    }
}
export declare namespace Applets {
    enum Language {
        ENGLISH = "en",
        CHINESE_SIMPLIFIED = "zh-Hans",
        CHINESE_TRADITIONAL = "zh-Hant",
        CHINESE_TRADITIONAL_HK = "zh-Hant-HK",
        CHINESE_TRADITIONAL_TW = "zh-Hant-TW",
        JAPANESE = "ja",
        KOREAN = "ko",
        BAHASA_MELAYU = "ms",
        BAHASA_INDONESIA = "id"
    }
    const LANGUAGES: {
        DEFAULT: Language;
        SUPPORTED: Language[];
        FALLBACKS: {
            "zh-Hant-HK": Language[];
            "zh-Hant-TW": Language[];
            "zh-Hant": Language[];
            "zh-Hans": Language[];
            ms: Language[];
            id: Language[];
            ja: Language[];
            ko: Language[];
            en: Language[];
            default: Language[];
        };
    };
    enum DateTimePickerType {
        TIME = "time",
        DATE = "date",
        DATETIME = "datetime"
    }
    enum ColorScheme {
        DARK = "dark",
        LIGHT = "light"
    }
    type Theme = {
        background?: string;
        text?: string;
        primary?: string;
        secondary?: string;
        tertiary?: string;
        accent?: string;
    };
    type Constants = {
        PATH: {
            BASE: string;
            RSRC: string;
            SHARED: {
                RSRC: string;
            };
        };
        DEVICE: any;
        LANGUAGE: string;
        COUNTRY: string;
        PERSON: any;
        CARD: any;
        CARDMASTER: any;
        CONTENT: any;
        FONTCSS: string;
        FONTPATH: string;
        CONTEXT?: any;
        UI: any;
    };
    const DefaultTheme: {
        light: {
            background: string;
            text: string;
            primary: string;
            secondary: string;
            tertiary: string;
            accent: string;
        };
        dark: {
            background: string;
            text: string;
            primary: string;
            secondary: string;
            tertiary: string;
            accent: string;
        };
    };
    type Environment = {
        DEVICE: Constants['DEVICE'];
        CARD: Constants['CARD'];
        CARDMASTER: Constants['CARDMASTER'];
        COUNTRY: string;
        LANGUAGE: string;
        CONTENT: {
            id: string;
            viewport: number;
            param: any;
            language: string;
        };
        FONTCSS: string;
        FONTPATH: string;
        CONTEXT: Constants['CONTEXT'];
        PERSON: Constants['PERSON'];
        deviceScheme: ColorScheme;
    };
    type Widget = {
        API_BASE: string;
        API: any;
        COLOR_SCHEME: ColorScheme;
        SETTINGS: any;
        MASTER_SETTINGS: any;
        MEMBERSHIP_PROGRAMS: any;
    };
    type Icon = {
        name: string;
        type?: string;
        class?: string;
    };
    type Navback = {
        type: string;
        onClick: (event: MouseEvent, ...args: any[]) => void;
    };
    type SocketConfig = {
        cardId: string;
        masterId: string;
        socketUrl: string;
    };
    type SocketSubject = {
        key: string;
        query?: string | object;
        start?: number | Date;
        end?: number | Date;
        step?: number;
    };
}
export interface ErrorResponse {
    error: Error;
}
export type Error = {
    statusCode?: number;
    statusMessage?: string;
    code?: number;
    message?: string;
    [key: string]: any;
};
export type Translations = {
    [key: string]: any;
};
export type MessageParam = {
    id: string;
    name: string;
    data: any;
    error?: any;
};
export interface $Data {
    [prop: string]: any;
    save(): void;
    add(prop: string): void;
}
export interface $Bag {
    addItems(items: any[]): void;
    updateItems(items: any[]): void;
    removeItems(items: any[]): void;
    items?: any;
    amount?: number;
}
