import { Fulfillments } from "@perkd/crm-types/dist/fulfillments.js";
export declare namespace Receipts {
    type ItemOption = {
        name: string;
        value: string;
    };
    type Item = {
        id?: string;
        productId?: string;
        variantId?: string;
        kind?: string;
        images?: string[];
        name: string;
        options?: ItemOption[];
        quantity: number;
        measure?: {
            unit: string;
            value: number;
        };
        amount: string;
        originalAmount: string;
        bundled?: string[];
        bundleId?: string;
        bundledItems?: Item[];
        custom?: Record<string, any>;
    };
    type Discount = {
        offerId: string;
        name: string;
        amount: string;
    };
    type PaymentMethod = {
        method: string;
        amount: string;
    };
    type DineinFulfill = {
        icon: string;
        name: string;
        spots: string[];
        address: string;
        scheduled?: {
            icon: string;
            desc: string;
            time?: string;
        };
    };
    type PickupFulfill = {
        icon: string;
        name: string;
        address: string;
        brand: string;
        scheduled: {
            icon: string;
            desc: string;
            time?: string;
        };
    };
    type DeliverFulfill = {
        icon: string;
        name: string;
        address: string;
        scheduled: {
            icon: string;
            desc: string;
            time?: string;
        };
        tracking: {
            carrierName: string;
            trackingNumber: string;
            trackingUrl: string;
        };
        deliveredAt: string;
    };
    type Fulfillment = {
        type: Fulfillments.Type;
        recipient?: Fulfillments.Recipient;
        receipt?: {
            queue?: string;
            authorisation?: string;
        };
        note?: string;
    } & {
        [key in Fulfillments.Type]?: DineinFulfill | DeliverFulfill | PickupFulfill;
    };
}
