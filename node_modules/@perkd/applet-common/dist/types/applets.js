export var Applets;
(function (Applets) {
    let Language;
    (function (Language) {
        Language["ENGLISH"] = "en";
        Language["CHINESE_SIMPLIFIED"] = "zh-Hans";
        Language["CHINESE_TRADITIONAL"] = "zh-Hant";
        Language["CHINESE_TRADITIONAL_HK"] = "zh-Hant-HK";
        Language["CHINESE_TRADITIONAL_TW"] = "zh-Hant-TW";
        Language["JAPANESE"] = "ja";
        Language["KOREAN"] = "ko";
        Language["BAHASA_MELAYU"] = "ms";
        Language["BAHASA_INDONESIA"] = "id";
    })(Language = Applets.Language || (Applets.Language = {}));
    Applets.LANGUAGES = {
        DEFAULT: Language.ENGLISH,
        SUPPORTED: [
            Language.ENGLISH,
            Language.CHINESE_SIMPLIFIED,
            Language.CHINESE_TRADITIONAL
        ],
        FALLBACKS: {
            [Language.CHINESE_TRADITIONAL_HK]: [Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.ENGLISH],
            [Language.CHINESE_TRADITIONAL_TW]: [Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_SIMPLIFIED, Language.ENGLISH],
            [Language.CHINESE_TRADITIONAL]: [Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL_HK, Language.CHINESE_SIMPLIFIED, Language.ENGLISH],
            [Language.CHINESE_SIMPLIFIED]: [Language.CHINESE_SIMPLIFIED, Language.CHINESE_TRADITIONAL, Language.CHINESE_TRADITIONAL_TW, Language.CHINESE_TRADITIONAL_HK, Language.ENGLISH],
            [Language.BAHASA_MELAYU]: [Language.BAHASA_MELAYU, Language.BAHASA_INDONESIA, Language.ENGLISH],
            [Language.BAHASA_INDONESIA]: [Language.BAHASA_INDONESIA, Language.BAHASA_MELAYU, Language.ENGLISH],
            [Language.JAPANESE]: [Language.JAPANESE, Language.ENGLISH],
            [Language.KOREAN]: [Language.KOREAN, Language.ENGLISH],
            [Language.ENGLISH]: [Language.ENGLISH],
            default: [Language.ENGLISH],
        },
    };
    let DateTimePickerType;
    (function (DateTimePickerType) {
        DateTimePickerType["TIME"] = "time";
        DateTimePickerType["DATE"] = "date";
        DateTimePickerType["DATETIME"] = "datetime";
    })(DateTimePickerType = Applets.DateTimePickerType || (Applets.DateTimePickerType = {}));
    let ColorScheme;
    (function (ColorScheme) {
        ColorScheme["DARK"] = "dark";
        ColorScheme["LIGHT"] = "light";
    })(ColorScheme = Applets.ColorScheme || (Applets.ColorScheme = {}));
    Applets.DefaultTheme = {
        light: {
            background: '#FFFFFFFF',
            text: '#1A1A1AFF',
            primary: '#F99300FF',
            secondary: '#7B7B7BFF',
            tertiary: '#999220FF',
            accent: '#0A84FFFF',
        },
        dark: {
            background: '#3D3D3DFF',
            text: '#FFFFFFFF',
            primary: '#FFB300FF',
            secondary: '#BABABAFF',
            tertiary: '#FF3F34FF',
            accent: '#0AA3FFFF',
        },
    };
})(Applets || (Applets = {}));
