import { Orders } from './crm/orders.js';
import { Fulfillments } from './crm/fulfillments.js';
export declare namespace Sales {
    type Order = Orders.Order;
    type Fulfillment = Fulfillments.Fulfillment;
    type GetUnpaidResponse = Sales.Order[];
    type MarkPaidResponse = Sales.Order & {
        personId?: string;
        memberId?: string;
        membershipId?: string;
        storeId?: string;
        _tenant?: string;
    };
}
