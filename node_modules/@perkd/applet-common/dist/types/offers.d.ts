import { OfferMasters } from '@perkd/crm-types/dist/offermasters.js';
import { Offers as CrmOffers } from '@perkd/crm-types/dist/offers.js';
export declare namespace Offers {
    type QualifiedParams = {
        membershipId: string;
        params?: {
            kind?: string[];
            tags?: string[];
        };
    };
    type QualifiedResponse = OfferMasters.OfferMaster[];
    type IssueParams = {
        masterId: string;
        membershipId: string;
        personalize?: {
            startTime?: Date;
            endTime?: Date;
            discountValue?: number;
        };
    };
    type IssueResponse = CrmOffers.Offer[];
}
