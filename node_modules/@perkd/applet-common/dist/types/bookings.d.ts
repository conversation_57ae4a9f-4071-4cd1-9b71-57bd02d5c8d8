import { Products as CrmProducts } from "@perkd/crm-types";
export declare namespace Bookings {
    type BodyParams = {
        personId: string;
        productIds: string[];
        from: Date;
        duration: number;
    };
    type Item = {
        kind: string;
        title: string;
        images: string[];
        unitPriceMeasure?: CrmProducts.UnitMeasure;
        unitPrice: number;
        units: number;
        quantity: number;
        price: number;
        amount: number;
        capacity: number;
        admit: number;
        sku: string;
        tags: Record<string, string[]>;
        productId: string;
        variantId: string;
        custom?: Record<string, any>;
    };
    type OpeningHour = {
        open: {
            day: number;
            time: string;
        };
        close: {
            day: number;
            time: string;
        };
    };
    type Venue = {
        id: string;
        name: string;
        title: string;
        description: string;
        capacity: number;
        interval: number;
        leadTime: number;
        unitPriceMeasure?: CrmProducts.UnitMeasure;
        prices?: any[];
        shared: boolean;
        images: string[];
        tags: string[];
        productIds: string[];
        placeId: string;
    };
    type DailySchedule = {
        date: string;
        open: {
            start: string;
            end: string;
        }[];
        capacity?: number;
    };
    type Availability = {
        schedule: DailySchedule[];
    };
}
