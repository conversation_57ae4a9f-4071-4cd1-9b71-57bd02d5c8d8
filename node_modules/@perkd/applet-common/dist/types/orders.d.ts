import { Persons } from './persons.js';
import { Orders as CrmOrders } from './crm/orders.js';
import { Fulfillments } from '@perkd/crm-types';
import { Fulfillments as CrmFulfillments } from '@perkd/crm-types/dist/fulfillments.js';
export declare namespace Orders {
    type Order = {
        orderId: string;
        receipt: {
            number: string;
        };
        currency: string;
        amount: number;
        quantity: number;
        status: string;
        personId: string;
        membershipId: string;
        createdAt: string;
    };
    type OrderWithProfile = Order & {
        profile: Persons.Person;
    };
    type OrderWithFulfillments = CrmOrders.Order & {
        id: string;
    };
    type CreateParams = {
        order: {
            receipt: string;
            quantity: number;
            currency: string;
            amount: number;
            paidAt: string;
        };
        membershipId?: string;
        profile?: Persons.Profile;
    };
    type QualifyParams = {
        order: {
            receipt: string;
            quantity: number;
            currency: string;
            amount: number;
            paidAt: string;
        };
    };
    type QualifyResponse = {
        name: string;
        programId: string;
        tierLevel: number;
    };
    type RelocateParams = {
        from: {
            position: {
                key: string;
                value: string | number;
            }[];
        };
        to: {
            position: {
                key: string;
                value: string | number;
            }[];
        };
    };
    type Item = CrmOrders.Item;
    type Recipient = CrmFulfillments.Recipient;
    type OrderWithFulfilled = {
        orderId: string;
        receipt: {
            queue: string;
        };
        quantity: number;
        fulfilled: {
            type: Fulfillments.Type;
            recipient?: Orders.Recipient;
            priority?: CrmFulfillments.Priority;
            minTime?: Date;
            maxTime?: Date;
            itemList: Orders.Item[];
            note?: string;
            receipt?: {
                queue?: string;
                authorisation?: string;
            };
            orderId: string;
            id: string;
        };
    };
}
