import { Memberships as CrmMemberships, Programs as CrmPrograms } from '@perkd/crm-types/dist/memberships.js';
import { Persons as CrmPersons } from '@perkd/crm-types/dist/persons.js';
import { Persons } from './persons.js';
export declare namespace Memberships {
    export type Membership = {
        membershipId: string;
        programId: string;
        tierLevel: number;
        startTime: string;
        endTime: string;
        cardNumber: string;
        state: CrmMemberships.State;
        qualifier: string;
        createdAt: string;
    };
    export type MembershipWithProfile = Membership & {
        profile: Persons.Person;
    };
    export type Program = {
        programId: string;
        name: string;
        type: CrmPrograms.Type;
        tiers: {
            level: number;
            name: string;
            card: {
                canIssue: boolean;
                image: {
                    original: string;
                    thumbnail: string;
                };
            };
            join: string;
        }[];
    };
    interface QualifyYesResponse {
        qualify: 'yes';
        personId?: string;
    }
    interface QualifyNoResponse {
        qualify: 'no';
        reason: string;
        person: CrmPersons.Person & {
            id: string;
            membership: Membership;
        };
    }
    interface QualifyConfirmPersonResponse {
        qualify: 'confirm_person';
        persons: Array<CrmPersons.Person & {
            id: string;
            membership: Membership;
        }>;
    }
    export type QualifyParams = {
        programId: string;
        tierLevel: number;
        profile: Persons.Profile;
    };
    export type QualifyResponse = QualifyYesResponse | QualifyNoResponse | QualifyConfirmPersonResponse;
    export type JoinParams = {
        programId: string;
        tierLevel: number;
        type: CrmPrograms.Type;
        profile: Persons.Profile;
    };
    export type JoinResponse = {
        personId: string;
        membershipId: string;
        programId: string;
        tierLevel: number;
        startTime: string;
        endTime: string;
        cardNumber: string;
    };
    export type ExportResponse = (Persons.Profile & {
        cardNumber: string;
        startTime: string;
        endTime: string;
    })[];
    export type RecruitFilter = {
        from?: string;
        to?: string;
        programId?: string;
        type?: string;
        format?: string;
        staffId?: string;
    };
    export type DailyRecruit = {
        date: string;
        type: string;
        format: string;
        location: {
            id?: string;
            type?: string;
            name?: string;
        };
        staffId: string;
        recruited: number;
        registered: number;
        lastRecruiteAt: string;
    };
    export {};
}
