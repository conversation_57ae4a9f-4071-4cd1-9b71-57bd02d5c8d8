export declare namespace Forms {
    enum InputType {
        TEXT = "text",
        TEL = "tel",
        EMAIL = "email",
        URL = "url",
        DATE = "date",
        TIME = "time",
        DATETIME = "datetime",
        MONEY = "money"
    }
    type InputConfig = {
        inputType: InputType;
        autocapitalize?: 'word' | 'sentences' | 'characters' | 'none';
        min?: string;
        max?: string;
        errorMessages?: {
            [key: string]: string;
        };
        inputOptions?: {
            defaultCountry?: string;
            defaultDate?: string;
            timeStamp?: 'start' | 'end' | '';
            smartFormt?: boolean;
        };
    };
    type Form = {
        name: string;
        schema: {
            type: string;
            required: string[];
            properties: {
                [key: string]: any;
            };
        };
    };
}
