import { Products as CrmProducts } from '@perkd/crm-types/dist/products.js';
export declare namespace Products {
    export enum Availability {
        ACTIVE = "active",
        SOLDOUT = "soldout",
        HIDE = "hide",
        PREORDER = "preorder"
    }
    type Variation = {
        title: string;
        value: any;
    };
    export type Product = CrmProducts.Product & CrmProducts.Variant & {
        variantId: string;
        subtitle: string;
        productId: string;
        globalize?: any;
        isLowQuantity?: boolean;
        isSoldOut?: boolean;
        isBackOrder?: boolean;
        links?: any[];
        behaviors?: any;
        variationList?: Variation[];
        bundleList?: CrmProducts.Bundle[];
        imageUrls?: string[];
        availability?: Availability;
    };
    export {};
}
