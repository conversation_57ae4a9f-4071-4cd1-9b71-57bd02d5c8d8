import { Places as CrmPlaces } from '@perkd/crm-types/dist/places.js';
import { Contacts } from '@perkd/crm-types/dist/contacts.js';
import { Geo } from '@perkd/crm-types/dist/geo.js';
import { Businesses } from './businesses.js';
export declare namespace Places {
    type Type = CrmPlaces.Type;
    const Type: typeof CrmPlaces.Type;
    type PositionKey = CrmPlaces.PositionKey;
    const PositionKey: typeof CrmPlaces.PositionKey;
    type Position = CrmPlaces.Position;
    type Spot = CrmPlaces.Spot;
    type Place = {
        id?: string;
        type: string;
        name: string;
        brand: Businesses.Brand;
        geo: Geo.Geometry;
        geoFence?: {
            radius: number;
        };
        locale?: Contacts.Locale;
        openingHours?: Businesses.Hours;
        urls?: any[];
        startTime?: Date;
        endTime?: Date;
        dinein?: {
            available: boolean;
            hours: Businesses.Hours;
        };
        pickup?: {
            available: boolean;
            hours: Businesses.Hours;
            timeslot: number;
            minFulfillTime?: number;
            maxFulfillTime?: number;
            instructions?: string;
        };
        deliver?: {
            available: boolean;
            hours: Businesses.Hours;
            timeslot: number;
            minFulfillTime?: number;
            maxFulfillTime?: number;
            instructions?: string;
            distance?: number;
            price?: number;
        };
        unlisted?: boolean;
        external?: any;
        visible: boolean;
        phoneList?: Contacts.Phone[];
        addressList?: Contacts.Address[];
    };
    type hoursParams = {
        openingHours: Businesses.Hours;
        dinein?: Businesses.Hours;
        pickup?: Businesses.Hours;
        deliver?: Businesses.Hours;
    };
    type hoursResponse = Place;
}
