import { Contacts } from '@perkd/crm-types/dist/contacts.js';
import { Businesses as CrmBusinesses } from '@perkd/crm-types/dist/businesses.js';
import { Staffs } from './staffs.js';
import { Applets } from './applets.js';
export declare namespace Businesses {
    type YearMonthDay = {
        year: number;
        month: number;
        day: number;
    };
    type Period = CrmBusinesses.Period;
    type TimePeriod = CrmBusinesses.TimePeriod;
    type Hours = {
        specific?: {
            date: YearMonthDay;
            periods: TimePeriod[];
        }[];
        periods: Period[];
    };
    type Brand = CrmBusinesses.Brand;
    type Merchant = {
        business: {
            name: string;
            brand: Brand;
            style: {
                light: Applets.Theme;
                dark: Applets.Theme;
            };
            locale: any;
            tags: any;
            notes: any[];
            isMain: boolean;
            callbacks: any[];
            place: any;
            socials: any[];
            wifi: any;
            facebook: any;
            google: any;
            payments: any;
            commerce: any;
            storedValue: any;
            offer: any;
            reward: any;
            message: any;
            credentials: any;
            external: any;
            visible: boolean;
            globalize: any;
            createdAt: string;
            modifiedAt: string;
            deletedAt: string | null;
            id: string;
            dateList: ({
                name: string;
                date: Date;
            } & YearMonthDay)[];
            phoneList: Contacts.Phone[];
            emailList: Contacts.Email[];
            addressList: (Contacts.Address & {
                formatted?: string;
            })[];
            identityList: {
                id?: string;
                identity: string;
                type?: string;
                provider: string;
                externalId: string;
                credentials?: any;
                country?: string;
                data?: any;
                authScheme?: string;
                visible?: boolean;
                valid?: {
                    start: Date;
                    end: Date;
                };
            }[];
            settingList: any[];
            activated: boolean;
            _tenant: string;
        };
        staffs: Staffs.Staff[];
        printers: {
            id: string;
            name: string;
            online: boolean;
        }[];
    };
}
