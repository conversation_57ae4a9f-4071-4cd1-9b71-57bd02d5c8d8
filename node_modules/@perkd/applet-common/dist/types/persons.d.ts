import { Persons as CrmPersons } from '@perkd/crm-types/dist/persons.js';
import { Contacts } from '@perkd/crm-types/dist/contacts.js';
import { Memberships } from './memberships.js';
export declare namespace Persons {
    type Phone = Contacts.Phone;
    type Address = Contacts.Address;
    type Email = Contacts.Email;
    type Gender = CrmPersons.Gender;
    type NameOrder = CrmPersons.NameOrder;
    const DEFAULT_NAME_ORDER = CrmPersons.NameOrder.GIVEN_FAMILY;
    type Person = {
        familyName: string;
        givenName: string;
        alias?: string;
        name?: {
            order: NameOrder;
            language?: string;
            salutation?: string;
            display?: string;
        };
        fullName?: string;
        gender: Gender;
        phones?: Contacts.Phone[];
        emails?: Contacts.Email[];
        phoneList?: Contacts.Phone[];
        emailList?: Contacts.Email[];
        profileImageUrl?: string;
        birthDate?: {
            id: string;
            name: string;
            date: string;
            year: number;
            month: number;
            day: number;
        };
    };
    type PersonWithMembership = Person & {
        id: string;
        membership: Memberships.Membership;
    };
    type Profile = {
        familyName?: string;
        givenName?: string;
        alias?: string;
        nameOrder?: CrmPersons.NameOrder;
        fullName?: string;
        gender?: Gender;
        birthDate?: string;
        countryCode?: string;
        formattedMobile?: string;
        mobile?: string;
        mobileOpt?: boolean;
        email?: string;
        emailOpt?: boolean;
        profileImageUrl?: string;
    };
    type Search = {
        q?: string;
        cardNumber?: string;
        programIds?: string[];
        tierLevel?: string;
        options?: object;
    };
}
