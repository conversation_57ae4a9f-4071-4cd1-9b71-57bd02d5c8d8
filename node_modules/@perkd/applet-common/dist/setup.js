import { Applets } from "./types/applets.js";
import { mergeObject } from "./utils.js";
import { hexToRgbo, getContrastColor } from "./utilsColor.js";
import commonTranslate from './i18n.json';
export const setupEnvironment = (constants) => {
    const { DEVICE, CARD, COUNTRY, CARDMASTER, CONTENT, PERSON, FONTCSS, FONTPATH, LANGUAGE, CONTEXT } = constants || {};
    return {
        DEVICE,
        CARD,
        CARDMASTER,
        COUNTRY,
        LANGUAGE,
        CONTENT,
        FONTCSS,
        FONTPATH,
        CONTEXT,
        PERSON,
        deviceScheme: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? Applets.ColorScheme.DARK : Applets.ColorScheme.LIGHT,
    };
};
export const setupWidget = (data, appletName) => {
    const { CARD, CARDMASTER } = data || {};
    const WIDGET_INSTANCE = CARD?.widgets?.find((w) => w.key === appletName) || {};
    const WIDGET_MASTER = CARDMASTER?.widgets?.find((w) => w.key === appletName) || {};
    return {
        API_BASE: WIDGET_INSTANCE?.param?.api?.baseUrl || WIDGET_MASTER?.param?.api?.baseUrl,
        API: WIDGET_INSTANCE?.param?.api || WIDGET_MASTER?.param?.api,
        COLOR_SCHEME: WIDGET_INSTANCE?.param?.colorScheme || WIDGET_MASTER?.param?.colorScheme,
        SETTINGS: WIDGET_INSTANCE?.param?.settings || WIDGET_MASTER?.param?.settings,
        MASTER_SETTINGS: WIDGET_MASTER?.param?.settings,
        MEMBERSHIP_PROGRAMS: WIDGET_INSTANCE?.param?.settings?.programs || WIDGET_MASTER?.param?.settings?.programs
    };
};
export const setupFont = (data, embeddedFont) => {
    if (!embeddedFont)
        return;
    const styleElement = document.createElement('style');
    const headElement = document.head || document.getElementsByTagName('head')[0];
    styleElement.textContent = `
        @font-face {
            font-family: Melbourne;
            font-weight: normal;
            src: local('Melbourne'), url('${data.FONTPATH}Melbourne.otf') format('opentype');
        }

        @font-face {
            font-family: Melbourne;
            font-weight: bold;
            src: local('Melbourne_bold'), url('${data.FONTPATH}Melbourne_bold.otf') format('opentype');
        }

        @font-face {
            font-family: picon;
            font-weight: normal;
            src: local('picon'), url('${data.FONTPATH}picon.ttf') format('truetype');
        }
        
        ${data.FONTCSS}
    `;
    headElement.appendChild(styleElement);
};
export const setupTheme = (data, appletName) => {
    // setup css variables 
    const { DEVICE, CARDMASTER, deviceScheme = Applets.ColorScheme.LIGHT } = data;
    const { WIDTH, HEIGHT, MIN_TOP_SPACE, STATUS_BAR_HEIGHT, NAV_BAR_HEIGHT, MIN_BOTTOM_SPACE, BOTTOM_TABS_HEIGHT, windowHeight, IOS, IS_LONG_SCREEN, APP } = DEVICE || {};
    const { VERSION } = APP || {};
    const CSS_ROOT = {
        '--width-screen': WIDTH + 'px',
        '--height-screen': HEIGHT + 'px',
        '--height-window': windowHeight + 'px',
        '--height-minTop': (MIN_TOP_SPACE || 20) + 'px',
        '--height-minBottom': (MIN_BOTTOM_SPACE || 20) + 'px',
        '--height-statusBar': (IOS ? STATUS_BAR_HEIGHT : 0) + 'px',
        '--height-navigationBar': (NAV_BAR_HEIGHT + (IOS ? STATUS_BAR_HEIGHT : 0)) + 'px',
        '--height-tabBar': BOTTOM_TABS_HEIGHT + 'px',
        '--device-ios': IOS,
        '--device-longScreen': IS_LONG_SCREEN,
        '--app-version': VERSION,
        '--font-size-base': Math.round(((WIDTH / 320 - 1) * 0.8 + 1) * 10) + 'px',
        '--size-base': `${Math.round(((WIDTH / 320 - 1) * 0.8 + 1) * 10)}`
    };
    Object.keys(CSS_ROOT).forEach((key) => {
        document.documentElement.style.setProperty(key, CSS_ROOT ? CSS_ROOT[key] : '');
    });
    // setup theme
    const WIDGET_MASTER = appletName ? (CARDMASTER?.widgets?.find((w) => w.key === appletName) || {}) : {};
    const { colorScheme } = WIDGET_MASTER?.param || {};
    const { brand, theme = colorScheme || deviceScheme } = CARDMASTER || {};
    const { light, dark } = WIDGET_MASTER?.param?.theme || brand?.style || {};
    const COLOR_SCHEME = {
        light: Object.assign({}, Applets.DefaultTheme.light, light),
        dark: Object.assign({}, Applets.DefaultTheme.dark, dark),
    };
    const colorKeys = Object.keys(COLOR_SCHEME[theme]);
    colorKeys.forEach((key) => {
        const color = COLOR_SCHEME[theme][key];
        if (color) {
            const { r, g, b, o } = hexToRgbo(color);
            const contrast = getContrastColor(color);
            document.documentElement.style.setProperty(`--color-brand-${key}`, `rgb(${r},${g},${b},${o})`);
            document.documentElement.style.setProperty(`--color-brand-${key}-contrast`, contrast);
        }
    });
    document.documentElement.setAttribute('theme', theme);
};
export const setupLanguage = async (data, locales, dayjs) => {
    const { ENGLISH } = Applets.Language;
    const { LANGUAGE = ENGLISH } = data;
    document.documentElement.lang = LANGUAGE;
    if (dayjs && locales && dayjs.locale) {
        const localLanguage = locales[LANGUAGE] || locales[ENGLISH];
        dayjs.locale(localLanguage);
    }
};
export const setupI18n = (translations) => mergeObject(commonTranslate, translations);
