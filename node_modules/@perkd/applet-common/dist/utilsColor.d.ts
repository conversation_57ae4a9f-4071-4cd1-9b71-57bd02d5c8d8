export declare const rgbToRgbo: (color: string) => {
    r: number;
    g: number;
    b: number;
    o: number;
};
export declare const hexToRgbo: (color: string) => {
    r: number;
    g: number;
    b: number;
    o: number;
};
export declare const colorToRgbo: (color: string) => {
    r: number;
    g: number;
    b: number;
    o: number;
};
export declare function rgbToHsl(r: number, g: number, b: number): {
    h: number;
    s: number;
    l: number;
};
export declare const getContrastColor: (color: string | {
    r: number;
    g: number;
    b: number;
    o: number;
}) => "#FFFFFF" | "#000000";
export declare const blendWithBackground: (frontColor: string | {
    r: number;
    g: number;
    b: number;
    o: number;
}, backColor: string | {
    r: number;
    g: number;
    b: number;
    o: number;
}) => {
    r: number;
    g: number;
    b: number;
    o: number;
};
export declare const applyOpacity: (color: string, opacity: number) => string;
