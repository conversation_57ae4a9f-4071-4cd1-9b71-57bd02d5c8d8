{"familyName": {"inputType": "text", "autocapitalize": "word"}, "givenName": {"inputType": "text", "autocapitalize": "word"}, "nameOrder": {"inputType": "text"}, "mobile": {"inputType": "tel"}, "email": {"inputType": "email"}, "birthDate": {"inputType": "date"}, "join": {"inputType": "date", "errorMessages": {"after_maximum_date": "join.after_maximum_date"}, "inputOptions": {"timeStamp": "start"}}, "expire": {"inputType": "date", "errorMessages": {"before_minimum_date": "expire.before_minimum_date"}, "inputOptions": {"timeStamp": "end"}}, "paidTime": {"inputType": "date"}, "receipt": {"inputType": "text"}, "quantity": {"inputType": "number", "inputmode": "numeric", "pattern": "^[0-9]*$"}, "amount": {"inputType": "money"}, "paidAt": {"inputType": "datetime", "inputOptions": {"smartFormat": true}}}