# Applet Common

Common utilities, types, and interfaces for building web applets in the Perkd app ecosystem.

## Table of Contents
- [Build Process](#build-process)
  - [Build Modes](#build-modes)
  - [Build Steps](#build-steps)
- [Core Integration](#core-integration)
  - [AppBridge](#appbridge)
  - [Setup](#setup)
- [Features](#features)
  - [Actions](#actions)
    - [API Actions](./doc/actions/api.md)
    - [Interaction](./doc/actions/interaction.md)
    - [Order](./doc/actions/order.md)
    - [Others](./doc/actions/others.md)
  - [API Requests](#api-requests)
  - [WebSocket](#websocket)
  - [Topics](#topics)
- [Utilities](#utilities)
  - [Receipt Utils](#receipt-utils)
  - [Common Utils](#utils)
- [Support](#support)
  - [Types](#types)
  - [Input Configuration](#input-configuration)
  - [Internationalization](#internationalization)

## Build Process
Use Vue3 + Vite + Vue-i18n + TypeScript to build the applet.

### Build Modes
Use `VITE_SINGLE_PAGE` to determine the build mode.
- **Embedded**: Single-page applet with direct app integration, `VITE_SINGLE_PAGE`=true
  - Uses app's local resources
  - Supports EJS templates
  - Single HTML file
- **Standalone**: Multi-page applet with API communication, `VITE_SINGLE_PAGE`=false
  - Bundled resources
  - Multiple HTML files
  - API-based data access

### Build Steps
1. Pre-build validation ([`preBuild.ts`](./doc/preBuild.md))
   - Validates imports
   - Checks configurations
2. Vite build
3. Post-build processing ([`postBuild.ts`](./doc/preBuild.md))
   - Ensures browser compatibility
   - Transforms imports


## Core Integration

### AppBridge
Communication bridge between web applets and native app (must be included in index.html or main.ts).
```html
<script src="<%- constants.PATH.SHARED.RSRC %>appbridge-1.0.min.js"></script>
```
```typescript
import "@perkd/applet-common/appbridge"
```
[Learn more](./doc/appBridge.md)

### Setup
Initial applet configuration and lifecycle management. [Learn more](./doc/setup.md)
```typescript
import { getConstants } from '@perkd/applet-common/actions'
import { setupEnvironment, setupWidget, setupFont, setupTheme, setupLanguage, setupI18n } from '@perkd/applet-common/setup'
import { Applets } from '@perkd/applet-common/types/applets'
import { formatDateTime as dayjs, LOCALE_LANGUAGE } from '@perkd/format-datetime'

// Retrieve constants from app
const constants = await getConstants()

// Configure applet
const appletName = 'myApplet'
const ENVIRONMENT = setupEnvironment(constants)
const WIDGET = setupWidget(ENVIRONMENT, appletName)

// Setup theme
setupTheme(ENVIRONMENT, appletName)

// Setup fonts
// access to VITE_SINGLE_PAGE from env
const embedded = import.meta.env.VITE_SINGLE_PAGE === 'true'
setupFont(ENVIRONMENT, embedded)

// Setup language
// if applet need to format dateTime, need to set dayjs locale
setupLanguage(ENVIRONMENT)
setupLanguage(ENVIRONMENT, LOCALE_LANGUAGE, dayjs)

// Setup i18n
import { createI18n } from 'vue-i18n'
import translations from '@/i18n.json'

const i18n = createI18n({
    locale: getTransLang(ENVIRONMENT.LANGUAGE), // set locale
    fallbackLocale: Applets.LANGUAGES.DEFAULT, // set fallback locale
    messages: setupI18n(translations), // setup translations
    legacy: false
})
```

### Actions
Pre-defined actions for interacting with the native app:
- [API Actions](./doc/actions/api.md): Backend service calls
- [Interaction](./doc/actions/interaction.md): User interactions (call, share, etc.)
- [Order](./doc/actions/order.md): Order management
- [Others](./doc/actions/others.md): Utility actions

```typescript
callTo('1234567890')
// equivalent to window.$perkd.do('communicate.call', { number: '1234567890' })
```

## Features

### API Requests
Type-safe API request definitions and handlers.
```typescript
const response = await apiRequest('endpoint.name', params)
```
[View API Documentation](./doc/api-request.md)

### WebSocket
Real-time communication with single socket.
```typescript
const socket = await startSocket('orders', params)
```
[Learn more](./doc/websockets.md)

### Topics
Manages multiple WebSocket connections.
```typescript
const topics = new Topics('myApplet', {
  orders: { key: 'sales.order.created' }
}, config)
await topics.start()
```
[Learn more](./doc/topics.md)

## Utilities

### Receipt Utils
Order data formatting for receipts.
```typescript
const receipt = {
  items: toItems(order.itemList, order.currency),
  bill: toBill(order, fulfillment)
}
```
[Learn more](./doc/utilsReceipt.md)

### Utils
Common utility functions.
```typescript
// Format currency
formatAmount(1000, 'HKD') // '$1,000.00'

// Handle errors
if (isErrorResponse(result)) {
  console.error(result.error)
}
```
[Learn more](./doc/utils.md)

## Support

### [Types](./src/types)
TypeScript definitions for:
- App bridge interfaces
- WebSocket communication
- Order and receipt data
- API requests and responses
- Common data structures

### [Input Configuration](./src/inputConfig.json)
Default input configurations for form fields:
```typescript
// Example usage in form
const config = {
  familyName: {
    inputType: 'text',
    autocapitalize: 'word'
  },
  amount: {
    inputType: 'money'
  },
  birthDate: {
    inputType: 'date'
  }
}
```

Supported input types:
- `text`: Text input with optional autocapitalize
- `tel`: Phone number input
- `email`: Email input with validation
- `date`: Date picker with optional timeStamp (start/end)
- `datetime`: DateTime picker with smart formatting
- `number`: Numeric input with pattern validation
- `money`: Currency amount input

Common configurations:
- `inputType`: Type of input field
- `autocapitalize`: Text capitalization mode
- `inputmode`: Input mode hint for virtual keyboard
- `pattern`: Validation pattern
- `errorMessages`: Custom error messages
- `inputOptions`: Additional options like timeStamp or smartFormat

### [Internationalization](./src/i18n.json)
Built-in translations for:
- Error messages
- System notifications
- Common UI text

