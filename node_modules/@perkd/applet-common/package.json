{"name": "@perkd/applet-common", "version": "0.3.16", "description": "Shared types & interface definitions, untils & common functions", "author": "<EMAIL>", "license": "ISC", "type": "module", "engines": {"node": ">=18"}, "files": ["dist"], "main": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./types/*": {"types": "./dist/types/*.d.ts", "default": "./dist/types/*.js"}, "./fonts/*": {"default": "./dist/fonts/*"}, "./styles/*": {"default": "./dist/styles/*"}, "./images/*": {"default": "./dist/images/*"}, "./*.json": "./dist/*.json", "./*": {"types": "./dist/*.d.ts", "default": "./dist/*.js"}}, "scripts": {"build": "npx tsc && node scripts/copy-files.js", "update": "ncu -u && rm -rf node_modules/ yarn.lock && yarn install", "test": "mocha -r ts-node/register tests/**/*.test.ts", "test-one": "mocha -r ts-node/register", "release": "rm -rf dist/ tsconfig.tsbuildinfo && tsc && rm -rf node_modules/ yarn.lock && yarn workspaces focus --production"}, "repository": {"type": "git", "url": "git+ssh://**************/perkd/applet-common.git"}, "bugs": {"url": "https://github.com/perkd/applet-common/issues"}, "homepage": "https://github.com/perkd/applet-common#readme", "dependencies": {"@perkd/crm-types": "github:perkd/crm-types#semver:^1.9.51", "@perkd/format-datetime": "github:perkd/format-datetime#semver:^1.3.0"}, "devDependencies": {"@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.2", "@types/mocha": "^10.0.10", "@types/node": "^22.13.10", "@types/ws": "^8", "ts-node": "^10.9.2", "typescript": "~5.8.2", "ws": "^8.18.1"}, "packageManager": "yarn@4.4.1", "resolutions": {"@perkd/crm-types": "github:perkd/crm-types#main"}}