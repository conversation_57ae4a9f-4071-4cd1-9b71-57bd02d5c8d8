import formatDateTime from 'dayjs';
import updateLocale from 'dayjs/plugin/updateLocale';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import isoWeek from 'dayjs/plugin/isoWeek';
import weekday from 'dayjs/plugin/weekday';
import relativeTime from 'dayjs/plugin/relativeTime';
import isBetween from 'dayjs/plugin/isBetween';
import duration from 'dayjs/plugin/duration';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import objectSupport from 'dayjs/plugin/objectSupport';
import customFormat from './plugin/customFormat';
import calendar from './plugin/calendar';
import humane from './plugin/humane';
export declare const LOCALE_LANGUAGE: Record<string, string>;
export { updateLocale, localizedFormat, isoWeek, weekday, relativeTime, isBetween, duration, utc, timezone, quarterOfYear, customParseFormat, objectSupport };
export { customFormat, calendar, humane, formatDateTime };
