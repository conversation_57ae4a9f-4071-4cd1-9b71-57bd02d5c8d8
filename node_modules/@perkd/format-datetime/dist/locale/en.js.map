{"version": 3, "file": "en.js", "sourceRoot": "", "sources": ["../../src/locale/en.ts"], "names": [], "mappings": ";;AAAA,eAAe;AACf,MAAM,MAAM,GAAG;IACX,IAAI,EAAE,IAAI;IACV,QAAQ,EAAE,0DAA0D,CAAC,KAAK,CAAC,GAAG,CAAC;IAC/E,MAAM,EAAE,uFAAuF,CAAC,KAAK,CAAC,GAAG,CAAC;IAC1G,SAAS,EAAE,CAAC;IACZ,aAAa,EAAE,6BAA6B,CAAC,KAAK,CAAC,GAAG,CAAC;IACvD,WAAW,EAAE,iDAAiD,CAAC,KAAK,CAAC,GAAG,CAAC;IACzE,WAAW,EAAE,sBAAsB,CAAC,KAAK,CAAC,GAAG,CAAC;IAC9C,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;QACX,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAClC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;IACxD,CAAC;IACD,OAAO,EAAE;QACL,EAAE,EAAE,OAAO;QACX,GAAG,EAAE,IAAI;QACT,CAAC,EAAE,YAAY;QACf,EAAE,EAAE,iBAAiB;QACrB,GAAG,EAAE,kBAAkB;QACvB,IAAI,EAAE,uBAAuB;QAC7B,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,YAAY;QAChB,GAAG,EAAE,aAAa;QAClB,IAAI,EAAE,kBAAkB;KAC3B;IACD,QAAQ,EAAE;QACN,OAAO,EAAE,aAAa;QACtB,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,YAAY;QACrB,QAAQ,EAAE,aAAa;QACvB,QAAQ,EAAE,MAAM;QAChB,QAAQ,EAAE,aAAa;QACvB,QAAQ,EAAE,GAAG;QACb,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,SAAS;KACxB;IACD,YAAY,EAAE;QACV,MAAM,EAAE,OAAO;QACf,IAAI,EAAE,QAAQ;QACd,CAAC,EAAE,eAAe;QAClB,CAAC,EAAE,UAAU;QACb,EAAE,EAAE,YAAY;QAChB,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,UAAU;QACd,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,SAAS;QACb,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,WAAW;QACf,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,UAAU;KACjB;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,cAAc,EAAE,CAAC;QACjB,YAAY,EAAE,QAAQ;QACtB,SAAS,EAAE;YACP,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,QAAQ;SACjB;QACD,IAAI,EAAE,WAAW;QACjB,OAAO,EAAE,UAAU;QACnB,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,SAAS;QACb,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,SAAS;QACb,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,UAAU;QACd,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,SAAS;QACb,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,WAAW;QACf,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,UAAU;KACjB;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,gBAAgB,EAAE,KAAK;QACvB,QAAQ,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACnG,SAAS,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACpG,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACjG,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACtG,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE;KAClG;CACJ,CAAA;AAED,kBAAe,MAAM,CAAA"}