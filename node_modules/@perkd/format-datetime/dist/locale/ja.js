"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Japanese [ja]
const index_1 = require("./../index");
const locale = {
    name: 'ja',
    weekdays: '日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日'.split('_'),
    weekdaysShort: '日_月_火_水_木_金_土'.split('_'),
    weekdaysMin: '日_月_火_水_木_金_土'.split('_'),
    months: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),
    monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),
    ordinal: n => `${n}日`,
    formats: {
        LTS: 'Ah时',
        LT: 'Ah时mm分',
        L: 'MMMD日LTX',
        LL: 'YYYY年MMMD日LTX',
        LLL: 'MMMD日ddddLTX',
        LLLL: 'YYYY年MMMD日ddddLTX',
        l: 'MMMD日',
        ll: 'YYYY年MMMD日',
        lll: 'MMMD日dddd',
        llll: 'YYYY年MMMD日dddd',
    },
    calendar: {
        lastDay: '[昨日]',
        sameDay: '[今日]',
        nextDay: '[明日]',
        lastWeek: '[先週の]dddd',
        sameWeek: '[今度の]dddd',
        nextWeek: '[次の]dddd',
        sameYear: 'l',
        sameElse: 'll',
        timeFormat: '%cLTX'
    },
    humane: {
        daysToRelative: 0,
        daysToCalendar: 1,
        skipFromUnit: 'second',
        startFrom: {
            value: 30,
            unit: 'second'
        },
        soon: '数%uで',
        justnow: 'ちょうど今',
        s: '1秒',
        ss: '%d秒',
        m: '1分',
        mm: '%d分',
        h: '1時間',
        hh: '%d時間',
        d: '1日間',
        dd: '%d日間',
        M: '1ヶ月',
        MM: '%dヶ月',
        y: '1年',
        yy: '%d年',
    },
    period: {
        daysToCalendar: 1,
        showSameDayToday: false,
        sameYear: { startDate: 'lyx', endDate: 'l', startTime: 'LYX', endTime: 'L', format: '%ds-%de' },
        sameMonth: { startDate: 'lyx', endDate: 'D日', startTime: 'LYX', endTime: 'D日LTX', format: '%ds-%de' },
        sameDay: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTX', format: '%ds-%de' },
        sameMeridiem: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTZ', format: '%ds-%de' },
        others: { startDate: 'll', endDate: 'll', startTime: 'LL', endTime: 'LL', format: '%ds-%de' }
    },
    meridiem: hour => (hour < 12 ? '午前' : '午後'),
    relativeTime: {
        future: '%s後',
        past: '%s前',
        s: '数秒',
        m: '1分',
        mm: '%d分',
        h: '1時間',
        hh: '%d時間',
        d: '1日',
        dd: '%d日',
        M: '1ヶ月',
        MM: '%dヶ月',
        y: '1年',
        yy: '%d年'
    }
};
index_1.formatDateTime.locale(locale, null, true);
exports.default = locale;
//# sourceMappingURL=ja.js.map