"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Malay [ms]
const index_1 = require("./../index");
const locale = {
    name: 'ms',
    weekdays: 'Ahad_<PERSON>in_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_Sabtu'.split('_'),
    weekdaysShort: 'Ahd_Isn_Sel_Rab_Kha_Jum_Sab'.split('_'),
    weekdaysMin: 'Ah_Is_Sl_Rb_Km_Jm_Sb'.split('_'),
    months: 'Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember'.split('_'),
    monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis'.split('_'),
    weekStart: 1,
    formats: {
        LTS: 'ha',
        LT: 'h.mma',
        L: 'D MMM, LTX',
        LL: 'D MMM YYYY, LTX',
        LLL: 'dddd, D MMM, LTX',
        LLLL: 'dddd, D MMM YYYY, LTX',
        l: 'D MMM',
        ll: 'D MMM YYYY',
        lll: 'dddd, D MMM',
        llll: 'dddd, D MMM YYYY',
    },
    calendar: {
        lastDay: '[Kelmarin]',
        sameDay: '[Hari ini]',
        nextDay: '[Esok]',
        lastWeek: 'dddd [lepas]',
        sameWeek: 'dddd [ini]',
        nextWeek: 'dddd [depan]',
        sameYear: 'l',
        sameElse: 'll',
        timeFormat: '%c, LTX'
    },
    humane: {
        daysToRelative: 0,
        daysToCalendar: 1,
        skipFromUnit: 'second',
        startFrom: {
            value: 30,
            unit: 'second'
        },
        soon: 'dalam beberapa %u',
        justnow: 'tadi',
        s: '1 saat',
        ss: '%d saat',
        m: '1 minit',
        mm: '%d minit',
        h: '1 jam',
        hh: '%d jam',
        d: '1 hari',
        dd: '%d hari',
        M: '1 bulan',
        MM: '%d bulan',
        y: '1 tahun',
        yy: '%d tahun',
    },
    period: {
        daysToCalendar: 1,
        showSameDayToday: false,
        sameYear: { startDate: 'l', endDate: 'lyx', startTime: 'LYX', endTime: 'LYX', format: '%ds - %de' },
        sameMonth: { startDate: 'D', endDate: 'lyx', startTime: 'LYX', endTime: 'LYX', format: '%ds - %de' },
        sameDay: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTX', format: '%ds - %de' },
        sameMeridiem: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTX', format: '%ds - %de' },
        others: { startDate: 'll', endDate: 'll', startTime: 'LL', endTime: 'LL', format: '%ds - %de' }
    },
    relativeTime: {
        future: 'dalam %s',
        past: '%s yang lepas',
        s: 'beberapa saat',
        m: 'seminit',
        mm: '%d minit',
        h: 'sejam',
        hh: '%d jam',
        d: 'sehari',
        dd: '%d hari',
        M: 'sebulan',
        MM: '%d bulan',
        y: 'setahun',
        yy: '%d tahun'
    },
    ordinal: n => `${n}.`
};
index_1.formatDateTime.locale(locale, null, true);
exports.default = locale;
//# sourceMappingURL=ms.js.map