{"version": 3, "file": "ko.js", "sourceRoot": "", "sources": ["../../src/locale/ko.ts"], "names": [], "mappings": ";;AAAA,cAAc;AACd,sCAA2C;AAE3C,MAAM,MAAM,GAAG;IACX,IAAI,EAAE,IAAI;IACV,QAAQ,EAAE,6BAA6B,CAAC,KAAK,CAAC,GAAG,CAAC;IAClD,aAAa,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC;IACzC,WAAW,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC;IACvC,MAAM,EAAE,wCAAwC,CAAC,KAAK,CAAC,GAAG,CAAC;IAC3D,WAAW,EAAE,wCAAwC,CAAC,KAAK,CAAC,GAAG,CAAC;IAChE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,OAAO,EAAE;QACL,GAAG,EAAE,MAAM;QACX,EAAE,EAAE,UAAU;QACd,CAAC,EAAE,aAAa;QAChB,EAAE,EAAE,mBAAmB;QACvB,GAAG,EAAE,kBAAkB;QACvB,IAAI,EAAE,wBAAwB;QAC9B,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,cAAc;QAClB,GAAG,EAAE,aAAa;QAClB,IAAI,EAAE,mBAAmB;KAC5B;IACD,QAAQ,EAAE;QACN,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,WAAW;QACrB,QAAQ,EAAE,UAAU;QACpB,QAAQ,EAAE,WAAW;QACrB,QAAQ,EAAE,GAAG;QACb,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,QAAQ;KACvB;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,cAAc,EAAE,CAAC;QACjB,YAAY,EAAE,QAAQ;QACtB,SAAS,EAAE;YACP,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,QAAQ;SACjB;QACD,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,IAAI;QACb,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;KACZ;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,gBAAgB,EAAE,KAAK;QACvB,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE;QACjG,SAAS,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE;QACzG,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACjG,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACtG,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE;KAClG;IACD,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3C,YAAY,EAAE;QACV,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,MAAM;QACZ,CAAC,EAAE,KAAK;QACR,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,KAAK;KACZ;CACJ,CAAA;AACD,sBAAc,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAEzC,kBAAe,MAAM,CAAA"}