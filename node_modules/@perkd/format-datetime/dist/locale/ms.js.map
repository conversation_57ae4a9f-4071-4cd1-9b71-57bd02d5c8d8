{"version": 3, "file": "ms.js", "sourceRoot": "", "sources": ["../../src/locale/ms.ts"], "names": [], "mappings": ";;AAAA,aAAa;AACb,sCAA2C;AAE3C,MAAM,MAAM,GAAG;IACX,IAAI,EAAE,IAAI;IACV,QAAQ,EAAE,4CAA4C,CAAC,KAAK,CAAC,GAAG,CAAC;IACjE,aAAa,EAAE,6BAA6B,CAAC,KAAK,CAAC,GAAG,CAAC;IACvD,WAAW,EAAE,sBAAsB,CAAC,KAAK,CAAC,GAAG,CAAC;IAC9C,MAAM,EAAE,mFAAmF,CAAC,KAAK,CAAC,GAAG,CAAC;IACtG,WAAW,EAAE,iDAAiD,CAAC,KAAK,CAAC,GAAG,CAAC;IACzE,SAAS,EAAE,CAAC;IACZ,OAAO,EAAE;QACL,GAAG,EAAE,IAAI;QACT,EAAE,EAAE,OAAO;QACX,CAAC,EAAE,YAAY;QACf,EAAE,EAAE,iBAAiB;QACrB,GAAG,EAAE,kBAAkB;QACvB,IAAI,EAAE,uBAAuB;QAC7B,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,YAAY;QAChB,GAAG,EAAE,aAAa;QAClB,IAAI,EAAE,kBAAkB;KAC3B;IACD,QAAQ,EAAE;QACN,OAAO,EAAE,YAAY;QACrB,OAAO,EAAE,YAAY;QACrB,OAAO,EAAE,QAAQ;QACjB,QAAQ,EAAE,cAAc;QACxB,QAAQ,EAAE,YAAY;QACtB,QAAQ,EAAE,cAAc;QACxB,QAAQ,EAAE,GAAG;QACb,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,SAAS;KACxB;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,cAAc,EAAE,CAAC;QACjB,YAAY,EAAE,QAAQ;QACtB,SAAS,EAAE;YACP,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,QAAQ;SACjB;QACD,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,MAAM;QACf,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,SAAS;QACb,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,UAAU;QACd,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,QAAQ;QACZ,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,SAAS;QACb,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,UAAU;QACd,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,UAAU;KACjB;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,gBAAgB,EAAE,KAAK;QACvB,QAAQ,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACnG,SAAS,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACpG,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACjG,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;QACtG,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE;KAClG;IACD,YAAY,EAAE;QACV,MAAM,EAAE,UAAU;QAClB,IAAI,EAAE,eAAe;QACrB,CAAC,EAAE,eAAe;QAClB,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,UAAU;QACd,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,QAAQ;QACZ,CAAC,EAAE,QAAQ;QACX,EAAE,EAAE,SAAS;QACb,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,UAAU;QACd,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,UAAU;KACjB;IACD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG;CACxB,CAAA;AACD,sBAAc,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAEzC,kBAAe,MAAM,CAAA"}