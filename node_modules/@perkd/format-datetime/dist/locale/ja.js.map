{"version": 3, "file": "ja.js", "sourceRoot": "", "sources": ["../../src/locale/ja.ts"], "names": [], "mappings": ";;AAAA,gBAAgB;AAChB,sCAA2C;AAE3C,MAAM,MAAM,GAAG;IACX,IAAI,EAAE,IAAI;IACV,QAAQ,EAAE,6BAA6B,CAAC,KAAK,CAAC,GAAG,CAAC;IAClD,aAAa,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC;IACzC,WAAW,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC;IACvC,MAAM,EAAE,wCAAwC,CAAC,KAAK,CAAC,GAAG,CAAC;IAC3D,WAAW,EAAE,wCAAwC,CAAC,KAAK,CAAC,GAAG,CAAC;IAChE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG;IACrB,OAAO,EAAE;QACL,GAAG,EAAE,KAAK;QACV,EAAE,EAAE,QAAQ;QACZ,CAAC,EAAE,UAAU;QACb,EAAE,EAAE,eAAe;QACnB,GAAG,EAAE,cAAc;QACnB,IAAI,EAAE,mBAAmB;QACzB,CAAC,EAAE,OAAO;QACV,EAAE,EAAE,YAAY;QAChB,GAAG,EAAE,WAAW;QAChB,IAAI,EAAE,gBAAgB;KACzB;IACD,QAAQ,EAAE;QACN,OAAO,EAAE,MAAM;QACf,OAAO,EAAE,MAAM;QACf,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE,WAAW;QACrB,QAAQ,EAAE,WAAW;QACrB,QAAQ,EAAE,UAAU;QACpB,QAAQ,EAAE,GAAG;QACb,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,OAAO;KACtB;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,cAAc,EAAE,CAAC;QACjB,YAAY,EAAE,QAAQ;QACtB,SAAS,EAAE;YACP,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,QAAQ;SACjB;QACD,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,OAAO;QAChB,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;KACZ;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,gBAAgB,EAAE,KAAK;QACvB,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE;QAC/F,SAAS,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE;QACrG,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;QAC/F,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;QACpG,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE;KAChG;IACD,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3C,YAAY,EAAE;QACV,MAAM,EAAE,KAAK;QACb,IAAI,EAAE,KAAK;QACX,CAAC,EAAE,IAAI;QACP,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;QACT,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,IAAI;QACP,EAAE,EAAE,KAAK;KACZ;CACJ,CAAA;AACD,sBAAc,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAEzC,kBAAe,MAAM,CAAA"}