"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Indonesian [id]
const index_1 = require("./../index");
const locale = {
    name: 'id',
    weekdays: '<PERSON><PERSON>_<PERSON>in_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Jumat_Sabtu'.split('_'),
    months: '<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>t_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember'.split('_'),
    weekdaysShort: '<PERSON>_Sen_Sel_Rab_Kam_Jum_Sab'.split('_'),
    monthsShort: 'Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des'.split('_'),
    weekdaysMin: 'Mg_Sn_Sl_Rb_Km_Jm_Sb'.split('_'),
    weekStart: 1,
    formats: {
        LTS: 'ha',
        LT: 'h.mma',
        L: 'D MMM, LTX',
        LL: 'D MMM YYYY, LTX',
        LLL: 'dddd, D MMM, LTX',
        LLLL: 'dddd, D MMM YYYY, LTX',
        l: 'D MMM',
        ll: 'D MMM YYYY',
        lll: 'dddd, D MMM',
        llll: 'dddd, D MMM YYYY',
    },
    calendar: {
        lastDay: '[Kemarin]',
        sameDay: '[Hari ini]',
        nextDay: '[Besok]',
        lastWeek: 'dddd [lalu]',
        sameWeek: 'dddd [ini]',
        nextWeek: 'dddd [depan]',
        sameYear: 'l',
        sameElse: 'll',
        timeFormat: '%c, LTX'
    },
    humane: {
        daysToRelative: 0,
        daysToCalendar: 1,
        skipFromUnit: 'second',
        startFrom: {
            value: 30,
            unit: 'second'
        },
        soon: 'dalam beberapa %u',
        justnow: 'baru saja',
        s: '1 detik',
        ss: '%d detik',
        m: '1 menit',
        mm: '%d menit',
        h: '1 jam',
        hh: '%d jam',
        d: '1 hari',
        dd: '%d hari',
        M: '1 bulan',
        MM: '%d bulan',
        y: '1 tahun',
        yy: '%d tahun',
    },
    period: {
        daysToCalendar: 1,
        showSameDayToday: false,
        sameYear: { startDate: 'l', endDate: 'lyx', startTime: 'LYX', endTime: 'LYX', format: '%ds - %de' },
        sameMonth: { startDate: 'D', endDate: 'lyx', startTime: 'LYX', endTime: 'LYX', format: '%ds - %de' },
        sameDay: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTX', format: '%ds - %de' },
        sameMeridiem: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTX', format: '%ds - %de' },
        others: { startDate: 'll', endDate: 'll', startTime: 'LL', endTime: 'LL', format: '%ds - %de' }
    },
    relativeTime: {
        future: 'dalam %s',
        past: '%s yang lalu',
        s: 'beberapa detik',
        m: 'semenit',
        mm: '%d menit',
        h: 'sejam',
        hh: '%d jam',
        d: 'sehari',
        dd: '%d hari',
        M: 'sebulan',
        MM: '%d bulan',
        y: 'setahun',
        yy: '%d tahun'
    },
    ordinal: n => `${n}.`
};
index_1.formatDateTime.locale(locale, null, true);
exports.default = locale;
//# sourceMappingURL=id.js.map