{"version": 3, "file": "zh-cn.js", "sourceRoot": "", "sources": ["../../src/locale/zh-cn.ts"], "names": [], "mappings": ";;AAAA,0BAA0B;AAC1B,sCAA2C;AAE3C,MAAM,MAAM,GAAG;IACX,IAAI,EAAE,OAAO;IACb,QAAQ,EAAE,6BAA6B,CAAC,KAAK,CAAC,GAAG,CAAC;IAClD,aAAa,EAAE,sBAAsB,CAAC,KAAK,CAAC,GAAG,CAAC;IAChD,WAAW,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC;IACvC,MAAM,EAAE,uCAAuC,CAAC,KAAK,CAAC,GAAG,CAAC;IAC1D,WAAW,EAAE,wCAAwC,CAAC,KAAK,CAAC,GAAG,CAAC;IAChE,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;QACxB,QAAQ,MAAM,EAAE,CAAC;YACb,KAAK,GAAG;gBACJ,OAAO,GAAG,MAAM,GAAG,CAAA;YACvB;gBACI,OAAO,GAAG,MAAM,GAAG,CAAA;QAC3B,CAAC;IACL,CAAC;IACD,SAAS,EAAE,CAAC;IACZ,SAAS,EAAE,CAAC;IACZ,OAAO,EAAE;QACL,GAAG,EAAE,KAAK;QACV,EAAE,EAAE,QAAQ;QACZ,CAAC,EAAE,SAAS;QACZ,EAAE,EAAE,cAAc;QAClB,GAAG,EAAE,aAAa;QAClB,IAAI,EAAE,kBAAkB;QACxB,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,WAAW;QACf,GAAG,EAAE,UAAU;QACf,IAAI,EAAE,eAAe;KACxB;IACD,QAAQ,EAAE;QACN,OAAO,EAAE,MAAM;QACf,OAAO,EAAE,MAAM;QACf,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,MAAM;QAChB,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,GAAG;QACb,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,OAAO;KACtB;IACD,YAAY,EAAE;QACV,MAAM,EAAE,KAAK;QACb,IAAI,EAAE,KAAK;QACX,CAAC,EAAE,IAAI;QACP,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,OAAO;QACX,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,OAAO;QACX,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,OAAO;QACX,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;KACb;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,cAAc,EAAE,CAAC;QACjB,YAAY,EAAE,QAAQ;QACtB,SAAS,EAAE;YACP,KAAK,EAAE,EAAE;YACT,IAAI,EAAE,QAAQ;SACjB;QACD,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,IAAI;QACb,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,OAAO;QACX,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,OAAO;QACX,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;QACV,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,OAAO;QACX,CAAC,EAAE,KAAK;QACR,EAAE,EAAE,MAAM;KACb;IACD,MAAM,EAAE;QACJ,cAAc,EAAE,CAAC;QACjB,gBAAgB,EAAE,KAAK;QACvB,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE;QAC/F,SAAS,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE;QACrG,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;QAC/F,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;QACpG,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE;KAChG;IACD,QAAQ,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;QACvB,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM,CAAA;QAChC,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;YACX,OAAO,IAAI,CAAA;QACf,CAAC;aAAM,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;YAClB,OAAO,IAAI,CAAA;QACf,CAAC;aAAM,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;YACnB,OAAO,IAAI,CAAA;QACf,CAAC;aAAM,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;YACnB,OAAO,IAAI,CAAA;QACf,CAAC;aAAM,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;YACnB,OAAO,IAAI,CAAA;QACf,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;CACJ,CAAA;AACD,sBAAc,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAEzC,kBAAe,MAAM,CAAA"}