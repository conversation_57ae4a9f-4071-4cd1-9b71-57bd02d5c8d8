"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Chinese (Taiwan) [zh-tw]
const index_1 = require("./../index");
const locale = {
    name: 'zh-tw',
    weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),
    weekdaysShort: '週日_週一_週二_週三_週四_週五_週六'.split('_'),
    weekdaysMin: '日_一_二_三_四_五_六'.split('_'),
    months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),
    monthsShort: '1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月'.split('_'),
    ordinal: (number, period) => {
        switch (period) {
            case 'W':
                return `${number}週`;
            default:
                return `${number}日`;
        }
    },
    formats: {
        LTS: 'Ah點',
        LT: 'Ah點mm分',
        L: 'M月D日LTX',
        LL: 'YYYY年M月D日LTX',
        LLL: 'M月D日ddddLTX',
        LLLL: 'YYYY年M月D日ddddLTX',
        l: 'M月D日',
        ll: 'YYYY年M月D日',
        lll: 'M月D日dddd',
        llll: 'YYYY年M月D日dddd',
    },
    calendar: {
        lastDay: '[昨天]',
        sameDay: '[今天]',
        nextDay: '[明天]',
        lastWeek: '[上]dddd',
        sameWeek: 'dddd',
        nextWeek: '[下]dddd',
        sameYear: 'l',
        sameElse: 'll',
        timeFormat: '%cLTX'
    },
    relativeTime: {
        future: '%s後',
        past: '%s前',
        s: '幾秒',
        m: '1 分鐘',
        mm: '%d 分鐘',
        h: '1 小時',
        hh: '%d 小時',
        d: '1 天',
        dd: '%d 天',
        M: '1 個月',
        MM: '%d 個月',
        y: '1 年',
        yy: '%d 年'
    },
    humane: {
        daysToRelative: 0,
        daysToCalendar: 1,
        skipFromUnit: 'second',
        startFrom: {
            value: 30,
            unit: 'second'
        },
        soon: '幾%u後',
        justnow: "剛剛",
        s: '1 秒',
        ss: '%d 秒',
        m: '1 分鐘',
        mm: '%d 分鐘',
        h: '1 小時',
        hh: '%d 小時',
        d: '1 天',
        dd: '%d 天',
        M: '1 個月',
        MM: '%d 個月',
        y: '1 年',
        yy: '%d 年',
    },
    period: {
        daysToCalendar: 1,
        showSameDayToday: false,
        sameYear: { startDate: 'lyx', endDate: 'l', startTime: 'LYX', endTime: 'L', format: '%ds-%de' },
        sameMonth: { startDate: 'lyx', endDate: 'D日', startTime: 'LYX', endTime: 'D日LTX', format: '%ds-%de' },
        sameDay: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTX', format: '%ds-%de' },
        sameMeridiem: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTZ', format: '%ds-%de' },
        others: { startDate: 'll', endDate: 'll', startTime: 'LL', endTime: 'LL', format: '%ds-%de' }
    },
    meridiem: (hour, minute) => {
        const hm = (hour * 100) + minute;
        if (hm < 600) {
            return '凌晨';
        }
        else if (hm < 900) {
            return '早上';
        }
        else if (hm < 1100) {
            return '上午';
        }
        else if (hm < 1300) {
            return '中午';
        }
        else if (hm < 1800) {
            return '下午';
        }
        return '晚上';
    }
};
index_1.formatDateTime.locale(locale, null, true);
exports.default = locale;
//# sourceMappingURL=zh-tw.js.map