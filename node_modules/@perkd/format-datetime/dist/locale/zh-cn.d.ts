declare const locale: {
    name: string;
    weekdays: string[];
    weekdaysShort: string[];
    weekdaysMin: string[];
    months: string[];
    monthsShort: string[];
    ordinal: (number: any, period: any) => string;
    weekStart: number;
    yearStart: number;
    formats: {
        LTS: string;
        LT: string;
        L: string;
        LL: string;
        LLL: string;
        LLLL: string;
        l: string;
        ll: string;
        lll: string;
        llll: string;
    };
    calendar: {
        lastDay: string;
        sameDay: string;
        nextDay: string;
        lastWeek: string;
        sameWeek: string;
        nextWeek: string;
        sameYear: string;
        sameElse: string;
        timeFormat: string;
    };
    relativeTime: {
        future: string;
        past: string;
        s: string;
        m: string;
        mm: string;
        h: string;
        hh: string;
        d: string;
        dd: string;
        M: string;
        MM: string;
        y: string;
        yy: string;
    };
    humane: {
        daysToRelative: number;
        daysToCalendar: number;
        skipFromUnit: string;
        startFrom: {
            value: number;
            unit: string;
        };
        soon: string;
        justnow: string;
        s: string;
        ss: string;
        m: string;
        mm: string;
        h: string;
        hh: string;
        d: string;
        dd: string;
        M: string;
        MM: string;
        y: string;
        yy: string;
    };
    period: {
        daysToCalendar: number;
        showSameDayToday: boolean;
        sameYear: {
            startDate: string;
            endDate: string;
            startTime: string;
            endTime: string;
            format: string;
        };
        sameMonth: {
            startDate: string;
            endDate: string;
            startTime: string;
            endTime: string;
            format: string;
        };
        sameDay: {
            startDate: string;
            endDate: string;
            startTime: string;
            endTime: string;
            format: string;
        };
        sameMeridiem: {
            startDate: string;
            endDate: string;
            startTime: string;
            endTime: string;
            format: string;
        };
        others: {
            startDate: string;
            endDate: string;
            startTime: string;
            endTime: string;
            format: string;
        };
    };
    meridiem: (hour: any, minute: any) => "凌晨" | "早上" | "上午" | "中午" | "下午" | "晚上";
};
export default locale;
