"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Korean [ko]
const index_1 = require("./../index");
const locale = {
    name: 'ko',
    weekdays: '일요일_월요일_화요일_수요일_목요일_금요일_토요일'.split('_'),
    weekdaysShort: '일_월_화_수_목_금_토'.split('_'),
    weekdaysMin: '일_월_화_수_목_금_토'.split('_'),
    months: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),
    monthsShort: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),
    ordinal: n => n,
    formats: {
        LTS: 'A h시',
        LT: 'A h시 mm분',
        L: 'MMM D일, LTX',
        LL: 'YYYY년 MMM D일, LTX',
        LLL: 'MMM D일 dddd, LTX',
        LLLL: 'YYYY년 MMM D일 dddd, LTX',
        l: 'MMM D일',
        ll: 'YYYY년 MMM D일',
        lll: 'MMM D일 dddd',
        llll: 'YYYY년 MMM D일 dddd',
    },
    calendar: {
        lastDay: '어제',
        sameDay: '오늘',
        nextDay: '내일',
        lastWeek: '[지난주]dddd',
        sameWeek: '[이번]dddd',
        nextWeek: '[다음주]dddd',
        sameYear: 'l',
        sameElse: 'll',
        timeFormat: '%c LTX'
    },
    humane: {
        daysToRelative: 0,
        daysToCalendar: 1,
        skipFromUnit: 'second',
        startFrom: {
            value: 30,
            unit: 'second'
        },
        soon: '몇 %u 후',
        justnow: '방금',
        s: '1초',
        ss: '%d초',
        m: '1분',
        mm: '%d분',
        h: '1시간',
        hh: '%d시간',
        d: '하루',
        dd: '%d일',
        M: '1달',
        MM: '%d달',
        y: '1년',
        yy: '%d년',
    },
    period: {
        daysToCalendar: 1,
        showSameDayToday: false,
        sameYear: { startDate: 'lyx', endDate: 'l', startTime: 'LYX', endTime: 'L', format: '%ds - %de' },
        sameMonth: { startDate: 'lyx', endDate: 'D일', startTime: 'LYX', endTime: 'D일, LTX', format: '%ds - %de' },
        sameDay: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTX', format: '%ds - %de' },
        sameMeridiem: { startDate: 'lyx', endDate: '', startTime: 'LYX', endTime: 'LTZ', format: '%ds - %de' },
        others: { startDate: 'll', endDate: 'll', startTime: 'LL', endTime: 'LL', format: '%ds - %de' }
    },
    meridiem: hour => (hour < 12 ? '오전' : '오후'),
    relativeTime: {
        future: '%s 후',
        past: '%s 전',
        s: '몇 초',
        m: '1분',
        mm: '%d분',
        h: '한 시간',
        hh: '%d시간',
        d: '하루',
        dd: '%d일',
        M: '한 달',
        MM: '%d달',
        y: '일 년',
        yy: '%d년'
    }
};
index_1.formatDateTime.locale(locale, null, true);
exports.default = locale;
//# sourceMappingURL=ko.js.map