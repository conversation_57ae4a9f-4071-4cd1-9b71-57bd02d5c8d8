"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDateTime = exports.humane = exports.calendar = exports.customFormat = exports.objectSupport = exports.customParseFormat = exports.quarterOfYear = exports.timezone = exports.utc = exports.duration = exports.isBetween = exports.relativeTime = exports.weekday = exports.isoWeek = exports.localizedFormat = exports.updateLocale = exports.LOCALE_LANGUAGE = void 0;
const dayjs_1 = __importDefault(require("dayjs"));
exports.formatDateTime = dayjs_1.default;
const updateLocale_1 = __importDefault(require("dayjs/plugin/updateLocale"));
exports.updateLocale = updateLocale_1.default;
const localizedFormat_1 = __importDefault(require("dayjs/plugin/localizedFormat"));
exports.localizedFormat = localizedFormat_1.default;
const isoWeek_1 = __importDefault(require("dayjs/plugin/isoWeek"));
exports.isoWeek = isoWeek_1.default;
const weekday_1 = __importDefault(require("dayjs/plugin/weekday"));
exports.weekday = weekday_1.default;
const relativeTime_1 = __importDefault(require("dayjs/plugin/relativeTime"));
exports.relativeTime = relativeTime_1.default;
const isBetween_1 = __importDefault(require("dayjs/plugin/isBetween"));
exports.isBetween = isBetween_1.default;
const duration_1 = __importDefault(require("dayjs/plugin/duration"));
exports.duration = duration_1.default;
const utc_1 = __importDefault(require("dayjs/plugin/utc"));
exports.utc = utc_1.default;
const timezone_1 = __importDefault(require("dayjs/plugin/timezone"));
exports.timezone = timezone_1.default;
const quarterOfYear_1 = __importDefault(require("dayjs/plugin/quarterOfYear"));
exports.quarterOfYear = quarterOfYear_1.default;
const customParseFormat_1 = __importDefault(require("dayjs/plugin/customParseFormat"));
exports.customParseFormat = customParseFormat_1.default;
const objectSupport_1 = __importDefault(require("dayjs/plugin/objectSupport"));
exports.objectSupport = objectSupport_1.default;
const customFormat_1 = __importDefault(require("./plugin/customFormat"));
exports.customFormat = customFormat_1.default;
const calendar_1 = __importDefault(require("./plugin/calendar"));
exports.calendar = calendar_1.default;
const humane_1 = __importDefault(require("./plugin/humane"));
exports.humane = humane_1.default;
const en_1 = __importDefault(require("./locale/en"));
dayjs_1.default.extend(weekday_1.default);
dayjs_1.default.extend(updateLocale_1.default);
dayjs_1.default.extend(localizedFormat_1.default);
dayjs_1.default.extend(isoWeek_1.default);
dayjs_1.default.extend(relativeTime_1.default, {
    thresholds: [
        { l: 's', r: 1 },
        { l: 'm', r: 1 },
        { l: 'mm', r: 59, d: 'minute' },
        { l: 'h', r: 1 },
        { l: 'hh', r: 23, d: 'hour' },
        { l: 'd', r: 1 },
        { l: 'dd', r: 29, d: 'day' },
        { l: 'M', r: 1 },
        { l: 'MM', r: 11, d: 'month' },
        { l: 'y', r: 1 },
        { l: 'yy', d: 'year' }
    ]
});
dayjs_1.default.extend(isBetween_1.default);
dayjs_1.default.extend(duration_1.default);
dayjs_1.default.extend(utc_1.default);
dayjs_1.default.extend(timezone_1.default);
dayjs_1.default.extend(quarterOfYear_1.default);
dayjs_1.default.extend(customParseFormat_1.default);
dayjs_1.default.extend(objectSupport_1.default);
dayjs_1.default.extend(customFormat_1.default);
dayjs_1.default.extend(calendar_1.default);
dayjs_1.default.extend(humane_1.default);
dayjs_1.default.Ls['en'] = en_1.default;
exports.LOCALE_LANGUAGE = {
    'en': 'en',
    'ms': 'ms',
    'id': 'id',
    'ja': 'ja',
    'ko': 'ko',
    'zh-Hans': 'zh-cn',
    'zh-Hant': 'zh-tw',
    'zh-Hant-TW': 'zh-tw',
    'zh-Hant-HK': 'zh-hk',
};
//# sourceMappingURL=index.js.map