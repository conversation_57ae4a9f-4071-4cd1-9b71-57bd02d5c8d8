import dayjs from 'dayjs';
declare module 'dayjs' {
    interface ILocale {
        relativeTime?: {
            future: string;
            past: string;
            s: string;
            m: string;
            mm: string;
            h: string;
            hh: string;
            d: string;
            dd: string;
            M: string;
            MM: string;
            y: string;
            yy: string;
        };
        humane?: {
            daysToRelative: number;
            daysToCalendar: number;
            skipFromUnit: string;
            startFrom: {
                value: number;
                unit: string;
            };
            soon: string;
            justnow: string;
            s: string;
            ss: string;
            m: string;
            mm: string;
            h: string;
            hh: string;
            d: string;
            dd: string;
            M: string;
            MM: string;
            y: string;
            yy: string;
        };
        period?: {
            daysToCalendar: number;
            sameYear: {
                startDate: string;
                endDate: string;
                startTime: string;
                endTime: string;
                format: string;
            };
            sameMonth: {
                startDate: string;
                endDate: string;
                startTime: string;
                endTime: string;
                format: string;
            };
            sameDay: {
                startDate: string;
                endDate: string;
                startTime: string;
                endTime: string;
                format: string;
            };
            sameMeridiem: {
                startDate: string;
                endDate: string;
                startTime: string;
                endTime: string;
                format: string;
            };
            others: {
                startDate: string;
                endDate: string;
                startTime: string;
                endTime: string;
                format: string;
            };
        };
    }
    interface Dayjs {
        humane(now?: dayjs.ConfigType, showTime?: boolean, formats?: Object): string;
        smartDateTime(now?: dayjs.ConfigType, showTime?: boolean, formats?: Object): string;
    }
    function durationToString(duration?: DurationObject, formats?: Object): Object;
    function getDuration(date1: dayjs.ConfigType, date2: dayjs.ConfigType): DurationObject;
    function getHumanePeriod(start: dayjs.ConfigType, end: dayjs.ConfigType, now?: dayjs.ConfigType, showTime?: boolean, formats?: Object): string;
}
type DurationObject = {
    year?: number;
    month?: number;
    day?: number;
    hour?: number;
    minute?: number;
    second?: number;
    firstDateWasLater?: boolean;
};
declare const _default: (o: any, c: typeof dayjs.Dayjs, d: typeof dayjs) => void;
export default _default;
