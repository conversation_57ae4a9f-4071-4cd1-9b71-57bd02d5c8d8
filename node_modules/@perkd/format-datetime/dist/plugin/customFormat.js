"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dayjs_1 = __importDefault(require("dayjs"));
exports.default = (o, c, d) => {
    const proto = c.prototype;
    const oldFormat = proto.format;
    proto.format = function (formatStr) {
        if (!this.isValid()) {
            return oldFormat.bind(this)(formatStr);
        }
        const str = formatStr || 'YYYY-MM-DDTHH:mm:ssZ';
        const { formats = {} } = this.$locale();
        const result = str.replace(/\[([^\]]+)]|LT[S|X|Z]?|lyx|LYX|L{1,4}$/g, (match) => {
            const minute = this.minute();
            const year = this.year();
            const isSameYear = year === (0, dayjs_1.default)().year();
            const smartTime = minute === 0 ? 'LTS' : 'LT';
            switch (match) {
                case 'LTZ':
                    return formats[smartTime].replace('A', '').replace('a', '');
                case 'LTX':
                    return smartTime;
                case 'L':
                case 'LL':
                case 'LLL':
                case 'LLLL':
                    return formats[formatStr].replace('LTX', smartTime);
                case 'lyx':
                    return formats[isSameYear ? 'l' : 'll'];
                case 'LYX':
                    return formats[isSameYear ? 'L' : 'LL'].replace('LTX', smartTime);
                default:
                    return match;
            }
        });
        return oldFormat.bind(this)(result);
    };
};
//# sourceMappingURL=customFormat.js.map