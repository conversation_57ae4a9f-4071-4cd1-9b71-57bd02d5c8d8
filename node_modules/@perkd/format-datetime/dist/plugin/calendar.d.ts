import dayjs from 'dayjs';
declare module 'dayjs' {
    interface ILocale {
        calendar?: {
            lastDay: string;
            sameDay: string;
            nextDay: string;
            nextWeek: string;
            sameWeek: string;
            lastWeek: string;
            sameYear: string;
            sameElse: string;
            timeFormat: string;
        };
    }
    interface Dayjs {
        calendar(referenceTime?: dayjs.ConfigType, showTime?: boolean, formats?: any): string;
    }
    function calendarFormat(date: dayjs.ConfigType, referenceTime?: dayjs.ConfigType): string;
}
declare const _default: (o: any, c: typeof dayjs.Dayjs, d: typeof dayjs) => void;
export default _default;
