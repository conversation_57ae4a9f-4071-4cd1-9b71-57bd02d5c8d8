"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = (o, c, d) => {
    const calendarFormat = {
        lastDay: '[yesterday]',
        sameDay: '[today]',
        nextDay: '[tomorrow]',
        lastWeek: '[last] dddd',
        sameWeek: 'on dddd',
        nextWeek: '[next] dddd',
        sameYear: 'l',
        sameElse: 'll',
        timeFormat: '%c, LTX'
    };
    c.prototype.calendar = function (referenceTime = undefined, showTime = false, formats) {
        const format = Object.assign({}, this.$locale().calendar || calendarFormat, formats);
        const reference = d(referenceTime || undefined).startOf('d');
        const diff = this.startOf('d').diff(reference, 'd');
        const sameWeek = this.isoWeek() === reference.isoWeek();
        const lastWeek = this.isoWeek() === reference.isoWeek() - 1;
        const nextWeek = this.isoWeek() === reference.isoWeek() + 1;
        const sameYear = this.year() === reference.year();
        const sameElse = 'sameElse';
        /* eslint-disable no-nested-ternary */
        const retVal = diff === 0 ? 'sameDay' :
            diff === -1 ? 'lastDay' :
                diff === 1 ? 'nextDay' :
                    sameWeek ? 'sameWeek' :
                        lastWeek ? 'lastWeek' :
                            nextWeek ? 'nextWeek' :
                                sameYear ? 'sameYear' : sameElse;
        /* eslint-enable no-nested-ternary */
        const currentFormat = format[retVal] || calendarFormat[retVal];
        const dateString = typeof currentFormat === 'function' ? currentFormat.call(this, d()) : this.format(currentFormat);
        return showTime ? this.format(format.timeFormat).replace('%c', dateString) : dateString;
    };
    d.calendarFormat = function (date, referenceTime) {
        const reference = d(referenceTime || undefined).startOf('d');
        const current = d(date || undefined).startOf('d');
        const diff = current.diff(reference, 'days', true);
        const sameWeek = current.isoWeek() === reference.isoWeek();
        const lastWeek = current.isoWeek() === reference.isoWeek() - 1;
        const nextWeek = current.isoWeek() === reference.isoWeek() + 1;
        const sameYear = current.year() === reference.year();
        const sameElse = 'sameElse';
        return diff === 0 ? 'sameDay' :
            diff === -1 ? 'lastDay' :
                diff === 1 ? 'nextDay' :
                    sameWeek ? 'sameWeek' :
                        lastWeek ? 'lastWeek' :
                            nextWeek ? 'nextWeek' :
                                sameYear ? 'sameYear' : sameElse;
    };
};
//# sourceMappingURL=calendar.js.map