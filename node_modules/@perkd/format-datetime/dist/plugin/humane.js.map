{"version": 3, "file": "humane.js", "sourceRoot": "", "sources": ["../../src/plugin/humane.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAsEA,MAAM,gBAAgB,GAAG;IACvB,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,iBAAiB,EAAE,KAAK;CACzB,CAAA;AACD,MAAM,OAAO,GAAG;IACd,MAAM,EAAE,EAAE;IACV,IAAI,EAAE,GAAG;IACT,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,GAAG;IACV,MAAM,EAAE,IAAI;IACZ,GAAG,EAAE,GAAG;IACR,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,GAAG;IACT,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,IAAI;IACb,MAAM,EAAE,GAAG;IACX,OAAO,EAAE,IAAI;IACb,SAAS,EAAE,GAAG;CACf,CAAA;AAED,kBAAe,CAAC,CAAM,EAAE,CAAqB,EAAE,CAAe,EAAE,EAAE;IAChE,SAAS,SAAS,CAAC,GAAW,EAAE,IAAY,EAAE,QAAgB;QAC5D,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnD,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED,kHAAkH;IAClH,kEAAkE;IAClE,+EAA+E;IAC/E,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,GAAG,GAAG,SAAS,EAAE,WAAoB,IAAI,EAAE,OAAgB;QACxF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACrF,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA;QAC3D,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QAClB,MAAM,cAAc,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAC/C,MAAM,EAAE,iBAAiB,KAAkB,cAAc,EAA3B,QAAQ,UAAK,cAAc,EAAnD,qBAAkC,CAAiB,CAAA;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;QACpF,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,KAAK,CAAA;QACzF,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;QAC1F,MAAM,IAAI,GAAG,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA;QACjF,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;QACvE,MAAM,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;QAChF,MAAM,YAAY,GAAG,QAAQ,IAAI,cAAc,CAAC;QAEhD,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QACpE,OAAO,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;IACnF,CAAC,CAAA;IAED,CAAC,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,MAAwB,SAAS,EAAE,WAAoB,IAAI,EAAE,OAAgB;QACjH,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACrF,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,CAAA;QACjC,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QACzC,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAA;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;QACpF,MAAM,YAAY,GAAG,cAAc,IAAI,QAAQ,CAAA;QAE/C,OAAO,YAAY;YACjB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC;YAC9B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IACxE,CAAC,CAAA;IAED,CAAC,CAAC,gBAAgB,GAAG,UAAU,cAA8B,EAAE,OAAY;QACzE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACvF,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,CAAA;QAC/B,MAAM,KAAqC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,gBAAgB,EAAE,cAAc,CAAC,EAAxF,EAAE,iBAAiB,OAAqE,EAAhE,QAAQ,cAAhC,qBAAkC,CAAsD,CAAA;QAC9F,MAAM,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,YAAY,CAAA;QAC7E,MAAM,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAA;QACzD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;QAC7F,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,GAAW,EAAE,KAAa,EAAE,EAAE;YACxF,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC3B,sBAAsB;YACtB,sEAAsE;YACtE,8DAA8D;YAC9D,MAAM,UAAU,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,IAAI,KAAK,CAAC,CAAA;YAE1F,IAAI,UAAU;gBAAE,OAAO,GAAG,CAAA;YAC1B,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAA;YACvC,OAAO,GAAG,CAAA;QACZ,CAAC,EAAE,EAAE,CAAC,CAAA;QACN,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QAE7C,OAAO,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACrF,CAAC,CAAA;IAED,CAAC,CAAC,WAAW,GAAG,UAAU,KAAuB,EAAE,KAAuB;QACxE,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;QACjB,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;QACjB,IAAI,iBAA0B,CAAC;QAE/B,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,SAAS,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,6BAA6B;QAEjF,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;YAClB,OAAO;gBACL,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,MAAM,EAAE,CAAC;gBACT,iBAAiB,EAAE,KAAK;aACzB,CAAA;QACH,CAAC;QACD,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,MAAM,GAAG,GAAG,EAAE,CAAA;YACd,EAAE,GAAG,EAAE,CAAA;YACP,EAAE,GAAG,GAAG,CAAA;YACR,iBAAiB,GAAG,IAAI,CAAA;QAC1B,CAAC;aAAM,CAAC;YACN,iBAAiB,GAAG,KAAK,CAAA;QAC3B,CAAC;QAED,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAA;QACjC,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,CAAA;QACnC,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAA;QACjC,IAAI,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAA;QACpC,IAAI,OAAO,GAAG,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAA;QACvC,IAAI,OAAO,GAAG,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAA;QAEvC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,OAAO,GAAG,EAAE,GAAG,OAAO,CAAA;YACtB,OAAO,EAAE,CAAA;QACX,CAAC;QACD,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,OAAO,GAAG,EAAE,GAAG,OAAO,CAAA;YACtB,QAAQ,EAAE,CAAA;QACZ,CAAC;QACD,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACjB,QAAQ,GAAG,EAAE,GAAG,QAAQ,CAAA;YACxB,KAAK,EAAE,CAAA;QACT,CAAC;QACD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,MAAM,mBAAmB,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAA;YACzI,IAAI,mBAAmB,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,gBAAgB;gBACrD,KAAK,GAAG,mBAAmB,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,mBAAmB,CAAC,CAAA;YACzE,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,mBAAmB,GAAG,KAAK,CAAA;YACrC,CAAC;YACD,KAAK,EAAE,CAAA;QACT,CAAC;QACD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,KAAK,GAAG,EAAE,GAAG,KAAK,CAAA;YAClB,KAAK,EAAE,CAAA;QACT,CAAC;QACD,OAAO;YACL,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,KAAK;YACZ,GAAG,EAAE,KAAK;YACV,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,OAAO;YACf,iBAAiB;SAClB,CAAA;IACH,CAAC,CAAA;IAED,qDAAqD;IACrD,gDAAgD;IAChD,kEAAkE;IAClE,4FAA4F;IAC5F,8DAA8D;IAC9D,0FAA0F;IAC1F,CAAC,CAAC,eAAe,GAAG,UAAU,KAAuB,EAAE,GAAqB,EAAE,GAAqB,EAAE,QAAQ,GAAG,IAAI,EAAE,OAAa;;QACjI,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QAClB,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;QACtB,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QAElB,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;YAAE,OAAO,EAAE,CAAA;QAC7B,IAAI,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAA;YAChC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;QAC1C,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACvF,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QAEzC,mFAAmF;QACnF,gFAAgF;QAChF,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,cAAc,CAAA;QAC7G,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,cAAc,CAAA;QAEzG,MAAM,YAAY,GAAG,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc;YACpF,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;gBACnB,CAAC,CAAC,CAAC,CAAC,aAAa,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW;oBAC3E,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU;wBACxC,CAAC,CAAC,QAAQ,CAAA;QAClB,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;QAC/E,IAAI,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAA;QAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAA;QAE9C,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QACnD,MAAM,eAAe,GAAG,IAAI,MAAM,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;QACvE,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,QAAiB,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAE7F,IAAI,OAAO,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,MAAM,GAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAS,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAA;YAChG,WAAW,GAAG,CAAA,MAAA,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,0CAAG,CAAC,CAAC,KAAI,WAAW,CAAA;QACjE,CAAC;QAED,MAAM,UAAU,GAAG,CAAC,IAAiB,EAAE,CAAS,EAAE,UAAmB,EAAE,EAAE;YACvE,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;YAC1D,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC9D,IAAI,cAAc,IAAI,CAAC,UAAU;gBAAE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAEpE,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;YACtD,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAA;YAC7F,MAAM,kBAAkB,GAAG,cAAc,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;YAChE,MAAM,gBAAgB,GAAG,UAAU,IAAI,kBAAkB,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,cAAc,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAA;YAEpJ,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QACxD,CAAC,CAAA;QAED,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QACpF,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QAE1E,OAAO,WAAW,IAAI,SAAS;YAC7B,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC;YAC9D,CAAC,CAAC,CAAC,WAAW,IAAI,SAAS,CAAC,CAAA;IAChC,CAAC,CAAA;AACH,CAAC,CAAA"}