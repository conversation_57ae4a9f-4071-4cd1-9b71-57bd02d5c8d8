"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
const DEFAULT_DURATION = {
    year: 0,
    month: 0,
    day: 0,
    hour: 0,
    minute: 0,
    second: 0,
    firstDateWasLater: false
};
const STRINGS = {
    nodiff: '',
    year: 'y',
    years: 'yy',
    month: 'M',
    months: 'MM',
    day: 'd',
    days: 'dd',
    hour: 'h',
    hours: 'hh',
    minute: 'm',
    minutes: 'mm',
    second: 's',
    seconds: 'ss',
    delimiter: ' ',
};
exports.default = (o, c, d) => {
    function pluralize(num, word, timeUnit) {
        const key = STRINGS[word + (num === 1 ? '' : 's')];
        return timeUnit[key].replace('%d', num);
    }
    // special optimize: start string - within startFrom.value startFrom.unit, show just now or in few tartFrom.unit, 
    // within daysToRelative: show relative time: 2 hours 3 mins ago, 
    // within daysToCalendar: show calendar date, yesterday, 2pm, last Tuesday, 3pm
    c.prototype.humane = function (now = undefined, showTime = true, formats) {
        const config = Object.assign({}, this.$locale().humane || d.Ls['en'].humane, formats);
        const { daysToRelative, startFrom, justnow, soon } = config;
        const NOW = d(now);
        const durationObject = d.getDuration(this, NOW);
        const { firstDateWasLater } = durationObject, duration = __rest(durationObject, ["firstDateWasLater"]);
        const diffDays = Math.abs(this.startOf('day').diff(NOW.startOf('day'), 'day', true));
        const showStartString = Math.abs(this.diff(NOW, startFrom.unit, true)) <= startFrom.value;
        const findUnitIndex = Object.keys(duration).findIndex((k) => k === startFrom.unit);
        const unit = findUnitIndex >= 0 ? Object.keys(duration)[findUnitIndex] : 'second';
        const unitString = config[STRINGS[`${unit}s`]].replace('%d', '').trim();
        const startString = firstDateWasLater ? soon.replace('%u', unitString) : justnow;
        const showRelative = diffDays <= daysToRelative;
        if (!showRelative)
            return this.smartDateTime(NOW, showTime, config);
        return showStartString ? startString : d.durationToString(durationObject, config);
    };
    c.prototype.smartDateTime = function (now = undefined, showTime = true, formats) {
        const config = Object.assign({}, this.$locale().humane || d.Ls['en'].humane, formats);
        const { daysToCalendar } = config;
        const NOW = d(now);
        const sameYear = this.isSame(NOW, 'year');
        const formatString = sameYear ? 'l' : 'll';
        const diffDays = Math.abs(this.startOf('day').diff(NOW.startOf('day'), 'day', true));
        const showCalendar = daysToCalendar >= diffDays;
        return showCalendar
            ? this.calendar(NOW, showTime)
            : this.format(showTime ? formatString.toUpperCase() : formatString);
    };
    d.durationToString = function (durationObject, formats) {
        const config = Object.assign({}, d.Ls[d.locale()].humane || d.Ls['en'].humane, formats);
        const { skipFromUnit } = config;
        const _a = Object.assign({}, DEFAULT_DURATION, durationObject), { firstDateWasLater } = _a, duration = __rest(_a, ["firstDateWasLater"]);
        const relativeTime = d.Ls[d.locale()].relativeTime || d.Ls['en'].relativeTime;
        const relativeKey = firstDateWasLater ? 'future' : 'past';
        const skipFrom = Object.keys(duration).findIndex((key) => skipFromUnit.includes(key));
        const result = Object.keys(duration).reduce((res, key, index) => {
            const value = duration[key];
            // skip when value = 0
            // if skipFromUnit = minute, skipFrom = 4, skip minute & seconds value
            // if no value was kept before skipFrom, keep at least 1 value
            const shouldSkip = value === 0 || (res.length > 0 && skipFrom !== -1 && skipFrom <= index);
            if (shouldSkip)
                return res;
            res.push(pluralize(value, key, config));
            return res;
        }, []);
        const joined = result.join(STRINGS.delimiter);
        return joined ? relativeTime[relativeKey].replace('%s', joined) : config['justnow'];
    };
    d.getDuration = function (date1, date2) {
        let m1 = d(date1);
        let m2 = d(date2);
        let firstDateWasLater;
        m1.add(m2.utcOffset() - m1.utcOffset(), 'minutes'); // shift timezone of m1 to m2
        if (m1.isSame(m2)) {
            return {
                year: 0,
                month: 0,
                day: 0,
                hour: 0,
                minute: 0,
                second: 0,
                firstDateWasLater: false,
            };
        }
        if (m1.isAfter(m2)) {
            const tmp = m1;
            m1 = m2;
            m2 = tmp;
            firstDateWasLater = true;
        }
        else {
            firstDateWasLater = false;
        }
        let yDiff = m2.year() - m1.year();
        let mDiff = m2.month() - m1.month();
        let dDiff = m2.date() - m1.date();
        let hourDiff = m2.hour() - m1.hour();
        let minDiff = m2.minute() - m1.minute();
        let secDiff = m2.second() - m1.second();
        if (secDiff < 0) {
            secDiff = 60 + secDiff;
            minDiff--;
        }
        if (minDiff < 0) {
            minDiff = 60 + minDiff;
            hourDiff--;
        }
        if (hourDiff < 0) {
            hourDiff = 24 + hourDiff;
            dDiff--;
        }
        if (dDiff < 0) {
            const daysInLastFullMonth = d(`${m2.year()}-${m2.month() + 1 > 9 ? '' : '0'}${m2.month() + 1}`, 'YYYY-MM').subtract(1, 'M').daysInMonth();
            if (daysInLastFullMonth < m1.date()) { // 31/01 -> 2/03
                dDiff = daysInLastFullMonth + dDiff + (m1.date() - daysInLastFullMonth);
            }
            else {
                dDiff = daysInLastFullMonth + dDiff;
            }
            mDiff--;
        }
        if (mDiff < 0) {
            mDiff = 12 + mDiff;
            yDiff--;
        }
        return {
            year: yDiff,
            month: mDiff,
            day: dDiff,
            hour: hourDiff,
            minute: minDiff,
            second: secDiff,
            firstDateWasLater,
        };
    };
    // same meridiem: 2 Jan, 3pm-5pm | yesterday, 3pm-5pm
    // same day: 2 Jan, 3am-5pm | yesterday, 3am-5pm
    // same year: 2 May, 5pm - 3 May, 8pm | 2 - 3 May | today - 10 Jun
    // others: 2 May 2022, 5pm - 3 May 2023, 8pm | 2 May 2022 - 3 May 2023 | today - 10 Jun 2024
    // ⚠️⚠️ /yx/gi format = dynamic year, hide YYYY when same year
    // ⚠️⚠️ daysToCalendar = 1, show tody/tomorrow/yesterday, if no relative day, set it as -1
    d.getHumanePeriod = function (start, end, now, showTime = true, formats) {
        var _a;
        const NOW = d(now);
        const START = d(start);
        const END = d(end);
        if (!start && !end)
            return '';
        if ((!start && end) || (start && !end)) {
            const DATE = start ? START : END;
            return DATE.smartDateTime(NOW, showTime);
        }
        const CONFIG = Object.assign({}, d.Ls[d.locale()].period || d.Ls['en'].period, formats);
        const SAMEDAY = START.isSame(END, 'day');
        const IS_TODAY = START.isSame(NOW, 'day');
        // if start or end is relative time, we can only show the full date for start / end
        // eg: 2 - 3 Jun, if 3 Jun is today, need to update the display to 2 Jun - today
        const startRelative = Math.abs(START.startOf('day').diff(NOW.startOf('day'), 'day')) <= CONFIG.daysToCalendar;
        const endRelative = Math.abs(END.startOf('day').diff(NOW.startOf('day'), 'day')) <= CONFIG.daysToCalendar;
        const formatString = SAMEDAY && START.format('a') === END.format('a') ? 'sameMeridiem'
            : SAMEDAY ? 'sameDay'
                : !(startRelative || endRelative) && START.isSame(END, 'month') ? 'sameMonth'
                    : (START.isSame(END, 'year')) ? 'sameYear'
                        : 'others';
        const { startDate, endDate, startTime, endTime, format } = CONFIG[formatString];
        let startFormat = showTime ? startTime : startDate;
        const endFormat = showTime ? endTime : endDate;
        const timeOnlyFormats = ['LTZ', 'LTX', 'LTS', 'LT'];
        const timeFormatRegex = new RegExp(`${timeOnlyFormats.join('|')}`, 'g');
        const dynamicYear = (f, sameYear) => f.replace(/yx/gi, sameYear ? '' : f[0]);
        if (SAMEDAY && IS_TODAY && !CONFIG.showSameDayToday) {
            const format = (d.Ls[d.locale()] || d.Ls['en']).formats[dynamicYear(startFormat, true)];
            startFormat = ((_a = format.match(timeFormatRegex)) === null || _a === void 0 ? void 0 : _a[0]) || startFormat;
        }
        const getDisplay = (date, f, isRelative) => {
            const adjustFormat = dynamicYear(f, date.isSame(NOW, 'y'));
            const onlyTimeFormat = timeOnlyFormats.includes(adjustFormat);
            if (onlyTimeFormat || !isRelative)
                return date.format(adjustFormat);
            const timeFormat = adjustFormat.match(timeFormatRegex);
            const calendarFormat = d.Ls[d.locale()].calendar.timeFormat || d.Ls['en'].calendar.timeFormat;
            const calendarTimeFormat = calendarFormat.match(timeFormatRegex);
            const adjustTimeFormat = timeFormat && calendarTimeFormat ? { timeFormat: calendarFormat.replace(calendarTimeFormat[0], timeFormat[0]) } : undefined;
            return date.calendar(NOW, showTime, adjustTimeFormat);
        };
        const startString = startFormat ? getDisplay(START, startFormat, startRelative) : '';
        const endString = endFormat ? getDisplay(END, endFormat, endRelative) : '';
        return startString && endString
            ? format.replace('%ds', startString).replace('%de', endString)
            : (startString || endString);
    };
};
//# sourceMappingURL=humane.js.map