## Format Date/Time

Dayjs including updateLocale, localizedFormat, isoWeek, weekday, relativeTime plugins, and addon customFormat, calendar, humane plugins

- [dayjs](https://day.js.org/en/) - HTML enhanced for web apps!
- [updateLocale](https://day.js.org/docs/en/plugin/update-locale) - update a locale's properties.
- [localizedFormat](https://day.js.org/docs/en/plugin/localized-format) - API to supply localized format options
    - pre-configed 8 [locales](/src/locale):\
    en, ms, id, zh-tw, zh-cn, zh-hk, ja, ko
- [isoWeek](https://day.js.org/docs/en/plugin/iso-week) - get or set the ISO week of the year
- [weekday](https://day.js.org/docs/en/plugin/weekday) - get or set locale aware day of the week.
- [relativeTime](https://day.js.org/docs/en/plugin/relative-time) - formats date to relative time strings (e.g. 3 hours ago).
- [isBetween](https://day.js.org/docs/en/plugin/is-between) - IsBetween adds .isBetween() API to returns a boolean indicating if a date is between two other dates.
- [duration](https://day.js.org/docs/en/plugin/duration) - Duration adds .duration .isDuration APIs to support duration..
- [utc](https://day.js.org/docs/en/manipulate/utc) - This returns a Day.js object with a flag to use UTC time.
- [timezone](https://day.js.org/docs/en/plugin/timezone) - Timezone adds dayjs.tz .tz .tz.guess .tz.setDefault APIs to parse or display between time zones.

- [customFormat](#1-custom-format) - support smart time format (LTX).
- [calendar](#2-calendar) - support same/last/next week.
- [humane](#3-humane) - relativeTime & calendar plugin required. get humane string / duration object / smart date time string based on relative time, humane, period format configs


#### You could also access current locale format:
```js
const locale = formatDateTime.locale() // get current locale key
const config = formatDateTime.Ls[locale] // get current locale format config
const { 
    name, weekdays, months, weekStart, weekdaysShort, monthsShort, weekdaysMin, ordinal, 
    formats, calendar, humane, period, relativeTime  
} = config
```

#### Generate min js files that can be used in web
```
yarn build
```
```js
// copy paste formatDateTime.js & language (except en, as pre build-in) file in your webpage 
// remember to include this to change the locale
formatDateTime.locale('zh-cn')
```


## Extensions

#### 1. Custom Format
- __LTX__: if minute === 0, show ha, otherwise h.mma
- __lyx__: if year === this year, hide year, otherwise show YYYY

| Format | English Locale               | Sample Output                 | ZH Locale               | Sample Output             |
| ------ | ---------------------------- | ----------------------------- | ------------------------|-------------------------- | 
| `LT`   | h:mma                        | 8:02pm                        | Ah点mm分                 | 晚上8点02分                | 
| `LTS`  | ha                           | 8pm                           | Ah点                     | 晚上8点                   | 
| `LTX`  | h:mma or ha (m=0)            | 8:02pm / 8pm                  | Ah点mm分 / Ah点           | 晚上8点02分 / 晚上8点      | 
| `LTZ`  | h:mm or h (m=0)<br/> use in period: 8-10pm  | 8:02 / 8       | h点mm分 / h点             | 8点02分 /8点              |  
| `L`    | D MMM, LTX                   | 16 Aug, 8pm                   | M月D日LTX                 | 8月16日晚上8点             | 
| `LL`   | D MMM YYYY, LTX              | 16 Aug 2023, 8pm              | YYYY年M月D日LTX           | 2023年8月16日晚上8点       | 
| `LYX`   | D MMM YYYY, LTX, D MMM, LTX | 16 Aug 2023, 8pm, 16 Aug, 8pm | YYYY年M月D日LTX, M月D日LTX | 2023年8月16日晚上8点       | 
| `LLL`  | dddd, D MMM, LTX             | Sunday, 5 Feb, 8pm            | M月D日ddddLTX             | 2月5日星期天晚上8点        | 
| `LLLL` | dddd, D MMM YYYY, LTX        | Sunday, 5 Feb 2023, 8pm       | YYYY年M月D日ddddLTX       | 2023年2月5日星期天晚上8点   | 
| `l`    | D MMM                        | 16 Aug                        | M月D日                    | 8月16日                  |
| `ll`   | D MMM, YYYY                  | 16 Aug 2023                   | YYYY年M月D日              | 2023年8月16日             | 
| `lyx`   | D MMM, YYYY  or D MMM       | 16 Aug 2023 / 16 Aug          | YYYY年M月D日 / M月D日      | 2023年8月16日 / 8月16日   | 
| `lll`  | dddd, D MMM                  | Wednesday, Aug 16             | M月D日dddd                | 8月16日星期三             | 
| `llll` | dddd, D MMM YYYY             | Wednesday, Aug 16, 2023       | YYYY年M月D日dddd          | 2023年8月16日星期三        | 
```js
formatDateTime('2023-02-05 20:00').format('LTX') // 8pm
formatDateTime('2023-02-05 20:10').format('LTX') // 8:10pm
formatDateTime('2023-02-05 20:00').format('L') // 5 Feb, 8pm
formatDateTime('2023-02-05 20:00').format('LL') // 5 Feb 2023, 8pm
formatDateTime('2023-02-05 20:00').format('LLL') // Sunday, 5 Feb, 8pm
formatDateTime('2023-02-05 20:00').format('LLLL') // Sunday, 5 Feb 2023, 8pm
```

#### 2. Calendar
- get calendar date/time compared to referenceTime provided (default = now)
  ##### .calendar(now = undefined, showTime = false, formats?)
```js
formatDateTime('2023-05-01 18:10:00').calendar() // compared to NOW, 1 May
formatDateTime('2023-05-01 15:00:00').calendar('2023-05-02') // yesterday
formatDateTime('2023-05-01 15:00:00').calendar('2023-05-02', true) // yesterday, 3pm
```

#### 3. humane
  ##### .humane(now = undefined, showTime: true, formats?)
```js
// get humane time string
formatDateTime().add(40, 'second').humane() // in 40 secs
formatDateTime().subtract(40, 'second').humane() // 40 secs ago
formatDateTime().subtract(1, 'day').hour(4).minute(0).humane() // yesterday, 4am
```

  ##### .smartDateTime(now = undefined, showTime = true)

```js
formatDateTime().hour(4).minute(0).smartDateTime() // today, 4am
formatDateTime().subtract(1, 'day').hour(4).minute(0).smartDateTime() // yesterday, 4am
formatDateTime('2023-01-01 08:00:00').year(formatDateTime().year()).smartDateTime() // 1 Jan, 8am, same year hide year
formatDateTime('2022-01-01 08:00:00').smartDateTime() // 1 Jan 2022, 8am
```

  ##### getDuration(date1, date2)
```js
formatDateTime.getDuration('2023-01-01', '2022-07-10')

// return a duration object 
// {
//     year: 0,
//     month: 5,
//     day: 22,
//     hour: 0,
//     minute: 0,
//     second: 0,
//     firstDateWasLater: true,
// }
-->
```


  ##### getHumanePeriod(date1, date2, now = undefined, showTime = true, formats?)
```js
// return a humanePeriod string
formatDateTime.getHumanePeriod('2022-07-10', '2023-01-01', undefined, false) // 10 Jul 2022 - 1 Jan 2023
formatDateTime.getHumanePeriod('2023-01-01', '2023-01-10', undefined, false) // 1 - 10 Jan
formatDateTime.getHumanePeriod('2023-01-01', '2023-04-10', undefined, false) // 1 Jan - 10 Apr
formatDateTime.getHumanePeriod(formatDateTime().hour(3).minute(10), formatDateTime().hour(10).minute(0)) // today, 3:10am - 10am
```
