{"name": "format-datetime", "version": "1.3.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && npx rollup -c build/rollup.config.mjs && node build/post-build.mjs", "test-one": "tsc && mocha -r ts-node/register", "test": "mocha -r ts-node/register tests/**/*.test.ts", "update": "ncu -u", "reinstall": "rm -rf node_modules/ yarn.lock; yarn; rm -rf dist/ tsconfig.tsbuildinfo; tsc"}, "files": ["README.md", "dist", "!*/__tests__", "!tsconfig.tsbuildinfo"], "dependencies": {"dayjs": "^1.11.13"}, "devDependencies": {"@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.2", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@types/mocha": "^10.0.10", "@types/node": "^22.13.0", "eslint-plugin-mocha": "^10.5.0", "gzip-size-cli": "^5.1.0", "replace-in-file": "^8.3.0", "rollup": "^4.34.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.36.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "packageManager": "yarn@4.6.0"}